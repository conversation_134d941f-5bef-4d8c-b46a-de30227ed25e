<?php $user->restrictionUser(true, $conn); ?>
<div class="row">
                    <div class="col-lg-12">
                        <section class="panel">
                            <header class="panel-heading">
                            <h3>โปรโมชั่นเติมเงิน <small>ตรวจสอบแก้ไขโปรโมชั่น</small></h3>
                            </header>
				                <div class="panel-body">
    <div class="col-lg-12" style="margin: 10px 5px;">
        <form method="post" action="?url=manager/presults-wallet" class="form-inline pull-right">
            <div class="form-group">
                <input type="text" class="form-control" name="search" placeholder="ค้นหา ไอดี/เบอร์โทร/รหัสอ้างอิง">
                <input type="submit" class="btn btn-info" name="btn_search" value="Search">
            </div>
        </form>
    </div>
    <div class="panel panel-default">
        <div class="panel-body no-padd" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
                <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                <?php } ?>
                <table id="example1" class="table no-margn">
                    <thead>
                        <tr>
                            <th>id</th>
                            <th>Item Name</th>
							<th>รายระเอียด</th>
							<th>รหัสไอเท็ม</th>
							<th>ออฟชั่น</th>
                            <th>เวลาใช้งาน</th>
                            <th>จำนวนชิ้น</th>
                            <th>โปร</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 30;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM WEB_PromotionItems_Wallet ORDER BY id asc";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
								<td><?php echo $row[2]; ?></td>
								<td><?php echo $row[3]; ?></td>
								<td><?php echo $row[4]; ?></td>
                                <td><?php echo $row[5]; ?></td>
                                <td><?php echo $row[6]; ?></td>
                                <td><?php echo $row[7]; ?></td>
                                <td><div class="btn-group">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    <?php echo B_ACTION; ?> <span class="caret"></span>
                                     </button>
                                    <ul class="dropdown-menu" role="menu">
									<?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                    <li><a href="?url=manager/edit-promotion&id=<?php echo $row[0]; ?>">Edit</a></li>
                                    <?php } ?>
                                    </ul>
                                    </div></td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=manager/promotion&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=manager/promotion&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=manager/promotion&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
