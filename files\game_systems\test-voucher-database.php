<?php
// ไฟล์ทดสอบการเชื่อมต่อ Database และดึงข้อมูล Voucher Packages
// ใช้สำหรับ Debug และตรวจสอบข้อมูล

require_once '../_app/dbinfo.inc.php';
require_once '../_app/general_config.inc.php';

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>ทดสอบ Database - Voucher Packages</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: #1a1a1a; color: #fff; }
        .container { margin-top: 2rem; }
        .test-section { background: #2d2d2d; padding: 1.5rem; border-radius: 10px; margin-bottom: 1.5rem; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre { background: #1e1e1e; padding: 1rem; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1><i class='fas fa-database'></i> ทดสอบ Database - Voucher Packages</h1>
        <p class='text-muted'>ตรวจสอบการเชื่อมต่อ Database และข้อมูล Voucher Packages</p>";

// ทดสอบการเชื่อมต่อ Database
echo "<div class='test-section'>
        <h3>🔌 ทดสอบการเชื่อมต่อ Database</h3>";

if (isset($conn) && $conn !== false) {
    echo "<p class='success'>✅ เชื่อมต่อ Database สำเร็จ</p>";
    
    // ทดสอบ Query พื้นฐาน
    $testQuery = "SELECT @@VERSION AS SQLVersion, DB_NAME() AS DatabaseName";
    $testResult = sqlsrv_query($conn, $testQuery);
    
    if ($testResult) {
        $dbInfo = sqlsrv_fetch_array($testResult, SQLSRV_FETCH_ASSOC);
        echo "<p class='info'>📊 Database: " . htmlspecialchars($dbInfo['DatabaseName']) . "</p>";
        echo "<p class='info'>🔧 SQL Server Version: " . htmlspecialchars($dbInfo['SQLVersion']) . "</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถเชื่อมต่อ Database ได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบการมีอยู่ของตาราง VoucherPackages
echo "<div class='test-section'>
        <h3>📋 ตรวจสอบตาราง VoucherPackages</h3>";

$checkTableQuery = "
    SELECT COUNT(*) as TableExists 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'VoucherPackages'
";

$checkTableResult = sqlsrv_query($conn, $checkTableQuery);

if ($checkTableResult) {
    $tableInfo = sqlsrv_fetch_array($checkTableResult, SQLSRV_FETCH_ASSOC);
    
    if ($tableInfo['TableExists'] > 0) {
        echo "<p class='success'>✅ ตาราง VoucherPackages มีอยู่ในระบบ</p>";
        
        // ตรวจสอบโครงสร้างตาราง
        $structureQuery = "
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'VoucherPackages'
            ORDER BY ORDINAL_POSITION
        ";
        
        $structureResult = sqlsrv_query($conn, $structureQuery);
        
        if ($structureResult) {
            echo "<h5>📊 โครงสร้างตาราง:</h5>";
            echo "<table class='table table-dark table-striped'>
                    <thead>
                        <tr>
                            <th>ชื่อคอลัมน์</th>
                            <th>ประเภทข้อมูล</th>
                            <th>NULL ได้</th>
                            <th>ค่าเริ่มต้น</th>
                        </tr>
                    </thead>
                    <tbody>";
            
            while ($column = sqlsrv_fetch_array($structureResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>
                        <td>" . htmlspecialchars($column['COLUMN_NAME']) . "</td>
                        <td>" . htmlspecialchars($column['DATA_TYPE']) . "</td>
                        <td>" . htmlspecialchars($column['IS_NULLABLE']) . "</td>
                        <td>" . htmlspecialchars($column['COLUMN_DEFAULT'] ?? 'NULL') . "</td>
                      </tr>";
            }
            
            echo "</tbody></table>";
        }
        
    } else {
        echo "<p class='error'>❌ ไม่พบตาราง VoucherPackages</p>";
        echo "<p class='warning'>⚠️ กรุณารันไฟล์ database/create_voucher_packages_table.sql ก่อน</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถตรวจสอบตารางได้</p>";
}

echo "</div>";

// ทดสอบ Stored Procedure
echo "<div class='test-section'>
        <h3>⚙️ ตรวจสอบ Stored Procedures</h3>";

$checkSPQuery = "
    SELECT name 
    FROM sys.objects 
    WHERE type = 'P' AND name IN ('sp_GetActiveVoucherPackages', 'sp_ManageVoucherPackage')
";

$checkSPResult = sqlsrv_query($conn, $checkSPQuery);

if ($checkSPResult) {
    $storedProcs = [];
    while ($sp = sqlsrv_fetch_array($checkSPResult, SQLSRV_FETCH_ASSOC)) {
        $storedProcs[] = $sp['name'];
    }
    
    if (in_array('sp_GetActiveVoucherPackages', $storedProcs)) {
        echo "<p class='success'>✅ sp_GetActiveVoucherPackages พร้อมใช้งาน</p>";
    } else {
        echo "<p class='error'>❌ ไม่พบ sp_GetActiveVoucherPackages</p>";
    }
    
    if (in_array('sp_ManageVoucherPackage', $storedProcs)) {
        echo "<p class='success'>✅ sp_ManageVoucherPackage พร้อมใช้งาน</p>";
    } else {
        echo "<p class='error'>❌ ไม่พบ sp_ManageVoucherPackage</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถตรวจสอบ Stored Procedures ได้</p>";
}

echo "</div>";

// ทดสอบดึงข้อมูล Voucher Packages
echo "<div class='test-section'>
        <h3>📦 ทดสอบดึงข้อมูล Voucher Packages</h3>";

$getPackagesQuery = "EXEC sp_GetActiveVoucherPackages";
$getPackagesResult = sqlsrv_query($conn, $getPackagesQuery);

if ($getPackagesResult !== false) {
    $packages = [];
    while ($package = sqlsrv_fetch_array($getPackagesResult, SQLSRV_FETCH_ASSOC)) {
        $packages[] = $package;
    }
    
    echo "<p class='success'>✅ ดึงข้อมูลสำเร็จ จำนวน " . count($packages) . " แพ็คเกจ</p>";
    
    if (!empty($packages)) {
        echo "<h5>📋 รายการแพ็คเกจ:</h5>";
        echo "<table class='table table-dark table-striped'>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>ชื่อ</th>
                        <th>รายละเอียด</th>
                        <th>ราคา</th>
                        <th>Item#</th>
                        <th>ไอคอน</th>
                        <th>สี</th>
                        <th>ลำดับ</th>
                        <th>สถานะ</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($packages as $package) {
            echo "<tr>
                    <td>" . htmlspecialchars($package['PackageID']) . "</td>
                    <td>" . htmlspecialchars($package['PackageName']) . "</td>
                    <td>" . htmlspecialchars($package['PackageDescription']) . "</td>
                    <td>" . number_format($package['PackageValue']) . " Cash</td>
                    <td>" . htmlspecialchars($package['ItemNum']) . "</td>
                    <td><i class='" . htmlspecialchars($package['PackageIcon']) . "'></i> " . htmlspecialchars($package['PackageIcon']) . "</td>
                    <td><span style='background:" . htmlspecialchars($package['PackageColor']) . "; padding:2px 8px; border-radius:3px; color:#000;'>" . htmlspecialchars($package['PackageColor']) . "</span></td>
                    <td>" . htmlspecialchars($package['SortOrder']) . "</td>
                    <td><span class='badge bg-success'>Active</span></td>
                  </tr>";
        }
        
        echo "</tbody></table>";
        
        // แสดงข้อมูล JSON สำหรับ JavaScript
        echo "<h5>🔧 ข้อมูล JSON สำหรับ JavaScript:</h5>";
        echo "<pre>" . htmlspecialchars(json_encode($packages, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
        
    } else {
        echo "<p class='warning'>⚠️ ไม่มีแพ็คเกจที่เปิดใช้งาน</p>";
    }
    
} else {
    echo "<p class='error'>❌ ไม่สามารถดึงข้อมูลแพ็คเกจได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบ Query ทั้งหมด (รวมถึงที่ปิดใช้งาน)
echo "<div class='test-section'>
        <h3>📊 ข้อมูลแพ็คเกจทั้งหมด (รวมที่ปิดใช้งาน)</h3>";

$getAllQuery = "SELECT * FROM VoucherPackages ORDER BY SortOrder, PackageValue";
$getAllResult = sqlsrv_query($conn, $getAllQuery);

if ($getAllResult !== false) {
    $allPackages = [];
    while ($package = sqlsrv_fetch_array($getAllResult, SQLSRV_FETCH_ASSOC)) {
        $allPackages[] = $package;
    }
    
    echo "<p class='info'>📊 ข้อมูลทั้งหมด: " . count($allPackages) . " แพ็คเกจ</p>";
    
    $activeCount = count(array_filter($allPackages, function($p) { return $p['IsActive'] == 1; }));
    $inactiveCount = count($allPackages) - $activeCount;
    
    echo "<p class='success'>✅ เปิดใช้งาน: $activeCount แพ็คเกจ</p>";
    if ($inactiveCount > 0) {
        echo "<p class='warning'>⚠️ ปิดใช้งาน: $inactiveCount แพ็คเกจ</p>";
    }
    
} else {
    echo "<p class='error'>❌ ไม่สามารถดึงข้อมูลทั้งหมดได้</p>";
}

echo "</div>";

// สรุปผลการทดสอบ
echo "<div class='test-section'>
        <h3>📋 สรุปผลการทดสอบ</h3>";

$issues = [];

// ตรวจสอบปัญหาต่างๆ
if (!isset($conn) || $conn === false) {
    $issues[] = "ไม่สามารถเชื่อมต่อ Database ได้";
}

if (!isset($tableInfo) || $tableInfo['TableExists'] == 0) {
    $issues[] = "ไม่พบตาราง VoucherPackages";
}

if (!isset($storedProcs) || !in_array('sp_GetActiveVoucherPackages', $storedProcs)) {
    $issues[] = "ไม่พบ Stored Procedure sp_GetActiveVoucherPackages";
}

if (!isset($packages) || empty($packages)) {
    $issues[] = "ไม่มีแพ็คเกจที่เปิดใช้งาน";
}

if (empty($issues)) {
    echo "<p class='success'>🎉 ระบบพร้อมใช้งาน! ไม่พบปัญหาใดๆ</p>";
    echo "<p class='info'>✅ สามารถใช้งานหน้า shop-voucher.php ได้แล้ว</p>";
} else {
    echo "<p class='error'>❌ พบปัญหาที่ต้องแก้ไข:</p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>$issue</li>";
    }
    echo "</ul>";
    
    echo "<h5>🔧 วิธีแก้ไข:</h5>";
    echo "<ol>
            <li>ตรวจสอบการเชื่อมต่อ Database ในไฟล์ _app/dbinfo.inc.php</li>
            <li>รันไฟล์ database/create_voucher_packages_table.sql</li>
            <li>ตรวจสอบ permissions ของ Database User</li>
            <li>เพิ่มข้อมูลแพ็คเกจผ่าน Admin Panel</li>
          </ol>";
}

echo "</div>";

echo "    </div>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'></script>
</body>
</html>";
?>
