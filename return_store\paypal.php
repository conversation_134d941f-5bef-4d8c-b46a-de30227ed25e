<?php
date_default_timezone_set('asia/bangkok');

function createLog($value) {
    file_put_contents(
            pathinfo(__FILE__, PATHINFO_DIRNAME) . '\\donationLog.html', @date('d/m/Y') . ' ' . @date('H:i:s') . ' - ' . utf8_decode($value) . '<br />', FILE_APPEND
    );
}

class PayPalNIP {

    private $timeout = 20; // Timeout in seconds

    public function notificationPost() {
        $postdata = 'cmd=_notify-validate';
        foreach ($_POST as $key => $value) {
            $valued = $this->clearStr($value);
            $postdata .= "&$key=$valued";
        }
        return $this->validar($postdata);
    }

    private function clearStr($str) {
        if (!get_magic_quotes_gpc()) {
            $str = addslashes($str);
        }
        return $str;
    }

    private function validar($data) {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, "https://www.paypal.com/cgi-bin/webscr");
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        $result = trim(curl_exec($curl));
        curl_close($curl);
        return $result;
    }

}

if (count($_POST) > 0) {
    $ppNIP = new PayPalNIP();
    $valido = $ppNIP->notificationPost();

    if (trim($_POST['payment_status']) == 'Completed') {

        // transaction accepted
        createLog("-----------------------------------");
        createLog("-- Paypal - " . $_POST['payment_status']);
        createLog("- Value: " . $_POST['mc_gross']);
        createLog("- Currency: " . $_POST['mc_currency']);
        createLog("- CustomerID: " . $_POST['custom']);
        createLog("- Transaction ID: " . $_POST['txn_id']);
        createLog("-- Script executed!");

        $CustomerID = $_POST['custom'];

        // Database conection
        include_once("../_app/dbPDO.php");

        $DB = Conexao::getInstance();

        if ($_POST['item_number'] == 1 || $_POST['item_number'] == 2 || $_POST['item_number'] == 3 || $_POST['item_number'] == 4 || $_POST['item_number'] == 5) {
            if ($_POST['item_number'] == 1) {
                // PACK 1
                $PROCEDURE = $DB->prepare("EXEC WEB_DonationPacks ?, ?");
                $PROCEDURE->execute(array($CustomerID, '1'));
            } elseif ($_POST['item_number'] == 2) {
                // PACK 2
                $PROCEDURE = $DB->prepare("EXEC WEB_DonationPacks ?, ?");
                $PROCEDURE->execute(array($CustomerID, '2'));
            } elseif ($_POST['item_number'] == 3) {
                // PACK 3
                $PROCEDURE = $DB->prepare("EXEC WEB_DonationPacks ?, ?");
                $PROCEDURE->execute(array($CustomerID, '3'));
            } elseif ($_POST['item_number'] == 4) {
                // PACK 4
                $PROCEDURE = $DB->prepare("EXEC WEB_DonationPacks ?, ?");
                $PROCEDURE->execute(array($CustomerID, '4'));
            } elseif ($_POST['item_number'] == 5) {
                // PACK 5
                $PROCEDURE = $DB->prepare("EXEC WEB_DonationPacks ?, ?");
                $PROCEDURE->execute(array($CustomerID, '5'));
            }
        } elseif ($_POST['item_number'] == 6 || $_POST['item_number'] == 7 || $_POST['item_number'] == 8 || $_POST['item_number'] == 9) {
            // available
        } else {
            // some other problem, check the log
            createLog("<font color=red>-- Paypal: Requested failed: " . $valido . " Status: " . $_POST['payment_status'] . " CustomerID: " . $_POST['custom'] . "</font>");
        }
    }
}else {
	// POST away
	createLog("<font color=red>-- Paypal: This script was open manually from browser.</font>");
}
?>