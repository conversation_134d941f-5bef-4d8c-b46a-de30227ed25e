<?php $user->restrictionUser(true, $conn); ?>
<?php $zpanel->checkSession(true); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> charecter Manager
        <small>
            ระบบ จัดการตัวละคร
        </small>
    </h1>
</div>
<div class="alert alert-primary alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">How it works</span>
                <br>
                โปรดตรวจสอบให้แน่ใจก่อนทุกครั้ง ก่อนทำการ <code>แก้ไข</code> <code>อัพเดต</code> <code>ลบข้อมูล</code>
                เพื่อป้องการข้อผิดพลาด หรือป้องการการทำงานของระบบผิดพลาด
                ถ้าเกิดข้อผิดพลาดจะบางอย่างจะกลับมาเป็นปรกติไม่ได้.
                <br>
                <div class="d-flex mt-2 mb-1 fs-xs text-danger">
                    ค่าใช้จ่ายในการเปลียนอาชีพ <?php echo $Config['CashChangClass']; ?> cash
                </div>
            </div>
        </div>
    </div>
</div>
<?php 
                $getCharID = filter_input(INPUT_GET, 'charid', FILTER_VALIDATE_INT);
                $usernum = floor($getCharID/16);
                $selectPlayerData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$usernum'";
                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, array());
                $selectPlayerDataRows = sqlsrv_rows_affected($selectPlayerDataQuery);
                $selectPlayerFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                $userid =  $selectPlayerFetch['ID'];
                $usernum =  $selectPlayerFetch['UserNum'];

                $selectCashData = "SELECT * FROM " . DATABASE_CCA . ".dbo.CashAccount WHERE UserNum = '$usernum'";
                $selectCashDataParam = array();
                $selectCashDataQuery = sqlsrv_query($conn, $selectCashData, $selectCashDataParam);
                $selectCashDataFetch = sqlsrv_fetch_array($selectCashDataQuery, SQLSRV_FETCH_ASSOC);
                $Cash =  $selectCashDataFetch['Cash'];

                $name =  $userLogin->recCharecter($getCharID,'Name', $conn);
                $name2 = $userLogin->thaitrans($name);
                $Login = $userLogin->recCharecter($getCharID,'Login', $conn);
                $style3 = $userLogin->recCharecter($getCharID,'Style', $conn);
                $style2 = $userLogin->decode_style($style3);
                $class = $style2['Class_Name']; 
                $Gender = $style2['Gender_Description'];
                $Aura = $style2['Aura_Description']; 
                // $Hair = $style2['Hair_Description']; 
                //$Hair_Color = $style2['Hair_Color_Description']; 
                // $Face = $style2['Face_Description'];
                $rank2 = $userLogin->recCharecter($getCharID,'Reputation', $conn);
                if(isset($_POST['btn_changclass'])){ 
                    $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                    $new_class = strip_tags(trim($_POST['newclass']));
                    
                    if (empty($getCharID)) {
                            echo '<script type="text/javascript">';
                            echo 'setTimeout(function () { Swal.fire("Error","ตรวจสอบไม่พบข้อมูลผู้เล่นนี้!","error");';
                            echo '});</script>';
                    }elseif($Cash < $Config['CashChangClass']){
                        echo '<script type="text/javascript">';
                        echo 'setTimeout(function () { Swal.fire("Error","แคสไม่พอใช้งาน!","error");';
                        echo '});</script>';
                    } else {
                            $getDateNow = date('Y-m-d H:i:s');
                            $selectPlayerData = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = '$getCharID'";
                            $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, array());
                            $selectPlayerDataRows = sqlsrv_rows_affected($selectPlayerDataQuery);
                            $selectPlayerFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                            $classchange = $selectPlayerFetch['ChangeClass'];
                                    if ($selectPlayerDataRows) {
                                            if($classchange == 0){
                                                    $classchang = "EXECUTE ".DATABASE_WEB.".dbo.WEB_Cabal_Change_class ?, ?";
                                                    $classchangParams = array($getCharID,$new_class);
                                                    $classchangQuery = sqlsrv_query($conn, $classchang, $classchangParams);
                                                    
                                                    if ($classchangQuery) {
                                                        $updatePlayer = "UPDATE " . DATABASE_CCA . ".dbo.CashAccount SET Cash = Cash-'".$Config['CashChangClass']."' WHERE UserNum = '$usernum'";
                                                        $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer,array());
                                                        echo '<script type="text/javascript">';
                                                        echo 'setTimeout(function () { Swal.fire("Success !!!","เปลียนอาชีพให้ตัวละคร '.$getCharID.' เรียบร้อยแล้ว!","success");';
                                                        echo '});</script>';
                                                    }else{
                                                        echo '<script type="text/javascript">';
                                                        echo 'setTimeout(function () { Swal.fire("Error","เปลียนอาชีพ WA ผิดพลาด0!","error");';
                                                        echo '});</script>';
                                                    }
                                            }else{
                                                echo '<script type="text/javascript">';
                                                echo 'setTimeout(function () { Swal.fire("Error","ข้อมูลผิดพลาด2!","error");';
                                                echo '});</script>';
                                            }
                                                    
                                    } else {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("Error","ข้อมูลผิดพลาด1!","error");';
                                            echo '});</script>';
                                    }
                                        
                        }
                }
?>
<?php  if ($getCharID === NULL){  ?>
<div class="alert alert-danger alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-danger-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-danger-300 fa-flip-vertical"></i>
                <i class="fal fa-times icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">เกิดข้อมูลผิดพลาด</span>
                <br>
                ตรวจสอบไม่พอข้อมูลผู้เล้นนี้ คุณแน่ใจหรือไม่ว่าข้อมูลนี้ถูกต้อง
                ลองกลับไปตรวจสอบข้อมูลใหม่อีกครั้งก่อนทำการ <code>แก้ไข</code> <code>อัพเดต</code> <code>ลบข้อมูล</code>
                เพื่อป้องการข้อผิดพลาด หรือป้องการการทำงานของระบบผิดพลาด
                ถ้าเกิดข้อผิดพลาดจะบางอย่างจะกลับมาเป็นปรกติไม่ได้.
                <br>
            </div>
        </div>
    </div>
</div>
<?php } ?>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Edit New Class <span class="fw-300"><i>เปลียนอาชีพ</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="col-xl-12">
                        <div id="c_1" class="card border shadow-0 mb-g shadow-sm-hover"
                            data-filter-tags="oliver kopyov">
                            <div class="card-body border-faded border-top-0 border-left-0 border-right-0 rounded-top">
                                <div class="d-flex flex-row align-items-center">
                                    <?php if($Login == 0){ ?>
                                    <span class="status status-secondary mr-3">
                                        <?php } else { ?>
                                        <span class="status status-success mr-3">
                                            <?php } ?>
                                            <span class="rounded-circle profile-image d-block "
                                                style="background-image:url('assets/images/cabal/class/<?php echo $class; ?>.gif'); background-size: cover;"></span>
                                        </span>
                                        <div class="info-card-text flex-1">
                                            <a href="javascript:void(0);"
                                                class="fs-xl text-truncate text-truncate-lg text-info">
                                                ชื่อตัวละคร : <?php echo $name2; ?>
                                            </a>
                                            <span class="text-truncate text-truncate-xl">ไอดีผู้ใช้งาน :
                                                <?php echo $usernum; ?>, <?php echo $userid; ?> , Cash ทั้งหมดที่มี :
                                                <?php echo number_format($Cash); ?>
                                            </span>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="frame-heading">
                        <form role="form" method="post" enctype="multipart/form-data">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="input_Opt" class="control-label">อาชีพ: </label>
                                    <select class="js-max-length1 form-control" name="newclass" id="newclass"
                                        multiple="multiple" id="multiple-max-length">
                                        <optgroup label="เลือกอาชีพ">
                                            <option value="1">เปลียนเป็น Warrior</option>
                                            <option value="2">เปลียนเป็น Blader</option>
                                            <option value="3">เปลียนเป็น Wizard</option>
                                            <option value="4">เปลียนเป็น Force Archer</option>
                                            <option value="5">เปลียนเป็น Force Shielder</option>
                                            <option value="6">เปลียนเป็น Force Blader</option>
                                            <option value="7">เปลียนเป็น Gladiator</option>
                                            <option value="8">เปลียนเป็น Force Guner</option>
                                            <option value="9">เปลียนเป็น Dark mage</option>
                                        </optgroup>
                                    </select>

                                </div>
                            </div>
                            <div class="col-md-12">
                                <div
                                    class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row">
                                    <button type="submit" name="btn_changclass"
                                        class="btn btn-primary ml-auto waves-effect waves-themed">
                                        <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>

        <!--row end -->
    </div>