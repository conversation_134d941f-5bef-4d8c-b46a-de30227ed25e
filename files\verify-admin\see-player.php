<?php $user->restrictionUser(true, $conn); ?>
					<header class="page-header">
						<h2>User Profile</h2>
					
						<div class="right-wrapper pull-right">
							<ol class="breadcrumbs">
								<li>
									<a href="index.php">
										<i class="fa fa-home"></i>
									</a>
								</li>
								<li><span>Pages</span></li>
								<li><span>User Profile</span></li>
							</ol>
					
							<a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
						</div>
					</header>

					<!-- start: page -->
                    <?php
                $getIDx = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                $getBan = $_GET['ban'];

                // If there is a variable and delete is not empty then execute the function
                if (isset($getBan) && !(empty($getBan))) {
                    if ($getBan == "wait") {
                        echo '<p class="text-red text-bolder">'. T_CONFIRMBAN .'</p>';
						echo '<a href="?url=manager/see-player&id=' . $getIDx . '&ban=true" class="btn btn-danger">'. B_YESBAN .'</a>';
                    } else if ($getBan == "true") {
                        $selectChar = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$getIDx'";
                        $selectCharQuery = sqlsrv_query($conn, $selectChar, array());
                        $selectCharFetch = sqlsrv_fetch_array($selectCharQuery, SQLSRV_FETCH_ASSOC);

                        $selectAccountByChar = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$selectCharFetch[UserNum]'";
                        $selectAccountByCharQuery = sqlsrv_query($conn, $selectAccountByChar, array());
                        $selectAccountByCharFetch = sqlsrv_fetch_array($selectAccountByCharQuery, SQLSRV_FETCH_ASSOC);

						// CustomerID
						
                        $getCustomerByChar = $selectAccountByCharFetch['ID'];
						$getCustomerID = $selectAccountByCharFetch['UserNum'];
                        // Passing true as string because any word can be true in a condition
                        // first delete account
                        $banPlayer = "UPDATE ".DATABASE_ACC.".dbo.cabal_auth_table SET AuthType = ? WHERE ID = ?";
                        $banPlayerParam = array('2',$getCustomerByChar);
                        $banPlayerQuery = sqlsrv_query($conn, $banPlayer, $banPlayerParam);
                        if (sqlsrv_rows_affected($banPlayerQuery)) {
                            // generate log
							$zpanel->generateWebLog($conn, '1', $getCustomerID, 'player banned', 'player banned by admin');
                            $returnSuccess = S_ACCOUNT_BANNED;
                            echo '<a href="?url=manager/players" class="btn btn-default" style="margin: 5px 0;">Return to chars page</a>';
                        } else {
                            $returnWarning = W_ACCOUNT_BANNED;
                        }
                    } else if($getBan == 'unban-wait'){
                        echo '<p class="text-red text-bolder">'. T_CONFIRMUNBAN .'</p>';
                        echo '<a href="?url=manager/see-player&id=' . $getIDx . '&ban=unban" class="btn btn-danger">'. B_YESUNBAN .'</a>';
                    }else if($getBan == 'unban'){
                        $selectChar = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$getIDx'";
                        $selectCharQuery = sqlsrv_query($conn, $selectChar, array());
                        $selectCharFetch = sqlsrv_fetch_array($selectCharQuery, SQLSRV_FETCH_ASSOC);

                        $selectAccountByChar = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$selectCharFetch[UserNum]'";
                        $selectAccountByCharQuery = sqlsrv_query($conn, $selectAccountByChar, array());
                        $selectAccountByCharFetch = sqlsrv_fetch_array($selectAccountByCharQuery, SQLSRV_FETCH_ASSOC);

                        // Passing true as string because any word can be true in a condition
                        $banPlayer = "UPDATE ".DATABASE_ACC.".dbo.cabal_auth_table SET AuthType = ? WHERE ID = ?";
                        $banPlayerParam = array('1',$getCustomerByChar);
                        $banPlayerQuery = sqlsrv_query($conn, $banPlayer, $banPlayerParam);
                        if (sqlsrv_rows_affected($banPlayerQuery)) {
                            // generate log
							$zpanel->generateWebLog($conn, '1', $getCustomerID, 'player unbanned', 'player unbanned by admin');
                            $returnSuccess = S_ACCOUNT_UNBANNED;
                            echo '<a href="?url=manager/players" class="btn btn-default" style="margin: 5px 0;">Return to chars page</a>';
                        } else {
                            $returnWarning = W_ACCOUNT_UNBANNED;
                        }
                    }else if ($getBan != "wait" || $getBan != "true" || $getBan != 'unban-wait' || $getBan != 'unban') {
                        $returnError = E_INVALIDVALUE;
                    }
                }else{

                    // select char to get customerID
                    $selectChar = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$getIDx'";
                    $selectCharQuery = sqlsrv_query($conn, $selectChar, array());
                    $selectCharFetch = sqlsrv_fetch_array($selectCharQuery, SQLSRV_FETCH_ASSOC);

                    // CustomerID
                    $getCustomerIDByChar = $selectCharFetch['ID'];
                    // get server log by ID
                    $selectPlrAccount = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE ID = '$getCustomerIDByChar'";
                    $selectPlrAccountParam = array();
                    $selectPlrAccountOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectPlrAccountQuery = sqlsrv_query($conn, $selectPlrAccount, $selectPlrAccountParam, $selectPlrAccountOpt);
                    $selectPlrAccountRows = sqlsrv_num_rows($selectPlrAccountQuery);

                    // get data info
                    $selectPlayerData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_charge_auth WHERE UserNum = '$getIDx'";
                    $selectPlayerDataParam = array();
                    $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                    $selectPlayerDataFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);

					// get cash account
                    $selectCashData = "SELECT * FROM ".DATABASE_CCA.".dbo.CashAccount WHERE UserNum = '$getIDx'";
                    $selectPlayerDataParam = array();
                    $selectCashDataQuery = sqlsrv_query($conn, $selectCashData, $selectCashDataParam);
                    $selectCashDataFetch = sqlsrv_fetch_array($selectCashDataQuery, SQLSRV_FETCH_ASSOC);

                    if ($selectPlrAccountRows) {
                        while ($resPlayer = sqlsrv_fetch_array($selectPlrAccountQuery, SQLSRV_FETCH_ASSOC)) {
                            ?>
 
					<div class="row">
						<div class="col-md-4 col-lg-3">
							<section class="panel">
								<div class="panel-body">
									<div class="thumb-info mb-md">
										<img src="<?php if (!$userLogin->recUserInfo('url', $conn)) {
                                                        echo 'home/images/user.png';
                                                    } else {
                                                        echo 'http://'.$userLogin->recUserInfo('url', $conn);
                                                    }
                                                ?>" class="rounded img-responsive" alt="John Doe">
										<div class="thumb-info-title">
											<span class="thumb-info-inner"><?php echo $resPlayer['ID']; ?></span>
											<span class="thumb-info-type"><?php echo $isDev = ($resPlayer['IsDeveloper'] ? 'Developer' : 'Player'); ?></span>
										</div>
									</div>

									<div class="widget-toggle-expand mb-md">
										<div class="widget-header">
											<h6>Profile Completion</h6>
											<div class="widget-toggle">+</div>
										</div>
										<div class="widget-content-collapsed">
											<div class="progress progress-xs light">
												<div class="progress-bar" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%;">
													100%
												</div>
											</div>
										</div>
										<div class="widget-content-expanded">
											<ul class="simple-todo-list">
                                            <?php
                                                        // Variables Date
                                                        $start_date = date('d-m-Y', strtotime($selectPlayerDataFetch['ExpireDate']));
                                                        $expire_date = date('d-m-Y', strtotime($selectPlayerDataFetch['ExpireDate']));
                                                        $today_date = date('d-m-Y');

                                                        /* Start Date */
                                                        $start_explode = explode('-', $start_date);
                                                        $start_day = $start_explode[0];
                                                        $start_month = $start_explode[1];
                                                        $start_year = $start_explode[2];

                                                        /* Expire Date */
                                                        $expire_explode = explode('-', $expire_date);
                                                        $expire_day = $expire_explode[0];
                                                        $expire_month = $expire_explode[1];
                                                        $expire_year = $expire_explode[2];

                                                        /* Today Date */
                                                        $today_explode = explode('-', $today_date);
                                                        $today_day = $today_explode[0];
                                                        $today_month = $today_explode[1];
                                                        $today_year = $today_explode[2];

                                                        $start = gregoriantojd($start_month, $start_day, $start_year);
                                                        $expire = gregoriantojd($expire_month, $expire_day, $expire_year);
                                                        $today = gregoriantojd($today_month, $today_day, $today_year);

                                                        $date_current = $expire - $today; //หาวันที่ยังเหลืออยู่
                                                    ?>
                                                <li class="completed">แพทตินั่ม:   <?php if ($date_current > 0) { ?>
                                                    <p class="label label-success">Premium 
                                                    <?php
                                                        echo 'วันหมดอายุ ';
                                                        echo $expire_date.' เหลือ ';
                                                        echo $date_current.' วัน ';

                                                        ?></p>
                                                    <?php } else { ?>
                                                    <p class="label label-danger">หมดอายุ <a href="#" data-target="#extPremiumModal" data-toggle="modal" class="pull-right text-white text-bolder"><?php echo T_ADD_ACCOUNT_PREMIUM; ?></a></p>
                                                    <?php } ?></li>
                                                <li class="completed">สถานะไอดี: <?php if ($resPlayer['AuthType'] == '0') {
                                                ?>
                                                <p class="label label-success">ยังไม่มีการล็อกอินเกมส์</p>
                                                <?php
                                                            } elseif ($resPlayer['AuthType'] == '1') {
                                                                ?>
                                                <p class="label label-success">ปรกติ</p>
                                                <?php
                                                            } elseif ($resPlayer['AuthType'] == '2') {
                                                                ?>
                                                <p class="label label-danger">โดนแบน</p>
                                                <?php
                                                            } elseif ($resPlayer['AuthType'] == '3') {
                                                                ?>
                                                <p class="label label-danger">แบน </p>
                                                <?php
                                                            } elseif ($resPlayer['AuthType'] == '4') {
                                                                ?>
                                                <p class="label label-danger">แบน </p>
                                                <?php
                                                            } elseif ($resPlayer['AuthType'] == '5') {
                                                                ?>
                                                <p class="label label-danger">แบน </p>
                                                <?php
                                                            } else {
                                                                ?>
                                                <p class="label label-success">ไม่มีข้อมูล</p>
												
                                                <?php
					
                                            } ?></li>
                                                <li class="completed">สถานะออนไลน์: 
                                                <?php  if ($resPlayer['Login'] == '0') { ?>
                                                <p class="label label-danger">ออฟไลน์</p>
                                                <?php } elseif($resPlayer['Login'] == '1') {
                                                                ?>
                                                <p class="label label-success">ออนไลน์</p>
                                                <?php } ?>
                                                </li>
                                                <li class="completed">UserNum: <?php echo $resPlayer['UserNum']; ?></li>
												<li class="completed">Email: <?php echo $resPlayer['Email']; ?></li>
												<li class="completed">Phone: <?php echo $resPlayer['Phone_Number']; ?></li>
                                                <li class="completed">IP สมัคร: <?php echo $resPlayer['IP']; ?></li>
                                                <li class="completed">IP เข้าเกมส์: <?php echo $resPlayer['LastIp']; ?></li>
                                                <li>สมัครไอดีเมื้อ: วันที่ <?php echo date('d/m/Y '.เวลา.' H:i', strtotime($resPlayer['createDate'])); ?></li>
                                                <li>เข้าเกมสืล่าสุด: วันที่ <?php echo date('d/m/Y '.เวลา.' H:i', strtotime($resPlayer['LoginTime'])); ?></li>
												<li>ออกเกมส์ล่าสุด วันที่ <?php echo date('d/m/Y '.เวลา.' H:i', strtotime($resPlayer['LogoutTime'])); ?></li>
												<li>PlayTime <?php echo $resPlayer['PlayTime']/60; ?> นาที</li>
											</ul>
										</div>
									</div>

									<hr class="dotted short">

									<h6 class="text-muted">จัดการไอดี</h6>
									<?php if ($resPlayer['AuthType'] == 1){
									echo '<button type="button" class="mb-xs mt-xs mr-xs btn btn-warning"  data-toggle="modal" data-target="#banned">แบนไอดี</button>';
									} else {
										echo '<button type="button" class="mb-xs mt-xs mr-xs btn btn-danger"  data-toggle="modal" data-target="#banned">ปรดแบน</button>';
									}
										?>
									<div class="clearfix">
										<a class="text-uppercase text-muted pull-right" href="#">(ทั้งหมด)</a>
									</div>

									<hr class="dotted short">

									<div class="social-icons-list">
										<a rel="tooltip" data-placement="bottom" target="_blank" href="http://www.facebook.com" data-original-title="Facebook"><i class="fa fa-facebook"></i><span>Facebook</span></a>
										<a rel="tooltip" data-placement="bottom" href="http://www.twitter.com" data-original-title="Twitter"><i class="fa fa-twitter"></i><span>Twitter</span></a>
										<a rel="tooltip" data-placement="bottom" href="http://www.linkedin.com" data-original-title="Linkedin"><i class="fa fa-linkedin"></i><span>Linkedin</span></a>
									</div>

								</div>
							</section>  
                             
						

						</div>
						<div class="col-md-8 col-lg-6">

							<div class="tabs">
								<ul class="nav nav-tabs tabs-primary">
									<li class="active">
										<a href="#overview" data-toggle="tab">Overview</a>
									</li>
									<li>
										<a href="#vip" data-toggle="tab">VIP</a>
									</li>
									<li>
										<a href="#edit" data-toggle="tab">Edit</a>
									</li>
								</ul>
								<div class="tab-content">
									<div id="overview" class="tab-pane active">
										<h4 class="mb-md">Update Status</h4>

										<div class="timeline timeline-simple mt-xlg mb-md">
											<div class="tm-body">
												<div class="tm-title">
													<h3 class="h5 text-uppercase">update</h3>
												</div>
												<ol class="tm-items">
												<li>
														<div class="tm-box">
													
																<h2>ข้อมูลการเติมเงิน<small> ล่าสุด</small></h2>
																<table class="table table-striped no-margn" id="datatable-default">
																	<thead>
																		<tr>
																			<th>id</th>
																			<th>user_id</th>
																			<th>password</th>
																			<th>amount</th>
																			<th>status</th>
																			<th>added_time</th>
																			<th>check</th>
																		</tr>
																	</thead>
																	<tbody>
																		<?php
																		$selectLastUsers = "SELECT TOP 300 * FROM WEB_Tmoney WHERE user_no = '$getCustomerIDByChar' ORDER BY card_id DESC";
																		$selectLastUsersParam = array();
																		$selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
																		$selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

																		if (sqlsrv_num_rows($selectLastUsersQuery)) {
																			while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
																				?>
																				<tr>
																					<td><?php echo $resLastUsers['card_id']; ?></td>
																					<td><?php echo $resLastUsers['user_no']; ?></td>
																					<td><?php echo $resLastUsers['password']; ?></td>
																					<td><?php echo $userLogin->statustrue($resLastUsers['amount']); ?></td>
																					<td><span class="label
																					<?php echo $label = ($resLastUsers['status'] == '1' ? ' label-danger' : ($resLastUsers['status'] == '0' ? ' label-success' : 'label-default'));?>">
																					<?php echo $status = ($resLastUsers['status'] == '1' ? 'ใช้งานแล้ว' : ($resLastUsers['status'] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
																					</td>
																					<td><?php echo $resLastUsers['added_time']; ?></td>
																					<td><span class="label
																					<?php echo $label = ($resLastUsers['checks'] == '1' ? ' label-danger' : ($resLastUsers['checks'] == '0' ? ' label-success' : 'label-default'));?>">
																					<?php echo $status = ($resLastUsers['checks'] == '1' ? 'ใช้งานแล้ว' : ($resLastUsers['checks'] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
																					</td>
																				</tr>
																				<?php
																			}
																		} else {
																			echo W_NOTHING_RETURNED;
																		}
																		?>
																	</tbody>
																</table>
																<a href="?url=manager/tview" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
												
														</div>
													</li>
													<li>
														<div class="tm-box">
															<p class="text-muted mb-none">กำลังอัพเดด.</p>
															<p>
																กำลังอัพเดด <span class="text-primary">#awesome</span>
															</p>
														</div>
													</li>
													<li>
														<div class="tm-box">
															<p class="text-muted mb-none">7 months ago.</p>
															<p>
															กำลังอัพเดด <span class="text-primary">#awesome</span>
															</p>
														</div>
													</li>
													
												</ol>
											</div>
										</div>
									</div>
							<!--tab panel vip id -->		
									<div id="vip" class="tab-pane">

										<form method="post" name="j_add_vipcard"  class="form-horizontal" >
									
											<h4 class="mb-xlg">เพิ่มบัตร VIP</h4>
											<fieldset class="mb-xl">
												<div class="col-lg-12 j_alert"></div>
												<div class="form-group">
													<label class="col-md-3 control-label" for="profileNewPassword">Password Admin</label>
													<div class="col-md-8">
														<input type="password" name="input_passadmin" class="form-control" id="profileNewPassword">
													</div>
												</div>
												<div class="form-group">
													<label class="col-md-3 control-label" for="profileNewPasswordRepeat">เลือก Code VIP</label>
													<div class="col-md-8">
													<select name="input_cardvip" data-plugin-selecttwo="" class="form-control populate js-example-responsive select2-hidden-accessible" style="width: 100%;" tabindex="-1" aria-hidden="true">
																<optgroup label="Code vip ราคา 500/1000 point">
																	<option value="11111111111111">Code 500</option>
																	<option value="**************">Code 1000</option>
																</optgroup>
															</select>
													</div>
												</div>
											</fieldset>
											<div class="panel-footer">
												<div class="row">
													<div class="col-md-9 col-md-offset-3">
														<input type="hidden" name="CustomerID" value="<?php echo strip_tags(trim($_GET['id'])); ?>">
														<input type="hidden" name="AdminCID" value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
														<button type="submit" class="btn btn-primary">Submit</button>
														<button type="reset" class="btn btn-default">Reset</button>
														<img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
													</div>
												</div>
											</div>

										</form>

									</div>
							<!-- -->

								<!--tab panel vip id -->		
								<div id="edit" class="tab-pane">

									<form class="form-horizontal" method="get">

										<h4 class="mb-xlg">Change Password</h4>
										<fieldset class="mb-xl">
											<div class="form-group">
												<label class="col-md-3 control-label" for="profileNewPassword">New Password</label>
												<div class="col-md-8">
													<input type="text" class="form-control" id="profileNewPassword">
												</div>
											</div>
											<div class="form-group">
												<label class="col-md-3 control-label" for="profileNewPasswordRepeat">Repeat New Password</label>
												<div class="col-md-8">
													<input type="text" class="form-control" id="profileNewPasswordRepeat">
												</div>
											</div>
										</fieldset>
										<div class="panel-footer">
											<div class="row">
												<div class="col-md-9 col-md-offset-3">
													<button type="submit" class="btn btn-primary">Submit</button>
													<button type="reset" class="btn btn-default">Reset</button>
												</div>
											</div>
										</div>

									</form>

									</div>
									<!-- -->
								</div>
							</div>
						</div>
						<div class="col-md-12 col-lg-3">

							<h4 class="mb-md">ข้อมูลทางการเงิน</h4>
							<ul class="simple-card-list mb-xlg">
								<li class="primary">
									<a class="mb-xs mt-xs mr-xs modal-with-move-anim  label" href="#modalFormcash"><h3><?php echo number_format($selectCashDataFetch['Cash']); ?></h3></a>
									<p>Cash.</p>
									<?php 
									
								echo '<div id="modalFormcash" class="modal-block modal-block-primary mfp-hide">
										<section class="panel">
											<header class="panel-heading">
												<h2 class="panel-title">Cash And Reward</h2>
											</header>
											<div class="panel-body">
												<form id="demo-form" class="form-horizontal mb-lg" novalidate="novalidate">
													<div class="form-group mt-lg">
														<label class="col-sm-3 control-label">Password Admin</label>
														<div class="col-sm-9">
															<input type="Password" name="Password" class="form-control" placeholder="Password Admin..." required="">
														</div>
													</div>
													<div class="form-group">
														<label class="col-sm-3 control-label">Reward</label>
														<div class="col-sm-9">
															<input type="reward" name="reward" class="form-control" placeholder="Point..." required="">
														</div>
													</div>
													
													<div class="form-group">
														<label class="col-sm-3 control-label">State</label>
														<div class="col-sm-9">
															<select data-plugin-selecttwo="" class="form-control populate js-example-responsive select2-hidden-accessible" style="width: 100%;" tabindex="-1" aria-hidden="true">
																<optgroup label="Cash/Reward point">
																	<option value="cash">Cash</option>
																	<option value="reward">Reward</option>
																</optgroup>
															</select>
														</div>
													</div>
												
												</form>
											</div>
											<footer class="panel-footer">
												<div class="row">
													<div class="col-md-12 text-right">
														<button class="btn btn-primary modal-confirm">Submit</button>
														<button class="btn btn-default modal-dismiss">Cancel</button>
													</div>
												</div>
											</footer>
										</section>
									</div>';
									?>
								</li>
								<li class="primary">
								<a class="mb-xs mt-xs mr-xs modal-with-move-anim  label" href="#modalFormcash"><h3><?php echo number_format($selectCashDataFetch['CashBonus']); ?></h3></a>
									<p>CashBonus.</p>
								</li>
								<li class="primary">
								<a class="mb-xs mt-xs mr-xs modal-with-move-anim  label" href="#modalFormcash"><h3><?php echo number_format($selectCashDataFetch['Reward']); ?></h3></a>
									<p>Reward</p>
								</li>
							</ul>

							<section class="panel">
                            <?php
                                    $selectUserChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx/8 = '$getIDx' ORDER BY CharacterIdx asc";
                                    $selectUserCharsParam = array();
                                    $selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                                    $selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
                                    $selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

                                    if ($selectNumRowsChars) {  ?>
								<header class="panel-heading">
									<div class="panel-actions">
										<a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
										<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
									</div>

									<h2 class="panel-title">
										<span class="label label-primary label-sm text-weight-normal va-middle mr-sm"><?php echo $selectNumRowsChars; ?></span>
										<span class="va-middle">Total Chars</span>
									</h2>
								</header>
								<div class="panel-body">
									<div class="content">
										<ul class="simple-user-list">
                                        <?php   while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                                $style = $userLogin->decode_style($resUserChars['Style']);
                                            ?>
											<li>
												<figure class="image rounded">
													<img src="assets/images/cabal/class/<?php echo $style['Class_Name']; ?>.gif" alt="--" class="img-circle">
												</figure>
												<span class="title"><?php echo $userLogin->thaitrans($resUserChars['Name']); ?></span>
                                                
                                                <?php echo $resUserChars['CharacterIdx']; ?> เลขตัวละคร  <a href="?url=manager/edit-char&id=<?php echo $resUserChars['CharacterIdx']; ?>" class="pull-right text-white"><strong><?php echo T_EDITCHAR; ?></strong></a></p>
											</li>
                                            <?php } } else { ?>
												<header class="panel-heading">
													<div class="panel-actions">
														<a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
														<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
													</div>

													<h2 class="panel-title">แจ้งเตือน</h2>
													<p class="panel-subtitle">ยังไม่มีตัวละคร.</p>
												</header>
                                                
                                            <?php } ?>
										</ul>
										<hr class="dotted short">
										<div class="text-right">
											<a class="text-uppercase text-muted" href="#">(ทั้งหมด)</a>
										</div>
									</div>
								</div>
								
							</section>

						</div>

					</div>
					<!-- end: page -->
				</section>
			</div>

			<aside id="sidebar-right" class="sidebar-right">
				<div class="nano">
					<div class="nano-content">
						<a href="#" class="mobile-close visible-xs">
							Collapse <i class="fa fa-chevron-right"></i>
						</a>
			
						<div class="sidebar-right-wrapper">
			
							<div class="sidebar-widget widget-calendar">
								<h6>Upcoming Tasks</h6>
								<div data-plugin-datepicker data-plugin-skin="dark" ></div>
			
								<ul>
									<li>
										<time datetime="2016-04-19T00:00+00:00">04/19/2016</time>
										<span>Company Meeting</span>
									</li>
								</ul>
							</div>
			
							<div class="sidebar-widget widget-friends">
								<h6>Friends</h6>
								<ul>
									<li class="status-online">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-online">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-offline">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-offline">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
								</ul>
							</div>
                            <?php
                                    }
                                } else {
                                    $returnWarning = W_PLAYER_NOT_FOUND;
                                }
                            }
                        
                            ?>
                            <?php if (isset($returnSuccess)) { ?>
                                <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                            <?php } elseif (isset($returnWarning)) { ?>
                                <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                            <?php } elseif (isset($returnError)) { ?>
                                <div class="alert alert-danger"><?php echo $returnError; ?></div>
                            <?php } ?>
						</div>
					</div>
				</div>
			</aside>


<!-- Modal Ban id-->
<div class="modal fade" id="banned" tabindex="-1" role="dialog" aria-labelledby="financialModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">แบนไอดีพร้อมข้อมูลการแบน</h4>
            </div>
            <form method="post" name="j_add_banned" action="">
                <div class="modal-body">
                    <div class="col-lg-12 j_alert"></div>
                    <p class="text-danger">การแบนไอดี ต้องกรอก รายระเอียดการแบนให้ครบเพื่อ admin ท่านอื่นจะได้ตรวจสอบได้ง่าย<br> <span class="text-bolder">พร้อมหลักฐานการแบนถ้ามี</span></p>
					<div class="form-group">
                        <label for="plr-gc">แบน/ปรดแบน</label>
						
						<select class="form-control" data-plugin-multiselect="" data-plugin-options="{ &quot;maxHeight&quot;: 200 }" id="input_authType" name="input_authType" style="display: none;">
							<option value="2" selected="">BANNED</option>
							<option value="1">UNBANNED</option>
						</select>
					</div>
                    
                    <div class="form-group">
                        <label for="plr-gc">รายระเอียด</label>
                        <input class="form-control" id="input_detail" type="text" name="input_detail">
                    </div>
					<div class="form-group">
                        <label for="plr-gc">หลักฐาน</label>
                        <input class="form-control" id="input_premise" type="text" name="input_premise" >
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    <input type="submit" class="btn btn-primary" value="banned">
                    <input type="hidden" name="CustomerID" value="<?php echo strip_tags(trim($_GET['id'])); ?>">
                    <input type="hidden" name="AdminCID" value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
                    <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                </div>
            </form>
        </div>
    </div>
</div>



<!-- extend premium modal -->
<div class="modal fade" id="extPremiumModal" tabindex="-1" role="dialog" aria-labelledby="extPremiumModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="extPremiumModalLabel"><?php echo TM_H_EXT_PREMIUM; ?></h4>
            </div>
            <form method="post" name="j_ext_premium" action="">
                <div class="modal-body">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-info"><?php echo TM_PREMIUM_EXT_INFO; ?></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    <input type="submit" class="btn btn-primary" value="<?php echo B_EXT_PRE; ?>">
                    <input type="hidden" name="CustomerID" value="<?php echo strip_tags(trim($_GET['id'])); ?>">
                    <input type="hidden" name="AdminCID" value="<?php echo $getCustomerID; ?>">
                    <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                </div>
            </form>
        </div>
    </div>
</div>
