<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-user-clock"></i> ทดสอบการแก้ไขปัญหา Session Timeout
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหาระบบเด้งออกเมื่อกดปุ่มอัพเดทอัตโนมัติ</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ปัญหาระบบเด้งออกจากระบบเมื่อใช้งานอัพเดทอัตโนมัติ
                </div>
                
                <h5>❌ ปัญหาที่เกิดขึ้น</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">Session Issues</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-times text-danger"></i> กดปุ่มอัพเดทอัตโนมัติแล้วเด้งออก</li>
                                    <li><i class="fal fa-times text-danger"></i> AJAX calls ไม่ผ่าน authentication</li>
                                    <li><i class="fal fa-times text-danger"></i> Session timeout ไม่มีการจัดการ</li>
                                    <li><i class="fal fa-times text-danger"></i> ไม่มีการตรวจสอบ session status</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Solutions Applied</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> แก้ไข AJAX URLs ให้ผ่าน routing</li>
                                    <li><i class="fal fa-check text-success"></i> เพิ่ม session management</li>
                                    <li><i class="fal fa-check text-success"></i> ตรวจสอบ session ทุก 5 นาที</li>
                                    <li><i class="fal fa-check text-success"></i> เพิ่มการจัดการ session expired</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔧 การแก้ไขที่ทำ</h5>
                
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. แก้ไข AJAX URLs
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>เปลี่ยนจาก:</strong></p>
                                <pre><code>fetch('files/manager_charecter/api/character-data.php?action=live_stats')</code></pre>
                                
                                <p><strong>เป็น:</strong></p>
                                <pre><code>fetch('?url=manager_charecter/api/character-data&action=live_stats', {
    method: 'GET',
    credentials: 'same-origin',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Cache-Control': 'no-cache'
    }
})</code></pre>
                                
                                <p><strong>ผลลัพธ์:</strong> AJAX calls ผ่าน authentication system</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. Session Management
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>เพิ่มฟังก์ชัน handleSessionExpired:</strong></p>
                                <pre><code>function handleSessionExpired() {
    // หยุด auto refresh
    clearInterval(autoRefreshInterval);
    
    // แสดงการแจ้งเตือน
    addNotificationToArea('⚠️ Session หมดอายุ', 'danger');
    
    // Redirect ไปหน้า login
    setTimeout(() => {
        window.location.href = '?url=login';
    }, 2000);
}</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Session Status Monitoring
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ตรวจสอบ session ทุก 5 นาที:</strong></p>
                                <pre><code>function checkSessionStatus() {
    fetch('?url=manager_charecter/api/character-data&action=live_stats')
    .then(response => {
        if (!response.ok || response.status === 401) {
            handleSessionExpired();
        }
        return response.text();
    })
    .then(text => {
        if (text.includes('<!DOCTYPE')) {
            handleSessionExpired();
        }
    });
}

// ตั้งค่าการตรวจสอบ
setInterval(checkSessionStatus, 300000); // 5 minutes</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingFour">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseFour">
                                    4. Auto Refresh Improvements
                                </button>
                            </h6>
                        </div>
                        <div id="collapseFour" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ปรับปรุงการตั้งค่า:</strong></p>
                                <ul>
                                    <li>เพิ่มระยะเวลาเป็น 30 วินาที (ลด server load)</li>
                                    <li>เพิ่มการแจ้งเตือนเมื่อเริ่มต้น</li>
                                    <li>ตรวจสอบ session ก่อนทำ AJAX</li>
                                    <li>จัดการ error response อย่างเหมาะสม</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบระบบ</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="testSessionStatus()">
                            <i class="fal fa-user-check"></i> ทดสอบ Session Status
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testAPICall()">
                            <i class="fal fa-plug"></i> ทดสอบ API Call
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="simulateSessionExpired()">
                            <i class="fal fa-user-times"></i> จำลอง Session Expired
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="openCharacterMonitor()">
                            <i class="fal fa-external-link"></i> เปิด Character Monitor
                        </button>
                    </div>
                </div>
                
                <div id="test-results" class="mt-3"></div>
                
                <h5 class="mt-4">📊 Session Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Current Session</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
                                echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
                                echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
                                echo "<p><strong>Session Start:</strong> " . date('Y-m-d H:i:s', $_SESSION['session_start'] ?? time()) . "</p>";
                                
                                // Session timeout settings
                                $timeout = ini_get('session.gc_maxlifetime');
                                echo "<p><strong>Session Timeout:</strong> " . ($timeout ? $timeout . ' seconds (' . round($timeout/60) . ' minutes)' : 'Not set') . "</p>";
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Recommendations</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>ใช้ Auto Refresh ระยะเวลาไม่เกิน 30 วินาที</li>
                                    <li>ตรวจสอบ Console (F12) หา errors</li>
                                    <li>หลีกเลี่ยงการเปิดหลายแท็บพร้อมกัน</li>
                                    <li>ปิดแท็บอื่นที่ไม่ใช้งาน</li>
                                    <li>รีเฟรชหน้าถ้ามีปัญหา</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 Troubleshooting</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>ปัญหา</th>
                                <th>สาเหตุ</th>
                                <th>วิธีแก้ไข</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>เด้งออกเมื่อกด Auto Refresh</td>
                                <td>AJAX URL ไม่ผ่าน authentication</td>
                                <td>ใช้ routing system แทน direct file access</td>
                            </tr>
                            <tr>
                                <td>Session หมดอายุเร็ว</td>
                                <td>Server timeout settings</td>
                                <td>ลดความถี่ของ AJAX calls</td>
                            </tr>
                            <tr>
                                <td>ไม่มีการแจ้งเตือน session expired</td>
                                <td>ไม่มี error handling</td>
                                <td>เพิ่ม session monitoring</td>
                            </tr>
                            <tr>
                                <td>หน้าค้างหรือไม่ตอบสนอง</td>
                                <td>JavaScript errors</td>
                                <td>ตรวจสอบ Console และรีเฟรชหน้า</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข AJAX URLs ให้ผ่าน authentication</li>
                                <li>✅ เพิ่ม session management</li>
                                <li>✅ ตรวจสอบ session status อัตโนมัติ</li>
                                <li>✅ จัดการ session expired</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ปรับปรุง auto refresh (30 วินาที)</li>
                                <li>✅ เพิ่ม error handling</li>
                                <li>✅ แสดงการแจ้งเตือนที่เหมาะสม</li>
                                <li>✅ ป้องกันการเด้งออกจากระบบ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testSessionStatus() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ session status...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=live_stats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> Session Status Test</h6>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Session Valid:</strong> ' + (response.ok && !responseText.includes('<!DOCTYPE') ? '✅ Yes' : '❌ No') + '</p>';
        
        if (response.ok && responseText.trim().startsWith('{')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>API Response:</strong> ' + (data.success ? '✅ Success' : '❌ Failed') + '</p>';
            } catch (e) {
                html += '<p><strong>API Response:</strong> ❌ Invalid JSON</p>';
            }
        } else if (responseText.includes('<!DOCTYPE')) {
            html += '<p><strong>Response Type:</strong> ❌ HTML (Session Expired)</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testAPICall() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ API call...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=recent_activities&limit=5', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            }
        });
        
        const responseText = await response.text();
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> API Call Test</h6>';
        html += '<p><strong>URL:</strong> ?url=manager_charecter/api/character-data&action=recent_activities&limit=5</p>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Content-Type:</strong> ' + (response.headers.get('content-type') || 'Not set') + '</p>';
        
        if (responseText.trim().startsWith('{')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>JSON Valid:</strong> ✅ Yes</p>';
                html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                if (data.success && data.data) {
                    html += '<p><strong>Data Count:</strong> ' + data.data.length + '</p>';
                }
            } catch (e) {
                html += '<p><strong>JSON Valid:</strong> ❌ No</p>';
            }
        } else {
            html += '<p><strong>Response Type:</strong> ' + (responseText.includes('<!DOCTYPE') ? 'HTML' : 'Unknown') + '</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> API Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function simulateSessionExpired() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = `
        <div class="alert alert-warning">
            <h6><i class="fal fa-exclamation-triangle"></i> จำลอง Session Expired</h6>
            <p>นี่คือสิ่งที่จะเกิดขึ้นเมื่อ session หมดอายุ:</p>
            <ol>
                <li>ระบบจะหยุดการ auto refresh</li>
                <li>แสดงการแจ้งเตือน "Session หมดอายุ"</li>
                <li>เปลี่ยนปุ่มเป็น "Session หมดอายุ" สีแดง</li>
                <li>แสดง confirm dialog</li>
                <li>Redirect ไปหน้า login</li>
            </ol>
            <p><strong>หมายเหตุ:</strong> การจำลองนี้จะไม่ redirect จริง</p>
        </div>
    `;
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
