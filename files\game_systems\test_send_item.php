<?php
/**
 * Test file for debugging send_item.php
 */

// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

header('Content-Type: application/json');

echo "<h2>Testing Send Item System</h2>";

// Test 1: Check if config files are loaded
echo "<h3>1. Configuration Test</h3>";
if (isset($dbinfo)) {
    echo "✅ dbinfo.inc.php loaded successfully<br>";
    echo "Host: " . ($dbinfo['host'] ?? 'Not set') . "<br>";
    echo "Database: " . ($dbinfo['database'] ?? 'Not set') . "<br>";
    echo "Username: " . ($dbinfo['username'] ?? 'Not set') . "<br>";
} else {
    echo "❌ dbinfo.inc.php not loaded or \$dbinfo not set<br>";
}

// Test 2: Check sqlsrv extension
echo "<h3>2. SQL Server Extension Test</h3>";
if (extension_loaded('sqlsrv')) {
    echo "✅ sqlsrv extension is loaded<br>";
} else {
    echo "❌ sqlsrv extension is NOT loaded<br>";
    echo "Available extensions: " . implode(', ', get_loaded_extensions()) . "<br>";
}

// Test 3: Database connection
echo "<h3>3. Database Connection Test</h3>";
try {
    if (!isset($dbinfo)) {
        throw new Exception("Database configuration not found");
    }
    
    $serverName = $dbinfo['host'];
    if (isset($dbinfo['port']) && $dbinfo['port']) {
        $serverName .= "," . $dbinfo['port'];
    }
    
    $connectionOptions = [
        "Database" => $dbinfo['database'],
        "Uid" => $dbinfo['username'],
        "PWD" => $dbinfo['password'],
        "CharacterSet" => "UTF-8"
    ];
    
    echo "Attempting to connect to: $serverName<br>";
    echo "Database: " . $dbinfo['database'] . "<br>";
    
    $connection = sqlsrv_connect($serverName, $connectionOptions);
    
    if ($connection === false) {
        $errors = sqlsrv_errors();
        echo "❌ Database connection failed:<br>";
        foreach ($errors as $error) {
            echo "Error: " . $error['message'] . "<br>";
        }
    } else {
        echo "✅ Database connection successful<br>";
        
        // Test 4: Check if tables exist
        echo "<h3>4. Table Existence Test</h3>";
        
        // Check item_sends table
        $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'item_sends'";
        $stmt = sqlsrv_query($connection, $sql);
        if ($stmt && $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            if ($row['count'] > 0) {
                echo "✅ item_sends table exists<br>";
            } else {
                echo "❌ item_sends table does NOT exist<br>";
            }
        }
        
        // Check notifications table
        $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'notifications'";
        $stmt = sqlsrv_query($connection, $sql);
        if ($stmt && $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            if ($row['count'] > 0) {
                echo "✅ notifications table exists<br>";
            } else {
                echo "❌ notifications table does NOT exist<br>";
            }
        }
        
        // Check account table
        $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'account'";
        $stmt = sqlsrv_query($connection, $sql);
        if ($stmt && $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            if ($row['count'] > 0) {
                echo "✅ account table exists<br>";
                
                // Test sample account
                $sql = "SELECT TOP 1 UserNum, ID FROM account";
                $stmt = sqlsrv_query($connection, $sql);
                if ($stmt && $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                    echo "Sample account found: " . $row['ID'] . " (UserNum: " . $row['UserNum'] . ")<br>";
                }
            } else {
                echo "❌ account table does NOT exist<br>";
            }
        }
        
        // Test 5: Test insert into item_sends
        echo "<h3>5. Insert Test</h3>";
        try {
            $test_data = [
                'player_id' => 1,
                'player_username' => 'test_player',
                'item_id' => 123,
                'item_name' => 'Test Item',
                'quantity' => 1,
                'duration' => 31,
                'send_method' => 'mail',
                'item_code' => '0000007B00000000',
                'options_code' => '****************',
                'admin_username' => 'test_admin',
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'test'
            ];
            
            $sql = "INSERT INTO item_sends (
                player_id, player_username, item_id, item_name, quantity, duration, send_method,
                item_code, options_code, admin_username, created_at, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = array(
                $test_data['player_id'],
                $test_data['player_username'],
                $test_data['item_id'],
                $test_data['item_name'],
                $test_data['quantity'],
                $test_data['duration'],
                $test_data['send_method'],
                $test_data['item_code'],
                $test_data['options_code'],
                $test_data['admin_username'],
                $test_data['created_at'],
                $test_data['status']
            );
            
            $stmt = sqlsrv_prepare($connection, $sql, $params);
            
            if ($stmt === false) {
                echo "❌ Prepare failed: " . print_r(sqlsrv_errors(), true) . "<br>";
            } else {
                if (sqlsrv_execute($stmt)) {
                    echo "✅ Test insert successful<br>";
                    
                    // Get the inserted ID
                    $id_sql = "SELECT SCOPE_IDENTITY() as send_id";
                    $id_stmt = sqlsrv_query($connection, $id_sql);
                    if ($id_stmt && $id_result = sqlsrv_fetch_array($id_stmt, SQLSRV_FETCH_ASSOC)) {
                        echo "Inserted record ID: " . $id_result['send_id'] . "<br>";
                        
                        // Clean up test record
                        $delete_sql = "DELETE FROM item_sends WHERE id = ?";
                        $delete_stmt = sqlsrv_prepare($connection, $delete_sql, array($id_result['send_id']));
                        if ($delete_stmt && sqlsrv_execute($delete_stmt)) {
                            echo "✅ Test record cleaned up<br>";
                        }
                    }
                } else {
                    echo "❌ Execute failed: " . print_r(sqlsrv_errors(), true) . "<br>";
                }
            }
        } catch (Exception $e) {
            echo "❌ Insert test failed: " . $e->getMessage() . "<br>";
        }
        
        sqlsrv_close($connection);
    }
} catch (Exception $e) {
    echo "❌ Connection test failed: " . $e->getMessage() . "<br>";
}

// Test 6: Test send_item.php directly
echo "<h3>6. API Test</h3>";
echo "To test the API, send a POST request to send_item.php with this data:<br>";
echo "<pre>";
echo json_encode([
    'playerUsername' => 'test_player',
    'itemCode' => '0000007B00000000',
    'optionsCode' => '****************',
    'quantity' => 1,
    'duration' => 31,
    'sendMethod' => 'mail',
    'adminUsername' => 'test_admin'
], JSON_PRETTY_PRINT);
echo "</pre>";

echo "<h3>7. JavaScript Test</h3>";
echo "You can test from browser console with:<br>";
echo "<pre>";
echo "fetch('send_item.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        playerUsername: 'test_player',
        itemCode: '0000007B00000000',
        optionsCode: '****************',
        quantity: 1,
        duration: 31,
        sendMethod: 'mail',
        adminUsername: 'test_admin'
    })
}).then(r => r.json()).then(console.log);";
echo "</pre>";

echo "<h3>8. Common Issues & Solutions</h3>";
echo "<ul>";
echo "<li><strong>sqlsrv extension not loaded:</strong> Install Microsoft SQL Server Driver for PHP</li>";
echo "<li><strong>Connection failed:</strong> Check server name, port, credentials</li>";
echo "<li><strong>Tables don't exist:</strong> Run database_setup_sqlserver.sql</li>";
echo "<li><strong>Permission denied:</strong> Check SQL Server user permissions</li>";
echo "<li><strong>CORS errors:</strong> Check browser console for cross-origin issues</li>";
echo "</ul>";

echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Fix any issues shown above</li>";
echo "<li>Test the API using the JavaScript code</li>";
echo "<li>Check browser console for errors</li>";
echo "<li>Check server error logs</li>";
echo "</ol>";
?>
