<?php
require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>ทดสอบตาราง Elite Dungeon Info</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; }
        .monospace { font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>";

echo "<h1>👑 ทดสอบระบบ Elite Dungeon Info</h1>";

// ฟังก์ชันแปลงเวลา Unix timestamp
function formatUnixTime($timestamp) {
    if ($timestamp <= 0) {
        return 'ไม่เคยรีเซ็ต';
    }
    return date('d/m/Y H:i:s', $timestamp);
}

// ฟังก์ชันแปลง varbinary เป็น hex string
function formatBinaryData($binaryData) {
    if (empty($binaryData) || $binaryData === '0x') {
        return 'ไม่มีข้อมูล';
    }
    
    // แปลง binary เป็น hex และแสดงแค่ส่วนแรก
    $hex = bin2hex($binaryData);
    if (strlen($hex) > 32) {
        return substr($hex, 0, 32) . '...';
    }
    return $hex;
}

// ทดสอบการเชื่อมต่อฐานข้อมูล
echo "<div class='test-section'>
        <h3>🔌 ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";

$conn = db_connect();

if (isset($conn) && $conn !== false) {
    echo "<p class='success'>✅ เชื่อมต่อ Database สำเร็จ</p>";
    
    // ทดสอบ Query พื้นฐาน
    $testQuery = "SELECT @@VERSION AS SQLVersion, DB_NAME() AS DatabaseName";
    $testResult = sqlsrv_query($conn, $testQuery);
    
    if ($testResult) {
        $dbInfo = sqlsrv_fetch_array($testResult, SQLSRV_FETCH_ASSOC);
        echo "<p class='info'>📊 Database: " . htmlspecialchars($dbInfo['DatabaseName']) . "</p>";
        echo "<p class='info'>🔧 SQL Server Version: " . htmlspecialchars($dbInfo['SQLVersion']) . "</p>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถเชื่อมต่อ Database ได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// ทดสอบการมีอยู่ของตาราง cabal_elite_dungeon_info
echo "<div class='test-section'>
        <h3>📋 ตรวจสอบตาราง cabal_elite_dungeon_info</h3>";

$checkTableQuery = "
    SELECT COUNT(*) as TableExists 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'cabal_elite_dungeon_info'
";

$checkTableResult = sqlsrv_query($conn, $checkTableQuery);

if ($checkTableResult) {
    $tableInfo = sqlsrv_fetch_array($checkTableResult, SQLSRV_FETCH_ASSOC);
    
    if ($tableInfo['TableExists'] > 0) {
        echo "<p class='success'>✅ ตาราง cabal_elite_dungeon_info มีอยู่ในระบบ</p>";
        
        // ตรวจสอบโครงสร้างตาราง
        $structureQuery = "
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'cabal_elite_dungeon_info'
            ORDER BY ORDINAL_POSITION
        ";
        
        $structureResult = sqlsrv_query($conn, $structureQuery);
        
        if ($structureResult) {
            echo "<h4>📊 โครงสร้างตาราง:</h4>";
            echo "<table>";
            echo "<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th><th>Default</th></tr>";
            
            while ($column = sqlsrv_fetch_array($structureResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['COLUMN_NAME']) . "</td>";
                echo "<td>" . htmlspecialchars($column['DATA_TYPE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['IS_NULLABLE']) . "</td>";
                echo "<td>" . htmlspecialchars($column['COLUMN_DEFAULT'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ตรวจสอบข้อมูลในตาราง
        echo "<h4>📈 สถิติข้อมูล:</h4>";
        
        // จำนวนแถวทั้งหมด
        $countQuery = "SELECT COUNT(*) as total_rows FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info";
        $countResult = sqlsrv_query($conn, $countQuery);
        if ($countResult) {
            $countData = sqlsrv_fetch_array($countResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='info'>📊 จำนวนแถวทั้งหมด: " . number_format($countData['total_rows']) . " แถว</p>";
        }
        
        // จำนวนผู้เล่นที่มีข้อมูล Elite Dungeon (ไม่ใช่ default)
        $activeQuery = "SELECT COUNT(*) as active_players FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE EliteDungeonData != 0x";
        $activeResult = sqlsrv_query($conn, $activeQuery);
        if ($activeResult) {
            $activeData = sqlsrv_fetch_array($activeResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='success'>✅ ผู้เล่นที่มีข้อมูล Elite Dungeon: " . number_format($activeData['active_players']) . " คน</p>";
        }
        
        // จำนวนผู้เล่นที่มี ResetTime > 0
        $resetQuery = "SELECT COUNT(*) as reset_players FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE ResetTime > 0";
        $resetResult = sqlsrv_query($conn, $resetQuery);
        if ($resetResult) {
            $resetData = sqlsrv_fetch_array($resetResult, SQLSRV_FETCH_ASSOC);
            echo "<p class='warning'>⚠️ ผู้เล่นที่เคยรีเซ็ต: " . number_format($resetData['reset_players']) . " คน</p>";
        }
        
        // ResetTime ล่าสุด
        $latestResetQuery = "SELECT MAX(ResetTime) as latest_reset FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE ResetTime > 0";
        $latestResetResult = sqlsrv_query($conn, $latestResetQuery);
        if ($latestResetResult) {
            $latestResetData = sqlsrv_fetch_array($latestResetResult, SQLSRV_FETCH_ASSOC);
            $latestReset = $latestResetData['latest_reset'] ?? 0;
            echo "<p class='info'>🕒 รีเซ็ตล่าสุด: " . formatUnixTime($latestReset) . "</p>";
        }
        
        // แสดงข้อมูลตัวอย่าง 5 แถวแรก
        echo "<h4>📋 ข้อมูลตัวอย่าง (5 แถวแรก):</h4>";
        $sampleQuery = "SELECT TOP 5 * FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info ORDER BY ResetTime DESC";
        $sampleResult = sqlsrv_query($conn, $sampleQuery);
        
        if ($sampleResult) {
            echo "<table>";
            echo "<tr><th>CharacterIdx</th><th>EliteDungeonData</th><th>ResetTime</th><th>ResetTime (Formatted)</th></tr>";
            
            while ($sample = sqlsrv_fetch_array($sampleResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($sample['CharacterIdx']) . "</td>";
                echo "<td class='monospace'>" . htmlspecialchars(formatBinaryData($sample['EliteDungeonData'])) . "</td>";
                echo "<td>" . htmlspecialchars($sample['ResetTime']) . "</td>";
                echo "<td>" . htmlspecialchars(formatUnixTime($sample['ResetTime'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // แสดงสถิติการรีเซ็ตรายวัน (7 วันล่าสุด)
        echo "<h4>📊 สถิติการรีเซ็ตรายวัน (7 วันล่าสุด):</h4>";
        $dailyResetQuery = "
            SELECT 
                CAST(DATEADD(second, ResetTime, '1970-01-01') AS DATE) as ResetDate,
                COUNT(*) as ResetCount
            FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info
            WHERE ResetTime > 0 
                AND DATEADD(second, ResetTime, '1970-01-01') >= DATEADD(day, -7, GETDATE())
            GROUP BY CAST(DATEADD(second, ResetTime, '1970-01-01') AS DATE)
            ORDER BY ResetDate DESC
        ";
        
        $dailyResetResult = sqlsrv_query($conn, $dailyResetQuery);
        
        if ($dailyResetResult) {
            echo "<table>";
            echo "<tr><th>วันที่</th><th>จำนวนการรีเซ็ต</th></tr>";
            
            $hasResetData = false;
            while ($dailyReset = sqlsrv_fetch_array($dailyResetResult, SQLSRV_FETCH_ASSOC)) {
                $hasResetData = true;
                $resetDate = $dailyReset['ResetDate'];
                if (is_object($resetDate)) {
                    $resetDateFormatted = $resetDate->format('d/m/Y');
                } else {
                    $resetDateFormatted = $resetDate;
                }
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($resetDateFormatted) . "</td>";
                echo "<td>" . number_format($dailyReset['ResetCount']) . " ครั้ง</td>";
                echo "</tr>";
            }
            
            if (!$hasResetData) {
                echo "<tr><td colspan='2' class='text-center'>ไม่มีข้อมูลการรีเซ็ตใน 7 วันล่าสุด</td></tr>";
            }
            
            echo "</table>";
        }
        
        // ตรวจสอบการเชื่อมโยงกับตาราง character
        echo "<h4>🔗 ทดสอบการเชื่อมโยงกับตาราง Character:</h4>";
        $joinTestQuery = "
            SELECT TOP 5 
                ed.CharacterIdx, 
                ed.ResetTime,
                ct.Name,
                ct.LEV,
                ct.Style,
                auth.UserNum,
                auth.ID as UserID
            FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info ed
            LEFT JOIN [".DATABASE_SV."].[dbo].cabal_character_table ct ON ed.CharacterIdx = ct.CharacterIdx
            LEFT JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table auth ON FLOOR(ct.CharacterIdx/16) = auth.UserNum
            WHERE ed.ResetTime > 0
            ORDER BY ed.ResetTime DESC
        ";
        
        $joinTestResult = sqlsrv_query($conn, $joinTestQuery);
        
        if ($joinTestResult) {
            echo "<p class='success'>✅ การเชื่อมโยงกับตาราง Character และ Auth สำเร็จ</p>";
            echo "<table>";
            echo "<tr><th>UserNum</th><th>Username</th><th>CharacterIdx</th><th>ชื่อตัวละคร</th><th>Level</th><th>Style</th><th>ResetTime</th></tr>";
            
            while ($joinData = sqlsrv_fetch_array($joinTestResult, SQLSRV_FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($joinData['UserNum'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($joinData['UserID'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($joinData['CharacterIdx']) . "</td>";
                echo "<td>" . htmlspecialchars($joinData['Name'] ?? 'N/A') . "</td>";
                echo "<td>Lv." . htmlspecialchars($joinData['LEV'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($joinData['Style'] ?? 'N/A') . "</td>";
                echo "<td>" . formatUnixTime($joinData['ResetTime']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ ไม่สามารถเชื่อมโยงกับตาราง Character ได้</p>";
            if (sqlsrv_errors()) {
                echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
            }
        }
        
    } else {
        echo "<p class='error'>❌ ไม่พบตาราง cabal_elite_dungeon_info</p>";
        echo "<p class='warning'>⚠️ กรุณาสร้างตารางด้วย SQL ต่อไปนี้:</p>";
        echo "<pre>
CREATE TABLE [".DATABASE_SV."].[dbo].[cabal_elite_dungeon_info] (
    [CharacterIdx] int NOT NULL,
    [EliteDungeonData] varbinary(1200) DEFAULT 0x NOT NULL,
    [ResetTime] bigint DEFAULT 0 NOT NULL,
    PRIMARY KEY ([CharacterIdx])
);
        </pre>";
    }
} else {
    echo "<p class='error'>❌ ไม่สามารถตรวจสอบตารางได้</p>";
    if (sqlsrv_errors()) {
        echo "<pre class='error'>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
}

echo "</div>";

// สรุปผลการทดสอบ
echo "<div class='test-section'>
        <h3>📋 สรุปผลการทดสอบ</h3>";

$issues = [];

// ตรวจสอบปัญหาต่างๆ
if (!isset($conn) || $conn === false) {
    $issues[] = "ไม่สามารถเชื่อมต่อ Database ได้";
}

if (!isset($tableInfo) || $tableInfo['TableExists'] == 0) {
    $issues[] = "ไม่พบตาราง cabal_elite_dungeon_info";
}

if (empty($issues)) {
    echo "<p class='success'>🎉 <strong>ระบบพร้อมใช้งาน!</strong> ไม่พบปัญหาใดๆ</p>";
    echo "<p class='info'>✅ สามารถใช้งานระบบตรวจสอบ Elite Dungeon Info ได้แล้ว</p>";
    echo "<p><a href='manage-elite-dungeon.php' style='color: blue;'>👉 ไปยังหน้าจัดการ Elite Dungeon</a></p>";
} else {
    echo "<p class='error'>❌ <strong>พบปัญหา:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>$issue</li>";
    }
    echo "</ul>";
    echo "<p class='warning'>⚠️ กรุณาแก้ไขปัญหาข้างต้นก่อนใช้งาน</p>";
}

echo "</div>";

echo "</body></html>";
?>
