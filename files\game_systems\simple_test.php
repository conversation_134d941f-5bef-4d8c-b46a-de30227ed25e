<?php
echo "<h2>Simple Test</h2>";

// ทดสอบการเชื่อมต่อฐานข้อมูล
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

if ($conn) {
    echo "<p style='color: green;'>✅ Database connected</p>";
    
    // ทดสอบการสร้าง table
    $createSql = "
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_Code' AND xtype='U')
    CREATE TABLE [dbo].[WEB_Redeem_Code] (
        [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
        [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
        [quantity] int NULL,
        [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
        [datecreated] datetime DEFAULT getdate() NULL,
        [expiry_date] datetime NULL
    )";
    
    $createStmt = sqlsrv_query($conn, $createSql);
    if ($createStmt) {
        echo "<p style='color: green;'>✅ Table created/exists</p>";
        
        // ทดสอบการเพิ่มข้อมูล
        $testCode = 'TEST-' . rand(1000, 9999) . '-' . rand(1000, 9999);
        $insertSql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status) VALUES (?, ?, ?, '1')";
        $insertStmt = sqlsrv_query($conn, $insertSql, [$testCode, '1:0:31', 1]);
        
        if ($insertStmt) {
            echo "<p style='color: green;'>✅ Insert successful: $testCode</p>";
            
            // ลบข้อมูลทดสอบ
            $deleteSql = "DELETE FROM WEB_Redeem_Code WHERE code = ?";
            sqlsrv_query($conn, $deleteSql, [$testCode]);
            echo "<p style='color: blue;'>🗑️ Test data cleaned</p>";
        } else {
            echo "<p style='color: red;'>❌ Insert failed: " . print_r(sqlsrv_errors(), true) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Table creation failed: " . print_r(sqlsrv_errors(), true) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
}

// ทดสอบ session
session_start();
if (isset($_SESSION['userLogin'])) {
    echo "<p style='color: green;'>✅ Session exists</p>";
} else {
    echo "<p style='color: red;'>❌ No session</p>";
}

echo "<h3>Manual Code Generation Test</h3>";

function generateCode() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $format = 'XXXX-XXXX-XXXX-XXXX';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

for ($i = 0; $i < 5; $i++) {
    $code = generateCode();
    echo "<p>Generated code $i: <code>$code</code></p>";
}
?>
