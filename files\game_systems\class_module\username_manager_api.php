<?php
session_start();
if (!isset($_SESSION['userLogin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่มีการ login']);
    exit;
}

require_once("../../../_app/dbinfo.inc.php");
require_once("../../../_app/general_config.inc.php");

$conn = db_connect();

// รับข้อมูลจาก POST
$action = $_POST['action'] ?? '';
$table = $_POST['table'] ?? '';
$username = $_POST['username'] ?? '';
$id = $_POST['id'] ?? '';

// ตรวจสอบ table ที่อนุญาต
$allowedTables = [
    'WEB_Discord_boost_userLog',
    'WEB_Donate_Title_userLog', 
    'WEB_Event_RewardLog',
    'WEB_EventOBT_DataLog',
    'WEB_Facebook_Share',
    'WEB_Gamer_Steamer_userLog'
];

if (!in_array($table, $allowedTables)) {
    echo json_encode(['success' => false, 'message' => 'Table ไม่ถูกต้อง']);
    exit;
}

switch ($action) {
    case 'list':
        listUsernames($conn, $table);
        break;

    case 'add':
        addUsername($conn, $table, $username);
        break;
    
    case 'edit':
        editUsername($conn, $table, $id, $username, $_POST['status'] ?? '1');
        break;

    case 'delete':
        deleteUsername($conn, $table, $id);
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Action ไม่ถูกต้อง']);
        break;
}

function listUsernames($conn, $table) {
    try {
        // ตรวจสอบว่า table มีอยู่จริงหรือไม่
        $checkTableSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
        $checkStmt = sqlsrv_query($conn, $checkTableSql, [$table]);
        
        if (!$checkStmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบ table ได้']);
            return;
        }
        
        $tableExists = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($tableExists['count'] == 0) {
            // สร้าง table ใหม่ถ้าไม่มี
            createTable($conn, $table);
        }
        
        // ดึงข้อมูลจาก table
        $sql = "SELECT id, userid, status, added_time FROM [$table] ORDER BY added_time DESC";
        $stmt = sqlsrv_query($conn, $sql);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถดึงข้อมูลได้']);
            return;
        }
        
        $data = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // แปลง DateTime object เป็น string
            if (isset($row['added_time']) && $row['added_time'] instanceof DateTime) {
                $row['added_time'] = $row['added_time']->format('Y-m-d H:i:s');
            }
            $data[] = $row;
        }
        
        echo json_encode(['success' => true, 'data' => $data]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function addUsername($conn, $table, $username) {
    try {
        if (empty($username)) {
            echo json_encode(['success' => false, 'message' => 'กรุณากรอก Username']);
            return;
        }
        
        // ตรวจสอบว่า table มีอยู่จริงหรือไม่
        $checkTableSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
        $checkStmt = sqlsrv_query($conn, $checkTableSql, [$table]);
        
        if (!$checkStmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบ table ได้']);
            return;
        }
        
        $tableExists = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($tableExists['count'] == 0) {
            // สร้าง table ใหม่ถ้าไม่มี
            createTable($conn, $table);
        }
        
        // ตรวจสอบว่า username ซ้ำหรือไม่
        $checkSql = "SELECT COUNT(*) as count FROM [$table] WHERE userid = ?";
        $checkStmt = sqlsrv_query($conn, $checkSql, [$username]);
        
        if (!$checkStmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
            return;
        }
        
        $exists = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($exists['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Username นี้มีอยู่แล้ว']);
            return;
        }
        
        // เพิ่มข้อมูลใหม่
        $sql = "INSERT INTO [$table] (userid, status, added_time) VALUES (?, 1, GETDATE())";
        $stmt = sqlsrv_query($conn, $sql, [$username]);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถเพิ่มข้อมูลได้']);
            return;
        }
        
        echo json_encode(['success' => true, 'message' => 'เพิ่ม Username สำเร็จ']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function editUsername($conn, $table, $id, $username, $status) {
    try {
        if (empty($id)) {
            echo json_encode(['success' => false, 'message' => 'ไม่มี ID ที่จะแก้ไข']);
            return;
        }

        if (empty($username)) {
            echo json_encode(['success' => false, 'message' => 'กรุณากรอก Username']);
            return;
        }

        // ตรวจสอบว่า username ซ้ำหรือไม่ (ยกเว้น record ปัจจุบัน)
        $checkSql = "SELECT COUNT(*) as count FROM [$table] WHERE userid = ? AND id != ?";
        $checkStmt = sqlsrv_query($conn, $checkSql, [$username, $id]);

        if (!$checkStmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
            return;
        }

        $exists = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($exists['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Username นี้มีอยู่แล้ว']);
            return;
        }

        // อัปเดตข้อมูล
        $sql = "UPDATE [$table] SET userid = ?, status = ? WHERE id = ?";
        $stmt = sqlsrv_query($conn, $sql, [$username, $status, $id]);

        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถแก้ไขข้อมูลได้']);
            return;
        }

        echo json_encode(['success' => true, 'message' => 'แก้ไข Username สำเร็จ']);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function deleteUsername($conn, $table, $id) {
    try {
        if (empty($id)) {
            echo json_encode(['success' => false, 'message' => 'ไม่มี ID ที่จะลบ']);
            return;
        }

        $sql = "DELETE FROM [$table] WHERE id = ?";
        $stmt = sqlsrv_query($conn, $sql, [$id]);

        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
            return;
        }

        echo json_encode(['success' => true, 'message' => 'ลบ Username สำเร็จ']);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function createTable($conn, $tableName) {
    $sql = "CREATE TABLE [$tableName] (
        [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [userid] varchar(50) COLLATE Thai_CI_AS NULL,
        [status] tinyint NULL DEFAULT 1,
        [added_time] datetime NOT NULL DEFAULT GETDATE()
    )";
    
    $stmt = sqlsrv_query($conn, $sql);
    
    if (!$stmt) {
        throw new Exception('ไม่สามารถสร้าง table ได้: ' . print_r(sqlsrv_errors(), true));
    }
    
    return true;
}
?>
