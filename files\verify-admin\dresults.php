<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?> <small><?php echo PT_MANAGEPLAYERS_DESC; ?></small></h1></div>
<script type="text/javascript" charset="utf8" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-2.0.3.js"></script>
<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=manager/donate" class="btn btn-block btn-info">กลับไปยังหน้า ข้อมูลการเติมเงิน</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 10;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM ".DATABASE_CCA.".dbo.truemoney WHERE user_no LIKE '$getResult%' OR password LIKE '$getResult%'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Player not found</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover">
                        <thead>
                             <tr>
                            <th>Card ID</th>
                            <th>password</th>
							<th>User</th>
							<th>ราคาบัตร</th>
							<th>สถานะบัตร</th>
							<th>เวลา</th>
							<th><?php echo T_ACTION; ?></th>
                        </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr
                                    <?php
                                    $selectUsersData = "SELECT * FROM ".DATABASE_CCA.".dbo.truemoney WHERE card_id = '$row[0]'";
                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, array());
                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                                    if($selectUsersDataFetch['AuthType'] == '2' ||
                                            $selectUsersDataFetch['AuthType'] == '3'){ echo ' class="bg-red text-white"'; }
                                ?>>
                                 <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
								<td><?php echo $row[2]; ?></td>
								<td><?
	 switch($row[3])
	 {
		 case 0 : echo '0'; break;
		 case 1 : echo'<font color="green">50</font>'; break;
		 case 2 : echo'<font color="green">90</font>'; break;
		 case 3 : echo'<font color="green">150</font>'; break;
		 case 4 : echo'<font color="green">300</font>'; break;
		 case 5 : echo'<font color="green">500</font>'; break;
		 case 6 : echo'<font color="green">1000</font>'; break;
	 }
	 ?></td>
								
								<td><?
	 switch($row[4])
	 {
		 case 0 : echo 'รอการตรวจสอบ'; break;
		 case 1 : echo'<font color="green">ผ่าน</font>'; break;
		 case 2 : echo'<font color="red">ถูกใช้ไปแล้ว</font>'; break;
		 case 3 : echo'<font color="red">รหัสไม่ถูกต้อง</font>'; break;
		 case 4 : echo'<font color="red">บัตรทรูมูฟ</font>'; break;

	 }
	 ?></td>
								<td><?php echo date('d-m-Y H:i:s',strtotime($row[5])); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                            <?php echo B_ACTION; ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">

										 <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                                <li><a href="?url=manager/see-donate&id=<?php echo $row[2]; ?>"><?php echo B_SEEPLRINFO; ?></a></li>
                                                <li><a href="?url=manager/edit-donate&id=<?php echo $row[2]; ?>">Edit</a></li>
                                            <?php } ?>
                                                </ul>
                                            </div>
                                        </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/tickets&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript">
$(document).ready(function(){
$('.pagination').pagination({
        items: <?php echo $rowsReturned;?>,
        itemsOnPage: <?php echo $rowsPerPage;?>,
        cssStyle: 'light-theme',
		currentPage : <?php echo $pageNum;?>,
		hrefTextPrefix : '?url=manager/dresults&pageNum='
    });
	});
</script>