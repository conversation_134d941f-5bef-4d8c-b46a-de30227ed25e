<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

// Define database constants if not defined
if (!defined('DATABASE_CCA')) {
    define('DATABASE_CCA', $dbinfo['database']);
}
if (!defined('DATABASE_SV')) {
    define('DATABASE_SV', $dbinfo['database']);
}
if (!defined('DATABASE_ACC')) {
    define('DATABASE_ACC', $dbinfo['database']);
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

/**
 * Send item to Cash Inventory
 */
function sendToCashInventory($conn, $userNum, $itemdex, $inputOption, $inputDur, $quantity = 1) {
    try {
        // Use defined database constant
        $DATABASE_CCA = DATABASE_CCA;

        // Convert parameters to proper types
        $userNum = (int)$userNum;
        $itemdex = (string)$itemdex; // Item code as string
        $inputOption = (string)$inputOption; // Options code as string
        $inputDur = (int)$inputDur;

        // ส่งไอเทมตามจำนวน quantity ที่กำหนด (แต่ละรอบส่ง 1 ชิ้น)
        for ($i = 0; $i < $quantity; $i++) {
            $sql = "EXECUTE [" . $DATABASE_CCA . "].[dbo].up_AddMyCashItemByItem ?, ?, ?, ?, ?, ?, ?";
            $params = array(
                $userNum,
                '0',
                1, // ส่งทีละ 1 ชิ้นในแต่ละรอบ
                $itemdex,
                $inputOption,
                $inputDur,
                'Web Management System'
            );

            error_log("Cash inventory params (item " . ($i + 1) . "/$quantity): " . print_r($params, true));

            $stmt = sqlsrv_prepare($conn, $sql, $params);

            if ($stmt === false || !sqlsrv_execute($stmt)) {
                $errors = sqlsrv_errors();
                $errorMessage = "Cash inventory failed for item " . ($i + 1) . ": ";
                foreach ($errors as $error) {
                    $errorMessage .= $error['message'] . " ";
                }
                throw new Exception($errorMessage);
            }
        }

        return ['success' => true, 'method' => 'cash_inventory', 'params' => $params];

    } catch (Exception $e) {
        error_log("sendToCashInventory error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Send item via Mail System
 */
function sendToMail($conn, $usernum, $itemid, $option, $duration, $quantity = 1) {
    try {
        // Convert parameters to proper types
        $usernum = (int)$usernum;
        $itemid = (string)$itemid; // Item code as bigint (string)
        $option = (string)$option; // Options code as bigint (string)
        $duration = (int)$duration;

        $title = 'Item from Admin';
        $content = 'You have received an item from the administration team.';
        $expirationDay = null; // No expiration

        // ส่งไอเทมตามจำนวน quantity ที่กำหนด
        for ($i = 0; $i < $quantity; $i++) {
            $sql = "EXECUTE [" . DATABASE_SV . "].[dbo].cabal_sp_mail_send_GM ?, ?, ?, ?, ?, ?, ?";
            $params = array(
                $usernum,
                $title,
                $content,
                $itemid,
                $option,
                $duration,
                $expirationDay
            );

            error_log("Mail params (item " . ($i + 1) . "/$quantity): " . print_r($params, true));

            $stmt = sqlsrv_prepare($conn, $sql, $params);

            if ($stmt === false || !sqlsrv_execute($stmt)) {
                $errors = sqlsrv_errors();
                $errorMessage = "Mail send failed for item " . ($i + 1) . ": ";
                foreach ($errors as $error) {
                    $errorMessage .= $error['message'] . " ";
                }
                throw new Exception($errorMessage);
            }
        }

        return ['success' => true, 'method' => 'mail', 'params' => $params];

    } catch (Exception $e) {
        error_log("sendToMail error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Send item to Event Inventory
 */
function sendToEventInventory($conn, $usernum, $itemid, $option, $duration, $quantity = 1) {
    try {
        // Use defined database constant
        $DATABASE_SV = DATABASE_SV;

        // Convert parameters to proper types
        $usernum = (int)$usernum;
        $itemid = (string)$itemid; // Item code as string
        $option = (string)$option; // Options code as string
        $duration = (int)$duration;

        $expireDay = 0; // No expiration
        $registeDateTime = date('Y-m-d H:i:s');

        // ส่งไอเทมตามจำนวน quantity ที่กำหนด (แต่ละรอบส่ง 1 ชิ้น)
        for ($i = 0; $i < $quantity; $i++) {
            $sql = "EXECUTE [" . $DATABASE_SV . "].[dbo].cabal_sp_event_inventory_reward ?, ?, ?, ?, ?, ?, ?, ?";
            $params = array(
                $usernum,
                $itemid,
                $option,
                $duration,
                1, // ส่งทีละ 1 ชิ้นในแต่ละรอบ
                "0",
                $expireDay,
                $registeDateTime
            );

            error_log("Event inventory params (item " . ($i + 1) . "/$quantity): " . print_r($params, true));

            $stmt = sqlsrv_prepare($conn, $sql, $params);

            if ($stmt === false || !sqlsrv_execute($stmt)) {
                $errors = sqlsrv_errors();
                $errorMessage = "Event inventory failed for item " . ($i + 1) . ": ";
                foreach ($errors as $error) {
                    $errorMessage .= $error['message'] . " ";
                }
                throw new Exception($errorMessage);
            }
        }

        return ['success' => true, 'method' => 'event_inventory', 'params' => $params];

    } catch (Exception $e) {
        error_log("sendToEventInventory error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Create notification for item send
 */
function createNotification($conn, $data) {
    try {
        $notification_id = uniqid('notif_', true);
        $title = "Item Sent Successfully";
        $message = "Item sent to {$data['player_username']} via {$data['send_method']}";
        $type = 'item_send';
        $priority = 'normal';
        $created_at = date('Y-m-d H:i:s');
        $is_read = 0;
        $admin_username = $data['admin_username'] ?? 'System';

        $details = json_encode([
            'send_id' => $data['send_id'],
            'player_username' => $data['player_username'],
            'item_id' => $data['item_id'],
            'item_code' => $data['item_code'],
            'options_code' => $data['options_code'],
            'quantity' => $data['quantity'],
            'duration' => $data['duration'],
            'send_method' => $data['send_method'],
            'status' => $data['status']
        ]);

        $sql = "INSERT INTO notifications (
            notification_id, title, message, type, priority, details,
            admin_username, created_at, is_read, expires_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, DATEADD(day, 7, GETDATE()))";

        $params = array(
            &$notification_id, &$title, &$message, &$type, &$priority, &$details,
            &$admin_username, &$created_at, &$is_read
        );

        $stmt = sqlsrv_prepare($conn, $sql, $params);

        if ($stmt === false || !sqlsrv_execute($stmt)) {
            // Don't throw error for notification failure, just log it
            $errors = sqlsrv_errors();
            error_log("Failed to create notification: " . print_r($errors, true));
            return null;
        }

        return $notification_id;

    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
        // Don't throw error for notification failure
        return null;
    }
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Debug logging
    error_log("Send Item API called");
    error_log("Raw input: " . $input);
    error_log("Parsed data: " . print_r($data, true));

    if (!$data) {
        error_log("JSON decode failed: " . json_last_error_msg());
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }

    // Validate required fields
    $required_fields = ['playerUsername', 'itemCode', 'optionsCode', 'quantity', 'duration', 'sendMethod'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }

    // Validate data
    if ((int)$data['quantity'] <= 0) {
        throw new Exception('Quantity must be greater than 0');
    }

    if (empty($data['itemCode'])) {
        throw new Exception('Invalid item code');
    }

    $allowed_methods = ['inventory', 'mail', 'warehouse'];
    if (!in_array($data['sendMethod'], $allowed_methods)) {
        throw new Exception('Invalid send method');
    }

    // Check if player exists
    $player_sql = "SELECT UserNum, ID FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE ID = ?";
    $player_stmt = sqlsrv_prepare($conn, $player_sql, array(&$data['playerUsername']));

    if ($player_stmt === false || !sqlsrv_execute($player_stmt)) {
        throw new Exception("Error checking player existence");
    }

    $player = sqlsrv_fetch_array($player_stmt, SQLSRV_FETCH_ASSOC);
    if (!$player) {
        error_log("Player not found: " . $data['playerUsername']);
        throw new Exception("Player '{$data['playerUsername']}' not found in database");
    }

    error_log("Player found: " . print_r($player, true));

    // Convert hex codes to decimal for game database
    $item_code_hex = $data['itemCode'];
    $options_code_hex = $data['optionsCode'];

    // Extract item ID from item code (first 8 characters typically represent item ID)
    $item_id = hexdec(substr($item_code_hex, 0, 8));
    $item_name = 'Item_' . $item_id;

    // Convert full codes to decimal for stored procedures
    $item_code_decimal = hexdec($item_code_hex);
    $options_code_decimal = hexdec($options_code_hex);

    error_log("Item conversion: ID=$item_id, ItemCode=$item_code_decimal, OptionsCode=$options_code_decimal");
    
    // Prepare item data
    $player_id = (int)$player['UserNum'];
    $player_username = $player['ID'];
    $quantity = (int)$data['quantity'];
    $duration = (int)$data['duration'];
    $send_method = $data['sendMethod'];
    $item_code = $item_code_hex; // Store hex version
    $options_code = $options_code_hex; // Store hex version
    $admin_username = $data['adminUsername'] ?? 'System';
    $created_at = date('Y-m-d H:i:s');
    $status = 'pending';
    
    // Insert into item_sends table
    $insert_sql = "INSERT INTO item_sends (
        player_id, player_username, item_id, item_name, quantity, duration, send_method,
        item_code, options_code, admin_username, created_at, status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $insert_params = array(
        &$player_id, &$player_username, &$item_id, &$item_name, &$quantity, &$duration, &$send_method,
        &$item_code, &$options_code, &$admin_username, &$created_at, &$status
    );

    $insert_stmt = sqlsrv_prepare($conn, $insert_sql, $insert_params);

    if ($insert_stmt === false || !sqlsrv_execute($insert_stmt)) {
        $errors = sqlsrv_errors();
        $errorMessage = "Insert failed: ";
        foreach ($errors as $error) {
            $errorMessage .= $error['message'] . " ";
        }
        throw new Exception($errorMessage);
    }

    // Get the inserted ID
    $id_sql = "SELECT SCOPE_IDENTITY() as send_id";
    $id_stmt = sqlsrv_query($conn, $id_sql);
    $id_result = sqlsrv_fetch_array($id_stmt, SQLSRV_FETCH_ASSOC);
    $send_id = $id_result['send_id'];
    
    // Send item to game database based on method
    $final_status = 'pending';
    $game_result = null;

    try {
        if ($data['sendMethod'] === 'inventory') {
            // Send to Cash Inventory - uses hex codes as strings
            $game_result = sendToCashInventory($conn, $player_id, $item_code_hex, $options_code_hex, $duration, $quantity);
            $final_status = 'sent_to_cash_inventory';

        } elseif ($data['sendMethod'] === 'mail') {
            // Send via Mail System - uses decimal codes as strings for bigint
            $game_result = sendToMail($conn, $player_id, (string)$item_code_hex, (string)$options_code_hex, $duration, $quantity);
            $final_status = 'sent_to_mail';

        } elseif ($data['sendMethod'] === 'warehouse') {
            // Send to Event Inventory - uses hex codes as strings
            $game_result = sendToEventInventory($conn, $player_id, $item_code_hex, $options_code_hex, $duration, $quantity);
            $final_status = 'sent_to_event_inventory';

        } else {
            throw new Exception('Invalid send method: ' . $data['sendMethod']);
        }

        error_log("Game system result: " . print_r($game_result, true));

    } catch (Exception $e) {
        error_log("Game system error: " . $e->getMessage());
        $final_status = 'failed';
        $error_message = $e->getMessage();

        // Update with error
        $error_sql = "UPDATE item_sends SET status = ?, error_message = ? WHERE id = ?";
        $error_params = array(&$final_status, &$error_message, &$send_id);
        $error_stmt = sqlsrv_prepare($conn, $error_sql, $error_params);
        sqlsrv_execute($error_stmt);

        throw $e;
    }

    // Update status
    $update_sql = "UPDATE item_sends SET status = ?, processed_at = GETDATE() WHERE id = ?";
    $update_params = array(&$final_status, &$send_id);
    $update_stmt = sqlsrv_prepare($conn, $update_sql, $update_params);

    if ($update_stmt === false || !sqlsrv_execute($update_stmt)) {
        $errors = sqlsrv_errors();
        $errorMessage = "Update failed: ";
        foreach ($errors as $error) {
            $errorMessage .= $error['message'] . " ";
        }
        throw new Exception($errorMessage);
    }
    
    // Log the transaction
    $log_details = json_encode([
        'action' => 'send_item',
        'item_id' => $item_id,
        'item_code' => $item_code,
        'options_code' => $options_code,
        'quantity' => $quantity,
        'duration' => $duration,
        'method' => $send_method,
        'send_id' => $send_id
    ]);

    $log_sql = "INSERT INTO admin_logs (admin_username, action, target_player, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $log_params = array(
        &$admin_username,
        'Send Item',
        &$player_username,
        &$log_details,
        $_SERVER['REMOTE_ADDR'] ?? null,
        $_SERVER['HTTP_USER_AGENT'] ?? null,
        &$created_at
    );

    $log_stmt = sqlsrv_prepare($conn, $log_sql, $log_params);
    if ($log_stmt === false || !sqlsrv_execute($log_stmt)) {
        // Log error but don't fail the transaction
        error_log("Failed to log admin action");
    }
    
    // Create notification
    $notification_data = [
        'send_id' => $send_id,
        'player_username' => $player_username,
        'item_id' => $item_id,
        'item_code' => $item_code,
        'options_code' => $options_code,
        'quantity' => $quantity,
        'duration' => $duration,
        'send_method' => $send_method,
        'status' => $final_status,
        'admin_username' => $admin_username
    ];

    $notification_id = createNotification($conn, $notification_data);
    error_log("Notification created: " . ($notification_id ? $notification_id : 'failed'));

    // Commit transaction
    sqlsrv_commit($conn);

    // Return final success response
    http_response_code(200);

    $final_response = [
        'success' => true,
        'message' => 'Item sent successfully',
        'data' => [
            'send_id' => $send_id,
            'player' => $player['ID'],
            'player_id' => $player_id,
            'item_id' => $item_id,
            'item_name' => $item_name,
            'item_code' => $item_code_hex,
            'item_code_decimal' => $item_code_decimal,
            'options_code' => $options_code_hex,
            'options_code_decimal' => $options_code_decimal,
            'quantity' => $quantity,
            'duration' => $duration,
            'method' => $send_method,
            'status' => $final_status,
            'timestamp' => $created_at,
            'game_result' => $game_result,
            'notification_id' => $notification_id,
            'processed_at' => date('Y-m-d H:i:s')
        ],
        'final_results' => [
            'item_delivered' => true,
            'notification_sent' => $notification_id ? true : false,
            'database_logged' => true,
            'transaction_completed' => true
        ]
    ];

    // Log final response
    error_log("Final response: " . json_encode($final_response));

    // Send final response
    echo json_encode($final_response);
    exit;
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        sqlsrv_rollback($conn);
        sqlsrv_close($conn);
    }

    error_log("Error in send_item.php: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Close connection if still open
if (isset($conn)) {
    sqlsrv_close($conn);
}
?>
