<?php
require ('../../_app/dbinfo.inc.php');
require ('../../_app/ssh.inc.php');
require '../../vendor/autoload.php';

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use phpseclib3\Net\SSH2;

$ssh = ssh_connect(); // สมมติว่าฟังก์ชันนี้อยู่ใน ssh-inc.php
$ssh->setTimeout(30); // ตั้ง timeout สำหรับ SSH
$uptime = $ssh->exec('uptime');

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    
    $newIp = isset($_POST["ip"]) ? $_POST["ip"] : null;
    $userNum = $_POST["usernum"];

    

if (!empty($newIp) && filter_var(trim($newIp), FILTER_VALIDATE_IP)) {
    
        //ลบกฎทั้งหมดที่เกี่ยวข้องกับ NewIp
        $deleteExistingCommand = "sudo iptables -D DOCKER -s $newIp -d **********/32 ! -i docker0 -o docker0 -p tcp -m multiport --dports 35001,35002,35003 -j ACCEPT";
        
        while (true) {
            $deleteResult = $ssh->exec($deleteExistingCommand);
            if (strpos($deleteResult, "Bad rule") !== false) {
                break; // หยุดลบถ้ากฎไม่มีอยู่แล้ว
            }
        }

        // ลบกฎเก่าที่เกี่ยวกับ OldIp ถ้ามีค่า
        if (!empty($newIp) && filter_var($newIp, FILTER_VALIDATE_IP)) {
            $deleteCommand = "sudo iptables -D DOCKER -s $newIp -d **********/32 ! -i docker0 -o docker0 -p tcp -m multiport --dports 35001,35002,35003 -j ACCEPT";
            $ssh->exec($deleteCommand);
        }

        // เพิ่มกฎใหม่สำหรับ NewIp
        //$addCommand = "sudo iptables -A DOCKER -s $newIp -d **********/32 ! -i docker0 -o docker0 -p tcp -m multiport --dports 35001,35002,35003 -j ACCEPT";
        $addCommand = "sudo iptables -I DOCKER 1 -s $newIp -d **********/32 ! -i docker0 -o docker0 -p tcp -m multiport --dports 35001,35002,35003 -j ACCEPT";
        
        $ssh->exec($addCommand);

        $backupCommand = "sudo cp /etc/iptables/rules.v4 /etc/iptables/rules-backup-$(date +'%Y-%m-%d_%H-%M-%S').v4";
        $ssh->exec($backupCommand);

        // บันทึกกฎ iptables
        $ssh->exec("sudo iptables-save > /etc/iptables/rules.v4");

        $ssh->exec("sudo systemctl restart netfilter-persistent");

         // อัปเดต `NewIp` และ `UserNum` ในฐานข้อมูล
        $updateSQL = "UPDATE Account.dbo.cabal_auth_table SET NewIp = ? WHERE UserNum = ?";
        $params = array($newIp, $userNum);
        $stmt = sqlsrv_query($conn, $updateSQL, $params);

        if ($stmt === false) {
            echo "Error: ไม่สามารถอัปเดตฐานข้อมูลได้";
        } else {
            echo "IP $newIp ถูกเพิ่มไปยัง Server และอัปเดตฐานข้อมูลสำเร็จ!";
        }

        echo "IP $newIp ถูกเพิ่มไปยัง Server สำเร็จ!";
    } else {
        echo "Error: New IP address is invalid.";
    }
    exit();
}
?>