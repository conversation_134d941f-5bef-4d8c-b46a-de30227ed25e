<?php 
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn); ?>

<div class="col-lg-12">
                        <section class="panel">
                            <header class="panel-heading">
                            <h3><?php echo PT_SERVERLOG; ?> <small><?php echo PT_SERVERLOG_DESC; ?></small></h3>
                            </header>
				                <div class="panel-body">
<div class="row">
    <div class="panel panel-default">
        <div class="panel-heading" role="tab" id="headingOne">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                    <?php echo H_CLICK_SEE_DETAILS; ?>
                </a>
            </h4>
        </div>
        <div id="collapseOne" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne">
            <div class="panel-body no-padd">
                <table class="table no-margn" style="border: 1px solid #CCC;">
                    <thead>
                        <tr>
                            <th>Cheat ID</th>
                            <th>Cheat name</th>
                            <th>Cheat description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <p class="text-red" style="margin: 10px;">
                            <?php echo T_SERVERLOG_INFO; ?> &middot; <?php echo T_ENGLISHONLY; ?>
                        </p>
                        <tr class="active">
                            <th>1</th>
                            <th>Backpack</th>
                            <th>Backpack problems</th>
                        </tr>
                        <tr class="warning">
                            <th>13</th>
                            <th>API</th>
                            <th>All returns or problems with api</th>
                        </tr>
                        <tr class="danger">
                            <th>14</th>
                            <th>flying or other cheats</th>
                            <th>All info about flying cheat or others</th>
                        </tr>
                        <tr class="warning">
                            <th>17</th>
                            <th>Bad event</th>
                            <th>Any wrong event called by api or something</th>
                        </tr>
                        <tr class="danger">
                            <th>19</th>
                            <th>Improved detection</th>
                            <th>Problems with pickup fast items or probably cheating</th>
                        </tr>
                        <tr class="danger">
                            <th>21</th>
                            <th>Press key</th>
                            <th>The user pressed probably activation key cheat</th>
                        </tr>
                        <tr class="danger">
                            <th>26</th>
                            <th>Camera hack or speed hack</th>
                            <th>Players using camera or speed hacking</th>
                        </tr>
                        <tr class="success">
                            <th>33</th>
                            <th>Admin Cheat (commands, respawns, etc)</th>
                            <th>Action executed by the admin like commands or respawn items, cars etc.</th>
                        </tr>
                        <tr class="info">
                            <th>99</th>
                            <th>Disconnect peer</th>
                            <th>Player disconnect because any problem or normal disconnecting</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-body no-padd">
            <div class="col-lg-12 no-padd">
                <table class="table no-margn">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>CustomerID</th>
                            <th>Customer IP</th>
                            <th>Gamertag</th>
                            <th>CheatID</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 10;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_hackuser_list ORDER BY releaseDate DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr<?php
                            // 1 = backpack (unknown id or wrong backpack)
                            // 13 = api reports                                
                            // 14 = flying cheat
                            // 17 = bad event
                            // 19 = Improved detection
                            // 21 = press key (probably active hack key)
                            // 26 = camerahack or speed hack
                            // 33 = Admin Cheat (commands, respawns and other actions)
                            // 99 = disconnect peer
                            // If log is admin cheat or dev actions the background is setted to green or success because is dev
                            // minor problems like api reports the background is light yellow or warning
                            // disconnect peer is an only info so background is blue or info
                            // other cheats ids the background is red because it can be a real cheat
                            echo $class = ($row[7] == '33' ? ' class="success"' :
                                    ($row[7] == '14' || $row[7] == '26' || $row[7] == '19' || $row[7] == '21' ? ' class="danger"' :
                                            ($row[7] == '13' || $row[7] == '17' ? ' class="warning"' :
                                                    ($row[7] == '99' ? ' class="info"' : ' class="active"'))));
                            ?>>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[3]; ?></td>
                                <td><?php echo $row[5]; ?></td>
                                <td><?php echo $gamertag = (!$row[9] ? 'SYSTEM' : $row[9]); ?></td>
                                <td><?php echo $row[7]; ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                            <?php echo B_ACTION; ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a href="?url=server-admin/see-log&id=<?php echo $row[0]; ?>"><?php echo B_SEEALLINFO; ?></a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=server-admin/serverlog&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=server-admin/serverlog&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=server-admin/serverlog&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>