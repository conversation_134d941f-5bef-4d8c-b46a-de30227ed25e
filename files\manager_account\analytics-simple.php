<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.panel-featured-primary {
    border-left-color: #0088cc;
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Labels and Badges */
.label {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.label-success {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.label-warning {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.label-danger {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.label-info {
    background: linear-gradient(135deg, #5bc0de, #31b0d5);
}

.label-default {
    background: linear-gradient(135deg, #777, #555);
}

/* Alert Enhancements */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 15px;
}

.alert h4, .alert h5 {
    margin-top: 0;
    margin-bottom: 10px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }
}
</style>

<header class="page-header">
    <h2>วิเคราะห์ข้อมูลบัญชีผู้เล่น (Simple Version)</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Analytics Simple</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Simple version with basic queries only
$timeRange = isset($_GET['range']) ? $_GET['range'] : '24';

// Initialize default values
$totalStats = array(
    'total_accounts' => 0,
    'online_accounts' => 0,
    'banned_accounts' => 0,
    'new_today' => 0,
    'new_week' => 0,
    'active_today' => 0,
    'active_week' => 0,
    'avg_playtime' => 0,
    'total_playtime' => 0
);

$financialStats = array(
    'total_cash' => 0,
    'total_cash_bonus' => 0,
    'total_cash_all' => 0,
    'total_rewards' => 0,
    'avg_cash' => 0,
    'accounts_with_cash' => 0
);

$characterStats = array(
    'total_characters' => 0,
    'accounts_with_chars' => 0,
    'avg_level' => 0,
    'max_level' => 0,
    'high_level_chars' => 0,
    'capella_chars' => 0,
    'procyon_chars' => 0
);

$securityStats = array(
    'suspicious_logins' => 0,
    'night_logins' => 0
);

// Try simple queries first
try {
    // Simple account count
    $simpleQuery = "SELECT COUNT(*) as total FROM cabal_auth_table";
    $result = sqlsrv_query($conn, $simpleQuery);
    if ($result !== false) {
        $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
        $totalStats['total_accounts'] = $row['total'];
        sqlsrv_free_stmt($result);
    }
} catch (Exception $e) {
    // If simple query fails, try with database prefix
    try {
        $prefixQuery = "SELECT COUNT(*) as total FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
        $result = sqlsrv_query($conn, $prefixQuery);
        if ($result !== false) {
            $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
            $totalStats['total_accounts'] = $row['total'];
            sqlsrv_free_stmt($result);
        }
    } catch (Exception $e2) {
        // Use demo data
        $totalStats['total_accounts'] = 1250;
        $totalStats['online_accounts'] = 45;
        $totalStats['new_today'] = 12;
        $totalStats['active_today'] = 234;
    }
}

// Try to get online users
try {
    $onlineQuery = "SELECT COUNT(*) as online FROM cabal_auth_table WHERE Login = 1";
    $result = sqlsrv_query($conn, $onlineQuery);
    if ($result !== false) {
        $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
        $totalStats['online_accounts'] = $row['online'];
        sqlsrv_free_stmt($result);
    }
} catch (Exception $e) {
    $totalStats['online_accounts'] = rand(20, 80);
}

// Set some demo data for display
if ($totalStats['total_accounts'] == 0) {
    $totalStats = array(
        'total_accounts' => 1250,
        'online_accounts' => 45,
        'banned_accounts' => 8,
        'new_today' => 12,
        'new_week' => 89,
        'active_today' => 234,
        'active_week' => 567,
        'avg_playtime' => 180,
        'total_playtime' => 225000
    );
    
    $financialStats = array(
        'total_cash' => ********,
        'total_cash_bonus' => 2500000,
        'total_cash_all' => ********,
        'total_rewards' => 890000,
        'avg_cash' => 14000,
        'accounts_with_cash' => 890
    );
    
    $characterStats = array(
        'total_characters' => 3450,
        'accounts_with_chars' => 1180,
        'avg_level' => 85.5,
        'max_level' => 200,
        'high_level_chars' => 456,
        'capella_chars' => 1720,
        'procyon_chars' => 1730
    );
    
    $securityStats = array(
        'suspicious_logins' => 3,
        'night_logins' => 45
    );
}

// Generate demo hourly activity data
$hourlyActivity = array();
for ($i = 0; $i < 24; $i++) {
    // Simulate realistic activity pattern
    if ($i >= 6 && $i <= 23) {
        $hourlyActivity[$i] = rand(20, 100);
    } else {
        $hourlyActivity[$i] = rand(5, 25);
    }
}

// Demo top players
$topPlayers = array(
    array('ID' => 'Player001', 'PlayTime' => 15000, 'status' => 'ออนไลน์'),
    array('ID' => 'Player002', 'PlayTime' => 14500, 'status' => 'ออฟไลน์'),
    array('ID' => 'Player003', 'PlayTime' => 14200, 'status' => 'ออนไลน์'),
    array('ID' => 'Player004', 'PlayTime' => 13800, 'status' => 'ออฟไลน์'),
    array('ID' => 'Player005', 'PlayTime' => 13500, 'status' => 'ออนไลน์'),
);
?>

<div class="row">
    <!-- Time Range Selector -->
    <div class="col-md-12">
        <section class="panel">
            <div class="panel-body">
                <div class="alert alert-info">
                    <h4><i class="fa fa-info-circle"></i> ข้อมูลตัวอย่าง</h4>
                    <p>หน้านี้แสดงข้อมูลตัวอย่างเพื่อทดสอบการทำงานของระบบ หากต้องการข้อมูลจริง กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลและชื่อตาราง</p>
                    <p><a href="?url=manager_account/test-db" class="btn btn-primary btn-sm">ทดสอบฐานข้อมูล</a></p>
                </div>
                
                <div class="btn-group" role="group">
                    <a href="?url=manager_account/analytics-simple&range=1" 
                       class="btn <?php echo $timeRange == '1' ? 'btn-primary' : 'btn-default'; ?>">
                        1 ชั่วโมงล่าสุด
                    </a>
                    <a href="?url=manager_account/analytics-simple&range=24" 
                       class="btn <?php echo $timeRange == '24' ? 'btn-primary' : 'btn-default'; ?>">
                        24 ชั่วโมงล่าสุด
                    </a>
                    <a href="?url=manager_account/analytics-simple&range=168" 
                       class="btn <?php echo $timeRange == '168' ? 'btn-primary' : 'btn-default'; ?>">
                        7 วันล่าสุด
                    </a>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Account Overview -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($totalStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $totalStats['online_accounts']; ?> ออนไลน์</span>
                            <span class="text-danger"><?php echo $totalStats['banned_accounts']; ?> ถูกแบน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['new_today']; ?></strong>
                                <span class="text-secondary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['new_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-gamepad"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['active_today']; ?></strong>
                                <span class="text-tertiary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['active_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">แจ้งเตือนความปลอดภัย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $securityStats['suspicious_logins']; ?></strong>
                                <span class="text-quaternary">รายการ</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-warning"><?php echo $securityStats['night_logins']; ?> เข้าสู่ระบบกลางคืน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Hourly Activity Chart -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">กิจกรรมการเข้าสู่ระบบตามชั่วโมง (ข้อมูลตัวอย่าง)</h2>
            </header>
            <div class="panel-body">
                <canvas id="hourlyActivityChart" height="100"></canvas>
            </div>
        </section>
    </div>

    <!-- Financial Overview -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ภาพรวมทางการเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_cash_all']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash รวมทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['accounts_with_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">บัญชีที่มี Cash</p>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_rewards']); ?></span>
                            <p class="text-xs text-muted mb-none">Reward Points รวม</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['avg_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash เฉลี่ย</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Character Statistics -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติตัวละคร</h2>
            </header>
            <div class="panel-body">
                <canvas id="characterChart" height="150"></canvas>
                <div class="mt-lg">
                    <div class="row text-center">
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($characterStats['total_characters']); ?></span>
                            <p class="text-xs text-muted mb-none">ตัวละครทั้งหมด</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo round($characterStats['avg_level'], 1); ?></span>
                            <p class="text-xs text-muted mb-none">Level เฉลี่ย</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo $characterStats['max_level']; ?></span>
                            <p class="text-xs text-muted mb-none">Level สูงสุด</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Top Active Players -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ผู้เล่นที่ใช้งานมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ผู้เล่น</th>
                                <th>เวลาเล่น (ชั่วโมง)</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topPlayers as $player): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($player['ID']); ?></td>
                                    <td><?php echo round($player['PlayTime'] / 60, 1); ?></td>
                                    <td>
                                        <span class="label label-<?php echo $player['status'] == 'ออนไลน์' ? 'success' : 'default'; ?>">
                                            <?php echo $player['status']; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Hourly Activity Chart
    var hourlyCtx = document.getElementById('hourlyActivityChart').getContext('2d');
    var hourlyData = {
        labels: [
            '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
            '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
            '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
            '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
        ],
        datasets: [{
            label: 'การเข้าสู่ระบบ',
            data: [
                <?php 
                $hourlyValues = array();
                for ($i = 0; $i < 24; $i++) {
                    $hourlyValues[] = isset($hourlyActivity[$i]) ? $hourlyActivity[$i] : 0;
                }
                echo implode(',', $hourlyValues);
                ?>
            ],
            borderColor: '#0088cc',
            backgroundColor: 'rgba(0, 136, 204, 0.1)',
            borderWidth: 2,
            fill: true
        }]
    };
    
    new Chart(hourlyCtx, {
        type: 'line',
        data: hourlyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Character Distribution Chart
    var characterCtx = document.getElementById('characterChart').getContext('2d');
    var characterData = {
        labels: ['Capella', 'Procyon', 'High Level (100+)'],
        datasets: [{
            data: [
                <?php echo $characterStats['capella_chars']; ?>, 
                <?php echo $characterStats['procyon_chars']; ?>,
                <?php echo $characterStats['high_level_chars']; ?>
            ],
            backgroundColor: ['#0088cc', '#d9534f', '#5cb85c'],
            borderWidth: 0
        }]
    };
    
    new Chart(characterCtx, {
        type: 'doughnut',
        data: characterData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
