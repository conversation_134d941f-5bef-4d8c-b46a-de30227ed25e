<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');
require '../../_app/php/userLogin.class.php';
$userLogin = new userLogged();
$userLogin->exitHome();
if(isset($_POST['view'])){

			$query = "SELECT TOP 50 * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table ORDER BY LoginTime DESC";
			$list_result = sqlsrv_query($conn,$query,array(), array( "Scrollable" => SQLSRV_CURSOR_KEYSET ));
			$output = '';
			if(sqlsrv_num_rows($list_result) > 0){
			 while($row = sqlsrv_fetch_array($list_result)){

			$uidx = $row["UserNum"];

            // ดึงข้อมูลจาก WEB_User_Info
            $query_webuser = "SELECT * FROM WEB_User_Info WHERE UserNum = ?";
            $params = array($uidx);
            $result_webuser = sqlsrv_prepare($conn, $query_webuser, $params);
            
            if ($result_webuser === false) {
                die("Error in sqlsrv_prepare: " . print_r(sqlsrv_errors(), true));
            }
			if (sqlsrv_execute($result_webuser)) {
                $webuser_Fetch = sqlsrv_fetch_array($result_webuser, SQLSRV_FETCH_ASSOC);
                if ($webuser_Fetch === false) {
                    $output .= '<li><a href="#" class="text-bold text-italic">No WEB_User_Info for UserNum: ' . $uidx . '</a></li>';
                    continue;
                }

                // แปลง LoginTime เป็น string
                $loginTime = $row["LoginTime"] instanceof DateTime ? $row["LoginTime"]->format('Y-m-d H:i:s') : $row["LoginTime"];
				$output .= '
				<li class="unread">
					<a href="?url=manager_account/manage-account-edit&id=' . ($uidx ?? 'Unknown') . '" class="d-flex align-items-center">
						<span class="status status-success mr-2">
							<span class="profile-image rounded-circle d-inline-block">
								<img src="' . ($webuser_Fetch["picture"] ?? 'assets/images/avtar/user.png') . '" 
									class="profile-image profile-image-lg rounded-circle" alt="...">
							</span>
						</span>
						<span class="d-flex flex-column flex-1 ml-1">
							<span class="name">' . ($webuser_Fetch["UserNum"] ?? 'Unknown') . '#' . ($row["ID"] ?? 'No ID') . '#' . ($webuser_Fetch["register_method"] ?? 'Not Set') . '</span>
							<span class="msg-a fs-sm">IP: ' . ($row["LastIp"] ?? 'Unknown') . ' เวลาออนไลน์ ' . ($row["PlayTime"] ?? '0') . ' น.</span>
							<span class="fs-nano text-muted mt-1">ล็อกอินเวลา ' . ($loginTime ?? 'N/A') . '</span>
						</span>
					</a>
				</li>';
					

				}
			  }
			}else{
			 $output .= '
			 <li><a href="#" class="text-bold text-italic">No Noti Found</a></li>';
			}

			$status_query = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE Login = 1";
			$result = sqlsrv_query($conn, $status_query, array(), array( "Scrollable" => SQLSRV_CURSOR_KEYSET ));
			$count = sqlsrv_num_rows($result);
			$data = array(
				'notification' => $output,
				'countcharlogin_notification'  => $count
			);

			echo json_encode($data);

	}

?>