<?php
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php-error.log');
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

header('Content-Type: application/json');

$sql = "SELECT ID, ItemPriceID, ItemKindIdx, ItemOption, ItemCount
        FROM EventData.dbo.cabal_ems_event_npcitemshop_itemprice_table";

$stmt = sqlsrv_query($conn, $sql);

if ($stmt === false) {
    die(json_encode([
        'error' => 'Query failed',
        'details' => print_r(sqlsrv_errors(), true)
    ]));
}

$result = [];

while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $result[] = [  // ใช้ [] ไม่ใช่ $result[$row['ItemPriceID']]
        'ID' => (int)$row['ID'],
        'ItemPriceID' => (int)$row['ItemPriceID'],
        'ItemKindIdx' => (int)$row['ItemKindIdx'],
        'ItemOption'  => (int)$row['ItemOption'],
        'ItemCount'   => (int)$row['ItemCount']
    ];
}

echo json_encode($result);
