<?php 
// Test database connection and table structure
$zpanel->checkSession(true);

echo "<h2>Database Connection Test</h2>";

// Test basic connection
if ($conn) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    die();
}

// Test database constants
echo "<h3>Database Constants:</h3>";
echo "<ul>";
echo "<li>DATABASE_ACC: " . (defined('DATABASE_ACC') ? DATABASE_ACC : 'Not defined') . "</li>";
echo "<li>DATABASE_SV: " . (defined('DATABASE_SV') ? DATABASE_SV : 'Not defined') . "</li>";
echo "<li>DATABASE_CCA: " . (defined('DATABASE_CCA') ? DATABASE_CCA : 'Not defined') . "</li>";
echo "</ul>";

// Test table existence
echo "<h3>Table Existence Test:</h3>";

$tables_to_test = [
    'cabal_auth_table' => DATABASE_ACC,
    'cabal_character_table' => DATABASE_SV,
    'CashAccount' => DATABASE_CCA,
    'WEB_MyPurchases' => ''
];

foreach ($tables_to_test as $table => $database) {
    $db_prefix = $database ? "[$database].[dbo]." : "";
    $test_query = "SELECT TOP 1 * FROM {$db_prefix}$table";
    
    echo "<h4>Testing: {$db_prefix}$table</h4>";
    
    $result = sqlsrv_query($conn, $test_query);
    
    if ($result === false) {
        echo "<p style='color: red;'>❌ Table not found or query failed</p>";
        echo "<pre>Error: " . print_r(sqlsrv_errors(), true) . "</pre>";
        
        // Try without database prefix
        if ($database) {
            echo "<p>Trying without database prefix...</p>";
            $test_query_simple = "SELECT TOP 1 * FROM $table";
            $result_simple = sqlsrv_query($conn, $test_query_simple);
            
            if ($result_simple === false) {
                echo "<p style='color: red;'>❌ Still failed without database prefix</p>";
                echo "<pre>Error: " . print_r(sqlsrv_errors(), true) . "</pre>";
            } else {
                echo "<p style='color: orange;'>⚠️ Works without database prefix</p>";
                sqlsrv_free_stmt($result_simple);
            }
        }
    } else {
        echo "<p style='color: green;'>✅ Table exists and accessible</p>";
        
        // Get column information
        $columns_query = "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '$table'";
        $columns_result = sqlsrv_query($conn, $columns_query);
        
        if ($columns_result) {
            echo "<details><summary>Show columns</summary>";
            echo "<ul>";
            while ($col = sqlsrv_fetch_array($columns_result, SQLSRV_FETCH_ASSOC)) {
                echo "<li>{$col['COLUMN_NAME']} ({$col['DATA_TYPE']})</li>";
            }
            echo "</ul>";
            echo "</details>";
            sqlsrv_free_stmt($columns_result);
        }
        
        sqlsrv_free_stmt($result);
    }
    echo "<hr>";
}

// Test specific queries that are causing issues
echo "<h3>Specific Query Tests:</h3>";

$test_queries = [
    "Simple count" => "SELECT COUNT(*) as total FROM [".DATABASE_ACC."].[dbo].cabal_auth_table",
    "With CAST" => "SELECT COUNT(*), AVG(CAST(PlayTime as FLOAT)) as avg_playtime FROM [".DATABASE_ACC."].[dbo].cabal_auth_table",
    "Financial table" => "SELECT COUNT(*) as total FROM [".DATABASE_CCA."].[dbo].CashAccount",
    "Character table" => "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_character_table"
];

foreach ($test_queries as $name => $query) {
    echo "<h4>$name</h4>";
    echo "<code>$query</code><br>";
    
    $result = sqlsrv_query($conn, $query);
    
    if ($result === false) {
        echo "<p style='color: red;'>❌ Query failed</p>";
        echo "<pre>Error: " . print_r(sqlsrv_errors(), true) . "</pre>";
    } else {
        echo "<p style='color: green;'>✅ Query successful</p>";
        $data = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
        echo "<pre>" . print_r($data, true) . "</pre>";
        sqlsrv_free_stmt($result);
    }
    echo "<hr>";
}

echo "<h3>Recommendations:</h3>";
echo "<ol>";
echo "<li>If tables work without database prefix, update the queries to remove database prefixes</li>";
echo "<li>If specific columns don't exist, check the actual table structure</li>";
echo "<li>If data types are different, adjust the CAST operations</li>";
echo "<li>Make sure the user has proper permissions to access all databases</li>";
echo "</ol>";
?>
