<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php-error.log');
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

header('Content-Type: application/json');

$eventId = intval($_GET['eventId'] ?? 0);
if ($eventId <= 0) {
    echo json_encode(['error' => 'Invalid eventId']);
    exit;
}

if ($conn === false) {
    error_log("DB connection error: " . print_r(sqlsrv_errors(), true));
    echo json_encode(['error' => 'DB connection failed']);
    exit;
}

$result = ['items' => [], 'useFlag' => 0];

// ดึง UseFlag จากตาราง event หลัก
$statusStmt = sqlsrv_query($conn, "SELECT UseFlag FROM EventData.dbo.cabal_ems_event_table WHERE EventID = ?", [$eventId]);
if ($statusStmt !== false && ($row = sqlsrv_fetch_array($statusStmt, SQLSRV_FETCH_ASSOC))) {
    $result['useFlag'] = (int)$row['UseFlag'];
}

// ดึงรายการจาก shop table
$sql = "SELECT SlotIdx, PriceAlz,PricePoint,itemPriceID,ItemKindIdx, ItemOption, ItemDurationIdx, ExpirationTime, DailyLimit, WeeklyLimit, ItemPriceID
        FROM EventData.dbo.cabal_ems_event_npcitemshop_table
        WHERE EventID = ?";
$stmt = sqlsrv_query($conn, $sql, [$eventId]);
if ($stmt === false) {
    error_log("Query shop items failed: " . print_r(sqlsrv_errors(), true));
    echo json_encode(['error' => 'Query failed']);
    exit;
}

while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $slot = (int)$row['SlotIdx'];
    $itemPriceID = (int)$row['ItemPriceID'];
    $result['items'][$slot] = [
        "itemId"     => (int)$row['ItemKindIdx'],
        "itemOption" => (int)$row['ItemOption'],
        "duration"   => (int)$row['ItemDurationIdx'],
        "PriceAlz"     => (int)$row['PriceAlz'],
        "itemPriceID"     => (int)$row['itemPriceID'],
        "PricePoint"     => (int)$row['PricePoint'],
        "expire"     => ($row['ExpirationTime'] instanceof DateTime)
                        ? $row['ExpirationTime']->format('Y-m-d H:i:s')
                        : "1970-01-01 00:00:00",
        "daily"      => (int)$row['DailyLimit'],
        "weekly"     => (int)$row['WeeklyLimit'],



    ];
}

echo json_encode($result, JSON_UNESCAPED_UNICODE);
