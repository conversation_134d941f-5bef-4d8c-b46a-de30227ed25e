<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.panel-featured-primary {
    border-left-color: #0088cc;
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Circular Progress Bar */
.circular-bar {
    position: relative;
    display: inline-block;
}

.circular-bar-lg {
    width: 150px;
    height: 150px;
}

.circular-bar-chart {
    position: relative;
}

.circular-bar-chart strong {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: #666;
    text-align: center;
    line-height: 1.2;
}

.circular-bar-chart label {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

/* Alert Enhancements */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 15px;
}

.alert h4, .alert h5 {
    margin-top: 0;
    margin-bottom: 10px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Labels and Badges */
.label {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.label-success {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.label-warning {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.label-danger {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.label-info {
    background: linear-gradient(135deg, #5bc0de, #31b0d5);
}

.label-default {
    background: linear-gradient(135deg, #777, #555);
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }
}
</style>

<header class="page-header">
    <h2>ภาพรวมความปลอดภัยระบบ (Admin)</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Security Overview</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Security overview for admin
$timeRange = isset($_GET['range']) ? $_GET['range'] : '24';

// Suspicious login activities
$selectSuspiciousLogins = "SELECT 
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -$timeRange, GETDATE()) 
          AND UserNum IN (
              SELECT UserNum FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
              WHERE LoginTime >= DATEADD(hour, -$timeRange, GETDATE())
              GROUP BY UserNum 
              HAVING COUNT(DISTINCT LastIp) > 2
          ) THEN 1 END) as multiple_ip_logins,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 0 AND 5 
          AND LoginTime >= DATEADD(hour, -$timeRange, GETDATE()) THEN 1 END) as night_logins,
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -$timeRange, GETDATE()) THEN 1 END) as total_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$suspiciousQuery = sqlsrv_query($conn, $selectSuspiciousLogins);
$suspiciousStats = sqlsrv_fetch_array($suspiciousQuery, SQLSRV_FETCH_ASSOC);

// Rapid login attempts
$selectRapidLogins = "SELECT 
    UserNum,
    ID,
    COUNT(*) as login_attempts,
    MIN(LoginTime) as first_attempt,
    MAX(LoginTime) as last_attempt,
    COUNT(DISTINCT LastIp) as different_ips
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(hour, -1, GETDATE())
    GROUP BY UserNum, ID
    HAVING COUNT(*) > 5
    ORDER BY login_attempts DESC";
$rapidLoginsQuery = sqlsrv_query($conn, $selectRapidLogins);
$rapidLogins = array();
while ($row = sqlsrv_fetch_array($rapidLoginsQuery, SQLSRV_FETCH_ASSOC)) {
    $rapidLogins[] = $row;
}

// Concurrent sessions from different IPs
$selectConcurrentSessions = "SELECT 
    a1.UserNum,
    a1.ID,
    a1.LastIp as ip1,
    a2.LastIp as ip2,
    a1.LoginTime as login1,
    a2.LoginTime as login2
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table a1
    JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table a2 ON a1.UserNum = a2.UserNum
    WHERE a1.LastIp != a2.LastIp
    AND a1.LoginTime >= DATEADD(hour, -1, GETDATE())
    AND a2.LoginTime >= DATEADD(hour, -1, GETDATE())
    AND ABS(DATEDIFF(minute, a1.LoginTime, a2.LoginTime)) <= 10
    ORDER BY a1.LoginTime DESC";
$concurrentQuery = sqlsrv_query($conn, $selectConcurrentSessions);
$concurrentSessions = array();
while ($row = sqlsrv_fetch_array($concurrentQuery, SQLSRV_FETCH_ASSOC)) {
    $concurrentSessions[] = $row;
}

// Failed login attempts (simulated - you may need to adjust based on your logging system)
$selectFailedLogins = "SELECT 
    COUNT(*) as failed_attempts
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(hour, -$timeRange, GETDATE())
    AND AuthType = 2"; // Assuming AuthType 2 means failed/banned
$failedQuery = sqlsrv_query($conn, $selectFailedLogins);
$failedStats = sqlsrv_fetch_array($failedQuery, SQLSRV_FETCH_ASSOC);

// IP address analysis
$selectIPAnalysis = "SELECT 
    LastIp,
    COUNT(DISTINCT UserNum) as unique_accounts,
    COUNT(*) as total_logins,
    MIN(LoginTime) as first_seen,
    MAX(LoginTime) as last_seen
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -7, GETDATE())
    AND LastIp IS NOT NULL
    GROUP BY LastIp
    HAVING COUNT(DISTINCT UserNum) > 5
    ORDER BY unique_accounts DESC";
$ipAnalysisQuery = sqlsrv_query($conn, $selectIPAnalysis);
$ipAnalysis = array();
while ($row = sqlsrv_fetch_array($ipAnalysisQuery, SQLSRV_FETCH_ASSOC)) {
    $ipAnalysis[] = $row;
}

// Account security scores (simplified calculation)
$selectSecurityScores = "SELECT 
    UserNum,
    ID,
    CASE 
        WHEN COUNT(DISTINCT LastIp) > 5 THEN 'ต่ำ'
        WHEN COUNT(DISTINCT LastIp) > 2 THEN 'ปานกลาง'
        ELSE 'สูง'
    END as security_level,
    COUNT(DISTINCT LastIp) as ip_count,
    COUNT(*) as login_count,
    MAX(LoginTime) as last_login
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -30, GETDATE())
    GROUP BY UserNum, ID
    ORDER BY ip_count DESC";
$securityScoresQuery = sqlsrv_query($conn, $selectSecurityScores);
$securityScores = array();
$securityLevels = array('สูง' => 0, 'ปานกลาง' => 0, 'ต่ำ' => 0);
while ($row = sqlsrv_fetch_array($securityScoresQuery, SQLSRV_FETCH_ASSOC)) {
    $securityScores[] = $row;
    $securityLevels[$row['security_level']]++;
}

// Calculate overall security score
$totalAccounts = array_sum($securityLevels);
$overallSecurityScore = $totalAccounts > 0 ? 
    (($securityLevels['สูง'] * 100 + $securityLevels['ปานกลาง'] * 60 + $securityLevels['ต่ำ'] * 20) / $totalAccounts) : 0;
?>

<div class="row">
    <!-- Time Range Selector -->
    <div class="col-md-12">
        <section class="panel">
            <div class="panel-body">
                <div class="btn-group" role="group">
                    <a href="?url=manager_account/security-overview&range=1" 
                       class="btn <?php echo $timeRange == '1' ? 'btn-primary' : 'btn-default'; ?>">
                        1 ชั่วโมงล่าสุด
                    </a>
                    <a href="?url=manager_account/security-overview&range=24" 
                       class="btn <?php echo $timeRange == '24' ? 'btn-primary' : 'btn-default'; ?>">
                        24 ชั่วโมงล่าสุด
                    </a>
                    <a href="?url=manager_account/security-overview&range=168" 
                       class="btn <?php echo $timeRange == '168' ? 'btn-primary' : 'btn-default'; ?>">
                        7 วันล่าสุด
                    </a>
                </div>
                <div class="pull-right">
                    <button class="btn btn-warning" onclick="generateSecurityReport()">
                        <i class="fa fa-file-pdf"></i> สร้างรายงานความปลอดภัย
                    </button>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Overall Security Score -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">คะแนนความปลอดภัยรวม</h2>
            </header>
            <div class="panel-body text-center">
                <div class="circular-bar circular-bar-lg">
                    <div class="circular-bar-chart" 
                         data-percent="<?php echo round($overallSecurityScore); ?>" 
                         data-plugin-options='{ "barColor": "<?php echo $overallSecurityScore >= 80 ? '#5cb85c' : ($overallSecurityScore >= 60 ? '#f0ad4e' : '#d9534f'); ?>", "delay": 300, "size": 150, "lineWidth": 10 }'>
                        <strong>ความปลอดภัยรวม</strong>
                        <label><span class="percent"><?php echo round($overallSecurityScore); ?></span>%</label>
                    </div>
                </div>
                <h4 class="mt-lg">
                    ระดับ: 
                    <span class="<?php echo $overallSecurityScore >= 80 ? 'text-success' : ($overallSecurityScore >= 60 ? 'text-warning' : 'text-danger'); ?>">
                        <?php echo $overallSecurityScore >= 80 ? 'สูง' : ($overallSecurityScore >= 60 ? 'ปานกลาง' : 'ต่ำ'); ?>
                    </span>
                </h4>
            </div>
        </section>
    </div>

    <!-- Security Alerts -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">แจ้งเตือนความปลอดภัย</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="alert alert-warning">
                            <h5><i class="fa fa-exclamation-triangle"></i> การเข้าสู่ระบบจาก IP หลายตัว</h5>
                            <p><strong><?php echo $suspiciousStats['multiple_ip_logins']; ?></strong> รายการใน <?php echo $timeRange; ?> ชั่วโมงล่าสุด</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="alert alert-info">
                            <h5><i class="fa fa-moon"></i> การเข้าสู่ระบบกลางคืน</h5>
                            <p><strong><?php echo $suspiciousStats['night_logins']; ?></strong> รายการใน <?php echo $timeRange; ?> ชั่วโมงล่าสุด</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="alert alert-danger">
                            <h5><i class="fa fa-ban"></i> ความพยายามเข้าสู่ระบบที่ล้มเหลว</h5>
                            <p><strong><?php echo $failedStats['failed_attempts']; ?></strong> รายการใน <?php echo $timeRange; ?> ชั่วโมงล่าสุด</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="alert alert-success">
                            <h5><i class="fa fa-check-circle"></i> การเข้าสู่ระบบทั้งหมด</h5>
                            <p><strong><?php echo $suspiciousStats['total_logins']; ?></strong> รายการใน <?php echo $timeRange; ?> ชั่วโมงล่าสุด</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Security Level Distribution -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การกระจายระดับความปลอดภัย</h2>
            </header>
            <div class="panel-body">
                <canvas id="securityLevelChart" height="150"></canvas>
                <div class="mt-lg">
                    <div class="row text-center">
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold text-success"><?php echo $securityLevels['สูง']; ?></span>
                            <p class="text-xs text-muted mb-none">ความปลอดภัยสูง</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold text-warning"><?php echo $securityLevels['ปานกลาง']; ?></span>
                            <p class="text-xs text-muted mb-none">ความปลอดภัยปานกลาง</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold text-danger"><?php echo $securityLevels['ต่ำ']; ?></span>
                            <p class="text-xs text-muted mb-none">ความปลอดภัยต่ำ</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Rapid Login Attempts -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การเข้าสู่ระบบรวดเร็ว (1 ชั่วโมงล่าสุด)</h2>
            </header>
            <div class="panel-body">
                <?php if (empty($rapidLogins)): ?>
                    <p class="text-muted text-center">ไม่พบการเข้าสู่ระบบที่ผิดปกติ</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ผู้เล่น</th>
                                    <th>ครั้ง</th>
                                    <th>IP ที่แตกต่าง</th>
                                    <th>สถานะ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($rapidLogins, 0, 10) as $login): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($login['ID']); ?></td>
                                        <td><?php echo $login['login_attempts']; ?></td>
                                        <td><?php echo $login['different_ips']; ?></td>
                                        <td>
                                            <span class="label label-<?php echo $login['different_ips'] > 1 ? 'danger' : 'warning'; ?>">
                                                <?php echo $login['different_ips'] > 1 ? 'น่าสงสัย' : 'ตรวจสอบ'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- IP Address Analysis -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">IP ที่มีบัญชีหลายตัว</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>IP Address</th>
                                <th>บัญชีที่แตกต่าง</th>
                                <th>การเข้าสู่ระบบ</th>
                                <th>ความเสี่ยง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($ipAnalysis, 0, 10) as $ip): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($ip['LastIp']); ?></td>
                                    <td><?php echo $ip['unique_accounts']; ?></td>
                                    <td><?php echo $ip['total_logins']; ?></td>
                                    <td>
                                        <span class="label label-<?php echo $ip['unique_accounts'] > 10 ? 'danger' : ($ip['unique_accounts'] > 7 ? 'warning' : 'info'); ?>">
                                            <?php echo $ip['unique_accounts'] > 10 ? 'สูง' : ($ip['unique_accounts'] > 7 ? 'ปานกลาง' : 'ต่ำ'); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>

    <!-- Concurrent Sessions -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การเข้าสู่ระบบพร้อมกัน</h2>
            </header>
            <div class="panel-body">
                <?php if (empty($concurrentSessions)): ?>
                    <p class="text-muted text-center">ไม่พบการเข้าสู่ระบบพร้อมกันที่ผิดปกติ</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ผู้เล่น</th>
                                    <th>IP 1</th>
                                    <th>IP 2</th>
                                    <th>เวลา</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($concurrentSessions, 0, 10) as $session): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($session['ID']); ?></td>
                                        <td><?php echo htmlspecialchars($session['ip1']); ?></td>
                                        <td><?php echo htmlspecialchars($session['ip2']); ?></td>
                                        <td><?php echo date('H:i:s', strtotime($session['login1'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Security Level Distribution Chart
    var securityCtx = document.getElementById('securityLevelChart').getContext('2d');
    var securityData = {
        labels: ['ความปลอดภัยสูง', 'ความปลอดภัยปานกลาง', 'ความปลอดภัยต่ำ'],
        datasets: [{
            data: [
                <?php echo $securityLevels['สูง']; ?>,
                <?php echo $securityLevels['ปานกลาง']; ?>,
                <?php echo $securityLevels['ต่ำ']; ?>
            ],
            backgroundColor: ['#5cb85c', '#f0ad4e', '#d9534f'],
            borderWidth: 0
        }]
    };
    
    new Chart(securityCtx, {
        type: 'doughnut',
        data: securityData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function generateSecurityReport() {
    // Generate security report functionality
    window.open('?url=manager_account/security-report&range=<?php echo $timeRange; ?>', '_blank');
}
</script>
