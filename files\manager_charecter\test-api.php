<?php
// Direct API test without session requirement
require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>Character API Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { background-color: #d4edda; border-color: #c3e6cb; }
.error { background-color: #f8d7da; border-color: #f5c6cb; }
.info { background-color: #d1ecf1; border-color: #bee5eb; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style></head><body>";

echo "<h1>Character API Test</h1>";

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h3>1. ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";
try {
    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        echo "<div class='success'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ - พบตัวละคร " . number_format($row['count']) . " ตัว</div>";
    } else {
        echo "<div class='error'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 2: getLiveStats function
echo "<div class='test-section'>";
echo "<h3>2. ทดสอบฟังก์ชัน getLiveStats</h3>";
try {
    function getLiveStats($conn) {
        $stats = [];
        
        try {
            // Total characters
            $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
            $result = sqlsrv_query($conn, $sql);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['total_characters'] = $row['count'];
            }
            
            // Online characters
            $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE ChannelIdx > 0";
            $result = sqlsrv_query($conn, $sql);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['online_characters'] = $row['count'];
            }
            
            // Characters created today
            $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                    WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
            $result = sqlsrv_query($conn, $sql);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['created_today'] = $row['count'];
            }
            
        } catch (Exception $e) {
            error_log("Live stats error: " . $e->getMessage());
        }
        
        return [
            'success' => true,
            'data' => $stats,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    $liveStats = getLiveStats($conn);
    if ($liveStats['success']) {
        echo "<div class='success'>✅ ฟังก์ชัน getLiveStats ทำงานได้</div>";
        echo "<pre>" . json_encode($liveStats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<div class='error'>❌ ฟังก์ชัน getLiveStats ไม่ทำงาน</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 3: getRecentActivities function
echo "<div class='test-section'>";
echo "<h3>3. ทดสอบฟังก์ชัน getRecentActivities</h3>";
try {
    function getRecentActivities($conn, $limit = 5) {
        $activities = [];
        
        try {
            $sql = "SELECT TOP ? 
                        CharacterIdx,
                        Name,
                        LEV,
                        Style,
                        CreateDate,
                        WorldIdx,
                        Alz,
                        PlayTime
                    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                    ORDER BY CreateDate DESC";
            
            $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
            if ($stmt && sqlsrv_execute($stmt)) {
                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                    $createDate = $row['CreateDate'];
                    $formattedDate = '';
                    if ($createDate instanceof DateTime) {
                        $formattedDate = $createDate->format('Y-m-d H:i:s');
                    } elseif ($createDate) {
                        $formattedDate = date('Y-m-d H:i:s', strtotime($createDate));
                    } else {
                        $formattedDate = date('Y-m-d H:i:s');
                    }
                    
                    $activities[] = [
                        'character_idx' => $row['CharacterIdx'],
                        'name' => $row['Name'],
                        'level' => $row['LEV'],
                        'style' => $row['Style'],
                        'create_date' => $formattedDate,
                        'world_idx' => $row['WorldIdx'],
                        'alz' => $row['Alz'],
                        'playtime' => $row['PlayTime']
                    ];
                }
            }
            
        } catch (Exception $e) {
            error_log("Recent activities error: " . $e->getMessage());
        }
        
        return [
            'success' => true,
            'data' => $activities,
            'count' => count($activities)
        ];
    }
    
    $recentActivities = getRecentActivities($conn, 5);
    if ($recentActivities['success']) {
        echo "<div class='success'>✅ ฟังก์ชัน getRecentActivities ทำงานได้ - พบข้อมูล " . $recentActivities['count'] . " รายการ</div>";
        echo "<pre>" . json_encode($recentActivities, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<div class='error'>❌ ฟังก์ชัน getRecentActivities ไม่ทำงาน</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 4: Test actual API endpoint
echo "<div class='test-section'>";
echo "<h3>4. ทดสอบ API Endpoint</h3>";
echo "<div class='info'>";
echo "<p>เพื่อทดสอบ API endpoint โดยตรง ให้เข้าถึง URL ต่อไปนี้:</p>";
echo "<ul>";
echo "<li><a href='api/character-data.php?action=live_stats' target='_blank'>api/character-data.php?action=live_stats</a></li>";
echo "<li><a href='api/character-data.php?action=recent_activities&limit=5' target='_blank'>api/character-data.php?action=recent_activities&limit=5</a></li>";
echo "<li><a href='api/character-data.php?action=alerts' target='_blank'>api/character-data.php?action=alerts</a></li>";
echo "</ul>";
echo "<p><strong>หมายเหตุ:</strong> หาก API ต้องการ session authentication คุณอาจต้องเข้าสู่ระบบก่อน</p>";
echo "</div>";
echo "</div>";

// Test 5: Character table structure
echo "<div class='test-section'>";
echo "<h3>5. ตรวจสอบโครงสร้างตาราง</h3>";
try {
    $sql = "SELECT TOP 1 * FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        echo "<div class='success'>✅ ตารางมีโครงสร้างถูกต้อง</div>";
        echo "<p><strong>คอลัมน์ที่พบ:</strong></p>";
        echo "<ul>";
        foreach (array_keys($row) as $column) {
            echo "<li>" . $column . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='error'>❌ ไม่สามารถอ่านโครงสร้างตารางได้</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='test-section info'>";
echo "<h3>สรุปการทดสอบ</h3>";
echo "<p>หากการทดสอบทั้งหมดผ่าน แสดงว่า:</p>";
echo "<ul>";
echo "<li>✅ ฐานข้อมูลเชื่อมต่อได้</li>";
echo "<li>✅ ฟังก์ชัน API ทำงานได้</li>";
echo "<li>✅ ข้อมูลสามารถดึงมาได้</li>";
echo "<li>✅ การจัดการ DateTime ถูกต้อง</li>";
echo "</ul>";
echo "<p><strong>ขั้นตอนต่อไป:</strong> ทดสอบ character-monitor.php และ API endpoints ผ่าน browser</p>";
echo "</div>";

echo "</body></html>";
?>
