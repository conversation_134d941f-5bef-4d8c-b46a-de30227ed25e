<?php $user->restrictionUser(true, $conn); ?>
<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
    <script>
        // ฟังก์ชันหลักที่ทำงานเมื่อ DOM โหลดเสร็จ
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('Initializing...');
                await populateTable(); // โหลดข้อมูลในตาราง
                await updateDatalist(); // โหลดข้อมูลใน datalist
                console.log('Initialization complete.');

                // จัดการ Event สำหรับการเปลี่ยนแปลงค่าใน input
                document.querySelectorAll('.form-control').forEach(input => {
                    input.addEventListener('input', handleInputChange);
                });

                // เพิ่มฟังก์ชันคลิกใน DataTables
                setupTableClickEvent();
            } catch (error) {
                console.error('Error during initialization:', error);
            }
        });

        // ฟังก์ชันสำหรับจัดการ Event input
        function handleInputChange(event) {
            let inputItem = parseFloat(document.getElementById('input_Item').value) || 0;
            let bindid = parseFloat(document.getElementById('bind_id').value) || 0;
            let bindchar = parseFloat(document.getElementById('bind_char').value) || 0;
            let bindequ = parseFloat(document.getElementById('bind_equ').value) || 0;
            let hexitem = document.getElementById('hextodec').value || '';
            let decitem = document.getElementById('dectohex').value || '';

            console.log('Received values:', { inputItem, bindid, bindchar, bindequ, hexitem, decitem });

            // การคำนวณ
            let codebid = inputItem + bindid;
            let codebchar = inputItem + bindchar;
            let codebequ = inputItem + bindequ;

            let deccodeitem = hexitem ? parseInt(hexitem, 16) : 0;
            let hexcodeitem = decitem ? parseInt(decitem).toString(16) : '';

            // แสดงผลลัพธ์ใน input ต่าง ๆ
            document.getElementById('codeoutput_bid').value = codebid.toFixed(0);
            document.getElementById('codeoutput_bchar').value = codebchar.toFixed(0);
            document.getElementById('codeoutput_bequ').value = codebequ.toFixed(0);
            document.getElementById('deccode').value = deccodeitem.toFixed(0);
            document.getElementById('hexcode').value = hexcodeitem;
        }

        // ฟังก์ชันสำหรับโหลดข้อมูลไอเท็ม
        async function loadItems() {
            try {
                const response = await fetch('files/game_systems/import/item_fixed.dec');
                if (!response.ok) throw new Error(`HTTP status ${response.status}`);
                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, 'application/xml');
                const items = xmlDoc.getElementsByTagName('msg');

                const itemArray = [];
                for (let i = 0; i < items.length; i++) {
                    const id = items[i].getAttribute('id').replace('item', '');
                    const cont = items[i].getAttribute('cont');
                    itemArray.push({ id, cont });
                }
                console.log('Loaded items:', itemArray);
                return itemArray;
            } catch (error) {
                console.error('Error loading items:', error);
                return [];
            }
        }

        // ฟังก์ชันสำหรับอัปเดต datalist
        async function updateDatalist() {
            const items = await loadItems();
            const datalist = document.getElementById('items_list');
            datalist.innerHTML = '';

            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.cont} (ID: ${item.id})`;
                datalist.appendChild(option);
            });

            console.log('Datalist updated.');
        }

        // ฟังก์ชันสำหรับเพิ่มข้อมูลในตาราง
        async function populateTable() {
            const items = await loadItems();
            const tableBody = document.getElementById('itemsTableBody');
            tableBody.innerHTML = ''; // ล้างข้อมูลเดิม

            items.forEach(item => {
                const row = document.createElement('tr');
                const idCell = document.createElement('td');
                const contCell = document.createElement('td');

                idCell.textContent = item.id;
                contCell.textContent = item.cont;

                row.appendChild(idCell);
                row.appendChild(contCell);
                tableBody.appendChild(row);
            });

            // ใช้งาน DataTables
            $('#itemsTable').DataTable();

            console.log('Table populated.');
        }

        // ฟังก์ชันสำหรับจัดการ Event คลิกใน DataTables
        function setupTableClickEvent() {
            const table = $('#itemsTable').DataTable(); // ใช้ DataTables API
            $('#itemsTable tbody').on('click', 'tr', function () {
                const rowData = table.row(this).data();
                console.log('Row clicked:', rowData);
                if (rowData) {
                    document.getElementById('input_Item').value = rowData[0]; // อัปเดต input_Item ด้วย ID
                    handleInputChange(); // เรียกใช้ handleInputChange หลังจากอัปเดต input_Item
                }
            });
        }
    </script>


    <div class="subheader">
        <h1 class="subheader-title">
            <i class="subheader-icon fal fa-window"></i> แอดไอเท็ม && ตรวจสอบข้อมูลเกมส์
            <small>
                เป็นระบบ แอดไอเท็มรูปแบบหน้าเว็บ และค้นหารายชื่อไอเท็ม และอื่นๆอีกมากมาย!
            </small>
        </h1>
    </div>
    <?php
    function getPage($stmt, $pageNum, $rowsPerPage)
    {
        $offset = ($pageNum - 1) * $rowsPerPage;
        $rows = array();
        $i = 0;
        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
            array_push($rows, $row);
            $i++;
        }
        return $rows;
    }
    ?>
    <div class="row">
        <div class="col-xl-8">
            <div id="panel-1" class="panel">
                <div class="panel-hdr">
                    <h2>
                        Add Item <span class="fw-300"><i>Table</i></span>
                    </h2>
                    <div class="panel-toolbar">
                        <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                            data-original-title="Collapse"></button>
                        <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                            data-offset="0,10" data-original-title="Fullscreen"></button>
                        <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                            data-original-title="Close"></button>
                    </div>
                </div>
                <div class="panel-container">
                    <div class="panel-content">
                        <!-- row -->
                        <div class="row">
                            <div class="col-xl-12">

                                <div class="alert alert-info">
                                    <span span class="h5">How it works</span>
                                    <br> - ให้ตรวจสอบไอเท็มได้ที่ช่อง <code>Cash Inventory</code>
                                    <br>
                                    <strong>#รายระเอียด</strong>
                                    <br> - ให้ตรวจสอบ <code>UserID</code> ก่อนโดยการนำ <code>UserID</code>
                                    ไปกรอกในช่อง <code>UserID เช็ค</code>
                                    <br> - <code>Code Item</code> คือ รหัสไอเท็ม
                                    <br> - <code>Option</code> คือ ออฟชั่น
                                    <br> - <code>DurationIdx</code> คือ วันใช้งาน/วันหมดอายุ
                                    <br> - <code>Upgrade</code> คือ อัตราการตีบวกไอเท็ม
                                    ใช้ได้กับไอเท็มบางชนิดเท่านั้น
                                    <br> - <code>Quantity</code> คือ จำนวนครั้ง ในการแอดไอเท็ม
                                </div>
                                <div class="panel">
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <?php
                                            if (isset($_POST['btn_savechange_checking'])) {
                                                // variable
                                                $inputid = strip_tags(trim($_POST['input_checkid']));
                                                // condition
                                                if (empty($inputid)) {
                                                    //$returnWarning = '<div class="alert alert-success">
                                                    //<strong>Success!</strong> You should <a href="#" class="alert-link">read this message</a>.
                                                    // </div>';
                                        
                                                    echo '<script type="text/javascript">';
                                                    echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ตรวจสอบไม่พบข้อมูลบัญชีผู้ใช้นี้!","error");';
                                                    echo '});</script>';

                                                } else {
                                                    // get conversion info
                                                    $selectauth = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE ID = ?";
                                                    $selectauthParam = array($inputid);
                                                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);

                                                    if ($selectauthQuery === false) {
                                                        // กรณี query ผิดพลาด เช่น syntax error
                                                        die(print_r(sqlsrv_errors(), true));
                                                    }

                                                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                                                    if ($selectauthFetch) {
                                                        $UserNum = $selectauthFetch['UserNum'];
                                                        $ID = $selectauthFetch['ID'];

                                                        echo '<script type="text/javascript">';
                                                        echo 'setTimeout(function () { Swal.fire("success !!!","ข้อมูลผู้ใช้นี้ UserNum = ' . $UserNum . ' ID = ' . $ID . '","success"); }, 200);';
                                                        echo '</script>';
                                                    } else {
                                                        echo '<script type="text/javascript">';
                                                        echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ตรวจสอบไม่พบข้อมูลบัญชีผู้ใช้นี้!","error"); }, 200);';
                                                        echo '</script>';
                                                    }

                                                }
                                            }

                                            ?>
                                            <?php if (isset($returnSuccess)) { ?>
                                                <div class="alert alert-success j_alert"><?php echo $returnSuccess; ?>
                                                </div>
                                            <?php } elseif (isset($returnWarning)) { ?>
                                                <div class="alert alert-warning j_alert"><?php echo $returnWarning; ?>
                                                </div>
                                            <?php } elseif (isset($returnError)) { ?>
                                                <div class="alert alert-danger j_alert"><?php echo $returnError; ?>
                                                </div>
                                            <?php } ?>
                                            <form role="form" method="post" enctype="multipart/form-data">
                                                <div class="form-row">
                                                    <div class="col-md-4 mb-4">
                                                        <label class="form-label" for="">UserID เช็ค</label>
                                                        <input type="text" name="input_checkid" class="form-control"
                                                            placeholder="ตรวจสอบไอดี" value="<?php if (isset($inputid)) {
                                                                echo $inputid;
                                                            } ?>" required="กรอกไอดีเพื่อตรวจสอบข้อมูล">
                                                    </div>
                                                    <div class="col-md-8 mb-4">
                                                        <label class="form-label" for="">ตรวจสอบ </label>
                                                        <button type="submit" name="btn_savechange_checking"
                                                            class="mb-1 btn btn-danger btn-block">ตรวจสอบข้อมูลไอดี</button>
                                                    </div>
                                                </div>
                                            </form>
                                            <form role="form" method="post" name="j_add_adminitems"
                                                enctype="multipart/form-data">

                                                <div class="col-sm-12">
                                                    <div class="form-group row">
                                                        <label for="input_id" class="control-label">UserID: </label>
                                                        <input type="text" name="input_id" class="form-control"
                                                            placeholder="ไอดี" value="<?php if (isset($ID)) {
                                                                echo $ID;
                                                            } ?>" required="กรอกไอดีเพื่อตรวจสอบข้อมูล" readonly>
                                                    </div>
                                                </div>

                                                <div class="form-group row">
                                                    <div class="col-sm-3">
                                                        <div class="form-group">
                                                            <label for="input_Item" class="control-label">Code
                                                                Item:</label>
                                                            <!-- Input ที่สามารถพิมพ์หรือเลือกจาก datalist -->
                                                            <input list="items_list" name="input_Item" class="form-control"
                                                                id="input_Item" placeholder="พิมพ์หรือเลือกตัวเลขไอเท็ม">
                                                            <datalist id="items_list">
                                                                <!-- รายการจะถูกโหลดโดย JavaScript -->
                                                                <option>กรุณารอรายการโหลด...</option>
                                                            </datalist>

                                                            <!-- Hidden Inputs -->
                                                            <input type="hidden" class="form-control" id="bind_id"
                                                                value="4096">
                                                            <input type="hidden" class="form-control" id="bind_char"
                                                                value="528384">
                                                            <input type="hidden" class="form-control" id="bind_equ"
                                                                value="1572864">
                                                        </div>

                                                    </div>
                                                    <div class="col-sm-3">
                                                        <div class="form-group">
                                                            <label for="Option" class="control-label">Option:
                                                            </label><code>{ ea }</code>
                                                            <input type="text" name="input_Option" class="form-control"
                                                                id="Option" pattern="[0-9]+$" placeholder="" value="0">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div class="form-group">
                                                            <label for="input_Dur" class="control-label">DurationIdx:
                                                            </label>
                                                            <select data-plugin-selectTwo name="input_Dur" id="input_Dur"
                                                                pattern="[0-9]+$" class="form-control populate placeholder"
                                                                data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                                                                <optgroup label="อายุ ชม.">
                                                                    <option value="31">31->ถาวร</option>
                                                                    <option value="1">1->1 ชม.</option>
                                                                    <option value="2">2->2 ชม.</option>
                                                                    <option value="3">3->3 ชม.</option>
                                                                    <option value="4">4->4 ชม.</option>
                                                                    <option value="5">5->5 ชม.</option>
                                                                    <option value="6">6->6 ชม.</option>
                                                                    <option value="7">7->10 ชม.</option>
                                                                    <option value="8">8->12 ชม</option>
                                                                    <option value="9">9->1 วัน</option>
                                                                    <option value="10">10->3 วัน</option>
                                                                    <option value="11">11->5 วัน</option>
                                                                    <option value="12">12->7 วัน</option>
                                                                    <option value="13">13->10 วัน</option>
                                                                    <option value="14">14->14 วัน</option>
                                                                    <option value="15">15->15 วัน</option>
                                                                    <option value="16">16->20 วัน</option>
                                                                    <option value="17">17->30 วัน</option>
                                                                    <option value="18">18->45 วัน</option>
                                                                    <option value="19">19->60 วัน</option>
                                                                    <option value="20">20->90 วัน</option>
                                                                    <option value="21">21->100 วัน</option>
                                                                    <option value="22">22->120 วัน</option>
                                                                    <option value="23">23->180 วัน</option>
                                                                    <option value="24">24->270 วัน</option>
                                                                    <option value="25">25->365 วัน</option>
                                                                    <option value="26">26->3 นาที</option>
                                                                    <option value="27">27->30 นาที</option>
                                                                    <option value="28">28->90 นาที</option>
                                                                    <option value="29">29->10 นาที</option>
                                                                    <option value="30">30->0 วัน</option>
                                                                    <option value="31">31->0 วัน</option>
                                                                    <option value="0">ไม่มีอายุ</option>
                                                                </optgroup>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-2">
                                                        <div class="form-group">
                                                            <label for="input_Upgrade" class="control-label">Upgrade:
                                                            </label>
                                                            <select data-plugin-selectTwo name="input_Upgrade"
                                                                id="input_Upgrade" pattern="[a-zA-Z0-9]+$"
                                                                class="form-control populate placeholder"
                                                                data-plugin-options='{ "placeholder": "Select Upgrade", "allowClear": true }'>
                                                                <optgroup label="Upgrade ไม่ผูกไอดี">
                                                                    <option value="0">+0 </option>
                                                                    <option value="2">+1</option>
                                                                    <option value="4">+2</option>
                                                                    <option value="6">+3</option>
                                                                    <option value="8">+4</option>
                                                                    <option value="A">+5</option>
                                                                    <option value="C">+6</option>
                                                                    <option value="E">+7</option>
                                                                    <option value="10">+8</option>
                                                                    <option value="12">+9</option>
                                                                    <option value="14">+10</option>
                                                                    <option value="16">+11</option>
                                                                    <option value="18">+12</option>
                                                                    <option value="1A">+13</option>
                                                                    <option value="1C">+14</option>
                                                                    <option value="1E">+15</option>
                                                                    <option value="20">+16</option>
                                                                    <option value="22">+17</option>
                                                                    <option value="24">+18</option>
                                                                    <option value="26">+19</option>
                                                                    <option value="28">+20</option>
                                                                </optgroup>
                                                                <optgroup label="Upgrade ผูกไอดี">
                                                                    <option value="1">+0 binding</option>
                                                                    <option value="3">+1 binding</option>
                                                                    <option value="5">+2 binding</option>
                                                                    <option value="7">+3 binding</option>
                                                                    <option value="9">+4 binding</option>
                                                                    <option value="B">+5 binding</option>
                                                                    <option value="D">+6 binding</option>
                                                                    <option value="F">+7 binding</option>
                                                                    <option value="11">+8 binding</option>
                                                                    <option value="13">+9 binding</option>
                                                                    <option value="15">+10 binding</option>
                                                                    <option value="17">+11 binding</option>
                                                                    <option value="19">+12 binding</option>
                                                                    <option value="1B">+13 binding</option>
                                                                    <option value="1D">+14 binding</option>
                                                                    <option value="1F">+15 binding</option>
                                                                    <option value="21">+16 binding</option>
                                                                    <option value="23">+17 binding</option>
                                                                    <option value="25">+18 binding</option>
                                                                    <option value="27">+19 binding</option>
                                                                    <option value="29">+20 binding</option>
                                                                </optgroup>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div class="form-group">
                                                            <label for="input_quantity"
                                                                class="control-label">Quantity:</label>
                                                            <select data-plugin-selectTwo name="input_quantity"
                                                                id="input_quantity" pattern="[0-9]+$"
                                                                class="form-control populate placeholder"
                                                                data-plugin-options='{ "placeholder": "Select quantity", "allowClear": true }'>
                                                                <optgroup label="Quantity.">
                                                                    <option value="0">1 Terms.</option>
                                                                    <option value="1">2 Terms.</option>
                                                                    <option value="2">3 Terms.</option>
                                                                    <option value="3">4 Terms.</option>
                                                                    <option value="4">5 Terms.</option>
                                                                    <option value="5">6 Terms.</option>
                                                                    <option value="6">7 Terms.</option>
                                                                    <option value="9">10 Terms.</option>
                                                                    <option value="14">15 Terms</option>
                                                                    <option value="19">20 Terms</option>
                                                                </optgroup>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12">
                                                        <div class="form-group">
                                                            <label for="input_Item" class="control-label">Slot:</label>
                                                            <div class="m-md slider-primary">
                                                                <input name="input_Slot" id="slotitem" type="text" value="0"
                                                                    class="d-none" tabindex="-1" readonly="">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-4">
                                                        <div class="form-group">
                                                            <label for="input_Item" class="control-label">bind-id:</label>
                                                            <input type="text" class="form-control" pattern="[0-9]+$"
                                                                placeholder="รหัสผูกมัดไอดี" readonly="readonly"
                                                                id="codeoutput_bid">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <div class="form-group ">
                                                            <label for="input_Item" class="control-label">bind-Char:</label>
                                                            <input type="text" class="form-control" pattern="[0-9]+$"
                                                                placeholder="รหัสผูกมัดตัวละคร" readonly="readonly"
                                                                id="codeoutput_bchar">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <div class="form-group ">
                                                            <label for="input_Item" class="control-label">bind-equ:</label>
                                                            <input type="text" class="form-control" pattern="[0-9]+$"
                                                                placeholder="รหัสผูกมัดเมื้อสวมใส่" readonly="readonly"
                                                                id="codeoutput_bequ">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-3">
                                                        <div class="form-group">
                                                            <label for="input_Item" class="control-label">Hex to Dec
                                                                code:</label>
                                                            <input type="text" class="form-control" id="hextodec"
                                                                placeholder="HEX">
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-3">
                                                        <div class="form-group ">
                                                            <label for="input_Item" class="control-label">Dec
                                                                code:</label>
                                                            <input type="text" class="form-control" pattern="[0-9]+$"
                                                                placeholder="DEX CODE" readonly="readonly" id="deccode">
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-3">
                                                        <div class="form-group">
                                                            <label for="input_Item" class="control-label">Dec to hex
                                                                code:</label>
                                                            <input type="text" class="form-control" id="dectohex"
                                                                placeholder="DEX">
                                                        </div>
                                                    </div>

                                                    <div class="col-sm-3">
                                                        <div class="form-group ">
                                                            <label for="input_Item" class="control-label">Hex
                                                                code:</label>
                                                            <input type="text" class="form-control" pattern="[0-9]+$"
                                                                placeholder="HEX CODE" readonly="readonly" id="hexcode">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-sm-12">
                                                    <div class="form-group row">
                                                        <button type="submit" name="btn_savechange"
                                                            class="mb-1 mt-1 mr-1 btn btn-primary btn-block">ยืนยันไอเท็ม</button>
                                                        <input type="hidden" name="admin-user"
                                                            value="<?php echo $userLogin->recUserAccount('ID', $conn); ?>">
                                                        <input type="hidden" name="admin-useridx"
                                                            value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
                                                    </div>
                                                </div>

                                            </form>
                                        </div>

                                    </div>
                                </div>
                                <div class="panel">
                                    <div class="panel-container">
                                        <div class="panel-content">
                                            <h2>รายการไอเท็ม</h2>
                                            <table id=""
                                                class="table table-bordered table-hover table-striped w-100">
                                                <thead>
                                                    <tr>
                                                        <th>Id</th>
                                                        <th>ID</th>
                                                        <th>Item</th>
                                                        <th>ItemOpt</th>
                                                        <th>Dur</th>
                                                        <th>ส่งไอเท็ม</th>
                                                        <th>IsUse</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
													if (!isset($UserNum) || empty($UserNum)) {
														echo "<div style='color:red;'>❌ Error: ตัวแปร UserNum ไม่ถูกกำหนด หรือไม่มีค่า</div>";
														// หรือจะใช้ return หรือ exit เพื่อหยุดการทำงานต่อ
														return;
													}
                                                    $selectLastUsers = "SELECT TOP 10 * FROM [" . DATABASE_CCA . "].[dbo].MyCashItem WHERE UserNum = '$UserNum' AND IsUse = 0 ORDER BY RegDate DESC";
                                                    $selectLastUsersParam = array();
                                                    $selectLastUsersOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                                                    $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                                    if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                                        while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                                                            $selectauth = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '" . $resLastUsers['UserNum'] . "'";
                                                            $selectauthParam = array();
                                                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                                                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                                                            ?>
                                                            <tr>
                                                                <td><?php echo $resLastUsers['Id']; ?></td>
                                                                <td><?php echo $resLastUsers['UserNum']; ?>#<?php echo $selectauthFetch['ID']; ?></td>
                                                                <td><?php echo $resLastUsers['ItemKindIdx']; ?></td>
                                                                <td><?php echo $resLastUsers['ItemOpt']; ?></td>
                                                                <td><?php echo $resLastUsers['DurationIdx']; ?></td>
                                                                <td><?php echo date('d/m/Y H:i:s', strtotime($resLastUsers['RegDate'])); ?>
                                                                </td>
                                                                <td><?php if ($resLastUsers['IsUse'] == 1){
																			echo "Use";
																		}elseif ($resLastUsers['IsUse'] == 0){
																			echo "NotUse";
																		}else{
																			echo "error";
																		} ?>
																</td>
                                                                </td>
                                                                <td>
                                                                    <form method="post" name="j_delete_mycashitem" action="">
                                                                        <div class="j_alert"></div>
                                                                        <input type="hidden" name="mycashid"
                                                                            value="<?php echo $resLastUsers['Id']; ?>">
                                                                        <input type="hidden" name="useridx"
                                                                            value="<?php echo $resLastUsers['UserNum']; ?>">
                                                                        <button type="submit" class="btn btn-danger btn-sm">
                                                                            <i class="fa fa-trash" aria-hidden="true"></i>
                                                                             
                                                                        </button>
                                                                    </form>
                                                                </td>
                                                            </tr>
                                                            <?php
                                                        }
                                                    } else {
                                                        echo W_NOTHING_RETURNED;
                                                    }

                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- end row -->
                        </div>

                    </div>
                </div>

            </div>
        </div>

        <div class="col-xl-4">
            <div id="panel-2" class="panel">
                <div class="panel-hdr">
                    <h2>
                        Item <span class="fw-300"><i>Table</i></span>
                    </h2>
                    <div class="panel-toolbar">
                        <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                            data-original-title="Collapse"></button>
                        <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                            data-offset="0,10" data-original-title="Fullscreen"></button>
                        <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                            data-original-title="Close"></button>
                    </div>
                </div>
                <div class="panel-container ">
                    <div class="panel-content">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="alert alert-info">
                                    <span span class="h5">How it works</span>
                                    <br> - คลิกที่ชื่อไอเท็มรหัสไอเท็มจะเพิ่มไปที่ <code>Code Item ฝั่งซ้ายมือทันที</code>
                                    <br> - เมื้อมีการอัพเดทไอเท็มใหม่สามาแก้ไขได้ที่ไฟล์ <code>item_fixed.dec</code>
                                    ตำแหน่งไฟล์ <code>Folder หลัก</code>
                                </div>
                                <h2>รายการไอเท็ม</h2>
                                <table id="itemsTable" class="table table-bordered table-hover table-striped w-100">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Item Name</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsTableBody">
                                        <!-- ข้อมูลจะถูกเพิ่มผ่าน JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php } ?>