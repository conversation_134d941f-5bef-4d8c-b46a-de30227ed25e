<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-download"></i> ส่งออกข้อมูล Mail
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_mail/mail-statistics" class="btn btn-primary btn-sm">
            <i class="fal fa-chart-bar"></i> กลับสถิติ
        </a>
    </div>
</div>

<div class="row">
    <!-- Export Form -->
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">เลือกข้อมูลที่ต้องการส่งออก</h3>
            </div>
            <div class="card-body">
                <form id="exportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dataType">ประเภทข้อมูล</label>
                                <select class="form-control" id="dataType" name="data_type" required>
                                    <option value="">-- เลือกประเภทข้อมูล --</option>
                                    <option value="statistics">สถิติรายวัน</option>
                                    <option value="received_mails">เมลล์ที่รับ</option>
                                    <option value="sent_mails">เมลล์ที่ส่ง</option>
                                    <option value="deleted_mails">เมลล์ที่ถูกลบ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="exportFormat">รูปแบบไฟล์</label>
                                <select class="form-control" id="exportFormat" name="format" required>
                                    <option value="">-- เลือกรูปแบบ --</option>
                                    <option value="csv">CSV (Excel)</option>
                                    <option value="json">JSON</option>
                                    <option value="excel">Excel (XLS)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateFrom">วันที่เริ่มต้น</label>
                                <input type="date" class="form-control" id="dateFrom" name="date_from" 
                                       value="<?php echo date('Y-m-d', strtotime('-30 days')); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateTo">วันที่สิ้นสุด</label>
                                <input type="date" class="form-control" id="dateTo" name="date_to" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="includeContent">
                            <label class="custom-control-label" for="includeContent">
                                รวมเนื้อหาข้อความ (อาจทำให้ไฟล์มีขนาดใหญ่)
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-success">
                            <i class="fal fa-download"></i> ส่งออกข้อมูล
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewData()">
                            <i class="fal fa-eye"></i> ดูตัวอย่าง
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fal fa-undo"></i> รีเซ็ต
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Export History & Info -->
    <div class="col-xl-4">
        <div class="card mb-3">
            <div class="card-header">
                <h3 class="card-title">ข้อมูลการส่งออก</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>ประเภทข้อมูลที่สามารถส่งออกได้:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fal fa-chart-line text-primary"></i> <strong>สถิติรายวัน:</strong> สรุปข้อมูลรายวัน</li>
                        <li><i class="fal fa-inbox text-success"></i> <strong>เมลล์ที่รับ:</strong> รายละเอียดเมลล์ที่ผู้เล่นรับ</li>
                        <li><i class="fal fa-paper-plane text-info"></i> <strong>เมลล์ที่ส่ง:</strong> รายละเอียดเมลล์ที่ส่งออก</li>
                        <li><i class="fal fa-trash text-danger"></i> <strong>เมลล์ที่ถูกลบ:</strong> ประวัติเมลล์ที่ถูกลบ</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>รูปแบบไฟล์:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fal fa-file-csv text-success"></i> <strong>CSV:</strong> เหมาะสำหรับ Excel</li>
                        <li><i class="fal fa-file-code text-warning"></i> <strong>JSON:</strong> เหมาะสำหรับการประมวลผล</li>
                        <li><i class="fal fa-file-excel text-info"></i> <strong>Excel:</strong> ไฟล์ Excel โดยตรง</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <small>
                        <i class="fal fa-info-circle"></i>
                        การส่งออกข้อมูลจำนวนมากอาจใช้เวลาสักครู่ กรุณารอสักครู่
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Quick Export Buttons -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ส่งออกด่วน</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="quickExport('statistics', 'csv', 7)">
                        <i class="fal fa-chart-bar"></i> สถิติ 7 วันล่าสุด (CSV)
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="quickExport('received_mails', 'csv', 1)">
                        <i class="fal fa-inbox"></i> เมลล์รับวันนี้ (CSV)
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="quickExport('sent_mails', 'csv', 1)">
                        <i class="fal fa-paper-plane"></i> เมลล์ส่งวันนี้ (CSV)
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="quickExport('statistics', 'json', 30)">
                        <i class="fal fa-file-code"></i> สถิติ 30 วัน (JSON)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ตัวอย่างข้อมูล</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">กำลังโหลด...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ปิด</button>
                <button type="button" class="btn btn-success" onclick="proceedWithExport()">
                    <i class="fal fa-download"></i> ดำเนินการส่งออก
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('exportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    performExport();
});

function performExport() {
    const formData = new FormData(document.getElementById('exportForm'));
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        params.append(key, value);
    }
    
    // Add type parameter
    params.append('type', document.getElementById('exportFormat').value);
    params.append('data', document.getElementById('dataType').value);
    
    // Show loading
    showLoading('กำลังส่งออกข้อมูล...');
    
    // Create download link
    const url = 'files/manager_mail/export/mail-export.php?' + params.toString();
    
    // Create temporary link and click it
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Hide loading after a delay
    setTimeout(() => {
        hideLoading();
        showSuccess('การส่งออกข้อมูลเสร็จสิ้น');
    }, 2000);
}

function quickExport(dataType, format, days) {
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - days);
    const dateTo = new Date();
    
    const params = new URLSearchParams({
        'type': format,
        'data': dataType,
        'date_from': dateFrom.toISOString().split('T')[0],
        'date_to': dateTo.toISOString().split('T')[0]
    });
    
    showLoading('กำลังส่งออกข้อมูล...');
    
    const url = 'files/manager_mail/export/mail-export.php?' + params.toString();
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
        hideLoading();
        showSuccess('การส่งออกข้อมูลเสร็จสิ้น');
    }, 2000);
}

function previewData() {
    const dataType = document.getElementById('dataType').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    
    if (!dataType || !dateFrom || !dateTo) {
        alert('กรุณาเลือกประเภทข้อมูลและช่วงวันที่');
        return;
    }
    
    $('#previewModal').modal('show');
    
    // Simulate preview data loading
    setTimeout(() => {
        const previewContent = document.getElementById('previewContent');
        previewContent.innerHTML = `
            <div class="alert alert-info">
                <strong>ตัวอย่างข้อมูล:</strong> ${dataType}<br>
                <strong>ช่วงวันที่:</strong> ${dateFrom} ถึง ${dateTo}
            </div>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="thead-light">
                        <tr>
                            <th>คอลัมน์ 1</th>
                            <th>คอลัมน์ 2</th>
                            <th>คอลัมน์ 3</th>
                            <th>คอลัมน์ 4</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ข้อมูลตัวอย่าง 1</td>
                            <td>ข้อมูลตัวอย่าง 2</td>
                            <td>ข้อมูลตัวอย่าง 3</td>
                            <td>ข้อมูลตัวอย่าง 4</td>
                        </tr>
                        <tr>
                            <td colspan="4" class="text-center text-muted">... และอีก X รายการ</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="alert alert-warning">
                <small>นี่เป็นเพียงตัวอย่างข้อมูล ข้อมูลจริงจะแสดงเมื่อทำการส่งออก</small>
            </div>
        `;
    }, 1000);
}

function proceedWithExport() {
    $('#previewModal').modal('hide');
    performExport();
}

function showLoading(message) {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    
    overlay.innerHTML = `
        <div class="bg-white p-4 rounded text-center">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showSuccess(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fal fa-check-circle"></i> ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Set max date to today
document.getElementById('dateTo').max = new Date().toISOString().split('T')[0];
document.getElementById('dateFrom').max = new Date().toISOString().split('T')[0];

// Validate date range
document.getElementById('dateFrom').addEventListener('change', function() {
    document.getElementById('dateTo').min = this.value;
});

document.getElementById('dateTo').addEventListener('change', function() {
    document.getElementById('dateFrom').max = this.value;
});
</script>
