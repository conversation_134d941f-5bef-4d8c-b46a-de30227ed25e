<?php $user->restrictionUser(true, $conn); ?>
<header class="page-header">
    <h2><?php echo PT_MANAGEPLAYERS; ?></h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span><?php echo PT_MANAGEPLAYERS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<section class="panel">
    <header class="panel-heading">
        <div class="panel-actions">
            <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
            <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
        </div>

        <h2 class="panel-title">ตรวจสอบตัวละคร แบน ยืนยันไอดี</h2>
    </header>
    <div class="panel-body">
        <div class="table-responsive">
            <form method="post" action="?url=manager/presults" class="form-inline pull-right">
                <div class="form-group">
                    <input type="text" class="form-control" name="search" placeholder="ค้นหา ID IP Email Phone">
                    <input type="submit" class="btn btn-info" name="btn_search" value="Search">
                </div>
            </form>
            <hr>
            <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
            <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
            <?php } ?>
            <table class="table table-bordered table-striped table-condensed mb-none" id="datatable-default">
                <thead>
                    <tr>
                        <th>Id</th>
                        <th>UserNum</th>
                        <th>ID</th>
                        <th>ItemKindIdx</th>
                        <th>ItemOpt</th>
                        <th>DurationIdx</th>
                        <th>RegDate</th>
                        <th>IsUse</th>
                        <th>UseDate</th>
                        <th><?php echo T_ACTION; ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
 
                                                // generic function to get page
                                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                                    $rows = array();
                                                    $i = 0;
                                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                                        array_push($rows, $row);
                                                        $i++;
                                                    }
                                                    return $rows;
                                                }

                                                // Set the number of rows to be returned on a page.
                                                $rowsPerPage =1000;

                                                // Define and execute the query.  
                                                // Note that the query is executed with a "scrollable" cursor.
                                                $sql = "SELECT * FROM ".DATABASE_CCA.".dbo.MyCashItem ORDER BY RegDate DESC";

                                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                                if (!$stmt)
                                                    die(print_r(sqlsrv_errors(), true));

                                                // Get the total number of rows returned by the query.
                                                $rowsReturned = sqlsrv_num_rows($stmt);
                                                if ($rowsReturned === false)
                                                    die(print_r(sqlsrv_errors(), true));
                                                elseif ($rowsReturned == 0) {
                                                    echo W_NOTHING_RETURNED;
                                                    //exit();
                                                } else {
                                                    /* Calculate number of pages. */
                                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                                }

                                                // Display the selected page of data.
                                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                                foreach ($page as $row) {  ?>
                    <tr <?php
                                                    $selectUsersData = "SELECT * FROM ".DATABASE_CCA.".dbo.MyCashItem WHERE id = '$row[0]'";
                                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, array());
                                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);

                                                    $selectUsersDataid = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$row[2]'";
                                                    $selectUsersDataidQuery = sqlsrv_query($conn, $selectUsersDataid, array());
                                                    $selectUsersDataidFetch = sqlsrv_fetch_array($selectUsersDataidQuery, SQLSRV_FETCH_ASSOC);

                                                   $uid =  $selectUsersDataidFetch["ID"];
                                                    ?>>
                        <td><?php echo $row[0]; ?></td>
                        <td><?php echo $row[3]; ?></td>
                        <td><?php echo $uid; ?></td>
                        <td><?php echo $row[4]; ?></td>
                        <td><?php echo $row[5]; ?></td>
                        <td><?php echo $row[6]; ?></td>
                        <td><?php echo $row[7]; ?></td>
                        <td><?php echo $row[8]; ?></td>
                        <td><?php echo $row[9]; ?></td>
                        <td> <a class="text-info" href="?url=manager/see-player&id=<?php echo $row[0]; ?>"
                                data-toggle="tooltip" data-placement="left" title="" data-original-title="รายระเอียด"><i
                                    class="fa fa-info-circle"></i></a>

                            <?php } ?>


                        </td>
                    </tr>


                </tbody>
            </table>
        </div>
    </div>
    <div class="panel-body">
        <div class="col-lg-12 text-center">
            <ul class="pagination">
                <script type="text/javascript">
                $(document).ready(function() {
                    $('.pagination').pagination({
                        items: <?php echo $rowsReturned;?>,
                        itemsOnPage: <?php echo $rowsPerPage;?>,
                        cssStyle: 'light-theme',
                        currentPage: <?php echo $pageNum;?>,
                        hrefTextPrefix: '?url=manager/players&pageNum='
                    });
                });
                </script>
                <?php
                                        sqlsrv_close($conn);
                                        ?>
            </ul>
        </div>
    </div>
</section>