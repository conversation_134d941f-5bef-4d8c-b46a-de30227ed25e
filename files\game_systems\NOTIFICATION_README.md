# Notification System - การแจ้งเตือนการส่งไอเทม

ระบบแจ้งเตือนแบบ real-time สำหรับการส่งไอเทมในเกม Cabal Online

## ฟีเจอร์หลัก

### 1. **Real-time Notifications**
- แจ้งเตือนทันทีเมื่อส่งไอเทมสำเร็จ
- อัปเดตสถานะแบบ real-time ทุก 30 วินาที
- แสดงจำนวนการแจ้งเตือนที่ยังไม่ได้อ่าน

### 2. **Notification Bell Icon**
- ไอคอนกระดิ่งแจ้งเตือนที่มุมขวาบน
- Badge แสดงจำนวนการแจ้งเตือนที่ยังไม่ได้อ่าน
- Animation เมื่อมีการแจ้งเตือนใหม่

### 3. **Notification Dropdown**
- รายการการแจ้งเตือนล่าสุด 10 รายการ
- แสดงรายละเอียดการส่งไอเทม
- ปุ่ม "Mark all read" และ "View All"

### 4. **Toast Notifications**
- แจ้งเตือนแบบ popup เมื่อมีการแจ้งเตือนใหม่
- ใช้ SweetAlert2 สำหรับแสดงผล
- Auto-hide หลังจาก 5 วินาที

## ไฟล์ที่เกี่ยวข้อง

### 1. **notification_system.php**
- PHP API สำหรับจัดการการแจ้งเตือน
- รองรับ GET และ POST requests
- เชื่อมต่อกับฐานข้อมูล SQL Server

### 2. **notification_manager.js**
- JavaScript class สำหรับจัดการ UI
- Real-time polling และ updates
- Event handling และ user interactions

### 3. **notification_styles.css**
- CSS styles สำหรับ notification UI
- Responsive design
- Dark mode support

### 4. **database_setup_sqlserver.sql**
- ตาราง `notifications` สำหรับเก็บข้อมูลการแจ้งเตือน
- Indexes และ constraints

## โครงสร้างฐานข้อมูล

### ตาราง `notifications`
```sql
CREATE TABLE [dbo].[notifications](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [notification_id] [nvarchar](50) NOT NULL,
    [title] [nvarchar](255) NOT NULL,
    [message] [nvarchar](max) NOT NULL,
    [type] [nvarchar](50) NOT NULL DEFAULT 'info',
    [priority] [nvarchar](20) NOT NULL DEFAULT 'normal',
    [details] [nvarchar](max) NULL,
    [admin_username] [nvarchar](50) NOT NULL,
    [created_at] [datetime] NOT NULL,
    [is_read] [bit] NOT NULL DEFAULT 0,
    [read_at] [datetime] NULL,
    [expires_at] [datetime] NULL
)
```

## API Endpoints

### 1. **GET /notification_system.php**
ดึงรายการการแจ้งเตือน

**Parameters:**
- `admin` - ชื่อ admin (optional)
- `limit` - จำนวนรายการ (default: 50)
- `unread_only` - แสดงเฉพาะที่ยังไม่ได้อ่าน (true/false)
- `stats` - ดึงสถิติ (true/false)

**Response:**
```json
{
    "success": true,
    "notifications": [
        {
            "notification_id": "notif_123",
            "title": "Item Sent Successfully",
            "message": "Item sent to player_name via mail",
            "type": "item_send",
            "priority": "normal",
            "details": {
                "send_id": 123,
                "player_username": "player_name",
                "item_code": "0000007B00000000",
                "quantity": 1
            },
            "admin_username": "admin",
            "created_at": "2024-01-01 12:00:00",
            "is_read": false
        }
    ],
    "count": 1
}
```

### 2. **POST /notification_system.php**
สร้างหรือจัดการการแจ้งเตือน

**Create Notification:**
```json
{
    "action": "create",
    "send_id": 123,
    "player_username": "player_name",
    "item_id": 123,
    "item_code": "0000007B00000000",
    "options_code": "0000000000000000",
    "quantity": 1,
    "duration": 0,
    "send_method": "mail",
    "status": "sent_to_mail",
    "admin_username": "admin"
}
```

**Mark as Read:**
```json
{
    "action": "mark_read",
    "notification_id": "notif_123"
}
```

## การใช้งาน

### 1. **การติดตั้ง**
```bash
# 1. รัน SQL script เพื่อสร้างตาราง notifications
sqlcmd -S your_server -d your_database -i database_setup_sqlserver.sql

# 2. วางไฟล์ทั้งหมดในโฟลเดอร์เดียวกับ advanced-editor.php
# 3. ตรวจสอบว่าไฟล์ config มีการตั้งค่าฐานข้อมูลถูกต้อง
```

### 2. **การเรียกใช้ใน JavaScript**
```javascript
// Notification Manager จะถูกสร้างอัตโนมัติเมื่อโหลดหน้า
// สามารถเข้าถึงผ่าน window.notificationManager

// สร้างการแจ้งเตือนทดสอบ
window.notificationManager.createTestNotification();

// โหลดการแจ้งเตือนใหม่
window.notificationManager.loadNotifications();

// Mark notification as read
window.notificationManager.markAsRead('notif_123');
```

### 3. **การปรับแต่ง**
```javascript
// เปลี่ยนช่วงเวลา polling (default: 30 วินาที)
window.notificationManager.pollInterval = 60000; // 1 นาที

// เปลี่ยนชื่อ admin
window.notificationManager.adminUsername = 'your_admin_name';
```

## การทำงานของระบบ

### 1. **เมื่อส่งไอเทมสำเร็จ**
1. `send_item.php` สร้างการแจ้งเตือนในฐานข้อมูล
2. Frontend รับ response และอัปเดต notification manager
3. แสดง toast notification ให้ผู้ใช้เห็น
4. อัปเดต notification bell badge

### 2. **Real-time Updates**
1. Notification Manager polling ทุก 30 วินาที
2. ดึงการแจ้งเตือนใหม่จาก API
3. อัปเดต UI และแสดง toast สำหรับการแจ้งเตือนใหม่
4. อัปเดต badge count

### 3. **User Interactions**
1. คลิกที่ notification bell เพื่อดูรายการ
2. คลิกที่การแจ้งเตือนเพื่อ mark as read
3. คลิก "Mark all read" เพื่อทำเครื่องหมายทั้งหมด
4. คลิก "View All" เพื่อดูทั้งหมดในหน้าใหม่

## การปรับแต่งและขยายระบบ

### 1. **เพิ่มประเภทการแจ้งเตือนใหม่**
```javascript
// ใน notification_manager.js
getNotificationIcon(type) {
    const icons = {
        'item_send': '<i class="fas fa-gift text-success"></i>',
        'player_login': '<i class="fas fa-user text-info"></i>',
        'system_error': '<i class="fas fa-exclamation-triangle text-danger"></i>',
        // เพิ่มประเภทใหม่ที่นี่
    };
    return icons[type] || icons['info'];
}
```

### 2. **เพิ่มการกรองการแจ้งเตือน**
```javascript
// เพิ่มฟิลเตอร์ในการดึงข้อมูล
loadNotifications(silent = false, filters = {}) {
    const params = new URLSearchParams({
        admin: this.adminUsername,
        limit: 10,
        ...filters
    });
    
    fetch(`notification_system.php?${params}`)
    // ...
}
```

### 3. **เพิ่มการแจ้งเตือนแบบ Push**
```javascript
// ใช้ Web Push API สำหรับการแจ้งเตือนแบบ browser
if ('Notification' in window && 'serviceWorker' in navigator) {
    // ขอสิทธิ์การแจ้งเตือน
    Notification.requestPermission();
}
```

## การแก้ไขปัญหา

### 1. **ไม่มีการแจ้งเตือน**
- ตรวจสอบว่าตาราง `notifications` ถูกสร้างแล้ว
- ตรวจสอบการเชื่อมต่อฐานข้อมูล
- ดู console สำหรับ JavaScript errors

### 2. **Notification Bell ไม่แสดง**
- ตรวจสอบว่า `notification_manager.js` ถูกโหลด
- ตรวจสอบว่า CSS ถูกโหลด
- ตรวจสอบ DOM structure

### 3. **Real-time Updates ไม่ทำงาน**
- ตรวจสอบ network requests ใน browser dev tools
- ตรวจสอบ PHP errors ใน server logs
- ตรวจสอบ CORS settings

## ความปลอดภัย

1. **Input Validation** - ตรวจสอบข้อมูลทั้งหมดก่อนบันทึก
2. **SQL Injection Protection** - ใช้ prepared statements
3. **XSS Protection** - Escape HTML output
4. **Rate Limiting** - จำกัดการเรียก API
5. **Authentication** - ตรวจสอบสิทธิ์ admin

## Performance

1. **Database Indexing** - สร้าง indexes สำหรับ queries ที่ใช้บ่อย
2. **Pagination** - จำกัดจำนวนรายการที่ดึงมา
3. **Caching** - Cache ข้อมูลที่ไม่เปลี่ยนแปลงบ่อย
4. **Cleanup** - ลบการแจ้งเตือนเก่าที่หมดอายุ

ระบบแจ้งเตือนนี้ช่วยให้ admin สามารถติดตามการส่งไอเทมได้แบบ real-time และมีประสบการณ์การใช้งานที่ดีขึ้น!
