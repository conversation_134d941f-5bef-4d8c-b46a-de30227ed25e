<?php 
// API endpoint สำหรับ character data ที่ผ่าน authentication system
$user->restrictionUser(true, $conn);

// Set JSON header
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'live_stats':
            echo json_encode(getLiveStats($conn));
            break;
            
        case 'recent_activities':
            $limit = (int)($_GET['limit'] ?? 20);
            echo json_encode(getRecentActivities($conn, $limit));
            break;
            
        case 'alerts':
            echo json_encode(getCharacterAlerts($conn));
            break;
            
        case 'class_stats':
            echo json_encode(getClassStats($conn));
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action', 'available_actions' => ['live_stats', 'recent_activities', 'alerts', 'class_stats']]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

function getLiveStats($conn) {
    $stats = [];
    
    try {
        // Total characters
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_characters'] = $row['count'];
        }
        
        // Online characters
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE ChannelIdx > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['online_characters'] = $row['count'];
        }
        
        // Characters created today
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['created_today'] = $row['count'];
        }
        
        // Characters created this hour
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CreateDate >= DATEADD(hour, DATEDIFF(hour, 0, GETDATE()), 0)
                AND CreateDate < DATEADD(hour, DATEDIFF(hour, 0, GETDATE()) + 1, 0)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['created_this_hour'] = $row['count'];
        }
        
        // Average level
        $sql = "SELECT AVG(CAST(LEV AS FLOAT)) as avg_level FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE LEV > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['avg_level'] = round($row['avg_level'] ?? 0, 1);
        }
        
        // Total Alz
        $sql = "SELECT SUM(CAST(Alz AS BIGINT)) as total_alz FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_alz'] = $row['total_alz'] ?? 0;
        }
        
        // Total playtime
        $sql = "SELECT SUM(PlayTime) as total_playtime FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_playtime'] = $row['total_playtime'] ?? 0;
        }
        
    } catch (Exception $e) {
        error_log("Live stats error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

function getRecentActivities($conn, $limit = 20) {
    $activities = [];
    
    try {
        $sql = "SELECT TOP ? 
                    CharacterIdx,
                    Name,
                    LEV,
                    Style,
                    CreateDate,
                    WorldIdx,
                    Alz,
                    PlayTime
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                ORDER BY CreateDate DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                $formattedDate = '';
                if ($createDate instanceof DateTime) {
                    $formattedDate = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedDate = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedDate = date('Y-m-d H:i:s');
                }
                
                // คำนวณคลาสอย่างถูกต้อง
                $style = $row['Style'];
                $battleStyle = $style & 7;
                $extendedBattleStyle = ($style >> 23) & 1;
                $classIndex = $battleStyle | ($extendedBattleStyle << 3);
                
                $classNames = [
                    1 => 'Warrior',
                    2 => 'Blader',
                    3 => 'Wizard',
                    4 => 'Force Archer',
                    5 => 'Force Shielder',
                    6 => 'Force Blader',
                    7 => 'Gladiator',
                    8 => 'Force Gunner',
                    9 => 'Dark Mage'
                ];
                
                $className = $classNames[$classIndex] ?? 'Unknown';
                
                $activities[] = [
                    'character_idx' => $row['CharacterIdx'],
                    'name' => $row['Name'],
                    'level' => $row['LEV'],
                    'style' => $row['Style'],
                    'class_name' => $className,
                    'create_date' => $formattedDate,
                    'world_idx' => $row['WorldIdx'],
                    'alz' => $row['Alz'],
                    'playtime' => $row['PlayTime']
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Recent activities error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $activities,
        'count' => count($activities)
    ];
}

function getCharacterAlerts($conn) {
    $alerts = [];
    
    try {
        // High level characters created recently
        $sql = "SELECT TOP 5 
                    'high_level_new' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    CreateDate,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LEV > 50 AND CreateDate >= DATEADD(day, -7, GETDATE())
                ORDER BY LEV DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                $formattedTime = '';
                if ($createDate instanceof DateTime) {
                    $formattedTime = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedTime = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedTime = date('Y-m-d H:i:s');
                }
                
                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "High level new character: " . $row['Name'] . " (Level " . $row['LEV'] . ")",
                    'severity' => 'high',
                    'time' => $formattedTime,
                    'data' => $row
                ];
            }
        }
        
        // Characters with excessive Alz
        $sql = "SELECT TOP 3 
                    'high_alz' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE Alz > 10000000
                ORDER BY Alz DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "High Alz character: " . $row['Name'] . " (" . number_format($row['Alz']) . " Alz)",
                    'severity' => 'medium',
                    'time' => date('Y-m-d H:i:s'),
                    'data' => $row
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Character alerts error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $alerts,
        'count' => count($alerts)
    ];
}

function getClassStats($conn) {
    $stats = [];
    
    try {
        $sql = "SELECT 
                    Style,
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN ChannelIdx > 0 THEN 1 END) as online_count,
                    AVG(CAST(LEV AS FLOAT)) as avg_level
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                GROUP BY Style
                ORDER BY total_count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        $classDistribution = array();
        
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                // คำนวณคลาสแบบเดียวกับ PHP cabalstyle function
                $style = $row['Style'];
                $battleStyle = $style & 7; // 3 บิตแรก
                $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
                $classIndex = $battleStyle | ($extendedBattleStyle << 3);
                
                $classNames = [
                    1 => 'Warrior',
                    2 => 'Blader',
                    3 => 'Wizard',
                    4 => 'Force Archer',
                    5 => 'Force Shielder',
                    6 => 'Force Blader',
                    7 => 'Gladiator',
                    8 => 'Force Gunner',
                    9 => 'Dark Mage'
                ];
                
                $className = $classNames[$classIndex] ?? 'Unknown';
                
                // รวมจำนวนตามคลาส
                if (isset($classDistribution[$className])) {
                    $classDistribution[$className]['total_count'] += $row['total_count'];
                    $classDistribution[$className]['online_count'] += $row['online_count'];
                    $classDistribution[$className]['avg_level'] = ($classDistribution[$className]['avg_level'] + $row['avg_level']) / 2;
                } else {
                    $classDistribution[$className] = [
                        'class_name' => $className,
                        'total_count' => $row['total_count'],
                        'online_count' => $row['online_count'],
                        'avg_level' => round($row['avg_level'] ?? 0, 1)
                    ];
                }
            }
            
            // แปลงเป็น array และเรียงลำดับ
            foreach ($classDistribution as $data) {
                $stats[] = $data;
            }
            
            // เรียงลำดับตามจำนวน
            usort($stats, function($a, $b) {
                return $b['total_count'] - $a['total_count'];
            });
        }
        
    } catch (Exception $e) {
        error_log("Class stats error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats
    ];
}
?>
