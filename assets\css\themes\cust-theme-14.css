/* #THEME COLOR (variable overrides)
========================================================================== */
/*$white:								#fff !default;
$black:								#000 !default;*/
/*$base-text-color: $white;*/
/* #GLOBAL IMPORTS
========================================================================== */
/* #IMPORTS ~~
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by Dmitry Fadeyev (http://fadeyev.net)
    SASS port by Samuel Beek (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

  $color-white: hexToRGBString(#fff) => "255,255,255"
  $color-white: hexToRGBString(rgb(255,255,255)) => "255,255,255"
  $color-white: hexToRGBString(rgba(#fff,1)) => "255,255,255"
  
------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: contrast-ink($contrastvalue)
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* We will manually convert these primary colors to rgb for the dark mode option of the theme */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* custom file */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav footer */
/* nav parent level-0 */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.image-one {
    @extend %bg-image;
    background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

*/
.page-logo, .page-sidebar, .nav-footer, .bg-brand-gradient {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(60, 113, 155, 0.18)), to(transparent));
  background-image: linear-gradient(270deg, rgba(60, 113, 155, 0.18), transparent);
  background-color: #947505; }

/*
%shadow-hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
  }
}
*/
.btn-default {
  background-color: #f5f5f5;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f5f5f5), to(#f1f1f1));
  background-image: linear-gradient(to top, #f5f5f5, #f1f1f1);
  color: #444; }
  .btn-default:hover {
    border: 1px solid #c6c6c6; }
  .btn-default:focus {
    border-color: #ffd94d !important; }
  .active.btn-default {
    background: #ffd333;
    color: #fff; }

.header-function-fixed .btn-switch[data-class="header-function-fixed"], .nav-function-fixed .btn-switch[data-class="nav-function-fixed"], .nav-function-minify .btn-switch[data-class="nav-function-minify"], .nav-function-hidden .btn-switch[data-class="nav-function-hidden"], .nav-function-top .btn-switch[data-class="nav-function-top"], .footer-function-fixed .btn-switch[data-class="footer-function-fixed"], .nav-mobile-push .btn-switch[data-class="nav-mobile-push"], .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"], .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"], .mod-main-boxed .btn-switch[data-class="mod-main-boxed"], .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"], .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"], .mod-pace-custom .btn-switch[data-class="mod-pace-custom"], .mod-bigger-font .btn-switch[data-class="mod-bigger-font"], .mod-high-contrast .btn-switch[data-class="mod-high-contrast"], .mod-color-blind .btn-switch[data-class="mod-color-blind"], .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"], .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"], .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"], .mod-disable-animation .btn-switch[data-class="mod-disable-animation"], .mod-nav-link .btn-switch[data-class="mod-nav-link"], .mod-nav-dark .btn-switch[data-class="mod-nav-dark"], .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] {
  color: #fff;
  background: #ffc800 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:after, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:after, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:after, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:after, .nav-function-top .btn-switch[data-class="nav-function-top"]:after, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"]:after, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:after, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:after, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:after, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:after, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:after, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:after, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:after, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:after, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:after, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:after, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:after, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:after, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:after, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:after, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:after, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"]:after, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"]:after {
    background: #fff !important;
    color: #ffc800 !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"] + .onoffswitch-title, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"] + .onoffswitch-title, .nav-function-minify .btn-switch[data-class="nav-function-minify"] + .onoffswitch-title, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"] + .onoffswitch-title, .nav-function-top .btn-switch[data-class="nav-function-top"] + .onoffswitch-title, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"] + .onoffswitch-title, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"] + .onoffswitch-title, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"] + .onoffswitch-title, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"] + .onoffswitch-title, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"] + .onoffswitch-title, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"] + .onoffswitch-title, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"] + .onoffswitch-title, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"] + .onoffswitch-title, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"] + .onoffswitch-title, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"] + .onoffswitch-title, .mod-color-blind .btn-switch[data-class="mod-color-blind"] + .onoffswitch-title, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"] + .onoffswitch-title, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"] + .onoffswitch-title, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"] + .onoffswitch-title, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"] + .onoffswitch-title, .mod-nav-link .btn-switch[data-class="mod-nav-link"] + .onoffswitch-title, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"] + .onoffswitch-title, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] + .onoffswitch-title {
    color: #ffc800; }

.nav-mobile-slide-out #nmp .onoffswitch-title, .nav-mobile-slide-out #nmno .onoffswitch-title, .nav-function-top #mnl .onoffswitch-title, .nav-function-minify #mnl .onoffswitch-title, .mod-hide-nav-icons #mnl .onoffswitch-title, .nav-function-top #nfh .onoffswitch-title {
  color: #d58100; }

.nav-mobile-slide-out #nmp .onoffswitch-title-desc, .nav-mobile-slide-out #nmno .onoffswitch-title-desc, .nav-function-top #mnl .onoffswitch-title-desc, .nav-function-minify #mnl .onoffswitch-title-desc, .mod-hide-nav-icons #mnl .onoffswitch-title-desc, .nav-function-top #nfh .onoffswitch-title-desc {
  color: #ec9f28; }

.dropdown-icon-menu > ul > li .btn, .header-btn {
  border: 1px solid #ffe480;
  color: #a6a6a6; }
  .dropdown-icon-menu > ul > li .btn:hover, .header-btn:hover {
    border-color: #ffc800;
    background: #ffd333;
    color: #fff; }

.nav-mobile-slide-out #nmp:after,
.nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
.nav-function-minify #mnl:after,
.mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after {
  background: #ecdab3;
  border: 1px solid #bc9030;
  color: black; }

/* #GLOBAL IMPORTS
========================================================================== */
/*@import '_imports/_global-import';*/
/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)
                DOC: you can disable unused _modules
========================================================================== */
/* contains root variables to be used with css (see docs) */
/* html and body base styles */
html body {
  background-color: #fff; }
  html body a {
    color: #ffc800;
    background-color: transparent; }
    html body a:hover {
      color: #ffce1a; }

.header-icon {
  color: #666666; }
  .header-icon:not(.btn) > [class*='fa-']:first-child,
  .header-icon:not(.btn) > .ni:first-child {
    color: #ffc800; }
  .header-icon:not(.btn):hover > [class*='fa-']:only-child,
  .header-icon:not(.btn):hover > .ni {
    color: #404040; }
  .header-icon:not(.btn)[data-toggle="dropdown"] {
    /* header dropdowns */
    /* note: important rules to override popper's inline classes */
    /* end header dropdowns */ }
    .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] {
      color: #404040; }
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > [class*='fa-']:first-child,
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > .ni:first-child {
        color: #404040 !important; }
    .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
      border-color: #ccc; }
  .header-icon:hover {
    color: #404040; }

.page-header {
  background-color: #fff; }

#search-field {
  background: transparent;
  border: 1px solid transparent; }

.notification li.unread {
  background: #f5ecd7; }

.notification li > :first-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06); }
  .notification li > :first-child:hover {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
    background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.notification .name {
  color: #222222; }

.notification .msg-a,
.notification .msg-b {
  color: #555555; }

.notification.notification-layout-2 li {
  background: #f9f9f9; }
  .notification.notification-layout-2 li.unread {
    background: #fff; }
  .notification.notification-layout-2 li > :first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04); }

.notification.notification-layout-2:hover {
  cursor: pointer; }

.app-list-item {
  color: #666666; }
  .app-list-item:hover {
    border: 1px solid #e3e3e3; }
  .app-list-item:active {
    border-color: #ffc800; }

@media (min-width: 992px) {
  .header-function-fixed.nav-function-top .page-header {
    -webkit-box-shadow: 0px 0px 28px 2px rgba(153, 120, 0, 0.13);
            box-shadow: 0px 0px 28px 2px rgba(153, 120, 0, 0.13); } }

.nav-title {
  color: #e7b708; }

.nav-menu li.open > a {
  color: white; }

.nav-menu li.active {
  /* arrow that appears next to active/selected items */ }
  .nav-menu li.active > a {
    color: white;
    background-color: rgba(255, 255, 255, 0.04);
    -webkit-box-shadow: inset 3px 0 0 #ffc800;
            box-shadow: inset 3px 0 0 #ffc800; }
    .nav-menu li.active > a:hover > [class*='fa-'],
    .nav-menu li.active > a:hover > .ni {
      color: #e0c252; }
  .nav-menu li.active > ul {
    display: block; }
  .nav-menu li.active:not(.open) > a:before {
    color: #24b3a4; }

.nav-menu li a {
  color: #f9da66; }
  .nav-menu li a .dl-ref.label {
    color: rgba(255, 255, 255, 0.7); }
  .nav-menu li a > [class*='fa-'],
  .nav-menu li a > .ni {
    color: #f1bf09; }
  .nav-menu li a.collapsed .nav-menu-btn-sub-collapse {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }
  .nav-menu li a:hover {
    color: rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.1); }
    .nav-menu li a:hover .badge {
      color: #fff; }
    .nav-menu li a:hover > [class*='fa-'],
    .nav-menu li a:hover > .ni {
      color: #e0c252; }
    .nav-menu li a:hover > .badge {
      -webkit-box-shadow: 0 0 0 1px rgba(187, 148, 7, 0.8);
              box-shadow: 0 0 0 1px rgba(187, 148, 7, 0.8);
      border: 1px solid rgba(187, 148, 7, 0.8); }
  .nav-menu li a:focus {
    color: white; }
    .nav-menu li a:focus .badge {
      color: #fff; }

.nav-menu li b.collapse-sign {
  color: #ffce1a; }

.nav-menu li > ul {
  background-color: rgba(0, 0, 0, 0.1); }
  .nav-menu li > ul li a {
    color: #f9d44e; }
    .nav-menu li > ul li a > [class*='fa-'],
    .nav-menu li > ul li a > .ni {
      color: #f1bf09; }
    .nav-menu li > ul li a > .badge {
      color: #fff;
      background-color: #Be2D2D; }
    .nav-menu li > ul li a:hover {
      color: white;
      background-color: rgba(0, 0, 0, 0.1); }
      .nav-menu li > ul li a:hover > .nav-link-text > [class*='fa-'],
      .nav-menu li > ul li a:hover > .nav-link-text > .ni {
        color: #e0c252; }
  .nav-menu li > ul li.active > a {
    color: white;
    background-color: transparent; }
    .nav-menu li > ul li.active > a > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a > .nav-link-text > .ni {
      color: white; }
    .nav-menu li > ul li.active > a:hover > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a:hover > .nav-link-text > .ni {
      color: #e0c252; }
  .nav-menu li > ul li > ul li.active > a {
    color: white; }
  .nav-menu li > ul li > ul li a {
    color: #f8d144; }
    .nav-menu li > ul li > ul li a:hover {
      color: white; }
    .nav-menu li > ul li > ul li a > .badge {
      color: #fff;
      background-color: #Be2D2D;
      border: 1px solid #665000; }

/* nav clean elements */
.nav-menu-clean {
  background: #fff; }
  .nav-menu-clean li a {
    color: #665000 !important; }
    .nav-menu-clean li a span {
      color: #665000 !important; }
    .nav-menu-clean li a:hover {
      background-color: #f4f4f4 !important; }

/* nav bordered elements */
.nav-menu-bordered {
  border: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li a {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); }

.nav-filter input[type="text"] {
  background: rgba(0, 0, 0, 0.4);
  color: #fff; }
  .nav-filter input[type="text"]:not(:focus) {
    border-color: rgba(0, 0, 0, 0.1); }
  .nav-filter input[type="text"]:focus {
    border-color: #d4a808; }

.info-card {
  color: #fff; }
  .info-card .info-card-text {
    text-shadow: #000 0 1px; }

@media (min-width: 992px) {
  .nav-function-top {
    /* correct search field color */ }
    .nav-function-top #search-field {
      color: #fff; }
    .nav-function-top:not(.header-function-fixed) #nff {
      position: relative; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title {
        color: #d58100; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .nav-function-top:not(.header-function-fixed) #nff:after {
        background: #ecdab3;
        border: 1px solid #bc9030;
        color: black; }
    .nav-function-top .page-header {
      background-image: -webkit-gradient(linear, right top, left top, from(rgba(60, 113, 155, 0.18)), to(transparent));
      background-image: linear-gradient(270deg, rgba(60, 113, 155, 0.18), transparent);
      background-color: #947505;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(153, 120, 0, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(153, 120, 0, 0.13); }
      .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child,
      .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child {
        color: #ffd333; }
        .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child:hover,
        .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child:hover {
          color: #ffde66; }
      .nav-function-top .page-header .badge.badge-icon {
        -webkit-box-shadow: 0 0 0 1px #e6b400;
                box-shadow: 0 0 0 1px #e6b400; }
    .nav-function-top .page-sidebar {
      background: #fff;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(153, 120, 0, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(153, 120, 0, 0.13); }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a:before {
        color: #24b3a4; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
        color: inherit; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign {
        color: #f8cf3a; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li a {
        color: #947505; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul {
        background: #b69007; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a {
          color: #f9da66; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul {
          background: #b69007; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li:hover > a {
          background: rgba(0, 0, 0, 0.1);
          color: #fff; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:after {
          background: transparent; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
          color: #b69007; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a {
        color: #ffc800;
        background: transparent; } }

@media (min-width: 992px) {
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li.active.open > a:before {
    color: #24b3a4; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
    background: trasparent; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul {
    background-color: #947505; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul:before {
      color: #947505; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover {
    overflow: visible; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {
      background: #a78506;
      color: #fff; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child {
        background: #947505; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child:before {
          color: #947505; }
  .nav-function-minify:not(.nav-function-top) .page-header [data-class="nav-function-minify"] {
    background: #665000;
    border-color: #332800 !important;
    color: #fff !important; } }

.nav-footer .nav-footer-buttons > li > a {
  color: #f7c922; }

.nav-function-fixed .nav-footer {
  background: #947505; }
  .nav-function-fixed .nav-footer:before {
    background: rgba(197, 156, 7, 0.2);
    background: -webkit-gradient(linear, left top, right top, from(#947505), color-stop(50%, #ddaf08), color-stop(50%, #ddaf08), to(#947505));
    background: linear-gradient(to right, #947505 0%, #ddaf08 50%, #ddaf08 50%, #947505 100%); }

@media (min-width: 992px) {
  .nav-function-minify .nav-footer {
    background-color: #8a6d05; }
    .nav-function-minify .nav-footer [data-class="nav-function-minify"] {
      color: #f1bf09; }
    .nav-function-minify .nav-footer:hover {
      background-color: #a28106; }
      .nav-function-minify .nav-footer:hover [data-class="nav-function-minify"] {
        color: #e0c252; } }

.page-content-wrapper {
  background-color: #fef5d6; }

.subheader-icon {
  color: #e0c252; }

.subheader-title {
  color: #665000;
  text-shadow: #fff 0 1px; }
  .subheader-title small {
    color: #cca000; }

.page-footer {
  background: #fff;
  color: #4d4d4d; }

.accordion .card .card-header {
  background-color: #f7f9fa; }
  .accordion .card .card-header .card-title {
    color: #ffc800; }
    .accordion .card .card-header .card-title.collapsed {
      color: #cca000; }

.accordion.accordion-clean .card-header {
  background: #fff; }

.accordion.accordion-hover .card-header {
  background: #fff; }
  .accordion.accordion-hover .card-header:hover .card-title.collapsed {
    color: #fff;
    background-color: #ffd333; }

.accordion.accordion-hover .card-title:not(.collapsed) {
  color: #fff;
  background-color: #ffc800; }

/* 	DEV NOTE: The reason why we had to add this layer for alert colors is because BS4 
	does not allow you to add your own alert colors via variable control rather 
	through a systemetic agent that changes the theme colors. 

	REF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218
*/
.alert-primary {
  color: #8a7528;
  background-color: #faf0cc;
  border-color: #f1df9d; }

.alert-success {
  color: #8e8e8e;
  background-color: white;
  border-color: #dbe9e7; }

.alert-danger {
  color: #801e1e;
  background-color: #ecadad;
  border-color: #de7373; }

.alert-warning {
  color: #7f6120;
  background-color: #f4ead3;
  border-color: #d9b669; }

.alert-info {
  color: #1c4a6e;
  background-color: #b7d5ec;
  border-color: #65a5d8; }

.alert-secondary {
  color: #665000;
  background-color: #fff1bd;
  border-color: #ffe480; }

.badge.badge-icon {
  background-color: #Be2D2D;
  color: #fff;
  -webkit-box-shadow: 0 0 0 1px #fff;
          box-shadow: 0 0 0 1px #fff; }

/* btn switch */
.btn-switch {
  background: #997800;
  color: white; }
  .btn-switch:hover {
    color: white; }
  .btn-switch:after {
    color: white; }
  .btn-switch.active {
    color: #fff;
    background: #ffc800; }
    .btn-switch.active:before {
      color: rgba(0, 0, 0, 0.8); }
    .btn-switch.active:after {
      background: #fff;
      color: #ffc800; }

/* button used to close filter and mobile search */
.btn-search-close {
  color: #fff; }

/* buttons used in the header section of the page */
.header-btn[data-class='mobile-nav-on'] {
  border-color: #952323;
  background-color: #a92828;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#a92828), to(#801e1e));
  background-image: linear-gradient(to top, #a92828, #801e1e);
  color: #fff; }

/* dropdown btn */
/* used on info card pulldown filter */
.pull-trigger-btn {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.4);
  color: #fff !important;
  -webkit-box-shadow: 0px 0px 2px rgba(255, 200, 0, 0.3);
          box-shadow: 0px 0px 2px rgba(255, 200, 0, 0.3); }
  .pull-trigger-btn:hover {
    background: #ffc800;
    border-color: #e6b400; }

/* btn misc */
.btn-outline-default {
  color: #212529;
  border-color: #E5E5E5; }
  .btn-outline-default:hover, .btn-outline-default:not(:disabled):not(.disabled):active, .btn-outline-default:not(:disabled):not(.disabled).active,
  .show > .btn-outline-default.dropdown-toggle {
    color: #212529;
    background-color: #f9f9f9;
    border-color: #E5E5E5; }
  .btn-outline-default.disabled, .btn-outline-default:disabled {
    color: #212529; }

/* btn shadows */
.btn-primary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 200, 0, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 200, 0, 0.5); }

.btn-secondary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5);
          box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5); }

.btn-success {
  -webkit-box-shadow: 0 2px 6px 0 rgba(103, 180, 172, 0.5);
          box-shadow: 0 2px 6px 0 rgba(103, 180, 172, 0.5); }

.btn-info {
  -webkit-box-shadow: 0 2px 6px 0 rgba(44, 115, 171, 0.5);
          box-shadow: 0 2px 6px 0 rgba(44, 115, 171, 0.5); }

.btn-warning {
  -webkit-box-shadow: 0 2px 6px 0 rgba(210, 168, 77, 0.5);
          box-shadow: 0 2px 6px 0 rgba(210, 168, 77, 0.5); }

.btn-danger {
  -webkit-box-shadow: 0 2px 6px 0 rgba(190, 45, 45, 0.5);
          box-shadow: 0 2px 6px 0 rgba(190, 45, 45, 0.5); }

.btn-light {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5); }

.btn-dark {
  -webkit-box-shadow: 0 2px 6px 0 rgba(102, 80, 0, 0.5);
          box-shadow: 0 2px 6px 0 rgba(102, 80, 0, 0.5); }

.btn-icon-light {
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: transparent !important; }
  .btn-icon-light:not(.active):not(:active):not(:hover):not(:focus) {
    color: rgba(255, 255, 255, 0.7) !important; }
  .btn-icon-light:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important; }

/* bootstrap buttons */
.btn-link {
  color: #ffc800; }
  .btn-link:hover {
    color: #cca000; }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #ffc800; }

.card-header {
  background-color: #f7f9fa; }

.carousel-control-prev:hover {
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.carousel-control-next:hover {
  background: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to left, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

/* dropdown menu multi-level */
.dropdown-menu .dropdown-menu {
  background: #fff; }

.dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) {
  background: #f8f9fa;
  color: #cca000; }

.dropdown-item:hover, .dropdown-item:focus {
  color: #cca000;
  background-color: #f8f9fa; }

.dropdown-item.active, .dropdown-item:active {
  color: #b38c00;
  background-color: #ffefb3; }

.chat-segment-get .chat-message {
  background: #f1f0f0; }

.chat-segment-sent .chat-message {
  background: #67B4AC; }

/* transparent modal */
.modal-transparent .modal-content {
  -webkit-box-shadow: 0 1px 15px 1px rgba(153, 120, 0, 0.3);
          box-shadow: 0 1px 15px 1px rgba(153, 120, 0, 0.3); }

.modal-transparent .modal-content {
  background: rgba(46, 37, 5, 0.85); }

.panel {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  /* panel fullscreen */
  /* panel locked */ }
  .panel.panel-fullscreen {
    /* make panel header bigger */ }
    .panel.panel-fullscreen .panel-hdr {
      -webkit-box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(128, 100, 0, 0.1);
              box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(128, 100, 0, 0.1); }
  .panel.panel-locked:not(.panel-fullscreen) .panel-hdr:active h2:before {
    color: #Be2D2D; }

/* panel tag can be used globally */
.panel-tag {
  background: #eef7fd; }

/* panel header */
.panel-hdr {
  background: #fff; }

/* panel tap highlight */
.panel-sortable:not(.panel-locked) .panel-hdr:active {
  border-top-color: rgba(255, 211, 51, 0.7);
  border-left-color: rgba(255, 200, 0, 0.7);
  border-right-color: rgba(255, 200, 0, 0.7); }
  .panel-sortable:not(.panel-locked) .panel-hdr:active + .panel-container {
    border-color: transparent rgba(255, 200, 0, 0.7) rgba(230, 180, 0, 0.7); }

/*.panel-sortable .panel-hdr:active,
.panel-sortable .panel-hdr:active + .panel-container {
	@include transition-border(0.4s, ease-out);
}*/
.panel-sortable.panel-locked .panel-hdr:active {
  border-top-color: #d44a4a;
  border-left-color: #dc3545;
  border-right-color: #dc3545; }
  .panel-sortable.panel-locked .panel-hdr:active + .panel-container {
    border-color: transparent #dc3545 #dc3545; }

/* panel toolbar (sits inside panel header) */
.panel-toolbar .btn-panel {
  /* add default colors for action buttons */ }
  .panel-toolbar .btn-panel[data-action="panel-collapse"], .panel-toolbar .btn-panel.js-panel-collapse {
    background: #67B4AC; }
  .panel-toolbar .btn-panel[data-action="panel-fullscreen"], .panel-toolbar .btn-panel.js-panel-fullscreen {
    background: #D2a84D; }
  .panel-toolbar .btn-panel[data-action="panel-close"], .panel-toolbar .btn-panel.js-panel-close {
    background: #Be2D2D; }

/* placeholder */
.panel-placeholder {
  background-color: #f7eaba; }
  .panel-placeholder:before {
    background: #f7eaba; }

.mod-panel-clean .panel-hdr {
  background: #fff;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f7f7f7), to(#fff));
  background-image: linear-gradient(to bottom, #f7f7f7, #fff); }

@media only screen and (max-width: 420px) {
  /* making mobile spacing a little narrow */
  .panel .panel-hdr {
    color: #060606; } }

.popover .arrow {
  border-color: inherit; }

.menu-item,
label.menu-open-button {
  background: #ffc800;
  color: #fff !important; }
  .menu-item:hover,
  label.menu-open-button:hover {
    background: #cca000; }

.app-shortcut-icon {
  background: #ecf0f1;
  color: #ecf0f1; }

.menu-open:checked + .menu-open-button {
  background: #665000; }

/* nav tabs panel */
.nav-tabs-clean .nav-item .nav-link.active {
  border-bottom: 1px solid #ffc800;
  color: #ffc800; }

.nav-tabs-clean .nav-item .nav-link:hover {
  color: #ffc800; }

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #ffc800; }

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #cca000;
  background-color: #e6b400; }

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #ffce1a; }

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #ffde66;
  background-color: #ffde66;
  border-color: #ffde66; }

.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d; }
  .custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
    background-color: #e9ecef; }

.custom-control-label::before {
  background-color: #fff;
  border: #adb5bd solid 2px; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #e6b400;
  background-color: #ffc800; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e"); }

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ffde66; }

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: #ffde66; }

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ffde66; }

.custom-switch .custom-control-label::after {
  background-color: #adb5bd; }

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff; }

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ffde66; }

.custom-select {
  color: #495057;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  border: 1px solid #ced4da; }
  .custom-select:focus {
    border-color: #ffc800; }
    .custom-select:focus::-ms-value {
      color: #495057;
      background-color: #fff; }
  .custom-select:disabled {
    color: #6c757d;
    background-color: #e9ecef; }

.custom-file-input:focus ~ .custom-file-label {
  border-color: #ffc800; }

.custom-file-input[disabled] ~ .custom-file-label,
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef; }

.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse); }

.custom-file-label {
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da; }
  .custom-file-label::after {
    color: #495057;
    background-color: #e9ecef; }

.custom-range {
  background-color: transparent; }
  .custom-range::-webkit-slider-thumb {
    background-color: #ffc800;
    border: 0; }
    .custom-range::-webkit-slider-thumb:active {
      background-color: #ffd333; }
  .custom-range::-webkit-slider-runnable-track {
    background-color: #dee2e6; }
  .custom-range::-moz-range-thumb {
    background-color: #ffc800;
    border: 0; }
    .custom-range::-moz-range-thumb:active {
      background-color: #ffd333; }
  .custom-range::-moz-range-track {
    background-color: #dee2e6; }
  .custom-range::-ms-thumb {
    background-color: #ffc800;
    border: 0; }
    .custom-range::-ms-thumb:active {
      background-color: #ffd333; }
  .custom-range::-ms-fill-lower {
    background-color: #dee2e6; }
  .custom-range::-ms-fill-upper {
    background-color: #dee2e6; }
  .custom-range:disabled::-webkit-slider-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-moz-range-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-ms-thumb {
    background-color: #adb5bd; }

.page-link {
  color: #ffc800;
  background-color: #fff;
  border: 1px solid #dee2e6;
  /*&:focus {
    outline: $pagination-focus-outline;
  }*/ }
  .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6; }

.page-item.active .page-link {
  color: #fff;
  background-color: #ffc800; }

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff; }

.pagination .page-item:first-child:not(.active) .page-link,
.pagination .page-item:last-child:not(.active) .page-link,
.pagination .page-item.disabled .page-link {
  background: #ffefb3; }

.pagination .page-link:hover {
  background-color: #ffc800 !important;
  color: #fff; }

.list-group-item {
  border: 1px solid rgba(var(--theme-rgb-primary), 0.15); }
  .list-group-item.active {
    background-color: #ffc800;
    border-color: #ffc800; }

/* backgrounds */
.bg-white {
  background-color: #fff;
  color: #666666; }

.bg-faded {
  background-color: #f7f9fa; }

.bg-offwhite-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#fff0b8));
  background-image: linear-gradient(to top, #fff, #fff0b8); }

.bg-subtlelight {
  background-color: #fff8e0; }

.bg-subtlelight-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#fff8e0));
  background-image: linear-gradient(to top, #fff, #fff8e0); }

.bg-highlight {
  background-color: #f5ecd7; }

.bg-gray-50 {
  background-color: #f9f9f9; }

.bg-gray-100 {
  background-color: #f8f9fa; }

.bg-gray-200 {
  background-color: #e9ecef; }

.bg-gray-300 {
  background-color: #dee2e6; }

.bg-gray-400 {
  background-color: #ced4da; }

.bg-gray-500 {
  background-color: #adb5bd; }

.bg-gray-600 {
  background-color: #6c757d; }

.bg-gray-700 {
  background-color: #495057; }

.bg-gray-800 {
  background-color: #343a40; }

.bg-gray-900 {
  background-color: #212529; }

/* borders */
.border-faded {
  border: 1px solid rgba(0, 0, 0, 0.07); }

/* hover any bg */
/* inherits the parent background on hover */
.hover-bg {
  background: #fff; }

/* states */
.state-selected {
  background: #bbd7ed !important; }

/* demo window */
.demo-window {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12); }
  .demo-window:before {
    background: #e5e5e5; }
  .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    background: #ccc; }

.bg-trans-gradient {
  background: linear-gradient(250deg, #477090, #f2c10d); }

.notes {
  background: #f9f4b5; }

/* disclaimer class */
.disclaimer {
  color: #a2a2a2; }

/* online status */
.status {
  position: relative; }
  .status:before {
    background: #665000;
    border: 2px solid #fff; }
  .status.status-success:before {
    background: #67B4AC; }
  .status.status-danger:before {
    background: #Be2D2D; }
  .status.status-warning:before {
    background: #D2a84D; }

/* display frame */
.frame-heading {
  color: #ffca0a; }

.frame-wrap {
  background: white; }

/* time stamp */
.time-stamp {
  color: #b38c00; }

/* data-hasmore */
[data-hasmore] {
  color: #fff; }
  [data-hasmore]:before {
    background: rgba(0, 0, 0, 0.4); }

/* code */
code {
  background: #f1eddf; }

/* select background */
::-moz-selection {
  background: #665000;
  color: #fff; }
::selection {
  background: #665000;
  color: #fff; }

::-moz-selection {
  background: #665000;
  color: #fff; }

@media only screen and (max-width: 992px) {
  .page-wrapper {
    background: #fff; }
    .page-wrapper .page-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.09); }
    .page-wrapper .page-content {
      color: #222; }
      .page-wrapper .page-content .p-g {
        padding: 1.5rem; }
    .page-wrapper .page-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.09); }
  /* Off canvas */
  .nav-mobile-slide-out .page-wrapper .page-content {
    background: #fef5d6; }
  /* mobile nav show & hide button */
  /* general */
  .mobile-nav-on .page-sidebar {
    border-right: 1px solid rgba(0, 0, 0, 0.03);
    -webkit-box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52);
            box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52); }
  .mobile-nav-on .page-content-overlay {
    background: rgba(0, 0, 0, 0.09); } }

@media only screen and (max-width: 576px) {
  /* here we turn on mobile font for smaller screens */
  /*body {
		font-family: $mobile-page-font !important;
	}*/
  /* mobile nav search */
  .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field {
    background: #fff; }
    .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field:focus {
      border-color: #ffc800; } }

/* text area */
[contenteditable="true"]:empty:not(:focus):before {
  content: attr(data-placeholder);
  color: #e6b400; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

/* add background to focused inpur prepend and append */
.form-control:focus ~ .input-group-prepend {
  background: #ffc800; }

.has-length .input-group-text {
  border-color: #ffc800; }
  .has-length .input-group-text + .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1); }

.has-length .input-group-text:not([class^="bg-"]):not([class*=" bg-"]) {
  background: #ffc800;
  color: #fff !important; }

/* help block and validation feedback texts*/
.help-block {
  color: #e6b400; }

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #ffc800; }

.settings-panel h5 {
  color: #665000; }

.settings-panel .list {
  color: #666666; }
  .settings-panel .list:hover {
    color: #333333;
    background: rgba(255, 255, 255, 0.7); }

.settings-panel .expanded:before {
  border-bottom-color: #806400; }

@media only screen and (max-width: 992px) {
  .mobile-view-activated #nff,
  .mobile-view-activated #nfm,
  .mobile-view-activated #nfh,
  .mobile-view-activated #nft,
  .mobile-view-activated #mmb {
    position: relative; }
    .mobile-view-activated #nff .onoffswitch-title,
    .mobile-view-activated #nfm .onoffswitch-title,
    .mobile-view-activated #nfh .onoffswitch-title,
    .mobile-view-activated #nft .onoffswitch-title,
    .mobile-view-activated #mmb .onoffswitch-title {
      color: #d58100 !important; }
    .mobile-view-activated #nff .onoffswitch-title-desc,
    .mobile-view-activated #nfm .onoffswitch-title-desc,
    .mobile-view-activated #nfh .onoffswitch-title-desc,
    .mobile-view-activated #nft .onoffswitch-title-desc,
    .mobile-view-activated #mmb .onoffswitch-title-desc {
      color: #ec9f28 !important; }
    .mobile-view-activated #nff:after,
    .mobile-view-activated #nfm:after,
    .mobile-view-activated #nfh:after,
    .mobile-view-activated #nft:after,
    .mobile-view-activated #mmb:after {
      background: #ecdab3;
      border: 1px solid #bc9030;
      color: black; } }

/* Hierarchical Navigation */
.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul {
  /* addressing all second, third children */ }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {
    border-left: 1px solid #a78506; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:after {
    background-color: #f1bf09; }

.bg-primary-50 {
  background-color: #ffe480;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-100 {
  background-color: #ffde66;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-200 {
  background-color: #ffd94d;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-300 {
  background-color: #ffd333;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-400 {
  background-color: #ffce1a;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-500 {
  background-color: #ffc800;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-600 {
  background-color: #e6b400;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-700 {
  background-color: #cca000;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-800 {
  background-color: #b38c00;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-800:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-900 {
  background-color: #997800;
  color: white; }
  .bg-primary-900:hover {
    color: white; }

.color-primary-50 {
  color: #ffe480; }

.color-primary-100 {
  color: #ffde66; }

.color-primary-200 {
  color: #ffd94d; }

.color-primary-300 {
  color: #ffd333; }

.color-primary-400 {
  color: #ffce1a; }

.color-primary-500 {
  color: #ffc800; }

.color-primary-600 {
  color: #e6b400; }

.color-primary-700 {
  color: #cca000; }

.color-primary-800 {
  color: #b38c00; }

.color-primary-900 {
  color: #997800; }

.bg-success-50 {
  background-color: #bcdedb;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-100 {
  background-color: #abd6d1;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-200 {
  background-color: #9acdc8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-300 {
  background-color: #89c5bf;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-400 {
  background-color: #78bcb5;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-500 {
  background-color: #67B4AC;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-600 {
  background-color: #56aca3;
  color: white; }
  .bg-success-600:hover {
    color: white; }

.bg-success-700 {
  background-color: #4d9b93;
  color: white; }
  .bg-success-700:hover {
    color: white; }

.bg-success-800 {
  background-color: #448a83;
  color: white; }
  .bg-success-800:hover {
    color: white; }

.bg-success-900 {
  background-color: #3c7973;
  color: white; }
  .bg-success-900:hover {
    color: white; }

.color-success-50 {
  color: #bcdedb; }

.color-success-100 {
  color: #abd6d1; }

.color-success-200 {
  color: #9acdc8; }

.color-success-300 {
  color: #89c5bf; }

.color-success-400 {
  color: #78bcb5; }

.color-success-500 {
  color: #67B4AC; }

.color-success-600 {
  color: #56aca3; }

.color-success-700 {
  color: #4d9b93; }

.color-success-800 {
  color: #448a83; }

.color-success-900 {
  color: #3c7973; }

.bg-info-50 {
  background-color: #7ab1dd;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-100 {
  background-color: #65a5d8;
  color: white; }
  .bg-info-100:hover {
    color: white; }

.bg-info-200 {
  background-color: #5199d2;
  color: white; }
  .bg-info-200:hover {
    color: white; }

.bg-info-300 {
  background-color: #3d8ecd;
  color: white; }
  .bg-info-300:hover {
    color: white; }

.bg-info-400 {
  background-color: #3181bf;
  color: white; }
  .bg-info-400:hover {
    color: white; }

.bg-info-500 {
  background-color: #2C73AB;
  color: white; }
  .bg-info-500:hover {
    color: white; }

.bg-info-600 {
  background-color: #276597;
  color: white; }
  .bg-info-600:hover {
    color: white; }

.bg-info-700 {
  background-color: #225882;
  color: white; }
  .bg-info-700:hover {
    color: white; }

.bg-info-800 {
  background-color: #1c4a6e;
  color: white; }
  .bg-info-800:hover {
    color: white; }

.bg-info-900 {
  background-color: #173c5a;
  color: white; }
  .bg-info-900:hover {
    color: white; }

.color-info-50 {
  color: #7ab1dd; }

.color-info-100 {
  color: #65a5d8; }

.color-info-200 {
  color: #5199d2; }

.color-info-300 {
  color: #3d8ecd; }

.color-info-400 {
  color: #3181bf; }

.color-info-500 {
  color: #2C73AB; }

.color-info-600 {
  color: #276597; }

.color-info-700 {
  color: #225882; }

.color-info-800 {
  color: #1c4a6e; }

.color-info-900 {
  color: #173c5a; }

.bg-warning-50 {
  background-color: #ecdab3;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-100 {
  background-color: #e7d09e;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-200 {
  background-color: #e1c68a;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-300 {
  background-color: #dcbc76;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-400 {
  background-color: #d7b261;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-500 {
  background-color: #D2a84D;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-600 {
  background-color: #cd9e39;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-700 {
  background-color: #bc9030;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-800 {
  background-color: #a8802a;
  color: white; }
  .bg-warning-800:hover {
    color: white; }

.bg-warning-900 {
  background-color: #947125;
  color: white; }
  .bg-warning-900:hover {
    color: white; }

.color-warning-50 {
  color: #ecdab3; }

.color-warning-100 {
  color: #e7d09e; }

.color-warning-200 {
  color: #e1c68a; }

.color-warning-300 {
  color: #dcbc76; }

.color-warning-400 {
  color: #d7b261; }

.color-warning-500 {
  color: #D2a84D; }

.color-warning-600 {
  color: #cd9e39; }

.color-warning-700 {
  color: #bc9030; }

.color-warning-800 {
  color: #a8802a; }

.color-warning-900 {
  color: #947125; }

.bg-danger-50 {
  background-color: #e38888;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-100 {
  background-color: #de7373;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-200 {
  background-color: #d95f5f;
  color: white; }
  .bg-danger-200:hover {
    color: white; }

.bg-danger-300 {
  background-color: #d44a4a;
  color: white; }
  .bg-danger-300:hover {
    color: white; }

.bg-danger-400 {
  background-color: #cf3535;
  color: white; }
  .bg-danger-400:hover {
    color: white; }

.bg-danger-500 {
  background-color: #Be2D2D;
  color: white; }
  .bg-danger-500:hover {
    color: white; }

.bg-danger-600 {
  background-color: #a92828;
  color: white; }
  .bg-danger-600:hover {
    color: white; }

.bg-danger-700 {
  background-color: #952323;
  color: white; }
  .bg-danger-700:hover {
    color: white; }

.bg-danger-800 {
  background-color: #801e1e;
  color: white; }
  .bg-danger-800:hover {
    color: white; }

.bg-danger-900 {
  background-color: #6c1919;
  color: white; }
  .bg-danger-900:hover {
    color: white; }

.color-danger-50 {
  color: #e38888; }

.color-danger-100 {
  color: #de7373; }

.color-danger-200 {
  color: #d95f5f; }

.color-danger-300 {
  color: #d44a4a; }

.color-danger-400 {
  color: #cf3535; }

.color-danger-500 {
  color: #Be2D2D; }

.color-danger-600 {
  color: #a92828; }

.color-danger-700 {
  color: #952323; }

.color-danger-800 {
  color: #801e1e; }

.color-danger-900 {
  color: #6c1919; }

.bg-fusion-50 {
  background-color: #e6b400;
  color: rgba(0, 0, 0, 0.8); }
  .bg-fusion-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-fusion-100 {
  background-color: #cca000;
  color: rgba(0, 0, 0, 0.8); }
  .bg-fusion-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-fusion-200 {
  background-color: #b38c00;
  color: rgba(0, 0, 0, 0.8); }
  .bg-fusion-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-fusion-300 {
  background-color: #997800;
  color: white; }
  .bg-fusion-300:hover {
    color: white; }

.bg-fusion-400 {
  background-color: #806400;
  color: white; }
  .bg-fusion-400:hover {
    color: white; }

.bg-fusion-500 {
  background-color: #665000;
  color: white; }
  .bg-fusion-500:hover {
    color: white; }

.bg-fusion-600 {
  background-color: #4d3c00;
  color: white; }
  .bg-fusion-600:hover {
    color: white; }

.bg-fusion-700 {
  background-color: #332800;
  color: white; }
  .bg-fusion-700:hover {
    color: white; }

.bg-fusion-800 {
  background-color: #1a1400;
  color: white; }
  .bg-fusion-800:hover {
    color: white; }

.bg-fusion-900 {
  background-color: black;
  color: white; }
  .bg-fusion-900:hover {
    color: white; }

.color-fusion-50 {
  color: #e6b400; }

.color-fusion-100 {
  color: #cca000; }

.color-fusion-200 {
  color: #b38c00; }

.color-fusion-300 {
  color: #997800; }

.color-fusion-400 {
  color: #806400; }

.color-fusion-500 {
  color: #665000; }

.color-fusion-600 {
  color: #4d3c00; }

.color-fusion-700 {
  color: #332800; }

.color-fusion-800 {
  color: #1a1400; }

.color-fusion-900 {
  color: black; }

.color-white {
  color: #fff; }

.color-black {
  color: #222222; }

.bg-primary-gradient {
  background-image: linear-gradient(250deg, rgba(153, 120, 0, 0.7), transparent); }

.bg-danger-gradient {
  background-image: linear-gradient(250deg, rgba(108, 25, 25, 0.7), transparent); }

.bg-info-gradient {
  background-image: linear-gradient(250deg, rgba(23, 60, 90, 0.7), transparent); }

.bg-warning-gradient {
  background-image: linear-gradient(250deg, rgba(148, 113, 37, 0.7), transparent); }

.bg-success-gradient {
  background-image: linear-gradient(250deg, rgba(60, 121, 115, 0.7), transparent); }

.bg-fusion-gradient {
  background-image: linear-gradient(250deg, rgba(0, 0, 0, 0.7), transparent); }

.btn-primary {
  color: #212529;
  background-color: #ffc800;
  border-color: #ffc800;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-primary:hover {
    color: #212529;
    background-color: #d9aa00;
    border-color: #cca000; }
  .btn-primary:focus, .btn-primary.focus {
    color: #212529;
    background-color: #d9aa00;
    border-color: #cca000;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 176, 6, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 176, 6, 0.5); }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #212529;
    background-color: #ffc800;
    border-color: #ffc800; }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
  .show > .btn-primary.dropdown-toggle {
    color: #212529;
    background-color: #cca000;
    border-color: #bf9600; }
    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 176, 6, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 176, 6, 0.5); }

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62; }
  .btn-secondary:focus, .btn-secondary.focus {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b; }
    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }

.btn-success {
  color: #212529;
  background-color: #67B4AC;
  border-color: #67B4AC;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-success:hover {
    color: #fff;
    background-color: #51a49b;
    border-color: #4d9b93; }
  .btn-success:focus, .btn-success.focus {
    color: #fff;
    background-color: #51a49b;
    border-color: #4d9b93;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(93, 159, 152, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(93, 159, 152, 0.5); }
  .btn-success.disabled, .btn-success:disabled {
    color: #212529;
    background-color: #67B4AC;
    border-color: #67B4AC; }
  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
  .show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #4d9b93;
    border-color: #48938b; }
    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(93, 159, 152, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(93, 159, 152, 0.5); }

.btn-info {
  color: #fff;
  background-color: #2C73AB;
  border-color: #2C73AB;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-info:hover {
    color: #fff;
    background-color: #245f8d;
    border-color: #225882; }
  .btn-info:focus, .btn-info.focus {
    color: #fff;
    background-color: #245f8d;
    border-color: #225882;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(76, 136, 184, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(76, 136, 184, 0.5); }
  .btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #2C73AB;
    border-color: #2C73AB; }
  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
  .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #225882;
    border-color: #1f5178; }
    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(76, 136, 184, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(76, 136, 184, 0.5); }

.btn-warning {
  color: #212529;
  background-color: #D2a84D;
  border-color: #D2a84D;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-warning:hover {
    color: #212529;
    background-color: #c79832;
    border-color: #bc9030; }
  .btn-warning:focus, .btn-warning.focus {
    color: #212529;
    background-color: #c79832;
    border-color: #bc9030;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(183, 148, 72, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(183, 148, 72, 0.5); }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #D2a84D;
    border-color: #D2a84D; }
  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
  .show > .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #bc9030;
    border-color: #b2882d; }
    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(183, 148, 72, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(183, 148, 72, 0.5); }

.btn-danger {
  color: #fff;
  background-color: #Be2D2D;
  border-color: #Be2D2D;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-danger:hover {
    color: #fff;
    background-color: #9f2626;
    border-color: #952323; }
  .btn-danger:focus, .btn-danger.focus {
    color: #fff;
    background-color: #9f2626;
    border-color: #952323;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(200, 77, 77, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(200, 77, 77, 0.5); }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #Be2D2D;
    border-color: #Be2D2D; }
  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
  .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #952323;
    border-color: #8a2121; }
    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(200, 77, 77, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(200, 77, 77, 0.5); }

.btn-light {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-light:hover {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6; }
  .btn-light:focus, .btn-light.focus {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }
  .btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
  .show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #e6e6e6;
    border-color: #dfdfdf; }
    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }

.btn-dark {
  color: #fff;
  background-color: #665000;
  border-color: #665000;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-dark:hover {
    color: #fff;
    background-color: #403200;
    border-color: #332800; }
  .btn-dark:focus, .btn-dark.focus {
    color: #fff;
    background-color: #403200;
    border-color: #332800;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(125, 106, 38, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(125, 106, 38, 0.5); }
  .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #665000;
    border-color: #665000; }
  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
  .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #332800;
    border-color: #261e00; }
    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(125, 106, 38, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(125, 106, 38, 0.5); }

.btn-outline-primary {
  color: #ffc800;
  border-color: #ffc800; }
  .btn-outline-primary:hover {
    color: #212529;
    background-color: #ffc800;
    border-color: #ffc800; }
  .btn-outline-primary:focus, .btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 200, 0, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 200, 0, 0.5); }
  .btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #ffc800;
    background-color: transparent; }
  .btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-primary.dropdown-toggle {
    color: #212529;
    background-color: #ffc800;
    border-color: #ffc800; }
    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 200, 0, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 200, 0, 0.5); }

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d; }
  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-outline-secondary:focus, .btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }
  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent; }
  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }

.btn-outline-success {
  color: #67B4AC;
  border-color: #67B4AC; }
  .btn-outline-success:hover {
    color: #212529;
    background-color: #67B4AC;
    border-color: #67B4AC; }
  .btn-outline-success:focus, .btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(103, 180, 172, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(103, 180, 172, 0.5); }
  .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #67B4AC;
    background-color: transparent; }
  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
  .show > .btn-outline-success.dropdown-toggle {
    color: #212529;
    background-color: #67B4AC;
    border-color: #67B4AC; }
    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(103, 180, 172, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(103, 180, 172, 0.5); }

.btn-outline-info {
  color: #2C73AB;
  border-color: #2C73AB; }
  .btn-outline-info:hover {
    color: #fff;
    background-color: #2C73AB;
    border-color: #2C73AB; }
  .btn-outline-info:focus, .btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(44, 115, 171, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(44, 115, 171, 0.5); }
  .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #2C73AB;
    background-color: transparent; }
  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
  .show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #2C73AB;
    border-color: #2C73AB; }
    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(44, 115, 171, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(44, 115, 171, 0.5); }

.btn-outline-warning {
  color: #D2a84D;
  border-color: #D2a84D; }
  .btn-outline-warning:hover {
    color: #212529;
    background-color: #D2a84D;
    border-color: #D2a84D; }
  .btn-outline-warning:focus, .btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(210, 168, 77, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(210, 168, 77, 0.5); }
  .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #D2a84D;
    background-color: transparent; }
  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
  .show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #D2a84D;
    border-color: #D2a84D; }
    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(210, 168, 77, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(210, 168, 77, 0.5); }

.btn-outline-danger {
  color: #Be2D2D;
  border-color: #Be2D2D; }
  .btn-outline-danger:hover {
    color: #fff;
    background-color: #Be2D2D;
    border-color: #Be2D2D; }
  .btn-outline-danger:focus, .btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(190, 45, 45, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(190, 45, 45, 0.5); }
  .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #Be2D2D;
    background-color: transparent; }
  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
  .show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #Be2D2D;
    border-color: #Be2D2D; }
    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(190, 45, 45, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(190, 45, 45, 0.5); }

.btn-outline-light {
  color: #fff;
  border-color: #fff; }
  .btn-outline-light:hover {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-outline-light:focus, .btn-outline-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }
  .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #fff;
    background-color: transparent; }
  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
  .show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }

.btn-outline-dark {
  color: #665000;
  border-color: #665000; }
  .btn-outline-dark:hover {
    color: #fff;
    background-color: #665000;
    border-color: #665000; }
  .btn-outline-dark:focus, .btn-outline-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(102, 80, 0, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(102, 80, 0, 0.5); }
  .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #665000;
    background-color: transparent; }
  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
  .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #665000;
    border-color: #665000; }
    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(102, 80, 0, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(102, 80, 0, 0.5); }

.border-primary {
  border-color: #ffc800 !important; }

.border-secondary {
  border-color: #6c757d !important; }

.border-success {
  border-color: #67B4AC !important; }

.border-info {
  border-color: #2C73AB !important; }

.border-warning {
  border-color: #D2a84D !important; }

.border-danger {
  border-color: #Be2D2D !important; }

.border-light {
  border-color: #fff !important; }

.border-dark {
  border-color: #665000 !important; }

.text-primary {
  color: #ffc800 !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #b38c00 !important; }

.text-secondary {
  color: #6c757d !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important; }

.text-success {
  color: #67B4AC !important; }

a.text-success:hover, a.text-success:focus {
  color: #448a83 !important; }

.text-info {
  color: #2C73AB !important; }

a.text-info:hover, a.text-info:focus {
  color: #1c4a6e !important; }

.text-warning {
  color: #D2a84D !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #a8802a !important; }

.text-danger {
  color: #Be2D2D !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #801e1e !important; }

.text-light {
  color: #fff !important; }

a.text-light:hover, a.text-light:focus {
  color: #d9d9d9 !important; }

.text-dark {
  color: #665000 !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #1a1400 !important; }

.bg-primary {
  background-color: #ffc800 !important; }

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #cca000 !important; }

.bg-secondary {
  background-color: #6c757d !important; }

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #545b62 !important; }

.bg-success {
  background-color: #67B4AC !important; }

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #4d9b93 !important; }

.bg-info {
  background-color: #2C73AB !important; }

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #225882 !important; }

.bg-warning {
  background-color: #D2a84D !important; }

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #bc9030 !important; }

.bg-danger {
  background-color: #Be2D2D !important; }

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #952323 !important; }

.bg-light {
  background-color: #fff !important; }

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #e6e6e6 !important; }

.bg-dark {
  background-color: #665000 !important; }

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #332800 !important; }

:root {
  --theme-primary: #ffc800;
  --theme-secondary: #6c757d;
  --theme-success: #67B4AC;
  --theme-info: #2C73AB;
  --theme-warning: #D2a84D;
  --theme-danger: #Be2D2D;
  --theme-light: #fff;
  --theme-dark: #665000;
  --theme-rgb-primary: 255,200,0;
  --theme-rgb-success: 103,180,172;
  --theme-rgb-info: 44,115,171;
  --theme-rgb-warning: 210,168,77;
  --theme-rgb-danger: 190,45,45;
  --theme-rgb-fusion: 102,80,0;
  --theme-primary-50: #ffe480;
  --theme-primary-100: #ffde66;
  --theme-primary-200: #ffd94d;
  --theme-primary-300: #ffd333;
  --theme-primary-400: #ffce1a;
  --theme-primary-500: #ffc800;
  --theme-primary-600: #e6b400;
  --theme-primary-700: #cca000;
  --theme-primary-800: #b38c00;
  --theme-primary-900: #997800;
  --theme-success-50: #bcdedb;
  --theme-success-100: #abd6d1;
  --theme-success-200: #9acdc8;
  --theme-success-300: #89c5bf;
  --theme-success-400: #78bcb5;
  --theme-success-500: #67B4AC;
  --theme-success-600: #56aca3;
  --theme-success-700: #4d9b93;
  --theme-success-800: #448a83;
  --theme-success-900: #3c7973;
  --theme-info-50: #7ab1dd;
  --theme-info-100: #65a5d8;
  --theme-info-200: #5199d2;
  --theme-info-300: #3d8ecd;
  --theme-info-400: #3181bf;
  --theme-info-500: #2C73AB;
  --theme-info-600: #276597;
  --theme-info-700: #225882;
  --theme-info-800: #1c4a6e;
  --theme-info-900: #173c5a;
  --theme-warning-50: #ecdab3;
  --theme-warning-100: #e7d09e;
  --theme-warning-200: #e1c68a;
  --theme-warning-300: #dcbc76;
  --theme-warning-400: #d7b261;
  --theme-warning-500: #D2a84D;
  --theme-warning-600: #cd9e39;
  --theme-warning-700: #bc9030;
  --theme-warning-800: #a8802a;
  --theme-warning-900: #947125;
  --theme-danger-50: #e38888;
  --theme-danger-100: #de7373;
  --theme-danger-200: #d95f5f;
  --theme-danger-300: #d44a4a;
  --theme-danger-400: #cf3535;
  --theme-danger-500: #Be2D2D;
  --theme-danger-600: #a92828;
  --theme-danger-700: #952323;
  --theme-danger-800: #801e1e;
  --theme-danger-900: #6c1919;
  --theme-fusion-50: #e6b400;
  --theme-fusion-100: #cca000;
  --theme-fusion-200: #b38c00;
  --theme-fusion-300: #997800;
  --theme-fusion-400: #806400;
  --theme-fusion-500: #665000;
  --theme-fusion-600: #4d3c00;
  --theme-fusion-700: #332800;
  --theme-fusion-800: #1a1400;
  --theme-fusion-900: black; }

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #ffc800; }

.ct-series-a .ct-slice-pie, .ct-series-a .ct-slice-donut-solid, .ct-series-a .ct-area {
  fill: #ffc800; }

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #Be2D2D; }

.ct-series-b .ct-slice-pie, .ct-series-b .ct-slice-donut-solid, .ct-series-b .ct-area {
  fill: #Be2D2D; }

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #D2a84D; }

.ct-series-c .ct-slice-pie, .ct-series-c .ct-slice-donut-solid, .ct-series-c .ct-area {
  fill: #D2a84D; }

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #2C73AB; }

.ct-series-d .ct-slice-pie, .ct-series-d .ct-slice-donut-solid, .ct-series-d .ct-area {
  fill: #2C73AB; }

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #665000; }

.ct-series-e .ct-slice-pie, .ct-series-e .ct-slice-donut-solid, .ct-series-e .ct-area {
  fill: #665000; }

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: #67B4AC; }

.ct-series-f .ct-slice-pie, .ct-series-f .ct-slice-donut-solid, .ct-series-f .ct-area {
  fill: #67B4AC; }

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: #2C73AB; }

.ct-series-g .ct-slice-pie, .ct-series-g .ct-slice-donut-solid, .ct-series-g .ct-area {
  fill: #2C73AB; }

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: #997800; }

.ct-series-h .ct-slice-pie, .ct-series-h .ct-slice-donut-solid, .ct-series-h .ct-area {
  fill: #997800; }

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: #de7373; }

.ct-series-i .ct-slice-pie, .ct-series-i .ct-slice-donut-solid, .ct-series-i .ct-area {
  fill: #de7373; }

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: #e1c68a; }

.ct-series-j .ct-slice-pie, .ct-series-j .ct-slice-donut-solid, .ct-series-j .ct-area {
  fill: #e1c68a; }

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: #6c1919; }

.ct-series-k .ct-slice-pie, .ct-series-k .ct-slice-donut-solid, .ct-series-k .ct-area {
  fill: #6c1919; }

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: #997800; }

.ct-series-l .ct-slice-pie, .ct-series-l .ct-slice-donut-solid, .ct-series-l .ct-area {
  fill: #997800; }

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: #89c5bf; }

.ct-series-m .ct-slice-pie, .ct-series-m .ct-slice-donut-solid, .ct-series-m .ct-area {
  fill: #89c5bf; }

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: #3d8ecd; }

.ct-series-n .ct-slice-pie, .ct-series-n .ct-slice-donut-solid, .ct-series-n .ct-area {
  fill: #3d8ecd; }

.ct-series-o .ct-point, .ct-series-o .ct-line, .ct-series-o .ct-bar, .ct-series-o .ct-slice-donut {
  stroke: #ffd333; }

.ct-series-o .ct-slice-pie, .ct-series-o .ct-slice-donut-solid, .ct-series-o .ct-area {
  fill: #ffd333; }

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border-color: #E5E5E5; }

.select2-dropdown {
  border-color: #E5E5E5; }

.select2-search--dropdown:before {
  color: #ffc800; }

.select2-results__message {
  color: #ffc800 !important; }

.select2-container--open .select2-dropdown--above {
  border-color: #ffc800; }

.select2-container--open .select2-dropdown--below {
  border-color: #ffc800; }

.select2-container--default .select2-search--dropdown .select2-search__field {
  color: #495057;
  background-color: #fff;
  border-color: #E5E5E5;
  -webkit-box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025);
          box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025); }
  .select2-container--default .select2-search--dropdown .select2-search__field:focus {
    border-color: #cccccc; }

.select2-container--default .select2-results__group {
  padding: 0.5rem 0;
  color: #8e8e8e; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background: #fff2c2;
  color: #997800; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #ffc800;
  color: #fff; }

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-color: #ffc800; }

.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ffc800; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: #fff2c2;
  border-color: #ffce1a;
  color: #997800; }
  .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    color: #ffd333; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {
      color: #ffc800; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:active {
      -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset; }

.select2-container--default .select2-selection--single .select2-selection__clear {
  color: #Be2D2D; }
  .select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: #a92828; }

.select2-results__message {
  color: #Be2D2D; }

.sorting_asc,
.sorting_desc,
.even .sorting_1 {
  background-color: rgba(255, 200, 0, 0.03); }

.odd .sorting_1 {
  background-color: rgba(255, 200, 0, 0.04); }

.table-dark .sorting_asc,
.table-dark .sorting_desc,
.table-dark .even .sorting_1 {
  background-color: rgba(210, 168, 77, 0.15); }

.table-dark .odd .sorting_1 {
  background-color: rgba(210, 168, 77, 0.15); }

table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  background-color: #ffc800; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  background-color: #67B4AC; }

.dataTables_empty {
  color: #Be2D2D; }

.dataTables_wrapper tr.child td.child .dtr-details:before {
  color: #78bcb5; }

.dataTables_wrapper tr.child td.child .dtr-details:after {
  background: #9acdc8; }

div.dt-autofill-background {
  opacity: 0.2;
  background-color: #000; }

div.dt-autofill-handle {
  background: #ffc800; }

div.dt-autofill-select {
  background-color: #ffc800; }

/* FixedColumns */
.DTFC_LeftHeadWrapper:before,
.DTFC_LeftBodyWrapper:before,
.DTFC_LeftFootWrapper:before {
  background: #Be2D2D; }

/* KeyTable */
table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
  -webkit-box-shadow: inset 0 0 0px 1px #ffc800;
          box-shadow: inset 0 0 0px 1px #ffc800;
  background: rgba(255, 200, 0, 0.1); }

table.dataTable:not(.table-dark) tr.dtrg-group td {
  background: #fff; }

tr.dt-rowReorder-moving {
  outline-color: #67B4AC; }

table.dt-rowReorder-float {
  outline-color: #ffc800; }

/* Select */
table.dataTable.table-bordered .selected td {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable.table-bordered td.selected {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable tbody > tr.selected,
table.dataTable tbody > tr > .selected {
  -webkit-box-shadow: inset 0 0 0px 1px #ffc800;
          box-shadow: inset 0 0 0px 1px #ffc800;
  background: rgba(255, 200, 0, 0.1); }

.datepicker table tr td.old,
.datepicker table tr td.new {
  color: #ffce1a; }

.datepicker table tr td.active:active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active,
.datepicker table tr td.selected,
.datepicker table tr td.selected.highlighted,
.datepicker table tr td span.active.active,
.datepicker table tr td span.focused {
  background-color: #ffce1a;
  border-color: #ffc800;
  color: #fff; }

.datepicker table tr td.active:active:hover,
.datepicker table tr td.active.highlighted:active:hover,
.datepicker table tr td.active.active:hover,
.datepicker table tr td.active.highlighted.active:hover,
.datepicker table tr td.active:active:focus,
.datepicker table tr td.active.highlighted:active:focus,
.datepicker table tr td.active.active:focus,
.datepicker table tr td.active.highlighted.active:focus,
.datepicker table tr td.active:active.focus,
.datepicker table tr td.active.highlighted:active.focus,
.datepicker table tr td.active.active.focus,
.datepicker table tr td.active.highlighted.active.focus,
.datepicker table tr td.selected:active:hover,
.datepicker table tr td.selected.highlighted:active:hover,
.datepicker table tr td.selected.active:hover,
.datepicker table tr td.selected.highlighted.active:hover,
.datepicker table tr td.selected:active:focus,
.datepicker table tr td.selected.highlighted:active:focus,
.datepicker table tr td.selected.active:focus,
.datepicker table tr td.selected.highlighted.active:focus,
.datepicker table tr td.selected:active.focus,
.datepicker table tr td.selected.highlighted:active.focus,
.datepicker table tr td.selected.active.focus,
.datepicker table tr td.selected.highlighted.active.focus,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.highlighted:hover {
  background-color: #e6b400;
  border-color: #cca000;
  color: #fff; }

.datepicker.datepicker-inline {
  border: 1px solid #ebedf2; }

.datepicker thead th.prev, .datepicker thead th.datepicker-switch, .datepicker thead th.next {
  color: #a1a8c3; }

.daterangepicker table tr td.old,
.daterangepicker table tr td.new {
  color: #ffce1a; }

.daterangepicker table tr td.active:active,
.daterangepicker table tr td.active.highlighted:active,
.daterangepicker table tr td.active.active,
.daterangepicker table tr td.active.highlighted.active,
.daterangepicker table tr td.selected,
.daterangepicker table tr td.selected.highlighted,
.daterangepicker table tr td span.active.active,
.daterangepicker table tr td span.focused {
  background-color: #ffce1a;
  color: #fff; }

.daterangepicker table tr td.active:active:hover,
.daterangepicker table tr td.active.highlighted:active:hover,
.daterangepicker table tr td.active.active:hover,
.daterangepicker table tr td.active.highlighted.active:hover,
.daterangepicker table tr td.active:active:focus,
.daterangepicker table tr td.active.highlighted:active:focus,
.daterangepicker table tr td.active.active:focus,
.daterangepicker table tr td.active.highlighted.active:focus,
.daterangepicker table tr td.active:active.focus,
.daterangepicker table tr td.active.highlighted:active.focus,
.daterangepicker table tr td.active.active.focus,
.daterangepicker table tr td.active.highlighted.active.focus,
.daterangepicker table tr td.selected:active:hover,
.daterangepicker table tr td.selected.highlighted:active:hover,
.daterangepicker table tr td.selected.active:hover,
.daterangepicker table tr td.selected.highlighted.active:hover,
.daterangepicker table tr td.selected:active:focus,
.daterangepicker table tr td.selected.highlighted:active:focus,
.daterangepicker table tr td.selected.active:focus,
.daterangepicker table tr td.selected.highlighted.active:focus,
.daterangepicker table tr td.selected:active.focus,
.daterangepicker table tr td.selected.highlighted:active.focus,
.daterangepicker table tr td.selected.active.focus,
.daterangepicker table tr td.selected.highlighted.active.focus,
.daterangepicker table tr td.selected:hover,
.daterangepicker table tr td.selected.highlighted:hover {
  background-color: #e6b400;
  color: #fff; }

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
  border-color: #a1a8c3; }

.daterangepicker .in-range.available {
  background-color: #e7d09e; }

.daterangepicker .off.ends.in-range.available {
  background-color: #ecdab3; }

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: #dcbc76; }

.daterangepicker .calendar-table table thead tr th.month {
  color: #a1a8c3; }

.daterangepicker .ranges li.active {
  background-color: #ffc800; }

.irs--flat .irs-bar,
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single,
.irs--flat .irs-handle > i:first-child {
  background-color: #ffc800; }

.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  border-top-color: #ffc800; }

.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child {
  background-color: #e6b400; }

.irs--big .irs-bar {
  background-color: #ffd333;
  border-color: #ffc800;
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), color-stop(30%, #ffd333), to(#ffc800));
  background: linear-gradient(to bottom, #ffffff 0%, #ffd333 30%, #ffc800 100%); }

.irs--big .irs-from,
.irs--big .irs-to,
.irs--big .irs-single {
  background: #ffc800; }

.irs--modern .irs-bar {
  background: #56aca3;
  background: -webkit-gradient(linear, left top, left bottom, from(#78bcb5), to(#56aca3));
  background: linear-gradient(to bottom, #78bcb5 0%, #56aca3 100%); }

.irs--modern .irs-from,
.irs--modern .irs-to,
.irs--modern .irs-single {
  background-color: #67B4AC; }

.irs--modern .irs-from:before,
.irs--modern .irs-to:before,
.irs--modern .irs-single:before {
  border-top-color: #67B4AC; }

.irs--sharp .irs-bar,
.irs--sharp .irs-handle,
.irs--sharp .irs-from,
.irs--sharp .irs-to,
.irs--sharp .irs-single {
  background-color: #Be2D2D; }

.irs--sharp .irs-handle > i:first-child,
.irs--sharp .irs-from:before,
.irs--sharp .irs-to:before,
.irs--sharp .irs-single:before {
  border-top-color: #Be2D2D; }

.irs--sharp .irs-min,
.irs--sharp .irs-max {
  background-color: #801e1e; }

.irs--round .irs-handle {
  border-color: #2C73AB; }

.irs--round .irs-bar,
.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single {
  background-color: #2C73AB; }

.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before {
  border-top-color: #2C73AB; }

body:not(.mod-pace-custom) .pace .pace-progress {
  background: #ffc800; }

.mod-pace-custom .pace {
  background: #fff; }
  .mod-pace-custom .pace .pace-progress {
    background-color: #ffc800;
    background-image: linear-gradient(135deg, #ffc800 0%, #ffc800 25%, #cca000 25%, #cca000 50%, #ffc800 50%, #ffc800 75%, #cca000 75%, #cca000 100%); }

.mod-pace-custom.pace-running .page-content:before {
  background-color: #fef5d6; }

/* #Reset userselect
========================================================================== */
#myapp-0 {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }

#myapp-14 {
  -webkit-box-shadow: 0 0 0 3px #000000;
          box-shadow: 0 0 0 3px #000000; }

.page-sidebar,
.nav-footer,
.page-logo {
  background-image: none;
  background-color: #b98500; }

.page-content-wrapper {
  background: #fff; }

.page-content-wrapper a:not(.btn):not(.badge):not(.dropdown-item):not(.nav-link):not(.navbar-brand):not(.card-title):not([class*="fc-"]):not([class*="text-"]):not(.btn-search-close),
.modal-body a:not(.btn):not(.badge):not(.dropdown-item):not(.nav-link):not(.page-link):not(.navbar-brand):not(.card-title) {
  color: #333333; }

/*# sourceMappingURL=cust-theme-14.css.map */
