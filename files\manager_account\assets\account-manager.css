/* Account Manager Enhanced CSS */

/* Global Animations */
@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounceIn {
    0%, 20%, 40%, 60%, 80% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        opacity: 0;
        transform: scale3d(.3, .3, .3);
    }
    20% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
    40% {
        transform: scale3d(.9, .9, .9);
    }
    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }
    80% {
        transform: scale3d(.97, .97, .97);
    }
    100% {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
    }
}

/* Enhanced Card Styles */
.enhanced-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
    position: relative;
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.enhanced-card:hover::before {
    left: 100%;
}

.enhanced-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Interactive Buttons */
.btn-interactive {
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
    padding: 12px 30px;
}

.btn-interactive::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-interactive:hover::before {
    width: 300px;
    height: 300px;
}

.btn-interactive:active {
    transform: scale(0.95);
}

/* Gradient Buttons */
.btn-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.btn-gradient-danger {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    color: white;
}

.btn-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status Indicators */
.status-online {
    color: #28a745;
    animation: pulse 2s infinite;
}

.status-offline {
    color: #6c757d;
}

.status-banned {
    color: #dc3545;
    animation: glow 2s infinite;
}

/* Table Enhancements */
.table-enhanced {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 15px;
}

.table-enhanced tbody tr {
    transition: all 0.3s ease;
}

.table-enhanced tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Modal Enhancements */
.modal-enhanced .modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: bounceIn 0.6s ease-out;
}

.modal-enhanced .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    border: none;
    padding: 20px 30px;
}

.modal-enhanced .modal-body {
    padding: 30px;
}

.modal-enhanced .modal-footer {
    border: none;
    padding: 20px 30px;
}

/* Form Enhancements */
.form-enhanced .form-control {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-enhanced .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.form-enhanced .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Progress Bars */
.progress-enhanced {
    height: 10px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.progress-enhanced .progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-enhanced .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Notification Styles */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    padding: 20px;
    z-index: 9999;
    animation: slideInFromRight 0.5s ease-out;
    max-width: 400px;
}

.notification-toast.success {
    border-left: 5px solid #28a745;
}

.notification-toast.error {
    border-left: 5px solid #dc3545;
}

.notification-toast.warning {
    border-left: 5px solid #ffc107;
}

.notification-toast.info {
    border-left: 5px solid #17a2b8;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .enhanced-card {
        margin-bottom: 20px;
    }
    
    .btn-interactive {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .table-enhanced {
        font-size: 14px;
    }
    
    .modal-enhanced .modal-dialog {
        margin: 10px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .table-enhanced tbody tr:hover {
        background: #4a5568;
    }
    
    .form-enhanced .form-control {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .notification-toast {
        background: #2d3748;
        color: #e2e8f0;
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .enhanced-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn-interactive {
        display: none;
    }
    
    .modal-enhanced {
        display: none;
    }
}
