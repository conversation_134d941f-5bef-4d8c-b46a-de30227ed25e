<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-pie"></i> ทดสอบการวิเคราะห์ตัวละครครบถ้วน
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">การวิเคราะห์ตัวละครแบบครบถ้วน</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>เพิ่มแล้ว:</strong> การวิเคราะห์ตัวละครครบถ้วน 6 หมวดหมู่
                </div>
                
                <h5>📊 หมวดหมู่การวิเคราะห์ที่เพิ่ม</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-line"></i> Level Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Level 1-50 (เริ่มต้น)</li>
                                    <li>• Level 51-100 (กลาง)</li>
                                    <li>• Level 101-150 (สูง)</li>
                                    <li>• Level 151-200 (ผู้เชี่ยวชาญ)</li>
                                    <li>• Level 200+ (ปรมาจารย์)</li>
                                    <li>• เลเวลเฉลี่ย/สูงสุด/ต่ำสุด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-coins"></i> Wealth Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• ไม่มี Alz</li>
                                    <li>• 1-1M Alz</li>
                                    <li>• 1M-10M Alz</li>
                                    <li>• 10M-100M Alz</li>
                                    <li>• 100M-1B Alz</li>
                                    <li>• 1B+ Alz (เศรษฐี)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fal fa-clock"></i> Activity Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• ไม่เคยเล่น</li>
                                    <li>• เล่น < 1 ชม.</li>
                                    <li>• เล่น 1-10 ชม.</li>
                                    <li>• เล่น 10-100 ชม.</li>
                                    <li>• เล่น 100-1000 ชม.</li>
                                    <li>• เล่น 1000+ ชม. (ฮาร์ดคอร์)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fal fa-calendar"></i> Creation Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• สร้างวันนี้</li>
                                    <li>• สร้างสัปดาห์นี้</li>
                                    <li>• สร้างเดือนนี้</li>
                                    <li>• สร้างปีนี้</li>
                                    <li>• สร้างเก่ากว่า 1 ปี</li>
                                    <li>• ตัวละครเก่าสุด/ใหม่สุด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0"><i class="fal fa-globe"></i> World Distribution</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• จำนวนตัวละครแต่ละ World</li>
                                    <li>• เลเวลเฉลี่ยแต่ละ World</li>
                                    <li>• ผู้เล่นออนไลน์แต่ละ World</li>
                                    <li>• การกระจายตัวของผู้เล่น</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-dark">
                            <div class="card-header bg-dark text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-bar"></i> Progression Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Beginner (1-10)</li>
                                    <li>• Novice (11-50)</li>
                                    <li>• Intermediate (51-100)</li>
                                    <li>• Advanced (101-150)</li>
                                    <li>• Expert (151-200)</li>
                                    <li>• Master (200+)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔍 ตัวอย่างข้อมูลจาก Database</h5>
                <?php
                try {
                    // ทดสอบ Level Analysis
                    $sql = "SELECT 
                                COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                                COUNT(CASE WHEN LEV BETWEEN 51 AND 100 THEN 1 END) as level_51_100,
                                COUNT(CASE WHEN LEV BETWEEN 101 AND 150 THEN 1 END) as level_101_150,
                                COUNT(CASE WHEN LEV BETWEEN 151 AND 200 THEN 1 END) as level_151_200,
                                COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                                AVG(CAST(LEV AS FLOAT)) as avg_level,
                                MAX(LEV) as max_level,
                                MIN(LEV) as min_level
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        echo "<div class='row'>";
                        echo "<div class='col-md-6'>";
                        echo "<h6>📈 Level Analysis:</h6>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm table-striped'>";
                        echo "<tr><td>Level 1-50</td><td><span class='badge badge-success'>" . number_format($row['level_1_50']) . "</span></td></tr>";
                        echo "<tr><td>Level 51-100</td><td><span class='badge badge-info'>" . number_format($row['level_51_100']) . "</span></td></tr>";
                        echo "<tr><td>Level 101-150</td><td><span class='badge badge-warning'>" . number_format($row['level_101_150']) . "</span></td></tr>";
                        echo "<tr><td>Level 151-200</td><td><span class='badge badge-danger'>" . number_format($row['level_151_200']) . "</span></td></tr>";
                        echo "<tr><td>Level 200+</td><td><span class='badge badge-dark'>" . number_format($row['level_200_plus']) . "</span></td></tr>";
                        echo "<tr><td><strong>เลเวลเฉลี่ย</strong></td><td><strong>" . number_format($row['avg_level'], 1) . "</strong></td></tr>";
                        echo "<tr><td><strong>เลเวลสูงสุด</strong></td><td><strong>" . number_format($row['max_level']) . "</strong></td></tr>";
                        echo "</table></div></div>";
                        
                        // ทดสอบ Wealth Analysis
                        $sql = "SELECT 
                                    COUNT(CASE WHEN Alz = 0 THEN 1 END) as no_alz,
                                    COUNT(CASE WHEN Alz BETWEEN 1 AND 1000000 THEN 1 END) as alz_1m,
                                    COUNT(CASE WHEN Alz BETWEEN 1000001 AND 10000000 THEN 1 END) as alz_10m,
                                    COUNT(CASE WHEN Alz BETWEEN 10000001 AND 100000000 THEN 1 END) as alz_100m,
                                    COUNT(CASE WHEN Alz BETWEEN 100000001 AND 1000000000 THEN 1 END) as alz_1b,
                                    COUNT(CASE WHEN Alz > 1000000000 THEN 1 END) as alz_1b_plus,
                                    AVG(CAST(Alz AS BIGINT)) as avg_alz,
                                    SUM(CAST(Alz AS BIGINT)) as total_alz
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                        
                        $result2 = sqlsrv_query($conn, $sql);
                        
                        if ($result2 && $row2 = sqlsrv_fetch_array($result2, SQLSRV_FETCH_ASSOC)) {
                            echo "<div class='col-md-6'>";
                            echo "<h6>💰 Wealth Analysis:</h6>";
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm table-striped'>";
                            echo "<tr><td>ไม่มี Alz</td><td><span class='badge badge-secondary'>" . number_format($row2['no_alz']) . "</span></td></tr>";
                            echo "<tr><td>1-1M Alz</td><td><span class='badge badge-success'>" . number_format($row2['alz_1m']) . "</span></td></tr>";
                            echo "<tr><td>1M-10M Alz</td><td><span class='badge badge-info'>" . number_format($row2['alz_10m']) . "</span></td></tr>";
                            echo "<tr><td>10M-100M Alz</td><td><span class='badge badge-warning'>" . number_format($row2['alz_100m']) . "</span></td></tr>";
                            echo "<tr><td>100M-1B Alz</td><td><span class='badge badge-danger'>" . number_format($row2['alz_1b']) . "</span></td></tr>";
                            echo "<tr><td>1B+ Alz</td><td><span class='badge badge-dark'>" . number_format($row2['alz_1b_plus']) . "</span></td></tr>";
                            echo "<tr><td><strong>Alz เฉลี่ย</strong></td><td><strong>" . number_format($row2['avg_alz']) . "</strong></td></tr>";
                            echo "</table></div></div>";
                        }
                        
                        echo "</div>";
                    }
                    
                    // ทดสอบ Activity Analysis
                    $sql = "SELECT 
                                COUNT(CASE WHEN PlayTime = 0 THEN 1 END) as never_played,
                                COUNT(CASE WHEN PlayTime BETWEEN 1 AND 3600 THEN 1 END) as played_1h,
                                COUNT(CASE WHEN PlayTime BETWEEN 3601 AND 36000 THEN 1 END) as played_10h,
                                COUNT(CASE WHEN PlayTime BETWEEN 36001 AND 360000 THEN 1 END) as played_100h,
                                COUNT(CASE WHEN PlayTime BETWEEN 360001 AND 3600000 THEN 1 END) as played_1000h,
                                COUNT(CASE WHEN PlayTime > 3600000 THEN 1 END) as played_1000h_plus,
                                AVG(CAST(PlayTime AS FLOAT)) as avg_playtime,
                                SUM(CAST(PlayTime AS BIGINT)) as total_playtime
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    
                    $result3 = sqlsrv_query($conn, $sql);
                    
                    if ($result3 && $row3 = sqlsrv_fetch_array($result3, SQLSRV_FETCH_ASSOC)) {
                        echo "<div class='row mt-3'>";
                        echo "<div class='col-md-6'>";
                        echo "<h6>⏰ Activity Analysis:</h6>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm table-striped'>";
                        echo "<tr><td>ไม่เคยเล่น</td><td><span class='badge badge-secondary'>" . number_format($row3['never_played']) . "</span></td></tr>";
                        echo "<tr><td>เล่น < 1 ชม.</td><td><span class='badge badge-success'>" . number_format($row3['played_1h']) . "</span></td></tr>";
                        echo "<tr><td>เล่น 1-10 ชม.</td><td><span class='badge badge-info'>" . number_format($row3['played_10h']) . "</span></td></tr>";
                        echo "<tr><td>เล่น 10-100 ชม.</td><td><span class='badge badge-warning'>" . number_format($row3['played_100h']) . "</span></td></tr>";
                        echo "<tr><td>เล่น 100-1000 ชม.</td><td><span class='badge badge-danger'>" . number_format($row3['played_1000h']) . "</span></td></tr>";
                        echo "<tr><td>เล่น 1000+ ชม.</td><td><span class='badge badge-dark'>" . number_format($row3['played_1000h_plus']) . "</span></td></tr>";
                        echo "<tr><td><strong>เวลาเล่นเฉลี่ย</strong></td><td><strong>" . number_format($row3['avg_playtime'] / 3600, 1) . " ชม.</strong></td></tr>";
                        echo "</table></div></div>";
                        
                        // ทดสอบ World Distribution
                        $sql = "SELECT 
                                    WorldIdx,
                                    COUNT(*) as count,
                                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                                    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_count
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                GROUP BY WorldIdx
                                ORDER BY WorldIdx";
                        
                        $result4 = sqlsrv_query($conn, $sql);
                        
                        if ($result4) {
                            echo "<div class='col-md-6'>";
                            echo "<h6>🌍 World Distribution:</h6>";
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm table-striped'>";
                            echo "<thead><tr><th>World</th><th>ตัวละคร</th><th>เลเวลเฉลี่ย</th><th>ออนไลน์</th></tr></thead>";
                            echo "<tbody>";
                            
                            while ($row4 = sqlsrv_fetch_array($result4, SQLSRV_FETCH_ASSOC)) {
                                echo "<tr>";
                                echo "<td><strong>World " . $row4['WorldIdx'] . "</strong></td>";
                                echo "<td>" . number_format($row4['count']) . "</td>";
                                echo "<td>" . number_format($row4['avg_level'], 1) . "</td>";
                                echo "<td><span class='badge badge-success'>" . number_format($row4['online_count']) . "</span></td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table></div></div>";
                        }
                        
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบการแสดงผล</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติครบถ้วน
                    </button>
                    <button class="btn btn-info" onclick="testAnalysis()">
                        <i class="fal fa-chart-pie"></i> ทดสอบการวิเคราะห์
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการเพิ่มฟีเจอร์</h5>
                <div class="accordion" id="featuresAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. Level & Progression Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>การกระจายตามระดับ (Level 1-50, 51-100, 101-150, 151-200, 200+)</li>
                                    <li>เลเวลเฉลี่ย, สูงสุด, ต่ำสุด</li>
                                    <li>การจัดกลุ่มตามความก้าวหน้า (Beginner → Master)</li>
                                    <li>แสดงสีตามระดับความยาก</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. Wealth & Activity Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>การวิเคราะห์ความมั่งคั่ง (Alz) แบ่งเป็น 6 ระดับ</li>
                                    <li>การวิเคราะห์กิจกรรม (PlayTime) แบ่งเป็น 6 ระดับ</li>
                                    <li>Alz เฉลี่ย, รวม และการกระจาย</li>
                                    <li>เวลาเล่นเฉลี่ย, รวม และการจัดกลุ่ม</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Creation & World Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>การวิเคราะห์การสร้างตัวละคร (วันนี้, สัปดาห์, เดือน, ปี)</li>
                                    <li>ตัวละครเก่าสุดและใหม่สุด</li>
                                    <li>การกระจายตาม World พร้อมเลเวลเฉลี่ย</li>
                                    <li>ผู้เล่นออนไลน์แต่ละ World</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการเพิ่มการวิเคราะห์:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Level Analysis (5 ระดับ)</li>
                                <li>✅ Wealth Analysis (6 ระดับ)</li>
                                <li>✅ Activity Analysis (6 ระดับ)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Creation Analysis (5 ช่วงเวลา)</li>
                                <li>✅ World Distribution</li>
                                <li>✅ Progression Analysis (6 ระดับ)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function testAnalysis() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> เปิดหน้าสถิติเพื่อดูการวิเคราะห์ตัวละครครบถ้วน 6 หมวดหมู่</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
