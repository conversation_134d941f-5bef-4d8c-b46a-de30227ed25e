<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quantity System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-case { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Quantity System</h1>
        <p>ทดสอบการส่งไอเทมตามจำนวน Quantity ที่กำหนด</p>
        
        <!-- Manual Test Form -->
        <div class="test-case">
            <h3>📝 Manual Test</h3>
            <form id="testForm">
                <div class="form-group">
                    <label for="playerUsername">Player Username:</label>
                    <input type="text" id="playerUsername" value="test_player" required>
                </div>
                
                <div class="form-group">
                    <label for="itemCode">Item Code (Hex):</label>
                    <input type="text" id="itemCode" value="0000007B00000000" required>
                </div>
                
                <div class="form-group">
                    <label for="optionsCode">Options Code (Hex):</label>
                    <input type="text" id="optionsCode" value="0000000000000000" required>
                </div>
                
                <div class="form-group">
                    <label for="quantity">Quantity:</label>
                    <input type="number" id="quantity" value="5" min="1" max="100" required>
                </div>
                
                <div class="form-group">
                    <label for="duration">Duration:</label>
                    <input type="number" id="duration" value="31" required>
                </div>
                
                <div class="form-group">
                    <label for="sendMethod">Send Method:</label>
                    <select id="sendMethod" required>
                        <option value="inventory">ส่งไปยัง Cash Inventory</option>
                        <option value="mail">ส่งไปยัง Mail System</option>
                        <option value="warehouse">ส่งไปยัง Event Inventory</option>
                    </select>
                </div>
                
                <button type="submit">🚀 Test Send Item</button>
            </form>
        </div>
        
        <!-- Automated Tests -->
        <div class="test-case">
            <h3>🤖 Automated Tests</h3>
            <button onclick="runAllTests()">▶️ Run All Tests</button>
            <button onclick="testSingleItem()">1️⃣ Test Single Item (Quantity = 1)</button>
            <button onclick="testMultipleItems()">5️⃣ Test Multiple Items (Quantity = 5)</button>
            <button onclick="testLargeQuantity()">🔢 Test Large Quantity (Quantity = 10)</button>
            <button onclick="testDifferentMethods()">📮 Test All Methods</button>
        </div>
        
        <!-- Results -->
        <div id="results"></div>
    </div>

    <script>
        function showResult(type, title, content) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Time: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function sendTestItem(data) {
            try {
                const response = await fetch('files/game_systems/send_item.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                return result;
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        // Manual form submission
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                playerUsername: document.getElementById('playerUsername').value,
                itemCode: document.getElementById('itemCode').value,
                optionsCode: document.getElementById('optionsCode').value,
                quantity: parseInt(document.getElementById('quantity').value),
                duration: parseInt(document.getElementById('duration').value),
                sendMethod: document.getElementById('sendMethod').value,
                adminUsername: 'test_admin'
            };
            
            showResult('info', 'Sending Test Item...', JSON.stringify(formData, null, 2));
            
            const result = await sendTestItem(formData);
            
            if (result.success) {
                showResult('success', 'Test Successful!', JSON.stringify(result, null, 2));
            } else {
                showResult('error', 'Test Failed!', JSON.stringify(result, null, 2));
            }
        });
        
        async function testSingleItem() {
            const testData = {
                playerUsername: 'test_player_single',
                itemCode: '0000007B00000000',
                optionsCode: '0000000000000000',
                quantity: 1,
                duration: 31,
                sendMethod: 'inventory',
                adminUsername: 'test_admin'
            };
            
            showResult('info', 'Testing Single Item (Quantity = 1)', JSON.stringify(testData, null, 2));
            
            const result = await sendTestItem(testData);
            
            if (result.success) {
                showResult('success', 'Single Item Test Passed', 
                    `Expected: 1 item\nActual: ${result.data.quantity} item(s)\nStatus: ${result.data.status}`);
            } else {
                showResult('error', 'Single Item Test Failed', JSON.stringify(result, null, 2));
            }
        }
        
        async function testMultipleItems() {
            const testData = {
                playerUsername: 'test_player_multiple',
                itemCode: '0000007B00000000',
                optionsCode: '0000000000000000',
                quantity: 5,
                duration: 31,
                sendMethod: 'mail',
                adminUsername: 'test_admin'
            };
            
            showResult('info', 'Testing Multiple Items (Quantity = 5)', JSON.stringify(testData, null, 2));
            
            const result = await sendTestItem(testData);
            
            if (result.success) {
                showResult('success', 'Multiple Items Test Passed', 
                    `Expected: 5 items\nActual: ${result.data.quantity} item(s)\nStatus: ${result.data.status}`);
            } else {
                showResult('error', 'Multiple Items Test Failed', JSON.stringify(result, null, 2));
            }
        }
        
        async function testLargeQuantity() {
            const testData = {
                playerUsername: 'test_player_large',
                itemCode: '0000007B00000000',
                optionsCode: '0000000000000000',
                quantity: 10,
                duration: 31,
                sendMethod: 'warehouse',
                adminUsername: 'test_admin'
            };
            
            showResult('info', 'Testing Large Quantity (Quantity = 10)', JSON.stringify(testData, null, 2));
            
            const result = await sendTestItem(testData);
            
            if (result.success) {
                showResult('success', 'Large Quantity Test Passed', 
                    `Expected: 10 items\nActual: ${result.data.quantity} item(s)\nStatus: ${result.data.status}`);
            } else {
                showResult('error', 'Large Quantity Test Failed', JSON.stringify(result, null, 2));
            }
        }
        
        async function testDifferentMethods() {
            const methods = [
                { method: 'inventory', name: 'Cash Inventory' },
                { method: 'mail', name: 'Mail System' },
                { method: 'warehouse', name: 'Event Inventory' }
            ];
            
            for (let i = 0; i < methods.length; i++) {
                const method = methods[i];
                const testData = {
                    playerUsername: `test_player_${method.method}`,
                    itemCode: '0000007B00000000',
                    optionsCode: '0000000000000000',
                    quantity: 3,
                    duration: 31,
                    sendMethod: method.method,
                    adminUsername: 'test_admin'
                };
                
                showResult('info', `Testing ${method.name} (Quantity = 3)`, JSON.stringify(testData, null, 2));
                
                const result = await sendTestItem(testData);
                
                if (result.success) {
                    showResult('success', `${method.name} Test Passed`, 
                        `Method: ${method.method}\nExpected: 3 items\nActual: ${result.data.quantity} item(s)\nStatus: ${result.data.status}`);
                } else {
                    showResult('error', `${method.name} Test Failed`, JSON.stringify(result, null, 2));
                }
                
                // Wait 1 second between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        async function runAllTests() {
            showResult('info', 'Running All Tests', 'Starting comprehensive quantity testing...');
            
            await testSingleItem();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testMultipleItems();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testLargeQuantity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDifferentMethods();
            
            showResult('success', 'All Tests Completed', 'Check individual test results above.');
        }
        
        // Auto-load message
        window.addEventListener('load', function() {
            showResult('info', 'Quantity Test System Ready', 'Ready to test item quantity functionality.');
        });
    </script>
</body>
</html>
