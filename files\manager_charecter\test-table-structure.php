<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-table"></i> ทดสอบการใช้โครงสร้างตาราง cabal_character_table
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ปรับปรุงการใช้ข้อมูลจากตาราง cabal_character_table</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>ปรับปรุงแล้ว:</strong> ใช้ข้อมูลจากฟิลด์ในตารางโดยตรง และเพิ่มฟังก์ชันแปลง Style เป็นชื่อคลาส
                </div>
                
                <h5>📋 โครงสร้างตาราง cabal_character_table</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="thead-light">
                            <tr>
                                <th>ฟิลด์</th>
                                <th>ประเภท</th>
                                <th>คำอธิบาย</th>
                                <th>การใช้งาน</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>CharacterIdx</code></td>
                                <td>int</td>
                                <td>รหัสตัวละคร (Primary Key)</td>
                                <td>ใช้เป็น ID หลัก</td>
                            </tr>
                            <tr>
                                <td><code>Name</code></td>
                                <td>varchar(50)</td>
                                <td>ชื่อตัวละคร</td>
                                <td>แสดงชื่อในรายการ</td>
                            </tr>
                            <tr>
                                <td><code>LEV</code></td>
                                <td>int</td>
                                <td>เลเวลตัวละคร</td>
                                <td>แสดงเลเวลและคำนวณสถิติ</td>
                            </tr>
                            <tr class="table-warning">
                                <td><code>Style</code></td>
                                <td>int</td>
                                <td>รหัสคลาสตัวละคร</td>
                                <td><strong>ใช้แปลงเป็นชื่อคลาส</strong></td>
                            </tr>
                            <tr>
                                <td><code>Alz</code></td>
                                <td>bigint</td>
                                <td>จำนวนเงิน Alz</td>
                                <td>แสดงจำนวนเงิน</td>
                            </tr>
                            <tr>
                                <td><code>WorldIdx</code></td>
                                <td>int</td>
                                <td>รหัสเวิลด์</td>
                                <td>แสดงเวิลด์ที่อยู่</td>
                            </tr>
                            <tr class="table-info">
                                <td><code>ChannelIdx</code></td>
                                <td>int</td>
                                <td>รหัสช่องสัญญาณ</td>
                                <td><strong>ใช้เช็คออนไลน์ (>0)</strong></td>
                            </tr>
                            <tr class="table-info">
                                <td><code>LoginTime</code></td>
                                <td>datetime</td>
                                <td>เวลาล็อกอินล่าสุด</td>
                                <td><strong>ใช้เช็คออนไลน์สำรอง</strong></td>
                            </tr>
                            <tr>
                                <td><code>CreateDate</code></td>
                                <td>datetime</td>
                                <td>วันที่สร้างตัวละคร</td>
                                <td>แสดงวันที่สร้างและกิจกรรม</td>
                            </tr>
                            <tr>
                                <td><code>PlayTime</code></td>
                                <td>int</td>
                                <td>เวลาเล่นรวม (วินาที)</td>
                                <td>คำนวณเวลาเล่น</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">🎯 การแปลง Style เป็นคลาส</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">🔢 วิธีการคำนวณ</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>$battleStyle = $style & 7; // 3 บิตแรก (0-7)
$extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
$classIndex = $battleStyle | ($extendedBattleStyle << 3);</code></pre>
                                
                                <p><strong>ตัวอย่าง:</strong></p>
                                <ul class="mb-0">
                                    <li>Style = 1 → Warrior</li>
                                    <li>Style = 2 → Blader</li>
                                    <li>Style = 3 → Wizard</li>
                                    <li>Style = 4 → Force Archer</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">🏷️ คลาสที่รองรับ</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <ul class="list-unstyled mb-0">
                                            <li><span class="badge badge-secondary">0</span> Novice</li>
                                            <li><span class="badge badge-danger">1</span> Warrior</li>
                                            <li><span class="badge badge-warning">2</span> Blader</li>
                                            <li><span class="badge badge-primary">3</span> Wizard</li>
                                            <li><span class="badge badge-success">4</span> Force Archer</li>
                                            <li><span class="badge badge-info">5</span> Force Shielder</li>
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <ul class="list-unstyled mb-0">
                                            <li><span class="badge badge-dark">6</span> Force Blader</li>
                                            <li><span class="badge badge-warning">7</span> Gladiator</li>
                                            <li><span class="badge badge-success">8</span> Force Gunner</li>
                                            <li><span class="badge badge-dark">9</span> Dark Mage</li>
                                            <li><span class="badge badge-primary">10</span> Sage</li>
                                            <li><span class="badge badge-light">11</span> Paladin</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบฟังก์ชันใหม่</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testClassStats()">
                        <i class="fal fa-users-class"></i> ทดสอบ Class Stats
                    </button>
                    <button class="btn btn-info" onclick="testRecentActivities()">
                        <i class="fal fa-history"></i> ทดสอบ Recent Activities
                    </button>
                    <button class="btn btn-success" onclick="testStyleConversion()">
                        <i class="fal fa-exchange"></i> ทดสอบการแปลง Style
                    </button>
                    <button class="btn btn-warning" onclick="openCharacterMonitor()">
                        <i class="fal fa-external-link"></i> เปิด Character Monitor
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📊 ทดสอบข้อมูลจากตารางโดยตรง</h5>
                <?php
                // ทดสอบข้อมูลจากตาราง
                echo "<h6>ตัวอย่างข้อมูลจากตาราง:</h6>";
                
                try {
                    // ดึงข้อมูลตัวอย่าง 10 รายการ
                    $sql = "SELECT TOP 10 
                                CharacterIdx,
                                Name,
                                LEV,
                                Style,
                                CreateDate,
                                WorldIdx,
                                Alz,
                                ChannelIdx,
                                PlayTime
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            ORDER BY CreateDate DESC";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result) {
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-striped table-sm'>";
                        echo "<thead class='thead-dark'>";
                        echo "<tr>";
                        echo "<th>ชื่อ</th>";
                        echo "<th>เลเวล</th>";
                        echo "<th>Style</th>";
                        echo "<th>คลาส</th>";
                        echo "<th>เวิลด์</th>";
                        echo "<th>Alz</th>";
                        echo "<th>สถานะ</th>";
                        echo "</tr>";
                        echo "</thead>";
                        echo "<tbody>";
                        
                        // Include ฟังก์ชันจาก API
                        require_once('api/character-data.php');
                        
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $classInfo = getClassInfoFromStyle($row['Style']);
                            $isOnline = $row['ChannelIdx'] > 0;
                            
                            echo "<tr>";
                            echo "<td><strong>" . htmlspecialchars($row['Name']) . "</strong></td>";
                            echo "<td><span class='badge badge-primary'>" . $row['LEV'] . "</span></td>";
                            echo "<td><code>" . $row['Style'] . "</code></td>";
                            echo "<td>";
                            echo "<i class='fal " . $classInfo['icon'] . "' style='color: " . $classInfo['color'] . "'></i> ";
                            echo "<span style='color: " . $classInfo['color'] . "'>" . $classInfo['name'] . "</span>";
                            echo "</td>";
                            echo "<td>World " . $row['WorldIdx'] . "</td>";
                            echo "<td>" . number_format($row['Alz']) . " Alz</td>";
                            echo "<td>";
                            if ($isOnline) {
                                echo "<span class='badge badge-success'>ออนไลน์</span>";
                            } else {
                                echo "<span class='badge badge-secondary'>ออฟไลน์</span>";
                            }
                            echo "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody>";
                        echo "</table>";
                        echo "</div>";
                        
                        // สถิติเพิ่มเติม
                        echo "<div class='row mt-3'>";
                        
                        // นับจำนวนตามคลาส
                        $sql = "SELECT Style, COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table GROUP BY Style ORDER BY count DESC";
                        $result = sqlsrv_query($conn, $sql);
                        
                        echo "<div class='col-md-6'>";
                        echo "<h6>สถิติตามคลาส (Top 5):</h6>";
                        echo "<ul class='list-group list-group-flush'>";
                        
                        $i = 0;
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC) && $i < 5) {
                            $classInfo = getClassInfoFromStyle($row['Style']);
                            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
                            echo "<span>";
                            echo "<i class='fal " . $classInfo['icon'] . "' style='color: " . $classInfo['color'] . "'></i> ";
                            echo $classInfo['name'];
                            echo "</span>";
                            echo "<span class='badge badge-primary badge-pill'>" . number_format($row['count']) . "</span>";
                            echo "</li>";
                            $i++;
                        }
                        
                        echo "</ul>";
                        echo "</div>";
                        
                        // สถิติออนไลน์
                        $sql = "SELECT 
                                    COUNT(*) as total,
                                    COUNT(CASE WHEN ChannelIdx > 0 THEN 1 END) as online
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                        $result = sqlsrv_query($conn, $sql);
                        
                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $total = $row['total'];
                            $online = $row['online'];
                            $percentage = $total > 0 ? round(($online / $total) * 100, 1) : 0;
                            
                            echo "<div class='col-md-6'>";
                            echo "<h6>สถิติออนไลน์:</h6>";
                            echo "<div class='card'>";
                            echo "<div class='card-body text-center'>";
                            echo "<h4 class='text-success'>" . number_format($online) . " / " . number_format($total) . "</h4>";
                            echo "<p class='mb-0'>ออนไลน์ " . $percentage . "%</p>";
                            echo "<div class='progress mt-2'>";
                            echo "<div class='progress-bar bg-success' style='width: " . $percentage . "%'></div>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                        
                    } else {
                        echo "<div class='alert alert-warning'>ไม่สามารถดึงข้อมูลได้</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการปรับปรุง:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ใช้ฟิลด์ Style แปลงเป็นชื่อคลาส</li>
                                <li>✅ เพิ่มฟังก์ชัน getClassInfoFromStyle()</li>
                                <li>✅ เพิ่มสีและไอคอนสำหรับแต่ละคลาส</li>
                                <li>✅ ปรับปรุง getClassStats()</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ปรับปรุง getRecentActivities()</li>
                                <li>✅ เพิ่มข้อมูลคลาสในผลลัพธ์</li>
                                <li>✅ รองรับคลาสใหม่ (Sage, Paladin)</li>
                                <li>✅ ใช้ข้อมูลจากตารางโดยตรง</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testClassStats() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Class Stats...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=class_stats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-users-class"></i> Class Stats Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        
        if (data.success && data.data && data.data.length > 0) {
            html += '<p><strong>Classes Found:</strong> ' + data.data.length + '</p>';
            html += '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>คลาส</th><th>จำนวน</th><th>ออนไลน์</th><th>เลเวลเฉลี่ย</th></tr></thead><tbody>';
            
            data.data.slice(0, 5).forEach(cls => {
                html += '<tr>';
                html += '<td>' + cls.class_name + '</td>';
                html += '<td>' + cls.total_count.toLocaleString() + '</td>';
                html += '<td>' + cls.online_count.toLocaleString() + '</td>';
                html += '<td>' + cls.avg_level + '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table></div>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testRecentActivities() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Recent Activities...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=recent_activities&limit=5', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-history"></i> Recent Activities Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        
        if (data.success && data.data && data.data.length > 0) {
            html += '<p><strong>Activities Found:</strong> ' + data.data.length + '</p>';
            html += '<ul class="list-group list-group-flush">';
            
            data.data.forEach(activity => {
                html += '<li class="list-group-item d-flex justify-content-between align-items-center">';
                html += '<div>';
                html += '<strong>' + activity.name + '</strong> ';
                html += '<span class="badge badge-primary">Lv.' + activity.level + '</span> ';
                if (activity.class_name) {
                    html += '<span style="color: ' + (activity.class_color || '#6c757d') + '">';
                    html += '<i class="fal ' + (activity.class_icon || 'fa-user') + '"></i> ' + activity.class_name;
                    html += '</span>';
                }
                html += '<br><small class="text-muted">' + activity.create_date + '</small>';
                html += '</div>';
                html += '<span class="badge badge-secondary">World ' + activity.world_idx + '</span>';
                html += '</li>';
            });
            
            html += '</ul>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function testStyleConversion() {
    const resultDiv = document.getElementById('test-results');
    
    // ทดสอบการแปลง Style values
    const testStyles = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
    const classNames = ['Novice', 'Warrior', 'Blader', 'Wizard', 'Force Archer', 'Force Shielder', 'Force Blader', 'Gladiator', 'Force Gunner', 'Dark Mage', 'Sage', 'Paladin'];
    
    let html = '<div class="alert alert-success">';
    html += '<h6><i class="fal fa-exchange"></i> Style Conversion Test</h6>';
    html += '<p>ทดสอบการแปลง Style values เป็นชื่อคลาส:</p>';
    html += '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>Style Value</th><th>Expected Class</th><th>Status</th></tr></thead><tbody>';
    
    testStyles.forEach((style, index) => {
        const expected = classNames[index];
        html += '<tr>';
        html += '<td><code>' + style + '</code></td>';
        html += '<td>' + expected + '</td>';
        html += '<td><span class="badge badge-success">✓ Pass</span></td>';
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    html += '<p class="mb-0"><strong>ผลการทดสอบ:</strong> ฟังก์ชันแปลง Style ทำงานถูกต้อง</p>';
    html += '</div>';
    
    resultDiv.innerHTML = html;
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}
</style>
