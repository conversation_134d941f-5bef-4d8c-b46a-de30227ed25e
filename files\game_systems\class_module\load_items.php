<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$filename = __DIR__ . "../import/itemlist.xml";

// Sanitize unescaped ampersands
$raw = file_get_contents($filename);
$sanitized = preg_replace('/&(?!(amp|lt|gt|quot|apos);)/', '&amp;', $raw);

libxml_use_internal_errors(true);
$xml = simplexml_load_string($sanitized);

if ($xml === false) {
    $errors = libxml_get_errors();
    libxml_clear_errors();
    http_response_code(500);
    echo json_encode(["error" => "Failed to parse XML", "details" => $errors]);
    exit;
}

$items = [];
foreach ($xml->msg as $msg) {
    $idStr = (string)$msg['id'];
    if (preg_match('/item(\\d+)/', $idStr, $match)) {
        $id = (int)$match[1];
        $name = (string)$msg['cont'];
        $items[$id] = $name;
    }
}

header('Content-Type: application/json');
echo json_encode($items);
