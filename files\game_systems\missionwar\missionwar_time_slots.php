<?php
// missionwar_time_slots.php - CRUD for WEB_MissionWar_time_slots
require_once '../../../_app/dbinfo.inc.php';
$conn = db_connect();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        $sql = 'SELECT * FROM WEB_MissionWar_time_slots ORDER BY SlotID ASC';
        $stmt = sqlsrv_query($conn, $sql);
        $result = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $result[] = $row;
        }
        echo json_encode($result);
        break;
    case 'get':
        $id = intval($_GET['SlotID'] ?? 0);
        $sql = 'SELECT * FROM WEB_MissionWar_time_slots WHERE SlotID=?';
        $stmt = sqlsrv_query($conn, $sql, [$id]);
        echo json_encode(sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC));
        break;
    case 'add':
        $data = json_decode(file_get_contents('php://input'), true);
        $sql = 'INSERT INTO WEB_MissionWar_time_slots (SlotID, StartTime, EndTime, TierMin, TierMax) VALUES (?, ?, ?, ?, ?)';
        $params = [
            $data['SlotID'], $data['StartTime'], $data['EndTime'], $data['TierMin'], $data['TierMax']
        ];
        $stmt = sqlsrv_query($conn, $sql, $params);
        echo json_encode(['success'=>!!$stmt, 'SlotID'=>$data['SlotID']]);
        break;
    case 'edit':
        $data = json_decode(file_get_contents('php://input'), true);
        $sql = 'UPDATE WEB_MissionWar_time_slots SET StartTime=?, EndTime=?, TierMin=?, TierMax=? WHERE SlotID=?';
        $params = [
            $data['StartTime'], $data['EndTime'], $data['TierMin'], $data['TierMax'], $data['SlotID']
        ];
        $stmt = sqlsrv_query($conn, $sql, $params);
        echo json_encode(['success'=>!!$stmt]);
        break;
    case 'delete':
        $id = intval($_GET['SlotID'] ?? 0);
        $sql = 'DELETE FROM WEB_MissionWar_time_slots WHERE SlotID=?';
        $stmt = sqlsrv_query($conn, $sql, [$id]);
        echo json_encode(['success'=>!!$stmt]);
        break;
    default:
        echo json_encode(['error'=>'Invalid action']);
}
