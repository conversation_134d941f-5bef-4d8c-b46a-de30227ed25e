<?php
// missionwar_rewards.php - CRUD for WEB_MissionWar_rewards
require_once '../../../_app/dbinfo.inc.php';
$conn = db_connect();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        $sql = 'SELECT * FROM WEB_MissionWar_rewards ORDER BY RewardID ASC';
        $stmt = sqlsrv_query($conn, $sql);
        $result = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $result[] = $row;
        }
        echo json_encode($result);
        break;
    case 'get':
        $id = intval($_GET['RewardID'] ?? 0);
        $sql = 'SELECT * FROM WEB_MissionWar_rewards WHERE RewardID=?';
        $stmt = sqlsrv_query($conn, $sql, [$id]);
        echo json_encode(sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC));
        break;
    case 'add':
        $data = json_decode(file_get_contents('php://input'), true);
        $sql = 'INSERT INTO WEB_MissionWar_rewards (SlotID, ItemKindIdx, ItemOption, ItemDurationIdx, Alz) VALUES (?, ?, ?, ?, ?)';
        $params = [
            $data['SlotID'], $data['ItemKindIdx'], $data['ItemOption'], $data['ItemDurationIdx'], $data['Alz']
        ];
        $stmt = sqlsrv_query($conn, $sql, $params);
        if ($stmt) {
            // Get last inserted RewardID
            $idResult = sqlsrv_query($conn, 'SELECT SCOPE_IDENTITY() AS RewardID');
            $row = sqlsrv_fetch_array($idResult, SQLSRV_FETCH_ASSOC);
            echo json_encode(['success'=>true, 'RewardID'=>$row['RewardID']]);
        } else {
            echo json_encode(['success'=>false, 'error'=>'Insert failed']);
        }
        break;
    case 'edit':
        $data = json_decode(file_get_contents('php://input'), true);
        $sql = 'UPDATE WEB_MissionWar_rewards SET SlotID=?, ItemKindIdx=?, ItemOption=?, ItemDurationIdx=?, Alz=? WHERE RewardID=?';
        $params = [
            $data['SlotID'], $data['ItemKindIdx'], $data['ItemOption'], $data['ItemDurationIdx'], $data['Alz'], $data['RewardID']
        ];
        $stmt = sqlsrv_query($conn, $sql, $params);
        echo json_encode(['success'=>!!$stmt]);
        break;
    case 'delete':
        $id = intval($_GET['RewardID'] ?? 0);
        $sql = 'DELETE FROM WEB_MissionWar_rewards WHERE RewardID=?';
        $stmt = sqlsrv_query($conn, $sql, [$id]);
        echo json_encode(['success'=>!!$stmt]);
        break;
    default:
        echo json_encode(['error'=>'Invalid action']);
}
