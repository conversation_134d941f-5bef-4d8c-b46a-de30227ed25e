<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Manage-Account && Charecter
        <small>
            ระบบ ตรวจสอบข้อมูลผู้เล่น
        </small>
    </h1>
</div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Player Online All <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active p-3" data-toggle="tab" href="#tab_default" role="tab">
                                <i class="fal fa-cog text-info"></i>
                                <span class="hidden-sm-down ml-1">ผู้เล่น ออนไลน์ ทั้งหมด</span>
                            </a>
                        </li>
                    </ul>


                    <div class="tab-content pt-2">
                        <div class="tab-pane fade show active" id="tab_default" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">
                                    <div class="panel-content">
                                    </div>
                                    <div class="panel-content">
                                        <table id="dt-iteminvent" class="table table-sm table-bordered w-100">
                                            <thead>
                                                <tr>
                                                    <th>UserNum</th>
                                                    <th>ID</th>
                                                    <th>Last IP</th>
                                                    <th>CharacterIdx</th>
                                                    <th>Name</th>
                                                    <th>อาชีพ</th>
                                                    <th>สถานะ</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
													$query = "
														SELECT ca.UserNum, cc.CharacterIdx
														FROM ACCOUNT.dbo.cabal_auth_table ca
														INNER JOIN SERVER01.dbo.cabal_character_table cc ON ca.UserNum = cc.CharacterIdx/16
														WHERE ca.AuthType = 2;
													";

													$result = sqlsrv_query($conn, $query);
													if ($result === false) {
														die(print_r(sqlsrv_errors(), true));
													}

													while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {

															$Unum = $userLogin->CheckUserAccount($row['UserNum'],'UserNum', $conn);
															$Uid = $userLogin->CheckUserAccount($row['UserNum'],'ID', $conn);
															$AType = $userLogin->CheckUserAccount($row['UserNum'],'AuthType', $conn);
															$Lip = $userLogin->CheckUserAccount($row['UserNum'],'LastIp', $conn);


															$Charidx = $userLogin->recCharecter($row['CharacterIdx'],'CharacterIdx', $conn);
															$CharName = $userLogin->recCharecter($row['CharacterIdx'],'Name', $conn);
                                                            $name = $userLogin->thaitrans($CharName);
															 $ClassType = $userLogin->recCharecter($row['CharacterIdx'],'ClassStyle', $conn);
															 if  ($ClassType== 1  ) { $ClassType = "WA"; }
															 else if ($ClassType== 2 ) { $ClassType = "BL"; }
															 else if ($ClassType== 3 ) { $ClassType = "WI"; }
															 else if ($ClassType== 4 ) { $ClassType = "FA"; }
															 else if ($ClassType== 5 ) { $ClassType = "FS"; }
															 else if ($ClassType== 6 ) { $ClassType = "FB"; }
															 else if ($ClassType== 7 ) { $ClassType = "GL"; }
															 else if ($ClassType== 8 ) { $ClassType = "FG"; }
															 else if ($ClassType== 9 ) { $ClassType = "DM"; }
															 else {  $ClassType = "NO"; }
										
													?>

                                                <tr>
                                                    <td><?php echo $Unum; ?></td>
                                                    <td><?php echo $Uid; ?></td>
                                                    <td><a href="https://checkip.thaiware.com/?ip=<?php echo $Lip?>"
                                                            target="_blank"><?php echo $Lip?></a></td>
                                                    <td><?php echo $Charidx; ?></td>
                                                    <td><?php echo $name; ?></td>
                                                    <td><span class="badge badge-success badge-pill">
                                                            <?php echo $ClassType; ?>
                                                        </span></td>
                                                    <td><span
                                                            class="
								                        <?php echo $label = ($AType == '1' ? ' badge badge-success badge-pill' : ($AType == '2' ? ' badge badge-danger badge-pill' : ($AType == '3' ? ' badge badge-warning badge-pill' : 'badge badge-secondary badge-pill')));?>">
                                                            <?php echo $status = ($AType == '1' ? 'ปรกติ' : ($AType == '2' ? 'โดนแบน' : ($AType == '3' ? 'GM': 'Unknow')));?></span>
                                                    </td>
                                                    <td><a href="?url=manager_account/manage-account-edit&id=<?php echo $Unum; ?>"
                                                            class="btn btn-outline-warning btn-sm btn-icon rounded-circle waves-effect waves-themed"><i
                                                                class="fal fa-info-circle"></i></a></td>
                                                    <?php }
													sqlsrv_free_stmt($result);
													sqlsrv_close($conn);
													?>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>