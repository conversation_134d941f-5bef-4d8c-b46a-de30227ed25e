<?php
// Ensure required classes are loaded and session is started.
// Assuming $user and $zpanel are instantiated objects from your framework.
$user->restrictionUser(true, $conn);
$zpanel->checkSession(true);

// Initialize variables for messages
$returnSuccess = null;
$returnWarning = null;
$returnError = null;

// Sanitize and validate the UserNum from GET parameter
$userNum = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

// Redirect if no valid UserNum is provided
if ($userNum === false || $userNum === null) {
    header('Location: ?url=manager_account/manage-account');
    exit;
}

// Handle ban/unban actions first
$banAction = filter_input(INPUT_GET, 'ban', FILTER_UNSAFE_RAW); // Changed from FILTER_SANITIZE_STRING

if ($banAction) {
    switch ($banAction) {
        case 'wait':
            echo '<p class="text-red text-bolder">' . (defined('T_CONFIRMBAN') ? T_CONFIRMBAN : 'Are you sure you want to ban this account?') . '</p>';
            echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=true" class="btn btn-danger">' .'Yes, Ban'. '</a>';
            break;

        case 'true':
            // Fetch UserNum from ID if AuthType update uses ID.
            // Original code used UserNum from selectAccFetch but updated AuthType with ID. Let's assume AuthType should be updated using UserNum.
            // If UserNum is truly unique for update, this fetch might be redundant if $userNum from GET is reliable.
            // For safety, re-fetch the user data based on UserNum.
            $selectAccSql = "SELECT UserNum FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = ?";
            $selectAccParams = array($userNum);
            $selectAccQuery = sqlsrv_query($conn, $selectAccSql, $selectAccParams);

            if ($selectAccQuery && sqlsrv_has_rows($selectAccQuery)) {
                $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                $targetUserNum = $selectAccFetch['UserNum'];

                $banPlayerSql = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET AuthType = ? WHERE UserNum = ?";
                $banPlayerParams = array(2, $targetUserNum); // 2 for banned
                $banPlayerQuery = sqlsrv_query($conn, $banPlayerSql, $banPlayerParams);

                if ($banPlayerQuery && sqlsrv_rows_affected($banPlayerQuery)) {
                    $zpanel->generateWebLog($conn, '1', $targetUserNum, 'player banned', 'player banned by admin');
                    $returnSuccess = (defined('S_ACCOUNT_BANNED') ? S_ACCOUNT_BANNED : 'Account has been successfully banned.');
                    echo '<a href="?url=manager_account/manage-account" class="btn btn-default" style="margin: 5px 0;">Return to players page</a>';
                } else {
                    $returnWarning = (defined('W_ACCOUNT_BANNED') ? W_ACCOUNT_BANNED : 'Failed to ban account or account already banned.');
                }
            } else {
                $returnError = (defined('E_USER_NOT_FOUND') ? E_USER_NOT_FOUND : 'User not found for banning.');
            }
            break;

        case 'unban-wait':
            echo '<p class="text-red text-bolder">' . (defined('T_CONFIRMUNBAN') ? T_CONFIRMUNBAN : 'Are you sure you want to unban this account?') . '</p>';
            echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=unban" class="btn btn-danger">' . (defined('B_YESUNBAN') ? B_YESUNBAN : 'Yes, Unban') . '</a>';
            break;

        case 'unban':
            $selectAccSql = "SELECT UserNum FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = ?";
            $selectAccParams = array($userNum);
            $selectAccQuery = sqlsrv_query($conn, $selectAccSql, $selectAccParams);

            if ($selectAccQuery && sqlsrv_has_rows($selectAccQuery)) {
                $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                $targetUserNum = $selectAccFetch['UserNum'];

                $unbanPlayerSql = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET AuthType = ? WHERE UserNum = ?";
                $unbanPlayerParams = array(1, $targetUserNum); // 1 for unbanned/normal
                $unbanPlayerQuery = sqlsrv_query($conn, $unbanPlayerSql, $unbanPlayerParams);

                if ($unbanPlayerQuery && sqlsrv_rows_affected($unbanPlayerQuery)) {
                    $zpanel->generateWebLog($conn, '1', $targetUserNum, 'player unbanned', 'player unbanned by admin');
                    $returnSuccess = (defined('S_ACCOUNT_UNBANNED') ? S_ACCOUNT_UNBANNED : 'Account has been successfully unbanned.');
                    echo '<a href="?url=manager_account/manage-account" class="btn btn-default" style="margin: 5px 0;">Return to players page</a>';
                } else {
                    $returnWarning = (defined('W_ACCOUNT_UNBANNED') ? W_ACCOUNT_UNBANNED : 'Failed to unban account or account already unbanned.');
                }
            } else {
                $returnError = (defined('E_USER_NOT_FOUND') ? E_USER_NOT_FOUND : 'User not found for unbanning.');
            }
            break;

        default:
            $returnError = (defined('E_INVALIDVALUE') ? E_INVALIDVALUE : 'Invalid action specified.');
            break;
    }
}else{

// Fetch and display player data if not a ban/unban confirmation/execution
$resPlayer = null;
$selectCashDataFetch = null;
$selectGemDataFetch = null; // Assuming GemData is distinct from CashData or points
$selectTpointDataFetch = null;
$selectAuthDataFetch = null;
$selectWebAuthDataFetch = null;

if (!isset($banAction) || ($banAction !== 'wait' && $banAction !== 'unban-wait' && $banAction !== 'true' && $banAction !== 'unban')) {

// get data info
$selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$userNum'";
$selectPlayerDataParam = array();
$selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
    
if ($selectPlayerDataQuery && sqlsrv_has_rows($selectPlayerDataQuery)) {
    $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
    $userId = $resPlayer['UserNum'] ?? 0;


            // get cash account
        $selectCashData = "SELECT * FROM " . DATABASE_CCA . ".dbo.CashAccount WHERE UserNum = '$resPlayer[UserNum]'";
        $selectCashDataParam = array();
        $selectCashDataQuery = sqlsrv_query($conn, $selectCashData, $selectCashDataParam);
        $selectCashDataFetch = sqlsrv_fetch_array($selectCashDataQuery, SQLSRV_FETCH_ASSOC);

        $selectGemData = "SELECT * FROM " . DATABASE_CCA . ".dbo.CashAccount WHERE UserNum = '$resPlayer[UserNum]'";
        $selectGemDataParam = array();
        $selectGemDataQuery = sqlsrv_query($conn, $selectGemData, $selectGemDataParam);
        $selectGemDataFetch = sqlsrv_fetch_array($selectGemDataQuery, SQLSRV_FETCH_ASSOC);

        $selectTpointData = "SELECT * FROM " . DATABASE_NBL . ".dbo.Point WHERE UserNum = '$resPlayer[UserNum]'";
        $selectTpointDataParam = array();
        $selectTpointDataQuery = sqlsrv_query($conn, $selectTpointData, $selectTpointDataParam);
        $selectTpointDataFetch = sqlsrv_fetch_array($selectTpointDataQuery, SQLSRV_FETCH_ASSOC);
        
        $selectAuthData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_charge_auth WHERE UserNum = '$resPlayer[UserNum]'";
        $selectAuthDataParam = array();
        $selectAuthDataQuery = sqlsrv_query($conn, $selectAuthData, $selectAuthDataParam);
        $selectAuthDataFetch = sqlsrv_fetch_array($selectAuthDataQuery, SQLSRV_FETCH_ASSOC);
        
        $seleteWebAuthData = "SELECT * FROM ".DATABASE_WEB.".dbo.WEB_User_Info WHERE UserNum = '$resPlayer[UserNum]'";
        $selectWebAuthDataParam = array();
        $selectWebAuthDataQuery = sqlsrv_query($conn, $seleteWebAuthData, $selectWebAuthDataParam);
        $selectWebAuthDataFetch = sqlsrv_fetch_array($selectWebAuthDataQuery, SQLSRV_FETCH_ASSOC);



        $ExpireDate = date('Y-m-d H:i:s', strtotime($selectAuthDataFetch["ExpireDate"]));
        $today = date("Y-m-d H:i:s");    
    
        } else {
            $returnWarning = W_PLAYER_NOT_FOUND;
        }
}    
?>
<?php if (isset($returnSuccess)) { ?>
<div class="alert alert-success">
    <?php echo $returnSuccess; ?>
</div>
<?php } elseif (isset($returnWarning)) { ?>
<div class="alert alert-warning">
    <?php echo $returnWarning; ?>
</div>
<?php } elseif (isset($returnError)) { ?>
<div class="alert alert-danger">
    <?php echo $returnError; ?>
</div>
<?php } ?>
<style>
/* Add this to your custom CSS file (e.g., style.css) */
.profile-image {
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.3);
    /* Optional: Adds a subtle outer glow */
}

.status-indicator {
    /* Adjust positioning as needed */
    position: absolute;
    bottom: 8px;
    /* Distance from bottom */
    right: 8px;
    /* Distance from right */
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    /* White border for contrast */
}
</style>
<div class="subheader">
    <h1 class="subheader-title">
        <i class='subheader-icon fal fa-edit'>
        </i> Account Manager
        <small>
            จัดการไอดี
        </small>
    </h1>
</div>
<div class="row">
    <div class="col-lg-12 col-xl-5 order-lg-1 order-xl-1">
        <!-- profile summary -->
        <div class="card mb-g rounded-top">
            <div class="row no-gutters row-grid">
                <div class="col-lg-12">
                    <!-- profile summary -->
                    <div class="card mb-g rounded-top">
                        <div class="row no-gutters row-grid">

                            <div class="col-12">
                                <div class="card shadow-lg p-0 rounded-lg overflow-hidden">
                                    <div class="card-body p-4 text-center text-white">
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="position-relative mb-3">
                                                <img class="profile-image rounded-circle border border-primary border-3"
                                                    src="<?php echo htmlspecialchars($selectWebAuthDataFetch['picture'] ?? 'assets/img/default-avatar.png'); ?>"
                                                    alt="User Profile"
                                                    style="width: 120px; height: 120px; object-fit: cover;">
                                                <span
                                                    class="status-indicator
                                                    <?php echo ($resPlayer && $resPlayer['Login'] == "0") ? "bg-secondary" : "bg-success"; ?> position-absolute bottom-0 end-0 rounded-circle border border-white border-2"
                                                    style="width: 20px; height: 20px; right: 8px; bottom: 8px;"></span>
                                            </div>

                                            <h4 class="mb-1 text-white">
                                                <span
                                                    class="badge badge-pill
                                                        <?php echo ($resPlayer && $resPlayer['IsDeveloper'] ? 'badge-warning' : 'badge-info'); ?> mr-2">
                                                    <?php echo ($resPlayer && $resPlayer['IsDeveloper'] ? 'Developer' : 'Player'); ?>
                                                </span>
                                                <small class="text-muted ml-2 font-weight-bold">ID:
                                                    <?php echo htmlspecialchars($resPlayer['ID'] ?? 'N/A'); ?>
                                                    (UserNum:
                                                    <?php echo htmlspecialchars($resPlayer['UserNum'] ?? 'N/A'); ?>)</small>
                                            </h4>
                                            <p class="mb-3">
                                                <?php if ($resPlayer && $resPlayer['AuthType'] == "2"): ?>
                                                <span class="badge badge-danger badge-pill py-2 px-3">สถานะ
                                                    Banned <i class="fas fa-ban ml-1"></i></span>
                                                <?php else: ?>
                                                <span class="badge badge-success badge-pill py-2 px-3">สถานะ
                                                    Normal <i class="fas fa-check-circle ml-1"></i></span>
                                                <?php endif; ?>
                                            </p>
                                            <div class="mt-2">
                                                <?php
                                                        $expireDate = ($selectAuthDataFetch && $selectAuthDataFetch['ExpireDate'] instanceof DateTime) ? $selectAuthDataFetch['ExpireDate'] : new DateTime('1970-01-01');
                                                        $today = new DateTime();

                                                        if ($today > $expireDate):
                                                            echo '<span class="badge badge-warning badge-pill p-2"><i class="fas fa-exclamation-triangle mr-1"></i> ยังไม่ได้รับการยืนยัน ACTIVE</span>';
                                                        elseif ($today <= $expireDate):
                                                            echo '<span class="badge badge-info badge-pill p-2"><i class="fas fa-award mr-1"></i> ได้รับการยืนยัน ACTIVE แล้ว</span>';
                                                        else:
                                                            echo '<div class="spinner-grow spinner-grow-sm text-danger" role="status"> <span class="sr-only">Loading...</span> </div>';
                                                        endif;
                                                        ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="p-3 text-center">
                                    <div class="demo">
                                        <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
                                        <button type="button" data-toggle="modal"
                                            data-target=".modal-transparent-password"
                                            class="btn btn-sm btn-danger waves-effect waves-themed">เปลียนพาสเวิส
                                        </button>
                                        <button type="button" data-toggle="modal"
                                            data-target=".modal-transparent-subpassword"
                                            class="btn btn-sm btn-warning waves-effect waves-themed">เปลียนชัพพาส
                                        </button>
                                        <button type="button" data-toggle="modal" data-target=".modal-cash-transparent"
                                            class="btn btn-sm btn-outline-danger waves-effect waves-themed">Cash
                                        </button>
                                        <?php } ?>
                                        <button type="button" data-toggle="modal" data-target="#bannedModel"
                                            class="btn btn-sm btn-dark waves-effect waves-themed">Bannned
                                        </button>
                                    </div>
                                </div>
                            </div>


                            <div class="col-lg-12 col-md-10 col-sm-12">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-primary text-white d-flex align-items-center">
                                        <h2 class="h5 mb-0"><i class="fas fa-id-card mr-2"></i>ข้อมูลไอดี <span
                                                class="badge badge-light ml-2">Player Information</span></h2>
                                    </div>
                                    <div class="card-body">
                                        <?php if (isset($returnSuccess)): ?>
                                        <div class="alert alert-success" role="alert">
                                            <?php echo $returnSuccess; ?>
                                            <?php if (isset($result['return_link'])) echo $result['return_link']; ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (isset($returnWarning)): ?>
                                        <div class="alert alert-warning" role="alert">
                                            <?php echo $returnWarning; ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (isset($returnError)): ?>
                                        <div class="alert alert-danger" role="alert">
                                            <?php echo $returnError; ?>
                                        </div>
                                        <?php endif; ?>

                                        <?php
                                                    // This section assumes $resPlayer and other data fetches are successful
                                                    // as handled in the PHP logic you provided and refactored previously.
                                                    if (isset($resPlayer) && $resPlayer) {
                                                    ?>
                                        <div class="row mb-3">
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-user-alt mr-1"></i>
                                                        UserNum</span>
                                                    <span><?php echo $resPlayer['UserNum']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-calendar-alt mr-1"></i>
                                                        ID</span>
                                                    <span><?php echo $resPlayer['ID']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-calendar-alt mr-1"></i>
                                                        createDate</span>
                                                    <span><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resPlayer['createDate'])); ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-sign-out-alt mr-1"></i>
                                                        LogoutTime</span>
                                                    <span><?php echo !empty($row['LogoutTime']) ? date('d/m/Y เวลา H:i', strtotime($row['LogoutTime'])) : 'Null';  ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-sign-in-alt mr-1"></i>
                                                        LoginTime</span>
                                                    <span><?php echo !empty($row['LoginTime']) ? date('d/m/Y เวลา H:i', strtotime($row['LoginTime'])) : 'Null'; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-clock mr-1"></i> PlayTime</span>
                                                    <span><?php echo round($resPlayer['PlayTime'] / 60); ?>
                                                        ชม.</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-user-check mr-1"></i> LoginEx</span>
                                                    <span><?php echo $resPlayer['LoginEx']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-network-wired mr-1"></i> LastIp</span>
                                                    <span><?php echo $resPlayer['LastIp']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-envelope mr-1"></i> Email</span>
                                                    <span><?php echo $resPlayer['Email']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-map-marker-alt mr-1"></i> IP</span>
                                                    <span><?php echo $resPlayer['NewIp']; ?></span>
                                                </div>
                                            </div>
                                            <?php if (isset($selectWebAuthDataFetch)): ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-fingerprint mr-1"></i> auth_uid</span>
                                                    <span><?php echo $selectWebAuthDataFetch['auth_uid'] ?? 'N/A'; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fa fa-user mr-1"></i> First_name</span>
                                                    <span><?php echo $selectWebAuthDataFetch['name'] ?? 'N/A'; ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-chart-line mr-1"></i>
                                                        LoginCounter</span>
                                                    <span><?php echo $resPlayer['LoginCounter']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-phone mr-1"></i> Phone</span>
                                                    <span><?php echo $resPlayer['Phone']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-lock mr-1"></i> Secret_Code</span>
                                                    <span><?php echo $resPlayer['UserValidation']; ?></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <div class="d-flex align-items-center p-2 border rounded bg-light">
                                                    <span class="badge badge-primary mr-3 p-2 width-120"><i
                                                            class="fas fa-code mr-1"></i> IsDeveloper</span>
                                                    <span>
                                                        <?php echo $resPlayer['IsDeveloper']; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <?php
                                                    // Display ban/unban confirmation links if applicable
                                                    if (isset($result['message']) && isset($result['action_link'])) {
                                                        echo '<div class="text-center mt-4">';
                                                        echo $result['message'];
                                                        echo $result['action_link'];
                                                        echo '</div>';
                                                    }
                                                ?>

                                        <hr class="my-4">

                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="?url=manager_account/manage-account" class="btn btn-secondary"><i
                                                    class="fas fa-arrow-left mr-1"></i>
                                                กลับหน้ารายชื่อผู้เล่น</a>
                                            <div>
                                                <?php
                                                            if (isset($resPlayer['AuthType'])) {
                                                                if ($resPlayer['AuthType'] == 1) { // Assuming '1' means active/unbanned
                                                                    echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=wait" class="btn btn-danger mr-2"><i class="fas fa-ban mr-1"></i> แบนผู้เล่น</a>';
                                                                } elseif ($resPlayer['AuthType'] == 2) { // Assuming '2' means banned
                                                                    echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=unban-wait" class="btn btn-success mr-2"><i class="fas fa-check-circle mr-1"></i> เลิกแบนผู้เล่น</a>';
                                                                }
                                                            }
                                                            ?>
                                                <button class="btn btn-info disabled"><i class="fas fa-edit mr-1"></i>
                                                    แก้ไขข้อมูล (Under
                                                    Dev)</button>
                                            </div>
                                        </div>

                                        <?php } else { ?>
                                        <div class="alert alert-info text-center" role="alert">
                                            ไม่พบข้อมูลผู้เล่นสำหรับ UserNum นี้
                                        </div>
                                        <div class="text-center">
                                            <a href="?url=manager_account/manage-account" class="btn btn-secondary"><i
                                                    class="fas fa-arrow-left mr-1"></i>
                                                กลับหน้ารายชื่อผู้เล่น</a>
                                        </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- photos -->
                </div>

            </div>
        </div>
        <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
            <?php include "view-warehouse.php"; ?>                 
        <?php } ?>

    </div>


    <?php                     
$CharrecIdx = [];
$sql = "SELECT * FROM " . DATABASE_SV . ".dbo.cabal_character_table WHERE CharacterIdx/16 = '$userNum'";
$stmt = sqlsrv_query($conn, $sql);
while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $CharrecIdx[] = [
            'CharacterIdx' => $row['CharacterIdx'],
            'Name' => $row['Name'],
            'LEV' => $row['LEV'],
            'Alz' => $row['Alz'],
            'Nation' => $row['Nation'],
            'Style' => $row['Style'],
            'Login' => $row['Login']
        ];
}
if (count($CharrecIdx)): 
?>
    <div class="col-lg-12 col-xl-4 order-lg-3 order-xl-2">
        <?php foreach ($CharrecIdx as $CharInfo):
    $classInfo = $userLogin->cabalstyle($CharInfo['Style']);
    // Fetch Soul Ability data
    $selectabilitySql = "SELECT * FROM " . DATABASE_SV . ".dbo.cabal_soul_ability_table WHERE CharacterIdx = ?";
    $selectabilityQuery = sqlsrv_query($conn, $selectabilitySql, array($CharInfo['CharacterIdx']));
    $selectAPresChars = sqlsrv_fetch_array($selectabilityQuery, SQLSRV_FETCH_ASSOC);

    // Fetch War Exp data
    $selectWexpSql = "SELECT * FROM " . DATABASE_SV . ".dbo.cabal_WarExp_Table WHERE CharacterIdx = ?";
    $selectWexpQuery = sqlsrv_query($conn, $selectWexpSql, array($CharInfo['CharacterIdx']));
    $selectWexpresChars = sqlsrv_fetch_array($selectWexpQuery, SQLSRV_FETCH_ASSOC);
?>
        <!-- post comment -->
        <div class="card mb-g">
            <div class="card h-100 shadow-sm border-0 rounded-lg overflow-hidden">
                <div class="card-header bg-dark text-white py-3 d-flex align-items-center justify-content-between">
                    <h5 class="mb-0 text-white"><i class="fas fa-user-circle mr-2"></i>
                        <?php echo $userLogin->thaitrans($CharInfo['Name']); ?></h5>
                    <span class="badge badge-pill
                                                <?php echo ($CharInfo['Login'] == "0") ? "badge-secondary" : "badge-success"; ?>
                                                ml-auto px-3 py-2">
                        <?php echo ($CharInfo['Login'] == "0") ? 'Offline' : 'Online'; ?>
                        <i
                            class="fas fa-circle ml-1 <?php echo ($CharInfo['Login'] == "0") ? 'text-secondary' : 'text-success'; ?>"></i>
                    </span>
                </div>
                <div class="card-body p-3 d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0 mr-3">
                            <img src="assets/images/cabal/class/<?php echo htmlspecialchars($classInfo['Class_Name']) ?? 'default_class'; ?>.png"
                                alt="<?php echo htmlspecialchars($classInfo['Class_Name'] ?? 'Unknown Class'); ?>"
                                class="border border-secondary rounded-circle width-10 height-10 d-inline-block bg-faded"
                                style="width: 80px; height: 80px; object-fit: cover;">
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0 text-primary">Class: <span
                                    class="text-dark"><?php echo htmlspecialchars($classInfo['Class_Name'] ?? 'N/A'); ?></span>
                            </h6>
                            <h6 class="mb-0 text-primary">Level: <span
                                    class="text-dark"><?php echo htmlspecialchars($CharInfo['LEV'] ?? 'N/A'); ?></span>
                            </h6>
                            <h6 class="mb-0 text-primary">Nation: <span
                                    class="text-dark"><?php echo htmlspecialchars($userLogin->nation($CharInfo['Nation']) ?? 'N/A'); ?></span>
                            </h6>
                        </div>

                    </div>
                    <hr class="my-2">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="p-2 border rounded bg-light">
                                <small class="text-muted d-block">Experience</small>
                                <strong class="text-info"><?php echo number_format($CharInfo['Exp'] ?? 0); ?></strong>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-2 border rounded bg-light">
                                <small class="text-muted d-block">Alz</small>
                                <strong class="text-info"><?php echo number_format($CharInfo['Alz'] ?? 0); ?></strong>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <div class="p-2 border rounded bg-light">
                                <small class="text-muted d-block">Ability Points</small>
                                <strong
                                    class="text-warning"><?php echo number_format($selectAPresChars['AbilityPoint'] ?? 0); ?></strong>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="p-2 border rounded bg-light">
                                <small class="text-muted d-block">War EXP</small>
                                <strong
                                    class="text-danger"><?php echo number_format($selectWexpresChars['WarExp'] ?? 0); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light border-top d-flex justify-content-center">
                    <a href="?url=manager_charecter/view&char_id=<?php echo htmlspecialchars($CharInfo['CharacterIdx']); ?>"
                        class="btn btn-primary btn-sm waves-effect waves-themed">
                        <i class="fas fa-info-circle mr-1"></i> ดูรายละเอียด
                    </a>
                </div>
            </div>
        </div>
        <!-- post comment -->
        <?php endforeach; ?>
    </div>
    <?php else: ?>
    <div class="alert alert-danger mt-3">
        ❌ ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้ระบุ <code>?usernum=</code>
    </div>
    <?php endif; ?>


    <div class="col-lg-6 col-xl-3 order-lg-2 order-xl-3">
        <!-- add : -->
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                <?php
                $Cash = $selectTpointDataFetch['Cash'] ?? null;
                echo is_numeric($Cash) ? number_format($Cash) : 'Null';
                ?>
                <small class="m-0 l-h-n">Cash
                </small>
            </h3>
            <i class="fal fa-coins position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n1"
                style="font-size:6rem">
            </i>
        </div>

        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                <?php
                $CashBonus = $selectTpointDataFetch['CashBonus'] ?? null;
                echo is_numeric($CashBonus) ? number_format($CashBonus) : 'Null';
                ?>
                <small class="m-0 l-h-n">CashBonus
                </small>
            </h3>
            <i class="fal fa-gem position-absolute pos-right pos-bottom opacity-15  mb-n1 mr-n4"
                style="font-size: 6rem;">
            </i>
        </div>

        <div class="p-3 bg-success-200 rounded overflow-hidden position-relative text-white mb-g">
            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                <?php
                $Rpoint = $selectTpointDataFetch['Rpoint'] ?? null;
                echo is_numeric($Rpoint) ? number_format($Rpoint) : 'Null';
                ?>
                <small class="m-0 l-h-n">Rpoint
                </small>
            </h3>
            <i class="fal fa-lightbulb position-absolute pos-right pos-bottom opacity-15 mb-n5 mr-n6"
                style="font-size: 8rem;">
            </i>
        </div>

        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                <?php
                    $tpoint = $selectTpointDataFetch['TPoint'] ?? null;
                    echo is_numeric($tpoint) ? number_format($tpoint) : 'Null';
                    ?>
                <small class="m-0 l-h-n">Tpoint
                </small>
            </h3>
            <i class="fal fa-globe position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n4"
                style="font-size: 6rem;">
            </i>
        </div>

    </div>
</div>
<?php } ?>

  <!-- Modal แอดแคส -->
                    <div class="modal fade modal-cash-transparent" id="example-modal-backdrop-transparent" tabindex="-1"
                        role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Edit Cash</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                                    <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                // get data info
                                $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                $selectPlayerDataParam = array();
                                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                if (isset($_POST['btn-savechange-cash']))
                                {
                                    // variables
                                    $input_cash = strip_tags(trim($_POST['plr-cash']));
                                    $input_cashbonus = strip_tags(trim($_POST['plr-cashbonus']));
                                    $input_Rpoint= strip_tags(trim($_POST['plr-Rpoint']));
                                    $input_tpoint= strip_tags(trim($_POST['plr-Tpoint']));
                                    // condition
                                    if (empty($input_cash)){
                                        $input_cash = 0;
                                    }
                                    if (empty($input_cashbonus)){
                                        $input_cashbonus = 0;
                                    }
                                    if (empty($input_Rpoint)){
                                        $input_Rpoint = 0;
                                    }
                                    if (empty($input_tpoint)){
                                        $input_tpoint = 0;
                                    }
                                   
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        // update
                                        $NewPwd = $cat_password;
                                        //$NewPwd = substr(md5(mktime()),0,8);
                                        //$NewPwd = PWDENCRYPT('$NewPwd');
                                        $updatePlayer = "UPDATE " . DATABASE_CCA . ".dbo.CashAccount SET Cash = Cash+'$input_cash', CashBonus = CashBonus+'$input_cashbonus', Rpoint = Rpoint+'$input_Rpoint' WHERE UserNum = '$PlayerUserNum'";
                                        $updatePlayerParam = array();
                                        $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer, $updatePlayerParam);
                                        if (sqlsrv_rows_affected($updatePlayerQuery)){

                                            $updateTpoint = "UPDATE " . DATABASE_NBL . ".dbo.Point SET TPoint = TPoint+'$input_tpoint' WHERE UserNum = '$PlayerUserNum'";
                                            $updateTpointParam = array();
                                            $updateTpointQuery = sqlsrv_query($conn, $updateTpoint, $updateTpointParam);
                                                if (sqlsrv_rows_affected($updatePlayerQuery)){
                                                // generate a log
                                                $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'Update all Point player account', "playerId: { {$UserID} } Cash={ {$input_cash} } CashBonus={ {$input_cash} } Rpoint={ {$input_Rpoint} } Tpoint={ {$input_tpoint} }updated!");
                                                  //header('Location: ?url=game_manager/manage-account&update=true');
                                                echo '<script type="text/javascript">';
                                                echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","Player Id: {'.$UserID.'}  Cash={'.$input_cash.'} CashBonus={'.$input_cash.'} Rpoint={'.$input_Rpoint.'} CashAccount account was successfully updated!","success");';
                                                echo '});</script>';

                                                } else {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC . '","error");';
                                            echo '});</script>';
                                        }


                                        }else{
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC . '","error");';
                                            echo '});</script>';
                                        }
                                }
                                
                        
                            ?>
                                    <div class="modal-body">
                                        <div class="form-row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label" for="cash">
                                                    Cash
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <div class='icon-stack'>
                                                                <i
                                                                    class="fal fa-coins icon-stack-3x opacity-100 color-white"></i>
                                                            </div>
                                                        </span>
                                                    </div>
                                                    <input type="text" name="plr-cash" id="basic-addon1" value="0"
                                                        class="form-control" placeholder="cash" aria-label="cash"
                                                        aria-describedby="basic-addon1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label" for="cashbonus">
                                                    CashBonus
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <div class='icon-stack'>
                                                                <i
                                                                    class="fal fa-coins icon-stack-3x opacity-100 color-white"></i>
                                                            </div>
                                                        </span>
                                                    </div>
                                                    <input type="text" name="plr-cashbonus" id="basic-addon1" value="0"
                                                        class="form-control" placeholder="cashbonus"
                                                        aria-label="cashbonus" aria-describedby="basic-addon1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label" for="Rpoint">
                                                    Rpoint Cash
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <div class='icon-stack'>
                                                                <i
                                                                    class="fal fa-coins icon-stack-3x opacity-100 color-white"></i>
                                                            </div>
                                                        </span>
                                                    </div>
                                                    <input type="text" name="plr-Rpoint" id="basic-addon1" value="0"
                                                        class="form-control" placeholder="Rpoint" aria-label="Rpoint"
                                                        aria-describedby="basic-addon1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label" for="Rpoint">
                                                    Rpoint Tpoint
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <div class='icon-stack'>
                                                                <i
                                                                    class="fal fa-coins icon-stack-3x opacity-100 color-white"></i>
                                                            </div>
                                                        </span>
                                                    </div>
                                                    <input type="text" name="plr-Tpoint" id="basic-addon1" value="0"
                                                        class="form-control" placeholder="Tpoint" aria-label="Tpoint"
                                                        aria-describedby="basic-addon1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary"
                                            data-dismiss="modal">Close</button>
                                        <button type="submit" name="btn-savechange-cash" class="btn btn-primary">Save
                                            changes</button>
                                    </div>
                            </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal เปลียนพาสเวิส id-->
        <div class="modal fade modal-transparent-password" tabindex="-1" role="dialog" style="display: none;"
            aria-hidden="true">
            <div class="modal-dialog modal-transparent" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title text-white">
                            Chang New Password
                            <small class="m-0 text-white opacity-70">
                                เปลียนพาสเวิส
                            </small>
                        </h4>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">
                                <i class="fal fa-times">
                                </i>
                            </span>
                        </button>
                    </div>
                    <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                if (isset($_POST['btn-savechangePassword'])){
                                    // variables
                                    $input_mail = strip_tags(trim($_POST['plr-email']));
                                    $input_password = strip_tags(trim($_POST['plr-pass']));
                                    $plr_isdev = strip_tags(trim($_POST['plr-isdev']));
                                    // condition
                                    if (empty($input_mail))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_EMPTY_EMAIL . '","error");';
                                        echo '});</script>';
                                    }
                                    else if (empty($input_password))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_R_PASS_EMPTY . '","error");';
                                        echo '});</script>';
                                    }
                                    else if (!filter_var($input_mail, FILTER_VALIDATE_EMAIL))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_WRONG_EMAIL . '","error");';
                                        echo '});</script>';
                                    } else {
                                                                        // get data info
                                        $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                        $selectPlayerDataParam = array();
                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                        $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        $finalPass = $resPlayer['Password'];
                                        $NewPwd = $input_password;
                                        //$NewPwd = substr(md5(mktime()),0,8);
                                        if ($plr_isdev == '-1'){
                                            $plr_isdev = $resPlayer['IsDeveloper'];
                                        }
                                        //$NewPwd = PWDENCRYPT('$NewPwd');
                                        $updatePlayer = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET Email = '$input_mail', Password = PWDENCRYPT('$NewPwd'), IsDeveloper = '$plr_isdev' WHERE UserNum = '$PlayerUserNum'";
                                        $updatePlayerParam = array();
                                        $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer, $updatePlayerParam);
                                        if (sqlsrv_rows_affected($updatePlayerQuery)){
                                            // generate a log
                                            $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'update password player account', "playerId: { {$UserID} } password account was successfully updated!");
                                            if ($plr_isdev != '-1'){
                                                // update isDev UsersData table
                                                $updatePlayerData = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET IsDeveloper = '$plr_isdev' WHERE UserNum = '$PlayerUserNum'";
                                                $updatePlayerDataParam = array();
                                                $updatePlayerDataQuery = sqlsrv_query($conn, $updatePlayerData, $updatePlayerDataParam);
                                            }
                                            //header('Location: ?url=game_manager/manage-account&update=true');
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","' . S_PLR_ACCOUNT_UPDATED . '","success");';
                                            echo '});</script>';
                                        }
                                        else
                                        {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
                                            echo '});</script>';
                                        }
                                    }
                                }
                            ?>
                    <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                        <div class="modal-body">
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="Username">
                                        <?php echo T_ACCOUNTINFO; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="Username">
                                                <i class="ni ni-user fs-xl">
                                                </i>
                                            </span>
                                        </div>
                                        <input type="text" name="username" id="basic-addon1"
                                            value="<?php echo $resPlayer['ID']; ?>" class="form-control"
                                            placeholder="Username" aria-label="Username" aria-describedby="basic-addon1"
                                            readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="email">
                                        <?php echo T_EMAIL; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fal fa-envelope">
                                                </i>
                                            </span>
                                        </div>
                                        <input type="text" name="plr-email" id="basic-addon1"
                                            value="<?php echo $resPlayer['Email']; ?>" class="form-control"
                                            placeholder="Email" aria-label="Email" aria-describedby="basic-addon1">
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="npassword">
                                        <?php echo T_PASSWORD; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fal fa-key">
                                                </i>
                                            </span>
                                        </div>
                                        <input type="password" name="plr-pass" id="basic-addon1" class="form-control"
                                            placeholder="New password" aria-label="New password"
                                            aria-describedby="basic-addon1">
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="npassword">
                                        <?php echo T_ISDEV; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fal fa-wrench">
                                                </i>
                                            </span>
                                        </div>
                                        <select name="plr-isdev" class="custom-select">
                                            <option value="-1">สถานะไอดี :
                                                <?php echo $isDev = ($resPlayer['IsDeveloper'] ? 'DEV' : 'Player'); ?>
                                            </option>
                                            <option value="1">DEV
                                            </option>
                                            <option value="0">Player
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary waves-effect waves-themed"
                                data-dismiss="modal">Close
                            </button>
                            <button type="submit" name="btn-savechangePassword"
                                class="btn btn-primary waves-effect waves-themed">
                                <?php echo B_SAVECHANGES; ?>
                            </button>
                        </div>
                </div>
                </form>
            </div>
        </div>

        <!-- Modal เปลียนชัพพาสเวิส id-->
        <div class="modal fade modal-transparent-subpassword" tabindex="-1" role="dialog" style="display: none;"
            aria-hidden="true">
            <div class="modal-dialog modal-transparent" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title text-white">
                            Chang New Sub Password
                            <small class="m-0 text-white opacity-70">
                                เปลียนชัพพาสเวิส
                            </small>
                        </h4>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">
                                <i class="fal fa-times">
                                </i>
                            </span>
                        </button>
                    </div>
                    <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                if (isset($_POST['btn-savechangeSubpass'])){
                                    // variables
                                    $input_password = strip_tags(trim($_POST['plr-pass']));
                                    $pwtype= strip_tags(trim($_POST['inuput-pwtype']));
                                    // condition
									if (empty($input_password))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_R_PASS_EMPTY . '","error");';
                                        echo '});</script>';
                                    }
									else if (empty($getCategoryID))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!"," ไอดีผิดพลาด","error");';
                                        echo '});</script>';
									}
                                    else if ($pwtype == "-1" || $pwtype < "0")
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!"," เลือกชัพพาสเวิสที่จะเปลียน","error");';
                                        echo '});</script>';
                                    } else {
                                                                        // get data info
                                        $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                        $selectPlayerDataParam = array();
                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                        $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        $finalPass = $resPlayer['Password'];
                                        $NewPwd = $input_password;

                                        if (sqlsrv_rows_affected($selectPlayerDataQuery)){
                                            // generate a log
                                            $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'update Sub password player account', "playerId: { {$UserID} } password Type {$pwtype} account was successfully updated!");

                                            // updateSubpassData
                                            $updateSubpassData = "EXECUTE " . DATABASE_ACC . ".dbo.cabal_sp_sub_password_set ?,?,?";
                                            $updateSubpassDataParam = array($PlayerUserNum,$pwtype,$input_password);
                                            $updateSubpassDataQuery = sqlsrv_query($conn, $updateSubpassData, $updateSubpassDataParam);
											 if ($updateSubpassDataQuery) {
													echo '<script type="text/javascript">';
													echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","' . S_PLR_ACCOUNT_UPDATED . '","success");';
													echo '});</script>';
											 }else{
													echo '<script type="text/javascript">';
													echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
													echo '});</script>'; 
											 }
                                        }
                                        else
                                        {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
                                            echo '});</script>';
                                        }
                                    }
                                }
                            ?>
                    <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                        <div class="modal-body">
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="Username">
                                        <?php echo T_ACCOUNTINFO; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="Username">
                                                <i class="ni ni-user fs-xl">
                                                </i>
                                            </span>
                                        </div>
                                        <input type="text" name="username" id="basic-addon1"
                                            value="<?php echo $resPlayer['ID']; ?>" class="form-control"
                                            placeholder="Username" aria-label="Username" aria-describedby="basic-addon1"
                                            readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="npassword">
                                        <?php echo T_PASSWORD; ?>
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fal fa-key">
                                                </i>
                                            </span>
                                        </div>
                                        <input type="password" name="plr-pass" id="basic-addon1" class="form-control"
                                            placeholder="New password" aria-label="New password"
                                            aria-describedby="basic-addon1">
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label" for="npassword">
                                        เลือก SubPassword ที่จะเปลียน
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                                <i class="fal fa-wrench">
                                                </i>
                                            </span>
                                        </div>
                                        <select name="inuput-pwtype" class="custom-select">
                                            <option value="-1">เลือก SubPassword ที่จะเปลียน
                                            </option>
                                            <option value="1">SubPassword เข้าเกมส์
                                            </option>
                                            <option value="2">SubPassword ล็อคช่องเก็บของ
                                            </option>
                                            <option value="3">SubPassword ล็อคของในตัวละคร
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary waves-effect waves-themed"
                                data-dismiss="modal">Close
                            </button>
                            <button type="submit" name="btn-savechangeSubpass"
                                class="btn btn-primary waves-effect waves-themed">
                                <?php echo B_SAVECHANGES; ?>
                            </button>
                        </div>
                </div>
                </form>
            </div>
        </div>

        <!-- Modal Ban id-->
        <div class="modal fade" id="bannedModel" tabindex="-1" role="dialog" aria-labelledby="financialModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="myModalLabel">
                            แบนไอดีพร้อมข้อมูลการแบน
                        </h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;
                            </span>
                        </button>
                    </div>
                    <form method="post" name="j_add_banned" action="">
                        <div class="modal-body">
                            <div class="col-lg-12 j_alert"></div>
                            <h5 class="text-danger">
                                การแบนไอดีต้องลงรายระเอียดการแบนให้ครบเพื่อง่ายต่อการตรวจสอบ
                            </h5>
                            <div class="form-group">
                                <div class="demo">
                                    <?php 
                                        $selectAcc = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$userNum'";
                                        $selectAccQuery = sqlsrv_query($conn, $selectAcc, array());
                                        $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                                    ?>
                                    <?php if ($selectAccFetch['AuthType'] == "1"): ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1" checked>
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2">
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php elseif($selectAccFetch['AuthType'] == "2"): ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1">
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2" checked>
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php else: ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1">
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2" checked>
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="plr-gc">รายระเอียด
                                </label>
                                <input class="form-control" id="input_detail" type="text" name="input_detail"
                                    placeholder="เพิ่มรายระเอียดข้อมูลการแบนไอดี">
                            </div>
                            <div class="form-group">
                                <label for="plr-gc">หลักฐาน
                                </label>
                                <input class="form-control" id="input_premise" type="text" name="input_premise"
                                    placeholder="เพิ่มหลักฐานการแบน (ถ้ามี)">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-warning waves-effect waves-themed"
                                data-dismiss="modal">
                                <?php echo B_CLOSE; ?>
                            </button>
                            <button type="submit" class="btn btn-warning waves-effect waves-themed">ยืนยันข้อมูล
                            </button>
                            <input type="hidden" name="CustomerID" value="<?php echo strip_tags(trim($_GET['id'])); ?>">
                            <input type="hidden" name="AdminCID"
                                value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
                        </div>
                    </form>
                </div>
            </div>
        </div>