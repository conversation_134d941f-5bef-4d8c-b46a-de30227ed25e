<?php
// AJAX endpoint for getting user details
$zpanel->checkSession(true);

header('Content-Type: application/json');

if (!isset($_GET['user_id'])) {
    echo json_encode(['error' => 'User ID is required']);
    exit;
}

$userId = intval($_GET['user_id']);

try {
    // Get user account details
    $selectUserDetails = "SELECT 
        ID,
        UserNum,
        createDate,
        LoginTime,
        LogoutTime,
        LastIp,
        PlayTime,
        AuthType,
        Login
        FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
        WHERE UserNum = ?";
    
    $userQuery = sqlsrv_query($conn, $selectUserDetails, array($userId));
    
    if ($userQuery === false) {
        throw new Exception('Failed to query user details');
    }
    
    $userDetails = sqlsrv_fetch_array($userQuery, SQLSRV_FETCH_ASSOC);
    
    if (!$userDetails) {
        echo json_encode(['error' => 'User not found']);
        exit;
    }
    
    // Get character details
    $selectCharacters = "SELECT 
        CharacterIdx,
        CharName,
        LEV,
        Nation,
        Class,
        CreateDate
        FROM [".DATABASE_SV."].[dbo].cabal_character_table 
        WHERE CharacterIdx/8 = ?";
    
    $charQuery = sqlsrv_query($conn, $selectCharacters, array($userId));
    $characters = array();
    
    if ($charQuery !== false) {
        while ($char = sqlsrv_fetch_array($charQuery, SQLSRV_FETCH_ASSOC)) {
            $characters[] = $char;
        }
    }
    
    // Get financial details
    $selectFinancial = "SELECT 
        Cash,
        CashBonus,
        CashTotal,
        Reward
        FROM [".DATABASE_CCA."].[dbo].cabal_cash_table 
        WHERE CustomerID = ?";
    
    $finQuery = sqlsrv_query($conn, $selectFinancial, array($userId));
    $financial = array(
        'Cash' => 0,
        'CashBonus' => 0,
        'CashTotal' => 0,
        'Reward' => 0
    );
    
    if ($finQuery !== false) {
        $finData = sqlsrv_fetch_array($finQuery, SQLSRV_FETCH_ASSOC);
        if ($finData) {
            $financial = $finData;
        }
    }
    
    // Get recent login history
    $selectLoginHistory = "SELECT TOP 10
        LoginTime,
        LogoutTime,
        LastIp
        FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
        WHERE UserNum = ?
        ORDER BY LoginTime DESC";
    
    $historyQuery = sqlsrv_query($conn, $selectLoginHistory, array($userId));
    $loginHistory = array();
    
    if ($historyQuery !== false) {
        while ($history = sqlsrv_fetch_array($historyQuery, SQLSRV_FETCH_ASSOC)) {
            $loginHistory[] = array(
                'LoginTime' => $history['LoginTime'] ? date('Y-m-d H:i:s', strtotime($history['LoginTime'])) : null,
                'LogoutTime' => $history['LogoutTime'] ? date('Y-m-d H:i:s', strtotime($history['LogoutTime'])) : null,
                'LastIp' => $history['LastIp']
            );
        }
    }
    
    // Format response
    $response = array(
        'success' => true,
        'user' => array(
            'ID' => $userDetails['ID'],
            'UserNum' => $userDetails['UserNum'],
            'createDate' => $userDetails['createDate'] ? date('Y-m-d H:i:s', strtotime($userDetails['createDate'])) : null,
            'LoginTime' => $userDetails['LoginTime'] ? date('Y-m-d H:i:s', strtotime($userDetails['LoginTime'])) : null,
            'LogoutTime' => $userDetails['LogoutTime'] ? date('Y-m-d H:i:s', strtotime($userDetails['LogoutTime'])) : null,
            'LastIp' => $userDetails['LastIp'],
            'PlayTime' => round(($userDetails['PlayTime'] ?? 0) / 60, 1), // Convert to hours
            'AuthType' => $userDetails['AuthType'],
            'Login' => $userDetails['Login'],
            'Status' => getStatusText($userDetails['AuthType'], $userDetails['Login'])
        ),
        'characters' => $characters,
        'financial' => $financial,
        'loginHistory' => $loginHistory,
        'stats' => array(
            'totalCharacters' => count($characters),
            'maxLevel' => count($characters) > 0 ? max(array_column($characters, 'LEV')) : 0,
            'totalCash' => $financial['CashTotal'],
            'playTimeHours' => round(($userDetails['PlayTime'] ?? 0) / 60, 1)
        )
    );
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}

function getStatusText($authType, $login) {
    if ($authType == 2) {
        return 'ถูกแบน';
    } elseif ($login == 1) {
        return 'ออนไลน์';
    } else {
        return 'ออฟไลน์';
    }
}

function getStatusClass($authType, $login) {
    if ($authType == 2) {
        return 'danger';
    } elseif ($login == 1) {
        return 'success';
    } else {
        return 'default';
    }
}

function getClassText($classId) {
    $classes = array(
        1 => 'Warrior',
        2 => 'Blader',
        3 => 'Wizard',
        4 => 'Force Archer',
        5 => 'Force Shielder',
        6 => 'Force Blader',
        7 => 'Gladiator',
        8 => 'Force Gunner',
        9 => 'Dark Mage'
    );
    
    return isset($classes[$classId]) ? $classes[$classId] : 'Unknown';
}

function getNationText($nationId) {
    $nations = array(
        1 => 'Capella',
        2 => 'Procyon'
    );
    
    return isset($nations[$nationId]) ? $nations[$nationId] : 'Unknown';
}
?>
