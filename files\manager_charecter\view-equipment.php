<style>
.equip-slot {
  width: 100px;
  height: 100px;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 120% 120%; /* ขยายภาพให้ใหญ่ขึ้น */
  background-color: rgba(0, 0, 0, 0.3); /* ให้พื้นหลังโปร่งแสง */
  border-radius: 0.4rem;
  overflow: hidden;
  cursor: default;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  color: #fff;
  user-select: none;
}

.equip-slot.empty {
  background-color: rgba(68, 68, 68, 0.5);
  opacity: 0.5;
}

.equip-slot > div {
  background: rgba(0, 0, 0, 0.0);
  width: 100%;
  font-size: 11px;
  padding: 2px 4px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0.4rem;
  border-bottom-right-radius: 0.4rem;
  user-select: text;
}

.equip-slot .label {
  position: absolute;
  top: 2px;
  left: 4px;
  font-size: 10px;
  color: #ccc;
  user-select: none;
  pointer-events: none;
}

/* Highlight slot as drop target */
.equip-slot.drag-over {
    outline: 2px dashed #ff9800;
    box-shadow: 0 0 0 2px #ff9800;
    z-index: 2;
}

/* Highlight tab as drop target */
.nav-link.drag-tab-over {
    background: #ffe0b2 !important;
    border-bottom: 2px solid #ff9800 !important;
}
</style>


<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
    <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
        <div class="card-header bg-warning text-dark py-3">
            <h5 class="mb-0"><i class="fas fa-boxes mr-2"></i>ตัวละคร (Equipment View)</h5>
        </div>
        <div class="card-body" id="equipmentViewer">
            <div class="text-center text-muted">กำลังโหลดข้อมูลอุปกรณ์...</div>
        </div>
        <h3 class="mt-4">🔢 Equipment HexData รวมทั้งหมด</h3>
        <div style="position:relative;">
            <textarea id="equipmenthexAll" class="form-control mb-2" style="height:200px;" readonly></textarea>
            <div id="equipmenthexAllOverlay" style="pointer-events:none;position:absolute;left:0;top:0;width:100%;height:100%;border-radius:4px;display:none;"></div>
        </div>
        <button id="updateEquipmentBtn" class="btn btn-primary mb-4">Update Equipment</button>
    </div>
<?php } ?>

 <div class="modal fade" id="editEquipModal" tabindex="-1" role="dialog" aria-labelledby="editEquipModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editEquipModalLabel">แก้ไข Slot</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="editEquipForm">
                            <div class="form-group">
                                <label for="equipSlotNumber">Slot Number</label>
                                <input type="text" class="form-control" id="equipSlotNumber" readonly>
                            </div>
                            <div class="form-group">
                                <label for="equipItemIndex">Item Index</label>
                                <input type="text" class="form-control" id="equipItemIndex">
                            </div>
                            <div class="form-group">
                                <label for="equipSerial">Serial</label>
                                <input type="text" class="form-control" id="equipSerial">
                            </div>
                            <div class="form-group">
                                <label for="equipOption">Option</label>
                                <input type="text" class="form-control" id="equipOption">
                            </div>
                            <div class="form-group">
                                <label for="equipPeriod">Period</label>
                                <input type="text" class="form-control" id="equipPeriod">
                            </div>
                            <div class="form-group">
                                <label for="equipKindIdx">Kind Index</label>
                                <input type="text" class="form-control" id="equipKindIdx">
                            </div>
                            <div class="form-group">
                                <label for="equipHexPreview">Hex Preview</label>
                                <textarea class="form-control" id="equipHexPreview" readonly></textarea>
                            </div>
                            <div class="modal-footer">
                            <button type="button" class="btn btn-danger" id="deleteEquipSlotBtn" style="margin-right:auto;">ลบข้อมูล</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                            <button type="submit" class="btn btn-success">บันทึก</button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


<script>
let equipmentResult = {};
(function() {
    // Load slot mapping from slotconfig.json
    let slotMapping = [];
    $.get('_app/php/slotconfig.php', function(data) {
        try {
            slotMapping = JSON.parse(data);
        } catch(e) { slotMapping = []; }
        window.getSlotMapping = function() { return slotMapping; };
    });
})();
const equipImageBasePath = 'assets/images/items/';
(function () {
        window.equipItemNameMap = <?php echo json_encode($itemNameMap, JSON_UNESCAPED_UNICODE); ?>;
        const equipCharIdx = <?php echo isset($characterData['CharacterIdx']) ? (int)$characterData['CharacterIdx'] : 0; ?>;

        $.getJSON('_app/php/equipment_data.php?ajax=1&charIdx=' + encodeURIComponent(equipCharIdx))
            .done(function(equipData) {
                equipmentResult = equipData;
                // console.log('✅ Equipment Loaded:', equipmentResult);

                // เปลี่ยนจำนวน slotในแต่ละ tab เป็น 28 (4*7) ทุก tab
                const equipmentTabSlots = [28, 28, 28, 28];
                const equipmentTabNames = ['อุปกรณ์หลัก', 'สัตว์เลี้ยง', 'อุปกรณ์พิเศษ', 'อื่นๆ'];
                const equipmentNav = $('<ul class="nav nav-tabs" id="equipmentTabNav" role="tablist"></ul>');
                const equipmentContent = $('<div class="tab-content border border-top-0 p-3" id="equipmentTabs"></div>');

                // รอ slotMapping โหลดเสร็จ
                function renderSlots() {
                    let globalEquipSlotIndex = 0;
                    for (let tabIndex = 0; tabIndex < equipmentTabSlots.length; tabIndex++) {
                        const tabId = 'equip_tab_' + tabIndex;
                        equipmentNav.append(`
                            <li class="nav-item" role="presentation">
                                <a class="nav-link${tabIndex === 0 ? ' active' : ''}" id="${tabId}-tab" data-toggle="tab" href="#${tabId}" role="tab" aria-controls="${tabId}" aria-selected="${tabIndex === 0 ? 'true' : 'false'}">${equipmentTabNames[tabIndex]}</a>
                            </li>
                        `);

                        const equipGrid = $(`<div class="grid d-flex flex-wrap gap-2" id="equipGrid_${tabIndex}"></div>`);

                        for (let slotInTab = 0; slotInTab < equipmentTabSlots[tabIndex]; slotInTab++, globalEquipSlotIndex++) {
                            // ใช้ slotMapping
                            let slotObj = window.getSlotMapping()[globalEquipSlotIndex];
                            let slotIdx = slotObj ? parseInt(slotObj.slot) : globalEquipSlotIndex;
                            let slotId = slotObj ? slotObj.id : globalEquipSlotIndex;
                            const equipmentSlotData = equipmentResult[slotIdx] || {};
                            // id label is always shown and fixed
                            // Popover content
                            let popoverContent = `<div style='min-width:180px;'>`;
                            let itemName = '';
                            let itemImg = '';
                            if (equipmentSlotData && equipmentSlotData.itemIndex) {
                                itemName = window.equipItemNameMap[equipmentSlotData.itemIndex] || '';
                                itemImg = `<img src='${equipImageBasePath}${equipmentSlotData.itemIndex}.png' style='width:120px;height:120px;object-fit:contain;border-radius:12px;border:1px solid #ccc;background:#222;margin-bottom:8px;' onerror=\"this.src='${equipImageBasePath}default.png'\">`;
                            } else {
                                itemImg = `<img src='${equipImageBasePath}default.png' style='width:120px;height:120px;object-fit:contain;border-radius:12px;border:1px solid #ccc;background:#222;margin-bottom:8px;'>`;
                            }
                            popoverContent += itemImg;
                            popoverContent += `<div><b>${itemName}</b></div>`;
                            popoverContent += `<div>Slot: ${slotIdx}</div>`;
                            if (equipmentSlotData) {
                                popoverContent += `<div>ItemIndex: ${equipmentSlotData.itemIndex ?? ''}</div>`;
                                popoverContent += `<div>Option: ${equipmentSlotData.Option ?? ''}</div>`;
                                popoverContent += `<div>Serial: ${equipmentSlotData.Serial ?? ''}</div>`;
                                popoverContent += `<div>Period: ${equipmentSlotData.Period ?? ''}</div>`;
                                popoverContent += `<div>KindIdx: ${equipmentSlotData.KindIdx ?? ''}</div>`;
                            }
                            popoverContent += `</div>`;
                            const equipmentSlotDiv = $(`
                            <div class="equip-slot text-white text-center d-flex flex-column justify-content-end align-items-center" 
                                data-slot="${slotIdx}" 
                                data-id="${slotId}" 
                                id="equip_slot_${slotIdx}" draggable="${slotObj ? 'true' : 'false'}" 
                                data-toggle="popover" data-html="true" data-trigger="click" data-placement="right" data-content="${popoverContent.replace(/"/g, '&quot;')}">
                                <div class="slot-label" style="font-size:11px;"></div>
                            </div>
                            `);
                            // Show id as fixed label (top left)
                            equipmentSlotDiv.append(`<div class="label" style="position:absolute;top:0;left:0;font-size:10px;color:#ffc107;background:rgba(0,0,0,0.7);padding:0 4px;border-radius:3px;z-index:2;">Slot: ${slotObj ? slotObj.slot : ''}</div>`);
                            //equipmentSlotDiv.append(`<div class="label" style="position:absolute;top:0;left:0;font-size:10px;color:#ffc107;background:rgba(0,0,0,0.7);padding:0 4px;border-radius:3px;z-index:2;">ID: ${slotObj ? slotObj.id : ''}</div>`);
                            // Move slot label to top-left, below ID label
                            //equipmentSlotDiv.append(`<div class="label" style="position:absolute;top:18px;left:0;font-size:10px;color:#aaa;background:rgba(0,0,0,0.5);padding:0 4px;border-radius:3px;z-index:2;">Slot: ${slotObj ? slotObj.slot : ''}</div>`);

                            const equipItemIndex = equipmentSlotData.itemIndex ? parseInt(equipmentSlotData.itemIndex) : 0;
                            const equipOption = equipmentSlotData.Option !== undefined ? equipmentSlotData.Option : '';

                            if (equipItemIndex > 0 && !isNaN(equipItemIndex)) {
                                let imagePath = equipImageBasePath + equipItemIndex + '.png';
                                let image = new Image();
                                image.onload = function () {
                                    equipmentSlotDiv.css({
                                        backgroundImage: `url('${imagePath}')`,
                                        backgroundSize: '200% 200%',
                                        backgroundPosition: 'center'
                                    });
                                };
                                image.onerror = function () {
                                    let fallbackPath = equipImageBasePath + 'default.png';
                                    equipmentSlotDiv.css({
                                        backgroundImage: `url('${fallbackPath}')`,
                                        backgroundSize: '200% 200%',
                                        backgroundPosition: 'center'
                                    });
                                };
                                image.src = imagePath;

                                let itemName = window.equipItemNameMap[equipItemIndex] || '';
                                equipmentSlotDiv.append(`<div class="label" style="position:absolute;top:18px;left:0;font-size:10px;color:#aaa;background:rgba(0,0,0,0.5);padding:0 4px;border-radius:3px;z-index:2;">${itemName}</div>`);
                                //equipmentSlotDiv.append(`<div style="font-size:11px;color:#fff;background:rgba(0,0,0,0.1);">${itemName}</div>`);
                                equipmentSlotDiv.append(`<div style="font-size:10px;color:#ffe082;background:rgba(0,0,0,0.1);">${equipItemIndex}</div>`);
                                equipmentSlotDiv.append(`<div style="font-size:10px;color:#80cbc4;background:rgba(0,0,0,0.1);">: ${equipOption}</div>`);
                            } else {
                                equipmentSlotDiv.css({
                                    opacity: 0.5,
                                    backgroundColor: '#444'
                                }).addClass('empty');
                                equipmentSlotDiv.append(`<div style="font-size:11px;color:#888;">(ว่าง)</div>`);
                            }

                            equipGrid.append(equipmentSlotDiv);
                        }

                        equipmentContent.append(
                            $(`<div class="tab-pane fade${tabIndex === 0 ? ' show active' : ''}" id="${tabId}" role="tabpanel" aria-labelledby="${tabId}-tab">`).append(equipGrid)
                        );
                    }

                    $('#equipmentViewer').empty().append(equipmentNav).append(equipmentContent);
                    // Remove any previous switch before inserting
                    $('#moveSlotSwitchContainer').remove();
                    if (typeof insertSwitch === 'function') insertSwitch();
                    // Enable popover for all slots after switch is inserted
                    setTimeout(function() {
                        $('[data-toggle="popover"]').popover('dispose');
                        $('[data-toggle="popover"]').popover({container:'body', trigger:'click'});
                    }, 300);
                    // Use event delegation for popover to ensure it works after DOM updates
                    $(document).off('click.equipPopover').on('click.equipPopover', '.equip-slot', function(e) {
                        // Only show popover if not dragging
                        if (!window.allowMoveSlot) {
                            // Bootstrap popover will handle show/hide
                        }
                    });
                }
                // รอ slotMapping โหลดเสร็จ
                if (window.getSlotMapping && Array.isArray(window.getSlotMapping()) && window.getSlotMapping().length > 0) {
                    renderSlots();
                } else {
                    // ถ้า slotMapping ยังไม่มา รอ 100ms แล้ว render
                    let waitMapping = setInterval(function() {
                        if (window.getSlotMapping && Array.isArray(window.getSlotMapping()) && window.getSlotMapping().length > 0) {
                            clearInterval(waitMapping);
                            renderSlots();
                        }
                    }, 100);
                }

                // Drag & drop sorting (cross-tab)
                let dragFromSlot = null;
                let dragFromTab = null;
                $('.equip-slot').on('dragstart', function(e) {
                    if (!window.allowMoveSlot) {
                        e.preventDefault();
                        return false;
                    }
                    // Only allow drag for slot, not id
                    const slotIdx = $(this).data('slot');
                    const slotId = $(this).data('id');
                    let mapping = window.getSlotMapping();
                    // Only allow drag if slotObj exists (real slot)
                    const found = mapping.find(obj => parseInt(obj.slot) === parseInt(slotIdx));
                    if (!found) {
                        e.preventDefault();
                        return false;
                    }
                    // Drag by slot only
                    e.originalEvent.dataTransfer.setData('text/plain', slotIdx);
                    dragFromSlot = slotIdx;
                    dragFromTab = $(this).closest('.tab-pane').attr('id');
                    // Enable tab switching on drag
                    $('.nav-link').on('dragenter', function(ev) {
                        ev.preventDefault();
                        $('.nav-link').removeClass('drag-tab-over');
                        $(this).addClass('drag-tab-over');
                        // Auto-switch tab immediately on dragenter
                        const tabId = $(this).attr('href');
                        if (tabId) {
                            $(this).tab('show');
                        }
                    });
                    $('.nav-link').on('dragover', function(ev) {
                        ev.preventDefault();
                    });
                    $('.nav-link').on('dragleave', function(ev) {
                        $(this).removeClass('drag-tab-over');
                    });
                    $('.nav-link').on('drop', function(ev) {
                        ev.preventDefault();
                        $('.nav-link').removeClass('drag-tab-over');
                        // Switch tab on drop
                        const tabId = $(this).attr('href');
                        if (tabId) {
                            $(this).tab('show');
                        }
                    });
                });
                $('.equip-slot').on('dragover', function(e) {
                    if (!window.allowMoveSlot) {
                        e.preventDefault();
                        return false;
                    }
                    // Only allow drop on real slot
                    const toSlot = $(this).data('slot');
                    let mapping = window.getSlotMapping();
                    const found = mapping.find(obj => parseInt(obj.slot) === parseInt(toSlot));
                    if (!found) {
                        e.preventDefault();
                        return false;
                    }
                    e.preventDefault();
                    $('.equip-slot').removeClass('drag-over');
                    $(this).addClass('drag-over');
                });
                $('.equip-slot').on('dragleave', function(e) {
                    $(this).removeClass('drag-over');
                });
                $('.equip-slot').on('drop', function(e) {
                    if (!window.allowMoveSlot) {
                        e.preventDefault();
                        return false;
                    }
                    // Only allow drop for slot
                    const toSlot = $(this).data('slot');
                    let mapping = window.getSlotMapping();
                    const found = mapping.find(obj => parseInt(obj.slot) === parseInt(toSlot));
                    if (!found) {
                        e.preventDefault();
                        return false;
                    }
                    e.preventDefault();
                    $('.equip-slot').removeClass('drag-over');
                    const fromSlot = dragFromSlot;
                    // Find index by slot
                    const fromIdx = mapping.findIndex(obj => parseInt(obj.slot) === parseInt(fromSlot));
                    const toIdx = mapping.findIndex(obj => parseInt(obj.slot) === parseInt(toSlot));
                    if (fromIdx !== -1 && toIdx !== -1 && fromIdx !== toIdx) {
                        // Swap only slot
                        [mapping[fromIdx].slot, mapping[toIdx].slot] = [mapping[toIdx].slot, mapping[fromIdx].slot];
                        // Save mapping
                        $.post('_app/php/slotconfig.php', {slot_mapping: JSON.stringify(mapping)}, function(resp) {
                            location.reload();
                        });
                    }
                    dragFromSlot = null;
                    dragFromTab = null;
                    // Remove tab highlight
                    $('.nav-link').removeClass('drag-tab-over');
                    // Remove tab switching events
                    $('.nav-link').off('dragenter dragover dragleave drop');
                });

            let hexCombined = '0x';
            const totalSlots = equipmentTabSlots.reduce((sum, count) => sum + count, 0);
for (let i = 0; i < totalSlots; i++) {
    const slotData = equipmentResult[i];
    if (slotData?.HexData) {
        hexCombined += slotData.HexData;
    }
    // ถ้าไม่มีข้อมูล slot นี้ จะไม่เติมอะไรลงไป (ตัดออก)
}
$('#equipmenthexAll').val(hexCombined);
    
            })
            .fail(function(jqxhr, textStatus, error) {
                // console.error('❌ Equipment AJAX Failed:', textStatus, error);
                $('#equipmentViewer').html('<div class="alert alert-danger">โหลดข้อมูลอุปกรณ์ล้มเหลว: ' + textStatus + '</div>');
            });

        // ปุ่ม Update Equipment - ส่งข้อมูล HexData ที่แก้ไขกลับไปเซิร์ฟเวอร์
        $('#updateEquipmentBtn').on('click', function() {
            // อัปเดต HexData รวมทั้งหมดจาก equipmentResult
            let hexCombined = '0x';
            const totalSlots = [28,3,9,24].reduce((sum, count) => sum + count, 0);
            let debugLog = [];
            for (let i = 0; i < totalSlots; i++) {
                if (equipmentResult[i]?.HexData) {
                    hexCombined += equipmentResult[i].HexData;
                }
                debugLog.push({slot: i, data: equipmentResult[i]});
            }
            $('#equipmenthexAll').val(hexCombined);
            // Debug: log slot 64 data
            // console.log('DEBUG slot 64:', equipmentResult[64]);
            // console.log('DEBUG all slots:', debugLog);
            // อัปเดต Hex Preview ใน modal ถ้า modal เปิดอยู่
            if ($('#editEquipModal').hasClass('show') && currentEquipSlotIndex !== null) {
                const slotData = equipmentResult[currentEquipSlotIndex] || {};
                $('#equipHexPreview').val(slotData.HexData || '(ว่าง)');
            }
            // ส่งข้อมูลไปเซิร์ฟเวอร์
            $.ajax({
                url: '_app/php/equipment_data.php',
                method: 'POST',
                data: {
                    update_equipment: 1,
                    charIdx: equipCharIdx,
                    HexData: hexCombined
                },
                success: function(response) {
                    alert('อัปเดตข้อมูล Equipment เรียบร้อยแล้ว');
                    // console.log('Update Response:', response);
                },
                error: function(xhr, status, error) {
                    alert('เกิดข้อผิดพลาดในการอัปเดตข้อมูล: ' + error);
                    // console.error('Update Error:', error);
                }
            });
        });
    })();

let currentEquipSlotIndex = null;
// เปิด modal เมื่อคลิกขวาที่ slot
$(document).off('contextmenu.equip').on('contextmenu.equip', '.equip-slot', function(e) {
    e.preventDefault();
    currentEquipSlotIndex = $(this).data('slot');
    const data = equipmentResult[currentEquipSlotIndex] || {
        itemIndex: '',
        Serial: '',
        Option: '',
        Period: '',
        KindIdx: ''
    };
    $('#equipSlotLabel').text(currentEquipSlotIndex);
    $('#equipSlotNumber').val(currentEquipSlotIndex);
    $('#equipItemIndex').val(data.itemIndex || '0');
    $('#equipSerial').val(data.Serial || '0');
    $('#equipOption').val(data.Option || '0');
    $('#equipPeriod').val(data.Period || '0');
    $('#equipKindIdx').val(data.KindIdx || '0');
    // อัปเดต Hex preview แบบ real-time
    function updateModalHex() {
        let KindIdx = parseInt($('#equipKindIdx').val()) || 0;
        const itemIndex = parseInt($('#equipItemIndex').val()) || 0;
        const Serial = parseInt($('#equipSerial').val()) || 0;
        const Option = parseInt($('#equipOption').val()) || 0;
        const slotNum = parseInt($('#equipSlotNumber').val()) || 0;
        const Period = parseInt($('#equipPeriod').val()) || 0;
        KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        const hexData =
            bigIntToHexLE(KindIdx, 8) +
            bigIntToHexLE(Serial, 8) +
            bigIntToHexLE(Option, 8) +
            bigIntToHexLE(slotNum, 2) +
            bigIntToHexLE(Period, 4);
        $('#equipHexPreview').val(hexData.toUpperCase());
    }
    updateModalHex();
    $('#editEquipForm input').off('input.equiphex').on('input.equiphex', updateModalHex);
    // ตรวจสอบ modal ใน DOM ก่อนเปิด
    if ($('#editEquipModal').length) {
        $('#editEquipModal').modal('show');
    } else {
        alert('Modal ไม่พบใน DOM');
    }
});

    // ปิด modal เมื่อกดปุ่มยกเลิก หรือปุ่ม X (Bootstrap 4)
    $('#cancelEditSlot, #closeModalBtn').on('click', function() {
        $('#editSlotModal').modal('hide');
    });

    $('#editEquipForm').on('submit', function(e) {
    e.preventDefault();
    // อัปเดตข้อมูล slot ใน equipmentResult
    const slot = parseInt($('#equipSlotNumber').val());
    const itemIndex = parseInt($('#equipItemIndex').val()) || 0;
    const Serial = parseInt($('#equipSerial').val()) || 0;
    const Option = parseInt($('#equipOption').val()) || 0;
    const Period = parseInt($('#equipPeriod').val()) || 0;
    let KindIdx = parseInt($('#equipKindIdx').val()) || 0;
    KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
    const hexData = $('#equipHexPreview').val();
    equipmentResult[slot] = {
        KindIdx: KindIdx,
        itemIndex: itemIndex,
        Serial: Serial,
        Option: Option,
        Period: Period,
        HexData: hexData,
        _changed: true
    };
    // อัพเดท UI slot
    const slotDiv = $('#equip_slot_' + slot);
    slotDiv.empty().removeClass('empty');
    slotDiv.append(`<div class="label">${slot}</div>`);
    slotDiv.append(`<div style="font-size:11px;color:#fff;background:rgba(0,0,0,0.5);">${window.equipItemNameMap[itemIndex] || ''}</div>`);
    slotDiv.css({
        backgroundImage: itemIndex > 0 ? `url('${equipImageBasePath + itemIndex + ".png"}')` : '',
        backgroundColor: itemIndex > 0 ? '#222' : '#444',
        opacity: itemIndex > 0 ? 1 : 0.5
    });
    $('#editEquipModal').modal('hide');
    // อัปเดต HexData รวมทั้งหมดทันที (เรียง slot ตาม index)
    let hexCombined = '0x';
    const totalSlots = [28,3,9,24].reduce((sum, count) => sum + count, 0);
    for (let i = 0; i < totalSlots; i++) {
        if (equipmentResult[i]?.HexData) {
            hexCombined += equipmentResult[i].HexData;
        }
    }
    $('#equipmenthexAll').val(hexCombined);
});

// แปลงตัวเลขเป็น hex แบบ Little Endian
function bigIntToHexLE(value, byteLength) {
    const hex = value.toString(16).padStart(byteLength * 2, '0');
    const bytes = hex.match(/.{2}/g)?.reverse() || [];
    return bytes.join('');
}

// อัปเดต Hex รวมทั้งหมดใน textarea
function updateHexTextarea() {
    let hexCombined = '0x';
    const total = equipmentTabSlots.reduce((s, n) => s + n, 0);
    for (let i = 0; i < total; i++) {
        hexCombined += equipmentResult[i]?.HexData ?? '00'.repeat(60);
    }
    $('#equipmenthexAll').val(hexCombined);
}

// Fix: Ensure modal close button works for dynamically created modal
$(document).on('click', '#editEquipModal .close', function() {
    $('#editEquipModal').modal('hide');
});

// ตรวจสอบข้อมูลใน equipmentResult
// ไม่ต้องแสดง error ใน UI หาก equipmentResult ว่าง
// if (!equipmentResult || Object.keys(equipmentResult).length === 0) {
//     console.error('❌ ไม่มีข้อมูลใน equipmentResult');
// }

// Event: ลบข้อมูล slot
$(document).on('click', '#deleteEquipSlotBtn', function() {
    if (currentEquipSlotIndex !== null) {
        if (confirm('คุณต้องการลบไอเท็มในช่องนี้ใช่หรือไม่?')) {
            delete equipmentResult[currentEquipSlotIndex];
            $('#editEquipModal').modal('hide');
            // อัปเดต HexData รวมทั้งหมด
            let hexCombined = '0x';
            const totalSlots = [28,3,9,24].reduce((sum, count) => sum + count, 0);
            for (let i = 0; i < totalSlots; i++) {
                if (equipmentResult[i]?.HexData) {
                    hexCombined += equipmentResult[i].HexData;
                }
            }
            $('#equipmenthexAll').val(hexCombined);
            // อัปเดต UI slot ให้เป็นว่าง
            const slotDiv = $('#equip_slot_' + currentEquipSlotIndex);
            slotDiv.css({backgroundImage:'',backgroundColor:'#444',opacity:0.5}).addClass('empty');
            slotDiv.find('div:not(.label)').remove();
            slotDiv.append('<div style="font-size:11px;color:#888;">(ว่าง)</div>');
        }
    }
});


// ย้าย switch toggle ไปด้านบนขวาของ card-body (equipmentViewer)
$(function() {
    window.allowMoveSlot = false; // ค่าเริ่มต้นเป็นปิด
    const switchHtml = `
      <div id="moveSlotSwitchContainer" style="position:absolute;top:12px;right:24px;z-index:10;">
        <div class="custom-control custom-switch">
          <input type="checkbox" class="custom-control-input" id="toggleMoveSlotSwitch">
          <label class="custom-control-label" for="toggleMoveSlotSwitch" style="user-select:none;">Lock</label>
        </div>
      </div>
    `;
    // รอให้ equipmentViewer มีใน DOM ก่อน
    function insertSwitch() {
      const $viewer = $('#equipmentViewer');
      if ($viewer.length && $('#moveSlotSwitchContainer').length === 0) {
        $viewer.css('position','relative').prepend(switchHtml);
        // sync label and global var
        $('#toggleMoveSlotSwitch').prop('checked', false); // default Lock (active)
        window.allowMoveSlot = false;
        $('#toggleMoveSlotSwitch').next('label').text('Lock');
      } else if ($viewer.length === 0) {
        setTimeout(insertSwitch, 100);
      }
    }
    insertSwitch();
    // Use event delegation for switch to ensure it works after DOM updates
    $(document).off('change.toggleMoveSlotSwitch').on('change.toggleMoveSlotSwitch', '#toggleMoveSlotSwitch', function() {
      const checked = $(this).prop('checked');
      window.allowMoveSlot = checked;
      $(this).next('label').text(checked ? 'UnLock' : 'Lock');
      window.toggleMoveSlotSwitch = $(this);
    });
});
    </script>

