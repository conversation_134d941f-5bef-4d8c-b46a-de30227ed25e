<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Manage-Account && Charecter
        <small>
            ระบบ ตรวจสอบข้อมูลผู้เล่น
        </small>
    </h1>
</div>

<?php

if (isset($_POST['btn_loadaccountdata_json'])) {
    $params = array();
    $options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
    $selectChars = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table";
    $selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);

    if (sqlsrv_num_rows($selectCharsQuery)) {
        $array_data = array();
        // for($i = 1; $i < sqlsrv_num_rows($selectCharsQuery); $i++) {

        while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
            // $number = $i++;
            $array_data[] = array(
                'UserNum' => $resChars['UserNum'],
                'ID' => $resChars['ID'],
                'Login' => $resChars['Login'],
                'LoginTime' => $resChars['LoginTime'],
                'LogoutTime' => $resChars['LogoutTime'],
                'AuthType' => $resChars['AuthType'],
                'PlayTime' => $resChars['PlayTime'],
                'LastIp' => $resChars['LastIp'],
                'createDate' => $resChars['createDate'],
                'Email' => $resChars['Email'],
                'IP' => $resChars['IP'],
                'Phone_Number' => $resChars['Phone_Number'],
                'Action' => $resChars['UserNum']
            );
        }
        $response = array(
            "data" => $array_data
        );
        $final_data = json_encode($response, JSON_PRETTY_PRINT);
        if (file_put_contents('_data/account_data.json', $final_data)) {
            $returnSuccess = "File Appended Success fully";
        } else {
            $returnError = 'JSON File not exits';
        }
        //}  
    } else {
        $returnError = 'ข้อมูลผิดพลาด';
    }
}
if (isset($_POST['btn_loadcharacterdata_json'])) {
    $params = array();
    $options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
    $selectChars = "SELECT * FROM [" . DATABASE_SV . "].[dbo].cabal_character_table";
    $selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);

    if (sqlsrv_num_rows($selectCharsQuery)) {
        $array_data = array();
        // for($i = 1; $i < sqlsrv_num_rows($selectCharsQuery); $i++) {

        while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
            -
                $name = $userLogin->thaitrans($resChars['Name']);
            // $number = $i++;
            $array_data[] = array(
                'CharacterIdx' => $resChars[CharacterIdx],
                'Name' => "$name",
                'LEV' => $resChars[LEV],
                'STR' => $resChars[STR],
                'DEX' => $resChars[DEX],
                'INT' => $resChars[INT],
                'PNT' => $resChars[PNT],
                'Alz' => number_format($resChars[Alz]),
                'WorldIdx' => "$resChars[WorldIdx]",
                'LogoutTime' => "$resChars[LogoutTime]",
                'Reputation' => number_format($resChars[Reputation]),
                'LoginTime' => "$resChars[LoginTime]",
                'PlayTime' => "$resChars[PlayTime]",
                'ChannelIdx' => "$resChars[ChannelIdx]",
                'Nation' => "$resChars[Nation]",
                'Login' => "$resChars[Login]"
            );
        }
        $response = array(
            "data" => $array_data
        );
        $final_data = json_encode($response, JSON_PRETTY_PRINT);
        if (file_put_contents('_data/character_data.json', $final_data)) {
            $returnSuccess = "File Appended Success fully";
        } else {
            $returnError = 'JSON File not exits';
        }
        //}  
    } else {
        $returnError = 'ข้อมูลผิดพลาด';
    }
}

?>
<?php if (isset($returnSuccess)) { ?>
    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
<?php } elseif (isset($returnWarning)) { ?>
    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
<?php } elseif (isset($returnError)) { ?>
    <div class="alert alert-danger"><?php echo $returnError; ?></div>
<?php } ?>



<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Pets <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active p-3" data-toggle="tab" href="#tab_default-1" role="tab">
                                <i class="fal fa-table text-success"></i>
                                <span class="hidden-sm-down ml-1">ตาราง ไอดี</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link p-3" data-toggle="tab" href="#tab_default-2" role="tab">
                                <i class="fal fa-cog text-info"></i>
                                <span class="hidden-sm-down ml-1">ตาราง ตัวผู้เล่น</span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content pt-4">
                        <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">

                                    <table id="dt-account-datatables"
                                        class="table table-bordered table-hover table-striped w-100"></table>
                                    <form role="form" method="post" enctype="multipart/form-data">
                                        <div class="form-group">
                                            <button type="submit" name="btn_loadaccountdata_json"
                                                class="btn btn-primary btn-lg btn-block waves-effect waves-themed">
                                                </span>ถ้าค้นหาข้อมูลไม่เจอ ให้กดปุ่มโหลดข้อมูล <code>ไอดี</code> ใหม่
                                                (คลิก!!)
                                            </button>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab_default-2" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">
                                    <table id="dt-Characterdatatables"
                                        class="table table-bordered table-hover table-striped w-100"></table>
                                    <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                                        <div class="form-group">
                                            <button type="submit" name="btn_loadcharacterdata_json"
                                                class="btn btn-primary btn-lg btn-block waves-effect waves-themed">
                                                </span>ถ้าค้นหาข้อมูลไม่เจอ ให้กดปุ่มโหลดข้อมูล <code>ตัวผู้เล่น</code>
                                                ใหม่ (คลิก!!)
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--row end -->
    </div>