# ระบบสถิติและตรวจสอบ Mail

ระบบสถิติและการตรวจสอบความเคลื่อนไหวสำหรับระบบ Mail ในเกม Cabal

## ฟีเจอร์หลัก

### 1. หน้าสถิติหลัก (mail-statistics.php)
- แสดงสถิติรวมของระบบ Mail
- จำนวนเมลล์ที่ส่ง/รับทั้งหมด
- กิจกรรมวันนี้
- สถิติเนื้อหาเมลล์ (ไอเท็ม, Alz)
- สถิติเมลล์ที่ถูกลบ
- การดำเนินการด่วน

### 2. การวิเคราะห์ (mail-analytics.php)
- กราฟกิจกรรมรายวัน
- การกระจายตามชั่วโมง
- สถานะการรับเมลล์
- ไอเท็มที่ส่งมากที่สุด
- ตัวกรองตามช่วงเวลา (7, 30, 90 วัน)

### 3. ตรวจสอบสด (mail-monitor.php)
- การตรวจสอบความเคลื่อนไหวแบบ Real-time
- การแจ้งเตือนกิจกรรมผิดปกติ
- อัพเดทอัตโนมัติ
- สถิติสด
- ประวัติกิจกรรมล่าสุด

### 4. การตั้งค่า (mail-settings.php)
- ข้อมูลระบบ
- เครื่องมือบำรุงรักษา
- แผนผังการทำงาน
- การจัดการแคช

### 5. การส่งออกข้อมูล (mail-export-manager.php)
- ส่งออกข้อมูลในรูปแบบต่างๆ (CSV, JSON, Excel)
- เลือกช่วงวันที่
- ส่งออกด่วน
- ตัวอย่างข้อมูล

## โครงสร้างไฟล์

```
files/manager_mail/
├── mail-statistics.php          # หน้าสถิติหลัก
├── mail-analytics.php           # การวิเคราะห์และกราฟ
├── mail-monitor.php             # ตรวจสอบสด
├── mail-settings.php            # การตั้งค่าระบบ
├── mail-export-manager.php      # จัดการการส่งออก
├── api/
│   └── mail-data.php           # API สำหรับข้อมูล Real-time
├── export/
│   └── mail-export.php         # ระบบส่งออกข้อมูล
└── README.md                   # เอกสารนี้
```

## ตารางฐานข้อมูลที่ใช้

### ตารางหลัก
- `cabal_mail_received_table` - เมลล์ที่รับ
- `cabal_mail_sent_table` - เมลล์ที่ส่ง

### ตาราง Log
- `cabal_mail_received_deleted_logs` - ประวัติเมลล์รับที่ถูกลบ
- `cabal_mail_sent_deleted_logs` - ประวัติเมลล์ส่งที่ถูกลบ

## การใช้งาน

### เข้าถึงระบบ
1. เข้าสู่หน้าใดหน้าหนึ่งในระบบ Mail
2. คลิกปุ่ม "สถิติ Mail" เพื่อเข้าสู่หน้าสถิติหลัก
3. ใช้เมนูด้านบนเพื่อนำทางไปยังฟีเจอร์ต่างๆ

### การตรวจสอบสด
1. เข้าสู่หน้า "ตรวจสอบสด"
2. คลิก "เริ่มอัพเดทอัตโนมัติ" เพื่อเปิดการอัพเดทแบบ Real-time
3. ระบบจะอัพเดทข้อมูลทุก 10 วินาที

### การส่งออกข้อมูล
1. เข้าสู่หน้า "ส่งออกข้อมูล"
2. เลือกประเภทข้อมูลและรูปแบบไฟล์
3. กำหนดช่วงวันที่
4. คลิก "ส่งออกข้อมูล"

## API Endpoints

### GET /files/manager_mail/api/mail-data.php

#### Parameters:
- `action` - ประเภทข้อมูล
  - `live_stats` - สถิติสด
  - `recent_activities` - กิจกรรมล่าสุด
  - `alerts` - การแจ้งเตือน
  - `hourly_stats` - สถิติรายชั่วโมง
- `limit` - จำนวนรายการ (สำหรับ recent_activities)

#### Response Format:
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

## การแจ้งเตือน

### ประเภทการแจ้งเตือน
1. **Large Alz Transfer** - การโอน Alz จำนวนมาก (> 1,000,000)
2. **High Frequency Sender** - ผู้ส่งความถี่สูง (> 10 เมลล์/ชั่วโมง)

### การตั้งค่าการแจ้งเตือน
- แก้ไขค่าใน `getMailAlerts()` function ในไฟล์ API
- ปรับเกณฑ์การแจ้งเตือนตามต้องการ

## การปรับแต่ง

### เปลี่ยนช่วงเวลาการอัพเดท
แก้ไขใน `mail-monitor.php`:
```javascript
autoRefreshInterval = setInterval(refreshData, 10000); // 10 วินาที
```

### เปลี่ยนจำนวนรายการที่แสดง
แก้ไขใน PHP files:
```php
$rowsPerPage = 1000; // จำนวนรายการต่อหน้า
```

### เพิ่มสถิติใหม่
1. เพิ่ม SQL query ใน `getMailStatistics()` function
2. เพิ่มการแสดงผลใน HTML
3. อัพเดท API หากต้องการ Real-time

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **ข้อมูลไม่อัพเดท**
   - ตรวจสอบการเชื่อมต่อฐานข้อมูล
   - ตรวจสอบ JavaScript Console สำหรับ errors

2. **การส่งออกไม่ทำงาน**
   - ตรวจสอบสิทธิ์การเข้าถึงไฟล์
   - ตรวจสอบ PHP error logs

3. **กราฟไม่แสดง**
   - ตรวจสอบการโหลด Chart.js library
   - ตรวจสอบข้อมูลใน JavaScript Console

### การ Debug
1. เปิด Browser Developer Tools
2. ตรวจสอบ Network tab สำหรับ API calls
3. ตรวจสอบ Console tab สำหรับ JavaScript errors

## การอัพเดทในอนาคต

### ฟีเจอร์ที่วางแผนไว้
- [ ] การแจ้งเตือนผ่าน Email/SMS
- [ ] Dashboard แบบ Widget
- [ ] การเปรียบเทียบข้อมูลระหว่างช่วงเวลา
- [ ] การวิเคราะห์แบบ Machine Learning
- [ ] Mobile-responsive design

### การปรับปรุง
- [ ] เพิ่มการ Cache ข้อมูล
- [ ] ปรับปรุงประสิทธิภาพ Query
- [ ] เพิ่มการ Validation ข้อมูล
- [ ] ปรับปรุง UI/UX

## การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ error logs
2. ตรวจสอบเอกสารนี้
3. ติดต่อทีมพัฒนา

---

**หมายเหตุ:** ระบบนี้ออกแบบมาสำหรับการใช้งานภายในเท่านั้น กรุณาใช้ด้วยความระมัดระวังและทำ backup ข้อมูลเป็นประจำ
