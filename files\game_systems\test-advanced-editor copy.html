<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Advanced Editor</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .btn {
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        #debugConsole {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 12px;
            line-height: 1.4;
        }

        #debugConsole::-webkit-scrollbar {
            width: 8px;
        }

        #debugConsole::-webkit-scrollbar-track {
            background: #2c3e50;
            border-radius: 4px;
        }

        #debugConsole::-webkit-scrollbar-thumb {
            background: #34495e;
            border-radius: 4px;
        }

        #debugConsole::-webkit-scrollbar-thumb:hover {
            background: #4a6741;
        }

        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .list-group-item:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-info { color: #17a2b8 !important; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-vial mr-2"></i>Test Advanced Editor</h4>
                        <small><i class="fas fa-save mr-1"></i>Auto-saves your progress - data persists after refresh</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-2"></i>การทดสอบ Advanced Editor</h6>
                            <p class="mb-0">หน้านี้สำหรับทดสอบ Advanced Editor โดยแยกจากระบบหลัก</p>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" id="testAdvancedEditorBtn" class="btn btn-warning btn-lg w-100 mb-2">
                                    <i class="fas fa-cogs mr-2"></i>Test Advanced Editor
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" id="testCalculationBtn" class="btn btn-success btn-lg w-100 mb-2">
                                    <i class="fas fa-calculator mr-2"></i>Test Calculation
                                </button>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <button type="button" id="testHexConverterBtn" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-exchange-alt mr-2"></i>Test HEX Converter
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="button" id="testBindingCodeBtn" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-link mr-2"></i>Test Binding Code
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="button" id="testNotificationBtn" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-bell mr-2"></i>Test Notification
                                </button>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" id="testFormValidationBtn" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="fas fa-check-circle mr-2"></i>Test Form Validation
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" id="testTemplateSystemBtn" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-save mr-2"></i>Test Template System
                                </button>
                            </div>
                        </div>

                        <!-- Additional Tools -->
                        <div class="row">
                            <div class="col-md-3">
                                <button type="button" id="testItemTypeAnalyzerBtn" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="fas fa-search mr-2"></i>Item Type Analyzer
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" id="testAdvancedOptionsBtn" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-cogs mr-2"></i>Test Advanced Options
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" id="testDatabaseLoadBtn" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-database mr-2"></i>Test Database Load
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" id="testConverterBtn" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-exchange-alt mr-2"></i>Dec ⇄ Hex Converter
                                </button>
                            </div>
                        </div>

                        <hr>

                        <!-- Quick Test Form -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-edit mr-2"></i>Quick Test Form</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">Item Search:</label>
                                                <input type="text" id="testItemSearch" class="form-control" placeholder="Search item name..." list="itemSearchList">
                                                <datalist id="itemSearchList">
                                                    <!-- Items will be loaded here -->
                                                </datalist>
                                                <small class="text-muted">Search and select item from database</small>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Item ID:</label>
                                                <input type="number" id="testItemId" class="form-control" value="" readonly>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Item Type:</label>
                                                <select id="testItemType" class="form-control">
                                                    <option value="Weapon" selected>Weapon</option>
                                                    <option value="Helm">Helm</option>
                                                    <option value="Suit">Suit</option>
                                                    <option value="Gloves">Gloves</option>
                                                    <option value="Boots">Boots</option>
                                                    <option value="Bike">Bike</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Upgrade:</label>
                                                <select id="testUpgrade" class="form-control">
                                                    <option value="0">+0</option>
                                                    <option value="1">+1</option>
                                                    <option value="2">+2</option>
                                                    <option value="3">+3</option>
                                                    <option value="4">+4</option>
                                                    <option value="5">+5</option>
                                                    <option value="6">+6</option>
                                                    <option value="7">+7</option>
                                                    <option value="8">+8</option>
                                                    <option value="9">+9</option>
                                                    <option value="10">+10</option>
                                                    <option value="11">+11</option>
                                                    <option value="12">+12</option>
                                                    <option value="13">+13</option>
                                                    <option value="14">+14</option>
                                                    <option value="15" selected>+15</option>
                                                    <option value="16">+16</option>
                                                    <option value="17">+17</option>
                                                    <option value="18">+18</option>
                                                    <option value="19">+19</option>
                                                    <option value="20">+20</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Extreme:</label>
                                                <select id="testExtreme" class="form-control">
                                                    <option value="0" selected>0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Divine:</label>
                                                <select id="testDivine" class="form-control">
                                                    <option value="0" selected>0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Binding:</label>
                                                <select id="testBinding" class="form-control">
                                                    <option value="none">No Binding</option>
                                                    <option value="id">Bind to ID</option>
                                                    <option value="char" selected>Bind to Character</option>
                                                    <option value="equ">Bind on Equip</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Duration:</label>
                                                <select id="testDuration" class="form-control">
                                                    <optgroup label="อายุ ชม.">
                                                        <option value="31" selected>31->ถาวร</option>
                                                        <option value="1">1->1 ชม.</option>
                                                        <option value="2">2->2 ชม.</option>
                                                        <option value="3">3->3 ชม.</option>
                                                        <option value="4">4->4 ชม.</option>
                                                        <option value="5">5->5 ชม.</option>
                                                        <option value="6">6->6 ชม.</option>
                                                        <option value="7">7->10 ชม.</option>
                                                        <option value="8">8->12 ชม</option>
                                                        <option value="9">9->1 วัน</option>
                                                        <option value="10">10->3 วัน</option>
                                                        <option value="11">11->5 วัน</option>
                                                        <option value="12">12->7 วัน</option>
                                                        <option value="13">13->10 วัน</option>
                                                        <option value="14">14->14 วัน</option>
                                                        <option value="15">15->15 วัน</option>
                                                        <option value="16">16->20 วัน</option>
                                                        <option value="17">17->30 วัน</option>
                                                        <option value="18">18->45 วัน</option>
                                                        <option value="19">19->60 วัน</option>
                                                        <option value="20">20->90 วัน</option>
                                                        <option value="21">21->100 วัน</option>
                                                        <option value="22">22->120 วัน</option>
                                                        <option value="23">23->180 วัน</option>
                                                        <option value="24">24->270 วัน</option>
                                                        <option value="25">25->365 วัน</option>
                                                        <option value="26">26->3 นาที</option>
                                                        <option value="27">27->30 นาที</option>
                                                        <option value="28">28->90 นาที</option>
                                                        <option value="29">29->10 นาที</option>
                                                        <option value="30">30->0 วัน</option>
                                                        <option value="0">ไม่มีอายุ</option>
                                                    </optgroup>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Slot 1:</label>
                                                <select id="testSlot1" class="form-control">
                                                    <option value="NOT">NOT</option>
                                                    <option value="STR+1" selected>STR+1</option>
                                                    <option value="DEX+1">DEX+1</option>
                                                    <option value="INT+1">INT+1</option>
                                                    <option value="HP+10">HP+10</option>
                                                    <option value="MP+10">MP+10</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Slot 2:</label>
                                                <select id="testSlot2" class="form-control">
                                                    <option value="NOT">NOT</option>
                                                    <option value="STR+1">STR+1</option>
                                                    <option value="DEX+1">DEX+1</option>
                                                    <option value="INT+1">INT+1</option>
                                                    <option value="HP+10" selected>HP+10</option>
                                                    <option value="MP+10">MP+10</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Slot 3:</label>
                                                <select id="testSlot3" class="form-control">
                                                    <option value="NOT" selected>NOT</option>
                                                    <option value="STR+1">STR+1</option>
                                                    <option value="DEX+1">DEX+1</option>
                                                    <option value="INT+1">INT+1</option>
                                                    <option value="HP+10">HP+10</option>
                                                    <option value="MP+10">MP+10</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Craft Options -->
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Craft Option:</label>
                                                <select id="testCraftOption" class="form-control">
                                                    <option value="NOT" selected>NOT</option>
                                                </select>
                                                <small class="text-muted">Additional craft option slot</small>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Craft Height (0-7):</label>
                                                <select id="testCraftHeight" class="form-control">
                                                    <option value="0" selected>0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                </select>
                                                <small class="text-muted">Craft option strength level</small>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-3">
                                                <button type="button" id="calculateFromFormBtn" class="btn btn-primary w-100">
                                                    <i class="fas fa-calculator mr-2"></i>Calculate from Form
                                                </button>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" id="realTimeCalculation" checked>
                                                    <label class="form-check-label" for="realTimeCalculation">
                                                        <small><i class="fas fa-sync mr-1"></i>Real-time</small>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" id="resetFormBtn" class="btn btn-outline-secondary w-100">
                                                    <i class="fas fa-undo mr-2"></i>Reset Form
                                                </button>
                                            </div>
                                            <div class="col-md-3">
                                                <button type="button" id="clearSavedDataBtn" class="btn btn-outline-danger w-100">
                                                    <i class="fas fa-trash mr-2"></i>Clear Saved Data
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Results Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-chart-bar mr-2"></i>Calculation Results</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="resultsDisplay" class="alert alert-info">
                                            <i class="fas fa-info-circle mr-2"></i>No calculations performed yet. Use the buttons above to test functions.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <h6>Debug Console:</h6>
                                <div id="debugConsole" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace;">
                                    <!-- Debug messages will appear here -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" id="clearConsoleBtn" class="btn btn-secondary btn-sm">
                                <i class="fas fa-trash mr-1"></i>Clear Console
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load ItemManager -->
    <script src="js/item-manager.js"></script>
    
    <script>
        // Custom console for debugging
        const debugConsole = document.getElementById('debugConsole');
        
        function addToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-info',
                success: 'text-success', 
                warning: 'text-warning',
                error: 'text-danger'
            };
            
            const div = document.createElement('div');
            div.className = colors[type] || 'text-light';
            div.innerHTML = `[${timestamp}] ${message}`;
            debugConsole.appendChild(div);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        // Override console methods for debugging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warning');
        };
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            addToConsole('🚀 DOM loaded, initializing test environment...', 'info');
            
            // Check required libraries
            addToConsole(`jQuery loaded: ${typeof $ !== 'undefined'}`, 'info');
            addToConsole(`SweetAlert2 loaded: ${typeof Swal !== 'undefined'}`, 'info');
            addToConsole(`ItemManager class available: ${typeof ItemManager !== 'undefined'}`, 'info');
            
            try {
                // Initialize ItemManager
                if (typeof ItemManager !== 'undefined') {
                    window.itemManager = new ItemManager();
                    addToConsole('✅ ItemManager initialized successfully', 'success');
                } else {
                    addToConsole('❌ ItemManager class not found', 'error');
                }
            } catch (error) {
                addToConsole(`❌ Error initializing ItemManager: ${error.message}`, 'error');
            }
            
            // Setup test buttons
            document.getElementById('testAdvancedEditorBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Advanced Editor...', 'info');
                
                if (window.itemManager && typeof window.itemManager.showAdvancedEditor === 'function') {
                    window.itemManager.showAdvancedEditor();
                    addToConsole('✅ Advanced Editor called', 'success');
                } else {
                    addToConsole('❌ ItemManager or showAdvancedEditor not available', 'error');
                }
            });
            
            document.getElementById('testCalculationBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing calculation...', 'info');

                if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
                    const testData = {
                        itemId: 1000,
                        upgrade: 15,
                        extreme: 7,
                        divine: 10,
                        slot1: 'STR+1',
                        slot2: 'HP+10',
                        slot3: 'NOT'
                    };

                    const result = window.itemManager.generateCompleteItem(testData);
                    addToConsole(`✅ Calculation result: ${JSON.stringify(result)}`, 'success');
                } else {
                    addToConsole('❌ ItemManager or generateCompleteItem not available', 'error');
                }
            });

            // Test HEX Converter
            document.getElementById('testHexConverterBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing HEX Converter...', 'info');

                if (window.itemManager && typeof window.itemManager.convertHexToDec === 'function') {
                    const testCases = [
                        { hex: 'FF', expected: 255 },
                        { hex: '1000', expected: 4096 },
                        { hex: 'ABCD', expected: 43981 },
                        { hex: '0x10', expected: 16 }
                    ];

                    let allPassed = true;
                    const results = [];

                    testCases.forEach(test => {
                        const decValue = window.itemManager.convertHexToDec(test.hex);
                        const passed = decValue === test.expected;
                        allPassed = allPassed && passed;

                        results.push(`HEX ${test.hex} = DEC ${decValue} ${passed ? '✅' : '❌'}`);
                        addToConsole(`HEX ${test.hex} = DEC ${decValue} (expected: ${test.expected}) ${passed ? '✅' : '❌'}`, passed ? 'success' : 'error');
                    });

                    // Test reverse conversion
                    const decToHex = window.itemManager.convertDecToHex(255);
                    results.push(`DEC 255 = HEX ${decToHex}`);
                    addToConsole(`DEC 255 = HEX ${decToHex}`, 'success');

                    // Update results display
                    const resultsDiv = document.getElementById('resultsDisplay');
                    resultsDiv.className = allPassed ? 'alert alert-success' : 'alert alert-warning';
                    resultsDiv.innerHTML = `
                        <h6><i class="fas fa-exchange-alt mr-2"></i>HEX Converter Test Results</h6>
                        ${results.map(r => `<p class="mb-1">${r}</p>`).join('')}
                        <hr>
                        <small class="text-muted">All tests ${allPassed ? 'passed' : 'completed with some failures'}</small>
                    `;
                } else {
                    addToConsole('❌ HEX converter functions not available', 'error');
                }
            });

            // Test Binding Code
            document.getElementById('testBindingCodeBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Binding Code...', 'info');

                if (window.itemManager && typeof window.itemManager.calculateBindingCode === 'function') {
                    const bindingTypes = [
                        { type: 'id', expected: 4096, description: 'Bind to Account ID' },
                        { type: 'char', expected: 524288, description: 'Bind to Character (Corrected)' },
                        { type: 'equ', expected: 1572864, description: 'Bind on Equip' }
                    ];

                    const results = [];
                    let allPassed = true;

                    bindingTypes.forEach(binding => {
                        const code = window.itemManager.calculateBindingCode(binding.type);
                        const passed = code === binding.expected;
                        allPassed = allPassed && passed;

                        results.push({
                            type: binding.type,
                            description: binding.description,
                            code: code,
                            expected: binding.expected,
                            passed: passed
                        });

                        addToConsole(`Binding ${binding.type}: ${code} (${binding.description}) ${passed ? '✅' : '❌'}`, passed ? 'success' : 'error');
                    });

                    // Update results display
                    const resultsDiv = document.getElementById('resultsDisplay');
                    resultsDiv.className = allPassed ? 'alert alert-success' : 'alert alert-danger';
                    resultsDiv.innerHTML = `
                        <h6><i class="fas fa-link mr-2"></i>Binding Code Test Results</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Code</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${results.map(r => `
                                        <tr class="${r.passed ? 'table-success' : 'table-danger'}">
                                            <td><code>${r.type}</code></td>
                                            <td>${r.description}</td>
                                            <td><strong>${r.code}</strong></td>
                                            <td>${r.passed ? '✅ Pass' : '❌ Fail'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <small class="text-muted">All binding codes ${allPassed ? 'match expected values' : 'have some mismatches'}</small>
                    `;
                } else {
                    addToConsole('❌ Binding code function not available', 'error');
                }
            });

            // Test Notification
            document.getElementById('testNotificationBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Notification System...', 'info');

                if (window.itemManager && typeof window.itemManager.showNotification === 'function') {
                    window.itemManager.showNotification('ทดสอบการแจ้งเตือน', 'success');
                    addToConsole('✅ Success notification sent', 'success');

                    setTimeout(() => {
                        window.itemManager.showNotification('ทดสอบคำเตือน', 'warning');
                        addToConsole('⚠️ Warning notification sent', 'warning');
                    }, 1000);

                    setTimeout(() => {
                        window.itemManager.showNotification('ทดสอบข้อผิดพลาด', 'error');
                        addToConsole('❌ Error notification sent', 'error');
                    }, 2000);
                } else {
                    addToConsole('❌ Notification function not available', 'error');
                }
            });

            // Test Form Validation
            document.getElementById('testFormValidationBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Form Validation...', 'info');

                if (window.itemManager && typeof window.itemManager.validateForm === 'function') {
                    // Create a mock form for testing
                    const mockFormData = {
                        userId: '12345',
                        itemCode: '100',
                        option: '0',
                        duration: '0',
                        upgrade: '0',
                        quantity: '1',
                        slot: '0'
                    };

                    // Test validation
                    const isValid = window.itemManager.validateFormData(mockFormData);
                    addToConsole(`Form validation result: ${isValid ? 'VALID' : 'INVALID'}`, isValid ? 'success' : 'error');
                } else {
                    addToConsole('❌ Form validation function not available', 'error');
                }
            });

            // Test Template System
            document.getElementById('testTemplateSystemBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Template System...', 'info');

                if (window.itemManager && typeof window.itemManager.showTemplateDialog === 'function') {
                    window.itemManager.showTemplateDialog();
                    addToConsole('✅ Template dialog opened', 'success');
                } else {
                    addToConsole('❌ Template system not available', 'error');
                }
            });

            // Test Item Type Analyzer
            document.getElementById('testItemTypeAnalyzerBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Item Type Analyzer...', 'info');

                if (window.itemManager && typeof window.itemManager.showItemTypeAnalyzer === 'function') {
                    window.itemManager.showItemTypeAnalyzer();
                    addToConsole('✅ Item Type Analyzer opened', 'success');
                } else {
                    addToConsole('❌ Item Type Analyzer not available', 'error');
                }
            });

            // Dec ⇄ Hex Converter
            document.getElementById('testConverterBtn').addEventListener('click', function() {
                addToConsole('🔄 Opening Dec ⇄ Hex Converter...', 'info');
                showConverter();
            });

            // Test Database Load
            document.getElementById('testDatabaseLoadBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Database Load...', 'info');

                // Force reload database
                loadItemDatabase().then(database => {
                    if (database.length > 0) {
                        addToConsole(`✅ Database test successful: ${database.length} items loaded`, 'success');

                        // Show some sample items
                        const samples = database.slice(0, 5);
                        samples.forEach(item => {
                            addToConsole(`📦 Sample: ${item.name} (ID: ${item.id})`, 'info');
                        });

                        // Update results display
                        const resultsDiv = document.getElementById('resultsDisplay');
                        resultsDiv.className = 'alert alert-success';
                        resultsDiv.innerHTML = `
                            <h6><i class="fas fa-database mr-2"></i>Database Load Test Results</h6>
                            <p><strong>Total Items:</strong> ${database.length}</p>
                            <p><strong>Sample Items:</strong></p>
                            <ul class="mb-0">
                                ${samples.map(item => `<li>${item.name} (ID: ${item.id})</li>`).join('')}
                            </ul>
                            <hr>
                            <small class="text-muted">Database loaded successfully from item_fixed.dec</small>
                        `;
                    } else {
                        addToConsole('❌ Database test failed: No items loaded', 'error');
                    }
                });
            });

            // Test Advanced Options
            document.getElementById('testAdvancedOptionsBtn').addEventListener('click', function() {
                addToConsole('🧪 Testing Advanced Options Calculation...', 'info');

                if (window.itemManager && typeof window.itemManager.calculateAdvancedOptionsCode === 'function') {
                    const testItems = [
                        { itemType: 'Weapon', slot1: 'ATTACK', slot2: 'CRIT RATE', slot3: 'NOT' },
                        { itemType: 'Helm', slot1: 'MP', slot2: 'DEF', slot3: 'CRIT DAMAGE' },
                        { itemType: 'Suit', slot1: 'HP', slot2: 'DEF RATE', slot3: 'HP AUTO HEAL' },
                        { itemType: 'Gloves', slot1: 'ATTACK RATE', slot2: 'HP STEAL', slot3: 'NOT' },
                        { itemType: 'Boots', slot1: 'DEF', slot2: 'FLEE RATE', slot3: 'NOT' }
                    ];

                    const results = [];
                    testItems.forEach((item, index) => {
                        const result = window.itemManager.calculateAdvancedOptionsCode(item);
                        results.push({
                            name: `Test Item ${index + 1}`,
                            type: result.itemType,
                            optionsCode: result.optionsCode,
                            slots: result.slotDetails
                        });

                        addToConsole(`${item.itemType} (${item.slot1}, ${item.slot2}, ${item.slot3}): Options=${result.optionsCode}`, 'success');
                    });

                    // Update results display
                    const resultsDiv = document.getElementById('resultsDisplay');
                    resultsDiv.className = 'alert alert-success';
                    resultsDiv.innerHTML = `
                        <h6><i class="fas fa-cogs mr-2"></i>Advanced Options Test Results</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Item Name</th>
                                        <th>Detected Type</th>
                                        <th>Options Code</th>
                                        <th>Slot Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${results.map(r => `
                                        <tr>
                                            <td><strong>${r.name}</strong></td>
                                            <td><span class="badge badge-info">${r.type}</span></td>
                                            <td><code>${r.optionsCode}</code></td>
                                            <td>
                                                <small>
                                                    ${r.slots.slot1.name}(${r.slots.slot1.hex}),
                                                    ${r.slots.slot2.name}(${r.slots.slot2.hex}),
                                                    ${r.slots.slot3.name}(${r.slots.slot3.hex})
                                                </small>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <small class="text-muted">All ${results.length} items tested successfully</small>
                    `;
                } else {
                    addToConsole('❌ Advanced Options function not available', 'error');
                }
            });
            
            document.getElementById('clearConsoleBtn').addEventListener('click', function() {
                debugConsole.innerHTML = '';
                addToConsole('Console cleared', 'info');
            });

            // Calculate from form
            document.getElementById('calculateFromFormBtn').addEventListener('click', function() {
                addToConsole('🧪 Calculating from form data...', 'info');

                const itemSearch = document.getElementById('testItemSearch').value;
                const itemType = document.getElementById('testItemType').value;
                const bindingType = document.getElementById('testBinding').value;
                const duration = parseInt(document.getElementById('testDuration').value);

                const formData = {
                    itemName: itemSearch,
                    itemType: itemType,
                    itemId: parseInt(document.getElementById('testItemId').value),
                    upgrade: parseInt(document.getElementById('testUpgrade').value),
                    extreme: parseInt(document.getElementById('testExtreme').value),
                    divine: parseInt(document.getElementById('testDivine').value),
                    slot1: document.getElementById('testSlot1').value,
                    slot2: document.getElementById('testSlot2').value,
                    slot3: document.getElementById('testSlot3').value,
                    craftOption: document.getElementById('testCraftOption').value,
                    craftHeight: parseInt(document.getElementById('testCraftHeight').value),
                    binding: bindingType,
                    duration: duration
                };

                addToConsole(`🔍 Selected item type: ${itemType}`, 'info');

                if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
                    const result = window.itemManager.generateCompleteItem(formData);

                    // Calculate binding code if needed
                    let bindingCode = 0;
                    let finalItemCode = result.itemCode;

                    if (bindingType !== 'none' && window.itemManager.calculateBindingCode) {
                        bindingCode = window.itemManager.calculateBindingCode(bindingType);
                        finalItemCode = result.itemCode + bindingCode;
                        addToConsole(`🔗 Binding applied: ${bindingType} (+${bindingCode})`, 'info');
                    }

                    // Display results
                    const resultsDiv = document.getElementById('resultsDisplay');
                    if (result.success) {
                        resultsDiv.className = 'alert alert-success';
                        resultsDiv.innerHTML = `
                            <h6><i class="fas fa-check-circle mr-2"></i>Complete Item Calculation</h6>
                            <div class="alert alert-info mb-3">
                                <strong>Item:</strong> ${formData.itemName}
                                <span class="badge badge-info ml-2">${formData.itemType}</span>
                                <span class="badge badge-secondary ml-1">ID: ${formData.itemId}</span>
                            </div>

                            <!-- Calculation Steps -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator mr-2"></i>Calculation Steps</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Step 1: Base Item Code</h6>
                                            <p class="mb-1"><strong>Item ID:</strong> ${formData.itemId}</p>
                                            <p class="mb-1"><strong>Upgrade:</strong> +${formData.upgrade}</p>
                                            <p class="mb-1"><strong>Extreme:</strong> ${formData.extreme}</p>
                                            <p class="mb-1"><strong>Divine:</strong> ${formData.divine}</p>
                                            <p class="mb-3"><strong>Result:</strong> <span class="badge badge-primary">${result.baseItemCode}</span></p>

                                            <h6 class="text-success">Step 2: Add Binding</h6>
                                            <p class="mb-1"><strong>Binding Type:</strong> ${result.binding.toUpperCase()}</p>
                                            <p class="mb-1"><strong>Binding Code:</strong> +${result.bindingCode}</p>
                                            <p class="mb-3"><strong>Final Item Code:</strong> <span class="badge badge-success">${result.itemCode}</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-warning">Step 3: Options Code (Separate)</h6>
                                            <p class="mb-1"><strong>Item Type:</strong> ${formData.itemType}</p>
                                            <p class="mb-1"><strong>Slot 1:</strong> ${formData.slot1}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot1?.hex || '0'}, Dec: ${result.slotDetails?.slot1?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Slot 2:</strong> ${formData.slot2}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot2?.hex || '0'}, Dec: ${result.slotDetails?.slot2?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Slot 3:</strong> ${formData.slot3}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot3?.hex || '0'}, Dec: ${result.slotDetails?.slot3?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Craft Option:</strong> ${formData.craftOption} (Height: ${formData.craftHeight})
                                                <small class="text-secondary">(Craft Code: ${result.slotDetails?.craft?.hex || '00'})</small>
                                            </p>
                                            <p class="mb-3"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>

                                            <h6 class="text-info">Additional Info</h6>
                                            <p class="mb-1"><strong>Duration:</strong> ${result.duration} days</p>
                                            <p class="mb-0"><strong>Calculation Order:</strong> Item Code + Binding → Options Code</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Final Results -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white py-2">
                                            <h6 class="mb-0">Final Decimal Values</h6>
                                        </div>
                                        <div class="card-body py-2 bg-light">
                                            <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                            <p class="mb-1 text-dark"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                                            <small class="text-secondary">Use these values in your game system</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white py-2">
                                            <h6 class="mb-0">Final Hexadecimal Values</h6>
                                        </div>
                                        <div class="card-body py-2 bg-light">
                                            <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                                            <p class="mb-1 text-dark"><strong>Options Code:</strong> <span class="badge badge-info">${result.hexOptionsCode}</span></p>
                                            <small class="text-secondary">Hex representation for debugging</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        addToConsole(`✅ Complete calculation: FinalItemCode=${finalItemCode}, OptionsCode=${result.optionsCode}, Binding=${bindingType}`, 'success');
                    } else {
                        resultsDiv.className = 'alert alert-danger';
                        resultsDiv.innerHTML = `
                            <h6><i class="fas fa-exclamation-triangle mr-2"></i>Calculation Failed</h6>
                            <p>Error: ${result.error}</p>
                        `;
                        addToConsole(`❌ Calculation failed: ${result.error}`, 'error');
                    }
                } else {
                    addToConsole('❌ ItemManager or generateCompleteItem not available', 'error');
                }
            });

            // Clear saved data
            document.getElementById('clearSavedDataBtn').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all saved form data? This action cannot be undone.')) {
                    clearSavedData();
                    addToConsole('🗑️ All saved form data has been cleared', 'warning');
                }
            });

            // Reset form
            document.getElementById('resetFormBtn').addEventListener('click', function() {
                document.getElementById('testItemSearch').value = 'Sample Battlehelm(Lv.50)';
                document.getElementById('testItemId').value = '';
                document.getElementById('testItemType').value = 'Helm';
                document.getElementById('testUpgrade').value = '15';
                document.getElementById('testExtreme').value = '7';
                document.getElementById('testDivine').value = '10';
                document.getElementById('testBinding').value = 'char';
                document.getElementById('testDuration').value = '31';
                document.getElementById('testSlot1').value = 'MP';
                document.getElementById('testSlot2').value = 'DEF';
                document.getElementById('testSlot3').value = 'NOT';
                document.getElementById('testCraftOption').value = 'NOT';
                document.getElementById('testCraftHeight').value = '0';

                // Update slot options for the reset item
                if (typeof updateSlotOptions === 'function') {
                    updateSlotOptions();
                }

                const resultsDiv = document.getElementById('resultsDisplay');
                resultsDiv.className = 'alert alert-info';
                resultsDiv.innerHTML = `
                    <h6><i class="fas fa-info-circle mr-2"></i>Form Reset Complete</h6>
                    <p class="mb-0">All fields have been reset to default values:</p>
                    <small class="text-muted">
                        Item: Sample Battlehelm(Lv.50) (Helm)<br>
                        Item ID: 1000, Upgrade: +15, Extreme: 7, Divine: 10<br>
                        Binding: Character, Duration: 31 days<br>
                        Slots: MP, DEF, NOT, Craft: NOT (Height: 0)
                    </small>
                `;

                addToConsole('🔄 Form reset to default values with binding settings', 'info');
            });

            // Global variable to store item data
            let itemDatabase = [];

            // LocalStorage key for saving form data
            const STORAGE_KEY = 'cabal_item_editor_data';

            // Real-time calculation state
            let isRealTimeEnabled = true;
            let realTimeTimeout = null;

            // Save form data to localStorage
            function saveFormData() {
                try {
                    const formData = {
                        itemSearch: document.getElementById('testItemSearch')?.value || '',
                        itemId: document.getElementById('testItemId')?.value || '',
                        itemType: document.getElementById('testItemType')?.value || 'Weapon',
                        upgrade: document.getElementById('testUpgrade')?.value || '0',
                        extreme: document.getElementById('testExtreme')?.value || '0',
                        divine: document.getElementById('testDivine')?.value || '0',
                        binding: document.getElementById('testBinding')?.value || 'none',
                        duration: document.getElementById('testDuration')?.value || '31',
                        slot1: document.getElementById('testSlot1')?.value || 'NOT',
                        slot2: document.getElementById('testSlot2')?.value || 'NOT',
                        slot3: document.getElementById('testSlot3')?.value || 'NOT',
                        craftOption: document.getElementById('testCraftOption')?.value || 'NOT',
                        craftHeight: document.getElementById('testCraftHeight')?.value || '0',
                        timestamp: new Date().toISOString()
                    };

                    localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
                    console.log('💾 Form data saved to localStorage');
                } catch (error) {
                    console.error('❌ Error saving form data:', error);
                }
            }

            // Load form data from localStorage
            function loadFormData() {
                try {
                    const savedData = localStorage.getItem(STORAGE_KEY);
                    if (!savedData) {
                        console.log('📝 No saved form data found');
                        return false;
                    }

                    const formData = JSON.parse(savedData);
                    console.log('📂 Loading saved form data:', formData);

                    // Restore form values
                    if (document.getElementById('testItemSearch')) document.getElementById('testItemSearch').value = formData.itemSearch || '';
                    if (document.getElementById('testItemId')) document.getElementById('testItemId').value = formData.itemId || '';
                    if (document.getElementById('testItemType')) document.getElementById('testItemType').value = formData.itemType || 'Weapon';
                    if (document.getElementById('testUpgrade')) document.getElementById('testUpgrade').value = formData.upgrade || '0';
                    if (document.getElementById('testExtreme')) document.getElementById('testExtreme').value = formData.extreme || '0';
                    if (document.getElementById('testDivine')) document.getElementById('testDivine').value = formData.divine || '0';
                    if (document.getElementById('testBinding')) document.getElementById('testBinding').value = formData.binding || 'none';
                    if (document.getElementById('testDuration')) document.getElementById('testDuration').value = formData.duration || '31';
                    if (document.getElementById('testSlot1')) document.getElementById('testSlot1').value = formData.slot1 || 'NOT';
                    if (document.getElementById('testSlot2')) document.getElementById('testSlot2').value = formData.slot2 || 'NOT';
                    if (document.getElementById('testSlot3')) document.getElementById('testSlot3').value = formData.slot3 || 'NOT';
                    if (document.getElementById('testCraftOption')) document.getElementById('testCraftOption').value = formData.craftOption || 'NOT';
                    if (document.getElementById('testCraftHeight')) document.getElementById('testCraftHeight').value = formData.craftHeight || '0';

                    addToConsole(`✅ Form data restored from ${formData.timestamp}`, 'success');
                    return true;
                } catch (error) {
                    console.error('❌ Error loading form data:', error);
                    return false;
                }
            }

            // Clear saved form data
            function clearSavedData() {
                try {
                    localStorage.removeItem(STORAGE_KEY);
                    addToConsole('🗑️ Saved form data cleared', 'info');
                } catch (error) {
                    console.error('❌ Error clearing saved data:', error);
                }
            }

            // Auto-save form data when inputs change
            function setupAutoSave() {
                const formInputs = [
                    'testItemSearch', 'testItemId', 'testItemType', 'testUpgrade',
                    'testExtreme', 'testDivine', 'testBinding', 'testDuration',
                    'testSlot1', 'testSlot2', 'testSlot3', 'testCraftOption', 'testCraftHeight'
                ];

                formInputs.forEach(inputId => {
                    const element = document.getElementById(inputId);
                    if (element) {
                        element.addEventListener('change', saveFormData);
                        element.addEventListener('input', debounce(saveFormData, 1000)); // Save after 1 second of no typing
                    }
                });

                console.log('🔄 Auto-save setup complete');
            }

            // Setup real-time calculation listeners
            function setupRealTimeCalculation() {
                const formInputs = [
                    'testItemSearch', 'testItemId', 'testItemType', 'testUpgrade',
                    'testExtreme', 'testDivine', 'testBinding', 'testDuration',
                    'testSlot1', 'testSlot2', 'testSlot3', 'testCraftOption', 'testCraftHeight'
                ];

                formInputs.forEach(inputId => {
                    const element = document.getElementById(inputId);
                    if (element) {
                        element.addEventListener('change', performRealTimeCalculation);
                        element.addEventListener('input', performRealTimeCalculation);
                    }
                });

                // Setup real-time toggle
                const realTimeCheckbox = document.getElementById('realTimeCalculation');
                if (realTimeCheckbox) {
                    realTimeCheckbox.addEventListener('change', function() {
                        isRealTimeEnabled = this.checked;
                        if (isRealTimeEnabled) {
                            addToConsole('✅ Real-time calculation enabled', 'success');
                            performRealTimeCalculation(); // Trigger immediate calculation
                        } else {
                            addToConsole('⏸️ Real-time calculation disabled', 'warning');
                            // Clear timeout
                            if (realTimeTimeout) {
                                clearTimeout(realTimeTimeout);
                            }
                        }
                    });
                }

                console.log('🔄 Real-time calculation setup complete');
            }

            // Debounce function to limit auto-save frequency
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Real-time calculation function
            function performRealTimeCalculation() {
                if (!isRealTimeEnabled) return;

                // Clear previous timeout
                if (realTimeTimeout) {
                    clearTimeout(realTimeTimeout);
                }

                // Set new timeout for debounced calculation
                realTimeTimeout = setTimeout(() => {
                    try {
                        addToConsole('🔄 Real-time calculation triggered...', 'info');

                        // Get current form data
                        const itemSearch = document.getElementById('testItemSearch')?.value || '';
                        const itemType = document.getElementById('testItemType')?.value || 'Weapon';
                        const bindingType = document.getElementById('testBinding')?.value || 'none';
                        const duration = parseInt(document.getElementById('testDuration')?.value || '31');

                        const formData = {
                            itemName: itemSearch,
                            itemType: itemType,
                            itemId: parseInt(document.getElementById('testItemId')?.value || '1000'),
                            upgrade: parseInt(document.getElementById('testUpgrade')?.value || '0'),
                            extreme: parseInt(document.getElementById('testExtreme')?.value || '0'),
                            divine: parseInt(document.getElementById('testDivine')?.value || '0'),
                            slot1: document.getElementById('testSlot1')?.value || 'NOT',
                            slot2: document.getElementById('testSlot2')?.value || 'NOT',
                            slot3: document.getElementById('testSlot3')?.value || 'NOT',
                            craftOption: document.getElementById('testCraftOption')?.value || 'NOT',
                            craftHeight: parseInt(document.getElementById('testCraftHeight')?.value || '0'),
                            binding: bindingType,
                            duration: duration
                        };

                        // Perform calculation
                        if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
                            const result = window.itemManager.generateCompleteItem(formData);

                            // Update results display
                            updateRealTimeResults(result, formData);
                        } else {
                            addToConsole('❌ ItemManager not available for real-time calculation', 'error');
                        }
                    } catch (error) {
                        addToConsole(`❌ Real-time calculation error: ${error.message}`, 'error');
                        console.error('Real-time calculation error:', error);
                    }
                }, 500); // 500ms delay for debouncing
            }

            // Update real-time results display
            function updateRealTimeResults(result, formData) {
                console.log('🔍 updateRealTimeResults called with:', result);
                console.log('🔍 slotDetails:', result.slotDetails);

                if (!result.success) {
                    const resultsDiv = document.getElementById('resultsDisplay');
                    resultsDiv.className = 'alert alert-danger';
                    resultsDiv.innerHTML = `
                        <h6><i class="fas fa-exclamation-triangle mr-2"></i>Real-time Calculation Error</h6>
                        <p>${result.error}</p>
                    `;
                    return;
                }

                // Display results with real-time indicator
                const resultsDiv = document.getElementById('resultsDisplay');
                resultsDiv.className = 'alert alert-success';
                resultsDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6><i class="fas fa-sync mr-2"></i>Real-time Item Calculation</h6>
                        <small class="text-muted"><i class="fas fa-clock mr-1"></i>Updated: ${new Date().toLocaleTimeString()}</small>
                    </div>

                    <div class="alert alert-info mb-3">
                        <strong>Item:</strong> ${formData.itemName}
                        <span class="badge badge-info ml-2">${formData.itemType}</span>
                        <span class="badge badge-secondary ml-1">ID: ${formData.itemId}</span>
                    </div>

                    <!-- Quick Results -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white py-2">
                                    <h6 class="mb-0">Final Results</h6>
                                </div>
                                <div class="card-body py-2 bg-light">
                                    <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                    <p class="mb-0 text-dark"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white py-2">
                                    <h6 class="mb-0">Hex Values</h6>
                                </div>
                                <div class="card-body py-2 bg-light">
                                    <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                                    <p class="mb-0 text-dark"><strong>Options Code:</strong> <span class="badge badge-info">${result.hexOptionsCode}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slot Details -->
                    <div class="row mb-2">
                        <div class="col-md-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white py-1">
                                    <h6 class="mb-0"><small>Slot Details</small></h6>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 1:</strong> ${formData.slot1}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot1?.hex || '0'}, Dec: ${result.slotDetails?.slot1?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 2:</strong> ${formData.slot2}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot2?.hex || '0'}, Dec: ${result.slotDetails?.slot2?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 3:</strong> ${formData.slot3}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot3?.hex || '0'}, Dec: ${result.slotDetails?.slot3?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Craft:</strong> ${formData.craftOption} (H:${formData.craftHeight})<br>
                                            <span class="text-secondary">Code: ${result.slotDetails?.craft?.hex || '00'}</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calculation Summary -->
                    <div class="row">
                        <div class="col-md-12">
                            <small class="text-secondary">
                                <strong>Calculation:</strong>
                                Base: ${result.baseItemCode} +
                                Binding: ${result.bindingCode} (${result.binding.toUpperCase()}) =
                                <strong class="text-dark">${result.itemCode}</strong> |
                                Options: <strong class="text-dark">${result.optionsCode}</strong>
                            </small>
                        </div>
                    </div>
                `;

                addToConsole(`✅ Real-time calculation complete: Item=${result.itemCode}, Options=${result.optionsCode}`, 'success');
            }

            // Load item data from item_fixed.dec
            async function loadItemDatabase() {
                try {
                    addToConsole('📥 Loading item database...', 'info');

                    // Try multiple possible paths
                    const possiblePaths = [
                        'files/game_systems/import/item_fixed.dec',
                        '../files/game_systems/import/item_fixed.dec',
                        './import/item_fixed.dec',
                        'import/item_fixed.dec'
                    ];

                    let response = null;
                    let usedPath = '';

                    for (const path of possiblePaths) {
                        try {
                            addToConsole(`🔍 Trying path: ${path}`, 'info');
                            response = await fetch(path);
                            if (response.ok) {
                                usedPath = path;
                                addToConsole(`✅ Found file at: ${path}`, 'success');
                                break;
                            }
                        } catch (e) {
                            addToConsole(`❌ Failed to load from: ${path}`, 'warning');
                        }
                    }

                    if (!response || !response.ok) {
                        throw new Error(`Could not find item_fixed.dec in any of the expected locations`);
                    }

                    const text = await response.text();
                    addToConsole(`📄 File loaded successfully (${text.length} characters)`, 'info');

                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(text, 'application/xml');

                    // Check for parsing errors
                    const parseError = xmlDoc.getElementsByTagName('parsererror');
                    if (parseError.length > 0) {
                        throw new Error('XML parsing error: ' + parseError[0].textContent);
                    }

                    const items = xmlDoc.getElementsByTagName('msg');
                    addToConsole(`🔍 Found ${items.length} items in XML`, 'info');

                    itemDatabase = [];
                    for (let i = 0; i < items.length; i++) {
                        const id = items[i].getAttribute('id');
                        const name = items[i].getAttribute('cont');

                        if (id && name) {
                            const numericId = id.replace('item', '');
                            itemDatabase.push({
                                id: parseInt(numericId),
                                name: name,
                                fullId: id
                            });
                        }
                    }

                    // Sort by ID for better organization
                    itemDatabase.sort((a, b) => a.id - b.id);

                    // Populate datalist
                    const datalist = document.getElementById('itemSearchList');
                    if (datalist) {
                        datalist.innerHTML = '';
                        itemDatabase.forEach(item => {
                            const option = document.createElement('option');
                            option.value = item.name;
                            option.setAttribute('data-id', item.id);
                            option.textContent = `${item.name} (ID: ${item.id})`;
                            datalist.appendChild(option);
                        });
                        addToConsole(`📋 Populated datalist with ${itemDatabase.length} options`, 'success');
                    } else {
                        addToConsole('⚠️ Datalist element not found', 'warning');
                    }

                    addToConsole(`✅ Successfully loaded ${itemDatabase.length} items from ${usedPath}`, 'success');
                    return itemDatabase;
                } catch (error) {
                    addToConsole(`❌ Error loading item database: ${error.message}`, 'error');
                    console.error('Full error:', error);
                    return [];
                }
            }

            // Handle item search selection
            function handleItemSelection() {
                const searchInput = document.getElementById('testItemSearch');
                const itemIdInput = document.getElementById('testItemId');
                const selectedItemName = searchInput.value.trim();

                if (!selectedItemName) {
                    addToConsole('⚠️ No item name entered', 'warning');
                    return;
                }

                addToConsole(`🔍 Searching for item: "${selectedItemName}"`, 'info');

                // Find exact match first
                let selectedItem = itemDatabase.find(item => item.name === selectedItemName);

                // If no exact match, try partial match
                if (!selectedItem) {
                    selectedItem = itemDatabase.find(item =>
                        item.name.toLowerCase().includes(selectedItemName.toLowerCase())
                    );
                }

                if (selectedItem) {
                    itemIdInput.value = selectedItem.id;
                    addToConsole(`✅ Selected item: ${selectedItem.name} (ID: ${selectedItem.id})`, 'success');

                    // Update search input to exact name if it was a partial match
                    if (searchInput.value !== selectedItem.name) {
                        searchInput.value = selectedItem.name;
                    }
                } else {
                    addToConsole(`❌ Item not found: "${selectedItemName}"`, 'error');
                    addToConsole(`💡 Try searching with partial name or check spelling`, 'info');

                    // Show some suggestions
                    const suggestions = itemDatabase
                        .filter(item => item.name.toLowerCase().includes(selectedItemName.toLowerCase().substring(0, 3)))
                        .slice(0, 5)
                        .map(item => item.name);

                    if (suggestions.length > 0) {
                        addToConsole(`💡 Suggestions: ${suggestions.join(', ')}`, 'info');
                    }
                }
            }

            // Search items by partial name
            function searchItems(query) {
                if (!query || query.length < 2) return [];

                const lowerQuery = query.toLowerCase();
                return itemDatabase
                    .filter(item => item.name.toLowerCase().includes(lowerQuery))
                    .slice(0, 10); // Limit to 10 results
            }

            // Show Dec ⇄ Hex Converter
            function showConverter() {
                const converterHTML = `
                    <div class="converter-tool">
                        <h5><i class="fas fa-exchange-alt mr-2"></i>Decimal ⇄ Hexadecimal Converter</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">Decimal to Hexadecimal</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Decimal Value:</label>
                                            <input type="number" id="decInput" class="form-control" placeholder="Enter decimal number..." min="0">
                                        </div>
                                        <div class="form-group">
                                            <label>Hexadecimal Result:</label>
                                            <input type="text" id="hexOutput" class="form-control" readonly placeholder="Hex result will appear here">
                                        </div>
                                        <button type="button" id="convertDecToHex" class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right mr-1"></i>Convert Dec → Hex
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">Hexadecimal to Decimal</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Hexadecimal Value:</label>
                                            <input type="text" id="hexInput" class="form-control" placeholder="Enter hex number (e.g., FF, 1A2B)" style="text-transform: uppercase;">
                                        </div>
                                        <div class="form-group">
                                            <label>Decimal Result:</label>
                                            <input type="text" id="decOutput" class="form-control" readonly placeholder="Decimal result will appear here">
                                        </div>
                                        <button type="button" id="convertHexToDec" class="btn btn-success btn-sm">
                                            <i class="fas fa-arrow-left mr-1"></i>Convert Hex → Dec
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Quick Reference</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Common Values:</h6>
                                                <small class="text-muted">
                                                    • 0 = 0x0<br>
                                                    • 255 = 0xFF<br>
                                                    • 256 = 0x100<br>
                                                    • 4096 = 0x1000<br>
                                                    • 65536 = 0x10000
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Binding Codes:</h6>
                                                <small class="text-muted">
                                                    • Account ID: 4096 = 0x1000<br>
                                                    • Character: 524288 = 0x80000<br>
                                                    • Bind on Equip: 1572864 = 0x180000
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div id="converterResults" class="alert alert-light" style="display: none;">
                                <!-- Conversion results will appear here -->
                            </div>
                        </div>
                    </div>
                `;

                // Update results display
                const resultsDiv = document.getElementById('resultsDisplay');
                resultsDiv.className = 'alert alert-info';
                resultsDiv.innerHTML = converterHTML;

                // Set up event listeners
                setupConverterListeners();

                addToConsole('✅ Dec ⇄ Hex Converter opened', 'success');
            }

            // Setup converter event listeners
            function setupConverterListeners() {
                // Decimal to Hex conversion
                const decInput = document.getElementById('decInput');
                const hexOutput = document.getElementById('hexOutput');
                const convertDecToHexBtn = document.getElementById('convertDecToHex');

                function convertDecToHex() {
                    const decValue = parseInt(decInput.value);
                    if (isNaN(decValue) || decValue < 0) {
                        hexOutput.value = '';
                        showConverterResult('❌ Please enter a valid positive decimal number', 'error');
                        return;
                    }

                    const hexValue = decValue.toString(16).toUpperCase();
                    hexOutput.value = hexValue;

                    showConverterResult(`✅ ${decValue} (decimal) = ${hexValue} (hex) = 0x${hexValue}`, 'success');
                    addToConsole(`🔄 Converted: ${decValue} → 0x${hexValue}`, 'info');
                }

                // Hex to Decimal conversion
                const hexInput = document.getElementById('hexInput');
                const decOutput = document.getElementById('decOutput');
                const convertHexToDecBtn = document.getElementById('convertHexToDec');

                function convertHexToDec() {
                    let hexValue = hexInput.value.trim().toUpperCase();

                    // Remove 0x prefix if present
                    if (hexValue.startsWith('0X')) {
                        hexValue = hexValue.substring(2);
                    }

                    // Validate hex input
                    if (!/^[0-9A-F]+$/.test(hexValue)) {
                        decOutput.value = '';
                        showConverterResult('❌ Please enter a valid hexadecimal number (0-9, A-F)', 'error');
                        return;
                    }

                    const decValue = parseInt(hexValue, 16);
                    if (isNaN(decValue)) {
                        decOutput.value = '';
                        showConverterResult('❌ Invalid hexadecimal number', 'error');
                        return;
                    }

                    decOutput.value = decValue;

                    showConverterResult(`✅ ${hexValue} (hex) = ${decValue} (decimal) = 0x${hexValue}`, 'success');
                    addToConsole(`🔄 Converted: 0x${hexValue} → ${decValue}`, 'info');
                }

                // Event listeners
                convertDecToHexBtn.addEventListener('click', convertDecToHex);
                convertHexToDecBtn.addEventListener('click', convertHexToDec);

                // Real-time conversion on input
                decInput.addEventListener('input', function() {
                    if (this.value.trim() !== '') {
                        convertDecToHex();
                    } else {
                        hexOutput.value = '';
                    }
                });

                hexInput.addEventListener('input', function() {
                    // Auto uppercase
                    this.value = this.value.toUpperCase();

                    if (this.value.trim() !== '') {
                        convertHexToDec();
                    } else {
                        decOutput.value = '';
                    }
                });

                // Enter key support
                decInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        convertDecToHex();
                    }
                });

                hexInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        convertHexToDec();
                    }
                });
            }

            // Show converter result
            function showConverterResult(message, type) {
                const resultsDiv = document.getElementById('converterResults');
                if (resultsDiv) {
                    resultsDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'}`;
                    resultsDiv.innerHTML = message;
                    resultsDiv.style.display = 'block';

                    // Auto hide after 3 seconds
                    setTimeout(() => {
                        resultsDiv.style.display = 'none';
                    }, 3000);
                }
            }

            // Update slot options based on item type
            function updateSlotOptions() {
                const itemType = document.getElementById('testItemType').value;

                if (window.itemManager && typeof window.itemManager.getSlotOptions === 'function') {
                    const slotOptions = window.itemManager.getSlotOptions(itemType);

                    // Update all slot dropdowns including craft option
                    ['testSlot1', 'testSlot2', 'testSlot3', 'testCraftOption'].forEach(slotId => {
                        const select = document.getElementById(slotId);
                        const currentValue = select.value;

                        // Clear existing options except NOT
                        select.innerHTML = '<option value="NOT">NOT</option>';

                        // Add new options
                        slotOptions.forEach(option => {
                            const optionElement = document.createElement('option');
                            optionElement.value = option;
                            optionElement.textContent = option;
                            select.appendChild(optionElement);
                        });

                        // Restore previous value if it exists in new options
                        if (slotOptions.includes(currentValue)) {
                            select.value = currentValue;
                        }
                    });

                    addToConsole(`🔄 Updated slot options for ${itemType} (${slotOptions.length} options)`, 'info');
                }
            }

            // Initialize slot options and set up listeners
            if (window.itemManager) {
                addToConsole('✅ ItemManager is available', 'success');
                addToConsole(`📦 ItemManager version: ${window.itemManager.version || 'Unknown'}`, 'info');

                // Load item database
                loadItemDatabase();

                // Load saved form data
                const dataLoaded = loadFormData();
                if (dataLoaded) {
                    addToConsole('📂 Previous form data restored', 'info');
                } else {
                    addToConsole('📝 Starting with fresh form', 'info');
                }

                // Setup auto-save
                setupAutoSave();

                // Setup real-time calculation
                setupRealTimeCalculation();

                // Set up item type change listener
                const itemTypeSelect = document.getElementById('testItemType');
                if (itemTypeSelect) {
                    itemTypeSelect.addEventListener('change', updateSlotOptions);
                }

                // Set up item search listener
                const itemSearchInput = document.getElementById('testItemSearch');
                if (itemSearchInput) {
                    // Real-time search as user types
                    itemSearchInput.addEventListener('input', function() {
                        const query = this.value.trim();
                        if (query.length >= 2) {
                            const matches = searchItems(query);
                            if (matches.length > 0) {
                                addToConsole(`🔍 Found ${matches.length} matches for "${query}"`, 'info');
                            }
                        }
                    });

                    // Handle selection when user finishes typing or selects from datalist
                    itemSearchInput.addEventListener('change', handleItemSelection);
                    itemSearchInput.addEventListener('blur', handleItemSelection);
                }

                // Initialize slot options (with delay to ensure form data is loaded first)
                setTimeout(() => {
                    updateSlotOptions();
                    addToConsole('🔄 Form initialization complete', 'success');

                    // Perform initial real-time calculation
                    if (isRealTimeEnabled) {
                        setTimeout(performRealTimeCalculation, 500);
                    }
                }, 200);
            } else {
                addToConsole('❌ ItemManager not found', 'error');
            }
        });
    </script>
</body>
</html>
