<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');
require '../../_app/php/userLogin.class.php';
$userLogin = new userLogged();
$userLogin->exitHome();
if(isset($_POST['view'])){
$params = array();
$options = array( "Scrollable" => SQLSRV_CURSOR_KEYSET );

			$query = "SELECT TOP 50 * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY LoginTime DESC";
			$list_result = sqlsrv_query($conn,$query,$params,$options);
			$output = '';
			if(sqlsrv_num_rows($list_result) > 0){
			 while($row = sqlsrv_fetch_array($list_result)){
				$name = $userLogin->thaitrans($row["Name"]);
				$useridx = floor($row["CharacterIdx"]/16);

					$selectaccount = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table where usernum = '$useridx' ";
					$selectaccountQuery = sqlsrv_query($conn, $selectaccount, $params, $options);
					$account_result = sqlsrv_fetch_array($selectaccountQuery);


										$NaTion2 = $userLogin->recCharecter($row['CharacterIdx'],'Nation', $conn);
                                        $NaTion  =  $userLogin->nation($NaTion2);
										$alzall = number_format($row["Alz"]);
										$playtimeH = floor($row["PlayTime"]/60). " Hr.";
										$worldidx = $userLogin->recCharecter($row['CharacterIdx'], 'WorldIdx', $conn);
										$world = $userLogin->worldnameidx($worldidx);
										//$logincount = $account_result['LoginCounter'];



										$ipAddresses = array($account_result['LastIp']);

										$targetSubnets = array(
											"104.28",
											"223.204",
											"202.87",
											"192.166",
											"185.213",
											"177.54",
											"27.131",
											"165.231",
											"125.212",
											"58.11",
											"180.183.90",
											"103.163"
										);
										foreach ($ipAddresses as $ipAddress) {
													$ipParts = explode(".", $ipAddress);
													$subnet = $ipParts[0] . "." . $ipParts[1];
										}
										if (in_array($subnet, $targetSubnets)) {
											$color = "danger";
										} else {
											$color = "success";
										}
										if($playtimeH < 1){
										$playtimeH = $row["PlayTime"]. " mm";
										}

		$ClassType = $userLogin->recCharecter($row['CharacterIdx'], 'Style', $conn);
		$classInfo = $userLogin->cabalstyle($ClassType);
		$color2 = isset($color2) ? $color2 : '#FFFFFF';


$output .= '<tr>
    <td>'.$account_result['ID'].'</td>
    <td>'.$account_result['UserNum'].'</td>
    <td>'.$account_result['LoginCounter'].'</td>
    <td><a href="https://checkip.thaiware.com/?ip='.$account_result['LastIp'].'" target="_blank">
        <span class="text-'.$color.' fw-500">'.$ipAddress.'</span>
    </a></td>
    <td>'.$row["CharacterIdx"].'</td>
    <td>'.$name.'</td>
    <td><span class="text-'.$color2.' fw-500">'.$classInfo["Class_Name"].'</span></td>
    <td>'.$row["LEV"].'</td>
    <td>'.$alzall.'</td>
    <td>'.$NaTion.'</td>
    <td>{'.$worldidx.'} '.$world.'</td>
    <td>'.$row["ChannelIdx"].'</td>
    <td>'.
        (isset($row["LoginTime"]) && $row["LoginTime"] !== null 
            ? date("d/m/Y H:i:s", strtotime($row["LoginTime"])) 
            : "ไม่พบข้อมูล").
    '</td>
    <td>'.$playtimeH.'</td>
    <td><a href="?url=manager_account/manage-account-edit&id='.$useridx.'" target="_blank"
        class="btn btn-sm btn-icon btn-outline-primary rounded-circle shadow-0" title="More options">
        <i class="fal fa-ellipsis-v"></i>
    </a></td>
</tr>';

			  }
			}else{
			 $output .= '
			 <li><a href="#" class="text-bold text-italic">No Noti Found</a></li>';
			}

			$status_query = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 1";
			$result = sqlsrv_query($conn, $status_query, $params, $options);
			$count = sqlsrv_num_rows($result);
			$data = array(
				'notification2' => $output,
				'countcharlogin_notification2'  => $count
			);

			echo json_encode($data);

	}

?>