<div class="row">
    <!-- Account Details -->
    <div class="col-lg-8 mb-4">
        <div class="content-card">
            <h3 class="section-title">
                <i class="fal fa-id-card"></i>
                ข้อมูลบัญชีผู้เล่น
            </h3>
            <?php if (isset($returnSuccess)): ?>
            <div class="alert alert-success" role="alert">
                <?php echo $returnSuccess; ?>
                <?php if (isset($result['return_link'])) echo $result['return_link']; ?>
            </div>
            <?php endif; ?>
            <?php if (isset($returnWarning)): ?>
            <div class="alert alert-warning" role="alert">
                <?php echo $returnWarning; ?>
            </div>
            <?php endif; ?>
            <?php if (isset($returnError)): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo $returnError; ?>
            </div>
            <?php endif; ?>

            <?php if (isset($resPlayer) && $resPlayer) { ?>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-hashtag"></i> UserNum
                    </span>
                    <span class="info-value"><?php echo $resPlayer['UserNum']; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-user"></i> ID
                    </span>
                    <span class="info-value"><?php echo $resPlayer['ID']; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-calendar-plus"></i> วันที่สร้าง
                    </span>
                    <span
                        class="info-value"><?php echo date('d/m/Y H:i', strtotime($resPlayer['createDate'])); ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-sign-in-alt"></i> เข้าสู่ระบบ
                    </span>
                    <span
                        class="info-value"><?php echo !empty($resPlayer['LoginTime']) ? date('d/m/Y H:i', strtotime($resPlayer['LoginTime'])) : 'ไม่มีข้อมูล'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-sign-out-alt"></i> ออกจากระบบ
                    </span>
                    <span
                        class="info-value"><?php echo !empty($resPlayer['LogoutTime']) ? date('d/m/Y H:i', strtotime($resPlayer['LogoutTime'])) : 'ไม่มีข้อมูล'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-clock"></i> เวลาเล่น
                    </span>
                    <span class="info-value"><?php echo round($resPlayer['PlayTime'] / 60); ?> ชั่วโมง</span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-chart-line"></i> จำนวนเข้าสู่ระบบ
                    </span>
                    <span class="info-value"><?php echo $resPlayer['LoginCounter']; ?> ครั้ง</span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-network-wired"></i> IP ล่าสุด
                    </span>
                    <a href="https://checkip.thaiware.com/?ip=<?php echo urlencode($resPlayer['LastIp']); ?>"
                        target="_blank" class="info-value" style="color: #007bff; text-decoration: none;">
                        <?php echo $resPlayer['LastIp']; ?>
                    </a>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-envelope"></i> อีเมล
                    </span>
                    <span class="info-value"><?php echo $resPlayer['Email'] ?: 'ไม่มีข้อมูล'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-globe"></i> IP ปัจจุบัน
                    </span>
                    <span class="info-value"><?php echo $resPlayer['NewIp'] ?: 'ไม่มีข้อมูล'; ?></span>
                </div>
                <?php if (isset($selectWebAuthDataFetch)): ?>
                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-fingerprint"></i> Auth UID
                    </span>
                    <span class="info-value"><?php echo $selectWebAuthDataFetch['auth_uid'] ?? 'ไม่มีข้อมูล'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-user-circle"></i> ชื่อจริง
                    </span>
                    <span class="info-value"><?php echo $selectWebAuthDataFetch['name'] ?? 'ไม่มีข้อมูล'; ?></span>
                </div>
                <?php endif; ?>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-phone"></i> เบอร์โทร
                    </span>
                    <span class="info-value"><?php echo $resPlayer['Phone'] ?: 'ไม่มีข้อมูล'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-shield-alt"></i> รหัสยืนยัน
                    </span>
                    <span class="info-value"><?php echo $resPlayer['UserValidation'] ?: 'ไม่มี'; ?></span>
                </div>

                <div class="info-item">
                    <span class="info-label">
                        <i class="fal fa-code"></i> สิทธิ์ Developer
                    </span>
                    <span
                        class="info-value"><?php echo $resPlayer['IsDeveloper'] ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?></span>
                </div>
            </div>

            <?php
                // Display ban/unban confirmation links if applicable
                if (isset($result['message']) && isset($result['action_link'])) {
                    echo '<div class="text-center mt-4">';
                    echo $result['message'];
                    echo $result['action_link'];
                    echo '</div>';
                }
            ?>

            <div class="d-flex justify-content-between align-items-center flex-wrap"
                style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                <a href="?url=manager_account/manage-account" class="action-btn primary">
                    <i class="fal fa-arrow-left"></i> กลับรายการ
                </a>
                <div class="d-flex gap-10 flex-wrap">
                    <?php
                        if (isset($resPlayer['AuthType'])) {
                            if ($resPlayer['AuthType'] == 1) {
                                echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=wait" class="action-btn danger">
                                        <i class="fal fa-ban"></i> แบนผู้เล่น
                                      </a>';
                            } elseif ($resPlayer['AuthType'] == 2) {
                                echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=unban-wait" class="action-btn success">
                                        <i class="fal fa-check-circle"></i> เลิกแบน
                                      </a>';
                            }
                        }
                        ?>
                </div>
            </div>
            <?php } else { ?>
            <div class="alert alert-info text-center" role="alert">
                ไม่พบข้อมูลผู้เล่นสำหรับ UserNum นี้
            </div>
            <div class="text-center">
                <a href="?url=manager_account/manage-account" class="action-btn primary">
                    <i class="fal fa-arrow-left"></i> กลับหน้ารายชื่อผู้เล่น
                </a>
            </div>
            <?php } ?>
        </div>
    </div>

    <!-- Character & Currency Section -->
    <div class="col-lg-4 mb-4">
        <!-- Currency Cards -->
        <div class="content-card">
            <h3 class="section-title">
                <i class="fal fa-wallet"></i>
                เงินในเกม
            </h3>

            <div class="currency-grid">
                <div class="currency-card cash">
                    <i class="fal fa-coins currency-icon"></i>
                    <div class="currency-value"><?php echo number_format($selectCashDataFetch['Cash'] ?? 0); ?></div>
                    <div class="currency-label">Cash</div>
                </div>

                <div class="currency-card bonus">
                    <i class="fal fa-gem currency-icon"></i>
                    <div class="currency-value"><?php echo number_format($selectCashDataFetch['CashBonus'] ?? 0); ?>
                    </div>
                    <div class="currency-label">Cash Bonus</div>
                </div>

                <div class="currency-card rpoint">
                    <i class="fal fa-star currency-icon"></i>
                    <div class="currency-value"><?php echo number_format($selectCashDataFetch['Rpoint'] ?? 0); ?></div>
                    <div class="currency-label">R Point</div>
                </div>

                <div class="currency-card tpoint">
                    <i class="fal fa-trophy currency-icon"></i>
                    <div class="currency-value"><?php echo number_format($selectGemDataFetch['ForcegemHave'] ?? 0); ?>
                    </div>
                    <div class="currency-label">Gem</div>
                </div>
            </div>
        </div>
    </div>
</div>