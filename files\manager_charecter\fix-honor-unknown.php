<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-tools"></i> แก้ไข Honor Class ที่แสดง Unknown
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา Honor Class แสดง Unknown</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ปัญหา Honor Class ที่แสดง Unknown ได้รับการแก้ไขแล้ว
                </div>
                
                <h5>❌ ปัญหาที่เกิดขึ้น</h5>
                <div class="alert alert-danger">
                    <h6>สาเหตุที่ Honor Class แสดง Unknown:</h6>
                    <ul class="mb-0">
                        <li><strong>NULL Values:</strong> Reputation เป็น NULL</li>
                        <li><strong>ค่าติดลบ:</strong> Reputation < 0</li>
                        <li><strong>ค่าเกินขอบเขต:</strong> Reputation > 20</li>
                        <li><strong>SQL Query ไม่จัดการกรณีพิเศษ:</strong> ใช้ ELSE 'Unknown'</li>
                    </ul>
                </div>
                
                <h5>✅ การแก้ไขที่ทำ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ SQL Query เดิม</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>CASE
    WHEN Reputation = 0 THEN 'No Class'
    WHEN Reputation = 1 THEN 'Class 1'
    ...
    WHEN Reputation = 20 THEN 'Class 20'
    ELSE 'Unknown'  -- ปัญหาตรงนี้
END</code></pre>
                                <p class="mb-0"><strong>ปัญหา:</strong> ไม่จัดการ NULL และค่าผิดปกติ</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ SQL Query ใหม่</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>CASE
    WHEN Reputation IS NULL THEN 'No Class'
    WHEN Reputation = 0 THEN 'No Class'
    WHEN Reputation BETWEEN 1 AND 20 THEN 'Class ' + CAST(Reputation AS VARCHAR)
    WHEN Reputation < 0 THEN 'No Class'
    WHEN Reputation > 20 THEN 'Class 20+'
    ELSE 'No Class'  -- แก้ไขแล้ว
END</code></pre>
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> จัดการทุกกรณีแล้ว</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔧 การปรับปรุงที่ทำ</h5>
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. แก้ไข SQL Query หลัก
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ไฟล์:</strong> character-statistics.php</p>
                                <p><strong>การแก้ไข:</strong></p>
                                <ul>
                                    <li>เพิ่ม <code>WHEN ReputationClass IS NULL THEN 'No Class'</code></li>
                                    <li>เพิ่ม <code>WHEN ReputationClass < 0 THEN 'No Class'</code></li>
                                    <li>เพิ่ม <code>WHEN ReputationClass > 20 THEN 'Class 20+'</code></li>
                                    <li>เปลี่ยน <code>ELSE 'Unknown'</code> เป็น <code>ELSE 'No Class'</code></li>
                                    <li>ใช้ <code>ISNULL(ReputationClass, 0)</code> สำหรับ honor_value</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. แก้ไข Top Players Logic
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การปรับปรุง PHP Logic:</strong></p>
                                <pre><code>if ($repClass === null || $repClass == 0 || $repClass < 0) {
    $honorClass = 'No Class';
} elseif ($repClass >= 1 && $repClass <= 20) {
    $honorClass = 'Class ' . $repClass;
} elseif ($repClass > 20) {
    $honorClass = 'Class 20+';
} else {
    $honorClass = 'No Class';
}</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. เพิ่มการรองรับ Class 20+
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>สำหรับค่าที่เกิน 20:</strong></p>
                                <ul>
                                    <li>แสดงเป็น "Class 20+" แทน "Unknown"</li>
                                    <li>ใช้สีดำ (dark) เหมือน Class 20</li>
                                    <li>ใช้ไอคอน crown สำหรับระดับสูง</li>
                                </ul>
                                
                                <p><strong>การจัดการสี:</strong></p>
                                <ul>
                                    <li>No Class: เทา (secondary)</li>
                                    <li>Class 1-5: เขียว (success)</li>
                                    <li>Class 6-10: น้ำเงิน (info)</li>
                                    <li>Class 11-15: เหลือง (warning)</li>
                                    <li>Class 16-19: แดง (danger)</li>
                                    <li>Class 20/20+: ดำ (dark)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแก้ไข</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="openDebugPage()">
                        <i class="fal fa-bug"></i> Debug Honor Class
                    </button>
                    <button class="btn btn-success" onclick="testHonorClass()">
                        <i class="fal fa-vial"></i> ทดสอบ Honor Class
                    </button>
                    <button class="btn btn-warning" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📊 ตัวอย่างข้อมูลที่แก้ไขแล้ว</h5>
                <?php
                // ทดสอบ SQL Query ใหม่
                try {
                    $sql = "SELECT 
                                CASE 
                                    WHEN ReputationClass IS NULL THEN 'No Class'
                                    WHEN ReputationClass = 0 THEN 'No Class'
                                    WHEN ReputationClass BETWEEN 1 AND 20 THEN 'Class ' + CAST(ReputationClass AS VARCHAR)
                                    WHEN ReputationClass < 0 THEN 'No Class'
                                    WHEN ReputationClass > 20 THEN 'Class 20+'
                                    ELSE 'No Class'
                                END as honor_class,
                                ISNULL(ReputationClass, 0) as honor_value,
                                COUNT(*) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            GROUP BY ReputationClass
                            ORDER BY ReputationClass";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead class='thead-dark'>";
                    echo "<tr><th>Honor Class</th><th>Honor Value</th><th>จำนวน</th><th>สถานะ</th></tr>";
                    echo "</thead><tbody>";
                    
                    $totalCount = 0;
                    $unknownCount = 0;
                    
                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $honorClass = $row['honor_class'];
                            $honorValue = $row['honor_value'];
                            $count = $row['count'];
                            $totalCount += $count;
                            
                            // ตรวจสอบว่ายังมี Unknown หรือไม่
                            if ($honorClass == 'Unknown') {
                                $unknownCount += $count;
                            }
                            
                            // กำหนดสี
                            $statusClass = 'success';
                            $statusText = 'แก้ไขแล้ว';
                            
                            if ($honorClass == 'Unknown') {
                                $statusClass = 'danger';
                                $statusText = 'ยังมีปัญหา';
                            } elseif ($honorClass == 'Class 20+') {
                                $statusClass = 'warning';
                                $statusText = 'ค่าเกิน 20';
                            }
                            
                            echo "<tr>";
                            echo "<td><strong>" . htmlspecialchars($honorClass) . "</strong></td>";
                            echo "<td><code>" . $honorValue . "</code></td>";
                            echo "<td>" . number_format($count) . "</td>";
                            echo "<td><span class='badge badge-{$statusClass}'>{$statusText}</span></td>";
                            echo "</tr>";
                        }
                    }
                    
                    echo "</tbody></table></div>";
                    
                    // สรุปผลการแก้ไข
                    echo "<div class='alert alert-" . ($unknownCount > 0 ? 'warning' : 'success') . " mt-3'>";
                    echo "<h6><i class='fal fa-" . ($unknownCount > 0 ? 'exclamation-triangle' : 'check-circle') . "'></i> สรุปผลการแก้ไข:</h6>";
                    echo "<p><strong>ตัวละครทั้งหมด:</strong> " . number_format($totalCount) . " ตัว</p>";
                    echo "<p><strong>แสดง Unknown:</strong> " . number_format($unknownCount) . " ตัว</p>";
                    
                    if ($unknownCount == 0) {
                        echo "<p class='mb-0 text-success'><strong>✅ แก้ไขสำเร็จ!</strong> ไม่มี Honor Class ที่แสดง Unknown แล้ว</p>";
                    } else {
                        echo "<p class='mb-0 text-warning'><strong>⚠️ ยังมีปัญหา:</strong> ยังมี " . number_format($unknownCount) . " ตัวที่แสดง Unknown</p>";
                    }
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ จัดการ NULL Values</li>
                                <li>✅ จัดการค่าติดลบ</li>
                                <li>✅ จัดการค่าเกิน 20</li>
                                <li>✅ เปลี่ยน ELSE เป็น 'No Class'</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ใช้ ISNULL() สำหรับ honor_value</li>
                                <li>✅ รองรับ Class 20+</li>
                                <li>✅ อัพเดท PHP Logic</li>
                                <li>✅ ไม่มี Unknown อีกต่อไป</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function openDebugPage() {
    window.open('?url=manager_charecter/debug-honor-class', '_blank');
}

function testHonorClass() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> เปิดหน้าสถิติเพื่อดูผลการแก้ไข Honor Class</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
