<?php $zpanel->checkSession(true);  ?>
<div class="card">
  <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
    <span>ส่งไอเท็มหลายหลาย ไอเท็ม พร้อมกัน</span>
    <button type="button" class="btn btn-sm btn-warning" id="editItemMasterBtn"><i class="fas fa-database"></i> แก้ไขฐานข้อมูลไอเท็ม</button>
  </div>
  <div class="card-body">
    <form id="multiItemSendForm">
      <div class="form-group">
        <label>Account ID</label>
        <input type="text" class="form-control" name="account_id" id="account_id" required placeholder="กรอก ID (Username)">
      </div>
    <div class="form-group mt-2">
        <label>ExpireDay (จำนวนวัน, เช่น 5)</label>
        <input type="number" class="form-control" name="ExpireDay" min="1" value="5" required style="max-width:120px;">
      </div>
      <div id="multiItemList"></div>

      <button type="button" class="btn btn-success btn-sm mb-2" id="addMultiItemBtn"><i class="fas fa-plus"></i> เพิ่มไอเท็ม</button>
      <button type="submit" class="btn btn-primary btn-block">ส่งไอเท็ม</button>
    </form>
  </div>
</div>
<!-- Modal: แก้ไขฐานข้อมูลไอเท็ม -->
<div class="modal fade" id="itemMasterModal" tabindex="-1" role="dialog" aria-labelledby="itemMasterModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="itemMasterModalLabel">แก้ไขฐานข้อมูลไอเท็ม (Web_ItemMaster)</h5>
        <button type="button" class="close" data-dismiss="modal" data-bs-dismiss="modal" aria-label="Close" id="closeModalBtn">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
          <table class="table table-bordered table-sm" id="itemMasterTable">
            <thead><tr>
              <th>ID</th><th>ItemID</th><th>Name</th><th>Option</th><th>Duration</th><th>Description</th><th>Active</th><th>Action <button type="button" class="btn btn-success btn-sm" id="addItemMasterRow"><i class="fas fa-plus"></i></button></th>
            </tr></thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" data-bs-dismiss="modal" id="closeModalFooterBtn">ปิด</button>
      </div>
    </div>
  </div>
</div>
<style>
.swal2-container {
  z-index: 12000 !important;
}
</style>
<script>
// --- Utility Functions ---
function closeModal() {
    console.log('[closeModal] Attempting to close modal...');

    // ลองหลายวิธีในการปิด modal
    try {
        // วิธีที่ 1: Bootstrap 4/5
        $('#itemMasterModal').modal('hide');

        // วิธีที่ 2: ลบ class โดยตรง
        setTimeout(function() {
            $('#itemMasterModal').removeClass('show');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        }, 100);

        console.log('[closeModal] Modal close attempted');
    } catch (error) {
        console.error('[closeModal] Error closing modal:', error);
    }
}
// --- Item Master Functions ---
let itemMasterList = [];
function fetchItemMaster(callback) {
    if (itemMasterList.length > 0) {
        if (callback) callback(itemMasterList);
        return;
    }
    $.get('_app/php/item_master_data.php?action=list', function(data) {
        itemMasterList = data;
        if (callback) callback(data);
    }, 'json').fail(function(xhr, status, error) {
        // Handle error silently
    });
}
function refreshItemMasterTable() {
    // reset cache เลือกให้ข้อมูลใหม่ครั้ง
    itemMasterList = [];
    fetchItemMaster(function(data) {
        let rows = '';
        data.forEach(function(item) {
            rows += `<tr data-id="${item.id}">
                <td>${item.id}</td>
                <td><input type="number" class="form-control form-control-sm item-id-edit" value="${item.item_id}"></td>
                <td><input type="text" class="form-control form-control-sm name-edit" value="${item.name}"></td>
                <td><input type="text" class="form-control form-control-sm option-edit" value="${item.item_option||''}"></td>
                <td><input type="text" class="form-control form-control-sm duration-edit" value="${item.duration||''}"></td>
                <td><input type="text" class="form-control form-control-sm desc-edit" value="${item.description||''}"></td>
                <td><input type="checkbox" class="is-active-edit" ${(item.is_active==1||item.is_active==='1')?'checked':''}></td>
                <td>
                    <button class="btn btn-sm btn-primary save-item-edit"><i class="fas fa-save"></i> บันทึก</button>
                    <button class="btn btn-sm btn-danger delete-item-master ml-1" data-item-id="${item.id}"><i class="fas fa-trash"></i></button>
                </td>
            </tr>`;
        });
        if (window.addingItemMasterRow) {
            rows = `<tr class="new-item-row">
                <td>New</td>
                <td><input type="number" class="form-control form-control-sm item-id-edit"></td>
                <td><input type="text" class="form-control form-control-sm name-edit"></td>
                <td><input type="text" class="form-control form-control-sm option-edit"></td>
                <td><input type="text" class="form-control form-control-sm duration-edit"></td>
                <td><input type="text" class="form-control form-control-sm desc-edit"></td>
                <td class="text-center"><input type="checkbox" class="is-active-edit" checked></td>
                <td><button class="btn btn-sm btn-success save-item-add"><i class="fas fa-save"></i> บันทึก</button></td>
            </tr>` + rows;
            window.addingItemMasterRow = false;
        }

        $('#itemMasterTable tbody').html(rows);
        $('#itemMasterModal').modal('show');
    });
}

function performDeleteRequest(numericId) {
    const requestData = {id: numericId};

    $.ajax({
        url: '_app/php/item_master_data.php?action=delete',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(res) {
            if (res && res.success) {
                // รีเฟรชตารางก่อนแสดง success message
                itemMasterList = []; // ล้าง cache
                refreshItemMasterTable();

                Swal.fire({
                    icon: 'success',
                    title: 'ลบสำเร็จ',
                    text: 'ลบรายการ ID: ' + numericId + ' เรียบร้อยแล้ว',
                    timer: 1500,
                    showConfirmButton: false
                });
            } else {
                let msg = 'ลบไม่สำเร็จ';
                if (res && res.msg) msg += '<br>' + res.msg;
                if (res && res.sql_errors) {
                    msg += '<br>SQL Error: ' + JSON.stringify(res.sql_errors);
                }

                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด',
                    html: msg
                });
            }
        },
        error: function(xhr, status, error) {
            let errorMsg = 'ไม่สามารถเชื่อมต่อเซิร์ฟเวอร์ได้';

            if (xhr.responseText) {
                try {
                    const errorRes = JSON.parse(xhr.responseText);
                    if (errorRes.msg) {
                        errorMsg = errorRes.msg;
                    }
                } catch (e) {
                    errorMsg += '<br>Response: ' + xhr.responseText.substring(0, 200);
                }
            }

            Swal.fire({
                icon: 'error',
                title: 'ข้อผิดพลาดระบบ',
                html: errorMsg + '<br>Status: ' + status + '<br>Error: ' + error
            });
        }
    });
}

function deleteItem(id) {
    if (!id || id === '' || id === null || id === undefined) {
        Swal.fire({
            icon: 'error',
            title: 'ข้อผิดพลาด',
            text: 'ไม่พบ ID ที่จะลบ (ID: ' + id + ')'
        });
        return;
    }

    // แปลง id เป็น number เพื่อให้แน่ใจ
    const numericId = parseInt(id);
    if (isNaN(numericId)) {
        Swal.fire({
            icon: 'error',
            title: 'ข้อผิดพลาด',
            text: 'ID ไม่ถูกต้อง: ' + id
        });
        return;
    }

    // ตรวจสอบว่า Swal มีอยู่หรือไม่
    if (typeof Swal === 'undefined') {
        if (confirm('ยืนยันการลบรายการ ID: ' + numericId + ' หรือไม่?')) {
            performDeleteRequest(numericId);
        }
        return;
    }

    Swal.fire({
        title: 'ยืนยันการลบ?',
        text: 'คุณต้องการลบรายการ ID: ' + numericId + ' หรือไม่?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'ลบ',
        cancelButtonText: 'ยกเลิก',
        allowOutsideClick: false,
        allowEscapeKey: false
    }).then((result) => {
        // ตรวจสอบทั้ง isConfirmed (SweetAlert2 v11+) และ value (SweetAlert2 เวอร์ชันเก่า)
        if (result.isConfirmed || result.value === true) {
            performDeleteRequest(numericId);
        }
    }).catch((error) => {
        // Fallback to native confirm
        if (confirm('ยืนยันการลบรายการ ID: ' + numericId + ' หรือไม่?')) {
            performDeleteRequest(numericId);
        }
    });
}
function saveNewItem(data) {
    if (!data.item_id || !data.name) {
        Swal.fire({
            icon: 'warning',
            title: 'ข้อมูลไม่ครบ',
            text: 'กรุณากรอก ItemID และ Name'
        });
        return;
    }
    $.ajax({
        url: '_app/php/item_master_data.php?action=add',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(res) {
            if (res.success) {
                // รีเฟรชตารางก่อนแสดง success message
                itemMasterList = []; // ล้าง cache
                refreshItemMasterTable();

                Swal.fire({
                    icon: 'success',
                    title: 'เพิ่มไอเท็มสำเร็จ',
                    text: 'เพิ่มไอเท็มใหม่เรียบร้อยแล้ว',
                    timer: 1500,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'เพิ่มไอเท็มไม่สำเร็จ',
                    text: res.msg || 'เกิดข้อผิดพลาดในการเพิ่มไอเท็ม'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'ข้อผิดพลาดระบบ',
                text: 'ไม่สามารถเชื่อมต่อเซิร์ฟเวอร์ได้'
            });
        }
    });
}
function saveEditItem(data) {
    if (!data.item_id || !data.name) {
        Swal.fire({
            icon: 'warning',
            title: 'ข้อมูลไม่ครบ',
            text: 'กรุณากรอก ItemID และ Name'
        });
        return;
    }

    $.ajax({
        url: '_app/php/item_master_data.php?action=edit',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(res) {
            if (res.success) {
                // รีเฟรชตารางก่อนแสดง success message
                itemMasterList = []; // ล้าง cache
                refreshItemMasterTable();

                Swal.fire({
                    icon: 'success',
                    title: 'บันทึกสำเร็จ',
                    text: 'แก้ไขข้อมูลเรียบร้อยแล้ว',
                    timer: 1500,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'บันทึกไม่สำเร็จ',
                    text: res.msg || 'เกิดข้อผิดพลาดในการบันทึก'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'ข้อผิดพลาดระบบ',
                text: 'ไม่สามารถเชื่อมต่อเซิร์ฟเวอร์ได้'
            });
        }
    });
}
// --- Multi Item Functions ---
function addMultiItemRow(val = {}) {
    fetchItemMaster(function(data) {
        const options = data.map(item => {
            const opt = (item.item_option !== undefined && item.item_option !== null) ? item.item_option : '';
            const dur = (item.duration !== undefined && item.duration !== null) ? item.duration : '';
            return `<option value="${item.item_id}" data-option="${opt}" data-duration="${dur}">${item.name} [${item.item_id}]</option>`;
        }).join('');
        const html = `<div class="input-group mb-1 multi-item-row">
            <select class="form-control item-master-select" name="itemid[]" required style="max-width:350px;" disabled>
                <option value="">-- เลือกไอเท็ม --</option>${options}
            </select>
            <input type="text" class="form-control" name="option[]" placeholder="Option" value="${val.option||''}" required style="max-width:80px;" disabled>
            <input type="text" class="form-control" name="duration[]" placeholder="Duration" value="${val.duration||''}" required style="max-width:80px;" disabled>
            <input type="text" class="form-control" name="amount[]" placeholder="จำนวน" value="${val.amount||'1'}" required style="max-width:60px;" disabled>
            <div class="input-group-append">
                <button type="button" class="btn btn-warning btn-edit-multi-item"><i class="fas fa-edit"></i></button>
                <button type="button" class="btn btn-danger btn-remove-multi-item"><i class="fas fa-trash"></i></button>
            </div>
        </div>`;
        $('#multiItemList').append(html);
        setupMultiItemRowHandlers();
    });
}
function setupMultiItemRowHandlers() {
    $('.item-master-select').last().on('change', function() {
        const sel = $(this).find('option:selected');
        const row = $(this).closest('.multi-item-row');
        const opt = sel.attr('data-option');
        const dur = sel.attr('data-duration');
        row.find('input[name="option[]"]').val((opt !== undefined && opt !== null && opt !== '') ? opt : '');
        row.find('input[name="duration[]"]').val((dur !== undefined && dur !== null && dur !== '') ? dur : '');
    });
}
function sendMultiItems(form) {
    const account_id = $(form).find('input[name="account_id"]').val().trim();
    if (!account_id) {
        Swal.fire({
            icon: 'warning',
            title: 'ข้อมูลไม่ครบ',
            text: 'กรุณากรอก ID (Username)'
        });
        return;
    }
    $.get('_app/php/get_usernum_by_id.php', {id: account_id}, function(res) {
        if (res.success && res.UserNum) {
            const $inputs = $(form).find('#multiItemList').find('select, input');
            $inputs.removeAttr('disabled');
            const formData = $(form).serializeArray()
                .filter(f => f.name !== 'account_id' && f.name !== 'usernum');
            formData.push({name: 'usernum', value: res.UserNum});
            $inputs.attr('disabled', true); // ใส่ disabled กลับ
            const paramData = $.param(formData);
            $.post('_app/php/send_multi_items_data.php', paramData, function(res3) {
                Swal.fire({
                    icon: res3.success ? 'success' : 'error',
                    title: res3.success ? 'ส่งไอเท็มสำเร็จ' : 'ส่งไอเท็มไม่สำเร็จ',
                    text: res3.message || (res3.success ? 'ส่งไอเท็มเรียบร้อยแล้ว' : 'เกิดข้อผิดพลาด'),
                    timer: res3.success ? 1500 : undefined,
                    showConfirmButton: !res3.success
                });
            }, 'json').fail(function(xhr, status, error) {
                // Handle error silently
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'ไม่พบ ID ในระบบ',
                text: res.msg || 'ไม่พบ ID ที่ระบุในฐานข้อมูล'
            });
        }
    }, 'json').fail(function(xhr, status, error) {
        // Handle error silently
    });
}
// --- Event Handlers ---
$(document).ready(function() {
    $('#editItemMasterBtn').on('click', refreshItemMasterTable);
    $('#addItemMasterRow').on('click', function() {
        window.addingItemMasterRow = true;
        refreshItemMasterTable();
    });

    // จัดการการปิด modal
    $('#closeModalBtn, #closeModalFooterBtn').on('click', function() {
        $('#itemMasterModal').modal('hide');
    });

    // จัดการเมื่อคลิกนอก modal
    $('#itemMasterModal').on('click', function(e) {
        if (e.target === this) {
            $(this).modal('hide');
        }
    });

    // จัดการ ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#itemMasterModal').hasClass('show')) {
            $('#itemMasterModal').modal('hide');
        }
    });
    $(document).on('click', '.delete-item-master', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $button = $(this);
        const $row = $button.closest('tr');

        // ลองหาวิธีต่างๆ ในการดึง ID
        let id = null;

        // วิธีที่ 1: จาก data-id ของ row
        id = $row.data('id');

        // วิธีที่ 2: จาก data-item-id ของปุ่ม
        if (!id) {
            id = $button.data('item-id');
        }

        // วิธีที่ 3: จาก attribute data-id ของ row
        if (!id) {
            id = $row.attr('data-id');
        }

        // วิธีที่ 4: จาก cell แรกของ row (ID column)
        if (!id) {
            const firstCell = $row.find('td:first').text().trim();
            if (firstCell && firstCell !== 'New' && !isNaN(firstCell)) {
                id = parseInt(firstCell);
            }
        }

        if (!id || id === '' || id === null || id === undefined) {
            Swal.fire({
                icon: 'error',
                title: 'ข้อผิดพลาด',
                text: 'ไม่พบ ID ของรายการที่จะลบ'
            });
            return;
        }

        deleteItem(id);
    });
    $(document).on('click', '.save-item-add', function() {
        const row = $(this).closest('tr');
        const data = {
            item_id: row.find('.item-id-edit').val(),
            name: row.find('.name-edit').val(),
            item_option: row.find('.option-edit').val(),
            duration: row.find('.duration-edit').val(),
            description: row.find('.desc-edit').val(),
            is_active: row.find('.is-active-edit').is(':checked') ? 1 : 0
        };
        saveNewItem(data);
    });
    $(document).on('click', '.save-item-edit', function() {
        const row = $(this).closest('tr');
        const data = {
            id: row.data('id'),
            item_id: row.find('.item-id-edit').val(),
            name: row.find('.name-edit').val(),
            item_option: row.find('.option-edit').val(),
            duration: row.find('.duration-edit').val(),
            description: row.find('.desc-edit').val(),
            is_active: row.find('.is-active-edit').is(':checked') ? 1 : 0
        };
        saveEditItem(data);
    });
    $('#addMultiItemBtn').on('click', function() { 
        addMultiItemRow(); 
    });
    $(document).on('click', '.btn-remove-multi-item', function() {
        $(this).closest('.multi-item-row').remove();
    });
    $(document).on('click', '.btn-edit-multi-item', function() {
        const row = $(this).closest('.multi-item-row');
        row.find('select, input').prop('disabled', false).first().focus();
        row.addClass('editing');
    });
    $(document).on('blur', '.multi-item-row input, .multi-item-row select', function() {
        const row = $(this).closest('.multi-item-row');
        setTimeout(function() {
            if (!row.find('input:focus,select:focus').length) {
                row.find('select, input').prop('disabled', true);
                row.removeClass('editing');
            }
        }, 200);
    });
    $('#multiItemSendForm').on('submit', function(e) {
        e.preventDefault();
        sendMultiItems(this);
    });
    addMultiItemRow();
});
</script>
