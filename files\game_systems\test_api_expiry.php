<?php
// ทดสอบ API กับวันหมดอายุ
session_start();
$_SESSION['userLogin'] = true; // จำลอง login

echo "<h2>ทดสอบ API กับวันหมดอายุ</h2>";

echo "<h3>1. ทดสอบไม่มีวันหมดอายุ</h3>";
$_POST = [
    'action' => 'generate',
    'count' => '1',
    'items' => '1:0:31',
    'quantity' => '1',
    'expiry_date' => '',
    'code_format' => 'TEST-XXXX-XXXX'
];

echo "<p>ข้อมูลที่ส่ง: " . json_encode($_POST) . "</p>";

ob_start();
include 'class_module/redeem_code_api_simple.php';
$output1 = ob_get_clean();

echo "<p>ผลลัพธ์: <code>$output1</code></p>";

echo "<h3>2. ทดสอบมีวันหมดอายุ (รูปแบบ datetime-local)</h3>";
$_POST = [
    'action' => 'generate',
    'count' => '1',
    'items' => '1:0:31',
    'quantity' => '1',
    'expiry_date' => '2025-12-31 23:59:00',
    'code_format' => 'TEST-XXXX-XXXX'
];

echo "<p>ข้อมูลที่ส่ง: " . json_encode($_POST) . "</p>";

ob_start();
include 'class_module/redeem_code_api_simple.php';
$output2 = ob_get_clean();

echo "<p>ผลลัพธ์: <code>$output2</code></p>";

echo "<h3>3. ทดสอบมีวันหมดอายุ (รูปแบบ ISO)</h3>";
$_POST = [
    'action' => 'generate',
    'count' => '1',
    'items' => '1:0:31',
    'quantity' => '1',
    'expiry_date' => '2025-06-30T12:00:00',
    'code_format' => 'TEST-XXXX-XXXX'
];

echo "<p>ข้อมูลที่ส่ง: " . json_encode($_POST) . "</p>";

ob_start();
include 'class_module/redeem_code_api_simple.php';
$output3 = ob_get_clean();

echo "<p>ผลลัพธ์: <code>$output3</code></p>";

echo "<h3>4. ทดสอบรูปแบบวันที่ผิด</h3>";
$_POST = [
    'action' => 'generate',
    'count' => '1',
    'items' => '1:0:31',
    'quantity' => '1',
    'expiry_date' => 'invalid-date',
    'code_format' => 'TEST-XXXX-XXXX'
];

echo "<p>ข้อมูลที่ส่ง: " . json_encode($_POST) . "</p>";

ob_start();
include 'class_module/redeem_code_api_simple.php';
$output4 = ob_get_clean();

echo "<p>ผลลัพธ์: <code>$output4</code></p>";

echo "<h3>5. ทดสอบฟังก์ชัน formatExpiryDate</h3>";

function formatExpiryDate($dateString) {
    try {
        $formats = [
            'Y-m-d\TH:i',
            'Y-m-d H:i:s',
            'Y-m-d H:i',
            'Y-m-d',
        ];
        
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateString);
            if ($date !== false) {
                return $date->format('Y-m-d H:i:s');
            }
        }
        
        $timestamp = strtotime($dateString);
        if ($timestamp !== false) {
            return date('Y-m-d H:i:s', $timestamp);
        }
        
        return false;
    } catch (Exception $e) {
        return false;
    }
}

$testDates = [
    '2025-12-31T23:59',
    '2025-12-31 23:59:59',
    '2025-12-31 23:59',
    '2025-12-31',
    '2025-06-30T12:00:00',
    'invalid-date',
    '31/12/2025',
    '2025/12/31 23:59:59'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Input</th><th>Output</th><th>Status</th></tr>";

foreach ($testDates as $testDate) {
    $result = formatExpiryDate($testDate);
    $status = $result ? '✅' : '❌';
    echo "<tr>";
    echo "<td><code>$testDate</code></td>";
    echo "<td><code>" . ($result ?: 'false') . "</code></td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>สรุป</h3>";
echo "<p>✅ การทดสอบเสร็จสิ้น</p>";
echo "<p>🔗 กลับไปทดสอบระบบหลัก: <a href='redeem_code_generator.php'>Redeem Code Generator</a></p>";
?>

<style>
table {
    width: 100%;
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
