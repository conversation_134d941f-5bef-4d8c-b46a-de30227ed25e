<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bug"></i> Debug Honor Class - ตรวจสอบค่า ReputationClass
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ตรวจสอบค่า ReputationClass ที่แสดง Unknown</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    <strong>วัตถุประสงค์:</strong> ตรวจสอบค่า ReputationClass ในฐานข้อมูลเพื่อหาสาเหตุที่แสดง Unknown
                </div>
                
                <h5>🔍 การตรวจสอบข้อมูล ReputationClass</h5>
                
                <?php
                try {
                    echo "<div class='row'>";
                    
                    // 1. ตรวจสอบค่า Reputation ทั้งหมดที่มีในระบบ
                    echo "<div class='col-md-6'>";
                    echo "<h6>1. ค่า Reputation ทั้งหมดในระบบ:</h6>";

                    $sql = "SELECT DISTINCT Reputation, COUNT(*) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table
                            GROUP BY Reputation
                            ORDER BY Reputation";
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead><tr><th>Reputation Value</th><th>จำนวน</th><th>Honor Class</th><th>สถานะ</th></tr></thead>";
                    echo "<tbody>";

                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $reputation = $row['Reputation'];
                            $count = $row['count'];
                            
                            // กำหนด Honor Class
                            $honorClass = '';
                            $status = '';
                            $statusClass = '';
                            
                            if ($reputation === null) {
                                $honorClass = 'NULL';
                                $status = 'NULL Value';
                                $statusClass = 'danger';
                            } elseif ($reputation == 0) {
                                $honorClass = 'No Class';
                                $status = 'Valid';
                                $statusClass = 'success';
                            } elseif ($reputation >= 1 && $reputation <= 20) {
                                $honorClass = 'Class ' . $reputation;
                                $status = 'Valid';
                                $statusClass = 'success';
                            } else {
                                $honorClass = 'Unknown';
                                $status = 'Invalid Range';
                                $statusClass = 'warning';
                            }

                            echo "<tr>";
                            echo "<td><code>" . ($reputation === null ? 'NULL' : $reputation) . "</code></td>";
                            echo "<td>" . number_format($count) . "</td>";
                            echo "<td><strong>" . $honorClass . "</strong></td>";
                            echo "<td><span class='badge badge-{$statusClass}'>{$status}</span></td>";
                            echo "</tr>";
                        }
                    }
                    
                    echo "</tbody></table></div></div>";
                    
                    // 2. ตรวจสอบค่าที่อยู่นอกช่วง 0-20
                    echo "<div class='col-md-6'>";
                    echo "<h6>2. ค่าที่อยู่นอกช่วง 0-20:</h6>";
                    
                    $sql = "SELECT ReputationClass, COUNT(*) as count 
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE ReputationClass IS NULL OR ReputationClass < 0 OR ReputationClass > 20
                            GROUP BY ReputationClass 
                            ORDER BY ReputationClass";
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead><tr><th>ReputationClass</th><th>จำนวน</th><th>ปัญหา</th></tr></thead>";
                    echo "<tbody>";
                    
                    $hasInvalidData = false;
                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $hasInvalidData = true;
                            $repClass = $row['ReputationClass'];
                            $count = $row['count'];
                            
                            $problem = '';
                            if ($repClass === null) {
                                $problem = 'NULL Value';
                            } elseif ($repClass < 0) {
                                $problem = 'ค่าติดลบ';
                            } elseif ($repClass > 20) {
                                $problem = 'เกิน Class 20';
                            }
                            
                            echo "<tr>";
                            echo "<td><code>" . ($repClass === null ? 'NULL' : $repClass) . "</code></td>";
                            echo "<td>" . number_format($count) . "</td>";
                            echo "<td><span class='badge badge-danger'>{$problem}</span></td>";
                            echo "</tr>";
                        }
                    }
                    
                    if (!$hasInvalidData) {
                        echo "<tr><td colspan='3' class='text-center text-success'>ไม่มีข้อมูลที่ผิดปกติ</td></tr>";
                    }
                    
                    echo "</tbody></table></div></div>";
                    echo "</div>";
                    
                    // 3. สถิติรวม
                    echo "<div class='row mt-4'>";
                    echo "<div class='col-12'>";
                    echo "<h6>3. สถิติรวม:</h6>";
                    
                    $sql = "SELECT 
                                COUNT(*) as total_characters,
                                COUNT(CASE WHEN ReputationClass IS NULL THEN 1 END) as null_count,
                                COUNT(CASE WHEN ReputationClass = 0 THEN 1 END) as no_class_count,
                                COUNT(CASE WHEN ReputationClass BETWEEN 1 AND 20 THEN 1 END) as valid_class_count,
                                COUNT(CASE WHEN ReputationClass < 0 OR ReputationClass > 20 THEN 1 END) as invalid_count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $total = $row['total_characters'];
                        $nullCount = $row['null_count'];
                        $noClassCount = $row['no_class_count'];
                        $validClassCount = $row['valid_class_count'];
                        $invalidCount = $row['invalid_count'];
                        
                        echo "<div class='row'>";
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-primary'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='text-primary'>" . number_format($total) . "</h5>";
                        echo "<small>ตัวละครทั้งหมด</small>";
                        echo "</div></div></div>";
                        
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-secondary'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='text-secondary'>" . number_format($noClassCount) . "</h5>";
                        echo "<small>No Class (0)</small>";
                        echo "</div></div></div>";
                        
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-success'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='text-success'>" . number_format($validClassCount) . "</h5>";
                        echo "<small>Class 1-20</small>";
                        echo "</div></div></div>";
                        
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-danger'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='text-danger'>" . number_format($nullCount) . "</h5>";
                        echo "<small>NULL Values</small>";
                        echo "</div></div></div>";
                        
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-warning'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='text-warning'>" . number_format($invalidCount) . "</h5>";
                        echo "<small>Invalid Range</small>";
                        echo "</div></div></div>";
                        
                        echo "<div class='col-md-2'>";
                        echo "<div class='card text-center border-info'>";
                        echo "<div class='card-body'>";
                        $unknownCount = $nullCount + $invalidCount;
                        echo "<h5 class='text-info'>" . number_format($unknownCount) . "</h5>";
                        echo "<small>จะแสดง Unknown</small>";
                        echo "</div></div></div>";
                        
                        echo "</div>";
                    }
                    
                    echo "</div></div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🔧 แนวทางแก้ไข</h5>
                <div class="alert alert-warning">
                    <h6><i class="fal fa-tools"></i> หากพบข้อมูลที่ผิดปกติ:</h6>
                    <ol>
                        <li><strong>NULL Values:</strong> อัพเดทให้เป็น 0 (No Class)</li>
                        <li><strong>ค่าติดลบ:</strong> อัพเดทให้เป็น 0 (No Class)</li>
                        <li><strong>ค่าเกิน 20:</strong> ตรวจสอบว่าเป็นข้อมูลที่ถูกต้องหรือไม่</li>
                        <li><strong>อัพเดท SQL Query:</strong> เพิ่มการจัดการ NULL และค่าผิดปกติ</li>
                    </ol>
                </div>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรชข้อมูล
                    </button>
                    <button class="btn btn-success" onclick="testFixedQuery()">
                        <i class="fal fa-tools"></i> ทดสอบ Query ที่แก้ไข
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📝 SQL Query ที่แก้ไขแล้ว</h5>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Query ที่จัดการ NULL และค่าผิดปกติ</h6>
                    </div>
                    <div class="card-body">
                        <pre><code>SELECT 
    CASE 
        WHEN ReputationClass IS NULL THEN 'No Class'
        WHEN ReputationClass = 0 THEN 'No Class'
        WHEN ReputationClass BETWEEN 1 AND 20 THEN 'Class ' + CAST(ReputationClass AS VARCHAR)
        WHEN ReputationClass < 0 THEN 'No Class'
        WHEN ReputationClass > 20 THEN 'Class 20+'
        ELSE 'Unknown'
    END as honor_class,
    ISNULL(ReputationClass, 0) as honor_value,
    COUNT(*) as count
FROM cabal_character_table 
GROUP BY ReputationClass
ORDER BY ReputationClass</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function refreshPage() {
    location.reload();
}

function testFixedQuery() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> ใช้ Query ที่แก้ไขแล้วในหน้าสถิติเพื่อแก้ปัญหา Unknown</div>';
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
