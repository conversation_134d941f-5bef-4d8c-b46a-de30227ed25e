<?php
require('../_app/dbinfo.inc.php');
require('../_app/pay.inc.php');
require('../_app/general_config.inc.php');

$params = array();
$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );		

if(empty($_SERVER['REMOTE_ADDR']) || strcmp($_SERVER['REMOTE_ADDR'],$_CONFIG['tmpay']['access_ip']) != 0) die('ERROR|ACCESS_DENIED');
else if(isset($_CONFIG['tmpay']['amount'][$_GET['amount']]) && strlen($_GET['password']) == 14 && isset($_GET['status']) && strcmp($_GET['hash'],md5($_GET['password'] . $_GET['password'])) == 0)
{
	$stmt = @sqlsrv_query($conn,"SELECT * FROM  WEB_Tmoney WHERE password = '".$_GET['password']."' AND status=0", $params, $options);
	$row = sqlsrv_fetch_array($stmt,SQLSRV_FETCH_ASSOC);

			if(empty($row['user_no'])) die('ERROR|INVALID_USER_NO');
				$result = @sqlsrv_query($conn,"UPDATE WEB_Tmoney SET status='".$_GET['status']."',amount='". $_GET['amount'] ."' WHERE card_id='". $row['card_id']."'") or die('ERROR|WHILE_UPDATE_TMN');
					if($_GET['amount'] > 0){
						$stmt2 = @sqlsrv_query($conn,"SELECT * FROM [".DATABASE_CCA."].[dbo].CashAccount WHERE ID='".$row['user_no']."'", $params, $options);
						$fetchuser = sqlsrv_fetch_array($stmt2,SQLSRV_FETCH_ASSOC);

							if($fetchuser['UserNum'] >= 1){
								//$tsql = @sqlsrv_query($conn,"UPDATE [".DATABASE_CCA."].[dbo].CashAccount SET Cash=Cash+'100',Reward=Reward+'100' WHERE ID='".$row['user_no']."'") or die('ERROR|WHILE_UPDATE_CASH');
								//$tsql = @sqlsrv_query($conn,"UPDATE [".DATABASE_CCA."].[dbo].CashAccount SET Cash=Cash+'".$_CONFIG['tmpay']['cash_amount'][$_GET['amount']]."',Reward=Reward+'".$_CONFIG['tmpay']['amount'][$_GET['amount']]."' WHERE ID='".$row['user_no']."'") or die('ERROR|WHILE_UPDATE_CASH');
								$tsql = @sqlsrv_query($conn,"EXECUTE [".DATABASE_WEB."].[dbo].WEB_DonationPacks_TrueM '".$row['user_no']."','". $_GET['amount'] ."'") or die('ERROR|WHILE_UPDATE_CASH');
								echo 'SUCCEED|UDT|UID=' . $row['user_no'];
							}else{
								echo 'SUCCEED|INS|UID=' . $row['user_no'];
							}

	}else{
		echo 'SUCCEED|AMT_0|UID=' . $row['user_no'];
	}
	@sqlsrv_close($conn);
}
$fbd_chr = array('\'','"',';','*','=','(',':',',','/','\\','(',')');
	foreach($_GET as $key=>$val)
	{
		$_GET[$key] = str_replace($fbd_chr,'',$val);
		if(isset($$key) == true)
		{
			unset($$key);
		}
	}
	foreach($_POST as $key=>$val)
	{
		$_POST[$key] = str_replace($fbd_chr,'',$val);
		if(isset($$key) == true)
		{
			unset($$key);
		}
	}
	foreach($_COOKIE as $key=>$val)
	{
		$_COOKIE[$key] = str_replace($fbd_chr,'',$val);
		if(isset($$key) == true)
		{
			unset($$key);
		}
	}
	foreach($_REQUEST as $key=>$val)
	{
		$_REQUEST[$key] = str_replace($fbd_chr,'',$val);
		if(isset($$key) == true)
		{
			unset($$key);
		}
	}
?>