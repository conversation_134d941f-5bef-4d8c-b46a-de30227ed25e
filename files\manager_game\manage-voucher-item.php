<?php $user->restrictionUser(true, $conn); ?>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager Voucher item <span class="fw-300"><i>จัดการแลกบัตรแคส</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table class="table table-bordered table-hover table-striped w-100">
                            <thead>
                                <tr>
                                    <th>idx</th>
                                    <th>userNum</th>
                                    <th>Id</th>
                                    <th>VoucherPack</th>
                                    <th>ราคา</th>
                                    <th>status</th>
                                    <th>อัพเดด</th>
                                    <th><?php echo T_ACTION; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php

                                // generic function to get page
                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                    $rows = array();
                                    $i = 0;
                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                        array_push($rows, $row);
                                        $i++;
                                    }
                                    return $rows;
                                }

                                // Set the number of rows to be returned on a page.
                                $rowsPerPage = 250;

                                // Define and execute the query.  
                                // Note that the query is executed with a "scrollable" cursor.
                                $sql = "SELECT * FROM  WEB_Voucher_chack where Voucher_status = 0 ORDER BY idx DESC ";

                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                if (!$stmt)
                                    die(print_r(sqlsrv_errors(), true));

                                // Get the total number of rows returned by the query.
                                $rowsReturned = sqlsrv_num_rows($stmt);
                                if ($rowsReturned === false)
                                    die(print_r(sqlsrv_errors(), true));
                                elseif ($rowsReturned == 0) {
                                    echo W_NOTHING_RETURNED;
                                    //exit();
                                } else {
                                    /* Calculate number of pages. */
                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                }

                                // Display the selected page of data.
                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                foreach ($page as $row) {

                            ?>
                                <tr>
                                    <td><?php echo $row[0]; ?></td>
                                    <td><?php echo $row[1]; ?></td>
                                    <td><?php echo $row[2]; ?></td>
                                    <td><?php echo $row[3]; ?></td>
                                    <td><?php echo $row[6]; ?></td>

                                    <td><span
                                            class="<?php echo $label = ($row[4] == '2' ? ' badge badge-danger badge-pill' : $label = $row[4] == '1' ? ' badge badge-danger badge-pill' : ($row[4] == '0' ? ' badge badge-success badge-pill' : 'badge badge-secondary badge-pill'));?>">
                                            <?php echo $status = ($row[4] == '2' ? 'Rewardไม่พอ' : $status = $row[4] == '1' ? 'ใช้งานแล้ว' : ($row[4] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
                                    </td>
                                    <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
                                    <td>
                                        <form method="post" name="j_add_Voucheritem" action="">
                                            <div class="j_alert"></div>
                                            <input type="hidden" name="Voucheckidx" value="<?php echo $row[0]; ?>">
                                            <input type="hidden" name="userid" value="<?php echo $row[2]; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm"><i
                                                    class="fa fa-chevron-circle-right"></i> ยืนยัน</button>
                                            <img src="assets/images/loading/loader.gif" class="load"
                                                alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                        </form>
                                    </td>

                                </tr>
                                <?php } ?>
                            </tbody>

                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>