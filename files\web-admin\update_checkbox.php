<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');
require '../../_app/php/userLogin.class.php';
$userLogin = new userLogged();
$userLogin->exitHome();

if (isset($_POST["checkboxValue_donate"])) {
    $checkboxValue_donate = $_POST["checkboxValue_donate"];

	$updateConfigDonate      = "UPDATE WEB_Config SET value = '$checkboxValue_donate' WHERE config_name = 'Donate'";
	$updateConfigDonateQuery = sqlsrv_query($conn, $updateConfigDonate, array());
	$updateConfigDonateRows  = sqlsrv_rows_affected($updateConfigDonateQuery);

	 if ($checkboxValue_donate) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_register"])) {
    $checkboxValue_register = $_POST["checkboxValue_register"];

   // update register
   $updateConfigRegister      = "UPDATE WEB_Config SET value = '$checkboxValue_register' WHERE config_name = 'register'";
   $updateConfigRegisterQuery = sqlsrv_query($conn, $updateConfigRegister, array());
   $updateConfigRegisterRows  = sqlsrv_rows_affected($updateConfigRegisterQuery);

	 if ($checkboxValue_register) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_ChangePassword"])) {
    $checkboxValue_ChangePassword = $_POST["checkboxValue_ChangePassword"];

   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_ChangePassword' WHERE config_name = 'ChangePassword'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_ChangePassword) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_maintenance"])) {
    $checkboxValue_maintenance = $_POST["checkboxValue_maintenance"];

   // update maintenance
   $updateConfigMaintenance      = "UPDATE WEB_Config SET value = '$checkboxValue_maintenance' WHERE config_name = 'maintenance'";
   $updateConfigMaintenanceQuery = sqlsrv_query($conn, $updateConfigMaintenance, array());
   $updateConfigMaintenanceRows  = sqlsrv_rows_affected($updateConfigMaintenanceQuery);

	 if ($checkboxValue_maintenance) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_store"])) {
    $checkboxValue_store = $_POST["checkboxValue_store"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_store' WHERE config_name = 'store'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_store) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_Voucher"])) {
    $checkboxValue_Voucher= $_POST["checkboxValue_Voucher"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_Voucher' WHERE config_name = 'Voucher'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_Voucher) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_Reward"])) {
    $checkboxValue_Reward = $_POST["checkboxValue_Reward"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_Reward' WHERE config_name = 'Reward'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_Reward) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_DeleteChar"])) {
    $checkboxValue_DeleteChar = $_POST["checkboxValue_DeleteChar"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_DeleteChar' WHERE config_name = 'DeleteChar'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_DeleteChar) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkboxValue_ChangeNation"])) {
    $checkboxValue_ChangeNation = $_POST["checkboxValue_ChangeNation"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_ChangeNation' WHERE config_name = 'ChangeNation'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_ChangeNation) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}
if (isset($_POST["checkboxValue_ResetStatus"])) {
    $checkboxValue_ResetStatus = $_POST["checkboxValue_ResetStatus"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_ResetStatus' WHERE config_name = 'ResetStatus'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_ResetStatus) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}
if (isset($_POST["checkboxValue_UpStatus"])) {
    $checkboxValue_UpStatus = $_POST["checkboxValue_UpStatus"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_UpStatus' WHERE config_name = 'UpStatus'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_UpStatus) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}
if (isset($_POST["checkboxValue_Creatcharector"])) {
    $checkboxValue_Creatcharector= $_POST["checkboxValue_Creatcharector"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkboxValue_Creatcharector' WHERE config_name = 'Creatcharector'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkboxValue_Creatcharector) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}
//game config

if (isset($_POST["checkbox_Event_ItemDaily"])) {
    $checkbox_Event_ItemDaily= $_POST["checkbox_Event_ItemDaily"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkbox_Event_ItemDaily' WHERE config_name = 'Event_ItemDaily'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkbox_Event_ItemDaily) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkbox_Event_GachaPong"])) {
    $checkbox_Event_GachaPong= $_POST["checkbox_Event_GachaPong"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkbox_Event_GachaPong' WHERE config_name = 'Event_GachaPong'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkbox_Event_GachaPong) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkbox_Event_Thailotto"])) {
    $checkbox_Event_Thailotto= $_POST["checkbox_Event_Thailotto"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkbox_Event_Thailotto' WHERE config_name = 'Event_Thailotto'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkbox_Event_Thailotto) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}

if (isset($_POST["checkbox_Event_Football"])) {
    $checkbox_Event_Football= $_POST["checkbox_Event_Football"];

   // update maintenance
   $updateConfig    = "UPDATE WEB_Config SET value = '$checkbox_Event_Football' WHERE config_name = 'Event_Football'";
   $updateConfigQuery = sqlsrv_query($conn, $updateConfig, array());
   $updateConfigRows  = sqlsrv_rows_affected($updateConfigQuery);

	 if ($checkbox_Event_Football) {
        echo "Checkbox is checked!";
    } else {
        echo "Checkbox is unchecked!";
    }
}
?>