<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-users"></i> ทดสอบการแก้ไขระบบออนไลน์
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขการเช็คผู้เล่นออนไลน์ให้ถูกต้อง</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ระบบเช็คผู้เล่นออนไลน์จากการ Login จริง แทนที่จะเช็คจาก ChannelIdx เพียงอย่างเดียว
                </div>
                
                <h5>🔍 วิธีการเช็คออนไลน์ใหม่</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ วิธีที่ 1 (หลัก)</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>เช็คจาก Auth Table:</strong></p>
                                <ul class="mb-0">
                                    <li>ตรวจสอบ login_flag = 1</li>
                                    <li>ตรวจสอบ logout_time IS NULL</li>
                                    <li>ตรวจสอบ ChannelIdx > 0</li>
                                    <li>เชื่อมกับ character table</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">🔄 วิธีที่ 2 (สำรอง)</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>เช็คจาก Login Time:</strong></p>
                                <ul class="mb-0">
                                    <li>ตรวจสอบ ChannelIdx > 0</li>
                                    <li>ตรวจสอบ LastLoginDate ใน 30 นาทีที่ผ่านมา</li>
                                    <li>ใช้เมื่อวิธีแรกไม่มีข้อมูล</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบระบบออนไลน์</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testOnlineStats()">
                        <i class="fal fa-users"></i> ทดสอบ Online Stats
                    </button>
                    <button class="btn btn-info" onclick="testLiveStats()">
                        <i class="fal fa-chart-line"></i> ทดสอบ Live Stats
                    </button>
                    <button class="btn btn-success" onclick="testBothMethods()">
                        <i class="fal fa-balance-scale"></i> เปรียบเทียบวิธีเก่า/ใหม่
                    </button>
                    <button class="btn btn-warning" onclick="openCharacterMonitor()">
                        <i class="fal fa-external-link"></i> เปิด Character Monitor
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📊 ทดสอบการเช็คออนไลน์โดยตรง</h5>
                <?php
                // ทดสอบการเช็คออนไลน์แบบต่าง ๆ
                echo "<h6>ทดสอบวิธีการเช็คออนไลน์:</h6>";
                
                try {
                    // วิธีเก่า: เช็คจาก ChannelIdx เพียงอย่างเดียว
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE ChannelIdx > 0";
                    $result = sqlsrv_query($conn, $sql);
                    $oldMethod = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $oldMethod = $row['count'];
                    }
                    
                    // วิธีใหม่ที่ 1: เช็คจาก Auth table
                    $sql = "SELECT COUNT(DISTINCT c.CharacterIdx) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table c
                            INNER JOIN [".DATABASE_AC."].[dbo].cabal_auth_table a ON c.UserNum = a.UserNum
                            WHERE a.login_flag = 1 
                            AND a.logout_time IS NULL
                            AND c.ChannelIdx > 0";
                    
                    $result = sqlsrv_query($conn, $sql);
                    $newMethod1 = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $newMethod1 = $row['count'];
                    }
                    
                    // วิธีใหม่ที่ 2: เช็คจาก LastLoginDate
                    $sql = "SELECT COUNT(*) as count 
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE ChannelIdx > 0 
                            AND LastLoginDate >= DATEADD(minute, -30, GETDATE())";
                    
                    $result = sqlsrv_query($conn, $sql);
                    $newMethod2 = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $newMethod2 = $row['count'];
                    }
                    
                    // แสดงผลการเปรียบเทียบ
                    echo "<div class='row'>";
                    echo "<div class='col-md-4'>";
                    echo "<div class='card text-center'>";
                    echo "<div class='card-header bg-secondary text-white'>วิธีเก่า</div>";
                    echo "<div class='card-body'>";
                    echo "<h4 class='text-secondary'>" . number_format($oldMethod) . "</h4>";
                    echo "<small>ChannelIdx > 0</small>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-4'>";
                    echo "<div class='card text-center'>";
                    echo "<div class='card-header bg-success text-white'>วิธีใหม่ 1</div>";
                    echo "<div class='card-body'>";
                    echo "<h4 class='text-success'>" . number_format($newMethod1) . "</h4>";
                    echo "<small>Auth Table + Login Flag</small>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-4'>";
                    echo "<div class='card text-center'>";
                    echo "<div class='card-header bg-info text-white'>วิธีใหม่ 2</div>";
                    echo "<div class='card-body'>";
                    echo "<h4 class='text-info'>" . number_format($newMethod2) . "</h4>";
                    echo "<small>LastLoginDate (30 min)</small>";
                    echo "</div></div></div>";
                    echo "</div>";
                    
                    // แสดงข้อมูลเพิ่มเติม
                    echo "<div class='alert alert-info mt-3'>";
                    echo "<h6><i class='fal fa-info-circle'></i> การเปรียบเทียบ:</h6>";
                    echo "<ul class='mb-0'>";
                    echo "<li><strong>วิธีเก่า:</strong> " . number_format($oldMethod) . " คน (อาจไม่แม่นยำ)</li>";
                    echo "<li><strong>วิธีใหม่ 1:</strong> " . number_format($newMethod1) . " คน (แม่นยำที่สุด)</li>";
                    echo "<li><strong>วิธีใหม่ 2:</strong> " . number_format($newMethod2) . " คน (สำรอง)</li>";
                    
                    $finalCount = $newMethod1 > 0 ? $newMethod1 : $newMethod2;
                    echo "<li><strong>ผลลัพธ์สุดท้าย:</strong> " . number_format($finalCount) . " คน</li>";
                    echo "</ul></div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🔧 การปรับปรุงที่ทำ</h5>
                <div class="accordion" id="improvementsAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. ปรับปรุงฟังก์ชัน getOnlineStats()
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#improvementsAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>เช็คจาก cabal_auth_table ก่อน</li>
                                    <li>ใช้ login_flag และ logout_time</li>
                                    <li>มีวิธีสำรองถ้าไม่มีข้อมูล</li>
                                    <li>เพิ่มข้อมูลสถิติรวม</li>
                                    <li>แสดงผู้เล่นออนไลน์ล่าสุด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. ปรับปรุงฟังก์ชัน getLiveStats()
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#improvementsAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>ใช้วิธีเดียวกับ getOnlineStats()</li>
                                    <li>เช็คจาก Auth table ก่อน</li>
                                    <li>มีวิธีสำรองด้วย LastLoginDate</li>
                                    <li>ผลลัพธ์แม่นยำกว่าเดิม</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. เพิ่มข้อมูลใหม่
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#improvementsAccordion">
                            <div class="card-body">
                                <p><strong>ข้อมูลใหม่ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>total_online - จำนวนผู้เล่นออนไลน์รวม</li>
                                    <li>recent_online - ผู้เล่นออนไลน์ล่าสุด 5 คน</li>
                                    <li>last_updated - เวลาที่อัพเดทล่าสุด</li>
                                    <li>error handling ที่ดีขึ้น</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ เช็คออนไลน์จากการ Login จริง</li>
                                <li>✅ ใช้ Auth table เป็นหลัก</li>
                                <li>✅ มีวิธีสำรองด้วย LastLoginDate</li>
                                <li>✅ ข้อมูลแม่นยำกว่าเดิม</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ เพิ่มข้อมูลสถิติรวม</li>
                                <li>✅ แสดงผู้เล่นออนไลน์ล่าสุด</li>
                                <li>✅ Error handling ที่ดีขึ้น</li>
                                <li>✅ ใช้งานได้กับทั้ง 2 ฟังก์ชัน</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testOnlineStats() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Online Stats...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=online_stats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-users"></i> Online Stats Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        
        if (data.success && data.data) {
            html += '<p><strong>Total Online:</strong> ' + (data.data.total_online || 0) + ' คน</p>';
            html += '<p><strong>Last Updated:</strong> ' + (data.data.last_updated || 'N/A') + '</p>';
            
            if (data.data.by_world && data.data.by_world.length > 0) {
                html += '<p><strong>By World:</strong></p><ul>';
                data.data.by_world.forEach(world => {
                    html += '<li>World ' + world.world + ': ' + world.count + ' คน</li>';
                });
                html += '</ul>';
            }
            
            if (data.data.recent_online && data.data.recent_online.length > 0) {
                html += '<p><strong>Recent Online:</strong></p><ul>';
                data.data.recent_online.forEach(player => {
                    html += '<li>' + player.name + ' (Lv.' + player.level + ', World ' + player.world + ')</li>';
                });
                html += '</ul>';
            }
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testLiveStats() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Live Stats...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=live_stats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-chart-line"></i> Live Stats Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        
        if (data.success && data.data) {
            html += '<p><strong>Total Characters:</strong> ' + (data.data.total_characters || 0).toLocaleString() + '</p>';
            html += '<p><strong>Online Characters:</strong> ' + (data.data.online_characters || 0).toLocaleString() + '</p>';
            html += '<p><strong>Created Today:</strong> ' + (data.data.created_today || 0).toLocaleString() + '</p>';
            html += '<p><strong>Average Level:</strong> ' + (data.data.avg_level || 0) + '</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testBothMethods() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังเปรียบเทียบวิธีเก่า/ใหม่...</div>';
    
    try {
        const [onlineResponse, liveResponse] = await Promise.all([
            fetch('files/manager_charecter/api/character-data.php?action=online_stats'),
            fetch('files/manager_charecter/api/character-data.php?action=live_stats')
        ]);
        
        const onlineData = await onlineResponse.json();
        const liveData = await liveResponse.json();
        
        let html = '<div class="alert alert-success">';
        html += '<h6><i class="fal fa-balance-scale"></i> เปรียบเทียบผลลัพธ์</h6>';
        html += '<div class="row">';
        html += '<div class="col-md-6">';
        html += '<h6>Online Stats API:</h6>';
        html += '<p>Total Online: ' + (onlineData.data?.total_online || 0) + ' คน</p>';
        html += '</div>';
        html += '<div class="col-md-6">';
        html += '<h6>Live Stats API:</h6>';
        html += '<p>Online Characters: ' + (liveData.data?.online_characters || 0) + ' คน</p>';
        html += '</div>';
        html += '</div>';
        
        const diff = Math.abs((onlineData.data?.total_online || 0) - (liveData.data?.online_characters || 0));
        html += '<p><strong>ความแตกต่าง:</strong> ' + diff + ' คน</p>';
        
        if (diff === 0) {
            html += '<p class="text-success"><i class="fal fa-check"></i> ผลลัพธ์ตรงกัน - ระบบทำงานถูกต้อง</p>';
        } else {
            html += '<p class="text-warning"><i class="fal fa-exclamation-triangle"></i> ผลลัพธ์แตกต่างกัน - อาจมีความล่าช้าในการอัพเดท</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
.card {
    margin-bottom: 1rem;
}
</style>
