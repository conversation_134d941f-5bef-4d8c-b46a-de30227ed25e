<?php 
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code. 
 */
$user->restrictionUser(true, $conn); ?>
<section class="panel">
<div class="panel-body">
<h2><?php echo PT_MANAGECATEGORY; ?> <small><?php echo PT_MANAGETICKETS_DESC; ?></small></h2>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body no-padd" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
                <?php if (isset($_GET['action'])){ if($_GET['action'] == 'update') { ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_H_CATEGORY_UPDATED; ?></div>
                <?php } else if($_GET['action'] == 'create'){ ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_CATEGORY_SUCCESS; ?></div>
                <?php }} ?>
                <table class="table table-striped no-margn">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 10;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM WEB_H_Category";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo '<div class="alert alert-warning flat"><strong>No have categories yet! Create one.</strong></div>';
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
                                <td><span style="padding: 5px;" class="label label-<?php echo $status = ($row[2] == '1' ? 'success' : ($row[2] == '0' ? 'danger' : 'default')); ?>"><?php echo $status = ($row[2] == '1' ? 'Activated' : ($row[2] == '0' ? 'Deactivated' : 'Unknown status')); ?></span></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                            <?php echo B_ACTION; ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a href="?url=helpdesk/a/edit-category&id=<?php echo $row[0]; ?>"><?php echo B_EDIT; ?></a></li>
                                            <li><a href="?url=helpdesk/a/edit-category&id=<?php echo $row[0]; ?>&delete=wait"><?php echo B_DELETE; ?></a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/create-category&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=manager/players&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=manager/players&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                if (isset($_POST['btn_create'])) {
                    // variables access
                    $inputName = strip_tags(trim($_POST['input_name']));
                    $inputStatus = strip_tags(trim($_POST['input_status']));

                    if (empty($inputName)) {
                        $returnWarning = W_H_EMPTY_NAME;
                    } else {
                        
                        // condition
                       // if(!$inputStatus){
                         //   $inputStatus = '1';
                       // }

                        // update UsersData
                        $updateCategory = "INSERT INTO WEB_H_Category (name, status) VALUES ('$inputName', '$inputStatus')";
                        $updateCategoryParam = array();
                        $updateCategoryQuery = sqlsrv_query($conn, $updateCategory, $updateCategoryParam);

                        if (sqlsrv_rows_affected($updateCategoryQuery)) {
                            unset($inputName, $inputStatus);
                            header('Location: ?url=helpdesk/a/category&action=create');
                        } else {
                            $returnError = E_CATEGORY;
                        }
                    }
                }
                ?>
                <?php if (isset($returnWarning)) { ?>
                    <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                <?php } elseif (isset($returnSuccess)) { ?>
                    <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                <?php } ?>
                <form role="form" method="post" enctype="multipart/form-data">
                    <h4>สร้าง กลุ่ม/หมวดหมู่</h4>
                    <div class="form-group">
                        <label for="input_name" class="text-red">ชื่อ กลุ่ม/หมวดหมู่</label>
                        <input type="text" name="input_name" class="form-control" placeholder="ชื่อ กลุ่ม/หมวดหมู่...">
                    </div>

                   <div class="form-group">
                        <label class="text-red">สถานะ การใช้งาน</label>
                        <div class="form-group">
                            <label>ใช้งาน</label>
                            <div class="switch-button showcase-switch-button">
                                <input id="input_on" name="input_status" value="1" type="radio">
                                <label for="input_on"></label>
                            </div>

                            <label>ยกเลิก</label>
                            <div class="switch-button showcase-switch-button">
                                <input id="input_off" name="input_status" value="0" type="radio">
                                <label for="input_off"></label>
                            </div>
                        </div>
                        <span class="help-block text-red well well-sm">Status default is <strong>Activated</strong></span>
                    </div>
                    <input type="submit" name="btn_create" class="btn btn-success btn-block" value="<?php echo B_CREATE; ?>">
                </form>
            </div>
        </div>
    </div>
</div>
</div>
</section>
<?php sqlsrv_close($conn); ?>
