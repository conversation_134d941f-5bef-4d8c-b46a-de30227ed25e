<?php $user->restrictionUser(true, $conn); ?>
<header class="page-header">
						<h2>Voucher Manager</h2>
						<div class="right-wrapper pull-right">
							<ol class="breadcrumbs">
								<li>
									<a href="index.php">
										<i class="fa fa-home"></i>
									</a>
								</li>
								<li><span>Voucher</span></li>
							</ol>
							<a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
						</div>
					</header>

                 	<section role="main" class="content-body card-margin">
					<!-- start: page -->

					<div class="row">
						<div class="col-md-6">
							<form id="form" action="forms-validation.html" class="form-horizontal" novalidate="novalidate">
								<section class="panel">
									<header class="panel-heading">
										<div class="panel-actions">
											<a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
											<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
										</div>

										<h2 class="panel-title">Basic Form Validation</h2>
										<p class="panel-subtitle">
											Basic validation will display a label with the error after the form control.
										</p>
									</header>
									<div class="panel-body">
										<div class="form-group has-error">
											<label class="col-sm-3 control-label">Full Name <span class="required" aria-required="true">*</span></label>
											<div class="col-sm-9">
												<input type="text" name="fullname" class="form-control" placeholder="eg.: John Doe" required="" aria-required="true" aria-invalid="true"><label id="fullname-error" class="error" for="fullname">This field is required.</label>
											</div>
										</div>
										<div class="form-group has-error">
											<label class="col-sm-3 control-label">Email <span class="required" aria-required="true">*</span></label>
											<div class="col-sm-9">
												<div class="input-group">
													<span class="input-group-addon">
														<i class="fa fa-envelope"></i>
													</span>
													<input type="email" name="email" class="form-control" placeholder="eg.: <EMAIL>" required="" aria-required="true">
												</div><label id="email-error" class="error" for="email">This field is required.</label>
											</div>
											<div class="col-sm-9">

											</div>
										</div>
										<div class="form-group">
											<label class="col-sm-3 control-label">GitHub</label>
											<div class="col-sm-9">
												<input type="url" name="github" class="form-control valid" placeholder="eg.: https://github.com/johndoe">
											</div>
										</div>
										<div class="form-group has-error">
											<label class="col-sm-3 control-label">Skills <span class="required" aria-required="true">*</span></label>
											<div class="col-sm-9">
												<textarea name="skills" rows="5" class="form-control" placeholder="Describe your skills" required="" aria-required="true"></textarea><label id="skills-error" class="error" for="skills">This field is required.</label>
											</div>
										</div>
									</div>
									<footer class="panel-footer">
										<div class="row">
											<div class="col-sm-9 col-sm-offset-3">
												<button class="btn btn-primary">Submit</button>
												<button type="reset" class="btn btn-default">Reset</button>
											</div>
										</div>
									</footer>
								</section>
							</form>
						</div>
						<!-- col-md-6 -->
						<div class="col-md-6">
							<form id="chk-radios-form" action="forms-validation.html" novalidate="novalidate">
								<section class="panel">
									<header class="panel-heading">
										<div class="panel-actions">
											<a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
											<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
										</div>

										<h2 class="panel-title">Validating Checkbox and Radios</h2>

										<p class="panel-subtitle">
											Easily validate checkboxes and radios tags.
										</p>
									</header>
									<div class="panel-body">
										<div class="form-group">
											<label class="col-sm-3 control-label">Porto Admin is <span class="required" aria-required="true">*</span></label>
											<div class="col-sm-9">
												<div class="radio-custom radio-primary">
													<input id="awesome" name="porto_is" type="radio" value="awesome" required="" aria-required="true">
													<label for="awesome">Awesome</label>
												</div>
												<div class="radio-custom radio-primary">
													<input id="very-awesome" name="porto_is" type="radio" value="very-awesome">
													<label for="very-awesome">Very Awesome</label>
												</div>
												<div class="radio-custom radio-primary">
													<input id="ultra-awesome" name="porto_is" type="radio" value="ultra-awesome">
													<label for="ultra-awesome">Ultran awesome</label>
												</div>
												<label class="error" for="porto_is"></label>
											</div>
										</div>
										<div class="form-group">
											<label class="col-sm-3 control-label">I will use it for <span class="required" aria-required="true">*</span></label>
											<div class="col-sm-9">
												<div class="checkbox-custom chekbox-primary">
													<input id="for-project" value="project" type="checkbox" name="for[]" required="" aria-required="true">
													<label for="for-project">My Project</label>
												</div>
												<div class="checkbox-custom chekbox-primary">
													<input id="for-website" value="website" type="checkbox" name="for[]">
													<label for="for-website">My Website</label>
												</div>
												<div class="checkbox-custom chekbox-primary">
													<input id="for-all" value="all" type="checkbox" name="for[]">
													<label for="for-all">All things I do</label>
												</div>
												<label class="error" for="for[]"></label>
											</div>
										</div>
									</div>
									<footer class="panel-footer">
										<div class="row">
											<div class="col-sm-9 col-sm-offset-3">
												<button class="btn btn-primary">Submit</button>
												<button type="reset" class="btn btn-default">Reset</button>
											</div>
										</div>
									</footer>
								</section>
							</form>
						</div>
					</div>


					<div class="row">
						<div class="col-lg-6">
							<form id="form" action="forms-validation.html" class="form-horizontal">
								<section class="card">
									<header class="card-header">
										<div class="card-actions">
											<a href="#" class="card-action card-action-toggle" data-card-toggle></a>
											<a href="#" class="card-action card-action-dismiss" data-card-dismiss></a>
										</div>

										<h2 class="card-title">ระบบส่งไอเท็ม</h2>
										<p class="card-subtitle">
											ระบบส่งไอเท็มให้ user
										</p>
									</header>
									<div class="card-body">
										<div class="form-group row">
											<label class="col-sm-3 control-label text-sm-right pt-2">Itemcode <span class="required">*</span></label>
											<div class="col-sm-9">
												<input type="text" name="input_item" class="form-control" placeholder="รหัสไอเท็ม" required/>
											</div>
										</div>
										<div class="form-group row">
											<label class="col-sm-3 control-label text-sm-right pt-2">ItemOption <span class="required">*</span></label>
											<div class="col-sm-9">
													<input type="text" name="input_opt" class="form-control" placeholder="ออฟชั่นไอเท็ม" required/>
												</div>
											</div>
										<div class="form-group row">
											<label class="col-sm-3 control-label text-sm-right pt-2">Duration <span class="required">*</span></label>
											<div class="col-sm-9">
														<div class="m-md slider-primary ui-slider ui-corner-all ui-slider-horizontal ui-widget ui-widget-content" data-plugin-slider="" data-plugin-options="{ &quot;value&quot;: 0, &quot;range&quot;: &quot;min&quot;, &quot;max&quot;: 31 }" data-plugin-slider-output="#listenSlider">
															<input id="listenSlider" type="hidden" value="0">
														<div class="ui-slider-range ui-corner-all ui-widget-header ui-slider-range-min" style="width: 100%;"></div><span tabindex="0" class="ui-slider-handle ui-corner-all ui-state-default" style="left: 100%;"></span></div>
														<p class="output">ปัจจุบัน <code>value</code> is: <b>0</b></p>
													</div>
											</div>
										<div class="form-group row">
											<label class="col-sm-3 control-label text-sm-right pt-2">UserID <span class="required">*</span></label>
											<div class="col-sm-9">
												<input type="text" name="input_opt" class="form-control" placeholder="ไอดีผู้เล่น" required/>
											</div>
										</div>
									</div>
									<footer class="panel-footer">
										<div class="row">
											<div class="col-sm-9 col-sm-offset-3">
												<button class="btn btn-primary">Submit</button>
												<button type="reset" class="btn btn-default">Reset</button>
											</div>
										</div>
									</footer>
								</section>
							</form>
						</div>
					
					<!-- end: page -->
				</section>   
