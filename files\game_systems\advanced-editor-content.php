<?php
// Advanced Editor Content - Only HTML content for loading
header('Content-Type: text/html; charset=utf-8');
?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info mb-3">
                <h5><i class="fas fa-cogs"></i> Advanced Item Editor</h5>
                <p class="mb-0">Complete item code calculation with options, slots, binding, and real-time preview.</p>
            </div>

            <!-- Main Form -->
            <form id="advancedItemForm">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Item Search:</label>
                        <input type="text" id="testItemSearch" class="form-control" placeholder="Search item name..." list="itemSearchList">
                        <datalist id="itemSearchList">
                            <!-- Items will be loaded here -->
                        </datalist>
                        <small class="text-secondary">Search and select item from database</small>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Item ID:</label>
                        <input type="number" id="testItemId" class="form-control" value="" placeholder="Item ID">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Item Type:</label>
                        <select id="testItemType" class="form-control">
                            <option value="Weapon" selected>Weapon</option>
                            <option value="Helm">Helm</option>
                            <option value="Suit">Suit</option>
                            <option value="Gloves">Gloves</option>
                            <option value="Boots">Boots</option>
                            <option value="Bike">Bike</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Binding:</label>
                        <select id="testBinding" class="form-control">
                            <option value="none">No Binding</option>
                            <option value="id">Bind to ID</option>
                            <option value="char" selected>Bind to Character</option>
                            <option value="equ">Bind on Equip</option>
                        </select>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-2">
                        <label class="form-label">Upgrade:</label>
                        <select id="testUpgrade" class="form-control">
                            <option value="0">+0</option>
                            <option value="5">+5</option>
                            <option value="10">+10</option>
                            <option value="15" selected>+15</option>
                            <option value="20">+20</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Extreme:</label>
                        <select id="testExtreme" class="form-control">
                            <option value="0" selected>0</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Divine:</label>
                        <select id="testDivine" class="form-control">
                            <option value="0" selected>0</option>
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="15">15</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Duration:</label>
                        <select id="testDuration" class="form-control">
                            <option value="31" selected>31->ถาวร</option>
                            <option value="17">17->30 วัน</option>
                            <option value="12">12->7 วัน</option>
                            <option value="9">9->1 วัน</option>
                            <option value="0">ไม่มีอายุ</option>
                        </select>
                    </div>
                </div>

                <!-- Slots Section -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <label class="form-label">Slot 1:</label>
                        <select id="testSlot1" class="form-control">
                            <option value="NOT" selected>NOT</option>
                            <option value="MP">MP</option>
                            <option value="HP">HP</option>
                            <option value="DEF">DEF</option>
                            <option value="ATTACK">ATTACK</option>
                            <option value="CRIT RATE">CRIT RATE</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Slot 2:</label>
                        <select id="testSlot2" class="form-control">
                            <option value="NOT" selected>NOT</option>
                            <option value="MP">MP</option>
                            <option value="HP">HP</option>
                            <option value="DEF">DEF</option>
                            <option value="ATTACK">ATTACK</option>
                            <option value="CRIT RATE">CRIT RATE</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Slot 3:</label>
                        <select id="testSlot3" class="form-control">
                            <option value="NOT" selected>NOT</option>
                            <option value="MP">MP</option>
                            <option value="HP">HP</option>
                            <option value="DEF">DEF</option>
                            <option value="ATTACK">ATTACK</option>
                            <option value="CRIT RATE">CRIT RATE</option>
                        </select>
                    </div>
                </div>

                <!-- Craft Options -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label class="form-label">Craft Option:</label>
                        <select id="testCraftOption" class="form-control">
                            <option value="NOT" selected>NOT</option>
                            <option value="MP">MP</option>
                            <option value="HP">HP</option>
                            <option value="DEF">DEF</option>
                            <option value="ATTACK">ATTACK</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Craft Height:</label>
                        <select id="testCraftHeight" class="form-control">
                            <option value="0" selected>0</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                        </select>
                    </div>
                </div>

                <!-- Control Buttons -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <button type="button" id="calculateAdvancedBtn" class="btn btn-primary w-100">
                            <i class="fas fa-calculator mr-2"></i>Calculate Advanced
                        </button>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="realTimeCalculation" checked>
                            <label class="form-check-label" for="realTimeCalculation">
                                <small><i class="fas fa-sync mr-1"></i>Real-time</small>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button type="button" id="resetAdvancedBtn" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-undo mr-2"></i>Reset Form
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" id="syncToBasicBtn" class="btn btn-outline-info w-100">
                            <i class="fas fa-arrow-up mr-2"></i>Sync ↑
                        </button>
                    </div>
                </div>
            </form>

            <!-- Results Display -->
            <div id="advancedResults" class="mt-4">
                <div class="alert alert-light">
                    <h6><i class="fas fa-info-circle mr-2"></i>Ready for Calculation</h6>
                    <p class="mb-0">Enter item details above and click "Calculate Advanced" or enable real-time calculation.</p>
                </div>
            </div>

            <!-- Debug Console -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white py-2">
                            <h6 class="mb-0"><i class="fas fa-terminal mr-2"></i>Debug Console</h6>
                        </div>
                        <div class="card-body p-0">
                            <div id="debugConsole" style="height: 200px; overflow-y: auto; background: #1e1e1e; color: #fff; padding: 10px; font-family: 'Courier New', monospace;">
                                <div style="color: #00ff00;">[INFO] Advanced Editor loaded and ready</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Advanced Editor JavaScript
console.log('🔧 Advanced Editor script loaded');

// Binding codes
const bindingCodes = {
    none: 0,
    id: 4096,
    char: 524288,
    equ: 1572864
};

// Add to console function
function addToConsole(message, type = 'info') {
    const console = document.getElementById('debugConsole');
    if (console) {
        const colors = {
            info: '#00ff00',
            warning: '#ffff00',
            error: '#ff0000',
            success: '#00ffff'
        };
        const color = colors[type] || '#ffffff';
        const timestamp = new Date().toLocaleTimeString();
        console.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
        console.scrollTop = console.scrollHeight;
    }
}

// Calculate Advanced function
function calculateAdvanced() {
    const itemId = parseInt(document.getElementById('testItemId').value) || 0;
    const itemType = document.getElementById('testItemType').value;
    const upgrade = parseInt(document.getElementById('testUpgrade').value) || 0;
    const binding = document.getElementById('testBinding').value;
    const duration = parseInt(document.getElementById('testDuration').value) || 31;
    
    if (itemId === 0) {
        alert('Please enter an Item ID');
        return;
    }
    
    // Calculate binding code
    let bindingCode = bindingCodes[binding] || 0;
    
    // Calculate final item code
    const finalItemCode = itemId + bindingCode;
    const hexItemCode = finalItemCode.toString(16).toUpperCase();
    
    // Display results
    const resultsHTML = `
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> Advanced Calculation Results</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>Final Results:</h6>
                    <p><strong>Item Code:</strong> <span style="background-color: #333; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">${finalItemCode}</span></p>
                    <p><strong>Hex Code:</strong> <span style="background-color: #333; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">${hexItemCode}</span></p>
                </div>
                <div class="col-md-6">
                    <h6>Details:</h6>
                    <p><strong>Base Item:</strong> ${itemId}</p>
                    <p><strong>Binding:</strong> +${bindingCode} (${binding.toUpperCase()})</p>
                    <p><strong>Type:</strong> ${itemType}</p>
                    <p><strong>Upgrade:</strong> +${upgrade}</p>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('advancedResults').innerHTML = resultsHTML;
    addToConsole(`✅ Calculated: ${itemId} + ${bindingCode} = ${finalItemCode} (${hexItemCode})`, 'success');
    
    // Sync back to basic form if available
    if (typeof syncFromAdvanced === 'function') {
        syncFromAdvanced(itemId);
    }
}

// Setup event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Calculate button
    const calcBtn = document.getElementById('calculateAdvancedBtn');
    if (calcBtn) {
        calcBtn.addEventListener('click', calculateAdvanced);
    }
    
    // Reset button
    const resetBtn = document.getElementById('resetAdvancedBtn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            document.getElementById('testItemSearch').value = '';
            document.getElementById('testItemId').value = '';
            document.getElementById('testItemType').value = 'Weapon';
            document.getElementById('testUpgrade').value = '0';
            document.getElementById('testExtreme').value = '0';
            document.getElementById('testDivine').value = '0';
            document.getElementById('testBinding').value = 'none';
            document.getElementById('testDuration').value = '31';
            document.getElementById('testSlot1').value = 'NOT';
            document.getElementById('testSlot2').value = 'NOT';
            document.getElementById('testSlot3').value = 'NOT';
            document.getElementById('testCraftOption').value = 'NOT';
            document.getElementById('testCraftHeight').value = '0';
            document.getElementById('advancedResults').innerHTML = `
                <div class="alert alert-light">
                    <h6><i class="fas fa-info-circle mr-2"></i>Form Reset</h6>
                    <p class="mb-0">All fields have been reset to default values.</p>
                </div>
            `;
            addToConsole('🔄 Form reset to defaults', 'info');
        });
    }
    
    // Sync button
    const syncBtn = document.getElementById('syncToBasicBtn');
    if (syncBtn) {
        syncBtn.addEventListener('click', function() {
            const itemId = document.getElementById('testItemId').value;
            if (itemId && typeof syncFromAdvanced === 'function') {
                syncFromAdvanced(itemId);
                addToConsole(`🔄 Synced ${itemId} to Basic Manager`, 'info');
            }
        });
    }
    
    addToConsole('✅ Advanced Editor initialized', 'success');
});
</script>
