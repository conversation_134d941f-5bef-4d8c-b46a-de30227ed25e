<?php $zpanel->checkSession(true);  ?>
<div class="card">
  <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
    <span>ส่งไอเท็มหลายชิ้นให้ 1 ไอดี</span>
    <button type="button" class="btn btn-sm btn-warning" id="editItemMasterBtn"><i class="fas fa-database"></i> แก้ไขฐานข้อมูลไอเท็ม</button>
  </div>
  <div class="card-body">
    <form id="multiItemSendForm">
      <div class="form-group">
        <label>Account ID (Username)</label>
        <input type="text" class="form-control" name="account_id" id="account_id" required placeholder="กรอก ID (Username)">
      </div>
      <div id="multiItemList"></div>
      <div class="form-group mt-2">
        <label>ExpireDay (จำนวนวัน, เช่น 5)</label>
        <input type="number" class="form-control" name="ExpireDay" min="1" value="5" required style="max-width:120px;">
      </div>
      <button type="button" class="btn btn-success btn-sm mb-2" id="addMultiItemBtn"><i class="fas fa-plus"></i> เพิ่มไอเท็ม</button>
      <button type="submit" class="btn btn-primary btn-block">ส่งไอเท็ม</button>
    </form>
  </div>
</div>
<!-- Modal: แก้ไขฐานข้อมูลไอเท็ม -->
<div class="modal fade" id="itemMasterModal" tabindex="-1" role="dialog" aria-labelledby="itemMasterModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="itemMasterModalLabel">แก้ไขฐานข้อมูลไอเท็ม (Web_ItemMaster)</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
          <table class="table table-bordered table-sm" id="itemMasterTable">
            <thead><tr>
              <th>ID</th><th>ItemID</th><th>Name</th><th>Option</th><th>Duration</th><th>Description</th><th>Active</th><th>Action <button type="button" class="btn btn-success btn-sm" id="addItemMasterRow"><i class="fas fa-plus"></i></button></th>
            </tr></thead>
            <tbody></tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
.swal2-container {
  z-index: 12000 !important;
}
</style>
<script>
// --- Utility Functions ---
function showAlert(icon, title, html) {
    Swal.fire({ icon, title, html });
}
function showDelayedAlert(icon, title, html, delay = 400) {
    setTimeout(() => showAlert(icon, title, html), delay);
}
// --- Item Master Functions ---
let itemMasterList = [];
function fetchItemMaster(callback) {
    console.log('[fetchItemMaster] called, itemMasterList.length =', itemMasterList.length);
    if (itemMasterList.length > 0) { 
        console.log('[fetchItemMaster] use cache', itemMasterList);
        if (callback) callback(itemMasterList);
        return;
    }
    $.get('_app/php/item_master_data.php?action=list', function(data) {
        console.log('[fetchItemMaster] ajax response', data);
        itemMasterList = data;
        if (callback) callback(data);
    }, 'json').fail(function(xhr, status, error) {
        console.log('[fetchItemMaster] $.get FAIL', {xhr, status, error});
    });
}
function refreshItemMasterTable() {
    console.log('[refreshItemMasterTable] called');
    // reset cache เพื่อให้ดึงข้อมูลใหม่ทุกครั้ง
    itemMasterList = [];
    fetchItemMaster(function(data) {
        console.log('[refreshItemMasterTable] fetchItemMaster callback', data);
        let rows = '';
        data.forEach(function(item) {
            rows += `<tr data-id="${item.id}">
                <td>${item.id}</td>
                <td><input type="number" class="form-control form-control-sm item-id-edit" value="${item.item_id}"></td>
                <td><input type="text" class="form-control form-control-sm name-edit" value="${item.name}"></td>
                <td><input type="text" class="form-control form-control-sm option-edit" value="${item.item_option}"></td>
                <td><input type="text" class="form-control form-control-sm duration-edit" value="${item.duration}"></td>
                <td><input type="text" class="form-control form-control-sm desc-edit" value="${item.description||''}"></td>
                <td><input type="checkbox" class="is-active-edit" ${(item.is_active==1||item.is_active==='1')?'checked':''}></td>
                <td>
                    <button class="btn btn-sm btn-primary save-item-edit"><i class="fas fa-save"></i> บันทึก</button>
                    <button class="btn btn-sm btn-danger delete-item-master ml-1"><i class="fas fa-trash"></i></button>
                </td>
            </tr>`;
        });
        if (window.addingItemMasterRow) {
            rows = `<tr class="new-item-row">
                <td>New</td>
                <td><input type="number" class="form-control form-control-sm item-id-edit"></td>
                <td><input type="text" class="form-control form-control-sm name-edit"></td>
                <td><input type="text" class="form-control form-control-sm option-edit"></td>
                <td><input type="text" class="form-control form-control-sm duration-edit"></td>
                <td><input type="text" class="form-control form-control-sm desc-edit"></td>
                <td class="text-center"><input type="checkbox" class="is-active-edit" checked></td>
                <td><button class="btn btn-sm btn-success save-item-add"><i class="fas fa-save"></i> บันทึก</button></td>
            </tr>` + rows;
            window.addingItemMasterRow = false;
        }
        console.log('[refreshItemMasterTable] rows html', rows);
        $('#itemMasterTable tbody').html(rows);
        $('#itemMasterModal').modal('show');
        console.log('[refreshItemMasterTable] modal shown');
    });
}

function deleteItem(id) {
    if (!id) {
        console.error('No id to delete');
        return;
    }
    console.log('Deleting id:', id); // debug
    Swal.fire({
        title: 'ยืนยันการลบ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'ลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '_app/php/item_master_edit.php?action=delete',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({data}),
                success: function(res) {
                    console.log('Delete response:', res); // debug
                    if (res.success) {
                        console.log('Swal success: ลบสำเร็จ');
                        refreshItemMasterTable(); // อัปเดตตารางทันที
                        Swal.fire({
                            icon: 'success',
                            title: 'ลบสำเร็จ',
                            timer: 1200,
                            showConfirmButton: false
                        });
                    } else {
                        let msg = 'ลบไม่สำเร็จ';
                        if (res.msg) msg += '<br>' + res.msg;
                        if (res.sql_errors) msg += '<br>' + JSON.stringify(res.sql_errors);
                        console.log('Swal error:', msg);
                        Swal.fire({
                            icon: 'error',
                            title: 'เกิดข้อผิดพลาด',
                            html: msg
                        });
                    }
                },
                error: function(xhr) {
                    console.log('AJAX error:', xhr);
                    Swal.fire({
                        icon: 'error',
                        title: 'เกิดข้อผิดพลาด',
                        text: 'ไม่สามารถเชื่อมต่อเซิร์ฟเวอร์ได้'
                    });
                }
            });
        }
    });
}
function saveNewItem(data) {
    if (!data.item_id || !data.name) {
        showAlert('warning', 'กรุณากรอก ItemID และ Name');
        return;
    }
    $.ajax({
        url: '_app/php/item_master_data.php?action=add',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(res) {
            if (res.success) {
                $('#itemMasterModal').modal('hide');
                showDelayedAlert('success', 'เพิ่มไอเท็มสำเร็จ');
                refreshItemMasterTable();
            } else {
                showDelayedAlert('error', 'เพิ่มไอเท็มไม่สำเร็จ');
            }
        }
    });
}
function saveEditItem(data) {
    $.ajax({
        url: '_app/php/item_master_edit.php?action=edit',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(res) {
            if (res.success) {
                $('#itemMasterModal').modal('hide');
                showDelayedAlert('success', 'บันทึกสำเร็จ');
                refreshItemMasterTable();
            } else {
                showDelayedAlert('error', 'บันทึกไม่สำเร็จ');
            }
        }
    });
}
// --- Multi Item Functions ---
function addMultiItemRow(val = {}) {
    console.log('[addMultiItemRow] called', val);
    fetchItemMaster(function(data) {
        console.log('[addMultiItemRow] fetchItemMaster callback', data);
        const options = data.map(item => {
            const opt = (item.item_option !== undefined && item.item_option !== null) ? item.item_option : '';
            const dur = (item.duration !== undefined && item.duration !== null) ? item.duration : '';
            return `<option value="${item.item_id}" data-option="${opt}" data-duration="${dur}">${item.name} [${item.item_id}]</option>`;
        }).join('');
        const html = `<div class="input-group mb-1 multi-item-row">
            <select class="form-control item-master-select" name="itemid[]" required style="max-width:350px;" disabled>
                <option value="">-- เลือกไอเท็ม --</option>${options}
            </select>
            <input type="text" class="form-control" name="option[]" placeholder="Option" value="${val.option||''}" required style="max-width:80px;" disabled>
            <input type="text" class="form-control" name="duration[]" placeholder="Duration" value="${val.duration||''}" required style="max-width:80px;" disabled>
            <input type="text" class="form-control" name="amount[]" placeholder="จำนวน" value="${val.amount||'1'}" required style="max-width:60px;" disabled>
            <div class="input-group-append">
                <button type="button" class="btn btn-warning btn-edit-multi-item"><i class="fas fa-edit"></i></button>
                <button type="button" class="btn btn-danger btn-remove-multi-item"><i class="fas fa-trash"></i></button>
            </div>
        </div>`;
        console.log('[addMultiItemRow] html', html);
        $('#multiItemList').append(html);
        setupMultiItemRowHandlers();
    });
}
function setupMultiItemRowHandlers() {
    console.log('[setupMultiItemRowHandlers] called');
    $('.item-master-select').last().on('change', function() {
        const sel = $(this).find('option:selected');
        const row = $(this).closest('.multi-item-row');
        const opt = sel.attr('data-option');
        const dur = sel.attr('data-duration');
        console.log('[setupMultiItemRowHandlers] select changed', {opt, dur});
        row.find('input[name="option[]"]').val((opt !== undefined && opt !== null && opt !== '') ? opt : '');
        row.find('input[name="duration[]"]').val((dur !== undefined && dur !== null && dur !== '') ? dur : '');
    });
}
function sendMultiItems(form) {
    const account_id = $(form).find('input[name="account_id"]').val().trim();
    console.log('[sendMultiItems] START', {form, account_id});
    if (!account_id) {
        console.log('[sendMultiItems] ไม่มี account_id');
        showAlert('warning', 'กรุณากรอก ID (Username)');
        return;
    }
    console.log('[sendMultiItems] account_id =', account_id);
    $.get('_app/php/get_usernum_by_id.php', {id: account_id}, function(res) {
        console.log('[sendMultiItems] get_usernum_by_id response:', res);
        if (res.success && res.UserNum) {
            const $inputs = $(form).find('#multiItemList').find('select, input');
            console.log('[sendMultiItems] $inputs ก่อน removeAttr', $inputs);
            $inputs.removeAttr('disabled');
            console.log('[sendMultiItems] $inputs หลัง removeAttr', $inputs);
            const formData = $(form).serializeArray()
                .filter(f => f.name !== 'account_id' && f.name !== 'usernum');
            console.log('[sendMultiItems] formData หลัง serializeArray', formData);
            formData.push({name: 'usernum', value: res.UserNum});
            $inputs.attr('disabled', true); // ใส่ disabled กลับ
            console.log('[sendMultiItems] formData หลัง push usernum', formData);
            const paramData = $.param(formData);
            console.log('[sendMultiItems] paramData =', paramData);
            $.post('_app/php/send_multi_items_data.php', paramData, function(res3) {
                console.log('[sendMultiItems] send_multi_items_data.php response:', res3);
                showAlert(res3.success ? 'success' : 'error', res3.message);
            }, 'json').fail(function(xhr, status, error) {
                console.log('[sendMultiItems] $.post FAIL', {xhr, status, error});
            });
        } else {
            console.log('[sendMultiItems] ไม่พบ usernum หรือ error', res);
            showAlert('error', res.msg || 'ไม่พบ ID นี้ในระบบ');
        }
    }, 'json').fail(function(xhr, status, error) {
        console.log('[sendMultiItems] $.get FAIL', {xhr, status, error});
    });
}
// --- Event Handlers ---
$(document).ready(function() {
    $('#editItemMasterBtn').on('click', refreshItemMasterTable);
    $('#addItemMasterRow').on('click', function() {
        window.addingItemMasterRow = true;
        refreshItemMasterTable();
    });
    $(document).on('click', '.delete-item-master', function() {
        const id = $(this).closest('tr').data('id');
        deleteItem(id);
    });
    $(document).on('click', '.save-item-add', function() {
        const row = $(this).closest('tr');
        const data = {
            item_id: row.find('.item-id-edit').val(),
            name: row.find('.name-edit').val(),
            item_option: row.find('.option-edit').val(),
            duration: row.find('.duration-edit').val(),
            description: row.find('.desc-edit').val(),
            is_active: row.find('.is-active-edit').is(':checked') ? 1 : 0
        };
        saveNewItem(data);
    });
    $(document).on('click', '.save-item-edit', function() {
        const row = $(this).closest('tr');
        const data = {
            id: row.data('id'),
            item_id: row.find('.item-id-edit').val(),
            name: row.find('.name-edit').val(),
            item_option: row.find('.option-edit').val(),
            duration: row.find('.duration-edit').val(),
            description: row.find('.desc-edit').val(),
            is_active: row.find('.is-active-edit').is(':checked') ? 1 : 0
        };
        saveEditItem(data);
    });
    $('#addMultiItemBtn').on('click', function() { 
        addMultiItemRow(); 
    });
    $(document).on('click', '.btn-remove-multi-item', function() {
        $(this).closest('.multi-item-row').remove();
    });
    $(document).on('click', '.btn-edit-multi-item', function() {
        const row = $(this).closest('.multi-item-row');
        row.find('select, input').prop('disabled', false).first().focus();
        row.addClass('editing');
    });
    $(document).on('blur', '.multi-item-row input, .multi-item-row select', function() {
        const row = $(this).closest('.multi-item-row');
        setTimeout(function() {
            if (!row.find('input:focus,select:focus').length) {
                row.find('select, input').prop('disabled', true);
                row.removeClass('editing');
            }
        }, 200);
    });
    $('#multiItemSendForm').on('submit', function(e) {
        e.preventDefault();
        sendMultiItems(this);
    });
    addMultiItemRow();
});
</script>
