<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-server"></i> ทดสอบ Analytics บน Production
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">การตรวจสอบปัญหา Character Analytics บน Host จริง</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    ตรวจสอบสาเหตุที่ character-analytics ไม่แสดงผลบน production server
                </div>
                
                <h5>🔍 การตรวจสอบระบบ</h5>
                
                <!-- Server Environment Check -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">🖥️ Server Environment</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr><td>PHP Version:</td><td><?php echo phpversion(); ?></td></tr>
                                    <tr><td>Server Software:</td><td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td></tr>
                                    <tr><td>Memory Limit:</td><td><?php echo ini_get('memory_limit'); ?></td></tr>
                                    <tr><td>Max Execution Time:</td><td><?php echo ini_get('max_execution_time'); ?>s</td></tr>
                                    <tr><td>Error Reporting:</td><td><?php echo error_reporting(); ?></td></tr>
                                    <tr><td>Display Errors:</td><td><?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">🔗 Database Connection</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    if ($conn) {
                                        echo '<div class="alert alert-success alert-sm">✅ Database connected</div>';
                                        
                                        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                                        $result = sqlsrv_query($conn, $sql);
                                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                            echo '<p>Total Characters: <strong>' . number_format($row['count']) . '</strong></p>';
                                        }
                                        
                                        // ทดสอบ query ที่ใช้ใน analytics
                                        $sql = "SELECT TOP 1 CreateDate FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY CreateDate DESC";
                                        $result = sqlsrv_query($conn, $sql);
                                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                            $latestDate = $row['CreateDate'];
                                            if ($latestDate instanceof DateTime) {
                                                echo '<p>Latest Character: <strong>' . $latestDate->format('Y-m-d H:i:s') . '</strong></p>';
                                            } else {
                                                echo '<p>Latest Character: <strong>' . $latestDate . '</strong></p>';
                                            }
                                        }
                                    } else {
                                        echo '<div class="alert alert-danger alert-sm">❌ Database not connected</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<div class="alert alert-danger alert-sm">❌ Database error: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- File System Check -->
                <h5 class="mt-4">📁 File System Check</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>File/Directory</th>
                                <th>Exists</th>
                                <th>Readable</th>
                                <th>Size</th>
                                <th>Modified</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $filesToCheck = [
                                'character-analytics.php',
                                '../assets/js/chart.min.js',
                                '../assets/js/chart.js',
                                '../assets/css/app.bundle.css',
                                '../assets/js/app.bundle.js'
                            ];
                            
                            foreach ($filesToCheck as $file) {
                                $exists = file_exists($file);
                                $readable = $exists ? is_readable($file) : false;
                                $size = $exists ? filesize($file) : 0;
                                $modified = $exists ? date('Y-m-d H:i:s', filemtime($file)) : 'N/A';
                                
                                $existsIcon = $exists ? '✅' : '❌';
                                $readableIcon = $readable ? '✅' : '❌';
                                
                                echo "<tr>";
                                echo "<td><code>$file</code></td>";
                                echo "<td>$existsIcon</td>";
                                echo "<td>$readableIcon</td>";
                                echo "<td>" . ($size > 0 ? number_format($size) . ' bytes' : 'N/A') . "</td>";
                                echo "<td>$modified</td>";
                                echo "</tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Analytics Data Test -->
                <h5 class="mt-4">📊 Analytics Data Test</h5>
                <?php
                function testAnalyticsData($conn) {
                    $results = [];
                    
                    try {
                        // Test 1: Daily creation data
                        $sql = "SELECT TOP 5
                                    CAST(CreateDate AS DATE) as create_date,
                                    COUNT(*) as new_characters
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                WHERE CreateDate >= DATEADD(day, -7, GETDATE())
                                GROUP BY CAST(CreateDate AS DATE)
                                ORDER BY create_date DESC";
                        
                        $result = sqlsrv_query($conn, $sql);
                        if ($result) {
                            $count = 0;
                            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                $count++;
                            }
                            $results['daily_creation'] = [
                                'status' => 'success',
                                'count' => $count,
                                'message' => "Found $count daily creation records"
                            ];
                        } else {
                            $results['daily_creation'] = [
                                'status' => 'error',
                                'count' => 0,
                                'message' => 'Query failed: ' . print_r(sqlsrv_errors(), true)
                            ];
                        }
                        
                        // Test 2: Playtime distribution
                        $sql = "SELECT TOP 5
                                    CASE 
                                        WHEN PlayTime < 3600 THEN '< 1 hour'
                                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                                        ELSE '10+ hours'
                                    END as playtime_range,
                                    COUNT(*) as count
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                GROUP BY 
                                    CASE 
                                        WHEN PlayTime < 3600 THEN '< 1 hour'
                                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                                        ELSE '10+ hours'
                                    END";
                        
                        $result = sqlsrv_query($conn, $sql);
                        if ($result) {
                            $count = 0;
                            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                $count++;
                            }
                            $results['playtime_distribution'] = [
                                'status' => 'success',
                                'count' => $count,
                                'message' => "Found $count playtime distribution records"
                            ];
                        } else {
                            $results['playtime_distribution'] = [
                                'status' => 'error',
                                'count' => 0,
                                'message' => 'Query failed: ' . print_r(sqlsrv_errors(), true)
                            ];
                        }
                        
                    } catch (Exception $e) {
                        $results['error'] = [
                            'status' => 'error',
                            'message' => $e->getMessage()
                        ];
                    }
                    
                    return $results;
                }
                
                $testResults = testAnalyticsData($conn);
                
                foreach ($testResults as $test => $result) {
                    $alertClass = $result['status'] === 'success' ? 'alert-success' : 'alert-danger';
                    $icon = $result['status'] === 'success' ? '✅' : '❌';
                    
                    echo "<div class='alert $alertClass alert-sm'>";
                    echo "<strong>$icon $test:</strong> " . $result['message'];
                    echo "</div>";
                }
                ?>
                
                <!-- JavaScript Dependencies Test -->
                <h5 class="mt-4">📜 JavaScript Dependencies Test</h5>
                <div id="js-test-results" class="alert alert-secondary">
                    <i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ JavaScript dependencies...
                </div>
                
                <!-- Quick Links -->
                <h5 class="mt-4">🔗 Quick Links</h5>
                <div class="btn-group">
                    <a href="?url=manager_charecter/character-analytics" class="btn btn-primary" target="_blank">
                        <i class="fal fa-chart-line"></i> Open Analytics (Normal)
                    </a>
                    <a href="?url=manager_charecter/character-analytics&debug=1" class="btn btn-warning" target="_blank">
                        <i class="fal fa-bug"></i> Open Analytics (Debug)
                    </a>
                    <a href="?url=manager_charecter/debug-analytics" class="btn btn-info" target="_blank">
                        <i class="fal fa-search"></i> Full Debug Page
                    </a>
                </div>
                
                <!-- Troubleshooting Guide -->
                <h5 class="mt-4">🛠️ Troubleshooting Guide</h5>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. หน้าว่างเปล่า (Blank Page)
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>ตรวจสอบ PHP error logs</li>
                                    <li>เปิด debug mode: <code>?debug=1</code></li>
                                    <li>ตรวจสอบ memory limit และ execution time</li>
                                    <li>ตรวจสอบ database connection</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. Charts ไม่แสดง
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>ตรวจสอบ Chart.js library โหลดหรือไม่</li>
                                    <li>ตรวจสอบ Console (F12) หา JavaScript errors</li>
                                    <li>ตรวจสอบ Network tab ว่า files โหลดได้หรือไม่</li>
                                    <li>ตรวจสอบ Canvas support ในเบราว์เซอร์</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. ข้อมูลไม่ถูกต้อง
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>ตรวจสอบ SQL queries ใน debug mode</li>
                                    <li>ตรวจสอบ DateTime handling</li>
                                    <li>ตรวจสอบ database permissions</li>
                                    <li>ตรวจสอบ data types และ encoding</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // ทดสอบ JavaScript dependencies
    setTimeout(function() {
        let results = [];
        
        // ทดสอบ jQuery
        if (typeof jQuery !== 'undefined') {
            results.push('✅ jQuery: ' + jQuery.fn.jquery);
        } else {
            results.push('❌ jQuery: Not loaded');
        }
        
        // ทดสอบ Chart.js
        if (typeof Chart !== 'undefined') {
            results.push('✅ Chart.js: ' + Chart.version);
        } else {
            results.push('❌ Chart.js: Not loaded');
        }
        
        // ทดสอบ Canvas
        const canvas = document.createElement('canvas');
        if (canvas.getContext && canvas.getContext('2d')) {
            results.push('✅ Canvas 2D: Supported');
        } else {
            results.push('❌ Canvas 2D: Not supported');
        }
        
        // ทดสอบ LocalStorage
        if (typeof Storage !== 'undefined') {
            results.push('✅ LocalStorage: Supported');
        } else {
            results.push('❌ LocalStorage: Not supported');
        }
        
        // แสดงผลลัพธ์
        const resultDiv = document.getElementById('js-test-results');
        const alertClass = results.some(r => r.includes('❌')) ? 'alert-warning' : 'alert-success';
        
        resultDiv.className = 'alert ' + alertClass;
        resultDiv.innerHTML = '<h6>JavaScript Dependencies:</h6><ul class="mb-0">' + 
            results.map(r => '<li>' + r + '</li>').join('') + '</ul>';
    }, 1000);
});
</script>

<style>
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.table-sm td {
    padding: 0.3rem;
    font-size: 0.875rem;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
