<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

// ดึงจำนวน voucher ที่รออนุมัติ (Voucher_status = 0)
$status_query = "SELECT COUNT(*) AS cnt FROM [".DATABASE_WEB."].[dbo].WEB_Voucher_chack WHERE Voucher_status = 0";
$result = sqlsrv_query($conn, $status_query);
$count = 0;
if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
    $count = $row['cnt'];
}
echo json_encode(['countvoucher_notification' => $count]);
exit;
?>
