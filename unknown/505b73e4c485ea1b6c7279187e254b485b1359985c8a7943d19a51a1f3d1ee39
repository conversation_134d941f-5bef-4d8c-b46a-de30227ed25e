<?php $user->restrictionUser(true, $conn); ?>
<?php
// โหลดไฟล์ JSON
$jsonFile = '_data/account_data_iptables.json';
$jsonData = file_get_contents($jsonFile);

if ($jsonData === false) {
    die("Error: Cannot read JSON file.");
}

$data = json_decode($jsonData, true);

if ($data === null || !is_array($data) || empty($data)) {
    die("Error: JSON decoding failed or data is empty.");
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<?php
if (isset($_POST['btn_loadaccountdata_json'])) {
    $params = array();
    $options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
    $selectChars = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table";
    $selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);

    if (sqlsrv_num_rows($selectCharsQuery)) {
        $array_data = array();
        while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
            $array_data[] = array(
                'UserNum' => $resChars['UserNum'],
                'ID' => $resChars['ID'],
                'Login' => $resChars['Login'],
                'LoginTime' => $resChars['LoginTime'],
                'LogoutTime' => $resChars['LogoutTime'],
                'AuthType' => $resChars['AuthType'],
                'PlayTime' => $resChars['PlayTime'],
                'LastIp' => $resChars['LastIp'],
                'createDate' => $resChars['createDate'],
                'Email' => $resChars['Email'],
                'NewIp' => $resChars['NewIp'],
                'OldIp' => $resChars['OldIp'],
                'Phone' => $resChars['Phone'],
                'Action' => $resChars['UserNum']
            );
        }
        $response = array(
            "data" => $array_data
        );
        $final_data = json_encode($response, JSON_PRETTY_PRINT);
        if (file_put_contents('_data/account_data_iptables.json', $final_data)) {
            $returnSuccess = "File Appended Success fully";
        } else {
            $returnError = 'JSON File not exits';
        }
        //}  
    } else {
        $returnError = 'ข้อมูลผิดพลาด';
    }
}
?>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Example <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="iptables-account-datatables" class="table table-bordered table-hover">

                        <thead>
                            <tr>
                                <th>UserNum</th>
                                <th>ID</th>
                                <th>Last IP</th>
                                <th>New IP</th>
                                <th>Old IP</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                      <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                    <div class="row">
                        <div class="col-xl-3">
                            <form role="form" method="post" enctype="multipart/form-data">
                                <div class="form-group">
                                    <button type="submit" name="btn_loadaccountdata_json"
                                        class="btn btn-primary btn-lg btn-block waves-effect waves-themed">
                                        </span>ถ้าค้นหาข้อมูลไม่เจอ ให้กดปุ่มโหลดข้อมูล <code>ไอดี</code> ใหม่
                                        (คลิก!!)
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                </div>
            
            </div>
        </div>
    </div>

<script>

$(document).on("click", ".add-ip-btn", function() {
    let ipAddress = $(this).data("ip").trim(); // ดึงค่า IP จาก data-ip

    if (ipAddress) {
        // แจ้งเตือนก่อนส่ง
        alert("กำลังส่ง IP: " + ipAddress);

        // แสดงค่าใน Developer Console
        //console.log("IP Address ที่ส่งไปยังเซิร์ฟเวอร์:", ipAddress);

        // ส่งค่าผ่าน AJAX
        $.post("files/server-admin/Exec_add_ip.php", { ip: ipAddress }, function(response) {
            alert(response); // แสดงผลลัพธ์จาก PHP
            reloadTable(); // โหลดข้อมูลใหม่โดยไม่ต้องรีเฟรช
        });

    } else {
        alert("Error: IP address is missing.");
    }
});

// ฟังก์ชันโหลดข้อมูลใหม่โดยไม่ต้องรีเฟรช
function reloadTable() {
    $.getJSON('_data/account_data_iptables.json', function(response) {
        let table = $('#iptables-account-datatables').DataTable();
        table.clear().rows.add(response.data).draw();
    });
}

 $(document).ready(function() {
    $.getJSON('_data/account_data_iptables.json', function(response) {
        if (response && response.data) {
            $('#iptables-account-datatables').DataTable({
                data: response.data,
                columns: [
                    { data: "UserNum", title: "UserNum" },
                    { data: "ID", title: "ID" },
                    { data: "LastIp", title: "Last IP" },
                    { data: "NewIp", title: "New IP" },
                    { data: "OldIp", title: "Old IP" },
                    { 
                        data: "Action", 
                        title: "Action", 
                        render: function(data, type, row) {
                            return `
                                <button class="btn btn-success btn-sm add-ip-btn" 
                                    data-ip="${row.NewIp}" 
                                    data-oldip="${row.OldIp}">
                                    <i class="fal fa-plus mr-1"></i> AddIP
                                </button>
                            `;
                        } 
                    }
                ],
                paging: true,
                searching: true,
                ordering: true
            });
        } else {
            console.error("JSON structure is incorrect or empty.");
        }
    }).fail(function(jqXHR, textStatus, errorThrown) {
        console.error("Error loading JSON: " + textStatus + " - " + errorThrown);
    });
});
</script>