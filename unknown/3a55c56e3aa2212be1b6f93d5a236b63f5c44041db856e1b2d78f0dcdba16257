<?php
// Ensure required classes are loaded and session is started.
// Assuming $user and $zpanel are instantiated objects from your framework.
$user->restrictionUser(true, $conn);
$zpanel->checkSession(true);

// Initialize variables for messages
$returnSuccess = null;
$returnWarning = null;
$returnError = null;

// Sanitize and validate the UserNum from GET parameter
$userNum = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

// Redirect if no valid UserNum is provided
if ($userNum === false || $userNum === null) {
    header('Location: ?url=manager_account/manage-account');
    exit;
}

// Handle ban/unban actions first
$banAction = filter_input(INPUT_GET, 'ban', FILTER_UNSAFE_RAW); // Changed from FILTER_SANITIZE_STRING

if ($banAction) {
    switch ($banAction) {
        case 'wait':
            echo '<p class="text-red text-bolder">' . (defined('T_CONFIRMBAN') ? T_CONFIRMBAN : 'Are you sure you want to ban this account?') . '</p>';
            echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=true" class="btn btn-danger">' .'Yes, Ban'. '</a>';
            break;

        case 'true':
            // Fetch UserNum from ID if AuthType update uses ID.
            // Original code used UserNum from selectAccFetch but updated AuthType with ID. Let's assume AuthType should be updated using UserNum.
            // If UserNum is truly unique for update, this fetch might be redundant if $userNum from GET is reliable.
            // For safety, re-fetch the user data based on UserNum.
            $selectAccSql = "SELECT UserNum FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = ?";
            $selectAccParams = array($userNum);
            $selectAccQuery = sqlsrv_query($conn, $selectAccSql, $selectAccParams);

            if ($selectAccQuery && sqlsrv_has_rows($selectAccQuery)) {
                $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                $targetUserNum = $selectAccFetch['UserNum'];

                $banPlayerSql = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET AuthType = ? WHERE UserNum = ?";
                $banPlayerParams = array(2, $targetUserNum); // 2 for banned
                $banPlayerQuery = sqlsrv_query($conn, $banPlayerSql, $banPlayerParams);

                if ($banPlayerQuery && sqlsrv_rows_affected($banPlayerQuery)) {
                    $zpanel->generateWebLog($conn, '1', $targetUserNum, 'player banned', 'player banned by admin');
                    $returnSuccess = (defined('S_ACCOUNT_BANNED') ? S_ACCOUNT_BANNED : 'Account has been successfully banned.');
                    echo '<a href="?url=manager_account/manage-account" class="btn btn-default" style="margin: 5px 0;">Return to players page</a>';
                } else {
                    $returnWarning = (defined('W_ACCOUNT_BANNED') ? W_ACCOUNT_BANNED : 'Failed to ban account or account already banned.');
                }
            } else {
                $returnError = (defined('E_USER_NOT_FOUND') ? E_USER_NOT_FOUND : 'User not found for banning.');
            }
            break;

        case 'unban-wait':
            echo '<p class="text-red text-bolder">' . (defined('T_CONFIRMUNBAN') ? T_CONFIRMUNBAN : 'Are you sure you want to unban this account?') . '</p>';
            echo '<a href="?url=manager_account/manage-account-edit&id=' . $userNum . '&ban=unban" class="btn btn-danger">' . (defined('B_YESUNBAN') ? B_YESUNBAN : 'Yes, Unban') . '</a>';
            break;

        case 'unban':
            $selectAccSql = "SELECT UserNum FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = ?";
            $selectAccParams = array($userNum);
            $selectAccQuery = sqlsrv_query($conn, $selectAccSql, $selectAccParams);

            if ($selectAccQuery && sqlsrv_has_rows($selectAccQuery)) {
                $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                $targetUserNum = $selectAccFetch['UserNum'];

                $unbanPlayerSql = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET AuthType = ? WHERE UserNum = ?";
                $unbanPlayerParams = array(1, $targetUserNum); // 1 for unbanned/normal
                $unbanPlayerQuery = sqlsrv_query($conn, $unbanPlayerSql, $unbanPlayerParams);

                if ($unbanPlayerQuery && sqlsrv_rows_affected($unbanPlayerQuery)) {
                    $zpanel->generateWebLog($conn, '1', $targetUserNum, 'player unbanned', 'player unbanned by admin');
                    $returnSuccess = (defined('S_ACCOUNT_UNBANNED') ? S_ACCOUNT_UNBANNED : 'Account has been successfully unbanned.');
                    echo '<a href="?url=manager_account/manage-account" class="btn btn-default" style="margin: 5px 0;">Return to players page</a>';
                } else {
                    $returnWarning = (defined('W_ACCOUNT_UNBANNED') ? W_ACCOUNT_UNBANNED : 'Failed to unban account or account already unbanned.');
                }
            } else {
                $returnError = (defined('E_USER_NOT_FOUND') ? E_USER_NOT_FOUND : 'User not found for unbanning.');
            }
            break;

        default:
            $returnError = (defined('E_INVALIDVALUE') ? E_INVALIDVALUE : 'Invalid action specified.');
            break;
    }
}else{

// Fetch and display player data if not a ban/unban confirmation/execution
$resPlayer = null;
$selectCashDataFetch = null;
$selectGemDataFetch = null; // Assuming GemData is distinct from CashData or points
$selectTpointDataFetch = null;
$selectAuthDataFetch = null;
$selectWebAuthDataFetch = null;

if (!isset($banAction) || ($banAction !== 'wait' && $banAction !== 'unban-wait' && $banAction !== 'true' && $banAction !== 'unban')) {

// get data info
$selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$userNum'";
$selectPlayerDataParam = array();
$selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
    
if ($selectPlayerDataQuery && sqlsrv_has_rows($selectPlayerDataQuery)) {
    $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
    $userId = $resPlayer['UserNum'] ?? 0;


            // get cash account
        $selectCashData = "SELECT * FROM " . DATABASE_CCA . ".dbo.CashAccount WHERE UserNum = '$resPlayer[UserNum]'";
        $selectCashDataParam = array();
        $selectCashDataQuery = sqlsrv_query($conn, $selectCashData, $selectCashDataParam);
        $selectCashDataFetch = sqlsrv_fetch_array($selectCashDataQuery, SQLSRV_FETCH_ASSOC);

        $selectGemData = "SELECT * FROM " . DATABASE_SV . ".dbo.cabal_forcegem_table WHERE UserNum = '$resPlayer[UserNum]'";
        $selectGemDataParam = array();
        $selectGemDataQuery = sqlsrv_query($conn, $selectGemData, $selectGemDataParam);
        $selectGemDataFetch = sqlsrv_fetch_array($selectGemDataQuery, SQLSRV_FETCH_ASSOC);

        $selectTpointData = "SELECT * FROM " . DATABASE_NBL . ".dbo.Point WHERE UserNum = '$resPlayer[UserNum]'";
        $selectTpointDataParam = array();
        $selectTpointDataQuery = sqlsrv_query($conn, $selectTpointData, $selectTpointDataParam);
        $selectTpointDataFetch = sqlsrv_fetch_array($selectTpointDataQuery, SQLSRV_FETCH_ASSOC);
        
        $selectAuthData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_charge_auth WHERE UserNum = '$resPlayer[UserNum]'";
        $selectAuthDataParam = array();
        $selectAuthDataQuery = sqlsrv_query($conn, $selectAuthData, $selectAuthDataParam);
        $selectAuthDataFetch = sqlsrv_fetch_array($selectAuthDataQuery, SQLSRV_FETCH_ASSOC);
        
        $seleteWebAuthData = "SELECT * FROM ".DATABASE_WEB.".dbo.WEB_User_Info WHERE UserNum = '$resPlayer[UserNum]'";
        $selectWebAuthDataParam = array();
        $selectWebAuthDataQuery = sqlsrv_query($conn, $seleteWebAuthData, $selectWebAuthDataParam);
        $selectWebAuthDataFetch = sqlsrv_fetch_array($selectWebAuthDataQuery, SQLSRV_FETCH_ASSOC);



        $ExpireDate = date('Y-m-d H:i:s', strtotime($selectAuthDataFetch["ExpireDate"]));
        $today = date("Y-m-d H:i:s");    
    
        } else {
            $returnWarning = W_PLAYER_NOT_FOUND;
        }
} // ปิด if ที่บรรทัด 104

?>
<?php if (isset($returnSuccess)) { ?>
<div class="alert alert-success">
    <?php echo $returnSuccess; ?>
</div>
<?php } elseif (isset($returnWarning)) { ?>
<div class="alert alert-warning">
    <?php echo $returnWarning; ?>
</div>
<?php } elseif (isset($returnError)) { ?>
<div class="alert alert-danger">
    <?php echo $returnError; ?>
</div>
<?php } ?>
<style>
/* Modern Clean Design CSS */
.player-profile-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 30px;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
    transition: transform 0.3s ease;
}

.avatar-img:hover {
    transform: scale(1.05);
}

.status-dot {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
}

.status-dot.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background: #6c757d;
}

.profile-info {
    flex: 1;
    min-width: 300px;
}

.player-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: white;
}

.player-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.user-type {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.user-type.developer {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.user-type.player {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-num {
    padding: 5px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
}

.status-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    border: none;
}

.status-badge.active {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-badge.banned {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-badge.online {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-badge.offline {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.profile-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: flex-start;
}

.action-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn.primary {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.action-btn.warning {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.action-btn.success {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.action-btn.danger {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.content-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.info-label {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 120px;
    text-align: center;
    margin-right: 15px;
}

.info-value {
    font-weight: 600;
    color: #2c3e50;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Character Section Styles */
.character-profile {
    margin-bottom: 30px;
}

.character-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 15px;
    color: white;
}

.character-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.character-info {
    flex: 1;
}

.character-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: white;
}

.character-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.character-class,
.character-level {
    padding: 4px 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    font-size: 0.85rem;
    font-weight: 600;
}

.character-status {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.nation-badge {
    padding: 4px 10px;
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    border-radius: 10px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Currency Cards */
.currency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.currency-card {
    background: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.currency-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.currency-card.cash::before {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.currency-card.bonus::before {
    background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.currency-card.rpoint::before {
    background: linear-gradient(90deg, #11998e 0%, #38ef7d 100%);
}

.currency-card.tpoint::before {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.currency-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.currency-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.currency-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #2c3e50;
}

.currency-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-actions {
        justify-content: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .player-meta {
        justify-content: center;
    }

    .character-header {
        flex-direction: column;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .currency-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.account-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.account-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.info-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.info-card .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    padding: 20px;
}

.info-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: #e9ecef;
    border-left-color: #28a745;
    transform: translateX(5px);
}

.info-label {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.85rem;
    font-weight: 600;
    min-width: 120px;
    text-align: center;
    display: inline-block;
}

.character-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.character-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.character-card .card-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: none;
    color: white;
    padding: 20px;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.action-btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
}

.btn-gradient-danger {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    border: none;
    color: white;
}

.btn-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    color: white;
}
</style>
<div class="subheader fade-in">
    <h1 class="subheader-title">
        <i class='subheader-icon fal fa-user-edit'></i> Account Manager
        <small>
            จัดการและแก้ไขข้อมูลบัญชีผู้เล่น
        </small>
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_account/manage-account" class="btn btn-outline-primary action-btn">
            <i class="fal fa-arrow-left mr-2"></i>กลับรายการ
        </a>
    </div>
</div>
<!-- Modern Clean Layout -->
<div class="container-fluid">
    <!-- Player Profile Section -->
    <?php include "user-profile.php"; ?>

    <!-- Main Content Grid -->
    <?php include "user-info.php"; ?>
</div>
<?php include "character-info.php"; ?>

<?php } ?>