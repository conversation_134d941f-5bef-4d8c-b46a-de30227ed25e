<?php 
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code. 
 */
$user->restrictionUser(true, $conn); ?>
<section class="panel">
<div class="panel-body">
<h2><?php echo PT_MANAGECATEGORY; ?></h2>
<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                $getDelete = filter_input(INPUT_GET, 'delete', FILTER_DEFAULT);

                // If there is a variable and delete is not empty then execute the function
                if (isset($getDelete) && !(empty($getDelete))) {
                    if ($getDelete == "wait") {
                        echo '<p class="text-red text-bolder">Are you sure you want to delete?</p>';
                        echo '<a href="?url=helpdesk/a/edit-category&id=' . $getCategoryID . '&delete=true" class="btn btn-danger">Yes, delete</a>';
                    } else if ($getDelete == "true") {
                        // delete category
                        $deleteCategory = "DELETE FROM WEB_H_Category WHERE id = ?";
                        $deleteCategoryParam = array($getCategoryID);
                        $deleteCategoryQuery = sqlsrv_query($conn, $deleteCategory, $deleteCategoryParam);
                        if (sqlsrv_rows_affected($deleteCategoryQuery)) {
                            $returnSuccess = S_CATEGORY_DELETED;
                            echo '<a href="?url=helpdesk/a/category" class="btn btn-default" style="margin: 5px 0;">Return to Category page</a>';
                        } else {
                            $returnWarning = W_CATEGORY_DELETE;
                        }
                    } else if ($getDelete != "wait" || $getDelete != "true") {
                        $returnError = "You try pass a value to variable dont valid!";
                    }
                } else {
                    // get server log by ID
                    $selectCategory = "SELECT * FROM WEB_H_Category WHERE id = '$getCategoryID'";
                    $$selectClanParam = array();
                    $selectCategoryOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectCategoryQuery = sqlsrv_query($conn, $selectCategory, $$selectClanParam, $selectCategoryOpt);
                    $selectCategoryRows = sqlsrv_num_rows($selectCategoryQuery);
                    $selectCategoryFetch = sqlsrv_fetch_array($selectCategoryQuery, SQLSRV_FETCH_ASSOC);

                    // condition
                    if (!$selectCategoryRows) {
                        $returnWarning = W_H_CATEGORY_NOT_FOUND;
                    } else {

                        if (isset($_POST['btn_savechange'])) {
                            // variables access
                            $inputName = strip_tags(trim($_POST['input_name']));
                            $inputStatus = strip_tags(trim($_POST['input_status']));

                            echo '<pre>';
                            var_dump($_POST);
                            echo '</pre>';

                            if (empty($inputName)) {
                                $returnWarning = W_H_EMPTY_NAME;
                            } else {

                                // condition
                                if ($inputStatus == '-1') {
                                    $inputStatus = '1';
                                }

                                // update UsersData
                                $updateCategory = "UPDATE WEB_H_Category SET name = '$inputName', status = '$inputStatus' WHERE id = '$getCategoryID'";
                                $updateCategoryParam = array();
                                $updateCategoryQuery = sqlsrv_query($conn, $updateCategory, $updateCategoryParam);

                                if (sqlsrv_rows_affected($updateCategoryQuery)) {
                                    // generate log
                                    $zpanel->generateWebLog($conn, '2', $getCustomerID, 'helpdesk category updated', 'category was successfully updated!');

                                    // redirect
                                    header('Location: ?url=helpdesk/a/category&action=update');
                                } else {
                                    $returnError = E_CATEGORY;
                                }
                            }
                        }
                    }
                
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
                <form method="post" enctype="multipart/form-data">
                    <h2 class="text-red">Edit Category</h2>
                    <hr>
                    <div class="col-lg-12">
                        <div class="form-group">
                            <h4 class="text-red">Name</h4>
                            <input class="form-control" type="text" name="input_name" value="<?php echo $selectCategoryFetch['name']; ?>">
                        </div>

                        <div class="form-group">
                            <h4 class="text-red">Status</h4>
                            <select class="form-control" name="input_status">
                                <option value="-1">Select an status ...</option>
                                <option value="0">Deactivated</option>
                                <option value="1">Activated</option>
                            </select>
                            <small class="help-block text-red">Default status is <strong>Active</strong> &middot; Status actual is <strong><?php echo $status = ($selectCategoryFetch['status'] == '1' ? 'Activated' : ($selectCategoryFetch['status'] == '0' ? 'Deactivated' : 'Unknown status')); ?></strong></small>
                        </div>

                        <div class="form-group">
                            <input type="submit" name="btn_savechange" class="btn btn-success btn-block" value="<?php echo B_SAVECHANGES; ?>">
                        </div>
                    </div>
                </form>
                <?php } ?>
            </div>
        </div>
    </div>
</div>
</div>
</section>
