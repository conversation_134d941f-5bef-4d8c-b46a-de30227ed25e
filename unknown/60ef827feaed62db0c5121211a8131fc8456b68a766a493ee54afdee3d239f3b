/*
Name: 			Tables / Advanced - Examples
Written by: 	<PERSON><PERSON> Themes - (http://www.okler.net)
Theme Version: 	1.7.0
*/

(function($) {

    'use strict';
    var datatableInit = function() {

        $('#datatable-default').DataTable({
            "order": [
                [3, "asc"]
            ]
        });


    };
    var datatableInitcapalla = function() {

        $('#datatable-default-capalla').dataTable();

    };
    var datatableInitpocyon = function() {

        $('#datatable-default-pocyon').dataTable();

    };
    var datatableInitclans = function() {

        $('#datatable-default-clans').dataTable();

    };
    $(function() {
        datatableInit();
        datatableInitcapalla();
        datatableInitpocyon();
        datatableInitclans();
    });

}).apply(this, [jQuery]);