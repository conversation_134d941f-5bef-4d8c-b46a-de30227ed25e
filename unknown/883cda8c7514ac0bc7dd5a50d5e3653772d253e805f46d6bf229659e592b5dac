<?php $zpanel->checkSession(true); ?>
<?php if ($zpanel->getConfigByValue('Donate', 'value', $conn) == '0'){ echo '<div class="alert alert-info fade in nomargin">
										<button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
										<h4>Under Construction!</h4>
										<strong>Under Construction !</strong> หน้าเว็บนี้ ? <a href="" class="alert-link">ปิดปรับปรุงขั่วคราว</a> ขออภัยในความสดวก.
										<p>
                                            <a href="home.php" class="btn btn-info mt-xs mb-xs">Yes, ไปหน้าแรก</a>
											<button class="btn btn-default mt-xs mb-xs" type="button">Not convinced yet</button>
										</p>
                                    </div>'; }else{; ?>

<header class="page-header">
    <h2>เติมเงิน True Monney</h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>หน้ารวมข้อมูล</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
<div class="row">
    <div class="col-md-12">
        <section class="panel panel-featured panel-featured-primary">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
                </div>

                <h2 class="panel-title">เติมเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="col-lg-12">
                    <div class="alert alert-warning"><i class="fa fa-bolt"></i>
                        เติมบัตรเสร็จโปรดรอประมาณ 5 นาที ถ้าบัตรมีปัญหาติดต่อ GM ตรวจสอบ</div>
                    <div class="col-md-4">
                        <?php    
                                            $formRedeem = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                                                    if (isset($formRedeem) && $formRedeem['btn_redeem']) {
                                                        $getID = $userLogin->recUserAccount('ID', $conn);
                                                        $selectCodeData = "SELECT * FROM WEB_Tmoney WHERE user_no = '$getID'";
                                                        $selectCodeDataParam = array();
                                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectCodeData, $selectCodeDataParam);
                                                        $selectCodeDataFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);


                                                        
                                                                $selectShow= "SELECT * FROM WEB_Promotion_show where id = '1'";
                                                                $selectShowParam = array();
                                                                $selectShowOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                                                                $selectShowQuery = sqlsrv_query($conn, $selectShow, $selectShowParam, $selectShowOption);
                                                                $selectNumRowShow = @sqlsrv_num_rows($selectShowQuery);
                                                                $resUserShow = sqlsrv_fetch_array($selectShowQuery, SQLSRV_FETCH_ASSOC);


                                                    $countReferral = "SELECT * FROM WEB_Tmoney WHERE user_no = ".$selectCodeDataFetch['user_no']." AND status = '0'";
                                                    $countReferralOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                                                    $countReferralQuery = sqlsrv_query($conn, $countReferral, array(), $countReferralOpt);
                                                    $row = @sqlsrv_num_rows($countReferralQuery);
                                                    $countstatus0 = refill_countcards($conn, $getID,'0') ? refill_countcards($conn, $getID,'0') : '0';
                                                    $countstatus1 = refill_countcards2($conn, $getID,'1') ? refill_countcards2($conn, $getID,'1') : '0';
                                                    $countstatus2 = refill_countcards3($conn, $formRedeem['code'],'0','1') ? refill_countcards3($conn, $formRedeem['code'],'0','1') : '0';


                                                    $selectcode = sqlsrv_query($conn,"SELECT * FROM WEB_Tmoney WHERE password = '".$formRedeem['code']."' AND (status = 0 OR status = 1)",array(),array("Scrollable" => SQLSRV_CURSOR_KEYSET));
                                                    $selectcodeFetch = @sqlsrv_num_rows($selectcode);
                                                        
                                                        if (empty($formRedeem['code'])) {
                                                            $returnWarning = 'โปรดกรอกรหัสอ้างอิง รหัสบัตรเงินสด';
                                                        } else if (misc_parsestring($formRedeem['code'],'0123456789') == FALSE || strlen($formRedeem['code']) != 14) {
                                                            $returnWarning = 'รหัสบัตรเงินสดที่ระบุมีรูปแบบที่ไม่ถูกต้อง';
                                                        }else if ($countstatus2 == 1){
                                                            $returnWarning = 'รหัสบัตรเงินสดที่ระบุ ถูกใช้งานไปแล้ว';
                                                        }else if ($countReplied >=3){
                                                            $returnWarning = 'ท่านยังมีรหัสบัตรเงินสดที่รอการตรวจสอบอยู่';    
                                                        }else if ($countstatus1 >= 3){
                                                            $returnWarning = 'ท่านเติมเงินผิดหลายครั้ง ระบบระงับการเติมเงินเป็นเวลา 24 ชั่วโมง';    
                                                        } else {
                                                            
                                                            if(($tmpay_ret = refill_sendcard($conn,$getID,$formRedeem['code'])) !== TRUE){
                                                                $returnWarning = 'ขออภัย ขณะนี้ระบบ TMPAY.NET ขัดข้อง กรุณาติดต่อเจ้าหน้าที่ (Error: ' . $tmpay_ret . ') ';
                                                                }
                                                                else
                                                                {
                                                                $returnSuccess = 'ได้รับข้อมูลบัตรเงินสดเรียบร้อย  กรุณารอการตรวจสอบจากระบบ';
															    $zpanel->generateWebLog($conn, '4',$userLogin->recUserAccount('UserNum', $conn), 'เติมเงิน', "ไอดี {$getID} รหัสบัตร {$formRedeem['code']}");

                                                                }
                                                        }
                                            }
                                                        ?>

                        <?php if (isset($returnWarning)) { ?>
                        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                        <?php } elseif (isset($returnError)) { ?>
                        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                        <?php } elseif (isset($returnSuccess)) { ?>
                        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                        <?php } ?>
                        <form method="post" action="">
                            <div class="form-group input-group">
                                <span class="input-group-addon"><i class="fa fa-credit-card"></i></i></span>
                                <input type="text" id="code" name="code" data-plugin-maxlength="" maxlength="14"
                                    class="form-control" data-toggle="tooltip" data-title="กรอกรหัสบัตรเงินสด!"
                                    title="กรอกรหัสบัตรเงินสด!" placeholder="รหัสบัตรเงินสด">
                            </div>
                            <div class="form-group">
                                <input type="submit" name="btn_redeem" class="btn btn-success" value="เติมบัตร">
                                <button id="portletRefresh" type="button" class="mb-1 mt-1 mr-1 btn btn-default"><i
                                        class="fa fa-refresh"></i>
                                    อัพเดดข้อมูล</button>
                            </div>
                        </form>


                    </div>

                </div>

                <div class="col-md-12">
                    <h2>ประวัติ <small>ข้อมูลการเติมเงิน 10 รายการล่าสุด</small></h2>
                    <?php
						 $ID = $userLogin->recUserAccount('ID', $conn);
                        $selectUserChars = "SELECT top 10* FROM WEB_Tmoney WHERE user_no = '$ID' ORDER BY added_time desc";
                        $selectUserCharsParam = array();
                        $selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
                        $selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);
						if ($selectNumRowsChars) {
                            while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {

                                $payment =  date('Y/m/d H:i', strtotime($resUserChars['added_time']));
                                $propayment =  date('Y/m/d H:i',strtotime($zpanel->getConfigByValue('Pro', 'value', $conn)));

                                ?>
                    <p class="well warning well-sm">
                        <label class="label label-primary text-red padd-sm" data-toggle="tooltip" data-placement="top"
                            title="เวลา!"
                            data-original-title="เวลา!"></i><?php echo date('d/m/Y H:i', strtotime($resUserChars['added_time'])); ?></label>
                        <label class="label label-success text-red padd-sm" data-toggle="tooltip" data-placement="top"
                            title="Code!"
                            data-original-title="Code!"></i><?php echo substr($resUserChars['password'],0,10),"..."; ?></label>
                        ราคา: <?php echo $userLogin->statustrue($resUserChars['amount']); ?>
                        : <?php echo $userLogin->statustruscard($resUserChars['status']); ?>
                    </p>

                    <?php
                            }
                        }
                        ?>
                </div>
            </div>
        </div>
    </section>
    <?php } ?>