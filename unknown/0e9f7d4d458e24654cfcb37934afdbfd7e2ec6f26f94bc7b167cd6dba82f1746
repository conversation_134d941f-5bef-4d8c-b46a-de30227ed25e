<?php
// ไฟล์ debug สำหรับตรวจสอบ Character Usage Logs
session_start();
if (!isset($_SESSION['userLogin'])) {
    echo "ต้อง login ก่อน";
    exit;
}

require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

echo "<h2>Debug Character Usage Logs System</h2>";

// ทดสอบการเชื่อมต่อ
echo "<h3>1. ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";
if ($conn) {
    echo "<p style='color: green;'>✅ เชื่อมต่อสำเร็จ</p>";
} else {
    echo "<p style='color: red;'>❌ เชื่อมต่อล้มเหลว</p>";
    exit;
}

// ตรวจสอบตาราง WEB_Redeem_CodesUsed_Character
echo "<h3>2. ตรวจสอบตาราง WEB_Redeem_CodesUsed_Character</h3>";
$checkTableSql = "SELECT COUNT(*) as count FROM sysobjects WHERE name='WEB_Redeem_CodesUsed_Character' AND xtype='U'";
$checkTableStmt = sqlsrv_query($conn, $checkTableSql);

if ($checkTableStmt) {
    $result = sqlsrv_fetch_array($checkTableStmt, SQLSRV_FETCH_ASSOC);
    if ($result['count'] > 0) {
        echo "<p style='color: green;'>✅ ตาราง WEB_Redeem_CodesUsed_Character มีอยู่แล้ว</p>";
        
        // ตรวจสอบโครงสร้างตาราง
        echo "<h4>โครงสร้างตาราง:</h4>";
        $structureSql = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'WEB_Redeem_CodesUsed_Character'";
        $structureStmt = sqlsrv_query($conn, $structureSql);
        
        if ($structureStmt) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th></tr>";
            while ($row = sqlsrv_fetch_array($structureStmt, SQLSRV_FETCH_ASSOC)) {
                echo "<tr><td>{$row['COLUMN_NAME']}</td><td>{$row['DATA_TYPE']}</td><td>{$row['IS_NULLABLE']}</td></tr>";
            }
            echo "</table>";
        }
        
        // ตรวจสอบข้อมูลในตาราง
        echo "<h4>ข้อมูลในตาราง:</h4>";
        $dataSql = "SELECT COUNT(*) as total_records FROM WEB_Redeem_CodesUsed_Character";
        $dataStmt = sqlsrv_query($conn, $dataSql);
        
        if ($dataStmt) {
            $dataResult = sqlsrv_fetch_array($dataStmt, SQLSRV_FETCH_ASSOC);
            echo "<p>จำนวนรายการทั้งหมด: <strong>{$dataResult['total_records']}</strong></p>";
            
            if ($dataResult['total_records'] > 0) {
                echo "<h4>ตัวอย่างข้อมูล (5 รายการล่าสุด):</h4>";
                $sampleSql = "SELECT TOP 5 * FROM WEB_Redeem_CodesUsed_Character ORDER BY used_date DESC";
                $sampleStmt = sqlsrv_query($conn, $sampleSql);
                
                if ($sampleStmt) {
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>ID</th><th>CodeID</th><th>CustomerID</th><th>Code</th><th>Character ID</th><th>Channel IDX</th><th>Used Date</th></tr>";
                    while ($row = sqlsrv_fetch_array($sampleStmt, SQLSRV_FETCH_ASSOC)) {
                        $usedDate = $row['used_date'];
                        if ($usedDate instanceof DateTime) {
                            $usedDate = $usedDate->format('Y-m-d H:i:s');
                        }
                        echo "<tr>";
                        echo "<td>{$row['id']}</td>";
                        echo "<td>{$row['CodeID']}</td>";
                        echo "<td>{$row['CustomerID']}</td>";
                        echo "<td>{$row['code']}</td>";
                        echo "<td>{$row['character_id']}</td>";
                        echo "<td>{$row['channel_idx']}</td>";
                        echo "<td>{$usedDate}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p style='color: red;'>❌ ไม่สามารถดึงตัวอย่างข้อมูลได้</p>";
                    echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลในตาราง</p>";
            }
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ ตาราง WEB_Redeem_CodesUsed_Character ไม่มี กำลังสร้าง...</p>";
        
        // สร้างตาราง
        $createSql = "CREATE TABLE [dbo].[WEB_Redeem_CodesUsed_Character] (
            [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            [CodeID] int NOT NULL,
            [CustomerID] int NULL,
            [code] nvarchar(20) COLLATE Thai_CI_AS NULL,
            [character_id] int NULL,
            [channel_idx] int NULL,
            [used_date] datetime DEFAULT getdate() NULL
        )";
        
        $createStmt = sqlsrv_query($conn, $createSql);
        if ($createStmt) {
            echo "<p style='color: green;'>✅ สร้างตาราง WEB_Redeem_CodesUsed_Character สำเร็จ</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถสร้างตารางได้</p>";
            echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบตารางได้</p>";
    echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
}

// ทดสอบ API
echo "<h3>3. ทดสอบ API list_usage_logs</h3>";
echo "<p><a href='class_module/character_redeem_code_api.php' target='_blank'>ทดสอบ API โดยตรง</a></p>";

// ทดสอบการเพิ่มข้อมูลตัวอย่าง
echo "<h3>4. เพิ่มข้อมูลตัวอย่าง</h3>";
if (isset($_GET['add_sample'])) {
    $insertSql = "INSERT INTO WEB_Redeem_CodesUsed_Character (CodeID, CustomerID, code, character_id, channel_idx, used_date) VALUES (?, ?, ?, ?, ?, GETDATE())";
    $insertStmt = sqlsrv_query($conn, $insertSql, [999, 12345, 'TEST-CHAR-CODE', 67890, 1]);
    
    if ($insertStmt) {
        echo "<p style='color: green;'>✅ เพิ่มข้อมูลตัวอย่างสำเร็จ</p>";
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มข้อมูลตัวอย่างได้</p>";
        echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
    }
    echo "<p><a href='?'>รีเฟรชหน้า</a></p>";
} else {
    echo "<p><a href='?add_sample=1'>คลิกเพื่อเพิ่มข้อมูลตัวอย่าง</a></p>";
}

echo "<hr>";
echo "<p><a href='character_redeem_code_generator.php'>กลับไปหน้าหลัก</a></p>";
?>
