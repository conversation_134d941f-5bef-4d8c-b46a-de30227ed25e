<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> แอดไอเท็ม && ตรวจสอบข้อมูลเกมส์
        <small>
        เป็นระบบ แอดไอเท็มรูปแบบหน้าเว็บ และค้นหารายชื่อไอเท็ม และอื่นๆอีกมากมาย!
        </small>
    </h1>
</div>
                    <div class="row">
                            <div class="col-xl-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                            MyCashItem <span class="fw-300"><i>Table</i></span>
                                        </h2>
                                        <div class="panel-toolbar">
                                            <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                                            <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                                            <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                                        </div>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <!-- tabs -->
                                            <ul class="nav nav-tabs" role="tablist">
                                                <li class="nav-item">
                                                    <a class="nav-link active p-3" data-toggle="tab" href="#tab_default-1" role="tab">
                                                        <i class="fal fa-cog text-success"></i>
                                                        <span class="hidden-sm-down ml-1">แอดไอเท็ม</span>
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link p-3" data-toggle="tab" href="#tab_default-2" role="tab">
                                                        <i class="fal fa-table text-info"></i>
                                                        <span class="hidden-sm-down ml-1">Dungeon List</span>
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link p-3" data-toggle="tab" href="#tab_default-3" role="tab">
                                                        <i class="fal fa-table text-info"></i>
                                                        <span class="hidden-sm-down ml-1">Quest List</span>
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link p-3" data-toggle="tab" href="#tab_default-4" role="tab">
                                                        <i class="fal fa-table text-info"></i>
                                                        <span class="hidden-sm-down ml-1">Monster List</span>
                                                    </a>
                                                </li>
                                            </ul>
                                            <!-- end tabs -->
                                            <!-- tab content -->
                                            <div class="tab-content pt-4">
                                                <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                                                    <!-- row -->
                                                    <div class="row">
                                                        <div class="col-xl-6">
                                                        
                                                        <div class="alert alert-info">
                                                                    <span  span class="h5">How it works</span>
                                                                    <br> - ให้ตรวจสอบไอเท็มได้ที่ช่อง  <code>Cash Inventory</code>
                                                                    <br>
                                                                    <strong>#รายระเอียด</strong>
                                                                    <br> - ให้ตรวจสอบ <code>UserID</code> ก่อนโดยการนำ <code>UserID</code> ไปกรอกในช่อง <code>UserID เช็ค</code>
                                                                    <br> - <code>Code Item</code> คือ รหัสไอเท็ม
                                                                    <br> - <code>Option</code> คือ ออฟชั่น
                                                                    <br> - <code>DurationIdx</code> คือ วันใช้งาน/วันหมดอายุ
                                                                    <br> - <code>Upgrade</code> คือ อัตราการตีบวกไอเท็ม ใช้ได้กับไอเท็มบางชนิดเท่านั้น
                                                                    <br> - <code>Quantity</code> คือ จำนวนครั้ง ในการแอดไอเท็ม
                                                                </div>
                                                        <div id="panel-1" class="panel">
                                                            <div class="panel-container show">
                                                                <div class="panel-content">		
                                                            <?php
                                                            if (isset($_POST['btn_savechange_checking'])) {
                                                                        // variable
                                                                        $inputid = strip_tags(trim($_POST['input_checkid']));
                                                                        // condition
                                                                        if (empty($inputid)) {
                                                                        //$returnWarning = '<div class="alert alert-success">
                                                                        //<strong>Success!</strong> You should <a href="#" class="alert-link">read this message</a>.
                                                                    // </div>';

                                                                        echo '<script type="text/javascript">';
                                                                        echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ตรวจสอบไม่พบข้อมูลบัญชีผู้ใช้นี้!","error");';
                                                                        echo '});</script>';
                                                                    
                                                                        }else{
                                                                    // get conversion info
                                                                    $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE ID  = '$inputid'";
                                                                    $selectauthParam = array();
                                                                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                                                                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                                                                    $UserNum         = $selectauthFetch['UserNum'];
                                                                    $ID              = $selectauthFetch['ID'];
                                                                    if ($inputid == $ID) {
                                                                    //unset($UserNum, $inputPassword, $inputRePassword, $inputEmail, $inputphone);
                                                                    //$returnSuccess = "ข้อมูลผู้ใช้นี้ UserNum = ({$UserNum}) ID = ({$ID})";

                                                                                echo '<script type="text/javascript">';
                                                                                echo 'setTimeout(function () { Swal.fire("success !!!","ข้อมูลผู้ใช้นี้ UserNum = '.$UserNum.' ID = '.$ID.'","success");';
                                                                                echo '});</script>';
                                                                    } else {
                                                                    //$returnError = "ERROR :: ระบบผิดพลาด";
                                                                                echo '<script type="text/javascript">';
                                                                                echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ตรวจสอบไม่พบข้อมูลบัญชีผู้ใช้นี้!","error");';
                                                                                echo '});</script>';
                                                                    }
                                                                }
                                                            }

                                                            ?>
                                                        <?php if (isset($returnSuccess)) { ?>
                                                        <div class="alert alert-success j_alert"><?php echo $returnSuccess; ?></div>
                                                        <?php } elseif (isset($returnWarning)) { ?>
                                                        <div class="alert alert-warning j_alert"><?php echo $returnWarning; ?></div>
                                                        <?php } elseif (isset($returnError)) { ?>
                                                        <div class="alert alert-danger j_alert"><?php echo $returnError; ?></div>
                                                        <?php } ?>
                                                        <form role="form" method="post"  enctype="multipart/form-data">
                                                                <div class="form-row">
                                                                    <div class="col-md-4 mb-4">
                                                                        <label class="form-label" for="">UserID เช็ค</label>
                                                                        <input type="text" name="input_checkid" class="form-control" placeholder="ตรวจสอบไอดี" value="<?php if (isset($inputid)) { echo $inputid; } ?>" required="กรอกไอดีเพื่อตรวจสอบข้อมูล">
                                                                    </div>
                                                                <div class="col-md-8 mb-4">
                                                                    <label class="form-label" for="">ตรวจสอบ </label>
                                                                    <button type="submit" name="btn_savechange_checking" class="mb-1 btn btn-danger btn-block">โหลดข้อมูลไอดี</button>
                                                                    </div>
                                                                </div>                          
                                                            </form>
                                                            <form role="form" method="post" name="j_add_adminitems" enctype="multipart/form-data">

                                                                <div class="col-sm-12">
                                                                    <div class="form-group row">
                                                                        <label for="input_id" class="control-label">UserID: </label>
                                                                        <input type="text" name="input_id" class="form-control" placeholder="ไอดี" value="<?php if (isset($ID)) { echo $ID; } ?>" required="กรอกไอดีเพื่อตรวจสอบข้อมูล" readonly>
                                                                    </div>
                                                                </div>
                                                                
                                                            <div class="form-group row">
                                                                <div class="col-sm-3">
                                                                    <div class="form-group">
                                                                        <label for="input_Item" class="control-label">Code Item:</label>
                                                                        <input type="text" name="input_Item" class="form-control" id="uweight" placeholder=""
                                                                            pattern="[0-9]+$" value="1">
                                                                        <input type="hidden" class="form-control" id="units" value="4096">
                                                                        <input type="hidden" class="form-control" id="units2" value="528384">
                                                                        <input type="hidden" class="form-control" id="units3" value="1572864">
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-3">
                                                                    <div class="form-group">
                                                                        <label for="Option" class="control-label">Option: </label><code>{ ea }</code>
                                                                        <input type="text" name="input_Option" class="form-control" id="units" pattern="[0-9]+$"
                                                                            placeholder="" value="0">
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label for="input_Dur" class="control-label">DurationIdx: </label>
                                                                        <select data-plugin-selectTwo name="input_Dur" id="input_Dur" pattern="[0-9]+$"
                                                                            class="form-control populate placeholder"
                                                                            data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                                                                            <optgroup label="อายุ ชม.">
                                                                                <option value="31">ถาวร</option>
                                                                                <option value="1">1 ชม.</option>
                                                                                <option value="2">2 ชม.</option>
                                                                                <option value="3">3 ชม.</option>
                                                                                <option value="4">4 ชม.</option>
                                                                                <option value="5">5 ชม.</option>
                                                                                <option value="6">6 ชม.</option>
                                                                                <option value="7">10 ชม.</option>
                                                                                <option value="8">12 ชม</option>
                                                                                <option value="9">1 วัน</option>
                                                                                <option value="10">3 วัน</option>
                                                                                <option value="11">5 วัน</option>
                                                                                <option value="12">7 วัน</option>
                                                                                <option value="13">10 วัน</option>
                                                                                <option value="14">14 วัน</option>
                                                                                <option value="15">15 วัน</option>
                                                                                <option value="16">20 วัน</option>
                                                                                <option value="17">30 วัน</option>
                                                                                <option value="18">45 วัน</option>
                                                                                <option value="19">60 วัน</option>
                                                                                <option value="20">90 วัน</option>
                                                                                <option value="21">100 วัน</option>
                                                                                <option value="22">120 วัน</option>
                                                                                <option value="23">180 วัน</option>
                                                                                <option value="24">270 วัน</option>
                                                                                <option value="25">365 วัน</option>
                                                                                <option value="0">ไม่มีอายุ</option>
                                                                            </optgroup>
                                                                        </select>
                                                                    </div>
                                                                </div>

                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label for="input_Upgrade" class="control-label">Upgrade:
                                                                        </label>
                                                                        <select data-plugin-selectTwo name="input_Upgrade" id="input_Upgrade"
                                                                            pattern="[a-zA-Z0-9]+$" class="form-control populate placeholder"
                                                                            data-plugin-options='{ "placeholder": "Select Upgrade", "allowClear": true }'>
                                                                            <optgroup label="Upgrade ไม่ผูกไอดี">
                                                                                <option value="0">+0 </option>
                                                                                <option value="2">+1</option>
                                                                                <option value="4">+2</option>
                                                                                <option value="6">+3</option>
                                                                                <option value="8">+4</option>
                                                                                <option value="A">+5</option>
                                                                                <option value="C">+6</option>
                                                                                <option value="E">+7</option>
                                                                                <option value="10">+8</option>
                                                                                <option value="12">+9</option>
                                                                                <option value="14">+10</option>
                                                                                <option value="16">+11</option>
                                                                                <option value="18">+12</option>
                                                                                <option value="1A">+13</option>
                                                                                <option value="1C">+14</option>
                                                                                <option value="1E">+15</option>
                                                                                <option value="20">+16</option>
                                                                                <option value="22">+17</option>
                                                                                <option value="24">+18</option>
                                                                                <option value="26">+19</option>
                                                                                <option value="28">+20</option>
                                                                            </optgroup>
                                                                            <optgroup label="Upgrade ผูกไอดี">
                                                                                <option value="1">+0 binding</option>
                                                                                <option value="3">+1 binding</option>
                                                                                <option value="5">+2 binding</option>
                                                                                <option value="7">+3 binding</option>
                                                                                <option value="9">+4 binding</option>
                                                                                <option value="B">+5 binding</option>
                                                                                <option value="D">+6 binding</option>
                                                                                <option value="F">+7 binding</option>
                                                                                <option value="11">+8 binding</option>
                                                                                <option value="13">+9 binding</option>
                                                                                <option value="15">+10 binding</option>
                                                                                <option value="17">+11 binding</option>
                                                                                <option value="19">+12 binding</option>
                                                                                <option value="1B">+13 binding</option>
                                                                                <option value="1D">+14 binding</option>
                                                                                <option value="1F">+15 binding</option>
                                                                                <option value="21">+16 binding</option>
                                                                                <option value="23">+17 binding</option>
                                                                                <option value="25">+18 binding</option>
                                                                                <option value="27">+19 binding</option>
                                                                                <option value="29">+20 binding</option>
                                                                            </optgroup>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-2">
                                                                    <div class="form-group">
                                                                        <label for="input_quantity" class="control-label">Quantity:</label>
                                                                        <select data-plugin-selectTwo name="input_quantity" id="input_quantity"
                                                                            pattern="[0-9]+$" class="form-control populate placeholder"
                                                                            data-plugin-options='{ "placeholder": "Select quantity", "allowClear": true }'>
                                                                            <optgroup label="Quantity.">
                                                                                <option value="0">1 Terms.</option>
                                                                                <option value="1">2 Terms.</option>
                                                                                <option value="2">3 Terms.</option>
                                                                                <option value="3">4 Terms.</option>
                                                                                <option value="4">5 Terms.</option>
                                                                                <option value="5">6 Terms.</option>
                                                                                <option value="6">7 Terms.</option>
                                                                                <option value="9">10 Terms.</option>
                                                                                <option value="14">15 Terms</option>
                                                                                <option value="19">20 Terms</option>
                                                                            </optgroup>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-12">
                                                                    <div class="form-group">
                                                                    <label for="input_Item" class="control-label">Slot:</label>
                                                                        <div class="m-md slider-primary">
                                                                            <input name="input_Slot" id="slotitem" type="text" value="0" class="d-none" tabindex="-1" readonly="">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            
                                                                            <div class="col-sm-4">
                                                                                <div class="form-group">
                                                                                    <label for="input_Item" class="control-label">bind-id:</label>
                                                                                    <input type="text" class="form-control" pattern="[0-9]+$" placeholder="รหัสผูกมัดไอดี"
                                                                                        readonly="readonly" id="total">
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-sm-4">
                                                                                <div class="form-group ">
                                                                                    <label for="input_Item" class="control-label">bind-Char:</label>
                                                                                    <input type="text" class="form-control" pattern="[0-9]+$" placeholder="รหัสผูกมัดตัวละคร"
                                                                                        readonly="readonly" id="total2">
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-sm-4">
                                                                                <div class="form-group ">
                                                                                    <label for="input_Item" class="control-label">bind-equ:</label>
                                                                                    <input type="text" " class=" form-control" pattern="[0-9]+$" placeholder="รหัสผูกมัดเมื้อสวมใส่"
                                                                                        readonly="readonly" id="total3">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    
                                                                            <div class="col-sm-12">
                                                                                <div class="form-group row">
                                                                                    <button type="submit" name="btn_savechange" class="mb-1 mt-1 mr-1 btn btn-primary btn-block">ยืนยันไอเท็ม</button>
                                                                                    <input type="hidden" name="admin-user" value="<?php echo $userLogin->recUserAccount('ID', $conn); ?>">
                                                                                    <input type="hidden" name="admin-useridx" value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
                                                                                </div>
                                                                            </div>
                                                                        </form>
                                                                        </div> 
                                                                    </div> 
                                                                </div> 
                                                            </div> 
                                                                        <div class="col-xl-6">
                                                                            <div id="panel-3" class="panel">
                                                                                <div class="panel-container show">
                                                                                    <div class="panel-content">									
                                                                                        <div class="frame-heading">
                                                                                            <table id="dt-itemlist" class="table table-bordered table-hover table-striped w-100">
                                                                                                <thead>
                                                                                                    <tr>
                                                                                                        <th>ItemId</th>
                                                                                                        <th>ItemName</th>
                                                                                                    </tr>
                                                                                                </thead>
                                                                                                    <tfoot>
                                                                                                        <tr>
                                                                                                            <th>ItemId</th>
                                                                                                            <th>ItemName</th>
                                                                                                        </tr>
                                                                                                    </tfoot>
                                                                                            </table>                                
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                    </div>
                                                                <!-- end row -->
                                                                </div>
                                                                </div>
                                                            <div class="tab-pane fade" id="tab_default-2" role="tabpanel">
                                                                <!-- row -->
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                    <table id="dt-quest" class="table table-bordered table-hover table-striped w-100">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>QuestId</th>
                                                                                <th>QuesName</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tfoot>
                                                                            <tr>
                                                                                <th>QuestId</th>
                                                                                <th>QuesName</th>
                                                                            </tr>
                                                                        </tfoot>
                                                                    </table>
                                                                    </div>
                                                                </div> </div>
                                                                <!-- end row -->
                                                                <div class="tab-pane fade" id="tab_default-3" role="tabpanel">
                                                                    <!-- row -->
                                                                    <div class="row">
                                                                        <div class="col-12">
                                                                        <table id="dt-DGlist" class="table table-bordered table-hover table-striped w-100">
                                                                            <thead>
                                                                                <tr>
                                                                                    <th>item</th>
                                                                                    <th>level</th>
                                                                                    <th>dun</th>
                                                                                    <th>world</th>
                                                                                    <th>name</th>

                                                                                </tr>
                                                                            </thead>
                                                                            <tfoot>
                                                                                <tr>
                                                                                    <th>item</th>
                                                                                    <th>level</th>
                                                                                    <th>dun</th>
                                                                                    <th>world</th>
                                                                                    <th>name</th>
                                                                                </tr>
                                                                            </tfoot>
                                                                            </table>
                                                                        </div>
                                                                    </div> </div>
                                                                    <!-- end row -->
                                                <div class="tab-pane fade" id="tab_default-4" role="tabpanel">
                                                    <!-- row -->
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <table id="dt-monsterlist" class="table table-bordered table-hover table-striped w-100">
                                                                <thead>
                                                                    <tr>
                                                                        <th>MonsterId</th>
                                                                        <th>MonsterName</th>
                                                                    </tr>
                                                            </thead>
                                                                <tfoot>
                                                                    <tr>
                                                                        <th>MonsterId</th>
                                                                        <th>MonsterName</th>
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                        </div>
                                                    </div> </div>
                                                    <!-- end row -->


        
  
                     
             