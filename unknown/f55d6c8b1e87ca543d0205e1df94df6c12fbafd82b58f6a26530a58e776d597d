@font-face {
	font-family: 'meteocons';
	src:url('../fonts/meteoconsbc3c.eot?lbb2co');
	src:url('../fonts/meteoconsd41d.eot?#iefixlbb2co') format('embedded-opentype'),
		url('../fonts/meteoconsbc3c.woff?lbb2co') format('woff'),
		url('../fonts/meteoconsbc3c.ttf?lbb2co') format('truetype'),
		url('../fonts/meteoconsbc3c.svg?lbb2co#meteocons') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="wi-"], [class*=" wi-"] {
	font-family: 'meteocons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wi-sunrise:before {
	content: "\e600";
}

.wi-sun:before {
	content: "\e601";
}

.wi-moon:before {
	content: "\e602";
}

.wi-sun2:before {
	content: "\e603";
}

.wi-windy:before {
	content: "\e604";
}

.wi-wind:before {
	content: "\e605";
}

.wi-snowflake:before {
	content: "\e606";
}

.wi-cloudy:before {
	content: "\e607";
}

.wi-cloud:before {
	content: "\e608";
}

.wi-weather:before {
	content: "\e609";
}

.wi-weather2:before {
	content: "\e60a";
}

.wi-weather3:before {
	content: "\e60b";
}

.wi-lines:before {
	content: "\e60c";
}

.wi-cloud2:before {
	content: "\e60d";
}

.wi-lightning:before {
	content: "\e60e";
}

.wi-lightning2:before {
	content: "\e60f";
}

.wi-rainy:before {
	content: "\e610";
}

.wi-rainy2:before {
	content: "\e611";
}

.wi-windy2:before {
	content: "\e612";
}

.wi-windy3:before {
	content: "\e613";
}

.wi-snowy:before {
	content: "\e614";
}

.wi-snowy2:before {
	content: "\e615";
}

.wi-snowy3:before {
	content: "\e616";
}

.wi-weather4:before {
	content: "\e617";
}

.wi-cloudy2:before {
	content: "\e618";
}

.wi-cloud3:before {
	content: "\e619";
}

.wi-lightning3:before {
	content: "\e61a";
}

.wi-sun3:before {
	content: "\e61b";
}

.wi-moon2:before {
	content: "\e61c";
}

.wi-cloudy3:before {
	content: "\e61d";
}

.wi-cloud4:before {
	content: "\e61e";
}

.wi-cloud5:before {
	content: "\e61f";
}

.wi-lightning4:before {
	content: "\e620";
}

.wi-rainy3:before {
	content: "\e621";
}

.wi-rainy4:before {
	content: "\e622";
}

.wi-windy4:before {
	content: "\e623";
}

.wi-windy5:before {
	content: "\e624";
}

.wi-snowy4:before {
	content: "\e625";
}

.wi-snowy5:before {
	content: "\e626";
}

.wi-weather5:before {
	content: "\e627";
}

.wi-cloudy4:before {
	content: "\e628";
}

.wi-lightning5:before {
	content: "\e629";
}

.wi-thermometer:before {
	content: "\e62a";
}

.wi-compass:before {
	content: "\e62b";
}

.wi-none:before {
	content: "\e62c";
}

.wi-celsius:before {
	content: "\e62d";
}

.wi-fahrenheit:before {
	content: "\e62e";
}

