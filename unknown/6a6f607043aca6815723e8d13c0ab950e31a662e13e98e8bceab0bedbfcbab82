$(function() {
    /*
     * Developed by FDEV
     * Copyright 2015
     * zPANEL All rights reserved
     *
     * zpanel.js jQuery Controller
     * All returns is in english only
     */

    // global variables
    var url = "home/switch/mstudio.php";
    var baseUrl = "home.php";

    $("#reloader").click(function() {
        $("#content").load("#content");

    });

   


    $(document).ready(function() {
        var dataTable = $('#empTable_item').DataTable({
            'processing': true,
            'serverSide': true,
            'serverMethod': 'post',
            //'searching': false, // Remove default Search Control
            'ajax': {
                url: '_app/aj_loadid_id.php',
                type: "post",
                error: function() {
                    $("#employee_grid_processing").css("display", "none");
                }
            },
            'columns': [{
                    data: 'UserNum'
                },
                {
                    data: 'ID'
                },
                {
                    data: 'Email'
                }
            ]
        });

        $('#searchByName').keyup(function() {
            dataTable.draw();
        });

        $('#searchByGender').change(function() {
            dataTable.draw();
        });
    });

    // add timeonline acc
    $('form[name="j_add_adminitems"]').submit(function() {
        var formData = $(this).serialize() + '&action=adminitems';
        var formVar = $(this);
        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'Success:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'DATA SUCCESS',
                        text: 'System ADD Item\n ระบบได้แอดไอเท็มให้ user เรียบร้อยแล้ว\nโปรดตรวจสอบไอเท็มใน inventory',
                        type: 'success',
                        nonblock: {
                            nonblock: true,
                            nonblock_opacity: .2
                        }
                    });
                } else if (data == 'warning:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    Swal.fire({
                        icon: 'error',
                        title: 'ERROR',
                        text: 'ERROR ::ตรวจสอบไม่พบ บัญชีผู้ใช้นี้ ระบบผิดพลาด',
                        timer: 5000
                    })

                } else if (data == 'warning:checkitem') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'DATA WARNING',
                        text: 'ตรวจสอบไม่พบรหัสไอเท็ม\nกรุณาตรวจสอบใหม่อีกครั้ง',
                        nonblock: {
                            nonblock: true,
                            nonblock_opacity: .2
                        }
                    });
                } else if (data == 'warning:checkopt') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'DATA WARNING',
                        text: 'ตรวจสอบไม่พบออฟชั่น\nกรุณาตรวจสอบใหม่อีกครั้ง',
                        nonblock: {
                            nonblock: true,
                            nonblock_opacity: .2
                        }
                    });
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'DATA WARNING',
                        text: 'ตรวจสอบไม่พบบัญชีนี้\nกรุณาตรวจสอบใหม่อีกครั้ง',
                        nonblock: {
                            nonblock: true,
                            nonblock_opacity: .2
                        }
                    });

                }
            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });

    $('form[name="j_select_Voucher"]').submit(function() {
        var formData = $(this).serialize() + '&action=add_Voucher';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: 'success',
                        text: 'ระบบได้ส่งข้อมูลถึง GM เรียบร้อยแล้ว!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'successp1') {
                    Swal.fire({
                        icon: 'success',
                        title: 'success',
                        text: 'ระบบได้ส่งข้อมูลถึง Pack1 ถึง GM เรียบร้อยแล้ว!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'successp2') {
                    Swal.fire({
                        icon: 'success',
                        title: 'success',
                        text: 'ระบบได้ส่งข้อมูลถึง Pack2 ถึง GM เรียบร้อยแล้ว!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'successp3') {
                    Swal.fire({
                        icon: 'success',
                        title: 'success',
                        text: 'ระบบได้ส่งข้อมูลถึง Pack3 ถึง GM เรียบร้อยแล้ว!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'successp4') {
                    Swal.fire({
                        icon: 'success',
                        title: 'success',
                        text: 'ระบบได้ส่งข้อมูลถึง Pack4 ถึง GM เรียบร้อยแล้ว!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'error') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'ข้อมูลผิดพลาด!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'errorcash') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Cash ไม่พอที่จะชื้อ Voucher !',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'errordata') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'ข้อมูลผิดพลาดติดต่อ ระบบ database ไม่ได้!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                } else if (data == 'emptyerror') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'ไม่มีข้อมูล!',
                        timer: 3000
                    })
                    window.setTimeout(function() {
                        location.reload()
                    }, 3000)
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });

    $('form[name="j_add_donate"]').submit(function() {
        var formData = $(this).serialize() + '&action=add_donate';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Success',
                        text: 'ระบบทำรายการสำเร็จ ',
                        type: 'success'
                    });
                    window.setTimeout(function() {
                        location.reload()
                    }, 1000)
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Error',
                        text: 'ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด',
                        type: 'error'
                    });
                    window.setTimeout(function() {
                        location.reload()
                    }, 500)
                } else if (data == 'Error:check1') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'warning',
                        text: 'ข้อมูลผู้เล่นไม่ถูกต้องโปรดติดต่อ GM',
                        type: 'warning'
                    });
                    window.setTimeout(function() {
                        location.reload()
                    }, 1000)
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });
    $('form[name="j_add_bringer"]').submit(function() {
        var formData = $(this).serialize() + '&action=add_bringer';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Success',
                        text: 'ระบบทำรายการสำเร็จ ',
                        type: 'success'
                    });
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Error',
                        text: 'ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด',
                        type: 'error'
                    });
                } else if (data == 'Error:check1') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'warning',
                        text: 'ข้อมูลผู้เล่นไม่ถูกต้องโปรดติดต่อ GM',
                        type: 'warning'
                    });
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });
    $('form[name="j_recover_char"]').submit(function() {
        var formData = $(this).serialize() + '&action=recoverdel_char';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Success',
                        text: 'ระบบทำรายการสำเร็จ ',
                        type: 'success'
                    });
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Error',
                        text: 'ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด',
                        type: 'error'
                    });
                } else if (data == 'Error:check1') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'warning',
                        text: 'ข้อมูลผู้เล่นไม่ถูกต้องโปรดติดต่อ GM',
                        type: 'warning'
                    });
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });
    $('form[name="j_delete_itemsinv"]').submit(function() {
        var formData = $(this).serialize() + '&action=delete_itemsinv';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Success',
                        text: 'ระบบทำรายการสำเร็จ ลบของใน inventory เรียบร้อยแล้ว',
                        type: 'success'
                    });
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Error',
                        text: 'ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด',
                        type: 'error'
                    });
                } else if (data == 'Error:check1') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'warning',
                        text: 'ข้อมูลผู้เล่นไม่ถูกต้องโปรดติดต่อ GM',
                        type: 'warning'
                    });
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });


$('form[name="j_delete_mycashitem"]').submit(function() {
        var formData = $(this).serialize() + '&action=delete_mycashitem';
        var formVar = $(this);

        $.ajax({
            url: url,
            data: formData,
            type: 'POST',
            beforeSend: function() {
                formVar.find('.load').fadeIn("slow");
            },
            success: function(data) {
                if (data == 'success') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Success',
                        text: 'ระบบทำรายการสำเร็จ ลบของใน inventory เรียบร้อยแล้ว',
                        type: 'success'
                    });
                } else if (data == 'Error:check') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'Error',
                        text: 'ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด',
                        type: 'error'
                    });
                } else if (data == 'Error:check1') {
                    formVar.find('.j_alert').fadeOut("slow");
                    new PNotify({
                        title: 'warning',
                        text: 'ข้อมูลผู้เล่นไม่ถูกต้องโปรดติดต่อ GM',
                        type: 'warning'
                    });
                }

            },
            complete: function() {
                formVar.find('.load').fadeOut("slow");
            }
        });
        return false;
    });
});