/* #THEME COLOR (variable overrides)
========================================================================== */
/* #GLOBAL IMPORTS
========================================================================== */
/* #IMPORTS ~~
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by Dmitry Fadeyev (http://fadeyev.net)
    SASS port by Samuel Beek (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

  $color-white: hexToRGBString(#fff) => "255,255,255"
  $color-white: hexToRGBString(rgb(255,255,255)) => "255,255,255"
  $color-white: hexToRGBString(rgba(#fff,1)) => "255,255,255"
  
------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: contrast-ink($contrastvalue)
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* We will manually convert these primary colors to rgb for the dark mode option of the theme */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* custom file */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav footer */
/* nav parent level-0 */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.image-one {
    @extend %bg-image;
    background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

*/
.page-logo, .page-sidebar, .nav-footer, .bg-brand-gradient {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(89, 143, 186, 0.18)), to(transparent));
  background-image: linear-gradient(270deg, rgba(89, 143, 186, 0.18), transparent);
  background-color: #3955bc; }

/*
%shadow-hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
  }
}
*/
.btn-default {
  background-color: #f5f5f5;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f5f5f5), to(#f1f1f1));
  background-image: linear-gradient(to top, #f5f5f5, #f1f1f1);
  color: #444; }
  .btn-default:hover {
    border: 1px solid #c6c6c6; }
  .btn-default:focus {
    border-color: #b9c5ee !important; }
  .active.btn-default {
    background: #a5b3e9;
    color: #fff; }

.header-function-fixed .btn-switch[data-class="header-function-fixed"], .nav-function-fixed .btn-switch[data-class="nav-function-fixed"], .nav-function-minify .btn-switch[data-class="nav-function-minify"], .nav-function-hidden .btn-switch[data-class="nav-function-hidden"], .nav-function-top .btn-switch[data-class="nav-function-top"], .footer-function-fixed .btn-switch[data-class="footer-function-fixed"], .nav-mobile-push .btn-switch[data-class="nav-mobile-push"], .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"], .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"], .mod-main-boxed .btn-switch[data-class="mod-main-boxed"], .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"], .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"], .mod-pace-custom .btn-switch[data-class="mod-pace-custom"], .mod-bigger-font .btn-switch[data-class="mod-bigger-font"], .mod-high-contrast .btn-switch[data-class="mod-high-contrast"], .mod-color-blind .btn-switch[data-class="mod-color-blind"], .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"], .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"], .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"], .mod-disable-animation .btn-switch[data-class="mod-disable-animation"], .mod-nav-link .btn-switch[data-class="mod-nav-link"], .mod-nav-dark .btn-switch[data-class="mod-nav-dark"], .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] {
  color: #fff;
  background: #7c91df !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:after, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:after, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:after, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:after, .nav-function-top .btn-switch[data-class="nav-function-top"]:after, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"]:after, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:after, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:after, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:after, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:after, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:after, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:after, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:after, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:after, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:after, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:after, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:after, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:after, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:after, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:after, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:after, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"]:after, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"]:after {
    background: #fff !important;
    color: #7c91df !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"] + .onoffswitch-title, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"] + .onoffswitch-title, .nav-function-minify .btn-switch[data-class="nav-function-minify"] + .onoffswitch-title, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"] + .onoffswitch-title, .nav-function-top .btn-switch[data-class="nav-function-top"] + .onoffswitch-title, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"] + .onoffswitch-title, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"] + .onoffswitch-title, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"] + .onoffswitch-title, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"] + .onoffswitch-title, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"] + .onoffswitch-title, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"] + .onoffswitch-title, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"] + .onoffswitch-title, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"] + .onoffswitch-title, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"] + .onoffswitch-title, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"] + .onoffswitch-title, .mod-color-blind .btn-switch[data-class="mod-color-blind"] + .onoffswitch-title, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"] + .onoffswitch-title, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"] + .onoffswitch-title, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"] + .onoffswitch-title, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"] + .onoffswitch-title, .mod-nav-link .btn-switch[data-class="mod-nav-link"] + .onoffswitch-title, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"] + .onoffswitch-title, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] + .onoffswitch-title {
    color: #7c91df; }

.nav-mobile-slide-out #nmp .onoffswitch-title, .nav-mobile-slide-out #nmno .onoffswitch-title, .nav-function-top #mnl .onoffswitch-title, .nav-function-minify #mnl .onoffswitch-title, .mod-hide-nav-icons #mnl .onoffswitch-title, .nav-function-top #nfh .onoffswitch-title {
  color: #d58100; }

.nav-mobile-slide-out #nmp .onoffswitch-title-desc, .nav-mobile-slide-out #nmno .onoffswitch-title-desc, .nav-function-top #mnl .onoffswitch-title-desc, .nav-function-minify #mnl .onoffswitch-title-desc, .mod-hide-nav-icons #mnl .onoffswitch-title-desc, .nav-function-top #nfh .onoffswitch-title-desc {
  color: #ec9f28; }

.dropdown-icon-menu > ul > li .btn, .header-btn {
  border: 1px solid #fafafa;
  color: #a6a6a6; }
  .dropdown-icon-menu > ul > li .btn:hover, .header-btn:hover {
    border-color: #7c91df;
    background: #a5b3e9;
    color: #fff; }

.nav-mobile-slide-out #nmp:after,
.nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
.nav-function-minify #mnl:after,
.mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after {
  background: white;
  border: 1px solid #ffa454;
  color: #3b3b3b; }

/* #GLOBAL IMPORTS
========================================================================== */
/*@import '_imports/_global-import';*/
/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)
                DOC: you can disable unused _modules
========================================================================== */
/* contains root variables to be used with css (see docs) */
/* html and body base styles */
html body {
  background-color: #fff; }
  html body a {
    color: #7c91df;
    background-color: transparent; }
    html body a:hover {
      color: #90a2e4; }

.header-icon {
  color: #666666; }
  .header-icon:not(.btn) > [class*='fa-']:first-child,
  .header-icon:not(.btn) > .ni:first-child {
    color: #7c91df; }
  .header-icon:not(.btn):hover > [class*='fa-']:only-child,
  .header-icon:not(.btn):hover > .ni {
    color: #404040; }
  .header-icon:not(.btn)[data-toggle="dropdown"] {
    /* header dropdowns */
    /* note: important rules to override popper's inline classes */
    /* end header dropdowns */ }
    .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] {
      color: #404040; }
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > [class*='fa-']:first-child,
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > .ni:first-child {
        color: #404040 !important; }
    .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
      border-color: #ccc; }
  .header-icon:hover {
    color: #404040; }

.page-header {
  background-color: #fff; }

#search-field {
  background: transparent;
  border: 1px solid transparent; }

.notification li.unread {
  background: white; }

.notification li > :first-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06); }
  .notification li > :first-child:hover {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
    background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.notification .name {
  color: #222222; }

.notification .msg-a,
.notification .msg-b {
  color: #555555; }

.notification.notification-layout-2 li {
  background: #f9f9f9; }
  .notification.notification-layout-2 li.unread {
    background: #fff; }
  .notification.notification-layout-2 li > :first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04); }

.notification.notification-layout-2:hover {
  cursor: pointer; }

.app-list-item {
  color: #666666; }
  .app-list-item:hover {
    border: 1px solid #e3e3e3; }
  .app-list-item:active {
    border-color: #7c91df; }

@media (min-width: 992px) {
  .header-function-fixed.nav-function-top .page-header {
    -webkit-box-shadow: 0px 0px 28px 2px rgba(48, 80, 197, 0.13);
            box-shadow: 0px 0px 28px 2px rgba(48, 80, 197, 0.13); } }

.nav-title {
  color: #768ad6; }

.nav-menu li.open > a {
  color: white; }

.nav-menu li.active {
  /* arrow that appears next to active/selected items */ }
  .nav-menu li.active > a {
    color: white;
    background-color: rgba(255, 255, 255, 0.04);
    -webkit-box-shadow: inset 3px 0 0 #7c91df;
            box-shadow: inset 3px 0 0 #7c91df; }
    .nav-menu li.active > a:hover > [class*='fa-'],
    .nav-menu li.active > a:hover > .ni {
      color: #b6bdd8; }
  .nav-menu li.active > ul {
    display: block; }
  .nav-menu li.active:not(.open) > a:before {
    color: #24b3a4; }

.nav-menu li a {
  color: #ccd4f0; }
  .nav-menu li a .dl-ref.label {
    color: rgba(255, 255, 255, 0.7); }
  .nav-menu li a > [class*='fa-'],
  .nav-menu li a > .ni {
    color: #7e91d8; }
  .nav-menu li a.collapsed .nav-menu-btn-sub-collapse {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }
  .nav-menu li a:hover {
    color: white;
    background-color: rgba(0, 0, 0, 0.1); }
    .nav-menu li a:hover .badge {
      color: #fff; }
    .nav-menu li a:hover > [class*='fa-'],
    .nav-menu li a:hover > .ni {
      color: #b6bdd8; }
    .nav-menu li a:hover > .badge {
      -webkit-box-shadow: 0 0 0 1px rgba(83, 108, 203, 0.8);
              box-shadow: 0 0 0 1px rgba(83, 108, 203, 0.8);
      border: 1px solid rgba(83, 108, 203, 0.8); }
  .nav-menu li a:focus {
    color: white; }
    .nav-menu li a:focus .badge {
      color: #fff; }

.nav-menu li b.collapse-sign {
  color: #90a2e4; }

.nav-menu li > ul {
  background-color: rgba(0, 0, 0, 0.1); }
  .nav-menu li > ul li a {
    color: #b9c3ea; }
    .nav-menu li > ul li a > [class*='fa-'],
    .nav-menu li > ul li a > .ni {
      color: #7e91d8; }
    .nav-menu li > ul li a > .badge {
      color: #fff;
      background-color: #ff2f44; }
    .nav-menu li > ul li a:hover {
      color: white;
      background-color: rgba(0, 0, 0, 0.1); }
      .nav-menu li > ul li a:hover > .nav-link-text > [class*='fa-'],
      .nav-menu li > ul li a:hover > .nav-link-text > .ni {
        color: #b6bdd8; }
  .nav-menu li > ul li.active > a {
    color: white;
    background-color: transparent; }
    .nav-menu li > ul li.active > a > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a > .nav-link-text > .ni {
      color: white; }
    .nav-menu li > ul li.active > a:hover > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a:hover > .nav-link-text > .ni {
      color: #b6bdd8; }
  .nav-menu li > ul li > ul li.active > a {
    color: white; }
  .nav-menu li > ul li > ul li a {
    color: #b1bce7; }
    .nav-menu li > ul li > ul li a:hover {
      color: white; }
    .nav-menu li > ul li > ul li a > .badge {
      color: #fff;
      background-color: #ff2f44;
      border: 1px solid #6e6e6e; }

/* nav clean elements */
.nav-menu-clean {
  background: #fff; }
  .nav-menu-clean li a {
    color: #6e6e6e !important; }
    .nav-menu-clean li a span {
      color: #6e6e6e !important; }
    .nav-menu-clean li a:hover {
      background-color: #f4f4f4 !important; }

/* nav bordered elements */
.nav-menu-bordered {
  border: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li a {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); }

.nav-filter input[type="text"] {
  background: rgba(0, 0, 0, 0.4);
  color: #fff; }
  .nav-filter input[type="text"]:not(:focus) {
    border-color: rgba(0, 0, 0, 0.1); }
  .nav-filter input[type="text"]:focus {
    border-color: #667dd1; }

.info-card {
  color: #fff; }
  .info-card .info-card-text {
    text-shadow: #000 0 1px; }

@media (min-width: 992px) {
  .nav-function-top {
    /* correct search field color */ }
    .nav-function-top #search-field {
      color: #fff; }
    .nav-function-top:not(.header-function-fixed) #nff {
      position: relative; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title {
        color: #d58100; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .nav-function-top:not(.header-function-fixed) #nff:after {
        background: white;
        border: 1px solid #ffa454;
        color: #3b3b3b; }
    .nav-function-top .page-header {
      background-image: -webkit-gradient(linear, right top, left top, from(rgba(89, 143, 186, 0.18)), to(transparent));
      background-image: linear-gradient(270deg, rgba(89, 143, 186, 0.18), transparent);
      background-color: #3955bc;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(48, 80, 197, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(48, 80, 197, 0.13); }
      .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child,
      .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child {
        color: #a5b3e9; }
        .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child:hover,
        .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child:hover {
          color: #ced6f3; }
      .nav-function-top .page-header .badge.badge-icon {
        -webkit-box-shadow: 0 0 0 1px #6880da;
                box-shadow: 0 0 0 1px #6880da; }
    .nav-function-top .page-sidebar {
      background: #fff;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(48, 80, 197, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(48, 80, 197, 0.13); }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a:before {
        color: #24b3a4; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
        color: inherit; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign {
        color: #a9b6e5; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li a {
        color: #3955bc; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul {
        background: #4f69ca; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a {
          color: #ccd4f0; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul {
          background: #4f69ca; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li:hover > a {
          background: rgba(0, 0, 0, 0.1);
          color: #fff; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:after {
          background: transparent; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
          color: #4f69ca; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a {
        color: #7c91df;
        background: transparent; } }

@media (min-width: 992px) {
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li.active.open > a:before {
    color: #24b3a4; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
    background: trasparent; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul {
    background-color: #3955bc; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul:before {
      color: #3955bc; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover {
    overflow: visible; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {
      background: #435fc6;
      color: #fff; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child {
        background: #3955bc; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child:before {
          color: #3955bc; }
  .nav-function-minify:not(.nav-function-top) .page-header [data-class="nav-function-minify"] {
    background: #6e6e6e;
    border-color: #545454 !important;
    color: #fff !important; } }

.nav-footer .nav-footer-buttons > li > a {
  color: #95a5df; }

.nav-function-fixed .nav-footer {
  background: #3955bc; }
  .nav-function-fixed .nav-footer:before {
    background: rgba(91, 115, 205, 0.2);
    background: -webkit-gradient(linear, left top, right top, from(#3955bc), color-stop(50%, #6e84d3), color-stop(50%, #6e84d3), to(#3955bc));
    background: linear-gradient(to right, #3955bc 0%, #6e84d3 50%, #6e84d3 50%, #3955bc 100%); }

@media (min-width: 992px) {
  .nav-function-minify .nav-footer {
    background-color: #3651b4; }
    .nav-function-minify .nav-footer [data-class="nav-function-minify"] {
      color: #7e91d8; }
    .nav-function-minify .nav-footer:hover {
      background-color: #3f5cc5; }
      .nav-function-minify .nav-footer:hover [data-class="nav-function-minify"] {
        color: #b6bdd8; } }

.page-content-wrapper {
  background-color: white; }

.subheader-icon {
  color: #b6bdd8; }

.subheader-title {
  color: #6e6e6e;
  text-shadow: #fff 0 1px; }
  .subheader-title small {
    color: #a1a1a1; }

.page-footer {
  background: #fff;
  color: #4d4d4d; }

.accordion .card .card-header {
  background-color: #f7f9fa; }
  .accordion .card .card-header .card-title {
    color: #7c91df; }
    .accordion .card .card-header .card-title.collapsed {
      color: #a1a1a1; }

.accordion.accordion-clean .card-header {
  background: #fff; }

.accordion.accordion-hover .card-header {
  background: #fff; }
  .accordion.accordion-hover .card-header:hover .card-title.collapsed {
    color: #fff;
    background-color: #a5b3e9; }

.accordion.accordion-hover .card-title:not(.collapsed) {
  color: #fff;
  background-color: #7c91df; }

/* 	DEV NOTE: The reason why we had to add this layer for alert colors is because BS4 
	does not allow you to add your own alert colors via variable control rather 
	through a systemetic agent that changes the theme colors. 

	REF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218
*/
.alert-primary {
  color: #747c9a;
  background-color: white;
  border-color: #f1f3f9; }

.alert-success {
  color: #688d64;
  background-color: white;
  border-color: #bce2b8; }

.alert-danger {
  color: #e20017;
  background-color: #ffdce0;
  border-color: #ff95a0; }

.alert-warning {
  color: #ff7b08;
  background-color: white;
  border-color: #ffd2ab; }

.alert-info {
  color: #2b6a9b;
  background-color: #e7f0f8;
  border-color: #97c1e2; }

.alert-secondary {
  color: #6e6e6e;
  background-color: white;
  border-color: #fafafa; }

.badge.badge-icon {
  background-color: #ff2f44;
  color: #fff;
  -webkit-box-shadow: 0 0 0 1px #fff;
          box-shadow: 0 0 0 1px #fff; }

/* btn switch */
.btn-switch {
  background: #878787;
  color: white; }
  .btn-switch:hover {
    color: white; }
  .btn-switch:after {
    color: white; }
  .btn-switch.active {
    color: #fff;
    background: #7c91df; }
    .btn-switch.active:before {
      color: white; }
    .btn-switch.active:after {
      background: #fff;
      color: #7c91df; }

/* button used to close filter and mobile search */
.btn-search-close {
  color: #fff; }

/* buttons used in the header section of the page */
.header-btn[data-class='mobile-nav-on'] {
  border-color: #fb0019;
  background-color: #ff162d;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#ff162d), to(#e20017));
  background-image: linear-gradient(to top, #ff162d, #e20017);
  color: #fff; }

/* dropdown btn */
/* used on info card pulldown filter */
.pull-trigger-btn {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.4);
  color: #fff !important;
  -webkit-box-shadow: 0px 0px 2px rgba(124, 145, 223, 0.3);
          box-shadow: 0px 0px 2px rgba(124, 145, 223, 0.3); }
  .pull-trigger-btn:hover {
    background: #7c91df;
    border-color: #6880da; }

/* btn misc */
.btn-outline-default {
  color: #212529;
  border-color: #E5E5E5; }
  .btn-outline-default:hover, .btn-outline-default:not(:disabled):not(.disabled):active, .btn-outline-default:not(:disabled):not(.disabled).active,
  .show > .btn-outline-default.dropdown-toggle {
    color: #212529;
    background-color: #f9f9f9;
    border-color: #E5E5E5; }
  .btn-outline-default.disabled, .btn-outline-default:disabled {
    color: #212529; }

/* btn shadows */
.btn-primary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(124, 145, 223, 0.5);
          box-shadow: 0 2px 6px 0 rgba(124, 145, 223, 0.5); }

.btn-secondary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5);
          box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5); }

.btn-success {
  -webkit-box-shadow: 0 2px 6px 0 rgba(71, 183, 58, 0.5);
          box-shadow: 0 2px 6px 0 rgba(71, 183, 58, 0.5); }

.btn-info {
  -webkit-box-shadow: 0 2px 6px 0 rgba(71, 145, 204, 0.5);
          box-shadow: 0 2px 6px 0 rgba(71, 145, 204, 0.5); }

.btn-warning {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 191, 135, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 191, 135, 0.5); }

.btn-danger {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 47, 68, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 47, 68, 0.5); }

.btn-light {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5); }

.btn-dark {
  -webkit-box-shadow: 0 2px 6px 0 rgba(110, 110, 110, 0.5);
          box-shadow: 0 2px 6px 0 rgba(110, 110, 110, 0.5); }

.btn-icon-light {
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: transparent !important; }
  .btn-icon-light:not(.active):not(:active):not(:hover):not(:focus) {
    color: rgba(255, 255, 255, 0.7) !important; }
  .btn-icon-light:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important; }

/* bootstrap buttons */
.btn-link {
  color: #7c91df; }
  .btn-link:hover {
    color: #536fd5; }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #7c91df; }

.card-header {
  background-color: #f7f9fa; }

.carousel-control-prev:hover {
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.carousel-control-next:hover {
  background: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to left, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

/* dropdown menu multi-level */
.dropdown-menu .dropdown-menu {
  background: #fff; }

.dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) {
  background: #f8f9fa;
  color: #536fd5; }

.dropdown-item:hover, .dropdown-item:focus {
  color: #536fd5;
  background-color: #f8f9fa; }

.dropdown-item.active, .dropdown-item:active {
  color: #3f5dd0;
  background-color: white; }

.chat-segment-get .chat-message {
  background: #f1f0f0; }

.chat-segment-sent .chat-message {
  background: #47b73a; }

/* transparent modal */
.modal-transparent .modal-content {
  -webkit-box-shadow: 0 1px 15px 1px rgba(48, 80, 197, 0.3);
          box-shadow: 0 1px 15px 1px rgba(48, 80, 197, 0.3); }

.modal-transparent .modal-content {
  background: rgba(42, 55, 101, 0.85); }

.panel {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  /* panel fullscreen */
  /* panel locked */ }
  .panel.panel-fullscreen {
    /* make panel header bigger */ }
    .panel.panel-fullscreen .panel-hdr {
      -webkit-box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(43, 71, 176, 0.1);
              box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(43, 71, 176, 0.1); }
  .panel.panel-locked:not(.panel-fullscreen) .panel-hdr:active h2:before {
    color: #ff2f44; }

/* panel tag can be used globally */
.panel-tag {
  background: #eef7fd; }

/* panel header */
.panel-hdr {
  background: #fff; }

/* panel tap highlight */
.panel-sortable:not(.panel-locked) .panel-hdr:active {
  border-top-color: rgba(165, 179, 233, 0.7);
  border-left-color: rgba(124, 145, 223, 0.7);
  border-right-color: rgba(124, 145, 223, 0.7); }
  .panel-sortable:not(.panel-locked) .panel-hdr:active + .panel-container {
    border-color: transparent rgba(124, 145, 223, 0.7) rgba(104, 128, 218, 0.7); }

/*.panel-sortable .panel-hdr:active,
.panel-sortable .panel-hdr:active + .panel-container {
	@include transition-border(0.4s, ease-out);
}*/
.panel-sortable.panel-locked .panel-hdr:active {
  border-top-color: #ff6272;
  border-left-color: #dc3545;
  border-right-color: #dc3545; }
  .panel-sortable.panel-locked .panel-hdr:active + .panel-container {
    border-color: transparent #dc3545 #dc3545; }

/* panel toolbar (sits inside panel header) */
.panel-toolbar .btn-panel {
  /* add default colors for action buttons */ }
  .panel-toolbar .btn-panel[data-action="panel-collapse"], .panel-toolbar .btn-panel.js-panel-collapse {
    background: #47b73a; }
  .panel-toolbar .btn-panel[data-action="panel-fullscreen"], .panel-toolbar .btn-panel.js-panel-fullscreen {
    background: #ffbf87; }
  .panel-toolbar .btn-panel[data-action="panel-close"], .panel-toolbar .btn-panel.js-panel-close {
    background: #ff2f44; }

/* placeholder */
.panel-placeholder {
  background-color: white; }
  .panel-placeholder:before {
    background: white; }

.mod-panel-clean .panel-hdr {
  background: #fff;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f7f7f7), to(#fff));
  background-image: linear-gradient(to bottom, #f7f7f7, #fff); }

@media only screen and (max-width: 420px) {
  /* making mobile spacing a little narrow */
  .panel .panel-hdr {
    color: #060606; } }

.popover .arrow {
  border-color: inherit; }

.menu-item,
label.menu-open-button {
  background: #7c91df;
  color: #fff !important; }
  .menu-item:hover,
  label.menu-open-button:hover {
    background: #536fd5; }

.app-shortcut-icon {
  background: #ecf0f1;
  color: #ecf0f1; }

.menu-open:checked + .menu-open-button {
  background: #6e6e6e; }

/* nav tabs panel */
.nav-tabs-clean .nav-item .nav-link.active {
  border-bottom: 1px solid #7c91df;
  color: #7c91df; }

.nav-tabs-clean .nav-item .nav-link:hover {
  color: #7c91df; }

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #7c91df; }

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #536fd5;
  background-color: #6880da; }

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #90a2e4; }

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #ced6f3;
  background-color: #ced6f3;
  border-color: #ced6f3; }

.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d; }
  .custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
    background-color: #e9ecef; }

.custom-control-label::before {
  background-color: #fff;
  border: #adb5bd solid 2px; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #6880da;
  background-color: #7c91df; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e"); }

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ced6f3; }

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: #ced6f3; }

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ced6f3; }

.custom-switch .custom-control-label::after {
  background-color: #adb5bd; }

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff; }

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #ced6f3; }

.custom-select {
  color: #495057;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  border: 1px solid #ced4da; }
  .custom-select:focus {
    border-color: #7c91df; }
    .custom-select:focus::-ms-value {
      color: #495057;
      background-color: #fff; }
  .custom-select:disabled {
    color: #6c757d;
    background-color: #e9ecef; }

.custom-file-input:focus ~ .custom-file-label {
  border-color: #7c91df; }

.custom-file-input[disabled] ~ .custom-file-label,
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef; }

.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse); }

.custom-file-label {
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da; }
  .custom-file-label::after {
    color: #495057;
    background-color: #e9ecef; }

.custom-range {
  background-color: transparent; }
  .custom-range::-webkit-slider-thumb {
    background-color: #7c91df;
    border: 0; }
    .custom-range::-webkit-slider-thumb:active {
      background-color: #a5b3e9; }
  .custom-range::-webkit-slider-runnable-track {
    background-color: #dee2e6; }
  .custom-range::-moz-range-thumb {
    background-color: #7c91df;
    border: 0; }
    .custom-range::-moz-range-thumb:active {
      background-color: #a5b3e9; }
  .custom-range::-moz-range-track {
    background-color: #dee2e6; }
  .custom-range::-ms-thumb {
    background-color: #7c91df;
    border: 0; }
    .custom-range::-ms-thumb:active {
      background-color: #a5b3e9; }
  .custom-range::-ms-fill-lower {
    background-color: #dee2e6; }
  .custom-range::-ms-fill-upper {
    background-color: #dee2e6; }
  .custom-range:disabled::-webkit-slider-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-moz-range-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-ms-thumb {
    background-color: #adb5bd; }

.page-link {
  color: #7c91df;
  background-color: #fff;
  border: 1px solid #dee2e6;
  /*&:focus {
    outline: $pagination-focus-outline;
  }*/ }
  .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6; }

.page-item.active .page-link {
  color: #fff;
  background-color: #7c91df; }

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff; }

.pagination .page-item:first-child:not(.active) .page-link,
.pagination .page-item:last-child:not(.active) .page-link,
.pagination .page-item.disabled .page-link {
  background: white; }

.pagination .page-link:hover {
  background-color: #7c91df !important;
  color: #fff; }

.list-group-item {
  border: 1px solid rgba(var(--theme-rgb-primary), 0.15); }
  .list-group-item.active {
    background-color: #7c91df;
    border-color: #7c91df; }

/* backgrounds */
.bg-white {
  background-color: #fff;
  color: #666666; }

.bg-faded {
  background-color: #f7f9fa; }

.bg-offwhite-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(white));
  background-image: linear-gradient(to top, #fff, white); }

.bg-subtlelight {
  background-color: white; }

.bg-subtlelight-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(white));
  background-image: linear-gradient(to top, #fff, white); }

.bg-highlight {
  background-color: white; }

.bg-gray-50 {
  background-color: #f9f9f9; }

.bg-gray-100 {
  background-color: #f8f9fa; }

.bg-gray-200 {
  background-color: #e9ecef; }

.bg-gray-300 {
  background-color: #dee2e6; }

.bg-gray-400 {
  background-color: #ced4da; }

.bg-gray-500 {
  background-color: #adb5bd; }

.bg-gray-600 {
  background-color: #6c757d; }

.bg-gray-700 {
  background-color: #495057; }

.bg-gray-800 {
  background-color: #343a40; }

.bg-gray-900 {
  background-color: #212529; }

/* borders */
.border-faded {
  border: 1px solid rgba(59, 59, 59, 0.07); }

/* hover any bg */
/* inherits the parent background on hover */
.hover-bg {
  background: #fff; }

/* states */
.state-selected {
  background: #ebf3f9 !important; }

/* demo window */
.demo-window {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12); }
  .demo-window:before {
    background: #e5e5e5; }
  .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    background: #ccc; }

.bg-trans-gradient {
  background: linear-gradient(250deg, #648eaf, #8496d7); }

.notes {
  background: #f9f4b5; }

/* disclaimer class */
.disclaimer {
  color: #a2a2a2; }

/* online status */
.status {
  position: relative; }
  .status:before {
    background: #6e6e6e;
    border: 2px solid #fff; }
  .status.status-success:before {
    background: #47b73a; }
  .status.status-danger:before {
    background: #ff2f44; }
  .status.status-warning:before {
    background: #ffbf87; }

/* display frame */
.frame-heading {
  color: #bfbfbf; }

.frame-wrap {
  background: white; }

/* time stamp */
.time-stamp {
  color: #949494; }

/* data-hasmore */
[data-hasmore] {
  color: #fff; }
  [data-hasmore]:before {
    background: rgba(0, 0, 0, 0.4); }

/* code */
code {
  background: white; }

/* select background */
::-moz-selection {
  background: #6e6e6e;
  color: #fff; }
::selection {
  background: #6e6e6e;
  color: #fff; }

::-moz-selection {
  background: #6e6e6e;
  color: #fff; }

@media only screen and (max-width: 992px) {
  .page-wrapper {
    background: #fff; }
    .page-wrapper .page-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.09); }
    .page-wrapper .page-content {
      color: #222; }
      .page-wrapper .page-content .p-g {
        padding: 1.5rem; }
    .page-wrapper .page-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.09); }
  /* Off canvas */
  .nav-mobile-slide-out .page-wrapper .page-content {
    background: white; }
  /* mobile nav show & hide button */
  /* general */
  .mobile-nav-on .page-sidebar {
    border-right: 1px solid rgba(0, 0, 0, 0.03);
    -webkit-box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52);
            box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52); }
  .mobile-nav-on .page-content-overlay {
    background: rgba(0, 0, 0, 0.09); } }

@media only screen and (max-width: 576px) {
  /* here we turn on mobile font for smaller screens */
  /*body {
		font-family: $mobile-page-font !important;
	}*/
  /* mobile nav search */
  .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field {
    background: #fff; }
    .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field:focus {
      border-color: #7c91df; } }

/* text area */
[contenteditable="true"]:empty:not(:focus):before {
  content: attr(data-placeholder);
  color: #aeaeae; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

/* add background to focused inpur prepend and append */
.form-control:focus ~ .input-group-prepend {
  background: #7c91df; }

.has-length .input-group-text {
  border-color: #7c91df; }
  .has-length .input-group-text + .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1); }

.has-length .input-group-text:not([class^="bg-"]):not([class*=" bg-"]) {
  background: #7c91df;
  color: #fff !important; }

/* help block and validation feedback texts*/
.help-block {
  color: #aeaeae; }

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #7c91df; }

.settings-panel h5 {
  color: #6e6e6e; }

.settings-panel .list {
  color: #666666; }
  .settings-panel .list:hover {
    color: #333333;
    background: rgba(255, 255, 255, 0.7); }

.settings-panel .expanded:before {
  border-bottom-color: #7b7b7b; }

@media only screen and (max-width: 992px) {
  .mobile-view-activated #nff,
  .mobile-view-activated #nfm,
  .mobile-view-activated #nfh,
  .mobile-view-activated #nft,
  .mobile-view-activated #mmb {
    position: relative; }
    .mobile-view-activated #nff .onoffswitch-title,
    .mobile-view-activated #nfm .onoffswitch-title,
    .mobile-view-activated #nfh .onoffswitch-title,
    .mobile-view-activated #nft .onoffswitch-title,
    .mobile-view-activated #mmb .onoffswitch-title {
      color: #d58100 !important; }
    .mobile-view-activated #nff .onoffswitch-title-desc,
    .mobile-view-activated #nfm .onoffswitch-title-desc,
    .mobile-view-activated #nfh .onoffswitch-title-desc,
    .mobile-view-activated #nft .onoffswitch-title-desc,
    .mobile-view-activated #mmb .onoffswitch-title-desc {
      color: #ec9f28 !important; }
    .mobile-view-activated #nff:after,
    .mobile-view-activated #nfm:after,
    .mobile-view-activated #nfh:after,
    .mobile-view-activated #nft:after,
    .mobile-view-activated #mmb:after {
      background: white;
      border: 1px solid #ffa454;
      color: #3b3b3b; } }

/* Hierarchical Navigation */
.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul {
  /* addressing all second, third children */ }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {
    border-left: 1px solid #435fc6; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:after {
    background-color: #7e91d8; }

.bg-primary-50 {
  background-color: #e2e7f8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-100 {
  background-color: #ced6f3;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-200 {
  background-color: #b9c5ee;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-300 {
  background-color: #a5b3e9;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-400 {
  background-color: #90a2e4;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-500 {
  background-color: #7c91df;
  color: white; }
  .bg-primary-500:hover {
    color: white; }

.bg-primary-600 {
  background-color: #6880da;
  color: white; }
  .bg-primary-600:hover {
    color: white; }

.bg-primary-700 {
  background-color: #536fd5;
  color: white; }
  .bg-primary-700:hover {
    color: white; }

.bg-primary-800 {
  background-color: #3f5dd0;
  color: white; }
  .bg-primary-800:hover {
    color: white; }

.bg-primary-900 {
  background-color: #3050c5;
  color: white; }
  .bg-primary-900:hover {
    color: white; }

.color-primary-50 {
  color: #e2e7f8; }

.color-primary-100 {
  color: #ced6f3; }

.color-primary-200 {
  color: #b9c5ee; }

.color-primary-300 {
  color: #a5b3e9; }

.color-primary-400 {
  color: #90a2e4; }

.color-primary-500 {
  color: #7c91df; }

.color-primary-600 {
  color: #6880da; }

.color-primary-700 {
  color: #536fd5; }

.color-primary-800 {
  color: #3f5dd0; }

.color-primary-900 {
  color: #3050c5; }

.bg-success-50 {
  background-color: #9bdd94;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-100 {
  background-color: #89d780;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-200 {
  background-color: #77d16d;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-300 {
  background-color: #65cb59;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-400 {
  background-color: #53c446;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-500 {
  background-color: #47b73a;
  color: white; }
  .bg-success-500:hover {
    color: white; }

.bg-success-600 {
  background-color: #3fa434;
  color: white; }
  .bg-success-600:hover {
    color: white; }

.bg-success-700 {
  background-color: #38902e;
  color: white; }
  .bg-success-700:hover {
    color: white; }

.bg-success-800 {
  background-color: #307d28;
  color: white; }
  .bg-success-800:hover {
    color: white; }

.bg-success-900 {
  background-color: #296a21;
  color: white; }
  .bg-success-900:hover {
    color: white; }

.color-success-50 {
  color: #9bdd94; }

.color-success-100 {
  color: #89d780; }

.color-success-200 {
  color: #77d16d; }

.color-success-300 {
  color: #65cb59; }

.color-success-400 {
  color: #53c446; }

.color-success-500 {
  color: #47b73a; }

.color-success-600 {
  color: #3fa434; }

.color-success-700 {
  color: #38902e; }

.color-success-800 {
  color: #307d28; }

.color-success-900 {
  color: #296a21; }

.bg-info-50 {
  background-color: #abcde8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-100 {
  background-color: #97c1e2;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-200 {
  background-color: #83b5dd;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-300 {
  background-color: #6fa9d7;
  color: white; }
  .bg-info-300:hover {
    color: white; }

.bg-info-400 {
  background-color: #5b9dd2;
  color: white; }
  .bg-info-400:hover {
    color: white; }

.bg-info-500 {
  background-color: #4791cc;
  color: white; }
  .bg-info-500:hover {
    color: white; }

.bg-info-600 {
  background-color: #3685c3;
  color: white; }
  .bg-info-600:hover {
    color: white; }

.bg-info-700 {
  background-color: #3177af;
  color: white; }
  .bg-info-700:hover {
    color: white; }

.bg-info-800 {
  background-color: #2b6a9b;
  color: white; }
  .bg-info-800:hover {
    color: white; }

.bg-info-900 {
  background-color: #265c87;
  color: white; }
  .bg-info-900:hover {
    color: white; }

.color-info-50 {
  color: #abcde8; }

.color-info-100 {
  color: #97c1e2; }

.color-info-200 {
  color: #83b5dd; }

.color-info-300 {
  color: #6fa9d7; }

.color-info-400 {
  color: #5b9dd2; }

.color-info-500 {
  color: #4791cc; }

.color-info-600 {
  color: #3685c3; }

.color-info-700 {
  color: #3177af; }

.color-info-800 {
  color: #2b6a9b; }

.color-info-900 {
  color: #265c87; }

.bg-warning-50 {
  background-color: white;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-100 {
  background-color: #fff5ed;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-200 {
  background-color: #ffe8d4;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-300 {
  background-color: #ffdaba;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-400 {
  background-color: #ffcda1;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-500 {
  background-color: #ffbf87;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-600 {
  background-color: #ffb16e;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-700 {
  background-color: #ffa454;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-800 {
  background-color: #ff963b;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-800:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-900 {
  background-color: #ff8921;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-900:hover {
    color: rgba(0, 0, 0, 0.8); }

.color-warning-50 {
  color: white; }

.color-warning-100 {
  color: #fff5ed; }

.color-warning-200 {
  color: #ffe8d4; }

.color-warning-300 {
  color: #ffdaba; }

.color-warning-400 {
  color: #ffcda1; }

.color-warning-500 {
  color: #ffbf87; }

.color-warning-600 {
  color: #ffb16e; }

.color-warning-700 {
  color: #ffa454; }

.color-warning-800 {
  color: #ff963b; }

.color-warning-900 {
  color: #ff8921; }

.bg-danger-50 {
  background-color: #ffafb7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-100 {
  background-color: #ff95a0;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-200 {
  background-color: #ff7c89;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-300 {
  background-color: #ff6272;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-400 {
  background-color: #ff495b;
  color: white; }
  .bg-danger-400:hover {
    color: white; }

.bg-danger-500 {
  background-color: #ff2f44;
  color: white; }
  .bg-danger-500:hover {
    color: white; }

.bg-danger-600 {
  background-color: #ff162d;
  color: white; }
  .bg-danger-600:hover {
    color: white; }

.bg-danger-700 {
  background-color: #fb0019;
  color: white; }
  .bg-danger-700:hover {
    color: white; }

.bg-danger-800 {
  background-color: #e20017;
  color: white; }
  .bg-danger-800:hover {
    color: white; }

.bg-danger-900 {
  background-color: #c80014;
  color: white; }
  .bg-danger-900:hover {
    color: white; }

.color-danger-50 {
  color: #ffafb7; }

.color-danger-100 {
  color: #ff95a0; }

.color-danger-200 {
  color: #ff7c89; }

.color-danger-300 {
  color: #ff6272; }

.color-danger-400 {
  color: #ff495b; }

.color-danger-500 {
  color: #ff2f44; }

.color-danger-600 {
  color: #ff162d; }

.color-danger-700 {
  color: #fb0019; }

.color-danger-800 {
  color: #e20017; }

.color-danger-900 {
  color: #c80014; }

.bg-fusion-50 {
  background-color: #aeaeae;
  color: rgba(0, 0, 0, 0.8); }
  .bg-fusion-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-fusion-100 {
  background-color: #a1a1a1;
  color: rgba(0, 0, 0, 0.8); }
  .bg-fusion-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-fusion-200 {
  background-color: #949494;
  color: white; }
  .bg-fusion-200:hover {
    color: white; }

.bg-fusion-300 {
  background-color: #878787;
  color: white; }
  .bg-fusion-300:hover {
    color: white; }

.bg-fusion-400 {
  background-color: #7b7b7b;
  color: white; }
  .bg-fusion-400:hover {
    color: white; }

.bg-fusion-500 {
  background-color: #6e6e6e;
  color: white; }
  .bg-fusion-500:hover {
    color: white; }

.bg-fusion-600 {
  background-color: #616161;
  color: white; }
  .bg-fusion-600:hover {
    color: white; }

.bg-fusion-700 {
  background-color: #545454;
  color: white; }
  .bg-fusion-700:hover {
    color: white; }

.bg-fusion-800 {
  background-color: #484848;
  color: white; }
  .bg-fusion-800:hover {
    color: white; }

.bg-fusion-900 {
  background-color: #3b3b3b;
  color: white; }
  .bg-fusion-900:hover {
    color: white; }

.color-fusion-50 {
  color: #aeaeae; }

.color-fusion-100 {
  color: #a1a1a1; }

.color-fusion-200 {
  color: #949494; }

.color-fusion-300 {
  color: #878787; }

.color-fusion-400 {
  color: #7b7b7b; }

.color-fusion-500 {
  color: #6e6e6e; }

.color-fusion-600 {
  color: #616161; }

.color-fusion-700 {
  color: #545454; }

.color-fusion-800 {
  color: #484848; }

.color-fusion-900 {
  color: #3b3b3b; }

.color-white {
  color: #fff; }

.color-black {
  color: #222222; }

.bg-primary-gradient {
  background-image: linear-gradient(250deg, rgba(48, 80, 197, 0.7), transparent); }

.bg-danger-gradient {
  background-image: linear-gradient(250deg, rgba(200, 0, 20, 0.7), transparent); }

.bg-info-gradient {
  background-image: linear-gradient(250deg, rgba(38, 92, 135, 0.7), transparent); }

.bg-warning-gradient {
  background-image: linear-gradient(250deg, rgba(255, 137, 33, 0.7), transparent); }

.bg-success-gradient {
  background-image: linear-gradient(250deg, rgba(41, 106, 33, 0.7), transparent); }

.bg-fusion-gradient {
  background-image: linear-gradient(250deg, rgba(59, 59, 59, 0.7), transparent); }

.btn-primary {
  color: #fff;
  background-color: #7c91df;
  border-color: #7c91df;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-primary:hover {
    color: #fff;
    background-color: #5d77d7;
    border-color: #536fd5; }
  .btn-primary:focus, .btn-primary.focus {
    color: #fff;
    background-color: #5d77d7;
    border-color: #536fd5;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(144, 162, 228, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(144, 162, 228, 0.5); }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #7c91df;
    border-color: #7c91df; }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
  .show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #536fd5;
    border-color: #4966d2; }
    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(144, 162, 228, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(144, 162, 228, 0.5); }

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62; }
  .btn-secondary:focus, .btn-secondary.focus {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b; }
    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }

.btn-success {
  color: #fff;
  background-color: #47b73a;
  border-color: #47b73a;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-success:hover {
    color: #fff;
    background-color: #3c9a31;
    border-color: #38902e; }
  .btn-success:focus, .btn-success.focus {
    color: #fff;
    background-color: #3c9a31;
    border-color: #38902e;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(99, 194, 88, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(99, 194, 88, 0.5); }
  .btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #47b73a;
    border-color: #47b73a; }
  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
  .show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #38902e;
    border-color: #34872b; }
    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(99, 194, 88, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(99, 194, 88, 0.5); }

.btn-info {
  color: #fff;
  background-color: #4791cc;
  border-color: #4791cc;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-info:hover {
    color: #fff;
    background-color: #337eb9;
    border-color: #3177af; }
  .btn-info:focus, .btn-info.focus {
    color: #fff;
    background-color: #337eb9;
    border-color: #3177af;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(99, 162, 212, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(99, 162, 212, 0.5); }
  .btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #4791cc;
    border-color: #4791cc; }
  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
  .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #3177af;
    border-color: #2e70a5; }
    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(99, 162, 212, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(99, 162, 212, 0.5); }

.btn-warning {
  color: #212529;
  background-color: #ffbf87;
  border-color: #ffbf87;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-warning:hover {
    color: #212529;
    background-color: #ffab61;
    border-color: #ffa454; }
  .btn-warning:focus, .btn-warning.focus {
    color: #212529;
    background-color: #ffab61;
    border-color: #ffa454;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 168, 121, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 168, 121, 0.5); }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #ffbf87;
    border-color: #ffbf87; }
  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
  .show > .btn-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffa454;
    border-color: #ff9d47; }
    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 168, 121, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 168, 121, 0.5); }

.btn-danger {
  color: #fff;
  background-color: #ff2f44;
  border-color: #ff2f44;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-danger:hover {
    color: #fff;
    background-color: #ff0922;
    border-color: #fb0019; }
  .btn-danger:focus, .btn-danger.focus {
    color: #fff;
    background-color: #ff0922;
    border-color: #fb0019;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 78, 96, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 78, 96, 0.5); }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #ff2f44;
    border-color: #ff2f44; }
  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
  .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #fb0019;
    border-color: #ee0018; }
    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 78, 96, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 78, 96, 0.5); }

.btn-light {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-light:hover {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6; }
  .btn-light:focus, .btn-light.focus {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }
  .btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
  .show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #e6e6e6;
    border-color: #dfdfdf; }
    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }

.btn-dark {
  color: #fff;
  background-color: #6e6e6e;
  border-color: #6e6e6e;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-dark:hover {
    color: #fff;
    background-color: #5b5b5b;
    border-color: #545454; }
  .btn-dark:focus, .btn-dark.focus {
    color: #fff;
    background-color: #5b5b5b;
    border-color: #545454;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(132, 132, 132, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(132, 132, 132, 0.5); }
  .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #6e6e6e;
    border-color: #6e6e6e; }
  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
  .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #545454;
    border-color: #4e4e4e; }
    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(132, 132, 132, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(132, 132, 132, 0.5); }

.btn-outline-primary {
  color: #7c91df;
  border-color: #7c91df; }
  .btn-outline-primary:hover {
    color: #fff;
    background-color: #7c91df;
    border-color: #7c91df; }
  .btn-outline-primary:focus, .btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(124, 145, 223, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(124, 145, 223, 0.5); }
  .btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #7c91df;
    background-color: transparent; }
  .btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #7c91df;
    border-color: #7c91df; }
    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(124, 145, 223, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(124, 145, 223, 0.5); }

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d; }
  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-outline-secondary:focus, .btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }
  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent; }
  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }

.btn-outline-success {
  color: #47b73a;
  border-color: #47b73a; }
  .btn-outline-success:hover {
    color: #fff;
    background-color: #47b73a;
    border-color: #47b73a; }
  .btn-outline-success:focus, .btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(71, 183, 58, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(71, 183, 58, 0.5); }
  .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #47b73a;
    background-color: transparent; }
  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
  .show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #47b73a;
    border-color: #47b73a; }
    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(71, 183, 58, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(71, 183, 58, 0.5); }

.btn-outline-info {
  color: #4791cc;
  border-color: #4791cc; }
  .btn-outline-info:hover {
    color: #fff;
    background-color: #4791cc;
    border-color: #4791cc; }
  .btn-outline-info:focus, .btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(71, 145, 204, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(71, 145, 204, 0.5); }
  .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #4791cc;
    background-color: transparent; }
  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
  .show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #4791cc;
    border-color: #4791cc; }
    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(71, 145, 204, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(71, 145, 204, 0.5); }

.btn-outline-warning {
  color: #ffbf87;
  border-color: #ffbf87; }
  .btn-outline-warning:hover {
    color: #212529;
    background-color: #ffbf87;
    border-color: #ffbf87; }
  .btn-outline-warning:focus, .btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 191, 135, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 191, 135, 0.5); }
  .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #ffbf87;
    background-color: transparent; }
  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
  .show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffbf87;
    border-color: #ffbf87; }
    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 191, 135, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 191, 135, 0.5); }

.btn-outline-danger {
  color: #ff2f44;
  border-color: #ff2f44; }
  .btn-outline-danger:hover {
    color: #fff;
    background-color: #ff2f44;
    border-color: #ff2f44; }
  .btn-outline-danger:focus, .btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 47, 68, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 47, 68, 0.5); }
  .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #ff2f44;
    background-color: transparent; }
  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
  .show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #ff2f44;
    border-color: #ff2f44; }
    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 47, 68, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 47, 68, 0.5); }

.btn-outline-light {
  color: #fff;
  border-color: #fff; }
  .btn-outline-light:hover {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-outline-light:focus, .btn-outline-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }
  .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #fff;
    background-color: transparent; }
  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
  .show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }

.btn-outline-dark {
  color: #6e6e6e;
  border-color: #6e6e6e; }
  .btn-outline-dark:hover {
    color: #fff;
    background-color: #6e6e6e;
    border-color: #6e6e6e; }
  .btn-outline-dark:focus, .btn-outline-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(110, 110, 110, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(110, 110, 110, 0.5); }
  .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #6e6e6e;
    background-color: transparent; }
  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
  .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #6e6e6e;
    border-color: #6e6e6e; }
    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(110, 110, 110, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(110, 110, 110, 0.5); }

.border-primary {
  border-color: #7c91df !important; }

.border-secondary {
  border-color: #6c757d !important; }

.border-success {
  border-color: #47b73a !important; }

.border-info {
  border-color: #4791cc !important; }

.border-warning {
  border-color: #ffbf87 !important; }

.border-danger {
  border-color: #ff2f44 !important; }

.border-light {
  border-color: #fff !important; }

.border-dark {
  border-color: #6e6e6e !important; }

.text-primary {
  color: #7c91df !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #3f5dd0 !important; }

.text-secondary {
  color: #6c757d !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important; }

.text-success {
  color: #47b73a !important; }

a.text-success:hover, a.text-success:focus {
  color: #307d28 !important; }

.text-info {
  color: #4791cc !important; }

a.text-info:hover, a.text-info:focus {
  color: #2b6a9b !important; }

.text-warning {
  color: #ffbf87 !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #ff963b !important; }

.text-danger {
  color: #ff2f44 !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #e20017 !important; }

.text-light {
  color: #fff !important; }

a.text-light:hover, a.text-light:focus {
  color: #d9d9d9 !important; }

.text-dark {
  color: #6e6e6e !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #484848 !important; }

.bg-primary {
  background-color: #7c91df !important; }

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #536fd5 !important; }

.bg-secondary {
  background-color: #6c757d !important; }

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #545b62 !important; }

.bg-success {
  background-color: #47b73a !important; }

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #38902e !important; }

.bg-info {
  background-color: #4791cc !important; }

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #3177af !important; }

.bg-warning {
  background-color: #ffbf87 !important; }

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #ffa454 !important; }

.bg-danger {
  background-color: #ff2f44 !important; }

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #fb0019 !important; }

.bg-light {
  background-color: #fff !important; }

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #e6e6e6 !important; }

.bg-dark {
  background-color: #6e6e6e !important; }

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #545454 !important; }

:root {
  --theme-primary: #7c91df;
  --theme-secondary: #6c757d;
  --theme-success: #47b73a;
  --theme-info: #4791cc;
  --theme-warning: #ffbf87;
  --theme-danger: #ff2f44;
  --theme-light: #fff;
  --theme-dark: #6e6e6e;
  --theme-rgb-primary: 124,145,223;
  --theme-rgb-success: 71,183,58;
  --theme-rgb-info: 71,145,204;
  --theme-rgb-warning: 255,191,135;
  --theme-rgb-danger: 255,47,68;
  --theme-rgb-fusion: 109.75,109.75,109.75;
  --theme-primary-50: #e2e7f8;
  --theme-primary-100: #ced6f3;
  --theme-primary-200: #b9c5ee;
  --theme-primary-300: #a5b3e9;
  --theme-primary-400: #90a2e4;
  --theme-primary-500: #7c91df;
  --theme-primary-600: #6880da;
  --theme-primary-700: #536fd5;
  --theme-primary-800: #3f5dd0;
  --theme-primary-900: #3050c5;
  --theme-success-50: #9bdd94;
  --theme-success-100: #89d780;
  --theme-success-200: #77d16d;
  --theme-success-300: #65cb59;
  --theme-success-400: #53c446;
  --theme-success-500: #47b73a;
  --theme-success-600: #3fa434;
  --theme-success-700: #38902e;
  --theme-success-800: #307d28;
  --theme-success-900: #296a21;
  --theme-info-50: #abcde8;
  --theme-info-100: #97c1e2;
  --theme-info-200: #83b5dd;
  --theme-info-300: #6fa9d7;
  --theme-info-400: #5b9dd2;
  --theme-info-500: #4791cc;
  --theme-info-600: #3685c3;
  --theme-info-700: #3177af;
  --theme-info-800: #2b6a9b;
  --theme-info-900: #265c87;
  --theme-warning-50: white;
  --theme-warning-100: #fff5ed;
  --theme-warning-200: #ffe8d4;
  --theme-warning-300: #ffdaba;
  --theme-warning-400: #ffcda1;
  --theme-warning-500: #ffbf87;
  --theme-warning-600: #ffb16e;
  --theme-warning-700: #ffa454;
  --theme-warning-800: #ff963b;
  --theme-warning-900: #ff8921;
  --theme-danger-50: #ffafb7;
  --theme-danger-100: #ff95a0;
  --theme-danger-200: #ff7c89;
  --theme-danger-300: #ff6272;
  --theme-danger-400: #ff495b;
  --theme-danger-500: #ff2f44;
  --theme-danger-600: #ff162d;
  --theme-danger-700: #fb0019;
  --theme-danger-800: #e20017;
  --theme-danger-900: #c80014;
  --theme-fusion-50: #aeaeae;
  --theme-fusion-100: #a1a1a1;
  --theme-fusion-200: #949494;
  --theme-fusion-300: #878787;
  --theme-fusion-400: #7b7b7b;
  --theme-fusion-500: #6e6e6e;
  --theme-fusion-600: #616161;
  --theme-fusion-700: #545454;
  --theme-fusion-800: #484848;
  --theme-fusion-900: #3b3b3b; }

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #7c91df; }

.ct-series-a .ct-slice-pie, .ct-series-a .ct-slice-donut-solid, .ct-series-a .ct-area {
  fill: #7c91df; }

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #ff2f44; }

.ct-series-b .ct-slice-pie, .ct-series-b .ct-slice-donut-solid, .ct-series-b .ct-area {
  fill: #ff2f44; }

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #ffbf87; }

.ct-series-c .ct-slice-pie, .ct-series-c .ct-slice-donut-solid, .ct-series-c .ct-area {
  fill: #ffbf87; }

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #4791cc; }

.ct-series-d .ct-slice-pie, .ct-series-d .ct-slice-donut-solid, .ct-series-d .ct-area {
  fill: #4791cc; }

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #6e6e6e; }

.ct-series-e .ct-slice-pie, .ct-series-e .ct-slice-donut-solid, .ct-series-e .ct-area {
  fill: #6e6e6e; }

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: #47b73a; }

.ct-series-f .ct-slice-pie, .ct-series-f .ct-slice-donut-solid, .ct-series-f .ct-area {
  fill: #47b73a; }

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: #4791cc; }

.ct-series-g .ct-slice-pie, .ct-series-g .ct-slice-donut-solid, .ct-series-g .ct-area {
  fill: #4791cc; }

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: #3050c5; }

.ct-series-h .ct-slice-pie, .ct-series-h .ct-slice-donut-solid, .ct-series-h .ct-area {
  fill: #3050c5; }

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: #ff95a0; }

.ct-series-i .ct-slice-pie, .ct-series-i .ct-slice-donut-solid, .ct-series-i .ct-area {
  fill: #ff95a0; }

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: #ffe8d4; }

.ct-series-j .ct-slice-pie, .ct-series-j .ct-slice-donut-solid, .ct-series-j .ct-area {
  fill: #ffe8d4; }

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: #c80014; }

.ct-series-k .ct-slice-pie, .ct-series-k .ct-slice-donut-solid, .ct-series-k .ct-area {
  fill: #c80014; }

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: #878787; }

.ct-series-l .ct-slice-pie, .ct-series-l .ct-slice-donut-solid, .ct-series-l .ct-area {
  fill: #878787; }

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: #65cb59; }

.ct-series-m .ct-slice-pie, .ct-series-m .ct-slice-donut-solid, .ct-series-m .ct-area {
  fill: #65cb59; }

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: #6fa9d7; }

.ct-series-n .ct-slice-pie, .ct-series-n .ct-slice-donut-solid, .ct-series-n .ct-area {
  fill: #6fa9d7; }

.ct-series-o .ct-point, .ct-series-o .ct-line, .ct-series-o .ct-bar, .ct-series-o .ct-slice-donut {
  stroke: #a5b3e9; }

.ct-series-o .ct-slice-pie, .ct-series-o .ct-slice-donut-solid, .ct-series-o .ct-area {
  fill: #a5b3e9; }

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border-color: #E5E5E5; }

.select2-dropdown {
  border-color: #E5E5E5; }

.select2-search--dropdown:before {
  color: #7c91df; }

.select2-results__message {
  color: #7c91df !important; }

.select2-container--open .select2-dropdown--above {
  border-color: #7c91df; }

.select2-container--open .select2-dropdown--below {
  border-color: #7c91df; }

.select2-container--default .select2-search--dropdown .select2-search__field {
  color: #495057;
  background-color: #fff;
  border-color: #E5E5E5;
  -webkit-box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025);
          box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025); }
  .select2-container--default .select2-search--dropdown .select2-search__field:focus {
    border-color: #cccccc; }

.select2-container--default .select2-results__group {
  padding: 0.5rem 0;
  color: #8e8e8e; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background: white;
  color: #3050c5; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #7c91df;
  color: #fff; }

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-color: #7c91df; }

.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #7c91df; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: white;
  border-color: #90a2e4;
  color: #3050c5; }
  .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    color: #a5b3e9; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {
      color: #7c91df; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:active {
      -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset; }

.select2-container--default .select2-selection--single .select2-selection__clear {
  color: #ff2f44; }
  .select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: #ff162d; }

.select2-results__message {
  color: #ff2f44; }

.sorting_asc,
.sorting_desc,
.even .sorting_1 {
  background-color: rgba(124, 145, 223, 0.03); }

.odd .sorting_1 {
  background-color: rgba(124, 145, 223, 0.04); }

.table-dark .sorting_asc,
.table-dark .sorting_desc,
.table-dark .even .sorting_1 {
  background-color: rgba(255, 191, 135, 0.15); }

.table-dark .odd .sorting_1 {
  background-color: rgba(255, 191, 135, 0.15); }

table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  background-color: #7c91df; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  background-color: #47b73a; }

.dataTables_empty {
  color: #ff2f44; }

.dataTables_wrapper tr.child td.child .dtr-details:before {
  color: #53c446; }

.dataTables_wrapper tr.child td.child .dtr-details:after {
  background: #77d16d; }

div.dt-autofill-background {
  opacity: 0.2;
  background-color: #000; }

div.dt-autofill-handle {
  background: #7c91df; }

div.dt-autofill-select {
  background-color: #7c91df; }

/* FixedColumns */
.DTFC_LeftHeadWrapper:before,
.DTFC_LeftBodyWrapper:before,
.DTFC_LeftFootWrapper:before {
  background: #ff2f44; }

/* KeyTable */
table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
  -webkit-box-shadow: inset 0 0 0px 1px #7c91df;
          box-shadow: inset 0 0 0px 1px #7c91df;
  background: rgba(124, 145, 223, 0.1); }

table.dataTable:not(.table-dark) tr.dtrg-group td {
  background: #fff; }

tr.dt-rowReorder-moving {
  outline-color: #47b73a; }

table.dt-rowReorder-float {
  outline-color: #7c91df; }

/* Select */
table.dataTable.table-bordered .selected td {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable.table-bordered td.selected {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable tbody > tr.selected,
table.dataTable tbody > tr > .selected {
  -webkit-box-shadow: inset 0 0 0px 1px #7c91df;
          box-shadow: inset 0 0 0px 1px #7c91df;
  background: rgba(124, 145, 223, 0.1); }

.datepicker table tr td.old,
.datepicker table tr td.new {
  color: #c7c7c7; }

.datepicker table tr td.active:active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active,
.datepicker table tr td.selected,
.datepicker table tr td.selected.highlighted,
.datepicker table tr td span.active.active,
.datepicker table tr td span.focused {
  background-color: #90a2e4;
  border-color: #7c91df;
  color: #fff; }

.datepicker table tr td.active:active:hover,
.datepicker table tr td.active.highlighted:active:hover,
.datepicker table tr td.active.active:hover,
.datepicker table tr td.active.highlighted.active:hover,
.datepicker table tr td.active:active:focus,
.datepicker table tr td.active.highlighted:active:focus,
.datepicker table tr td.active.active:focus,
.datepicker table tr td.active.highlighted.active:focus,
.datepicker table tr td.active:active.focus,
.datepicker table tr td.active.highlighted:active.focus,
.datepicker table tr td.active.active.focus,
.datepicker table tr td.active.highlighted.active.focus,
.datepicker table tr td.selected:active:hover,
.datepicker table tr td.selected.highlighted:active:hover,
.datepicker table tr td.selected.active:hover,
.datepicker table tr td.selected.highlighted.active:hover,
.datepicker table tr td.selected:active:focus,
.datepicker table tr td.selected.highlighted:active:focus,
.datepicker table tr td.selected.active:focus,
.datepicker table tr td.selected.highlighted.active:focus,
.datepicker table tr td.selected:active.focus,
.datepicker table tr td.selected.highlighted:active.focus,
.datepicker table tr td.selected.active.focus,
.datepicker table tr td.selected.highlighted.active.focus,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.highlighted:hover {
  background-color: #6880da;
  border-color: #536fd5;
  color: #fff; }

.datepicker.datepicker-inline {
  border: 1px solid #ebedf2; }

.datepicker thead th.prev, .datepicker thead th.datepicker-switch, .datepicker thead th.next {
  color: #a1a8c3; }

.daterangepicker table tr td.old,
.daterangepicker table tr td.new {
  color: #c7c7c7; }

.daterangepicker table tr td.active:active,
.daterangepicker table tr td.active.highlighted:active,
.daterangepicker table tr td.active.active,
.daterangepicker table tr td.active.highlighted.active,
.daterangepicker table tr td.selected,
.daterangepicker table tr td.selected.highlighted,
.daterangepicker table tr td span.active.active,
.daterangepicker table tr td span.focused {
  background-color: #90a2e4;
  color: #fff; }

.daterangepicker table tr td.active:active:hover,
.daterangepicker table tr td.active.highlighted:active:hover,
.daterangepicker table tr td.active.active:hover,
.daterangepicker table tr td.active.highlighted.active:hover,
.daterangepicker table tr td.active:active:focus,
.daterangepicker table tr td.active.highlighted:active:focus,
.daterangepicker table tr td.active.active:focus,
.daterangepicker table tr td.active.highlighted.active:focus,
.daterangepicker table tr td.active:active.focus,
.daterangepicker table tr td.active.highlighted:active.focus,
.daterangepicker table tr td.active.active.focus,
.daterangepicker table tr td.active.highlighted.active.focus,
.daterangepicker table tr td.selected:active:hover,
.daterangepicker table tr td.selected.highlighted:active:hover,
.daterangepicker table tr td.selected.active:hover,
.daterangepicker table tr td.selected.highlighted.active:hover,
.daterangepicker table tr td.selected:active:focus,
.daterangepicker table tr td.selected.highlighted:active:focus,
.daterangepicker table tr td.selected.active:focus,
.daterangepicker table tr td.selected.highlighted.active:focus,
.daterangepicker table tr td.selected:active.focus,
.daterangepicker table tr td.selected.highlighted:active.focus,
.daterangepicker table tr td.selected.active.focus,
.daterangepicker table tr td.selected.highlighted.active.focus,
.daterangepicker table tr td.selected:hover,
.daterangepicker table tr td.selected.highlighted:hover {
  background-color: #6880da;
  color: #fff; }

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
  border-color: #a1a8c3; }

.daterangepicker .in-range.available {
  background-color: #fff5ed; }

.daterangepicker .off.ends.in-range.available {
  background-color: white; }

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: #ffdaba; }

.daterangepicker .calendar-table table thead tr th.month {
  color: #a1a8c3; }

.daterangepicker .ranges li.active {
  background-color: #7c91df; }

.irs--flat .irs-bar,
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single,
.irs--flat .irs-handle > i:first-child {
  background-color: #7c91df; }

.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  border-top-color: #7c91df; }

.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child {
  background-color: #6880da; }

.irs--big .irs-bar {
  background-color: #a5b3e9;
  border-color: #7c91df;
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), color-stop(30%, #a5b3e9), to(#7c91df));
  background: linear-gradient(to bottom, #ffffff 0%, #a5b3e9 30%, #7c91df 100%); }

.irs--big .irs-from,
.irs--big .irs-to,
.irs--big .irs-single {
  background: #7c91df; }

.irs--modern .irs-bar {
  background: #3fa434;
  background: -webkit-gradient(linear, left top, left bottom, from(#53c446), to(#3fa434));
  background: linear-gradient(to bottom, #53c446 0%, #3fa434 100%); }

.irs--modern .irs-from,
.irs--modern .irs-to,
.irs--modern .irs-single {
  background-color: #47b73a; }

.irs--modern .irs-from:before,
.irs--modern .irs-to:before,
.irs--modern .irs-single:before {
  border-top-color: #47b73a; }

.irs--sharp .irs-bar,
.irs--sharp .irs-handle,
.irs--sharp .irs-from,
.irs--sharp .irs-to,
.irs--sharp .irs-single {
  background-color: #ff2f44; }

.irs--sharp .irs-handle > i:first-child,
.irs--sharp .irs-from:before,
.irs--sharp .irs-to:before,
.irs--sharp .irs-single:before {
  border-top-color: #ff2f44; }

.irs--sharp .irs-min,
.irs--sharp .irs-max {
  background-color: #e20017; }

.irs--round .irs-handle {
  border-color: #4791cc; }

.irs--round .irs-bar,
.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single {
  background-color: #4791cc; }

.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before {
  border-top-color: #4791cc; }

body:not(.mod-pace-custom) .pace .pace-progress {
  background: #7c91df; }

.mod-pace-custom .pace {
  background: #fff; }
  .mod-pace-custom .pace .pace-progress {
    background-color: #7c91df;
    background-image: linear-gradient(135deg, #7c91df 0%, #7c91df 25%, #536fd5 25%, #536fd5 50%, #7c91df 50%, #7c91df 75%, #536fd5 75%, #536fd5 100%); }

.mod-pace-custom.pace-running .page-content:before {
  background-color: white; }

/* #Reset userselect
========================================================================== */
#myapp-0 {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }

#myapp-8 {
  -webkit-box-shadow: 0 0 0 3px #000000;
          box-shadow: 0 0 0 3px #000000; }

/*# sourceMappingURL=cust-theme-8.css.map */
