<?php $user->restrictionUser(true, $conn); ?>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager เติมเงิน item <span class="fw-300"><i>เติมเงิน</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table class="table table-bordered table-hover table-striped w-100">
                            <thead>
                                <tr>
                                    <th>id</th>
                                    <th>Userid</th>
                                    <th>Transactionid</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Added_time</th>
                                    <th><?php echo T_ACTION; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 30;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM  WEB_Truewallet order by id desc";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);
                      
                        foreach ($page as $row) {

                            ?>
                                <?php
                                    
                                $idxrow = $row[0];
                                $selectUsersData = "SELECT * FROM WEB_Truewallet WHERE id = '$idxrow'";
                                $selectUsersDataParam = array();
                                $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
                                $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                            ?>
                                <tr>
                                    <td><?php echo $row[0]; ?></td>
                                    <td><?php echo $row[1]; ?></td>
                                    <td><?php echo $row[2]; ?></td>
                                    <td><?php echo $row[3]; ?></td>
                                    <td><?php echo $row[4]; ?></td>

                                    <td><span
                                            class="badge
								                <?php echo $label = ($row[5] == 'check_waiting' ? ' badge-warning badge-pill' : $label = $row[5] == 'check_pass' ? ' badge-danger badge-pill' : ($row[5] == 'check_success' ? ' badge-success badge-pill' : 'badge-light badge-pill'));?>">
                                            <?php echo $status = ($row[5] == 'check_waiting' ? 'รอตรวจสอบ' : $status = $row[5] == 'check_pass' ? 'ไม่ผ่าน' : ($row[5] == 'check_success' ? 'ผ่าน' : 'Unknow status'));?></span>
                                    </td>
                                    <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[6])); ?></td>
                                    <td>
                                        <form method="post" name="j_add_donate" action="">
                                            <div class="j_alert"></div>
                                            <div class="form-inline">
                                                <label class="sr-only" for="inlineFormInputName2">Name</label>
                                                <select name="statuscheck" class="custom-select my-1 mr-sm-2" id="inlineFormCustomSelectPref">
                                                    <option value="check_success">ผ่าน</option>
                                                    <option value="check_pass">ไม่ผ่าน</option>
                                                </select>
                                                <input type="hidden" name="id" value="<?php echo $row[0]; ?>">
                                                <input type="hidden" name="userid" value="<?php echo $row[1]; ?>">
                                                <button type="submit" class="btn btn-primary"><i
                                                    class="fa fa-chevron-circle-right"></i>ยืนยัน</button>
                                                    <img src="assets/images/loading/loader.gif" class="load"
                                                alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                            </div>
                                            
                                         
                                        </form>
                                    </td>

                                </tr>
                                <?php	} ?>
                            </tbody>
                        </table>

                    </div>
                </div>

                </section>