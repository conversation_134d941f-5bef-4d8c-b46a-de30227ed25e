<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');


function littleEndianHexToBigInt($hex) {
    $bytes = str_split($hex, 2);
    $reversed = implode('', array_reverse($bytes));
    return hexdec($reversed);
}

function bigIntToLittleEndianHex($value, $lengthBytes) {
    $hex = dechex($value);
    $hex = str_pad($hex, $lengthBytes * 2, '0', STR_PAD_LEFT);
    $bytes = str_split($hex, 2);
    return implode('', array_reverse($bytes));
}

function splitequipment($characterId, $binaryData) {
    $items = [];
    $hex = bin2hex($binaryData);
    $maxLength = strlen($hex);
    $pos = 0;

    while (($pos + 60) <= $maxLength) {
        $chunk = substr($hex, $pos, 60);
        if ($chunk === str_repeat('00', 60)) {
            $pos += 60;
            continue;
        }

        $KindIdx    = littleEndianHexToBigInt(substr($chunk, 0, 16));
        $ItemIndex  = $KindIdx & 0x6000fff;
        $Serial     = littleEndianHexToBigInt(substr($chunk, 16, 16));
        $Option     = littleEndianHexToBigInt(substr($chunk, 32, 16));
        $Slot       = littleEndianHexToBigInt(substr($chunk, 48, 4));
        $Period     = littleEndianHexToBigInt(substr($chunk, 52, 8));

        $hexData =
            bigIntToLittleEndianHex($KindIdx, 8) .
            bigIntToLittleEndianHex($Serial, 8) .
            bigIntToLittleEndianHex($Option, 8) .
            bigIntToLittleEndianHex($Slot, 2) .
            bigIntToLittleEndianHex($Period, 4);

        $items[$Slot] = [
            'KindIdx'   => $KindIdx,
            'itemIndex' => $ItemIndex,
            'Serial'    => $Serial,
            'Option'    => $Option,
            'Period'    => $Period,
            'HexData'   => strtoupper($hexData)
        ];
        $pos += 60;
    }

    return $items;
}

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
} elseif (isset($_POST['id'])) {
    $id = intval($_POST['id']);
}

if (isset($_POST['update_equipment']) && isset($_POST['HexData'])) {
    // รับข้อมูล hex string จาก textarea (ไม่ต้องแปลงเป็น binary)
    $hex = preg_replace('/^0x/i', '', $_POST['HexData']);
    // อัปเดตลง DB (UserNum = $id) โดยตรงเป็น hex string
    $query = "UPDATE " . DATABASE_SV . ".dbo.cabal_warehouse_table SET Data = CONVERT(varbinary(max), ?, 2) WHERE UserNum = ?";
    $stmt = sqlsrv_query($conn, $query, array($hex, $id));
    if ($stmt) {
        echo 'Update Success';
    } else {
        echo 'Update Failed';
    }
    exit;
}

if (isset($_GET['ajax'])) {
    $items = [];
    $query = "SELECT UserNum, Data FROM " . DATABASE_SV . ".dbo.cabal_warehouse_table WHERE UserNum = ?";
    $stmt = sqlsrv_query($conn, $query, array($id));
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $items = splitequipment($row['UserNum'], $row['Data']);
    }
    echo json_encode($items);
    exit;
}
?>
