<?php

function filter($var) {
    return stripslashes(htmlspecialchars($var));
}

// require
require_once('_app/php/paypal.class.php');
$p = new paypal_class;

// variables
$getActionBuy = filter_input(INPUT_GET, 'action', FILTER_VALIDATE_BOOLEAN);
$getPackID = filter_input(INPUT_GET, 'packid', FILTER_VALIDATE_INT);

// if $getAction not exists or is false, break code here
if (!$getActionBuy)
    exit;

switch ($getActionBuy) {

    case true:
        $custom = $userLogin->recUserAccount('CustomerID', $conn);
        switch ($getPackID) {
            case '1':
                $itemNumber = '1';
                $cost = '90';
                $wut = 'Beginner Pack';
                break;

            case '2':
                $itemNumber = '2';
                $cost = '150';
                $wut = 'Premium Pack';
                break;

            case '3':
                $itemNumber = '3';
                $cost = '300';
                $wut = 'Pioneer Pack';
                break;

            case '4':
                $itemNumber = '4';
                $cost = '500';
                $wut = 'Pioneer Pack';
                break;

            case '5':
                $itemNumber = '5';
                $cost = '1000';
                $wut = 'Pioneer Pack';
                break;

            default:
                $cost = '0';
                $wut = 'Unknown package';
                break;
        }

        $p->add_field('business', STORE_EMAIL);
        $p->add_field('image_url', STORE_IMAGE_URL);
        $p->add_field('return', HOME_WITH_PATH . '?url=store/returnPayment&action=process');
        $p->add_field('custom', $custom);
        $p->add_field('cancel_return', HOME_WITH_PATH . '?url=store/returnPayment&action=cancel');
        $p->add_field('notify_url', ROOT_WITH_PATH . '/return_store/return.php');
        $p->add_field('item_number', filter($getPackID));
        $p->add_field('item_name', $wut);
        $p->add_field('email', $userLogin->recUserAccount('email', $conn));
        $p->add_field('first_name', $userLogin->recUserInfo('name', $conn));
        $p->add_field('last_name', $userLogin->recUserInfo('last_name', $conn));
        $p->add_field('amount', $cost);
        $p->add_field('currency_code', STORE_CURRENCY);

        $p->submit_paypal_post();
        break;
}
?>