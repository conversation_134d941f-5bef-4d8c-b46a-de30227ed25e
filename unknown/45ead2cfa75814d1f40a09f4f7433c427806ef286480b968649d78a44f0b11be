<?php $user->restrictionUser(true, $conn); ?>
<header class="page-header">
    <h2>ItemManager</h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span><?php echo PT_MANAGEPLAYERS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>


<div class="col-lg-4">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">แอดไอเท็ม</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">

                <?php

if (isset($_POST['btn_savechange'])) {
 // variable
 $inputid       = strip_tags(trim($_POST['input_id']));
 $inputitem     = strip_tags(trim($_POST['input_Item']));
 $inputOption   = strip_tags(trim($_POST['input_Option']));
 $inputDur      = strip_tags(trim($_POST['input_Dur']));
 $inputUpgrade  = strip_tags(trim($_POST['input_Upgrade']));
 $inputSlot     = strip_tags(trim($_POST['input_Slot']));
 $inputQuantity = strip_tags(trim($_POST['input_quantity']));

 //admin id //
 $adminid  = $userLogin->recUserAccount('ID', $conn);
 $adminidx = $userLogin->recUserAccount('UserNum', $conn);

 $binding_id       = 4096 + $inputitem;
 $binding_chars    = 528384 + $inputitem;
 $binding_chars_eq = 1572864 + $inputitem;

 // condition
 if (empty($inputid)) {
  $returnWarning = "โปรดกรอกข้อมูล USER ID";
 } elseif (empty($inputitem)) {
  $returnWarning = "โปรดกรอกข้อมูล รหัสไอเท็ม";
 } else {

  if (empty($inputOption) && (empty($inputSlot))) {
   $inputOption = 0;
  } elseif ("1" == $inputSlot) {
   $inputOption = *********;
  } elseif ("2" == $inputSlot) {
   $inputOption = *********;
  } elseif ("3" == $inputSlot) {
   $inputOption = *********;
  } elseif ("4" == $inputSlot) {
   $inputOption = **********;
  } else {
   $returnError = "ERROR :: ระบบ Option ผิดพลาด";
  }

  $itemhex    = dechex($inputitem);
  $itemdexlen = strlen($itemhex);
  // $detail =   'HEXCODE: '.$itemhex. ' รหัสไอเท็ม: '.$itemdex. ' ออฟชั่น: '.$inputOption. ' ตำแหน่ง: '.$itemhex;
  if (strlen($itemhex) == "1") {
   $itemdex = hexdec($inputUpgrade . '0' . dechex($inputitem));
   // $returnSuccess = "SUCCESS1";
  } elseif (strlen($itemhex) == "2") {
   $itemdex = hexdec($inputUpgrade . '0' . dechex($inputitem));
   // $returnSuccess = "SUCCESS1";
  } elseif (strlen($itemhex) == "3") {
   $itemdex = hexdec($inputUpgrade . dechex($inputitem));
   //$returnSuccess = "SUCCESS2";
  } elseif (strlen($itemhex) == "4") {
   $itemdex = hexdec($inputUpgrade . dechex($inputitem));
   //$returnSuccess = "SUCCESS3";
  } elseif (strlen($itemhex) > 4) {
   $itemdex = $inputitem;
   //$returnSuccess = "SUCCESS3";
  } else {
   $returnError = "ERROR :: ระบบ Upgrade ผิดพลาด {$detail}";
  }

  $detail  = 'HEXCODE: ' . $itemhex . ' รหัสไอเท็ม: ' . $itemdex . ' ออฟชั่น: ' . $inputOption . ' ตำแหน่ง: ' . $itemdexlen;
  $dateNow = date('Y-m-d H:i:s');
  //   $returnSuccess = "SUCCESS :: {$detail} ระบบแอดไอเท็มเรียบร้อยแล้ว";
  // get conversion info
  $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE ID  = '$inputid'";
  $selectauthParam = array();
  $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
  $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
  $UserNum         = $selectauthFetch['UserNum'];

  if (sqlsrv_rows_affected($selectauthQuery)) {
   for ($i = 0; $i <= $inputQuantity; $i++) {
    $CashItemID       = "EXECUTE [" . DATABASE_CCA . "].[dbo].up_AddMyCashItemByItem ?, ?, ?, ?, ?, ?";
    $CashItemIDParams = array($UserNum, '0', '1', $itemdex, $inputOption, $inputDur);
    $CashItemIDQuery  = sqlsrv_query($conn, $CashItemID, $CashItemIDParams);
   }
   if ($CashItemIDQuery) {
    //unset($UserNum, $inputPassword, $inputRePassword, $inputEmail, $inputphone);
    $zpanel->generateWebLog($conn, '3', $adminidx, 'แอดไอเท็ม', "ให้ไอดี {$inputid} รหัสไอเท็ม : {$inputitem}/{$itemdex} ออฟชั่น : {$inputOption} Dur : {$inputDur}");
    $returnSuccess = "SUCCESS :: {$detail} ";

   } else {
    $returnError = "ERROR :: ระบบผิดพลาด {$detail}";
   }
  } else {
   $returnWarning = "WARNING :: ไม่มี UserID นี้";
  }

 }
}

if (isset($_POST['btn_savechange_checking'])) {
 // variable
 $inputid = strip_tags(trim($_POST['input_id']));
 // condition
 if (empty($inputid)) {
  $returnWarning = "โปรดกรอกข้อมูล USER ID";
 } else {

  // get conversion info
  $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE ID  = '$inputid'";
  $selectauthParam = array();
  $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
  $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
  $UserNum         = $selectauthFetch['UserNum'];
  $ID              = $selectauthFetch['ID'];
  if ($inputid == $ID) {
   //unset($UserNum, $inputPassword, $inputRePassword, $inputEmail, $inputphone);
   $returnSuccess = "ข้อมูลผู้ใช้นี้ UserNum = ({$UserNum}) ID = ({$ID})";
  } else {
   $returnError = "ERROR :: ระบบผิดพลาด";
  }
 }
}
?>
                <?php if (isset($returnSuccess)) { ?>
                <div class="alert alert-success j_alert"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                <div class="alert alert-warning j_alert"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                <div class="alert alert-danger j_alert"><?php echo $returnError; ?></div>
                <?php } ?>

                <form role="form" method="post" name="j_add_adminitems" enctype="multipart/form-data">
                    <div class="col-lg-12 j_alert"></div>

                    <div class="form-group">
                        <label for="input_id" class="control-label">UserID: </label>
                        <input type="text" name="input_id" class="form-control" id="searchByName" data-role="tagsinput"
                            pattern="[a-zA-Z0-9]+$" placeholder="ไอดีผู้เล่น" value="<?php if (isset($inputid)) {
 echo $inputid;
}
?>">
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="input_Item" class="control-label">Code Item:</label>
                                <input type="text" name="input_Item" class="form-control" id="input_Item"
                                    data-role="tagsinput" placeholder="รหัสไอเท็ม" pattern="[0-9]+$" value="<?php if (isset($inputitem)) {
 echo $inputitem;
}
?>">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="Option" class="control-label">Option: </label><code>{ ea }</code>
                                <input type="text" name="input_Option" class="form-control" id="input_Option"
                                    data-role="tagsinput" pattern="[0-9]+$" placeholder="ออฟชั่น" value="<?php if (isset($inputOption)) {
 echo $inputOption;
}
?>">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="input_Dur" class="control-label">DurationIdx: </label>
                                <select data-plugin-selectTwo name="input_Dur" id="input_Dur" pattern="[0-9]+$"
                                    class="form-control populate placeholder"
                                    data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                                    <optgroup label="อายุ ชม.">
                                        <option value="31">ถาวร</option>
                                        <option value="1">1 ชม.</option>
                                        <option value="2">2 ชม.</option>
                                        <option value="3">3 ชม.</option>
                                        <option value="4">4 ชม.</option>
                                        <option value="5">5 ชม.</option>
                                        <option value="6">6 ชม.</option>
                                        <option value="7">10 ชม.</option>
                                        <option value="8">12 ชม</option>
                                        <option value="9">1 วัน</option>
                                        <option value="10">3 วัน</option>
                                        <option value="11">5 วัน</option>
                                        <option value="12">7 วัน</option>
                                        <option value="13">10 วัน</option>
                                        <option value="14">14 วัน</option>
                                        <option value="15">15 วัน</option>
                                        <option value="16">20 วัน</option>
                                        <option value="17">30 วัน</option>
                                        <option value="18">45 วัน</option>
                                        <option value="19">60 วัน</option>
                                        <option value="20">90 วัน</option>
                                        <option value="21">100 วัน</option>
                                        <option value="22">120 วัน</option>
                                        <option value="23">180 วัน</option>
                                        <option value="24">270 วัน</option>
                                        <option value="25">365 วัน</option>
                                        <option value="0">ไม่มีอายุ</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>


                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="input_Item" class="control-label">bind-id:</label>
                                <input type="text" class="form-control" pattern="[0-9]+$" placeholder="ไอดี"
                                    readonly="readonly" value="<?php if (isset($binding_id)) {
 echo $binding_id;
}
?>">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="input_Item" class="control-label">bind-Char:</label>
                                <input type="text" class="form-control" pattern="[0-9]+$" placeholder="ตัวละคร"
                                    readonly="readonly" value="<?php if (isset($binding_chars)) {
 echo $binding_chars;
}
?>">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="input_Item" class="control-label">bind-equ:</label>
                                <input type="text" " class=" form-control" pattern="[0-9]+$" placeholder="สวมใส่"
                                    readonly="readonly" value="<?php if (isset($binding_chars_eq)) {
 echo $binding_chars_eq;
}
?>">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <p class="output">Slot: <code><b>0</b></code></p>
                                <div class="m-md slider-primary" data-plugin-slider
                                    data-plugin-options='{ "value": 0, "range": "min", "max": 4 }'
                                    data-plugin-slider-output="#listenSlider">
                                    <input name="input_Slot" id="listenSlider" type="hidden" value="0" />
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label for="input_Upgrade" class="control-label">Upgrade:<code>{S/W/B}</code>
                                </label>
                                <select data-plugin-selectTwo name="input_Upgrade" id="input_Upgrade"
                                    pattern="[a-zA-Z0-9]+$" class="form-control populate placeholder"
                                    data-plugin-options='{ "placeholder": "Select Upgrade", "allowClear": true }'>
                                    <optgroup label="Upgrade ไม่ผูกไอดี">
                                        <option value="0">+0 </option>
                                        <option value="2">+1</option>
                                        <option value="4">+2</option>
                                        <option value="6">+3</option>
                                        <option value="8">+4</option>
                                        <option value="A">+5</option>
                                        <option value="C">+6</option>
                                        <option value="E">+7</option>
                                        <option value="10">+8</option>
                                        <option value="12">+9</option>
                                        <option value="14">+10</option>
                                        <option value="16">+11</option>
                                        <option value="18">+12</option>
                                        <option value="1A">+13</option>
                                        <option value="1C">+14</option>
                                        <option value="1E">+15</option>
                                        <option value="20">+16</option>
                                        <option value="22">+17</option>
                                        <option value="24">+18</option>
                                        <option value="26">+19</option>
                                        <option value="28">+20</option>
                                    </optgroup>
                                    <optgroup label="Upgrade ผูกไอดี">
                                        <option value="1">+0 binding</option>
                                        <option value="3">+1 binding</option>
                                        <option value="5">+2 binding</option>
                                        <option value="7">+3 binding</option>
                                        <option value="9">+4 binding</option>
                                        <option value="B">+5 binding</option>
                                        <option value="D">+6 binding</option>
                                        <option value="F">+7 binding</option>
                                        <option value="11">+8 binding</option>
                                        <option value="13">+9 binding</option>
                                        <option value="15">+10 binding</option>
                                        <option value="17">+11 binding</option>
                                        <option value="19">+12 binding</option>
                                        <option value="1B">+13 binding</option>
                                        <option value="1D">+14 binding</option>
                                        <option value="1F">+15 binding</option>
                                        <option value="21">+16 binding</option>
                                        <option value="23">+17 binding</option>
                                        <option value="25">+18 binding</option>
                                        <option value="27">+19 binding</option>
                                        <option value="29">+20 binding</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label for="input_quantity" class="control-label">Quantity:</label>
                                <select data-plugin-selectTwo name="input_quantity" id="input_quantity"
                                    pattern="[0-9]+$" class="form-control populate placeholder"
                                    data-plugin-options='{ "placeholder": "Select quantity", "allowClear": true }'>
                                    <optgroup label="Quantity.">
                                        <option value="0">1 Terms.</option>
                                        <option value="1">2 Terms.</option>
                                        <option value="2">3 Terms.</option>
                                        <option value="3">4 Terms.</option>
                                        <option value="4">5 Terms.</option>
                                        <option value="5">6 Terms.</option>
                                        <option value="6">7 Terms.</option>
                                        <option value="9">10 Terms.</option>
                                        <option value="14">15 Terms</option>
                                        <option value="19">20 Terms</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>

                    </div>
                    <br> <br>
                    <div class="form-group">
                        <button type="submit" name="btn_savechange"
                            class="mb-1 mt-1 mr-1 btn btn-primary btn-block">ยืนยันไอเท็ม</button>
                    </div>
                    <div class="form-group">
                        <button type="submit" name="btn_savechange_checking"
                            class="mb-1 mt-1 mr-1 btn btn-danger btn-block">ตรวจสอบ ID</button>
                    </div>
                </form>

            </div>
        </div>
    </section>

    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">ค้นหา UserID</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table id='empTable_item' class="table table-bordered table-striped table-condensed mb-none">
                    <thead>
                        <tr>
                            <th>UserNum</th>
                            <th>ID</th>
                            <th>Email</th>
                        </tr>
                    </thead>


                </table>
            </div>
        </div>
    </section>

</div>

<div class="col-lg-4">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">ค้นหา item</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table id="datatable-item" class="table table-bordered table-striped table-condensed mb-none"
                    data-url="_data/items.json">
                    <thead>
                        <tr>
                            <th>ItemID</th>
                            <th>Name</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
</div>


<div class="col-lg-4">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>
            <h2 class="panel-title">ค้นหา Dungeon</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table id="datatable-dun_world" class="table table-bordered table-striped table-condensed mb-none"
                    data-url="_data/dun_world.json">
                    <thead>
                        <tr>
                            <th>item</th>
                            <th>level</th>
                            <th>dun</th>
                            <th>world</th>
                            <th>name</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
</div>
<div class="col-lg-4">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">ค้นหา Monster</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table id="datatable-monster" class="table table-bordered table-striped table-condensed mb-none"
                    data-url="_data/monsters.json">
                    <thead>
                        <tr>
                            <th>MonsterID</th>
                            <th>MonsterName</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
</div>
<div class="col-lg-4">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">ค้นหา Quest</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table id="datatable-quest" class="table table-bordered table-striped table-condensed mb-none"
                    data-url="_data/quest.json">
                    <thead>
                        <tr>
                            <th>quest id</th>
                            <th>quest</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
</div>
<div class="col-lg-12">
    <section class="panel">
        <header class="panel-heading">
            <div class="panel-actions">
                <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
                <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
            </div>

            <h2 class="panel-title">ไอเท็มแอดล่าสุด</h2>
        </header>
        <div class="panel-body">
            <div class="col-lg-12">
                <table class="table table-bordered table-striped table-condensed mb-none" id="datatable-default">
                    <thead>
                        <tr>
                            <th>Id</th>
                            <th>UserNum</th>
                            <th>ID</th>
                            <th>ItemKindIdx</th>
                            <th>ItemOpt</th>
                            <th>DurationIdx</th>
                            <th>RegDate</th>
                            <th>IsUse</th>
                            <th>UseDate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
$selectLastUsers      = "SELECT TOP 17 * FROM [" . DATABASE_CCA . "].[dbo].MyCashItem ORDER BY id DESC";
$selectLastUsersParam = array();
$selectLastUsersOpt   = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
$selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

if (sqlsrv_num_rows($selectLastUsersQuery)) {
 while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

  $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '" . $resLastUsers['UserNum'] . "'";
  $selectauthParam = array();
  $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
  $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

  ?>
                        <tr>
                            <td><?php echo $resLastUsers['Id']; ?></td>
                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                            <td><?php echo $selectauthFetch['ID']; ?></td>
                            <td><?php echo $resLastUsers['ItemKindIdx']; ?></td>
                            <td><?php echo $resLastUsers['ItemOpt']; ?></td>
                            <td><?php echo $resLastUsers['DurationIdx']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['RegDate'])); ?></td>
                            <td><?php echo $resLastUsers['IsUse']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['UseDate'])); ?></td>
                        </tr>
                        <?php
}
} else {
 echo W_NOTHING_RETURNED;
}
?>
                    </tbody>
                </table>
            </div>

        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    var dataTable = $('#empTable_item').DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'post',
        //'searching': false, // Remove default Search Control
        'ajax': {
            url: '_app/aj_loadid_id.php',
            type: "post",
            error: function() {
                $("#employee_grid_processing").css("display", "none");
            }
        },
        'columns': [{
                data: 'UserNum'
            },
            {
                data: 'ID'
            },
            {
                data: 'Email'
            }
        ]
    });

    $('#searchByName').keyup(function() {
        dataTable.draw();
    });

    $('#searchByGender').change(function() {
        dataTable.draw();
    });
});
</script>