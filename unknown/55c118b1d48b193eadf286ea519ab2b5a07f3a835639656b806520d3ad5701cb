<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
?>

<header class="page-header">
    <h2>รวมข้อมูลผู้เล่น</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="home.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>server-admin</span></li>
            <li><span>general-statistics</span></li>
        </ol>
        <a class="sidebar-right-toggle"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                <div class="col-lg-6">
                        <h2>สมัครไอดีล่าสุด<small> ผู้เล่นสมัครไอดีล่าสุด</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th><?php echo T_EMAIL; ?></th>
                                    <th><?php echo T_REGISTEREDIN; ?></th>
                                    <th><?php echo T_LAST_LOGIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table ORDER BY createDate DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><?php echo $resLastUsers['Email']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['createDate'])); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['LoginTime'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                        </div>
                    <div class="col-lg-6">
                        <h2><?php echo T_LAST_USERS; ?> <small><?php echo H_MAX_5_RESULTS; ?></small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th><?php echo T_EMAIL; ?></th>
                                    <th><?php echo T_REGISTEREDIN; ?></th>
                                    <th><?php echo T_LAST_LOGIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table ORDER BY LoginTime DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><?php echo $resLastUsers['Email']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['createDate'])); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['LoginTime'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>

                    <div class="col-lg-6">
                        <h2><?php echo T_LAST_CHARS; ?> <small><?php echo H_MAX_5_RESULTS; ?></small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CHARID; ?></th>
                                    <th><?php echo T_YOURNAME; ?></th>
                                    <th><?php echo T_LEVEL; ?></th>
                                    <th><?php echo T_GAMEMAP; ?></th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY LoginTime DESC";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                            <td><?php echo $resChars['LEV']?></td>
                                            <td><?php echo $resChars['WorldIdx']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resChars['CreateDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/chars" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-lg-12">
                        <h2><?php echo T_LAST_CLANS; ?> <small><?php echo H_MAX_5_RESULTS; ?></small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CLANID; ?></th>
                                    <th><?php echo T_CLAN_NAME; ?></th>
                                    <th><?php echo T_CLAN_TAG; ?></th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectClans = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].Guild ORDER BY Level DESC";
                                $selectClansParam = array();
                                $selectClansOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectClansQuery = sqlsrv_query($conn, $selectClans, $selectClansParam, $selectClansOpt);

                                if (sqlsrv_num_rows($selectClansQuery)) {
                                    while ($resClans = sqlsrv_fetch_array($selectClansQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resClans['GuildNo']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resClans['GuildName']); ?></td>
                                            <td><?php echo $resClans['Level']; ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($resClans['RegDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/clans" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <h2 class="panel-heading clean">
                <?php echo T_HELPDESK; ?> <small><?php echo T_HELPDESK_DESC; ?> (<?php echo H_MAX_5_RESULTS; ?>) <span class="text-red">(<?php echo T_ONLYWAITING; ?>)</span></small> 
            </h2>
            <div class="panel-body">
                <div class="row">
                    <div class="col-lg-12">
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_TICKET_TITLE; ?></th>
                                    <th><?php echo T_TICKET_RESUME; ?></th>
                                    <th><?php echo T_TICKET_STATUS; ?></th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectTickets = "SELECT TOP 5 * FROM WEB_H_Tickets WHERE status = '1' ORDER BY id DESC";
                                $selectTicketsParam = array();
                                $selectTicketsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, $selectTicketsParam, $selectTicketsOpt);

                                if (sqlsrv_num_rows($selectTicketsQuery)) {
                                    while ($resTickets = sqlsrv_fetch_array($selectTicketsQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo substr($resTickets['title'], 0, 15); ?></td>
                                            <td><?php echo $content = (strlen($resTickets['content']) > 15 ? substr($resTickets['title'], 0, 20) . ' ...' : $resTickets['content']); ?></td>
                                            <td><?php echo $status = ($resTickets['status'] == '1' ? T_WAITING : ($resTickets['status'] == '2' ? T_REPLIED : ($resTickets['status'] == '3' ? T_CLOSED : ($resTickets['status'] == '4' ? T_SOLVED : T_UNKNOWNSTATUS)))); ?></td>
                                            <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resTickets['createdate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=helpdesk/a/tickets" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>