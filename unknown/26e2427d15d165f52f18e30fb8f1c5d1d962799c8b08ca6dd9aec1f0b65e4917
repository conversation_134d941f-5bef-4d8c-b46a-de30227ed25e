<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code. 
 */
?>
<section class="panel">
<div class="panel-body">
    <h2><?php echo PT_MYTICKETS; ?> <small><?php echo PT_MYTICKETS_DESC; ?></small></h2>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="btn-group pull-right">
                            <a href="?url=helpdesk/u/tickets" class="btn btn-success btn-block"><i class="fa fa-mail-reply"></i> <?php echo B_GOBACK_MYTICKETS; ?></a>
                        </div>
                    </div>
                </div>
                <hr class="clean">
                <?php
                $formTicket = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                if (isset($formTicket['btn_create'])) {
                    // use filter_inpu_array to more security
                    // cond
                    if (empty($formTicket['title'])) {
                        $returnWarning = W_EMPTY_TITLE;
                    } else if (empty($formTicket['content'])) {
                        $returnWarning = W_H_CREATE_CONTENT_EMPTY;
                    } else if (strlen($formTicket['content']) < 20) {
                        $returnWarning = W_H_CREATE_CONTENT_LENGHT;
                    } else if ($formTicket['category'] == '-1') {
                        $returnWarning = W_H_CATEGORY_EMPTY;
                    } else {

                        // variables
                        //$convertContent = htmlspecialchars($formTicket['content']); - not needed

                        // create ticket
                        $createTicket = "INSERT INTO WEB_H_Tickets (PCustomerID, title, content, category, status) VALUES"
                                . " ('$getCustomerID', '$formTicket[title]', '$formTicket[content]', '$formTicket[category]', '1')";
                        $createTicketParam = array();
                        $createTicketQuery = sqlsrv_query($conn, $createTicket, $createTicketParam);

                        if (sqlsrv_rows_affected($createTicketQuery)) {
                            $returnSuccess = S_CREATE_TICKET;
                        } else {
                            $returnError = E_CREATE_TICKET;
                        }
                    }
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                <?php } ?>
                <form name="formTicket" method="post" action="" role="form">
                    <div class="form-group">
                        <input type="text" name="title" class="form-control" placeholder="<?php echo T_H_PH_TITLE; ?>" value="<?php
                        if (isset($formTicket['title'])) {
                            echo $formTicket['title'];
                        }
                        ?>">
                    </div>
                    <div class="form-group">
                        <textarea class="form-control" name="content" placeholder="<?php echo T_H_PH_CONTENT; ?>" style="height: 200px"><?php
                            if (isset($formTicket['content'])) {
                                echo $formTicket['content'];
                            }
                            ?></textarea>
                    </div>
                    <div class="form-group">
                        <select class="form-control" name="category">
                            <option value="-1"><?php echo T_SELECT_TICKET_CAT; ?></option>
                            <?php
                            $selectCategories = "SELECT * FROM WEB_H_Category WHERE status = 1 ORDER BY name ASC";
                            $selectCategoriesParam = array();
                            $selectCategoriesOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectCategoriesQuery = sqlsrv_query($conn, $selectCategories, $selectCategoriesParam, $selectCategoriesOpt);
                            if (sqlsrv_num_rows($selectCategoriesQuery)) {
                                while ($resCat = sqlsrv_fetch_array($selectCategoriesQuery, SQLSRV_FETCH_ASSOC)) {
                                    ?>
                                    <option value="<?php echo $resCat['id']; ?>"><?php echo $resCat['name']; ?></option>
                                    <?php
                                }
                            } else {
                                ?>
                                <option value="0"><?php echo W_H_NOHAVECAT; ?>!</option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="submit" class="btn btn-success" name="btn_create" value="<?php echo B_SEND; ?>">
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>
</div>
</section>