<?php
declare(strict_types=1);

// เพิ่มเวลาการทำงานและหน่วยความจำ
set_time_limit(600);
ini_set('memory_limit', '512M');

// ส่ง buffer เพื่อป้องกัน browser timeout
ob_start();
echo str_pad('', 4096);
ob_flush();
flush();

$rebootMessage = '';
$serverStatus = '';
$commandOutput = '';

try {
    $ssh = ssh_connect(); // สมมติว่าฟังก์ชันนี้อยู่ใน ssh-inc.php
    $ssh->setTimeout(30); // ตั้ง timeout สำหรับ SSH
    $uptime = $ssh->exec('uptime');
    if ($uptime === false || $uptime === null) {
        throw new Exception('Failed to check server status.');
    }
    $serverStatus = 'Server is online. Uptime: ' . htmlspecialchars(trim($uptime));

    // ตรวจสอบว่ามีคำขอ POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $action = escapeshellcmd($_POST['action']); // ป้องกัน shell injection

        // Sanitize inputs
        $chain = isset($_POST['chain']) ? escapeshellarg(strtoupper($_POST['chain'])) : '';
        $rule_num = isset($_POST['rule_num']) ? (int)$_POST['rule_num'] : 0;
        $source_ip = isset($_POST['source_ip']) ? filter_var($_POST['source_ip'], FILTER_VALIDATE_IP) : '';
        $dest_ip = isset($_POST['dest_ip']) ? filter_var($_POST['dest_ip'], FILTER_VALIDATE_IP) : '';
        $port = isset($_POST['port']) ? (int)$_POST['port'] : 0;
        $protocol = isset($_POST['protocol']) ? escapeshellarg(strtolower($_POST['protocol'])) : '';
        $target = isset($_POST['target']) ? escapeshellarg(strtoupper($_POST['target'])) : '';

        switch ($action) {
            case 'status':
                $command = "sudo iptables -L -n -v --line-numbers";
                break;
            case 'start':
                $command = "sudo systemctl start iptables";
                break;
            case 'stop':
                $command = "sudo systemctl stop iptables";
                break;
            case 'restart':
                $command = "sudo systemctl restart iptables";
                break;
            case 'add_rule':
                if (empty($chain) || empty($target) || empty($protocol)) {
                    throw new Exception('Chain, target, and protocol are required for adding a rule.');
                }
                $command = "sudo iptables -A $chain";
                if ($source_ip) {
                    $command .= " -s $source_ip";
                }
                if ($dest_ip) {
                    $command .= " -d $dest_ip";
                }
                if ($port && in_array(strtolower(trim($protocol, "'")), ['tcp', 'udp'])) {
                    $command .= " -p $protocol --dport $port";
                }
                $command .= " -j $target";
                break;
            case 'delete_rule':
                if (empty($chain) || $rule_num <= 0) {
                    throw new Exception('Chain and rule number are required for deleting a rule.');
                }
                $command = "sudo iptables -D $chain $rule_num";
                break;
            case 'flush_chain':
                if (empty($chain)) {
                    throw new Exception('Chain is required for flushing.');
                }
                $command = "sudo iptables -F $chain";
                break;
            case 'save_rules':
                $command = "sudo iptables-save > /etc/iptables/rules.v4";
                break;
            case 'load_rules':
                $command = "sudo iptables-restore < /etc/iptables/rules.v4";
                break;
            default:
                throw new Exception('Invalid command.');
        }

        // รันคำสั่งผ่าน SSH
        $result = $ssh->exec($command);
        if ($result === false || $result === null) {
            $commandOutput = strpos($command, 'nohup') !== false
                ? "Command '$command' started in background. Check logs for details."
                : "Failed to execute command: $command";
        } else {
            $commandOutput = "Command: $command\nResult:\n" . htmlspecialchars(trim($result));
        }
    }
} catch (Exception $e) {
    $errorMessage = htmlspecialchars($e->getMessage());
    if (strpos($errorMessage, 'SSH login failed') !== false || strpos($errorMessage, 'Failed to check server status') !== false) {
        $serverStatus = 'Server is offline or unreachable: ' . $errorMessage;
    } else {
        $rebootMessage = 'Error: ' . $errorMessage;
    }
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Manager-Server
        <small>
       ระบบ จัดการ server
        </small>
    </h1>
</div>
<div class="row">
	<div class="col-xl-12">
		<div id="panel-1" class="panel">
			<div class="panel-hdr">	
				<h2>
                ระบบ จัดการ Server Linux <span class="fw-300"><i> แก้ไขคอนฟิกที่ ssh-inc.php</i></span> 
				</h2>
				<div class="panel-toolbar">
					<button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
					<button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
					<button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
				</div>
			</div>
			<div class="panel-container show">
				<div class="panel-content">	
    <div class="container mt-5">
        <h2>Server Management</h2>

        <!-- แสดงสถานะเซิร์ฟเวอร์ -->
        <div class="alert alert-<?php echo strpos($serverStatus, 'online') !== false ? 'success' : (strpos($serverStatus, 'rebooting') !== false ? 'warning' : 'danger'); ?>" role="alert">
            <?php echo $serverStatus; ?>
        </div>

        <!-- แสดงข้อความสถานะการรีบูตหรือข้อผิดพลาด -->
        <?php if (!empty($rebootMessage)): ?>
            <div class="alert alert-<?php echo strpos($rebootMessage, 'Error') === false ? 'success' : 'danger'; ?>" role="alert">
                <?php echo htmlspecialchars($rebootMessage); ?>
            </div>
        <?php endif; ?>

        <!-- แสดงผลลัพธ์คำสั่ง -->
        <?php if (!empty($commandOutput)): ?>
            <div class="alert alert-info" role="alert">
                <pre><?php echo $commandOutput; ?></pre>
            </div>
        <?php endif; ?>


        <!-- กลุ่มคำสั่ง System command iptables -->
        <div class="command-group">
            <h3>System iptables</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="status">status</option>
                    <option value="stop">stop</option>
                    <option value="restart">restart</option>
                    <option value="start">start</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

                            <!-- กลุ่มคำสั่ง System iptables -->
                            <div class="command-group">
                                <h3>System iptables</h3>
                                <form method="post" class="form">
                                    <div class="form-group">
                                        <label>Action</label>
                                        <select name="action" class="form-control">
                                            <option value="status">Show Status</option>
                                            <option value="start">Start iptables</option>
                                            <option value="stop">Stop iptables</option>
                                            <option value="restart">Restart iptables</option>
                                            <option value="add_rule">Add Rule</option>
                                            <option value="delete_rule">Delete Rule</option>
                                            <option value="flush_chain">Flush Chain</option>
                                            <option value="save_rules">Save Rules</option>
                                            <option value="load_rules">Load Rules</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Chain</label>
                                        <select name="chain" class="form-control">
                                            <option value="INPUT">INPUT</option>
                                            <option value="OUTPUT">OUTPUT</option>
                                            <option value="FORWARD">FORWARD</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Rule Number (for delete)</label>
                                        <input type="number" name="rule_num" class="form-control" placeholder="Enter rule number">
                                    </div>
                                    <div class="form-group">
                                        <label>Source IP (optional)</label>
                                        <input type="text" name="source_ip" class="form-control" placeholder="e.g., ***********">
                                    </div>
                                    <div class="form-group">
                                        <label>Destination IP (optional)</label>
                                        <input type="text" name="dest_ip" class="form-control" placeholder="e.g., ***********">
                                    </div>
                                    <div class="form-group">
                                        <label>Port (optional, for TCP/UDP)</label>
                                        <input type="number" name="port" class="form-control" placeholder="e.g., 80">
                                    </div>
                                    <div class="form-group">
                                        <label>Protocol</label>
                                        <select name="protocol" class="form-control">
                                            <option value="tcp">TCP</option>
                                            <option value="udp">UDP</option>
                                            <option value="icmp">ICMP</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Target</label>
                                        <select name="target" class="form-control">
                                            <option value="ACCEPT">ACCEPT</option>
                                            <option value="DROP">DROP</option>
                                            <option value="REJECT">REJECT</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Execute</button>
                                </form>
                            </div>


    </div>  
  </div>
  </div>
  </div>
  </div>
  <script></script>

