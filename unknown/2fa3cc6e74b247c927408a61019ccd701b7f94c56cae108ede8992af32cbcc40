<!-- event_data_edit_ui.php - UI สำหรับแก้ไขข้อมูล WEB_Event_Data -->
<div class="card mb-4">
  <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
    <span><i class="fas fa-database mr-2"></i>แก้ไขข้อมูล All Events Data</span>
    <button class="btn btn-success btn-sm" id="addEventBtn"><i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่</button>
  </div>
  <div class="card-body p-0">
    <div class="table-responsive mb-0">
      <table class="table table-sm table-hover mb-0" id="eventDataTable">
        <thead class="thead-light">
          <tr>
            <th>ID</th>
            <th>Event</th>
            <th style="width:140px;word-break:break-all;white-space:pre-line;">Items</th>
            <th style="width:140px;word-break:break-all;white-space:pre-line;">Account Perms</th>
            <th style="width:140px;word-break:break-all;white-space:pre-line;">Character Perms</th>
            <th>Status</th>
            <th>Date Created</th>
            <th>Expdate</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>
<!-- Modal: Edit Event Data -->
<div class="modal fade" id="eventDataModal" tabindex="-1" role="dialog" aria-labelledby="eventDataModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="eventDataModalLabel">แก้ไขข้อมูล All Events Data</h5>
        <button type="button" class="close" id="eventDataModalCloseBtn" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="eventDataForm">
      <div class="modal-body">
        <input type="hidden" id="eventId" name="id">
        <div class="form-group">
          <label for="eventName">Event</label>
          <input type="text" class="form-control" id="eventName" name="Event" required>
        </div>
        <div class="form-group">
          <label>Items <span class="text-danger">(สำคัญ)</span></label>
          <div id="itemsList"></div>
          <button type="button" class="btn btn-sm btn-success mt-2" id="addItemBtn"><i class="fas fa-plus"></i> เพิ่มไอเท็ม</button>
        </div>
        <div class="form-group">
          <label>Account Permissions <span class="text-info">(สิทธิ์ไอดี)</span></label>
          <div id="permsAccountList"></div>
          <button type="button" class="btn btn-sm btn-primary mt-2" id="addPermAccountBtn"><i class="fas fa-plus"></i> เพิ่มสิทธิ์ไอดี</button>
        </div>
        <div class="form-group">
          <label>Character Permissions <span class="text-info">(สิทธิ์ตัวละคร)</span></label>
          <div id="permsCharList"></div>
          <button type="button" class="btn btn-sm btn-primary mt-2" id="addPermCharBtn"><i class="fas fa-plus"></i> เพิ่มสิทธิ์ตัวละคร</button>
        </div>
        <div class="form-group">
          <label for="eventStatus">Status</label>
          <select class="form-control" id="eventStatus" name="status">
            <option value="1">เปิด</option>
            <option value="0">ปิด</option>
          </select>
        </div>
        <div class="form-group">
          <label for="eventDateCreated">Date Created</label>
          <input type="datetime-local" class="form-control" id="eventDateCreated" name="datecreated" required>
        </div>
        <div class="form-group">
          <label for="eventExpdate">Expdate</label>
          <input type="datetime-local" class="form-control" id="eventExpdate" name="Expdate" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
        <button type="submit" class="btn btn-primary">บันทึก</button>
      </div>
      </form>
    </div>
  </div>
</div>
<script>
function loadEventData() {
  $.get('_app/php/event_data_edit.php?action=list', function(data) {
    console.log('Raw data from backend:', data);
    let rows = '';
    let events = [];
    // ถ้า data เป็น array/object อยู่แล้ว ไม่ต้อง parse
    if (Array.isArray(data)) {
      events = data;
    } else {
      try { events = JSON.parse(data); } catch(e) { events = []; }
    }
    console.log('Loaded events:', events);
    events.forEach(function(ev) {
      // Fallback date display
      let datecreated = ev.datecreated && typeof ev.datecreated === 'string' ? ev.datecreated.replace('T', ' ').slice(0, 19) : '-';
      let expdate = ev.Expdate && typeof ev.Expdate === 'string' ? ev.Expdate.replace('T', ' ').slice(0, 19) : '-';
      rows += `<tr>
        <td>${ev.id}</td>
        <td>${ev.Event}</td>
        <td style="word-break:break-all;white-space:pre-line;">${(ev.items || '-').replace(/,/g,'\n')}</td>
        <td style="word-break:break-all;white-space:pre-line;">${(ev.permissions_account || '-').replace(/,/g,'\n')}</td>
        <td style="word-break:break-all;white-space:pre-line;">${(ev.permissions_character || '-').replace(/,/g,'\n')}</td>
        <td>${ev.status == 1 ? "<span class='badge badge-success'>เปิด</span>" : "<span class='badge badge-danger'>ปิด</span>"}</td>
        <td>${datecreated}</td>
        <td>${expdate}</td>
        <td><button class='btn btn-sm btn-info editEventBtn' data-id='${ev.id}'>แก้ไข</button></td>
      </tr>`;
    });
    $('#eventDataTable tbody').html(rows);
  });
}
$(function() {
  // ปุ่มเพิ่มกิจกรรมใหม่
  $('#addEventBtn').on('click', function() {
    $('#eventId').val('');
    $('#eventName').val('');
    $('#itemsList').empty();
    addItemInput('');
    $('#permsAccountList').empty();
    $('#permsCharList').empty();
    $('#eventStatus').val('1');
    $('#eventDateCreated').val('');
    $('#eventExpdate').val('');
    $('#eventDataModal').modal('show');
    $('#eventDataForm').data('mode', 'add');
  });
  // ปุ่ม close modal ด้วย JS (รองรับ bootstrap modal)
  $('#eventDataModalCloseBtn').on('click', function(){
    $('#eventDataModal').modal('hide');
  });
  loadEventData();
  // ฟังก์ชันเพิ่ม input ไอเท็ม 1 แถว
  function addItemInput(val) {
    // ตรวจสอบจำนวน input ปัจจุบัน (เช่น จำกัด 20 ชิ้น)
    let maxItems = 20;
    let current = $('#itemsList .item-row').length;
    if(current >= maxItems) {
      Swal.fire({ icon: 'warning', title: 'จำกัดจำนวน', text: 'เพิ่มไอเท็มได้สูงสุด '+maxItems+' ชิ้น' });
      return;
    }
    let html = `<div class="input-group mb-1 item-row">
      <input type="text" class="form-control" name="item[]" value="${val||''}" required>
      <div class="input-group-append">
        <button type="button" class="btn btn-danger btn-remove-item"><i class="fas fa-trash"></i></button>
      </div>
    </div>`;
    $('#itemsList').append(html);
  }

  // ฟังก์ชันเพิ่ม input permission 1 แถว
function addPermInput(val) {
  let html = `<div class="input-group mb-1 perm-row">
    <input type="text" class="form-control" value="${val||''}" placeholder="เช่น PlayTime:500, Level:200" required>
    <div class="input-group-append">
      <button type="button" class="btn btn-danger btn-remove-perm"><i class="fas fa-trash"></i></button>
    </div>
  </div>`;
  return html;
}

  // เพิ่ม input เมื่อกด +
  $('#addItemBtn').on('click', function(){
    addItemInput('');
  });

  // เพิ่ม permission เมื่อกด +
// เพิ่ม permission ไอดี
$('#addPermAccountBtn').on('click', function(){
  let maxPerms = 10;
  let current = $('#permsAccountList .perm-row').length;
  if(current >= maxPerms) {
    Swal.fire({ icon: 'warning', title: 'จำกัดจำนวน', text: 'เพิ่มสิทธิ์ไอดีได้สูงสุด '+maxPerms+' รายการ' });
    return;
  }
  $('#permsAccountList').append(addPermInput(''));
});
// เพิ่ม permission ตัวละคร
$('#addPermCharBtn').on('click', function(){
  let maxPerms = 10;
  let current = $('#permsCharList .perm-row').length;
  if(current >= maxPerms) {
    Swal.fire({ icon: 'warning', title: 'จำกัดจำนวน', text: 'เพิ่มสิทธิ์ตัวละครได้สูงสุด '+maxPerms+' รายการ' });
    return;
  }
  $('#permsCharList').append(addPermInput(''));
});

  // ลบ permission
  $(document).on('click', '.btn-remove-perm', function(){
  $(this).closest('.perm-row').remove();
  });

  // ลบ input
  $(document).on('click', '.btn-remove-item', function(){
    $(this).closest('.item-row').remove();
  });

  $(document).on('click', '.editEventBtn', function() {
    let id = $(this).data('id');
    $.get('_app/php/event_data_edit.php?action=get&id=' + id, function(data) {
      let ev;
      if (typeof data === 'object') {
        ev = data;
      } else {
        try { ev = JSON.parse(data); } catch(e) { ev = {}; }
      }
      $('#eventId').val(ev.id);
      $('#eventName').val(ev.Event);
      // สร้าง input ไอเท็มใหม่
      $('#itemsList').empty();
      let itemsArr = (ev.items || '').split(',').filter(x=>x.trim()!=='');
      if(itemsArr.length === 0) itemsArr = [''];
      itemsArr.forEach(function(val) { addItemInput(val); });
      // สร้าง input permission ไอดีใหม่
      $('#permsAccountList').empty();
      let permsAccArr = (ev.permissions_account || '').split(',').filter(x=>x.trim()!=='');
      if(permsAccArr.length === 0) permsAccArr = [''];
      permsAccArr.forEach(function(val) { $('#permsAccountList').append(addPermInput(val)); });
      // สร้าง input permission ตัวละครใหม่
      $('#permsCharList').empty();
      let permsCharArr = (ev.permissions_character || '').split(',').filter(x=>x.trim()!=='');
      if(permsCharArr.length === 0) permsCharArr = [''];
      permsCharArr.forEach(function(val) { $('#permsCharList').append(addPermInput(val)); });
      $('#eventStatus').val(ev.status);
      // แปลง datecreated/Expdate เป็น datetime-local
      $('#eventDateCreated').val(ev.datecreated ? ev.datecreated.replace(' ', 'T').slice(0,16) : '');
      $('#eventExpdate').val(ev.Expdate ? ev.Expdate.replace(' ', 'T').slice(0,16) : '');
      $('#eventDataModal').modal('show');
    });
  });
  $('#eventDataForm').submit(function(e) {
    e.preventDefault();
    // รวมค่าทุก input item
    let items = [];
    $('#itemsList input[name="item[]"]').each(function(){
      let v = $(this).val().trim();
      if(v) items.push(v);
    });
  // รวมค่าทุก input permission ไอดี
  let permsAcc = [];
  $('#permsAccountList .perm-row input').each(function(){
    let v = $(this).val().trim();
    if(v) permsAcc.push(v);
  });
  // รวมค่าทุก input permission ตัวละคร
  let permsChar = [];
  $('#permsCharList .perm-row input').each(function(){
    let v = $(this).val().trim();
    if(v) permsChar.push(v);
  });
    let isAdd = $('#eventDataForm').data('mode') === 'add';
    let data = {
      id: $('#eventId').val(),
      Event: $('#eventName').val(),
    items: items.join(','),
    permissions_account: permsAcc.join(','),
    permissions_character: permsChar.join(','),
      status: $('#eventStatus').val(),
      datecreated: $('#eventDateCreated').val().replace('T', ' ')+':00',
      Expdate: $('#eventExpdate').val().replace('T', ' ')+':00'
    };
    let url = isAdd ? '_app/php/event_data_edit.php?action=add' : '_app/php/event_data_edit.php?action=edit';
    $.ajax({
      url: url,
      method: 'POST',
      contentType: 'application/json',
      data: JSON.stringify(data),
      success: function(res) {
        $('#eventDataModal').modal('hide');
        loadEventData();
        Swal.fire({ icon: 'success', title: isAdd ? 'เพิ่มกิจกรรมสำเร็จ' : 'บันทึกสำเร็จ', text: isAdd ? 'เพิ่มกิจกรรมใหม่เรียบร้อยแล้ว' : 'แก้ไขข้อมูลเรียบร้อยแล้ว' });
      },
      error: function() {
        Swal.fire({ icon: 'error', title: 'เกิดข้อผิดพลาด', text: 'ไม่สามารถบันทึกข้อมูลได้' });
      }
    });
  });
});
</script>
