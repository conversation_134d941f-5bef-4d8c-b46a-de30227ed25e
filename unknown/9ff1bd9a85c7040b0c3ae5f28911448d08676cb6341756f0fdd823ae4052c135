<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> My inventory
        <small> ระบบตรวจสอบไอเท็ม ในช่องเก็บของตัวละคร</small>
    </h1>
</div>
<?php
            $loadForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
            $usernumidx = strip_tags(trim($_POST['usernum']));

            if(empty($usernumidx)){
                $returnError = 'Oops, โปรดใส่เลขไอดี';
            }else{
            // select char by url id
            if (isset($loadForm) && isset($loadForm['loaddata'])) {

                $loadItemsPlayer = "EXECUTE [".DATABASE_SV."].[dbo].SPLIT_WAREHOUSE_ID ?";
                $loadItemsPlayerParams = array($usernumidx);
                $loadItemsPlayerQuery = sqlsrv_query($conn, $loadItemsPlayer, $loadItemsPlayerParams);
                    if ($loadItemsPlayerQuery) :
                        $returnSuccess = 'Char '.$usernumidx.' โหลดข้อมูลสำเร็จ!';
                    else:
                        $returnError = 'Oops, occurs an error';
                    endif;
                }else {
                    $returnWarning = "failed!";
                }
            }
            ?>
        
            <?php if (isset($returnSuccess)) { ?>
                <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
            <?php } elseif (isset($returnWarning)) { ?>
                <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
            <?php } elseif (isset($returnError)) { ?>
                <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
            <?php } elseif (isset($returnWarningGLOBAL)) { ?>
                <div class="alert alert-warning"><?php echo $returnWarningGLOBAL; ?></div>
            <?php } ?>
        
            <div class="alert bg-fusion-400 border-0 fade show">
                <div class="d-flex align-items-center">
                    <div class="alert-icon">
                        <i class="fal fa-shield-check text-warning"></i>
                    </div>
                    <div class="flex-1">
                        <span class="h5">โหลดไอเท็ม</span>
                        <br>
                        ผู้ดูแลระบบสามารถโหลดไอเท็มในตัวละครมาดูได้ว่าใครมีไอเท็มอะไรบ้าง
                    </div>
                    <div class="row mb-12">
                        <div class="col-sm-12 col-md-12 d-flex align-items-center justify-content-start">
                        <form action="" method="post">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text text-warning">
                                    <i class="ni ni-user fs-xl"></i>
                                </span>
                            </div>
                            <input type="text" name="usernum" class="form-control" placeholder="เลขไอดี">
                            <div class="input-group-append">
                                <input class="tn btn-warning shadow-0 waves-effect waves-themed" name="loaddata" type="submit" value="โหลดข้อมูล"/>
                            </div>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>USER SPLIT ITEM<span class="fw-300"><i>ข้อมูลโหลดไอเท็มในตัวละคร</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-bordered table-hover table-striped w-100">
                        <thead>
                            <tr>
                                <th>USERNUM</th>
                                <th>ID</th>
                                <th>KINDIDX</th>
                                <th>SERIALS</th>
                                <th>ITEMOPT</th>
                                <th>SLOTIDX</th>
                                <th>EXPIREDATE</th>
                                <th>DURATIONIDX</th>
                                <th>EXPIRE</th>
                                <th>split_ORDER</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $selectLastUsers      = "SELECT  * FROM [" . DATABASE_SV . "].[dbo].SPLIT_WAREHOUSE_TABLE_ID";
                            $selectLastUsersParam = array();
                            $selectLastUsersOpt   = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                            if (sqlsrv_num_rows($selectLastUsersQuery)) {
                            while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                            $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '". $resLastUsers['USERNUM'] ."'";
                            $selectauthParam = array();
                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                            $name = $userLogin->thaitrans($selectauthFetch['Name']);

                            ?>
                            <tr>
                                <td><?php echo $resLastUsers['USERNUM']; ?></td>
                                <td><?php echo $selectauthFetch['ID']; ?></td>
                                <td><?php echo $resLastUsers['KINDIDX']; ?></td>
                                <td><?php echo $resLastUsers['SERIALS']; ?></td>
                                <td><?php echo $resLastUsers['ITEMOPT']; ?></td>
                                <td><?php echo $resLastUsers['SLOTIDX']; ?></td>
                                <td><?php echo $resLastUsers['EXPIREDATE']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($resLastUsers['DURATIONIDX'])); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($resLastUsers['EXPIRE'])); ?></td>
                                <td><?php echo $resLastUsers['split_ORDER']; ?></td>
                            </tr>
                            <?php
                        }
                        } else {
                        echo W_NOTHING_RETURNED;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>


</div>