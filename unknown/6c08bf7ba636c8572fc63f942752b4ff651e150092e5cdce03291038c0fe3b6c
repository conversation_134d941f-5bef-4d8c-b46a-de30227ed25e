<?php
// ระบบใช้ Character Redeem Code
require_once '../_app/dbinfo.inc.php';

if (isset($_POST['redeemCharacterCode'])) {
    $inputCode = trim($_POST['inputCode']);
    $getID = $_SESSION['customer_id'] ?? 0;
    
    if (empty($inputCode)) {
        $returnError = "กรุณากรอก Redeem Code";
    } else {
        // ตรวจสอบ Code ในฐานข้อมูล
        $selectCode = "SELECT * FROM WEB_Redeem_Code_UserLogin WHERE code = ? AND status = '0'";
        $selectParam = array($inputCode);
        $selectQuery = sqlsrv_query($conn, $selectCode, $selectParam);
        
        if (sqlsrv_num_rows($selectQuery)) {
            $codeData = sqlsrv_fetch_array($selectQuery, SQLSRV_FETCH_ASSOC);
            
            // ตรวจสอบวันหมดอายุ
            if ($codeData['expiry_date']) {
                $now = new DateTime();
                $expiry = $codeData['expiry_date'];
                if ($now > $expiry) {
                    $returnError = "Redeem Code หมดอายุแล้ว";
                    exit;
                }
            }
            
            // ดึงข้อมูลตัวละครจาก Server01
            $characterSql = "SELECT * FROM Server01.dbo.cabal_character_table WHERE customer_id = ?";
            $characterStmt = sqlsrv_query($conn, $characterSql, array($getID));
            
            if (sqlsrv_num_rows($characterStmt)) {
                $characterData = sqlsrv_fetch_array($characterStmt, SQLSRV_FETCH_ASSOC);
                
                // ตรวจสอบเงื่อนไขตัวละคร
                $canRedeem = false;
                
                // ตรวจสอบสถานะ Online/Offline
                if ($codeData['character_status'] === 'all') {
                    $canRedeem = true;
                } elseif ($codeData['character_status'] === '1' && $characterData['login_flag'] == 1) {
                    $canRedeem = true; // ต้อง Online
                } elseif ($codeData['character_status'] === '0' && $characterData['login_flag'] == 0) {
                    $canRedeem = true; // ต้อง Offline
                }
                
                // ตรวจสอบ Channel
                if ($canRedeem && $codeData['channel_idx']) {
                    if ($characterData['channel_idx'] != $codeData['channel_idx']) {
                        $canRedeem = false;
                        $returnError = "ตัวละครต้องอยู่ใน Channel {$codeData['channel_idx']}";
                    }
                }
                
                if ($canRedeem) {
                    // แจกไอเท็ม
                    $explodeItems = explode(',', $codeData['items']);
                    $allSuccess = true;
                    
                    foreach ($explodeItems as $item) {
                        $item = trim($item);
                        $redeemCode = "EXECUTE [". DATABASE_NAME ."].[dbo].FN_AddFullItemToUser ?, ?, ?, -1, -1, 10000";
                        $redeemCodeParam = array($getID, $item, $codeData['quantity']);
                        $redeemCodeQuery = sqlsrv_query($conn, $redeemCode, $redeemCodeParam);
                        
                        if (!$redeemCodeQuery) {
                            $allSuccess = false;
                            break;
                        }
                    }
                    
                    if ($allSuccess) {
                        // อัปเดตสถานะ Code
                        $updateCode = "UPDATE WEB_Redeem_Code_UserLogin SET status = '1' WHERE id = ?";
                        $updateCodeQuery = sqlsrv_query($conn, $updateCode, array($codeData['id']));
                        
                        // บันทึกประวัติการใช้
                        $insertUsed = "INSERT INTO WEB_Redeem_CodesUsed_Character (CodeID, CustomerID, code, character_id, channel_idx, used_date) VALUES (?, ?, ?, ?, ?, GETDATE())";
                        $insertUsedQuery = sqlsrv_query($conn, $insertUsed, array(
                            $codeData['id'], 
                            $getID, 
                            $inputCode,
                            $characterData['character_id'],
                            $characterData['channel_idx']
                        ));
                        
                        $returnSuccess = "ใช้ Character Redeem Code สำเร็จ!";
                    } else {
                        $returnError = "เกิดข้อผิดพลาดในการแจกไอเท็ม";
                    }
                } else {
                    if ($codeData['character_status'] === '1') {
                        $returnError = "ตัวละครต้องอยู่ในสถานะ Online";
                    } elseif ($codeData['character_status'] === '0') {
                        $returnError = "ตัวละครต้องอยู่ในสถานะ Offline";
                    }
                }
            } else {
                $returnError = "ไม่พบข้อมูลตัวละคร";
            }
        } else {
            $returnError = "Redeem Code ไม่ถูกต้องหรือถูกใช้แล้ว";
        }
    }
}
?>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5><i class="fas fa-user-tag"></i> ใช้ Character Redeem Code</h5>
    </div>
    <div class="card-body">
        <?php if (isset($returnSuccess)): ?>
            <div class="alert alert-success"><?= $returnSuccess ?></div>
        <?php endif; ?>
        
        <?php if (isset($returnError)): ?>
            <div class="alert alert-danger"><?= $returnError ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="inputCode">Character Redeem Code:</label>
                <input type="text" id="inputCode" name="inputCode" class="form-control" 
                       placeholder="กรอก Character Redeem Code" required>
                <small class="text-muted">รูปแบบ: XXX-XXX-XXXX-XXX</small>
            </div>
            
            <button type="submit" name="redeemCharacterCode" class="btn btn-primary">
                <i class="fas fa-gift"></i> ใช้ Character Redeem Code
            </button>
        </form>
        
        <hr>
        
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> เงื่อนไขการใช้งาน:</h6>
            <ul class="mb-0">
                <li>ตัวละครต้องอยู่ในสถานะที่กำหนด (Online/Offline)</li>
                <li>ตัวละครต้องอยู่ใน Channel ที่กำหนด (ถ้ามี)</li>
                <li>Code ที่หมดอายุจะใช้ไม่ได้</li>
                <li>แต่ละ Code ใช้ได้เพียงครั้งเดียว</li>
            </ul>
        </div>
    </div>
</div>