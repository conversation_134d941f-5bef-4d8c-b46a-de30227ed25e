/**
 * DataTables Global Configuration
 * แก้ไขปัญหา CORS และตั้งค่าภาษาไทยแบบ global
 */

$(document).ready(function() {
    // ตั้งค่า default สำหรับ DataTables ทั้งหมด
    if ($.fn.DataTable) {
        // ตั้งค่าภาษาไทยเป็น default
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                // ใช้ไฟล์ภาษาไทยในเครื่อง แทน CDN
                url: 'assets/js/datatables-thai.json',
                
                // Fallback text กรณีไฟล์โหลดไม่ได้
                decimal: "",
                emptyTable: "ไม่มีข้อมูลในตาราง",
                info: "แสดง _START_ ถึง _END_ จาก _TOTAL_ รายการ",
                infoEmpty: "แสดง 0 ถึง 0 จาก 0 รายการ",
                infoFiltered: "(กรองจาก _MAX_ รายการทั้งหมด)",
                infoPostFix: "",
                thousands: ",",
                lengthMenu: "แสดง _MENU_ รายการ",
                loadingRecords: "กำลังโหลด...",
                processing: "กำลังประมวลผล...",
                search: "ค้นหา:",
                zeroRecords: "ไม่พบข้อมูลที่ตรงกัน",
                paginate: {
                    first: "หน้าแรก",
                    last: "หน้าสุดท้าย",
                    next: "ถัดไป",
                    previous: "ก่อนหน้า"
                },
                aria: {
                    sortAscending: ": เปิดใช้งานการเรียงลำดับจากน้อยไปมาก",
                    sortDescending: ": เปิดใช้งานการเรียงลำดับจากมากไปน้อย"
                }
            },
            
            // ตั้งค่าอื่นๆ
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "ทั้งหมด"]],
            processing: true,
            
            // ตั้งค่า DOM structure
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            
            // Error handling
            error: function(xhr, error, code) {
                console.error('DataTables Error:', error, code);
                if (xhr.status === 0) {
                    console.warn('CORS Error detected. Using fallback language settings.');
                }
            }
        });
        
        console.log('DataTables Thai configuration loaded successfully');
    }
});

/**
 * ฟังก์ชันสำหรับสร้าง DataTable พร้อมการตั้งค่าภาษาไทย
 * @param {string} selector - jQuery selector
 * @param {object} options - DataTable options
 * @returns {object} DataTable instance
 */
function createThaiDataTable(selector, options = {}) {
    const defaultOptions = {
        language: {
            url: 'assets/js/datatables-thai.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "ทั้งหมด"]],
        processing: true
    };
    
    // รวม options
    const finalOptions = $.extend(true, {}, defaultOptions, options);
    
    // สร้าง DataTable
    return $(selector).DataTable(finalOptions);
}

/**
 * ฟังก์ชันสำหรับแก้ไข CORS error แบบ fallback
 */
function fixDataTablesCORS() {
    // ตรวจสอบว่ามี DataTable ที่ใช้ CDN หรือไม่
    $('table[data-language-url*="cdn.datatables.net"]').each(function() {
        const $table = $(this);
        const currentUrl = $table.data('language-url');
        
        if (currentUrl && currentUrl.includes('cdn.datatables.net')) {
            console.warn('CORS URL detected:', currentUrl);
            
            // แทนที่ด้วย local file
            $table.data('language-url', 'assets/js/datatables-thai.json');
            
            // ถ้าตารางถูก initialize แล้ว ให้ destroy และสร้างใหม่
            if ($.fn.DataTable.isDataTable($table)) {
                const dt = $table.DataTable();
                const settings = dt.settings()[0];
                
                dt.destroy();
                
                // สร้างใหม่ด้วย local language file
                $table.DataTable($.extend(true, {}, settings.oInit, {
                    language: {
                        url: 'assets/js/datatables-thai.json'
                    }
                }));
            }
        }
    });
}

/**
 * Auto-fix CORS issues เมื่อ document ready
 */
$(document).ready(function() {
    // รอให้ DataTables โหลดเสร็จก่อน
    setTimeout(function() {
        fixDataTablesCORS();
    }, 1000);
});

/**
 * ฟังก์ชันสำหรับตรวจสอบและแก้ไข CORS error ใน console
 */
function checkDataTablesCORS() {
    const corsErrors = [];
    
    // ตรวจสอบ DataTables ทั้งหมด
    $('.dataTable, [id*="dt-"], [class*="datatable"]').each(function() {
        const $table = $(this);
        
        if ($.fn.DataTable.isDataTable($table)) {
            const dt = $table.DataTable();
            const settings = dt.settings()[0];
            
            if (settings.oLanguage && settings.oLanguage.sUrl) {
                const langUrl = settings.oLanguage.sUrl;
                
                if (langUrl.includes('cdn.datatables.net')) {
                    corsErrors.push({
                        table: $table.attr('id') || $table.attr('class'),
                        url: langUrl
                    });
                }
            }
        }
    });
    
    if (corsErrors.length > 0) {
        console.warn('Found DataTables with potential CORS issues:', corsErrors);
        return corsErrors;
    } else {
        console.log('No CORS issues found in DataTables');
        return [];
    }
}

/**
 * ฟังก์ชันสำหรับ debug DataTables
 */
function debugDataTables() {
    console.log('=== DataTables Debug Info ===');
    
    // นับจำนวน DataTables
    const tables = $('.dataTable, [id*="dt-"], [class*="datatable"]');
    console.log('Total tables found:', tables.length);
    
    // ตรวจสอบแต่ละตาราง
    tables.each(function(index) {
        const $table = $(this);
        const id = $table.attr('id') || 'table-' + index;
        
        console.log(`Table ${index + 1} (${id}):`);
        console.log('- Is DataTable:', $.fn.DataTable.isDataTable($table));
        
        if ($.fn.DataTable.isDataTable($table)) {
            const dt = $table.DataTable();
            const settings = dt.settings()[0];
            
            console.log('- Language URL:', settings.oLanguage.sUrl || 'Not set');
            console.log('- Responsive:', settings.responsive || false);
            console.log('- Processing:', settings.oFeatures.bProcessing || false);
        }
    });
    
    // ตรวจสอบ CORS
    checkDataTablesCORS();
    
    console.log('=== End Debug Info ===');
}

// Export functions สำหรับใช้ใน console
window.createThaiDataTable = createThaiDataTable;
window.fixDataTablesCORS = fixDataTablesCORS;
window.checkDataTablesCORS = checkDataTablesCORS;
window.debugDataTables = debugDataTables;
