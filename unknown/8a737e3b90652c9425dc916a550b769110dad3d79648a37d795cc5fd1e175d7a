<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get server log by ID
                $selectCategory = "SELECT * FROM  ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                $selectCategoryParam = array();
                $selectCategoryOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectCategoryQuery = sqlsrv_query($conn, $selectCategory, $selectCategoryParam, $selectCategoryOpt);
                $selectCategoryRows = sqlsrv_num_rows($selectCategoryQuery);

                // condition
                if (!$selectCategoryRows) {
                    $returnWarning = W_PLAYER_NOT_FOUND;
                } else {
                    // get data info
                    $selectPlayerData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                    $selectPlayerDataParam = array();
                    $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                    $selectPlayerDataFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                    $resPlayer = sqlsrv_fetch_array($selectCategoryQuery, SQLSRV_FETCH_ASSOC);

                    if (isset($_POST['btn-savechange'])) {
                        // variables
                        $cat_name = strip_tags(trim($_POST['plr-email']));
                        $cat_status = strip_tags(trim($_POST['plr-pass']));
                        $plr_isdev = strip_tags(trim($_POST['plr-isdev']));

                        // condition
                        if (empty($cat_name)) {
                            $returnWarning = W_EMPTY_EMAIL;
                        } else if (!filter_var($cat_name, FILTER_VALIDATE_EMAIL)) {
                            $returnWarning = W_WRONG_EMAIL;
                        } else {

							$UserID = $resPlayer['ID'];
                            $finalPass = $resPlayer['Password'];
                            // set IsDev if value is -1
                            if ($plr_isdev == '-1') {
                                $plr_isdev = $resPlayer['IsDeveloper'];
                            }
                            // update
							$NewPwd = $cat_status;
							//$NewPwd = substr(md5(mktime()),0,8);
							//$NewPwd = PWDENCRYPT('$NewPwd');
		
                            $updatePlayer = "UPDATE ".DATABASE_ACC.".dbo.cabal_auth_table SET Email = '$cat_name', Password = PWDENCRYPT('$NewPwd'), IsDeveloper = '$plr_isdev' WHERE UserNum = '$getCategoryID'";
                            $updatePlayerParam = array();
                            $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer, $updatePlayerParam);

                            if (sqlsrv_rows_affected($updatePlayerQuery)) {

                                // generate a log
                                $zpanel->generateWebLog($conn, '2', $UserID, 'update player account', 'player account was successfully updated!');

                                if ($plr_isdev != '-1') {
                                    // update isDev UsersData table
                                    $updatePlayerData = "UPDATE ".DATABASE_ACC.".dbo.cabal_auth_table SET IsDeveloper = '$plr_isdev' WHERE UserNum = '$getCategoryID'";
                                    $updatePlayerDataParam = array();
                                    $updatePlayerDataQuery = sqlsrv_query($conn, $updatePlayerData, $updatePlayerDataParam);
                                }
                                header('Location: ?url=manager/players&update=true');
                            } else {
                                $returnError = E_PLR_UPDATE_ACC;
                            }
                        }
                    }
				}
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
                <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                    <h2 class="text-red"><?php echo T_ACCOUNTINFO; ?> <small>UserName: <?php echo $resPlayer['ID']; ?></small></h2>
                    <hr>
                    <div class="col-lg-12">
                        <div class="form-group">
                            <h4 class="text-red"><?php echo T_EMAIL; ?></h4>
                            <input class="form-control" type="email" name="plr-email" value="<?php echo $resPlayer['Email']; ?>">
                        </div>

                        <div class="form-group">
                            <h4 class="text-red"><?php echo T_PASSWORD; ?></h4>
                            <input class="form-control" type="password" name="plr-pass" placeholder="New password ...">
                        </div>

                        <div class="form-group">
                            <h4 class="text-red"><?php echo T_ISDEV; ?></h4>
                            <select name="plr-isdev" class="form-control">
                                <option value="-1">สถานะไอดี : <?php echo $isDev = ($resPlayer['IsDeveloper'] ? 'DEV' : 'Player'); ?></option>
                                <option value="1">DEV</option>
                                <option value="0">Player</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <input type="submit" name="btn-savechange" class="btn btn-success btn-block" value="<?php echo B_SAVECHANGES; ?>">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>