<div class="page-header"><h1>ระบบเพิ่มยศชื้อขาย</h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getCharID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                $getCharForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                
                // account
                //$selectAccountChar = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx = '$getCharID'";
				$selectAccountChar = "EXECUTE WEB_tool_title '$getCharID'";
                $selectAccountCharQuery = sqlsrv_query($conn, $selectAccountChar, array());
                $selectAccountCharFetch = sqlsrv_fetch_array($selectAccountCharQuery);

                $selectUsersDataChar = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx = '$getCharID'";
                $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersDataChar, array());
                $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
				if($selectUsersDataFetch['Login'] ==1){
				   $returnError = "ผู้เล่นนี้ยังไม่ออกเกมส์!";
				}else{

					if($selectAccountCharQuery){
						$selectaddTitle= "SELECT * FROM WEB_Addtitle_log WHERE CharacterIdx = '$getCharID'";
						$selectaddTitleQuery = sqlsrv_query($conn, $selectaddTitle, array());
						$selectaddTitleFetch = sqlsrv_fetch_array($selectaddTitleQuery, SQLSRV_FETCH_ASSOC);
				    }
                        if(isset($getCharForm['btn-savechange'])) {
					        if($selectaddTitleFetch['CharacterIdx'] == ""){
						 $returnError = "ตัวละครนี้ทำยศไปแล้ว ทำช่ำไม่ได้";
					    }else{

                            $databin = bin2hex($selectAccountCharFetch['TitleData']);
                                if($selectaddTitle){
                                    $updateCharPlayerLog = "INSERT INTO WEB_Addtitle_log (CharacterIdx,Status,Data) VALUES ('".$getCharID."','1','".$databin."')";
                                    $updateCharPlayerLogQuery = sqlsrv_query($conn, $updateCharPlayerLog, array());
                                    $updateCharPlayerLogFetch = sqlsrv_fetch_array($updateCharPlayerLogQuery);
                                            if($updateCharPlayerLogQuery){
                                                $result = @sqlsrv_query($conn,"update  [".DATABASE_SV."].[dbo].cabal_achievement_title set TitleData=TitleData-0x282B00000000  where CharacterIdx='".$getCharID."'");
                                                if($result){
                                                    $returnSuccess = "อัพเดดยศให้เรียบร้อยแล้ว!";
                                                }else{
                                                $returnError = "Oops occurs an error-1!";
                                                }
                                    
                                            }else{
                                            $returnError = "Oops occurs an error-2!";
                                            }
                                }else{
                                $returnError = "Oops occurs an error-3!"; 
                                }
                        }   	
                    }		
                }			
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
                <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                    <h2 class="text-red">ตัวละคร<small>&middot; ชื่อตัวละคร: <?php echo $getCharID; ?></small></h2>
                    <hr>
                    <div class="col-lg-12">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <input type="submit" name="btn-savechange" class="btn btn-success btn-block" value="<?php echo B_SAVECHANGES; ?>">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>