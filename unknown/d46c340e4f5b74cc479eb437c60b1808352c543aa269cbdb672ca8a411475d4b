<?php
declare(strict_types=1);

// เพิ่มเวลาการทำงานและหน่วยความจำ
set_time_limit(600);
ini_set('memory_limit', '512M');

// ส่ง buffer เพื่อป้องกัน browser timeout
ob_start();
echo str_pad('', 4096);
ob_flush();
flush();

$rebootMessage = '';
$configContent = '';
$serverStatus = '';
$commandOutput = '';

try {
    $ssh = ssh_connect();
    $ssh->setTimeout(30); // ตั้ง timeout สำหรับ SSH
    $uptime = $ssh->exec('uptime');
    if ($uptime === false || $uptime === null) {
        throw new Exception('Failed to check server status.');
    }
    $serverStatus = 'Server is online. Uptime: ' . htmlspecialchars(trim($uptime));

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $action = escapeshellarg($_POST['action']); // Sanitize action
        $server_pointer = escapeshellarg($_POST['server_pointer'] ?? 'main');
        $channel_number = escapeshellarg($_POST['channel_number'] ?? '');
        $ip_address = escapeshellarg($_POST['ip_address'] ?? '');
        $sql_user = escapeshellarg($_POST['sql_user'] ?? 'SA');
        $sql_password = escapeshellarg($_POST['sql_password'] ?? '');

        switch ($_POST['action']) { // ใช้ $_POST['action'] เพื่อหลีกเลี่ยงการเปลี่ยนแปลง $action
            case in_array($_POST['action'], [
                'cabal_start', 'cabal_stop', 'cabal_restart', 'cabal_reload', 'cabal_status',
                        'channels_start', 'channels_stop', 'channels_restart', 'channels_reload', 'channels_status',
                        'war_start', 'war_stop', 'war_restart', 'war_reload', 'war_status',
                        'tech_start', 'tech_stop', 'tech_restart', 'tech_reload', 'tech_status',
                        'login_start', 'login_stop', 'login_restart', 'login_reload', 'login_status',
                        'eventmgr_start', 'eventmgr_stop', 'eventmgr_restart', 'eventmgr_reload', 'eventmgr_status',
                        'ashop_start', 'ashop_stop', 'ashop_restart', 'ashop_reload', 'ashop_status'
            ]):
                $command = "nohup $action $server_pointer > /tmp/$action.log 2>&1 &";
                break;
            case in_array($_POST['action'], ['create_backup', 'backup_dbs', 'create_dbs', 'restore_dbs', 'shrink_dbs']):
                $logFile = "/tmp/{$_POST['action']}.log";
                $ssh->exec("rm -f $logFile; touch $logFile && chmod 664 $logFile");
                $command = "nohup $action > $logFile 2>&1 &";
                break;
            case 'ghapi':
                $logFile = "/tmp/ghapi.log";
                $pidFile = "/tmp/ghapi.pid";
                $ssh->exec("rm -f $logFile; touch $logFile && chmod 664 $logFile");
                $command = "nohup python3 /opt/ghapi-python/app.py > $logFile 2>&1 & echo $! > $pidFile";
                break;
            case 'chan':
                if (empty($_POST['channel_number'])) {
                    throw new Exception('Channel number is required for chan command.');
                }
                $chan_action = escapeshellarg($_POST['chan_action'] ?? 'status');
                $command = "chan $channel_number $server_pointer $chan_action";
                break;
            case 'autobackup':
                $backup_state = escapeshellarg($_POST['backup_state'] ?? 'on');
                $command = "autobackup $server_pointer $backup_state";
                break;
            case 'cabal_db':
                $db_action = escapeshellarg($_POST['db_action'] ?? 'status');
                $server_name = escapeshellarg($_POST['server_name'] ?? 'Server01');
                $command = "cabal_db $db_action $server_pointer $server_name";
                break;
            case 'set_war_default':
            case 'set_war_flag':
                $command = $action;
                break;
            case 'server_create':
            case 'server_remove':
                $command = $action;
                break;
            case 'gms_proxy_reexec':
                $command = $action;
                break;
            case 'world_set_ip':
                if (empty($_POST['ip_address'])) {
                    throw new Exception('IP address is required for world_set_ip.');
                }
                $command = "world_set_ip $ip_address";
                break;
            case 'set_sql_access':
                $command = "set_sql_access $server_pointer $sql_user $sql_password";
                break;
            case 'ChangIP':
                $command = "cd /home/<USER>/cabal_scripts && ./changIPServer.sh";
                break;
            case 'ChangUserPassworld':
                $command = "cd /home/<USER>/cabal_scripts && ./ChangSqlUserPass.sh";
                break;
            case 'docker':
                $docker_action = escapeshellarg($_POST['docker_action'] ?? 'status');
                $container = escapeshellarg($_POST['container'] ?? 'cabal_main');
                $command = "docker $docker_action $container";
                break;
            case 'docker_ps':
                $command = "docker ps -a";
                break;
            case 'docker_stats':
                $command = "docker stats --no-stream";
                break;
            case 'htop':
                $command = "top -bn1 | head -n 10";
                break;
            case 'mc':
                $command = "ls -l";
                break;
            case 'df':
                $command = "df -h";
                break;
            case 'tcpdump':
                $command = "tcpdump -nni eth0 icmp -c 5";
                break;
            case 'docker_ps_s':
                $command = "docker ps -s";
                break;
            case 'reboot':
                $command = 'reboot';
                break;
            default:
                throw new Exception('Invalid command.');
        }

        $result = $ssh->exec($command);
        if ($result === false || $result === null) {
            $commandOutput = strpos($command, 'nohup') !== false
                ? "Command '$command' started in background. Check /tmp/{$_POST['action']}.log for details."
                : "Failed to execute command: $command";
            if (strpos($command, 'nohup') !== false) {
                $logCheck = $ssh->exec("head -n 5 /tmp/{$_POST['action']}.log");
                $commandOutput .= "\nInitial log output:\n" . htmlspecialchars(trim($logCheck));
            }
        } else {
            $commandOutput = "Command: $command\nResult: " . htmlspecialchars(trim($result));
        }

        if ($_POST['action'] === 'reboot') {
            $rebootMessage = 'Reboot command sent. The server is rebooting...';
            $serverStatus = 'Server is rebooting. Please wait a few minutes and refresh the page.';
        }
    }
} catch (Exception $e) {
    $errorMessage = htmlspecialchars($e->getMessage());
    if (strpos($errorMessage, 'SSH login failed') !== false || strpos($errorMessage, 'Failed to check server status') !== false) {
        $serverStatus = 'Server is offline or unreachable: ' . $errorMessage;
    } else {
        $rebootMessage = 'Error: ' . $errorMessage;
    }
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Manager-Server
        <small>
       ระบบ จัดการ server
        </small>
    </h1>
</div>
<div class="row">
	<div class="col-xl-12">
		<div id="panel-1" class="panel">
			<div class="panel-hdr">	
				<h2>
                ระบบ จัดการ Server Linux <span class="fw-300"><i> แก้ไขคอนฟิกที่ ssh-inc.php</i></span> 
				</h2>
				<div class="panel-toolbar">
					<button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
					<button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
					<button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
				</div>
			</div>
			<div class="panel-container show">
				<div class="panel-content">	
    <div class="container mt-5">
        <h2>Server Management</h2>

        <!-- แสดงสถานะเซิร์ฟเวอร์ -->
        <div class="alert alert-<?php echo strpos($serverStatus, 'online') !== false ? 'success' : (strpos($serverStatus, 'rebooting') !== false ? 'warning' : 'danger'); ?>" role="alert">
            <?php echo $serverStatus; ?>
        </div>

        <!-- แสดงข้อความสถานะการรีบูตหรือข้อผิดพลาด -->
        <?php if (!empty($rebootMessage)): ?>
            <div class="alert alert-<?php echo strpos($rebootMessage, 'Error') === false ? 'success' : 'danger'; ?>" role="alert">
                <?php echo htmlspecialchars($rebootMessage); ?>
            </div>
        <?php endif; ?>

        <!-- แสดงผลลัพธ์คำสั่ง -->
        <?php if (!empty($commandOutput)): ?>
            <div class="alert alert-info" role="alert">
                <pre><?php echo $commandOutput; ?></pre>
            </div>
        <?php endif; ?>


        <!-- กลุ่มคำสั่ง Cabal Service -->
        <div class="command-group">
            <h3>Cabal Service Management</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <?php
                    $cabal_commands = [
                        'cabal_start', 'cabal_stop', 'cabal_restart', 'cabal_reload', 'cabal_status',
                        'channels_start', 'channels_stop', 'channels_restart', 'channels_reload', 'channels_status',
                        'war_start', 'war_stop', 'war_restart', 'war_reload', 'war_status',
                        'tech_start', 'tech_stop', 'tech_restart', 'tech_reload', 'tech_status',
                        'login_start', 'login_stop', 'login_restart', 'login_reload', 'login_status',
                        'eventmgr_start', 'eventmgr_stop', 'eventmgr_restart', 'eventmgr_reload', 'eventmgr_status',
                        'ashop_start', 'ashop_stop', 'ashop_restart', 'ashop_reload', 'ashop_status'
                    ];
                    foreach ($cabal_commands as $cmd) {
                        echo "<option value='$cmd'>$cmd</option>";
                    }
                    ?>
                </select>
                <select name="server_pointer" class="custom-select form-control">
                    <option value="main">main</option>
                    <option value="test">test</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Channel -->
        <div class="command-group">
            <h3>Channel Management</h3>
            <form method="post" class="form-inline">
                <input type="hidden" name="action" value="chan">
                <input type="number" name="channel_number" class="form-control" placeholder="Channel number" required>
                <select name="server_pointer" class="custom-select form-control">
                    <option value="main">main</option>
                    <option value="test">test</option>
                </select>
                <select name="chan_action" class="custom-select form-control">
                    <option value="start">start</option>
                    <option value="stop">stop</option>
                    <option value="restart">restart</option>
                    <option value="reload">reload</option>
                    <option value="status">status</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Autobackup -->
        <div class="command-group">
            <h3>Autobackup</h3>
            <form method="post" class="form-inline">
                <input type="hidden" name="action" value="autobackup">
                <select name="server_pointer" class="custom-select form-control">
                    <option value="main">main</option>
                    <option value="test">test</option>
                </select>
                <select name="backup_state" class="custom-select form-control">
                    <option value="on">on</option>
                    <option value="off">off</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Database -->
        <div class="command-group">
            <h3>Database Management</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="create_backup">create_backup</option>
                    <option value="backup_dbs">backup_dbs</option>
                    <option value="create_dbs">create_dbs</option>
                    <option value="restore_dbs">restore_dbs</option>
                    <option value="shrink_dbs">shrink_dbs</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
            <form method="post" class="form-inline mt-2">
                <input type="hidden" name="action" value="cabal_db">
                <select name="db_action" class="custom-select form-control">
                    <option value="create">create</option>
                    <option value="remove">remove</option>
                    <option value="restore">restore</option>
                    <option value="backup">backup</option>
                    <option value="shrink">shrink</option>
                </select>
                <select name="server_pointer" class="custom-select form-control">
                    <option value="main">main</option>
                    <option value="test">test</option>
                </select>
                <input type="text" name="server_name" class="form-control" placeholder="Server name (e.g., Server01)" value="Server01">
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง War -->
        <div class="command-group">
            <h3>War Management</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="set_war_default">set_war_default</option>
                    <option value="set_war_flag">set_war_flag</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Server -->
        <div class="command-group">
            <h3>Server Management</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="server_create">server_create</option>
                    <option value="server_remove">server_remove</option>
                    <option value="gms_proxy_reexec">gms_proxy_reexec</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Network/IP -->
        <div class="command-group">
            <h3>Network/IP Management</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="world_set_ip">world_set_ip</option>
                    <option value="set_sql_access">set_sql_access</option>
                    <option value="ChangIP">ChangIP</option>
                    <option value="ChangUserPassworld">ChangUserPassworld</option>
                </select>
                <input type="text" name="ip_address" class="form-control" placeholder="IP address (e.g., *******)">
                <input type="text" name="sql_user" class="form-control" placeholder="SQL user (e.g., SA)" value="SA">
                <input type="text" name="sql_password" class="form-control" placeholder="SQL password">
                <select name="server_pointer" class="custom-select form-control">
                    <option value="main">main</option>
                    <option value="test">test</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Docker -->
        <div class="command-group">
            <h3>Docker Management</h3>
            <form method="post" class="form-inline">
                <input type="hidden" name="action" value="docker">
                <select name="docker_action" class="custom-select form-control">
                    <option value="start">start</option>
                    <option value="stop">stop</option>
                    <option value="restart">restart</option>
                </select>
                <select name="container" class="custom-select form-control">
                    <option value="cabal_test">cabal_test</option>
                    <option value="cabal_main">cabal_main</option>
                    <option value="sql_test">sql_test</option>
                    <option value="sql_main">sql_main</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
            <form method="post" class="form-inline mt-2">
                <select name="action" class="custom-select form-control">
                    <option value="docker_ps">docker ps -a</option>
                    <option value="docker_stats">docker stats</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง Ghapi-python -->
        <div class="command-group">
            <h3>Ghapi-python API</h3>
            <form method="post" class="form-inline">
                <input type="hidden" name="action" value="ghapi">
                <button type="submit" class="btn btn-primary">Run Ghapi-python</button>
            </form>
        </div>

        <!-- กลุ่มคำสั่ง System Monitoring -->
        <div class="command-group">
            <h3>System Monitoring</h3>
            <form method="post" class="form-inline">
                <select name="action" class="custom-select form-control">
                    <option value="htop">top -bn1</option>
                    <option value="mc">ls -l</option>
                    <option value="df">df -h</option>
                    <option value="tcpdump">tcpdump -nni eth0 icmp -c 5</option>
                    <option value="docker_ps_s">docker ps -s</option>
                </select>
                <button type="submit" class="btn btn-primary">Execute</button>
            </form>
        </div>

        <!-- ฟอร์มรีบูต -->
        <div class="command-group">
            <h3>Reboot Server</h3>
            <form method="post">
                <input type="hidden" name="action" value="reboot">
                <button type="submit" class="btn btn-danger"
                        onclick="return confirm('Are you sure you want to reboot the Linux server?');">
                    Reboot Linux Server
                </button>
            </form>
        </div>
    </div>  
  </div>
  </div>
  </div>
  </div>

