<?php 
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_SERVERLOG; ?> <small><?php echo PT_SERVERLOG_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getLogID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get server log by ID
                $selectWebLogID = "SELECT * FROM WEB_CE_Log WHERE id = '$getLogID'";
                $selectWebLogParam = array();
                $selectWebLogOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectWebLogQuery = sqlsrv_query($conn, $selectWebLogID, $selectWebLogParam, $selectWebLogOpt);
                $selectWebLogRows = sqlsrv_num_rows($selectWebLogQuery);

                if ($selectWebLogRows) {
                    while ($resServerLog = sqlsrv_fetch_array($selectWebLogQuery, SQLSRV_FETCH_ASSOC)) {
                        ?>
                        <div class="col-lg-6">
                            <h4 class="text-red">เลขไอดี</h4>
                            <p><?php echo $resServerLog['UserNum']; ?></p>
                            <hr>
                            <h4 class="text-red">เลขตัวละคร</h4>
                            <p><?php echo $resServerLog['Charidx']; ?></p>
                            <hr>
                            <h4 class="text-red">ชาแนล</h4>
                            <p><?php echo $resServerLog['Chanidx']; ?></p>
                            <hr>
                            <h4 class="text-red">แมพ</h4>
                            <p><?php echo $resServerLog['Worldidx']; ?></p>
                            <hr>
                            <h4 class="text-red">Hacktype</h4>
                            <p><?php echo $resServerLog['Hacktype']; ?></p>
                            <hr>
                            <h4 class="text-red">Hackcode</h4>
                            <p><?php echo $resServerLog['Hackcode']; ?></p>
                        </div>

                        <div class="col-lg-6">
                            <h4 class="text-red">ตำแหน่งโปรแกรม</h4>
                            <p><?php echo $resServerLog['Reason']; ?></p>
                            <hr>
                            <h4 class="text-red">MD5</h4>
                            <p><?php echo $resServerLog['Appcrc']; ?></p>
                            <hr>
                            <h4 class="text-red">รหัส pc</h4>
                            <p><?php echo $resServerLog['Pcwid']; ?></p>
                            <hr>
                            <h4 class="text-red">ชื่อเครื่อง pc</h4>
                            <p><?php echo $resServerLog['Pcname']; ?></p>
                            <hr>
                            <h4 class="text-red">ไอดี pc</h4>
                            <p><?php echo $resServerLog['Pcuser']; ?></p>
                            <hr>
                            <h4 class="text-red">เวลา</h4>
                            <p><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resServerLog['added_time'])); ?></p>
                        </div>
                        <?php
                    }
                }else{
                    $returnWarning = W_NOTHING_RETURNED;
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>