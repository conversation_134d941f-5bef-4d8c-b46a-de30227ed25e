var stack_topleft = { dir1: "down", dir2: "right", push: "top" };
var stack_bottomleft = { dir1: "right", dir2: "up", push: "top" };
var stack_bottomright = {
  dir1: "up",
  dir2: "left",
  firstpos1: 15,
  firstpos2: 15,
};
var stack_bar_top = {
  dir1: "down",
  dir2: "right",
  push: "top",
  spacing1: 0,
  spacing2: 0,
};
var stack_bar_bottom = { dir1: "up", dir2: "right", spacing1: 0, spacing2: 0 };

/*load vuocher*/
$(document).ready(function () {
  $("#js-sweetalert2-example-14").on("click", function () {
    var ipAPI = "https://api.ipify.org/?format=json";
    Swal.queue([
      {
        title: "ระบบตรวจสอบไอพี สาธารณะของคุณ",
        confirmButtonText: "ตรวจสอบไอพี IP",
        text: "IP สาธารณะของคุณ... " + "",
        showLoaderOnConfirm: true,
        preConfirm: function preConfirm() {
          return fetch(ipAPI)
            .then(function (response) {
              return response.json();
            })
            .then(function (data) {
              return Swal.insertQueueStep(data.ip);
            })
            .catch(function () {
              Swal.insertQueueStep({
                type: "error",
                title: "Unable to get your public IP",
              });
            });
        },
      },
    ]);
  }); //Timer functions example

  function load_login_notification(view = "") {
    $.ajax({
      url: "files/notification/fetch-charlogin.php",
      method: "POST",
      data: {
        view: view,
      },
      dataType: "json",
      success: function (data) {
        $(".dropdown-menu-login").html(data.notification);
        if (data.countcharlogin_notification > 0) {
          $(".countloginchar").html(data.countcharlogin_notification);
        }
      },
    });
  }
  load_login_notification(2500);
  setInterval(function () {
    load_login_notification();
  }, 50000);

  function load_unseen_notification(view = "") {
    $.ajax({
      url: "files/notification/fetch.php",
      method: "POST",
      data: {
        view: view,
      },
      dataType: "json",
      success: function (data) {
        $(".dropdown-menu-voucher").html(data.notification);
        if (data.unseen_notification > 0) {
          $(".countvoucher").html(data.unseen_notification);
        }
      },
    });
  }
  load_unseen_notification();
  setInterval(function () {
    load_unseen_notification();
  }, 3000);

  function load_mailitem_notification(view = "") {
    $.ajax({
      url: "files/notification/fetch-mailitem.php",
      method: "POST",
      data: {
        view: view,
      },
      dataType: "json",
      success: function (data) {
        $(".dropdown-menu-mailitem").html(data.notification);
        if (data.unseen_notification > 0) {
          $(".countmailitem").html(data.unseen_notification);
        }
      },
    });
  }
  load_mailitem_notification();
  setInterval(function () {
    load_mailitem_notification();
  }, 3000);
});

function load_reward_notification(view = "") {
  $.ajax({
    url: "files/notification/fetch-reward.php",
    method: "POST",
    data: {
      view: view,
    },
    dataType: "json",
    success: function (data) {
      if (data.countreward_notification > 0) {
        $(".countreward").html(data.countreward_notification);
      }
    },
  });
}
load_reward_notification();
setInterval(function () {
  load_reward_notification();
}, 3000);

function load_donate_notification(view = "") {
  $.ajax({
    url: "files/notification/fetch-donate.php",
    method: "POST",
    data: {
      view: view,
    },
    dataType: "json",
    success: function (data) {
      if (data.coundonate_notification > 0) {
        $(".countdonate").html(data.coundonate_notification);
      }
    },
  });
}
load_donate_notification();
setInterval(function () {
  load_donate_notification();
}, 3000);

function load_login_tables_notification(view = "") {
  $.ajax({
    url: "files/notification/fetch-charlogin-tables.php",
    method: "POST",
    data: {
      view: view,
    },
    dataType: "json",
    success: function (data) {
      $(".dropdown-menu-login2").html(data.notification2);
      if (data.countcharlogin_notification2 > 0) {
        $(".countloginchar2").html(data.countcharlogin_notification2);
      }
    },
  });
}
load_login_tables_notification(2500);
setInterval(function () {
  load_login_tables_notification();
}, 5000);

$("#datatables-default").dataTable({
  order: [[0, "desc"]],
  responsive: false,
});
$("#dt-basic-voucher").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});
$("#dt-basic-mailitem").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});
$("#dt-basic-donet").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});
$("#dt-basic-warch16").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});
$("#dt-basic-chars").dataTable({
  order: [[8, "desc"]],
  responsive: true,
});
$("#dt-basic-charslogin").dataTable({
  order: [[9, "desc"]],
  responsive: true,
});
$("#dt-basic-chars1").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});
$("#dt-basic-chars2").dataTable({
  order: [[0, "desc"]],
  responsive: true,
});

$("input[type=radio][name=skinchange]").change(function () {
  $(".irs").removeClassPrefix("irs--").addClass(this.value);
});
var ionskin = "flat";
$("#slotitem").ionRangeSlider({
  skin: ionskin,
  min: 0,
  max: 4,
  from: 0,
  step: 1,
  grid: true,
  grid_num: 4,
  grid_snap: false,
});

$("input[type=radio][name=skinchange]").change(function () {
  $(".irs").removeClassPrefix("irs--").addClass(this.value);
});
var ionskin = "flat";
$("#petsuplevel").ionRangeSlider({
  skin: ionskin,
  min: 0,
  max: 10,
  from: 0,
  step: 1,
  grid: true,
  grid_num: 10,
  grid_snap: false,
});

$("#dt-iteminvent").dataTable({
  order: [[0, "desc"]],
  responsive: true,
  dom:
    "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
    "<'row'<'col-sm-12'tr>>" +
    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
  buttons: [
    {
      extend: "colvis",
      text: "Column Visibility",
      titleAttr: "Col visibility",
      className: "btn-outline-default",
    },
    {
      extend: "csvHtml5",
      text: "CSV",
      titleAttr: "Generate CSV",
      className: "btn-outline-default",
    },
    {
      extend: "copyHtml5",
      text: "Copy",
      titleAttr: "Copy to clipboard",
      className: "btn-outline-default",
    },
    {
      extend: "print",
      text: '<i class="fal fa-print"></i>',
      titleAttr: "Print Table",
      className: "btn-outline-default",
    },
  ],
  columnDefs: [
    {
      targets: 0,
      orderable: true,
    },
  ],
});

$("#dt-itemlist").dataTable({
  responsive: true,
  processing: true,
  pageLength: false,
  ajax: "_data/items.json",
  deferRender: true,
  scrollY: 500,
  scrollCollapse: true,
  scroller: true,
  columnDefs: [
    {
      targets: -1,
      responsivePriority: -1,
      orderable: false,
    },
  ],
});

$("#dt-monsterlist").dataTable({
  responsive: true,
  processing: true,
  pageLength: false,
  ajax: "_data/monsters.json",
  deferRender: true,
  scrollY: 500,
  scrollCollapse: true,
  scroller: true,
  columnDefs: [
    {
      targets: -1,
      responsivePriority: -1,
      orderable: false,
    },
  ],
});

$("#dt-DGlist").dataTable({
  responsive: true,
  processing: true,
  pageLength: false,
  ajax: "_data/dun_world.json",
  deferRender: true,
  scrollY: 500,
  scrollCollapse: true,
  scroller: true,
  columnDefs: [
    {
      targets: -1,
      responsivePriority: -1,
      orderable: false,
    },
  ],
});

$("#dt-quest").dataTable({
  responsive: true,
  processing: true,
  pageLength: false,
  ajax: "_data/quest.json",
  deferRender: true,
  scrollY: 500,
  scrollCollapse: true,
  scroller: true,
  columnDefs: [
    {
      targets: -1,
      responsivePriority: -1,
      orderable: false,
    },
  ],
});

var columnSet = [
  {
    title: "CharacterIdx",
    id: "CharacterIdx",
    data: "CharacterIdx",
    placeholderMsg: "Server Generated ID",
    type: "readonly",
  },
  {
    title: "UserNum",
    id: "UserNum",
    data: "UserNum",
    type: "readonly",
  },
  {
    title: "ID",
    id: "ID",
    data: "ID",
    type: "readonly",
  },
  {
    title: "Name",
    id: "Name",
    data: "Name",
    type: "readonly",
  },
  {
    title: "Class",
    id: "Class",
    data: "Class",
    type: "readonly",
  },
  {
    title: "Warid",
    id: "Warid",
    data: "Warid",
    type: "text",
  },
  {
    title: "LEV",
    id: "LEV",
    data: "LEV",
    type: "text",
  },
  {
    title: "STR",
    id: "STR",
    data: "STR",
    type: "text",
  },
  {
    title: "DEX",
    id: "DEX",
    data: "DEX",
    type: "text",
  },
  {
    title: "INT",
    id: "INT",
    data: "INT",
    type: "text",
  },
  {
    title: "PNT",
    id: "PNT",
    data: "PNT",
    type: "text",
  },
  {
    title: "ALLSTATUS",
    id: "ALLSTATUS",
    data: "ALLSTATUS",
    type: "number",
  },
  {
    title: "Alz",
    id: "Alz",
    data: "Alz",
    type: "text",
  },
  {
    title: "WorldIdx",
    id: "WorldIdx",
    data: "WorldIdx",
    type: "text",
  },
  {
    title: "PlayTime",
    id: "PlayTime",
    data: "PlayTime",
    type: "text",
  },
  {
    title: "ChannelIdx",
    id: "ChannelIdx",
    data: "ChannelIdx",
    type: "text",
  },
  {
    title: "Nation",
    id: "Nation",
    data: "Nation",
    type: "text",
  },
  {
    title: "Login",
    id: "Login",
    data: "Login",
    type: "text",
  },
  {
    title: "Reputation",
    id: "Reputation",
    data: "Reputation",
    type: "text",
  },
  {
    title: "LogoutTime",
    id: "LogoutTime",
    data: "LogoutTime",
    type: "text",
  },
  {
    title: "LoginTime",
    id: "LoginTime",
    data: "LoginTime",
    type: "text",
  },
  {
    title: "Action",
    id: "Action",
    data: "Action",
    type: "text",
  },
];
/* start data table */
var myTable = $("#dt-Characterdatatables").dataTable({
  dom:
    "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
    "<'row'<'col-sm-12'tr>>" +
    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
  ajax: "_data/character_data.json",
  columns: columnSet,
  select: "single",
  altEditor: true,
  responsive: true,
  buttons: [
    {
      text: '<i class="fal fa-sync mr-1"></i> Synchronize',
      name: "refresh",
      className: "btn-primary btn-sm",
    },
  ],
  columnDefs: [
    {
      targets: 17,
      render: function (data, type, full, meta) {
        var badge = {
          1: {
            title: "Online",
            class: "badge-success",
          },
          0: {
            title: "Offline",
            class: "badge-danger",
          },
        };
        if (typeof badge[data] === "undefined") {
          return data;
        }
        return (
          '<span class="badge ' +
          badge[data].class +
          ' badge-pill">' +
          badge[data].title +
          "</span>"
        );
      },
    },
    {
      targets: 16,
      render: function (data, type, full, meta) {
        var package = {
          0: {
            title: "N/A",
            class: "badge-success",
          },
          1: {
            title: "Capella",
            class: "badge-warning",
          },
          2: {
            title: "Procyon",
            class: "badge-danger",
          },
          3: {
            title: "GM",
            class: "bg-danger-100 text-white",
          },
        };
        if (typeof package[data] === "undefined") {
          return data;
        }
        return (
          '<span class="badge ' +
          package[data].class +
          ' badge-pill">' +
          package[data].title +
          "</span>"
        );
      },
    },
    {
      targets: 21,
      render: function (data, type, full, meta) {
        //var number = Number(data.replace(/[^0-9.-]+/g,""));
        if (data > 0) {
          return (
            '<a href="?url=manager_game/manage-account-edit&id=' +
            data +
            '" class="btn btn-outline-warning btn-icon rounded-circle waves-effect waves-themed"><i class="fal fa-info-circle"></i></a>'
          );
        }
      },
    },
  ],
});

var columnSet = [
  {
    title: "CharacterIdx",
    id: "CharacterIdx",
    data: "CharacterIdx",
    placeholderMsg: "Server Generated ID",
    type: "readonly",
  },
  {
    title: "UserNum",
    id: "UserNum",
    data: "UserNum",
    type: "readonly",
  },
  {
    title: "ID",
    id: "ID",
    data: "ID",
    type: "readonly",
  },
  {
    title: "Name",
    id: "Name",
    data: "Name",
    type: "readonly",
  },
  {
    title: "Class",
    id: "Class",
    data: "Class",
    type: "readonly",
  },
  {
    title: "Warid",
    id: "Warid",
    data: "Warid",
    type: "text",
  },
  {
    title: "LEV",
    id: "LEV",
    data: "LEV",
    type: "text",
  },
  {
    title: "PlayTime",
    id: "PlayTime",
    data: "PlayTime",
    type: "text",
  },
  {
    title: "ChannelIdx",
    id: "ChannelIdx",
    data: "ChannelIdx",
    type: "text",
  },
  {
    title: "Nation",
    id: "Nation",
    data: "Nation",
    type: "text",
  },
  {
    title: "Reputation",
    id: "Reputation",
    data: "Reputation",
    type: "text",
  },
  {
    title: "Action",
    id: "Action",
    data: "Action",
    type: "text",
  },
];
/* start data table  war */
var myTable = $("#dt-Characterdatatables-War").dataTable({
  dom:
    "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
    "<'row'<'col-sm-12'tr>>" +
    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
  ajax: "_data/character_war_data.json",
  columns: columnSet,
  select: "single",
  altEditor: true,
  responsive: true,
  buttons: [
    {
      text: '<i class="fal fa-sync mr-1"></i> Synchronize',
      name: "refresh",
      className: "btn-primary btn-sm",
    },
  ],
  columnDefs: [
    {
      targets: 4,
      render: function (data, type, full, meta) {
        var badge = {
          1: {
            title: "Online",
            class: "badge-success",
          },
          0: {
            title: "Offline",
            class: "badge-danger",
          },
        };
        if (typeof badge[data] === "undefined") {
          return data;
        }
        return (
          '<span class="badge ' +
          badge[data].class +
          ' badge-pill">' +
          badge[data].title +
          "</span>"
        );
      },
    },
    {
      targets: 9,
      render: function (data, type, full, meta) {
        var package = {
          0: {
            title: "N/A",
            class: "badge-success",
          },
          1: {
            title: "Capella",
            class: "badge-warning",
          },
          2: {
            title: "Procyon",
            class: "badge-danger",
          },
          3: {
            title: "GM",
            class: "bg-danger-100 text-white",
          },
        };
        if (typeof package[data] === "undefined") {
          return data;
        }
        return (
          '<span class="badge ' +
          package[data].class +
          ' badge-pill">' +
          package[data].title +
          "</span>"
        );
      },
    },
    {
      targets: 11,
      render: function (data, type, full, meta) {
        //var number = Number(data.replace(/[^0-9.-]+/g,""));
        if (data > 0) {
          return (
            '<a href="?url=manager_game/manage-account-edit&id=' +
            data +
            '" class="btn btn-outline-warning btn-icon rounded-circle waves-effect waves-themed"><i class="fal fa-info-circle"></i></a>'
          );
        }
      },
    },
  ],
});

var columnSet = [
  {
    title: "PetSerial",
    id: "PetSerial",
    data: "PetSerial",
    placeholderMsg: "Server Generated ID",
    type: "readonly",
  },
  {
    title: "PetId",
    id: "PetId",
    data: "PetId",
    type: "text",
  },
  {
    title: "OwnerCharIdx",
    id: "OwnerCharIdx",
    data: "OwnerCharIdx",
    type: "text",
  },
  {
    title: "ItemIdx",
    id: "ItemIdx",
    data: "ItemIdx",
    type: "text",
  },
  {
    title: "Lev",
    id: "Lev",
    data: "Lev",
    type: "number",
  },
  {
    title: "LevExp",
    id: "LevExp",
    data: "LevExp",
    type: "text",
  },
  {
    title: "NickName",
    id: "NickName",
    data: "NickName",
    type: "text",
  },
];
/* start data table */
var myTable = $("#dt-petsdatatables").dataTable({
  /* check datatable buttons page for more info on how this DOM structure works */
  dom:
    "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
    "<'row'<'col-sm-12'tr>>" +
    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
  ajax: "_data/pets_data.json",
  columns: columnSet,
  /* selecting multiple rows will not work */
  select: "single",
  /* altEditor at work */
  altEditor: true,
  responsive: true,
  /* buttons uses classes from bootstrap, see buttons page for more details */
  buttons: [],
  columnDefs: [
    {
      targets: 4,
      render: function (data, type, full, meta) {
        var badge = {
          10: {
            title: "10",
            class: "badge-success",
          },
          9: {
            title: "9",
            class: "badge-warning",
          },
          5: {
            title: "1",
            class: "badge-danger",
          },
          1: {
            title: "0",
            class: "bg-danger-100 text-white",
          },
        };
        if (typeof badge[data] === "undefined") {
          return data;
        }
        return (
          '<span class="badge ' +
          badge[data].class +
          ' badge-pill">' +
          badge[data].title +
          "</span>"
        );
      },
    },
    {
      targets: 0,
      type: "currency",
      render: function (data, type, full, meta) {
        //var number = Number(data.replace(/[^0-9.-]+/g,""));
        if (data >= 0) {
          return '<span class="text-success fw-500">' + data + "</span>";
        } else {
          return '<span class="text-danger fw-500">' + data + "</span>";
        }
      },
    },
  ],
});

$(function () {
  $(".select2").select2();
  $(".select2-type-multiple").select2({
    placeholder: "เลือก Type",
  });
  $(".select2-amount-multiple").select2({
    placeholder: "เลือก amount",
  });
  $(".select2-amount2-multiple").select2({
    placeholder: "เลือก amount",
  });
  $(".js-hide-search").select2({
    minimumResultsForSearch: 1 / 0,
  });
  $(".js-max-length").select2({
    maximumSelectionLength: 2,
    placeholder: "Select maximum 2 items",
  });
  $(".js-max-length1").select2({
    maximumSelectionLength: 1,
    placeholder: "เลือกได้ 1 อาชีพ",
  });
  $(".select2-placeholder").select2({
    placeholder: "Select a state",
    allowClear: true,
  });
  $(".js-select2-icons").select2({
    minimumResultsForSearch: 1 / 0,
    templateResult: icon,
    templateSelection: icon,
    escapeMarkup: function (elm) {
      return elm;
    },
  });

  function icon(elm) {
    elm.element;
    return elm.id
      ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text
      : elm.text;
  }
  $(".js-data-example-ajax").select2({
    ajax: {
      url: "_data/repositories",
      dataType: "json",
      delay: 250,
      data: function (params) {
        return {
          q: params.term, // search term
          page: params.page,
        };
      },
      processResults: function (data, params) {
        // parse the results into the format expected by Select2
        // since we are using custom formatting functions we do not need to
        // alter the remote JSON data, except to indicate that infinite
        // scrolling can be used
        params.page = params.page || 1;
        return {
          results: data.items,
          pagination: {
            more: params.page * 30 < data.total_count,
          },
        };
      },
      cache: true,
    },
    placeholder: "Search for a repository",
    escapeMarkup: function (markup) {
      return markup;
    }, // let our custom formatter work
    minimumInputLength: 1,
    templateResult: formatRepo,
    templateSelection: formatRepoSelection,
  });

  function formatRepo(repo) {
    if (repo.loading) {
      return repo.text;
    }
    var markup =
      "<div class='select2-result-repository clearfix d-flex'>" +
      "<div class='select2-result-repository__avatar mr-2'><img src='" +
      repo.owner.avatar_url +
      "' class='width-2 height-2 mt-1 rounded' /></div>" +
      "<div class='select2-result-repository__meta'>" +
      "<div class='select2-result-repository__title fs-lg fw-500'>" +
      repo.full_name +
      "</div>";
    if (repo.description) {
      markup +=
        "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" +
        repo.description +
        "</div>";
    }
    markup +=
      "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
      "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " +
      repo.forks_count +
      " Forks</div>" +
      "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " +
      repo.stargazers_count +
      " Stars</div>" +
      "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " +
      repo.watchers_count +
      " Watchers</div>" +
      "</div>" +
      "</div></div>";
    return markup;
  }

  function formatRepoSelection(repo) {
    return repo.full_name || repo.text;
  }
});
