<?php
$user->restrictionUser(true, $conn);
$params = [];
$options = ['Scrollable' => SQLSRV_CURSOR_KEYSET];
?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class='subheader-icon fal fa-table'></i>Tables: <span class='fw-300'>Facebook Share</span> <sup
            class='badge badge-primary fw-500'>ADDON</sup>
        <small>
            กิจกรรม Facebook share
        </small>
    </h1>
    <?php
    if (isset($_POST['btn_insert'])) {
        $objCSV = fopen('eventdata.txt', 'r');
        while (($objArr = fgetcsv($objCSV, 1000, ',')) !== false) {
            $InsertEvent ="INSERT INTO WEB_Facebook_Share (userid,status,added_time) VALUES ('".$objArr[0] ."','0',GETDATE())";
            $InsertQuery = sqlsrv_query($conn, $InsertEvent, $params, $options);
        }
        fclose($objCSV);
        $returnSuccess = 'Import Done.';
    }
    if (isset($_POST['btn_Clean'])) {
        $registerPlayer = 'TRUNCATE table WEB_Facebook_Share';
        $InsertQuery = sqlsrv_query($conn, $registerPlayer, $params, $options);
        $returnSuccess = 'Clean Data Done.';
    }
    ?>
    <?php if (isset($returnSuccess)) { ?>
    <div class="alert alert-success j_dismiss"> <?php echo $returnSuccess; ?></div>
    <?php } elseif (isset($returnWarning)) { ?>
    <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
    <?php } elseif (isset($returnError)) { ?>
    <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
    <?php } ?>
</div>

<div class="panel">
    <div class="panel-hdr">
        <h2>
            Table <span class="fw-300"><i>Facebook Share</i></span>
        </h2>
        <div class="col-sm-12 col-md-6 d-flex align-items-center justify-content-end">
            <div class="dt-buttons">
                <form role="form" method="post" enctype="multipart/form-data">
                    <button onclick="return confirm('ยืนยันจะเพิ่มข้อมูล?')" type="submit" name="btn_insert"
                        class="btn buttons-csv buttons-html5 btn-outline-default" tabindex="0"
                        aria-controls="dt-basic-example" type="button" title="เพิ่มข้อมูลจาก text"><span>Import
                            Data</span></button>
                    <button onclick="return confirm('ยืนยันจะลบข้อมูลทั้งหมด?')" type="submit" name="btn_Clean"
                        class="btn buttons-copy buttons-html5 btn-outline-default" tabindex="0"
                        aria-controls="dt-basic-example" type="button" title="ลบข้อมูลทั้งหมด"><span>
                            ลบข้อมูลทั้งหมด</span></button>
                </form>
            </div>
        </div>
        <div class="panel-toolbar">
            <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                data-original-title="Collapse"></button>
            <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10"
                data-original-title="Fullscreen"></button>
            <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                data-original-title="Close"></button>
        </div>
    </div>
    <div class="panel-container show">

        <div class="panel-content">
            <div class="frame-wrap">
                <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>userid</th>
                            <th>status</th>
                            <th>date</th>
                            <th>Action</th>
                            <!--<th><?php echo T_ACTION; ?>
    </th>-->
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage)
                        {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = [];
                            $i = 0;
                            while (
                                ($row = sqlsrv_fetch_array(
                                    $stmt,
                                    SQLSRV_FETCH_NUMERIC,
                                    SQLSRV_SCROLL_ABSOLUTE,
                                    $offset + $i
                                )) &&
                                $i < $rowsPerPage
                            ) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 500;

                        // Define and execute the query.
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql =
                            'SELECT * FROM  WEB_Facebook_share where  status = 0 ORDER BY added_time DESC';

                        $stmt = sqlsrv_query(
                            $conn,
                            $sql,
                            [],
                            ['Scrollable' => 'static']
                        );
                        if (!$stmt) {
                            die(print_r(sqlsrv_errors(), true));
                        }

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false) {
                            die(print_r(sqlsrv_errors(), true));
                        } elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum'])
                            ? $_GET['pageNum']
                            : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) { ?>
                        <tr>
                            <td><?php echo $row[0]; ?></td>
                            <td><?php echo $row[1]; ?></td>
                            <td><span class="label
								<?php echo $label =
            $row[2] == '1'
                ? ' label-danger'
                : ($row[2] == '0'
                    ? ' label-success'
                    : 'label-default'); ?>">
                                    <?php echo $status =
                                        $row[2] == '1'
                                            ? 'ยืนยันเรียบร้อยแล้ว'
                                            : ($row[2] == '0'
                                                ? 'รอยืนยัน'
                                                : 'Unknow status'); ?></span>
                            </td>
                            <td><?php echo date(
                                'd/m/Y ' . T_AT . ' H:i',
                                strtotime($row[3])
                            ); ?></td>
                            <td>
                                <form method="post" name="j_add_facebookshare" action="">
                                    <div class="j_alert"></div>

                                    <input type="hidden" name="id" value="<?php echo $row[0]; ?>">
                                    <input type="hidden" name="charid" value="<?php echo $row[1]; ?>">
                                    <button type="submit" class="btn btn-primary">ยืนยันทำรานการ</button>
                                    <img src="assets/images/loading/loader.gif" class="load"
                                        alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                </form>
                            </td>
                        </tr>
                        <?php }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>