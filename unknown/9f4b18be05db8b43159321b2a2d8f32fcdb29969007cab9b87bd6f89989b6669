/**
 * Notification Manager for Item Sending System
 * Handles real-time notifications and notification display
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.pollInterval = 30000; // 30 seconds
        this.isPolling = false;
        this.adminUsername = 'Admin'; // Get from session or config
        
        this.init();
    }
    
    init() {
        this.createNotificationUI();
        this.startPolling();
        this.bindEvents();
    }
    
    /**
     * Create notification UI elements
     */
    createNotificationUI() {
        // Create notification bell icon
        const notificationHtml = `
            <div id="notification-container" class="position-relative d-inline-block">
                <button id="notification-bell" class="btn btn-outline-primary position-relative" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-bell"></i>
                    <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                        0
                    </span>
                </button>
                
                <div id="notification-dropdown" class="dropdown-menu dropdown-menu-end" style="width: 400px; max-height: 500px; overflow-y: auto;">
                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                        <span><strong>Notifications</strong></span>
                        <button id="mark-all-read" class="btn btn-sm btn-link p-0">Mark all read</button>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div id="notification-list">
                        <div class="dropdown-item text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i> Loading...
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item text-center">
                        <button id="view-all-notifications" class="btn btn-sm btn-primary">View All</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add to page (you may need to adjust the selector)
        const targetElement = document.querySelector('.navbar-nav') || document.querySelector('body');
        if (targetElement) {
            targetElement.insertAdjacentHTML('beforeend', notificationHtml);
        }
    }
    
    /**
     * Bind event handlers
     */
    bindEvents() {
        // Bell click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#notification-bell')) {
                this.loadNotifications();
            }
            
            if (e.target.closest('#mark-all-read')) {
                this.markAllAsRead();
            }
            
            if (e.target.closest('#view-all-notifications')) {
                this.showAllNotifications();
            }
            
            if (e.target.closest('.notification-item')) {
                const notificationId = e.target.closest('.notification-item').dataset.notificationId;
                this.markAsRead(notificationId);
            }
        });
    }
    
    /**
     * Start polling for new notifications
     */
    startPolling() {
        if (this.isPolling) return;
        
        this.isPolling = true;
        this.loadNotifications(); // Initial load
        
        setInterval(() => {
            this.loadNotifications(true); // Silent update
        }, this.pollInterval);
    }
    
    /**
     * Load notifications from server
     */
    async loadNotifications(silent = false) {
        try {
            const response = await fetch(`notification_system.php?admin=${this.adminUsername}&limit=10&unread_only=false`);
            const data = await response.json();
            
            if (data.success) {
                this.notifications = data.notifications;
                this.updateUI(silent);
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
            if (!silent) {
                this.showError('Failed to load notifications');
            }
        }
    }
    
    /**
     * Update notification UI
     */
    updateUI(silent = false) {
        this.updateBadge();
        this.updateDropdown();
        
        if (!silent) {
            this.showNewNotifications();
        }
    }
    
    /**
     * Update notification badge
     */
    updateBadge() {
        const badge = document.getElementById('notification-badge');
        const unreadCount = this.notifications.filter(n => !n.is_read).length;
        
        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
        
        this.unreadCount = unreadCount;
    }
    
    /**
     * Update notification dropdown
     */
    updateDropdown() {
        const list = document.getElementById('notification-list');
        if (!list) return;
        
        if (this.notifications.length === 0) {
            list.innerHTML = '<div class="dropdown-item text-center text-muted">No notifications</div>';
            return;
        }
        
        list.innerHTML = this.notifications.map(notification => {
            const isUnread = !notification.is_read;
            const timeAgo = this.getTimeAgo(notification.created_at);
            
            return `
                <div class="notification-item dropdown-item ${isUnread ? 'bg-light' : ''}" 
                     data-notification-id="${notification.notification_id}" 
                     style="cursor: pointer; border-left: ${isUnread ? '3px solid #007bff' : '3px solid transparent'};">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="fw-bold text-truncate">${notification.title}</div>
                            <div class="text-muted small text-truncate">${notification.message}</div>
                            <div class="text-muted small">${timeAgo}</div>
                        </div>
                        <div class="ms-2">
                            ${this.getNotificationIcon(notification.type)}
                            ${isUnread ? '<span class="badge bg-primary rounded-pill">New</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    /**
     * Get notification icon based on type
     */
    getNotificationIcon(type) {
        const icons = {
            'item_send': '<i class="fas fa-gift text-success"></i>',
            'error': '<i class="fas fa-exclamation-triangle text-danger"></i>',
            'warning': '<i class="fas fa-exclamation-circle text-warning"></i>',
            'info': '<i class="fas fa-info-circle text-info"></i>'
        };
        
        return icons[type] || icons['info'];
    }
    
    /**
     * Get time ago string
     */
    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
    
    /**
     * Mark notification as read
     */
    async markAsRead(notificationId) {
        try {
            const response = await fetch('notification_system.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'mark_read',
                    notification_id: notificationId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update local notification
                const notification = this.notifications.find(n => n.notification_id === notificationId);
                if (notification) {
                    notification.is_read = true;
                    notification.read_at = new Date().toISOString();
                }
                
                this.updateUI(true);
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    /**
     * Mark all notifications as read
     */
    async markAllAsRead() {
        const unreadNotifications = this.notifications.filter(n => !n.is_read);
        
        for (const notification of unreadNotifications) {
            await this.markAsRead(notification.notification_id);
        }
    }
    
    /**
     * Show new notifications as toast
     */
    showNewNotifications() {
        const newNotifications = this.notifications.filter(n => {
            const createdAt = new Date(n.created_at);
            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
            return createdAt > fiveMinutesAgo && !n.is_read;
        });
        
        newNotifications.forEach(notification => {
            this.showToast(notification);
        });
    }
    
    /**
     * Show toast notification
     */
    showToast(notification) {
        // Use SweetAlert2 toast if available
        if (typeof Swal !== 'undefined') {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true,
                onOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                }
            });
            
            Toast.fire({
                icon: notification.type === 'item_send' ? 'success' : 'info',
                title: notification.title,
                text: notification.message
            });
        }
    }
    
    /**
     * Show error message
     */
    showError(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                timer: 3000
            });
        } else {
            alert(message);
        }
    }
    
    /**
     * Show all notifications in modal
     */
    showAllNotifications() {
        // This would open a modal with all notifications
        // Implementation depends on your modal system
        console.log('Show all notifications modal');
    }
    
    /**
     * Create notification manually (for testing)
     */
    async createTestNotification() {
        try {
            const response = await fetch('notification_system.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'create',
                    send_id: 999,
                    player_username: 'test_player',
                    item_id: 123,
                    item_code: '0000007B00000000',
                    options_code: '0000000000000000',
                    quantity: 1,
                    duration: 0,
                    send_method: 'mail',
                    status: 'sent_to_mail',
                    admin_username: this.adminUsername
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.loadNotifications();
            }
        } catch (error) {
            console.error('Error creating test notification:', error);
        }
    }
}

// Initialize notification manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.notificationManager = new NotificationManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
