<?php

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');
require('../../_app/php/userLogin.class.php');
$userLogin = new userLogged();
$userLogin->exitHome(); // when you click in "Log out"
$params = array();
$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );	


			$selectChars = "SELECT  * FROM [".DATABASE_SV."].[dbo].cabal_pet_table";
            $selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);
                if (sqlsrv_num_rows($selectCharsQuery)) {
                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {	
										$name2 = $userLogin->thaitrans($resChars["NickName"]);
									
										$array_data[] = array( 
											 'PetSerial'               =>  $resChars['PetSerial'],
											 'PetId'          =>     $resChars["PetId"],
											 'OwnerCharIdx'          =>     $resChars["OwnerCharIdx"],
											 'ItemIdx'          =>     $resChars["ItemIdx"],
											 'Lev'          =>     $resChars["Lev"],
											 'LevExp'          =>     $resChars["LevExp"],
											 'NickName'          =>     $name2
										);  
									}
									$final_data = json_encode($array_data,JSON_UNESCAPED_UNICODE); 
									if(file_put_contents('pets_data.json', $final_data))  {  
										$message = "<label class='text-success'>File Appended Success fully</p>";  
									} else {  
										$message = 'JSON File not exits';  
							   		}  
						  
				   
				   echo 'ok1'; 
                } else {
					echo 'no1'; 
                }      
          
				@sqlsrv_close($conn);
?>