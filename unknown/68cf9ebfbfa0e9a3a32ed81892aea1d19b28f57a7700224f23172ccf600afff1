
<?php
$CharrecIdx = [];
$sql = "EXECUTE " . DATABASE_WEB . ".dbo.WEB_Get_cabal_character_list '$userNum'";
$stmt = sqlsrv_query($conn, $sql);

if ($stmt) {
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $CharrecIdx[] = [
            'CharacterIdx' => $row['CharacterIdx'] ?? 0,
            'Name' => $row['Name'] ?? 'Unknown',
            'LEV' => $row['LEV'] ?? 0,
            'Alz' => $row['Alz'] ?? 0,
            'Nation' => $row['Nation'] ?? 0,
            'Style' => $row['STYLE'] ?? 0,
            'Login' => $row['Login'] ?? 0
        ];
    }
}
?>

<div class="container-fluid">
    <?php if (!empty($CharrecIdx)): ?>
    <div class="row">
        <div class="col-12">
            <h3 class="section-title mb-4">
                <i class="fal fa-users"></i> ตัวละครในเกม
            </h3>
        </div>
    </div>

    <div class="row">
        <?php foreach ($CharrecIdx as $CharInfo):
            $classInfo = $userLogin->cabalstyle($CharInfo['Style']) ?? [];
            $className = $classInfo['Class_Name'] ?? 'UnknownClass';
            $styleIdx = $classInfo['CharacterIdx'] ?? '-';

            // Soul Ability
            $selectabilitySql = "SELECT AbilityPoint FROM " . DATABASE_SV . ".dbo.cabal_soul_ability_table WHERE CharacterIdx = ?";
            $selectabilityQuery = sqlsrv_query($conn, $selectabilitySql, [$CharInfo['CharacterIdx']]);
            $abilityData = sqlsrv_fetch_array($selectabilityQuery, SQLSRV_FETCH_ASSOC);
            $AbilityPoint = $abilityData['AbilityPoint'] ?? 0;

            // War EXP
            $selectWexpSql = "SELECT WarExp FROM " . DATABASE_SV . ".dbo.cabal_WarExp_Table WHERE CharacterIdx = ?";
            $selectWexpQuery = sqlsrv_query($conn, $selectWexpSql, [$CharInfo['CharacterIdx']]);
            $wexpData = sqlsrv_fetch_array($selectWexpQuery, SQLSRV_FETCH_ASSOC);
            $WarExp = $wexpData['WarExp'] ?? 0;
        ?>
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="content-card">
                <div class="character-header">
                    <img src="assets/images/cabal/class/<?php echo $className; ?>.png"
                         alt="<?php echo $className; ?>"
                         class="character-avatar">
                    <div class="character-info">
                        <h4 class="character-name"><?php echo $userLogin->thaitrans($CharInfo['Name']); ?></h4>
                        <div class="character-meta">
                            <span class="character-class"><?php echo htmlspecialchars($CharInfo['CharacterIdx']); ?></span>
                            <span class="character-class"><?php echo htmlspecialchars($className); ?></span>
                            <span class="character-level">Level <?php echo htmlspecialchars($CharInfo['LEV']); ?></span>
                        </div>
                        <div class="character-status">
                            <span class="status-badge <?php echo ($CharInfo['Login'] == 0) ? 'offline' : 'online'; ?>">
                                <?php echo ($CharInfo['Login'] == 0) ? 'ออฟไลน์' : 'ออนไลน์'; ?>
                            </span>
                            <span class="nation-badge"><?php echo htmlspecialchars($userLogin->nation($CharInfo['Nation']) ?? 'N/A'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($CharInfo['Exp'] ?? 0); ?></div>
                        <div class="stat-label">Experience</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($CharInfo['Alz']); ?></div>
                        <div class="stat-label">Alz</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($AbilityPoint); ?></div>
                        <div class="stat-label">Ability Points</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($WarExp); ?></div>
                        <div class="stat-label">War EXP</div>
                    </div>
                </div>

                <div class="text-center">
                    <a href="?url=manager_charecter/view&char_id=<?php echo htmlspecialchars($CharInfo['CharacterIdx']); ?>"
                       class="action-btn primary">
                        <i class="fal fa-search"></i> ดูรายละเอียด
                    </a>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php else: ?>
    <div class="row">
        <div class="col-12">
            <div class="content-card text-center">
                <i class="fal fa-user-slash" style="font-size: 4rem; color: #6c757d; margin-bottom: 20px;"></i>
                <h4 class="text-muted">ไม่พบตัวละคร</h4>
                <p class="text-muted">ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้สร้างตัวละคร</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>




