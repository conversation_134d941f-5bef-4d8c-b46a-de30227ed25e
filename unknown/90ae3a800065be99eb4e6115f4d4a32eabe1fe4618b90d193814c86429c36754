<?php $user->restrictionUser(true, $conn); ?>
<div class="page"><h3><?php echo PT_MANAGETICKETS; ?> <small><?php echo PT_MANAGETICKETS_DESC; ?></small></h3></div>
<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=manager/chars" class="btn btn-block btn-info">Back to the Chars page</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 1000;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $warid = $userLogin->wartochar($getResult);
                    $sql = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx LIKE '%".$getResult."%' OR Name LIKE '%".$getResult."%' OR CharacterIdx ='$warid'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Char not found</strong></div> ';
                        echo $namethai;
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover helpdesks" id="datatable-default">
                        <thead>
                        <tr>
                            <th>CharacterIdx</th>
                            <th>Name</th>
                            <th>LEV</th>
                            <th>Alz</th>
                            <th>WorldIdx</th>
							<th>Style</th>
							<th>ClassRank</th>
							<th>ChannelIdx</th>
							<th>Nation</th>
                            <th>PlayTime</th>
                            <th>Login</th>
                            <th>Warid</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr
                                    <?php
                                    $selectUsersDataChar = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = '$row[0]'";
                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersDataChar, array());
                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                                    $style = $userLogin->decode_style($row[12]);
                                    $usernum = floor($selectUsersDataFetch['CharacterIdx']/8);

                                    $selectUsersDataID = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$usernum'";
                                    $selectUsersDataIDQuery = sqlsrv_query($conn, $selectUsersDataID, array());
                                    $selectUsersDataIDFetch = sqlsrv_fetch_array($selectUsersDataIDQuery, SQLSRV_FETCH_ASSOC);

                                    if ($selectUsersDataIDFetch['AuthType'] == '2' ||  $selectUsersDataIDFetch['AuthType'] == '3' ||  $selectUsersDataIDFetch['AuthType'] == '4') {   
                                        echo ' class="bg-red text-white"';
                                    }
                                ?>>
                                    <td><a href="#"><?php echo $row[0]; ?></a></td>
                                    <td><a href="#"><?php echo $userLogin->thaitrans($row[1]); ?> : <?php echo $row[1]; ?></a></td>
                                    <td><a href="#"><?php echo $row[2]; ?></a></td>
                                    <td><a href="#"><?php echo $row[9]; ?></a></td>
                                    <td><a href="#"><?php echo $row[10]; ?></a></td>
                                    <td><img src="assets/images/icons/<?php echo $style['Class_Name']; ?>.png" data-toggle="tooltip" data-title="<?php echo $style['Class_Name']; ?>" title="<?php echo $style['Class_Name']; ?>" class="img-circle"  style="width:30px; height: 30px;"></td>
								    <td><?php  $userLogin->Reputation($row[25]); ?></td>
								    <td><?php echo $row[28]; ?></td>
								    <td><img src="assets/images/cabal/<?php echo  $userLogin->nation($row[30]); ?>.gif" data-toggle="tooltip" data-title="<?php echo  $userLogin->nation($row[30]); ?>!" title="<?php echo  $userLogin->nation($row[30]); ?>!" class="img-circle"  style="width:30px; height: 30px;"></td>
                                    <td><a href="#"><?php $minutes = $row[27] / 60; echo (int)$minutes . ' ช.ม'; ?></a></td>
                                    <td><?php if($row[34] == 0){ 
                                        echo '<label class="label label-danger text-red padd-sm">0ff Line</label>'; 
                                        }else{
                                        echo '<label class="label label-success text-red padd-sm">On Line</label>'; 
                                        }
	?>
								    </td>
                                   <td class="visible-lg visible-md visible-sm"><?php echo $warid = $userLogin->chartowar($row[0]); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                                <?php echo B_ACTION; ?> <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu" role="menu">
                                            <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                                <li><a href="?url=manager/see-char&id=<?php echo $row[0]; ?>"><?php echo B_SEEPLRINFO; ?></a></li>
                                                <li><a href="?url=manager/edit-char&id=<?php echo $row[0]; ?>">Edit</a></li>
												<li><a href="?url=manager/edit-char-trade&id=<?php echo $row[0]; ?>">เพิ่มยศ</a></li>
                                                <li><a href="?url=manager/see-mail&id=<?php echo $row[0]; ?>">ตรวจสอบเมลล์</a></li>
                                                <li><a href="?url=manager/see-char&id=<?php echo $row[0]; ?>&delete=wait">ลบตัวละคร</a></li>
                                            <?php } ?>
                                            <?php if ($selectUsersDataIDFetch['AuthType'] == '2' || $selectUsersDataIDFetch['AuthType'] == '3' || $selectUsersDataIDFetch['AuthType'] == '4') { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=unban-wait"><span class="text-white"><?php echo B_UNBAN; ?></span></a></li>
                                            <?php } else { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=wait"><span class="text-white"><?php echo B_BAN; ?></span></a></li>
                                            <?php } ?>
                                        </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
               
            </div>
        </div>
    </div>

</div>
