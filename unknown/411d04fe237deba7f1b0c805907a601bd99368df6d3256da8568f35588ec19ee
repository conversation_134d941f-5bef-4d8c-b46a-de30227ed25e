<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> manage-pets
        <small>
        ระบบ แก้ไขสัตว์เลี้ยงผู้เล่น
        </small>
    </h1>
</div>
<div class="alert alert-primary alert-dismissible">
	<button type="button" class="close" data-dismiss="alert" aria-label="Close">
		<span aria-hidden="true">
			<i class="fal fa-times"></i>
		</span>
	</button>
	<div class="d-flex flex-start w-100">
		<div class="mr-2 hidden-md-down">
			<span class="icon-stack icon-stack-lg">
				<i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
				<i class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
				<i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
			</span>
		</div>
		<div class="d-flex flex-fill">
			<div class="flex-fill">
                <span class="h5">How it works</span>
                <br> 
                -ผู้เล่น ออกจากเกมส์ไปรออยู่หน้า<code>Login หรือหน้า เลือกตัวละคร</code>
				<br> 
				-ให้ตรวจสอบ <code>PetSerial</code> จาก  <code>Pets Table</code> แล้วนำ <code>PetSerial</code> มากรอกข้อมูลลง <code>Textinput PetSerial</code>
				<br> 
                -การปรับ <code>Level</code> ให้นำ <code>PetSerial</code> มาใส่ใส่ใน <code>Textinput PetSerial</code>แล้วเลือก Level ที่ต้องการ แล้วกดปุ่ม <code>ยืนยันข้อมูล Level</code>
                <br> 
                -การปรับ <code>Option</code> ให้นำ <code>PetSerial</code> มาใส่ใส่ใน <code>Textinput PetSerial</code>แล้วเลือก Option ที่ต้องการ แล้วกดปุ่ม <code>ยืนยันข้อมูล Option</code>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-xl-6">
		<div id="panel-1" class="panel">
			<div class="panel-hdr">	
				<h2>
                Edit Pets <span class="fw-300"><i>แก้ไขข้อมูลสัตว์เลี้ยง</i></span> 
				</h2>
				<div class="panel-toolbar">
					<button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
					<button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
					<button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
				</div>
			</div>
			<div class="panel-container show">
				<div class="panel-content">									
                <?php
             if (isset($_POST['btn_savechangeLev'])) {
                // variable
                $inputPetid = strip_tags(trim($_POST['input_Petid']));
                $inputLev = strip_tags(trim($_POST['input_Lev']));
                // condition
                if (empty($inputPetid)) {
                    $returnWarning = "โปรดกรอกข้อมูล PetSerial";
                } else if (empty($inputLev)) {
                    $returnWarning = "โปรดกรอกข้อมูล Level"; 
                } else {

                //admin id //
                $adminid = $userLogin->recUserAccount('ID', $conn);
                $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                $dateNow = date('Y-m-d H:i:s');
                $selectauth = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table WHERE PetSerial = '$inputPetid'";
                $selectauthParam = array();
                $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                $PetSerial = $selectauthFetch['PetSerial'];
                    if(sqlsrv_rows_affected($selectauthQuery)){
                        $PetID = "UPDATE [".DATABASE_SV."].[dbo].cabal_pet_table SET Lev = ? WHERE PetSerial = ?";
                        $PetIDParams = array($inputLev,$inputPetid);
                        $PetIDParamsQuery = sqlsrv_query($conn, $PetID, $PetIDParams);
                            if ($PetIDParamsQuery) { 
                                $zpanel->generateWebLog($conn, '2', $adminidx, 'แก้ไข Level', "PetSerial: {$inputPetid} Level : {$inputLev} DateTime : {$dateNow}");
                                $returnSuccess = "SUCCESS :: แก้ไข PetSerial {$inputPetid} เป็น Level {$inputLev} เรียบร้อย";   
                            } else {
                            $returnError = "ERROR :: ระบบ อัพเดดเวล ผิดพลาด";  
                            }
                    }else{
                        $returnWarning = "ไม่มี PetSerial นี้";
                    }
                    
                }
            }
      
        if (isset($_POST['btn_savechange'])) {
                    // variable
                    $inputPetid = strip_tags(trim($_POST['input_Petid']));
                    $inputOption = strip_tags(trim($_POST['input_Opt']));
                    // condition
                    if (empty($inputPetid)) {
                        $returnWarning = "โปรดกรอกข้อมูล PetSerial";
                    } else {

                    //admin id //
                    $adminid = $userLogin->recUserAccount('ID', $conn);
                    $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                    $dateNow = date('Y-m-d H:i:s');
                    $selectauth = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table WHERE PetSerial = '$inputPetid'";
                    $selectauthParam = array();
                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                    $PetSerial = $selectauthFetch['PetSerial'];
                        if(sqlsrv_rows_affected($selectauthQuery)){
                                    $PetOpt = "EXECUTE WEB_Pets_Opt ?, ?";
                                    $PetOptParams = array($inputOption,$PetSerial);
                                    $PetOptQuery = sqlsrv_query($conn, $PetOpt, $PetOptParams);
                                        if ($PetOptQuery) {
                                                $zpanel->generateWebLog($conn, '2', $adminidx, 'แก้ไข ออฟชั่น', "PetSerial: {$inputPetid} ออฟชั่น : {$inputOption} DateTime : {$dateNow}");
                                                $returnSuccess = "SUCCESS :: แก้ไข PetSerial {$inputPetid} เป็น Option {$inputOption} เรียบร้อย";
                                        } else {
                                                $returnError = "ERROR :: ระบบ อัพเดด ออฟชั่นผิดพลาด";
                                        }
                        }else{
                            $returnWarning = "ไม่มี PetSerial นี้";
                        }
                        
                    }
                }

                
                if (isset($_POST['btn_loadpetsdata_json'])) {
                            $params = array();
                            $options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );	
                            $selectChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table";
                            $selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    $array_data = array();
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {	
                                                        $array_data[] = array(
                                                                        'PetSerial'    =>     "$resChars[PetSerial]",
                                                                        'PetId'        =>     "$resChars[PetId]",
                                                                        'OwnerCharIdx' =>     "$resChars[OwnerCharIdx]",
                                                                        'ItemIdx'      =>     "$resChars[ItemIdx]",
                                                                        'Lev'          =>     "$resChars[Lev]",
                                                                        'LevExp'       =>     "$resChars[LevExp]",
                                                                        'NickName'     =>     "$resChars[NickName]"
                                                                    );  
                                                    }

                                                    $response = array(
                                                        "data" => $array_data
                                                    );

                                                    $final_data = json_encode($response,JSON_PRETTY_PRINT); 
                                                    if(file_put_contents('_data/pets_data.json', $final_data))  {  
                                                        $message = "<label class='text-success'>File Appended Success fully</p>";  
                                                    } else {  
                                                        $message = 'JSON File not exits';  
                                                       }  
                                          
                                   
                                    $returnSuccess = 'โหลดข้อมูลสัตว์เลี้ยงเรียบร้อยแล้ว'; 
                                } else {
                                    $returnError = 'ข้อมูลผิดพลาด'; 
                                }   
                }   

                ?>
                <?php if (isset($returnSuccess)) { ?>
                <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
					<div class="frame-heading">
                    <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label for="input_Petid" class="control-label">PetSerial: </label>
                        <input type="text" name="input_Petid" class="form-control" id="input_Petid" placeholder="PetSerial"
                            pattern="[a-zA-Z0-9]+$" value="<?php if (isset($inputPetid)) echo $inputPetid; ?>">
                    </div>
                        <p><code>*จะแก้ไขสัตว์เลี้ยงควรตั้งชื่อสัตว์เลี้ยงก่อน*</code></p>
                </div>
                <div class="col-sm-12">
                            <div class="form-group">
                            <label for="input_Item" class="control-label">Slot:</label>
                                <div class="m-md slider-primary">
                                    <input name="input_Lev" id="petsuplevel" type="text" value="<?php if (isset($inputLev)) echo $inputLev; ?>" class="d-none" tabindex="-1" readonly="">
                                </div>
                            </div>
                            <p> with <code>max</code> value set to 10</p>
                        </div>
        
                <div class="col-md-12">
                        <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row">
                            <button type="submit" name="btn_savechangeLev" class="btn btn-danger ml-auto waves-effect waves-themed">
                            <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง Level
                            </button>
                        </div>
                    </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="input_Opt" class="control-label">Option Pets: </label>
                        <select data-plugin-selectTwo name="input_Opt" id="input_Opt" pattern="[0-9]+$"
                            class="form-control populate placeholder"
                            data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                            <optgroup label="อายุ ชม.">
                                <option value="0">รูว่าง</option>
                                <option value="1">HP</option>
                                <option value="2">MP</option>
                                <option value="3">Attack</option>
                                <option value="4">Magic Attack</option>
                                <option value="5">Defense</option>
                                <option value="6">Attack Rate</option>
                                <option value="7">Defense Rate</option>
                                <option value="8">Critical Damage</option>
                                <option value="9">Critical Rate</option>
                                <option value="10">Min Damage</option>
                                <option value="11">Max HP Steal Hit</option>
                                <option value="12">Max MP Steal Hit</option>
                                <option value="13">Max Critical Rate</option>
                                <option value="14">Sword Skill Amp</option>
                                <option value="15">Magic Skill Amp</option>
                                <option value="16">HP Steal %</option>
                                <option value="17">MP Steal %</option>
                                <option value="18">Evasion</option>
                                <option value="19">HP Auto Heal</option>
                                <option value="20">MP Auto Heal</option>
                                <option value="21">Add. Damage</option>
                                <option value="22">Skill Exp</option>
                                <option value="23">Alz Drop Amount</option>
                                <option value="24">2 slot item drop</option>
                                <option value="25">Resist Cri Rate</option>
                                <option value="26">Resist Cri Dmg</option>
                                <option value="27">Resist Cri Unmove</option>
                                <option value="28">Resist Cri Down</option>
                                <option value="29">Resist Cri Knock Back</option>
                                <option value="30">Resist Cri Stun</option>
                            </optgroup>
                        </select>
                        <span class="help-block text-red"><small>เวลาหมดอายุไอเท็ม</small></span>
                    </div>
                    </div>
                    <div class="col-md-12">
                        <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row">
                            <button type="submit" name="btn_savechange" class="btn btn-primary ml-auto waves-effect waves-themed">
                            <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง Option
                            </button>
                        </div>
                    </div>
                </form>                
			</div>

				</div>
			</div>
		</div>
	 
<!-- panel end -->
    </div>

	<div class="col-xl-6">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        Pets <span class="fw-300"><i>Table</i></span>
                                    </h2>
                                    <div class="panel-toolbar">
                                        <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                                        <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                                        <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                                    </div>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                       
                                        <ul class="nav nav-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active p-3" data-toggle="tab" href="#tab_default-1" role="tab">
                                                    <i class="fal fa-table text-success"></i>
                                                    <span class="hidden-sm-down ml-1">ตาราง PETS</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link p-3" data-toggle="tab" href="#tab_default-2" role="tab">
                                                    <i class="fal fa-cog text-info"></i>
                                                    <span class="hidden-sm-down ml-1">โหลดข้อมูล PETS ใหม่</span>
                                                </a>
                                            </li>
                                        </ul>

                                        <div class="tab-content pt-4">
                                            <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                                                <div class="row">
                                                    <div class="col-xl-12">
                                                        <table id="dt-petsdatatables" class="table table-bordered table-hover table-striped w-100"></table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tab-pane fade" id="tab_default-2" role="tabpanel">
                                                <div class="alert alert-info">
                                                 
                                                    <div class="panel">
                                                        <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                                                            <div class="form-group">
                                                                <button type="submit" name="btn_loadpetsdata_json" class="btn btn-primary btn-lg btn-block waves-effect waves-themed">
                                                                </span>ถ้าค้นหาข้อมูลไม่เจอ ให้กดปุ่มโหลดข้อมูลใหม่ (คลิก!!)
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                    


                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                       
<!--row end -->
</div>
