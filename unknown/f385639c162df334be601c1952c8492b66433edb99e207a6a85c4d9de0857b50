<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Donate History
        <small>รายการเติมเงิน Cash</small>
    </h1>
</div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>History <span class="fw-300"><i>รายการเติมเงิน</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-bordered table-hover table-striped w-100">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>Userid</th>
                                <th>Transactionid</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Added_time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $selectLastUsers      = "SELECT * FROM [dbo].WEB_Truewallet ORDER BY id DESC";
                            $selectLastUsersParam = array();
                            $selectLastUsersOpt   = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                            if (sqlsrv_num_rows($selectLastUsersQuery)) {
                            while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                            $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '" . $resLastUsers['UserNum'] . "'";
                            $selectauthParam = array();
                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                            ?>
                            <tr>
                                <td><?php echo $resLastUsers['id']; ?></td>
                                <td><?php echo $resLastUsers['Userid']; ?></td>
                                <td><?php echo $resLastUsers['Transactionid']; ?></td>
                                <td><?php if ($resLastUsers['Type'] == "Wallet"){
                                            echo '<span class="badge badge-primary">'.$resLastUsers['Type'].'</span>';
                                        }elseif ($resLastUsers['Type'] == "ibank"){
                                            echo '<span class="badge badge-info">'.$resLastUsers['Type'].'</span>';
                                        }
                                    ?>
                                </td>
                                <td><span class="badge badge-danger"><?php echo $resLastUsers['Amount']; ?></span></td>
                                <td><?php if ($resLastUsers['Status'] == "check_success"){
                                                echo '<span class="badge badge-success">ผ่าน</span>';
                                            }elseif ($resLastUsers['Status'] == "check_pass"){
                                                echo '<span class="badge badge-danger">ไม่ผ่าน</span>';
                                            }elseif ($resLastUsers['Status'] == "check_waiting"){
                                                echo '<span class="badge badge-warning">รอตรวจสอบจากผู้ดูแลระบบ</span>';
                                            }
                                        ?>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($resLastUsers['Added_time'])); ?></td>
                            </tr>
                            <?php
                        }
                        } else {
                        echo W_NOTHING_RETURNED;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>


</div>