<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
 */
ob_start();
session_start();

require('../_app/dbinfo.inc.php');
require('../_app/general_config.inc.php');
require('../_app/variables.php');

// sql inject protection
require('../_app/php/sql_inject.php');
require('../_app/php/sql_check.php');

// zpanel class
require('../_app/php/zpanel.class.php');
$zpanel = new zpanel();

 
?>
<!DOCTYPE html>
<html lang="<?php echo $_COOKIE['lang']; ?>">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title><?php echo $zpanel->getConfigByValue('title', 'value', $conn) . ' &middot; ' . $zpanel->getConfigByValue('description', 'value', $conn); ?></title>
        <meta name="description" content="<?php echo $zpanel->getConfigByValue('description', 'value', $conn); ?>">
        <meta name="author" content="FDEV">
    <!-- FAVICON -->
        <link rel="shortcut icon" href="../assets/images/icons/favicon.ico" type="image/x-icon" >
        <!-- core CSS -->
        <link rel="stylesheet" href="../assets/css/bootstrap/bootstrap.css" />
        <link rel="stylesheet" href="../assets/css/plugins/calendar/calendar.css" />
        <link rel="stylesheet" href="../assets/css/switch-buttons/switch-buttons.css" />
        <link rel="stylesheet" href="../assets/css/fontawesome/css/fontawesome-all.css" />
		
        <!-- Fonts  -->
       <link href="https://fonts.googleapis.com/css?family=Kanit:300" rel="stylesheet">


        <!-- Base Styling  -->

        <style type="text/css">
            body,td,th {
                font-family: Kanit, sans-serif;
            }
        </style>

        <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
          <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->

    </head>

    <body data-ng-app>
                   
					<?php if (isset($returnSuccess)) { ?>
                        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                    <?php } elseif (isset($returnWarning)) { ?>
                        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                    <?php } elseif (isset($returnError)) { ?>
                        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                    <?php } elseif (isset($returnWarningGLOBAL)) { ?>
                        <div class="alert alert-warning"><?php echo $returnWarningGLOBAL; ?></div>
                    <?php } ?>
					   <div class="col-lg-6">
                        <h2>POCYON<small> HorerWar</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th>ชื่อ</th>
                                    <th>Horner</th>
                                    <th>ออนไลน์</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].cabal_character_table Where Nation = 2 ORDER BY Reputation DESC";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $game->thaitrans(substr($resChars['Name'],0,20)); ?></td>
                                            <td > <a href="#" class="label label-primary text-red bg-defaul padd-sm"><?php echo number_format($resChars['Reputation'])?></a></td>
                                            <td><?php 
											if ($resChars['Login'] ==0){
												echo '<a href="#" class="label label-danger text-red bg-defaul padd-sm">OFFLINE</a>';
										}else{
												echo '<a href="#" class="label label-success text-red bg-defaul padd-sm">ON LINE</a>';
										}
									
										?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="#" class="btn btn-block btn-success" style="margin: 5px 0;">เพิ่มเติม</a>
                    </div>
                </div>
                <hr>
                <div class="row">
        <!-- JQuery v1.9.1 -->
        <script src="../assets/js/jquery/jquery-1.9.1.min.js" type="text/javascript"></script>
        <script src="../assets/js/plugins/underscore/underscore-min.js"></script>
        <script src="../assets/js/bootstrap/bootstrap.min.js"></script>
        <script src="../assets/js/globalize/globalize.min.js"></script>
        <script src="../assets/js/plugins/nicescroll/jquery.nicescroll.min.js"></script>
        <script src="../assets/js/plugins/sparkline/jquery.sparkline.min.js"></script>
        <!--<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.3.0-beta.14/angular.min.js"></script>-->
        <script src="../assets/js/angular/todo.js"></script>
        <script src="../assets/js/plugins/calendar/calendar.js"></script>
        <script src="../assets/js/plugins/calendar/calendar-conf.js"></script>
        <script src="../assets/js/plugins/inputmask/jquery.inputmask.bundle.js"></script>
        <!-- Wysihtml5 -->
        <script src="../assets/js/plugins/bootstrap-wysihtml5/wysihtml5-0.3.0.min.js"></script>
        <script src="../assets/js/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.js"></script>
        <!-- Custom JQuery -->
        <script src="../assets/js/app/custom.js" type="text/javascript"></script>
        <script src="../assets/js/app/zpanel.js" type="text/javascript"></script>
        <script src="../assets/js/plugins/smooth-anchor/jquery.anchor.js"></script>
		<!-- Paginationy -->
		 <script src="../assets/js/app/jquery.Pagination.js" ></script>

    </body>
</html>
