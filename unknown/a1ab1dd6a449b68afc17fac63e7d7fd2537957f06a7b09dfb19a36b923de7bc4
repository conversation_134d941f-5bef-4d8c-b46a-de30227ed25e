<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Drag Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .slot-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .slot {
            width: 93px;
            height: 93px;
            border: 1px solid #555;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
            background: #2e2e2e;
        }

        /* เพิ่ม CSS สำหรับรูปภาพที่เต็ม slot */
        .slot img {
            border-radius: 6px;
        }
        
        .slot[data-serial] {
            background: #222;
            cursor: grab !important;
        }
        
        .slot[data-serial]:active {
            cursor: grabbing !important;
        }
        
        .slot:not([data-serial]) {
            cursor: default !important;
        }
        
        .slot:hover {
            border-color: #007bff !important;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }
        
        .slot.drag-over {
            border-color: #28a745 !important;
            border-width: 3px !important;
            background-color: rgba(40, 167, 69, 0.1) !important;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.5) !important;
            transform: scale(1.05) !important;
        }
        
        .slot.dragging {
            opacity: 0.6 !important;
            transform: scale(0.95) !important;
            border-color: #007bff !important;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.5) !important;
        }
        
        .slot-number {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: 11px;
            color: #bbb;
            z-index: 2;
        }
        
        .item-name {
            background: rgba(0,0,0,0.7);
            color: #fff;
            padding: 2px 4px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 2px;
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            z-index: 3;
        }
        
        .debug {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Drag Test</h1>
        
        <div class="debug" id="debugInfo">
            Debug info will appear here...
        </div>
        
        <div class="slot-grid" id="slotGrid">
            <!-- Slots will be generated by JavaScript -->
        </div>
        
        <button onclick="resetTest()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            🔄 Reset Test
        </button>
    </div>

    <script>
        // Test data with ItemKind for images
        let testItems = [
            { SerialNum: '1', ItemName: 'Sword +15', SlotID: 0, PoolID: '1', ItemKind: 1 },
            { SerialNum: '2', ItemName: 'Shield +10', SlotID: 1, PoolID: '1', ItemKind: 2 },
            { SerialNum: '3', ItemName: 'Helmet +7', SlotID: 2, PoolID: '1', ItemKind: 3 }
        ];
        
        let dragData = null;
        
        function debugLog(message) {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debugDiv.scrollTop = debugDiv.scrollHeight;
            console.log(message);
        }
        
        function createSlots() {
            const grid = document.getElementById('slotGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 10; i++) {
                const item = testItems.find(item => parseInt(item.SlotID) === i);
                const slot = document.createElement('div');
                
                slot.className = 'slot';
                slot.dataset.slot = i;
                slot.dataset.pool = '1';
                
                if (item) {
                    slot.dataset.serial = item.SerialNum;
                    slot.draggable = true;
                } else {
                    slot.draggable = false;
                }
                
                // สร้างรูปไอเท็ม
                let itemImageHtml = '';
                if (item && item.ItemKind) {
                    itemImageHtml = `
                        <img src="assets/images/items/${item.ItemKind}.png"
                             alt="${item.ItemName}"
                             style="width:150%;height:150%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);object-fit:cover;object-position:center;pointer-events:none;z-index:1;border-radius:4px;opacity:0;transition:opacity 0.3s ease;"
                             onerror="this.onerror=null; this.src='assets/images/items/default.png';"
                             onload="this.style.opacity='1';">
                    `;
                }

                slot.innerHTML = `
                    ${itemImageHtml}
                    <div class="slot-number">${i}</div>
                    ${item ? `<div class="item-name">${item.ItemName}</div>` : '<div style="color: #666;">Empty</div>'}
                `;
                
                // Event listeners
                slot.addEventListener('dragstart', handleDragStart);
                slot.addEventListener('dragover', handleDragOver);
                slot.addEventListener('dragleave', handleDragLeave);
                slot.addEventListener('drop', handleDrop);
                slot.addEventListener('dragend', handleDragEnd);
                
                grid.appendChild(slot);
            }
            
            debugLog('Created 10 slots with ' + testItems.length + ' items');
        }
        
        function handleDragStart(e) {
            const slot = e.currentTarget;
            debugLog('dragStart: slot ' + slot.dataset.slot + ', serial: ' + slot.dataset.serial);
            
            if (!slot.dataset.serial) {
                e.preventDefault();
                return false;
            }
            
            slot.classList.add('dragging');
            
            dragData = {
                serial: slot.dataset.serial,
                slot: parseInt(slot.dataset.slot),
                pool: slot.dataset.pool
            };
            
            e.dataTransfer.effectAllowed = 'move';
            
            // สร้าง drag image ง่ายๆ
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 93;
            canvas.height = 93;
            
            ctx.fillStyle = '#222';
            ctx.fillRect(0, 0, 93, 93);
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, 93, 93);
            
            ctx.fillStyle = '#fff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Slot ' + slot.dataset.slot, 46, 46);
            
            e.dataTransfer.setDragImage(canvas, 46, 46);
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            
            const target = e.currentTarget;
            target.classList.add('drag-over');
        }
        
        function handleDragLeave(e) {
            const target = e.currentTarget;
            target.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            const target = e.currentTarget;
            target.classList.remove('drag-over');
            
            const targetSlot = parseInt(target.dataset.slot);
            
            debugLog('drop: target slot ' + targetSlot + ', drag data: ' + JSON.stringify(dragData));
            
            if (!dragData || dragData.slot === targetSlot) {
                debugLog('Invalid drop - same slot or no drag data');
                return;
            }
            
            const itemA = testItems.find(item => item.SerialNum == dragData.serial);
            const itemB = testItems.find(item => parseInt(item.SlotID) === targetSlot);
            
            debugLog('Items: A=' + (itemA ? itemA.ItemName : 'null') + ', B=' + (itemB ? itemB.ItemName : 'null'));
            
            if (!itemA) {
                debugLog('Error: Item A not found');
                return;
            }
            
            if (itemB) {
                // Swap items
                const confirmSwap = confirm(
                    `สลับตำแหน่งไอเท็ม?\n\n` +
                    `"${itemA.ItemName}" (Slot ${dragData.slot}) ↔ "${itemB.ItemName}" (Slot ${targetSlot})`
                );
                
                if (!confirmSwap) {
                    debugLog('Swap cancelled by user');
                    return;
                }
                
                const tempSlot = itemA.SlotID;
                itemA.SlotID = itemB.SlotID;
                itemB.SlotID = tempSlot;
                
                debugLog('Swapped: ' + itemA.ItemName + ' ↔ ' + itemB.ItemName);
            } else {
                // Move item to empty slot
                itemA.SlotID = targetSlot;
                debugLog('Moved: ' + itemA.ItemName + ' to slot ' + targetSlot);
            }
            
            createSlots(); // Refresh display
        }
        
        function handleDragEnd(e) {
            document.querySelectorAll('.slot').forEach(s => {
                s.classList.remove('dragging', 'drag-over');
            });
            dragData = null;
            debugLog('dragEnd');
        }
        
        function resetTest() {
            testItems = [
                { SerialNum: '1', ItemName: 'Sword +15', SlotID: 0, PoolID: '1', ItemKind: 1 },
                { SerialNum: '2', ItemName: 'Shield +10', SlotID: 1, PoolID: '1', ItemKind: 2 },
                { SerialNum: '3', ItemName: 'Helmet +7', SlotID: 2, PoolID: '1', ItemKind: 3 }
            ];
            createSlots();
            document.getElementById('debugInfo').innerHTML = '';
            debugLog('Test reset');
        }
        
        // Initialize
        createSlots();
        debugLog('Simple drag test initialized');
    </script>
</body>
</html>
