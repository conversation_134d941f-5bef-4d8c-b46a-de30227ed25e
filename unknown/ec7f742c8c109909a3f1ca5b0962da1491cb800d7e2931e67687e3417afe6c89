<?php
// เปิดแสดงข้อผิดพลาดเพื่อ Debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

$status_query = "SELECT COUNT(*) AS cnt FROM WEB_Truewallet WHERE Status = 2";
$result = sqlsrv_query($conn, $status_query);

$count = 0;
if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
    $count = $row['cnt'];
}

echo json_encode(['countdonate_notification' => $count]);
?>