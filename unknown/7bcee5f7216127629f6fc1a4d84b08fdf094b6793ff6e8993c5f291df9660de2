<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?> <small><?php echo PT_MANAGEPLAYERS_DESC; ?></small></h1></div>
<script type="text/javascript" charset="utf8" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-2.0.3.js"></script>
<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=manager/mailReceiver" class="btn btn-block btn-info">กลับไปยังหน้า การส่งเมลล์</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 10;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_mail_received_table WHERE ReceiverCharIdx LIKE '$getResult%' OR SenderCharIdx LIKE '$getResult%'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Player not found</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                            <th>ผู้รับ</th>
                            <th>Type</th>
                            <th>State</th>
                            <th>DeliveryTime</th>
                            <th>รับAlz</th>
                            <th>Alz</th>
                            <th>รับItem</th>
                            <th>รหัสไอเท็ม</th>
                            <th>ออฟชั่น</th>
                            <th>อายุ</th>
                            <th>ผู้ส่ง</th>
                            <th>ชื่อผู้ส่ง</th>
                            <th>หัวข้อ</th>
                            <th>ข้อความ</th>
                            <th><?php echo T_ACTION; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr
                                    <?php
                                    $selectUsersData = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_mail_received_table WHERE ReceiverCharIdx = '$row[0]'";
                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, array());
                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                                    if($selectUsersDataFetch['AuthType'] == '2' ||
                                            $selectUsersDataFetch['AuthType'] == '3'){ echo ' class="bg-red text-white"'; }
                                ?>>
                               <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[3]; ?></td>
                                <td><span class="label
                                <?php echo $label = ($row[4] == '0' ? ' label-success' : ($row[4] == '0' ? ' label-default' : 'label-danger'));?>">
                                <?php echo $status = ($row[4] == '0' ? 'Available' : ($row[4] == '1' ? 'Used' : 'Unknow status'));?></span></td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
                                <td><span class="label
                                <?php echo $label = ($row[7] == '0' ? ' label-success' : ($row[7] == '0' ? ' label-default' : 'label-danger'));?>">
                                <?php echo $status = ($row[7] == '0' ? 'Available' : ($row[7] == '1' ? 'Used' : 'Unknow status'));?></span></td>
                                <td><?php echo number_format($row[8]); ?></td>
                                <td><span class="label
                                <?php echo $label = ($row[9] == '0' ? ' label-success' : ($row[9] == '0' ? ' label-default' : 'label-danger'));?>">
                                <?php echo $status = ($row[9] == '0' ? 'Available' : ($row[9] == '1' ? 'Used' : 'Unknow status'));?></span></td>
                                <td><?php echo $row[10]; ?></td>
                                <td><?php echo $row[11]; ?></td>
                                <td><?php echo $row[12]; ?></td>
                                <td><?php echo $row[14]; ?></td>
                                <td><?php echo $userLogin->thaitrans($row[15]); ?></td>
                                <td><?php echo $userLogin->thaitrans($row[16]); ?></td>
                                <td><?php echo $userLogin->thaitrans($row[17]); ?></td>
                            
                                        <td>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                                    <?php echo B_ACTION; ?> <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" role="menu">
												<?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                                <li><a href="?url=manager/see-player&id=<?php echo $row[0]; ?>"><?php echo B_SEEPLRINFO; ?></a></li>
                                                <li><a href="?url=manager/edit-player&id=<?php echo $row[0]; ?>">Edit</a></li>
												<?php } ?>
												<?php if ($selectUsersDataFetch['AuthType'] == '2' || $selectUsersDataFetch['AuthType'] == '3' || $selectUsersDataFetch['AuthType'] == '4') { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=unban-wait"><span class="text-white"><?php echo B_UNBAN;		?></span></a></li>
												<?php } else { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=wait"><span class="text-white"><?php echo B_BAN; ?></span></a></li>
												 <?php } ?>
                                                </ul>
                                            </div>
                                        </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/tickets&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript">
$(document).ready(function(){
$('.pagination').pagination({
        items: <?php echo $rowsReturned;?>,
        itemsOnPage: <?php echo $rowsPerPage;?>,
        cssStyle: 'light-theme',
		currentPage : <?php echo $pageNum;?>,
		hrefTextPrefix : '?url=manager/mail-presults&pageNum='
    });
	});
</script>