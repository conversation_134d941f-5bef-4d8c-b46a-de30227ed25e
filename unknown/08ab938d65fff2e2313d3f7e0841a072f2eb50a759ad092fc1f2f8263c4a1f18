/*!
 * 
 * Super simple wysiwyg editor v0.8.16
 * https://summernote.org
 * 
 * 
 * Copyright 2013- <PERSON> and other contributors
 * summernote may be freely distributed under the MIT license.
 * 
 * Date: 2020-02-19T09:12Z
 * 
 */
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900");
@font-face {
  font-family: "summernote";
  font-style: normal;
  font-weight: 400;
  font-display: auto;
  src: url("webfonts/summernote.eot");
  src: url("webfonts/summernote.eot?#iefix") format("embedded-opentype"), url("webfonts/summernote.woff2") format("woff2"), url("webfonts/summernote.woff") format("woff"), url("webfonts/summernote.ttf") format("truetype"); }

[class^="note-icon"]:before,
[class*=" note-icon"]:before {
  display: inline-block;
  font-family: summernote;
  font-style: normal;
  font-size: inherit;
  text-decoration: inherit;
  text-rendering: auto;
  text-transform: none;
  vertical-align: middle;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  speak: none; }

.note-icon-fw {
  text-align: center;
  width: 1.25em; }

.note-icon-border {
  border: solid 0.08em #eee;
  border-radius: 0.1em;
  padding: 0.2em 0.25em 0.15em; }

.note-icon-pull-left {
  float: left; }

.note-icon-pull-right {
  float: right; }

.note-icon.note-icon-pull-left {
  margin-right: 0.3em; }

.note-icon.note-icon-pull-right {
  margin-left: 0.3em; }

.note-icon-align::before {
  content: "\ea01"; }

.note-icon-align-center::before {
  content: "\ea02"; }

.note-icon-align-indent::before {
  content: "\ea03"; }

.note-icon-align-justify::before {
  content: "\ea04"; }

.note-icon-align-left::before {
  content: "\ea05"; }

.note-icon-align-outdent::before {
  content: "\ea06"; }

.note-icon-align-right::before {
  content: "\ea07"; }

.note-icon-arrow-circle-down::before {
  content: "\ea08"; }

.note-icon-arrow-circle-left::before {
  content: "\ea09"; }

.note-icon-arrow-circle-right::before {
  content: "\ea0a"; }

.note-icon-arrow-circle-up::before {
  content: "\ea0b"; }

.note-icon-arrows-alt::before {
  content: "\ea0c"; }

.note-icon-arrows-h::before {
  content: "\ea0d"; }

.note-icon-arrows-v::before {
  content: "\ea0e"; }

.note-icon-bold::before {
  content: "\ea0f"; }

.note-icon-caret::before {
  content: "\ea10"; }

.note-icon-chain-broken::before {
  content: "\ea11"; }

.note-icon-circle::before {
  content: "\ea12"; }

.note-icon-close::before {
  content: "\ea13"; }

.note-icon-code::before {
  content: "\ea14"; }

.note-icon-col-after::before {
  content: "\ea15"; }

.note-icon-col-before::before {
  content: "\ea16"; }

.note-icon-col-remove::before {
  content: "\ea17"; }

.note-icon-eraser::before {
  content: "\ea18"; }

.note-icon-float-left::before {
  content: "\ea19"; }

.note-icon-float-none::before {
  content: "\ea1a"; }

.note-icon-float-right::before {
  content: "\ea1b"; }

.note-icon-font::before {
  content: "\ea1c"; }

.note-icon-frame::before {
  content: "\ea1d"; }

.note-icon-italic::before {
  content: "\ea1e"; }

.note-icon-link::before {
  content: "\ea1f"; }

.note-icon-magic::before {
  content: "\ea20"; }

.note-icon-menu-check::before {
  content: "\ea21"; }

.note-icon-minus::before {
  content: "\ea22"; }

.note-icon-orderedlist::before {
  content: "\ea23"; }

.note-icon-pencil::before {
  content: "\ea24"; }

.note-icon-picture::before {
  content: "\ea25"; }

.note-icon-question::before {
  content: "\ea26"; }

.note-icon-redo::before {
  content: "\ea27"; }

.note-icon-rollback::before {
  content: "\ea28"; }

.note-icon-row-above::before {
  content: "\ea29"; }

.note-icon-row-below::before {
  content: "\ea2a"; }

.note-icon-row-remove::before {
  content: "\ea2b"; }

.note-icon-special-character::before {
  content: "\ea2c"; }

.note-icon-square::before {
  content: "\ea2d"; }

.note-icon-strikethrough::before {
  content: "\ea2e"; }

.note-icon-subscript::before {
  content: "\ea2f"; }

.note-icon-summernote::before {
  content: "\ea30"; }

.note-icon-superscript::before {
  content: "\ea31"; }

.note-icon-table::before {
  content: "\ea32"; }

.note-icon-text-height::before {
  content: "\ea33"; }

.note-icon-trash::before {
  content: "\ea34"; }

.note-icon-underline::before {
  content: "\ea35"; }

.note-icon-undo::before {
  content: "\ea36"; }

.note-icon-unorderedlist::before {
  content: "\ea37"; }

.note-icon-video::before {
  content: "\ea38"; }

.note-editor {
  position: relative; }

.note-editor .note-dropzone {
  position: absolute;
  display: none;
  z-index: 100;
  color: #87cefa;
  background-color: #fff;
  opacity: .95; }

.note-editor .note-dropzone .note-dropzone-message {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  font-size: 28px;
  font-weight: 700; }

.note-editor .note-dropzone.hover {
  color: #098ddf; }

.note-editor.dragover .note-dropzone {
  display: table; }

.note-editor .note-editing-area {
  position: relative; }

.note-editor .note-editing-area .note-editable {
  outline: none; }

.note-editor .note-editing-area .note-editable sup {
  vertical-align: super; }

.note-editor .note-editing-area .note-editable sub {
  vertical-align: sub; }

.note-editor .note-editing-area .note-editable img.note-float-left {
  margin-right: 10px; }

.note-editor .note-editing-area .note-editable img.note-float-right {
  margin-left: 10px; }

.note-editor.note-frame, .note-editor.note-airframe {
  border: 1px solid #a9a9a9; }

.note-editor.note-frame.codeview .note-editing-area .note-editable, .note-editor.note-airframe.codeview .note-editing-area .note-editable {
  display: none; }

.note-editor.note-frame.codeview .note-editing-area .note-codable, .note-editor.note-airframe.codeview .note-editing-area .note-codable {
  display: block; }

.note-editor.note-frame .note-editing-area, .note-editor.note-airframe .note-editing-area {
  overflow: hidden; }

.note-editor.note-frame .note-editing-area .note-editable, .note-editor.note-airframe .note-editing-area .note-editable {
  background-color: #fff;
  color: #000;
  padding: 10px;
  overflow: auto;
  word-wrap: break-word; }

.note-editor.note-frame .note-editing-area .note-editable[contenteditable=false], .note-editor.note-airframe .note-editing-area .note-editable[contenteditable=false] {
  background-color: #e5e5e5; }

.note-editor.note-frame .note-editing-area .note-codable, .note-editor.note-airframe .note-editing-area .note-codable {
  display: none;
  width: 100%;
  padding: 10px;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-family: Menlo,Monaco,monospace,sans-serif;
  font-size: 14px;
  color: #ccc;
  background-color: #222;
  resize: none;
  outline: none;
  -ms-box-sizing: border-box;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 0;
  margin-bottom: 0; }

.note-editor.note-frame.fullscreen, .note-editor.note-airframe.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100% !important;
  z-index: 1050; }

.note-editor.note-frame.fullscreen .note-editable, .note-editor.note-airframe.fullscreen .note-editable {
  background-color: #fff; }

.note-editor.note-frame.fullscreen .note-resizebar, .note-editor.note-airframe.fullscreen .note-resizebar {
  display: none; }

.note-editor.note-frame .note-status-output, .note-editor.note-airframe .note-status-output {
  display: block;
  width: 100%;
  font-size: 14px;
  line-height: 1.42857143;
  height: 20px;
  margin-bottom: 0;
  color: #000;
  border: 0;
  border-top: 1px solid #e2e2e2; }

.note-editor.note-frame .note-status-output:empty, .note-editor.note-airframe .note-status-output:empty {
  height: 0;
  border-top: 0 solid transparent; }

.note-editor.note-frame .note-status-output .pull-right, .note-editor.note-airframe .note-status-output .pull-right {
  float: right !important; }

.note-editor.note-frame .note-status-output .text-muted, .note-editor.note-airframe .note-status-output .text-muted {
  color: #777; }

.note-editor.note-frame .note-status-output .text-primary, .note-editor.note-airframe .note-status-output .text-primary {
  color: #286090; }

.note-editor.note-frame .note-status-output .text-success, .note-editor.note-airframe .note-status-output .text-success {
  color: #3c763d; }

.note-editor.note-frame .note-status-output .text-info, .note-editor.note-airframe .note-status-output .text-info {
  color: #31708f; }

.note-editor.note-frame .note-status-output .text-warning, .note-editor.note-airframe .note-status-output .text-warning {
  color: #8a6d3b; }

.note-editor.note-frame .note-status-output .text-danger, .note-editor.note-airframe .note-status-output .text-danger {
  color: #a94442; }

.note-editor.note-frame .note-status-output .alert, .note-editor.note-airframe .note-status-output .alert {
  margin: -7px 0 0 0;
  padding: 7px 10px 2px 10px;
  border-radius: 0;
  color: #000;
  background-color: #f5f5f5; }

.note-editor.note-frame .note-status-output .alert .note-icon, .note-editor.note-airframe .note-status-output .alert .note-icon {
  margin-right: 5px; }

.note-editor.note-frame .note-status-output .alert-success, .note-editor.note-airframe .note-status-output .alert-success {
  color: #3c763d !important;
  background-color: #dff0d8 !important; }

.note-editor.note-frame .note-status-output .alert-info, .note-editor.note-airframe .note-status-output .alert-info {
  color: #31708f !important;
  background-color: #d9edf7 !important; }

.note-editor.note-frame .note-status-output .alert-warning, .note-editor.note-airframe .note-status-output .alert-warning {
  color: #8a6d3b !important;
  background-color: #fcf8e3 !important; }

.note-editor.note-frame .note-status-output .alert-danger, .note-editor.note-airframe .note-status-output .alert-danger {
  color: #a94442 !important;
  background-color: #f2dede !important; }

.note-editor.note-frame .note-statusbar, .note-editor.note-airframe .note-statusbar {
  background-color: #f5f5f5;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-top: 1px solid #ddd; }

.note-editor.note-frame .note-statusbar .note-resizebar, .note-editor.note-airframe .note-statusbar .note-resizebar {
  padding-top: 1px;
  height: 9px;
  width: 100%;
  cursor: ns-resize; }

.note-editor.note-frame .note-statusbar .note-resizebar .note-icon-bar, .note-editor.note-airframe .note-statusbar .note-resizebar .note-icon-bar {
  width: 20px;
  margin: 1px auto;
  border-top: 1px solid #a9a9a9; }

.note-editor.note-frame .note-statusbar.locked .note-resizebar, .note-editor.note-airframe .note-statusbar.locked .note-resizebar {
  cursor: default; }

.note-editor.note-frame .note-statusbar.locked .note-resizebar .note-icon-bar, .note-editor.note-airframe .note-statusbar.locked .note-resizebar .note-icon-bar {
  display: none; }

.note-editor.note-frame .note-placeholder, .note-editor.note-airframe .note-placeholder {
  padding: 10px; }

.note-editor.note-airframe {
  border: 0; }

.note-editor.note-airframe .note-editing-area .note-editable {
  padding: 0; }

.note-popover.popover {
  display: none;
  max-width: none; }

.note-popover.popover .popover-content a {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle; }

.note-popover.popover .arrow {
  left: 20px !important; }

.note-toolbar {
  position: relative; }

.note-popover .popover-content, .note-toolbar {
  margin: 0;
  padding: 0 0 5px 5px; }

.note-popover .popover-content > .note-btn-group, .note-toolbar > .note-btn-group {
  margin-top: 5px;
  margin-left: 0;
  margin-right: 5px; }

.note-popover .popover-content .note-btn-group .note-table, .note-toolbar .note-btn-group .note-table {
  min-width: 0;
  padding: 5px; }

.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker, .note-toolbar .note-btn-group .note-table .note-dimension-picker {
  font-size: 18px; }

.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher, .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher {
  position: absolute !important;
  z-index: 3;
  width: 10em;
  height: 10em;
  cursor: pointer; }

.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted, .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted {
  position: relative !important;
  z-index: 1;
  width: 5em;
  height: 5em;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC") repeat; }

.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted, .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted {
  position: absolute !important;
  z-index: 2;
  width: 1em;
  height: 1em;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC") repeat; }

.note-popover .popover-content .note-style .dropdown-style blockquote, .note-popover .popover-content .note-style .dropdown-style pre, .note-toolbar .note-style .dropdown-style blockquote, .note-toolbar .note-style .dropdown-style pre {
  margin: 0;
  padding: 5px 10px; }

.note-popover .popover-content .note-style .dropdown-style h1, .note-popover .popover-content .note-style .dropdown-style h2, .note-popover .popover-content .note-style .dropdown-style h3, .note-popover .popover-content .note-style .dropdown-style h4, .note-popover .popover-content .note-style .dropdown-style h5, .note-popover .popover-content .note-style .dropdown-style h6, .note-popover .popover-content .note-style .dropdown-style p, .note-toolbar .note-style .dropdown-style h1, .note-toolbar .note-style .dropdown-style h2, .note-toolbar .note-style .dropdown-style h3, .note-toolbar .note-style .dropdown-style h4, .note-toolbar .note-style .dropdown-style h5, .note-toolbar .note-style .dropdown-style h6, .note-toolbar .note-style .dropdown-style p {
  margin: 0;
  padding: 0; }

.note-popover .popover-content .note-color-all .note-dropdown-menu, .note-toolbar .note-color-all .note-dropdown-menu {
  min-width: 337px; }

.note-popover .popover-content .note-color .dropdown-toggle, .note-toolbar .note-color .dropdown-toggle {
  width: 20px;
  padding-left: 5px; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette, .note-toolbar .note-color .note-dropdown-menu .note-palette {
  display: inline-block;
  margin: 0;
  width: 160px; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette:first-child, .note-toolbar .note-color .note-dropdown-menu .note-palette:first-child {
  margin: 0 5px; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-palette-title, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-palette-title {
  font-size: 12px;
  margin: 2px 7px;
  text-align: center;
  border-bottom: 1px solid #eee; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset, .note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select {
  font-size: 11px;
  margin: 3px;
  padding: 0 3px;
  cursor: pointer;
  width: 100%;
  border-radius: 5px; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset:hover, .note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select:hover, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset:hover, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select:hover {
  background: #eee; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-row, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-row {
  height: 20px; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select-btn, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select-btn {
  display: none; }

.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn, .note-toolbar .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn {
  border: 1px solid #eee; }

.note-popover .popover-content .note-para .note-dropdown-menu, .note-toolbar .note-para .note-dropdown-menu {
  min-width: 216px;
  padding: 5px; }

.note-popover .popover-content .note-para .note-dropdown-menu > div:first-child, .note-toolbar .note-para .note-dropdown-menu > div:first-child {
  margin-right: 5px; }

.note-popover .popover-content .note-dropdown-menu, .note-toolbar .note-dropdown-menu {
  min-width: 160px; }

.note-popover .popover-content .note-dropdown-menu.right, .note-toolbar .note-dropdown-menu.right {
  right: 0;
  left: auto; }

.note-popover .popover-content .note-dropdown-menu.right::before, .note-toolbar .note-dropdown-menu.right::before {
  right: 9px;
  left: auto !important; }

.note-popover .popover-content .note-dropdown-menu.right::after, .note-toolbar .note-dropdown-menu.right::after {
  right: 10px;
  left: auto !important; }

.note-popover .popover-content .note-dropdown-menu.note-check a i, .note-toolbar .note-dropdown-menu.note-check a i {
  color: #00bfff;
  visibility: hidden; }

.note-popover .popover-content .note-dropdown-menu.note-check a.checked i, .note-toolbar .note-dropdown-menu.note-check a.checked i {
  visibility: visible; }

.note-popover .popover-content .note-fontsize-10, .note-toolbar .note-fontsize-10 {
  font-size: 10px; }

.note-popover .popover-content .note-color-palette, .note-toolbar .note-color-palette {
  line-height: 1; }

.note-popover .popover-content .note-color-palette div .note-color-btn, .note-toolbar .note-color-palette div .note-color-btn {
  width: 20px;
  height: 20px;
  padding: 0;
  margin: 0;
  border: 1px solid #fff; }

.note-popover .popover-content .note-color-palette div .note-color-btn:hover, .note-toolbar .note-color-palette div .note-color-btn:hover {
  border: 1px solid #000; }

.note-modal .modal-dialog {
  outline: 0;
  border-radius: 5px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
          box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5); }

.note-modal .form-group {
  margin-left: 0;
  margin-right: 0; }

.note-modal .note-modal-form {
  margin: 0; }

.note-modal .note-image-dialog .note-dropzone {
  min-height: 100px;
  font-size: 30px;
  line-height: 4;
  color: #d3d3d3;
  text-align: center;
  border: 4px dashed #d3d3d3;
  margin-bottom: 10px; }

@-moz-document url-prefix() {
  .note-modal .note-image-input {
    height: auto; } }

.note-placeholder {
  position: absolute;
  display: none;
  color: gray; }

.note-handle .note-control-selection {
  position: absolute;
  display: none;
  border: 1px solid #000; }

.note-handle .note-control-selection > div {
  position: absolute; }

.note-handle .note-control-selection .note-control-selection-bg {
  width: 100%;
  height: 100%;
  background-color: #000;
  -webkit-opacity: .3;
  -khtml-opacity: .3;
  -moz-opacity: .3;
  opacity: .3;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=30);
  filter: alpha(opacity=30); }

.note-handle .note-control-selection .note-control-handle, .note-handle .note-control-selection .note-control-sizing, .note-handle .note-control-selection .note-control-holder {
  width: 7px;
  height: 7px;
  border: 1px solid #000; }

.note-handle .note-control-selection .note-control-sizing {
  background-color: #000; }

.note-handle .note-control-selection .note-control-nw {
  top: -5px;
  left: -5px;
  border-right: none;
  border-bottom: none; }

.note-handle .note-control-selection .note-control-ne {
  top: -5px;
  right: -5px;
  border-bottom: none;
  border-left: none; }

.note-handle .note-control-selection .note-control-sw {
  bottom: -5px;
  left: -5px;
  border-top: none;
  border-right: none; }

.note-handle .note-control-selection .note-control-se {
  right: -5px;
  bottom: -5px;
  cursor: se-resize; }

.note-handle .note-control-selection .note-control-se.note-control-holder {
  cursor: default;
  border-top: none;
  border-left: none; }

.note-handle .note-control-selection .note-control-selection-info {
  right: 0;
  bottom: 0;
  padding: 5px;
  margin: 5px;
  color: #fff;
  background-color: #000;
  font-size: 12px;
  border-radius: 5px;
  -webkit-opacity: .7;
  -khtml-opacity: .7;
  -moz-opacity: .7;
  opacity: .7;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=70);
  filter: alpha(opacity=70); }

.note-hint-popover {
  min-width: 100px;
  padding: 2px; }

.note-hint-popover .popover-content {
  padding: 3px;
  max-height: 150px;
  overflow: auto; }

.note-hint-popover .popover-content .note-hint-group .note-hint-item {
  display: block !important;
  padding: 3px; }

.note-hint-popover .popover-content .note-hint-group .note-hint-item.active, .note-hint-popover .popover-content .note-hint-group .note-hint-item:hover {
  display: block;
  clear: both;
  font-weight: 400;
  line-height: 1.4;
  color: #fff;
  white-space: nowrap;
  text-decoration: none;
  background-color: #428bca;
  outline: 0;
  cursor: pointer; }

.note-toolbar .note-btn {
  background: #fff;
  border-color: #dae0e5;
  padding: .28rem .65rem;
  font-size: 13px; }

/* #BOOTSTRAP AND MIXINS - Base Unmodified Bootstrap file with theme mixins
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by Dmitry Fadeyev (http://fadeyev.net)
    SASS port by Samuel Beek (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

  $color-white: hexToRGBString(#fff) => "255,255,255"
  $color-white: hexToRGBString(rgb(255,255,255)) => "255,255,255"
  $color-white: hexToRGBString(rgba(#fff,1)) => "255,255,255"
  
------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: contrast-ink($contrastvalue)
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/* #BASE - Base Variable file along with font library, and colors.
========================================================================== */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* We will manually convert these primary colors to rgb for the dark mode option of the theme */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* custom file */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav footer */
/* nav parent level-0 */
/* nav link level-1 */
/* nav level-1 bg */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
/* SHORTCUT BUTTON (appears on bottom right of the page) */
/* GULP WARNINGS */
body {
  font-family: "Roboto", "Helvetica Neue", Helvetica, Arial;
  font-size: 0.8125rem;
  letter-spacing: 0.1px; }

.page-content {
  color: #666666; }

h1, h2, h3, h4, h5, h6 {
  line-height: 1.3;
  font-weight: 400; }

strong {
  font-weight: 500; }

h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small {
  font-weight: 300;
  display: block;
  font-size: 0.9375rem;
  line-height: 1.5;
  margin: 2px 0 1.5rem; }

h2 small,
h3 small,
.h2 small,
.h3 small {
  font-size: 0.9375rem; }

h4 small,
.h4 small {
  font-size: 0.875rem; }

h5 small,
h6 small,
.h5 small,
.h6 small {
  font-size: 0.8125rem; }

/* contrast text */
.text-contrast {
  color: #333333; }

/* text-gradient */
.text-gradient {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(25%, #6e4e9e), color-stop(50%, #62468d), color-stop(75%, #0c7cd5), to(#0960a5));
  background: linear-gradient(180deg, #6e4e9e 25%, #62468d 50%, #0c7cd5 75%, #0960a5 100%);
  color: #886ab5;
  background-clip: text;
  text-fill-color: transparent;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none; }

/* looking for font size? Check _helpers.scss */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
		width: 100%;
		background-position: center center;
		background-size: cover;
		background-repeat: no-repeat;
}

.image-one {
		@extend %bg-image;
		background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
		width: 100%;
		background-position: center center;
		background-size: cover;
		background-repeat: no-repeat;
}

*/
/*
%shadow-hover {
	box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
	transition: all 0.2s ease-in-out;

	&:hover {
		box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
	}
}
*/
/*%fixed-header-shadow {
	@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));
}*/
/*  %selected-dot {
		&:before {
			content: " ";
			display: block;
			border-radius: 50%;
			background: inherit;
			background-image: none;
			border: 2px solid rgba(0,0,0,0.2);
			position: absolute;
			top: 15px;
			left: 15px;
			height: 20px;
			width: 20px;
		}
		&:after {
			content: " ";
			height: inherit;
			width: inherit;
			border: 5px solid rgba(0,0,0,0.1);
			position: absolute;
			left: 0;
			top: 0;
			border-radius: 50%;
		} 
	}*/
/* patterns */
@font-face {
  font-family: "summernote";
  font-style: normal;
  font-weight: normal;
  src: url("../../../webfonts/summernote.eot");
  src: url("../../../webfonts/summernote.eot?#iefix") format("embedded-opentype"), url("../../../webfonts/summernote.woff2") format("woff2"), url("../../../webfonts/summernote.woff") format("woff"), url("../../../webfonts/summernote.ttf") format("truetype"); }

.note-editor.note-frame {
  border-color: rgba(0, 0, 0, 0.08); }

.note-para .note-btn-group .dropdown-menu.show {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.card-header.note-toolbar > .btn-group {
  margin-top: 0.5rem;
  margin-right: 0.5rem; }

.card-header.note-toolbar {
  padding: 0 0 0.5rem 0.5rem; }

.note-btn.btn-light {
  color: #7b7b7b; }

.note-popover .popover-content {
  padding: 0.3rem;
  border-radius: 5px; }

.note-hint-popover .popover-content .note-hint-group .note-hint-item.active, .note-hint-popover .popover-content .note-hint-group .note-hint-item:hover {
  background-color: #2196F3; }

.note-hint-popover .popover-content .note-hint-group .note-hint-item {
  display: block !important;
  padding: 5px;
  border-radius: 4px;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  margin: 3px 0; }

.note-btn-group .dropdown-item {
  padding: 0.5rem 0.5rem; }

.note-popover .popover-content > .btn-group {
  margin-top: 0; }

.note-popover .note-btn-group:last-child {
  margin-right: 0; }

.note-editor.note-frame .note-editing-area {
  border-radius: 4px; }

/*# sourceMappingURL=summernote.css.map */
