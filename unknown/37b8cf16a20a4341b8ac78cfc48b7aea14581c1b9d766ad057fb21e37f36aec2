<?php $zpanel->checkSession(true); ?>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-user-tag"></i> ระบบสร้าง Redeem Code สำหรับตัวละคร</h5>
    </div>
    <div class="card-body">
        <!-- ฟอร์มสร้าง Code -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">สร้าง Character Redeem Code ใหม่</h6>
                    </div>
                    <div class="card-body">
                        <form id="generateCharacterCodeForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="characterStatus">สถานะตัวละคร:</label>
                                        <select id="characterStatus" class="form-control" required>
                                            <option value="">-- เลือกสถานะ --</option>
                                            <option value="0">Offline เท่านั้น</option>
                                            <option value="1">Online เท่านั้น</option>
                                            <option value="all">ทั้งหมด (Online + Offline)</option>
                                        </select>
                                        <small class="text-muted">ระบบจะสร้าง code ให้ตัวละครทั้งหมดที่ตรงสถานะที่เลือก</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="quantity">Quantity (จำนวนไอเท็มต่อ Code):</label>
                                        <input type="number" id="quantity" class="form-control" min="1" value="1" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="channelIdx">Channel ที่กำหนด:</label>
                                        <select id="channelIdx" class="form-control">
                                            <option value="">-- ทุก Channel --</option>
                                            <option value="1">Channel 1</option>
                                            <option value="2">Channel 2</option>
                                            <option value="3">Channel 3</option>
                                            <option value="4">Channel 4</option>
                                            <option value="6">Channel 6</option>
                                            <option value="9">Channel 9</option>
                                            <option value="15">Channel 15</option>
                                            <option value="16">Channel 16</option>
                                            <option value="17">Channel 17</option>
                                            <option value="18">Channel 18</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="codePrefix">รูปแบบ Code:</label>
                                        <select id="codePrefix" class="form-control">
                                            <option value="XXX-XXX-XXXX-XXX">XXX-XXX-XXXX-XXX</option>
                                            <option value="XXXX-XXXX-XXXX-XXXX">XXXX-XXXX-XXXX-XXXX</option>
                                            <option value="XXXXXXXX-XXXX-XXXX">XXXXXXXX-XXXX-XXXX</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="items">รายการไอเท็ม (รูปแบบ: itemid:option:duration):</label>
                                <textarea id="items" class="form-control" rows="5" placeholder="ตัวอย่าง:&#10;6685:0:0,6685:0:0,6685:0:0,&#10;6500:20:0,&#10;33561487:5:0&#10;&#10;(ระบบจะตัด newline ออกและต่อข้อมูลกัน)" required></textarea>
                                <small class="text-muted">แยกแต่ละไอเท็มด้วยเครื่องหมาย , (comma) - สามารถขึ้นบรรทัดใหม่ได้ (ระบบจะต่อข้อมูลให้)</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="expiryDate">วันหมดอายุ (ไม่บังคับ):</label>
                                        <input type="datetime-local" id="expiryDate" class="form-control">
                                        <small class="text-muted">หากไม่กำหนด Code จะไม่หมดอายุ</small>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-magic"></i> สร้าง Character Redeem Code
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">คำอธิบายระบบ</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>การทำงานของระบบ:</strong></p>
                        <ul class="small">
                            <li>ระบบจะตรวจสอบตัวละครใน SERVER01</li>
                            <li>สร้าง Code ให้ตัวละครทั้งหมดที่ตรงเงื่อนไข</li>
                            <li>แต่ละ Code จะผูกกับตัวละครเฉพาะ (1 ตัวละคร = 1 Code)</li>
                            <li>จำนวน Code = จำนวนตัวละครที่พบ</li>
                        </ul>

                        <p><strong>สถานะตัวละคร:</strong></p>
                        <ul class="small">
                            <li><code>0</code> = Offline เท่านั้น</li>
                            <li><code>1</code> = Online เท่านั้น</li>
                            <li><code>all</code> = ทั้งหมด</li>
                        </ul>

                        <p><strong>สถานะการใช้งาน Code:</strong></p>
                        <ul class="small">
                            <li><code>0</code> = ยังไม่ใช้งาน (เริ่มต้น)</li>
                            <li><code>1</code> = ใช้งานแล้ว</li>
                        </ul>

                        <p><strong>Channel:</strong></p>
                        <ul class="small">
                            <li>กำหนด Channel ที่ตัวละครต้องอยู่</li>
                            <li>ว่างเปล่า = ทุก Channel</li>
                        </ul>

                        <p><strong>รูปแบบไอเท็ม:</strong></p>
                        <code>itemid:option:duration</code>
                        <ul class="small">
                            <li><code>1:0:31</code> - Upgrade Core (High)</li>
                            <li><code>10:0:31</code> - Upgrade Core (Medium)</li>
                            <li><code>1214:0:31</code> - ไอเท็มพิเศษ</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- แสดงผลลัพธ์ -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white d-flex justify-content-between">
                        <h6 class="mb-0">รายการ Character Redeem Codes</h6>
                        <div>
                            <button id="refreshCodes" class="btn btn-sm btn-light">
                                <i class="fas fa-sync"></i> รีเฟรช
                            </button>
                            <button id="downloadCodes" class="btn btn-sm btn-warning">
                                <i class="fas fa-download"></i> ดาวน์โหลด
                            </button>
                            <button id="clearAllCodes" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i> ลบข้อมูลทั้งหมด
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="codesResult" class="table-responsive">
                            <!-- ตารางจะแสดงที่นี่ -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log การใช้งาน Character Code -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Log การใช้งาน Character Redeem Code</h6>
                <div>
                    <a href="files/game_systems/debug_character_usage_logs.php" target="_blank" class="btn btn-sm btn-warning mr-2">
                        <i class="fas fa-bug"></i> Debug
                    </a>
                    <button id="downloadCharacterUsageLogs" class="btn btn-sm btn-light mr-2">
                        <i class="fas fa-download"></i> ดาวน์โหลด Log
                    </button>
                    <button id="deleteAllCharacterLogs" class="btn btn-sm btn-danger mr-2">
                        <i class="fas fa-trash-alt"></i> ลบ Log ทั้งหมด
                    </button>
                    <button id="refreshCharacterLogs" class="btn btn-sm btn-light">
                        <i class="fas fa-sync"></i> รีเฟรช Log
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="characterUsageLogsTable">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Code ID</th>
                                <th>Code</th>
                                <th>Customer ID</th>
                                <th>Character ID</th>
                                <th>Channel IDX</th>
                                <th>วันที่ใช้</th>
                                <th>การจัดการ</th>
                            </tr>
                        </thead>
                        <tbody id="characterUsageLogsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadAllCharacterCodes();
    loadCharacterUsageLogs();

    $('#generateCharacterCodeForm').submit(function(e) {
        e.preventDefault();
        generateCharacterCodes();
    });

    $('#refreshCodes').click(function() {
        loadAllCharacterCodes();
    });

    $('#downloadCodes').click(function() {
        downloadCharacterCodes();
    });

    $('#clearAllCodes').click(function() {
        clearAllCharacterCodes();
    });

    $('#refreshCharacterLogs').click(function() {
        loadCharacterUsageLogs();
    });

    $('#downloadCharacterUsageLogs').click(function() {
        downloadCharacterUsageLogs();
    });

    $('#deleteAllCharacterLogs').click(function() {
        deleteAllCharacterUsageLogs();
    });
});

function generateCharacterCodes() {
    // ประมวลผลข้อมูล items - ตัด newline ออกและต่อข้อมูลกัน
    let itemsValue = $('#items').val();
    console.log('Original items value:', itemsValue);

    // ตัด newline ออกและต่อข้อมูลกัน
    itemsValue = itemsValue
        .replace(/\r?\n/g, '')   // ลบ newline ออกทั้งหมด (ข้อมูลจะต่อกัน)
        .replace(/\s*,\s*/g, ',') // ลบ space รอบ comma
        .replace(/,+/g, ',')     // ลบ comma ซ้ำ
        .replace(/^,+|,+$/g, '')  // ลบ comma ที่ต้นและท้าย
        .trim();

    console.log('Processed items value:', itemsValue);

    const formData = {
        action: 'generate',
        items: itemsValue,
        quantity: $('#quantity').val(),
        character_status: $('#characterStatus').val(),
        channel_idx: $('#channelIdx').val(),
        expiry_date: $('#expiryDate').val(),
        code_format: $('#codePrefix').val()
    };

    // Debug
    console.log('Character Status Value:', $('#characterStatus').val());
    console.log('Form Data:', formData);

    if (!formData.items) {
        alert('กรุณากรอกรายการไอเท็ม');
        return;
    }

    if (!formData.character_status || formData.character_status === '') {
        alert('กรุณาเลือกสถานะตัวละคร (ค่าปัจจุบัน: "' + formData.character_status + '")');
        return;
    }

    $('#generateCharacterCodeForm button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังสร้าง...');

    $.post('files/game_systems/class_module/character_redeem_code_api.php', formData, function(data) {
        console.log('API Response:', data);
        if (data.success) {
            loadAllCharacterCodes();
            if (data.characters_found > 0) {
                alert(`${data.message}\nตัวละครที่พบ: ${data.characters_found} ตัว\nCodes ที่สร้าง: ${data.codes_generated} codes`);
            } else {
                alert('สำเร็จ แต่ไม่พบตัวละครที่ตรงเงื่อนไข\nลองเปลี่ยนสถานะหรือ Channel');
            }
            $('#generateCharacterCodeForm')[0].reset();
        } else {
            let errorMsg = 'เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถสร้าง codes ได้');
            if (data.debug_info) {
                errorMsg += '\n\nข้อมูล Debug:';
                errorMsg += '\n- ตัวละครทั้งหมด: ' + data.debug_info.total_characters_in_db;
                errorMsg += '\n- ตัวละครที่ตรงสถานะ: ' + data.debug_info.characters_with_status;
                errorMsg += '\n- สถานะที่เลือก: ' + data.debug_info.character_status;
                errorMsg += '\n- Channel: ' + (data.debug_info.channel_idx || 'ทุก Channel');
            }
            alert(errorMsg);
        }
    }, 'json').fail(function(xhr, status, error) {
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#generateCharacterCodeForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-magic"></i> สร้าง Character Redeem Code');
    });
}

function loadAllCharacterCodes() {
    $.post('files/game_systems/class_module/character_redeem_code_api.php', {action: 'list'}, function(data) {
        console.log('API Response:', data);
        if (data.success) {
            displayCharacterCodesTable(data.codes);
        } else {
            console.error('API Error:', data.message);
            $('#codesResult').html('<div class="alert alert-danger">เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถโหลดข้อมูลได้') + '</div>');
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        console.error('Response Text:', xhr.responseText);
        $('#codesResult').html('<div class="alert alert-danger">เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error + '</div>');
    });
}

function displayCharacterCodesTable(codes) {
    console.log('Displaying codes:', codes);

    if (!codes || codes.length === 0) {
        $('#codesResult').html('<div class="alert alert-info">ยังไม่มี Character Redeem Codes</div>');
        return;
    }

    let html = `
        <table class="table table-striped table-sm">
            <thead class="thead-dark">
                <tr>
                    <th>ID</th>
                    <th>Code</th>
                    <th>User Num</th>
                    <th>Character ID</th>
                    <th>Character Name</th>
                    <th>Qty</th>
                    <th>Channel</th>
                    <th>Used</th>
                    <th>วันที่สร้าง</th>
                    <th>หมดอายุ</th>
                    <th>จัดการ</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    codes.forEach(function(code) {
        const channelText = code.channel_idx ? `Channel ${code.channel_idx}` : 'ทุก Channel';
        const expiryText = code.expiry_date ? new Date(code.expiry_date).toLocaleString('th-TH') : 'ไม่หมดอายุ';
        const usedText = code.status == '1' ? 'ใช้แล้ว' : 'ยังไม่ใช้';
        const usedBadge = code.status == '1' ? 'danger' : 'success';

        html += `
            <tr>
                <td>${code.id}</td>
                <td><code>${code.code}</code></td>
                <td><span class="badge badge-info">${code.user_num || 'N/A'}</span></td>
                <td>${code.character_idx || 'N/A'}</td>
                <td><strong>${code.character_name || 'N/A'}</strong></td>
                <td>${code.quantity}</td>
                <td>${channelText}</td>
                <td><span class="badge badge-${usedBadge}">${usedText}</span></td>
                <td>${new Date(code.datecreated).toLocaleString('th-TH')}</td>
                <td>${expiryText}</td>
                <td>
                    <button class="btn btn-sm btn-primary edit-code" data-id="${code.id}" data-code="${code.code}" data-items="${code.items}" data-quantity="${code.quantity}" data-used="${code.status}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-code" data-id="${code.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    $('#codesResult').html(html);

    // เพิ่ม event handlers สำหรับปุ่มแก้ไขและลบ
    $('.edit-code').click(function() {
        const id = $(this).data('id');
        const code = $(this).data('code');
        const items = $(this).data('items');
        const quantity = $(this).data('quantity');
        const used = $(this).data('used');

        editCharacterCode(id, code, items, quantity, used);
    });

    $('.delete-code').click(function() {
        const id = $(this).data('id');
        deleteCharacterCode(id);
    });
}

function downloadCharacterCodes() {
    window.open('files/game_systems/class_module/character_redeem_code_api.php?action=download', '_blank');
}

function clearAllCharacterCodes() {
    if (confirm('คุณแน่ใจหรือไม่ที่จะลบข้อมูล Character Redeem Codes ทั้งหมด?\n\nการกระทำนี้ไม่สามารถยกเลิกได้!')) {
        $.post('files/game_systems/class_module/character_redeem_code_api.php', {action: 'clear_all'}, function(data) {
            if (data.success) {
                loadAllCharacterCodes();
                alert('ลบข้อมูลทั้งหมดเรียบร้อยแล้ว');
            } else {
                alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบข้อมูลได้'));
            }
        }, 'json').fail(function(xhr, status, error) {
            alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
        });
    }
}

function editCharacterCode(id, code, items, quantity, used) {
    const newItems = prompt('แก้ไขรายการไอเท็ม:', items);
    if (newItems === null) return; // ยกเลิก

    const newQuantity = prompt('แก้ไขจำนวน:', quantity);
    if (newQuantity === null) return; // ยกเลิก

    const currentStatus = used == '1' ? 'ใช้แล้ว' : 'ยังไม่ใช้';
    const newUsed = confirm(`สถานะปัจจุบัน: ${currentStatus}\n\nกด OK = ใช้แล้ว (1)\nกด Cancel = ยังไม่ใช้ (0)`) ? '1' : '0';

    // ประมวลผลข้อมูล items - ตัด newline ออกและต่อข้อมูลกัน
    let processedItems = newItems
        .replace(/\r?\n/g, '')   // ลบ newline ออกทั้งหมด (ข้อมูลจะต่อกัน)
        .replace(/\s*,\s*/g, ',') // ลบ space รอบ comma
        .replace(/,+/g, ',')     // ลบ comma ซ้ำ
        .replace(/^,+|,+$/g, '')  // ลบ comma ที่ต้นและท้าย
        .trim();

    $.post('files/game_systems/class_module/character_redeem_code_api.php', {
        action: 'edit',
        id: id,
        items: processedItems,
        quantity: parseInt(newQuantity),
        used: newUsed
    }, function(data) {
        if (data.success) {
            loadAllCharacterCodes();
            alert('แก้ไขข้อมูลเรียบร้อยแล้ว');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถแก้ไขข้อมูลได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    });
}

function deleteCharacterCode(id) {
    if (confirm('คุณแน่ใจหรือไม่ที่จะลบ Code นี้?\n\nการกระทำนี้ไม่สามารถยกเลิกได้!')) {
        $.post('files/game_systems/class_module/character_redeem_code_api.php', {
            action: 'delete',
            id: id
        }, function(data) {
            if (data.success) {
                loadAllCharacterCodes();
                alert('ลบข้อมูลเรียบร้อยแล้ว');
            } else {
                alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบข้อมูลได้'));
            }
        }, 'json').fail(function(xhr, status, error) {
            alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
        });
    }
}

function loadCharacterUsageLogs() {
    $.post('files/game_systems/class_module/character_redeem_code_api.php', {
        action: 'list_usage_logs'
    }, function(data) {
        console.log('Character usage logs response:', data);
        if (data.success) {
            displayCharacterUsageLogs(data.logs);
        } else {
            console.error('Failed to load character usage logs:', data.message);
            $('#characterUsageLogsTableBody').html('<tr><td colspan="8" class="text-center text-danger">เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถโหลดข้อมูลได้') + '</td></tr>');
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error loading character usage logs:', error);
        console.error('Response Text:', xhr.responseText);
        $('#characterUsageLogsTableBody').html('<tr><td colspan="8" class="text-center text-danger">เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error + '</td></tr>');
    });
}

function displayCharacterUsageLogs(logs) {
    const tbody = $('#characterUsageLogsTableBody');
    tbody.empty();

    console.log('Displaying character usage logs:', logs);

    if (!logs || logs.length === 0) {
        tbody.append('<tr><td colspan="8" class="text-center">ไม่มีข้อมูล Log การใช้งาน</td></tr>');
        return;
    }

    // เก็บข้อมูล logs ไว้ใช้สำหรับดาวน์โหลด
    window.characterUsageLogs = logs;

    logs.forEach(function(log, index) {
        console.log(`Character Log ${index}:`, log);
        const usedDate = log.used_date || log.dateused || log.datecreated || '-';

        tbody.append(`
            <tr>
                <td>${log.id || '-'}</td>
                <td>${log.CodeID || '-'}</td>
                <td><code>${log.code || '-'}</code></td>
                <td>${log.CustomerID || '-'}</td>
                <td>${log.character_id || '-'}</td>
                <td>${log.channel_idx || '-'}</td>
                <td>${usedDate}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deleteCharacterUsageLog(${log.id || 0})">
                        <i class="fas fa-trash"></i> ลบ
                    </button>
                </td>
            </tr>
        `);
    });
}

function deleteCharacterUsageLog(id) {
    if (!confirm('คุณต้องการลบ Log การใช้งานนี้หรือไม่?')) {
        return;
    }

    $.post('files/game_systems/class_module/character_redeem_code_api.php', {
        action: 'delete_usage_log',
        id: id
    }, function(data) {
        if (data.success) {
            loadCharacterUsageLogs();
            alert('ลบ Log การใช้งานสำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json');
}

function deleteAllCharacterUsageLogs() {
    if (!window.characterUsageLogs || window.characterUsageLogs.length === 0) {
        alert('ไม่มีข้อมูล Log การใช้งาน');
        return;
    }

    const confirmMessage = `คุณต้องการลบ Log การใช้งานทั้งหมด ${window.characterUsageLogs.length} รายการหรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้!`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // แสดง loading
    $('#deleteAllCharacterLogs').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังลบ...');

    $.post('files/game_systems/class_module/character_redeem_code_api.php', {
        action: 'delete_all_usage_logs'
    }, function(data) {
        if (data.success) {
            loadCharacterUsageLogs(); // รีเฟรชรายการ
            alert(`ลบ Log การใช้งานทั้งหมดสำเร็จ ${data.deleted_count} รายการ`);
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#deleteAllCharacterLogs').prop('disabled', false).html('<i class="fas fa-trash-alt"></i> ลบ Log ทั้งหมด');
    });
}

function downloadCharacterUsageLogs() {
    if (!window.characterUsageLogs || window.characterUsageLogs.length === 0) {
        alert('ไม่มีข้อมูล Log การใช้งาน');
        return;
    }

    let content = 'Character Redeem Code Usage Logs - Downloaded on ' + new Date().toLocaleString() + '\n';
    content += '='.repeat(80) + '\n\n';
    content += `Total Usage Records: ${window.characterUsageLogs.length}\n\n`;

    window.characterUsageLogs.forEach(function(log, index) {
        content += `${index + 1}. Log ID: ${log.id || 'N/A'}\n`;
        content += `   Code ID: ${log.CodeID || 'N/A'}\n`;
        content += `   Code: ${log.code || 'N/A'}\n`;
        content += `   Customer ID: ${log.CustomerID || 'N/A'}\n`;
        content += `   Character ID: ${log.character_id || 'N/A'}\n`;
        content += `   Channel IDX: ${log.channel_idx || 'N/A'}\n`;
        content += `   Used Date: ${log.used_date || log.dateused || 'N/A'}\n`;
        content += '\n';
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'character_redeem_code_usage_logs_' + new Date().getTime() + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`ดาวน์โหลด Character Log การใช้งาน ${window.characterUsageLogs.length} รายการสำเร็จ!`);
}
</script>