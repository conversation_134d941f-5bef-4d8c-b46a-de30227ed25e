<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> manage-Donate
        <small>
            ระบบ แก้ไขเปลียนแปลงระบบเติมเงิน
        </small>
    </h1>
</div>

<div class="row">
    <div class="col-xl-6">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Edit Donate <span class="fw-300"><i>แก้ไขเปลียนแปลงระบบเติมเงิน</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <?php
             if (isset($_POST['btn_savechangepack'])) {
                // variable
                $inputuserids = strip_tags(trim($_POST['input_userids']));
                $inputamounts = strip_tags(trim($_POST['input_amounts']));
                // condition
                if (empty($inputuserids)) {
                    $returnWarning = "โปรดกรอกข้อมูล ไอดีเติมเงิน";
                } else if (empty($inputamounts)) {
                    $returnWarning = "เลือก จำนวนเงิน"; 
                } else {

                //admin id //
                $adminid = $userLogin->recUserAccount('ID', $conn);
                $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                $dateNow = date('Y-m-d H:i:s');
                
                    $selectauth = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE ID = '$inputuserids'";
                    $selectauthParam = array();
                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                    $id = $selectauthFetch['ID'];
                    
                    if(sqlsrv_rows_affected($selectauthQuery)){
                        
                                $AmountOpt = "EXECUTE WEB_DonationPacks_TrueW ?, ?";
                                $AmountParams = array($id,$inputamounts);
                                $AmountQuery = sqlsrv_query($conn, $AmountOpt, $AmountParams);
                                    if ($AmountQuery) {
                                    //$zpanel->generateWebLog($conn, '2', $adminidx, 'แก้ไข ออฟชั่น', "PetSerial: {$inputPetid} ออฟชั่น : {$inputOption} DateTime : {$dateNow}");
                                        $returnSuccess = "SUCCESS :: เพิ่มข้อมูลเติมเงิน id {$id} ราคา {$inputamounts} เรียบร้อย";
                                    } else {
                                         $returnError = "ERROR :: ระบบ EXECUTE WEB_PromotionItems_Wallet ผิดพลาด";
                                    }
                    }else{
                        $returnWarning = "ไม่มี PetSerial นี้";
                    }
                    
                }
            }
      
        if (isset($_POST['btn_savechange'])) {
                    // variable
                    $inputuserid = strip_tags(trim($_POST['input_userid']));
                    $inputtransactionid = strip_tags(trim($_POST['input_Transactionid']));
                    $inputtype = strip_tags(trim($_POST['input_type']));
                    $inputamount = strip_tags(trim($_POST['input_amount']));
                    // condition
                    if (empty($inputuserid)) {
                        $returnWarning = "โปรดกรอกข้อมูล ไอดีเติมเงิน";
                    } else if (empty($inputtransactionid)){
                        $returnWarning = "โปรดกรอกข้อมูล เลขอ้างอิง";
                    }else if (empty($inputtype)){
                        $returnWarning = "เลือก ระบบเติมเงิน";
                    }else if (empty($inputamount)){
                        $returnWarning = "เลือก จำนวนเงิน";
                    }else{

                    //admin id //
                    $adminid = $userLogin->recUserAccount('ID', $conn);
                    $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                    
                    $dateNow = date('Y-m-d H:i:s');
                    $status = "check_success";
                    $selectauth = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE ID = '$inputuserid'";
                    $selectauthParam = array();
                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                    $id = $selectauthFetch['ID'];
                    
                        if(sqlsrv_rows_affected($selectauthQuery)){
                                    $inserttm = "INSERT INTO WEB_Truewallet (Userid, Transactionid ,Type ,Amount ,Status ,Added_time) VALUES (?, ?, ?, ?, ?, ?)";
                                    $inserttmParams = array($id,$inputtransactionid,$inputtype,$inputamount,$status,$dateNow);
                                    $inserttmQuery = sqlsrv_query($conn, $inserttm, $inserttmParams);
                                        if ($inserttmQuery) {
                                                $AmountOpt = "EXECUTE WEB_DonationPacks_TrueW ?, ?";
                                                $AmountParams = array($id,$inputamount);
                                                $AmountQuery = sqlsrv_query($conn, $AmountOpt, $AmountParams);
                                                if ($AmountQuery) {
                                                    //$zpanel->generateWebLog($conn, '2', $adminidx, 'แก้ไข ออฟชั่น', "PetSerial: {$inputPetid} ออฟชั่น : {$inputOption} DateTime : {$dateNow}");
                                                    $returnSuccess = "SUCCESS :: เพิ่มข้อมูลเติมเงิน id {$id} เลขอ้างอิง {$inputtransactionid} ราคา {$inputamount} เรียบร้อย";
                                                    } else {
                                                            $returnError = "ERROR :: ระบบ EXECUTE WEB_PromotionItems_Wallet ผิดพลาด  {$id}";
                                                    }
                                        } else {
                                                $returnError = "ERROR :: ระบบ insert WEB_Truewallet ผิดพลาด";
                                        }
                        }else{
                            $returnWarning = "ไม่มี ไอดีนี้ นี้";
                        }
                        
                    }
                }

                
                ?>
                    <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                    <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                    <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                    <?php } ?>
                    <div class="frame-heading">
                        <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                            <div class="alert bg-warning-500 alert-dismissible fade show">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">
                                        <i class="fal fa-times"></i>
                                    </span>
                                </button>
                                <div class="d-flex flex-start w-100">
                                    <div class="mr-2 hidden-md-down">
                                        <span class="icon-stack icon-stack-lg">
                                            <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                                            <i
                                                class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                                            <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
                                        </span>
                                    </div>
                                    <div class="d-flex flex-fill">
                                        <div class="flex-fill">
                                            <span class="h5">How it works (แนะนำการทำงาน)</span>
                                            <br>
                                            -ไอดีผู้เล่น<code>UserID</code><br>
                                            -เลขอ้างอิงตามไบเสร็จ หรือไบแจ้งหนี้<code>Transactionid</code><br>
                                            -ผู้ให้บริการเติมเงิน<code>Type</code><br>
                                            -จำนวนเงิน<code>Amount</code><br><br>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel-content p-0">
                                <form>
                                    <div class="panel-content">
                                        <div class="form-row">
                                            <div class="col-md-6 mb-4">
                                                <label class="form-label" for="input_userid">UserID</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text" id="input_userid"><i
                                                                class="fas fa-user-check"></i></span>
                                                    </div>
                                                    <input type="text" name="input_userid" class="form-control"
                                                        id="input_userid" placeholder="Username"
                                                        aria-describedby="input_userid" pattern="[a-zA-Z0-9]+$"
                                                        value="<?php if (isset($id)) echo $id; ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-4">
                                                <label class="form-label"
                                                    for="input_Transactionid">Transactionid</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text" id="input_Transactionid"><i
                                                                class="fas fa-key"></i></span>
                                                    </div>
                                                  <input type="text" name="input_Transactionid" class="form-control"
                                                        id="input_Transactionid"  value="<?php if (isset($inputTransactionid)) echo $inputTransactionid; ?>">
                                                </div>
                                            </div>

                                        </div>
                                        <div class="form-row mb-2">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label" for="multiple-type">
                                                    Type service:
                                                </label>
                                                <select class="select2-type-multiple form-control" name="input_type"
                                                    multiple="multiple" id="multiple-type">
                                                    <optgroup label="ผู้ให้บริการ.">
                                                        <option value="truewallet">truewallet</option>
                                                        <option value="truemoney">truemoney</option>
                                                        <option value="ibank">ibank</option>
                                                        <option value="7-11">7-11</option>
                                                    </optgroup>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label" for="multiple-amount">
                                                    Amount:
                                                </label>
                                                <select class="select2-amount-multiple form-control" name="input_amount"
                                                    multiple="multiple" id="multiple-amount">
                                                    <optgroup label="จำนวนเงิน.">
                                                        <option value="50">50 บาท</option>
                                                        <option value="90">90 บาท</option>
                                                        <option value="100">100 บาท</option>
                                                        <option value="150">150 บาท</option>
                                                        <option value="300">300 บาท</option>
                                                        <option value="500">500 บาท</option>
                                                        <option value="1000">1000 บาท</option>
														<option value="350">350 บาท</option>
                                                    </optgroup>
                                                </select>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0
                                        d-flex flex-row">
                                        <button name="btn_savechange"
                                            class="btn btn-primary ml-auto waves-effect waves-themed"
                                            type="submit"><span
                                                class="fal fa-check mr-1"></span>เพิ่มข้อมูลเติมเงิน(สำหรับผู้เล่นยืนยันไม่ได้)</button>
                                    </div>
                            </div>
                            <div class="alert bg-warning-500 alert-dismissible fade show">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">
                                        <i class="fal fa-times"></i>
                                    </span>
                                </button>
                                <div class="d-flex flex-start w-100">
                                    <div class="mr-2 hidden-md-down">
                                        <span class="icon-stack icon-stack-lg">
                                            <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                                            <i
                                                class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                                            <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
                                        </span>
                                    </div>
                                    <div class="d-flex flex-fill">
                                        <div class="flex-fill">
                                            <span class="h5">How it works (แนะนำการทำงาน)</span>
                                            <br>
                                            -ไอดีผู้เล่น<code>UserID</code><br>
                                            -จำนวนเงิน<code>Amount</code><br><br>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-content">
                                <div class="form-row">
                                    <div class="col-md-6 mb-4">
                                        <label class="form-label" for="input_userids">UserID</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="input_userids"><i
                                                        class="fas fa-user-check"></i></span>
                                            </div>
                                            <input type="text" name="input_userids" class="form-control"
                                                id="input_userids" placeholder="Username"
                                                aria-describedby="input_userids" pattern="[a-zA-Z0-9]+$"
                                                value="<?php if (isset($id)) echo $id; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label" for="input_amounts">
                                            Amount:
                                        </label>
                                        <select class="select2-amount2-multiple form-control" name="input_amounts"
                                            multiple="multiple2" id="input_amounts">
                                            <optgroup label="จำนวนเงิน.">
                                                <option value="50">50 บาท</option>
                                                <option value="90">90 บาท</option>
                                                <option value="100">100 บาท</option>
                                                <option value="150">150 บาท</option>
                                                <option value="300">300 บาท</option>
                                                <option value="500">500 บาท</option>
                                                <option value="1000">1000 บาท</option>
												<option value="350">350 บาท</option>
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-12">
                                <div
                                    class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row">
                                    <button type="submit" name="btn_savechangepack"
                                        class="btn btn-danger ml-auto waves-effect waves-themed">
                                        <span class="fal fa-check mr-1"></span>แอดโปรโมชั่น(สำหรับผู้ยืนยันแล้วบัตรแล้ว)
                                    </button>
                                </div>
                            </div>



                        </form>
                    </div>

                </div>
            </div>
        </div>

        <!-- panel end -->
    </div>

    <div class="col-xl-6">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    เติมเงิน <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active p-3" data-toggle="tab" href="#tab_default-1" role="tab">
                                <i class="fal fa-table text-success"></i>
                                <span class="hidden-sm-down ml-1">ประวัติเติมเงิน</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link p-3" data-toggle="tab" href="#tab_default-2" role="tab">
                                <i class="fal fa-cog text-info"></i>
                                <span class="hidden-sm-down ml-1">ทดสอบระบบ Donets</span>
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content pt-4">
                        <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">
                                    <table id="dt-basic-donet"
                                        class="table table-bordered table-hover table-striped w-100">
                                        <thead>
                                            <tr>
                                                <th>id</th>
                                                <th>Userid</th>
                                                <th>Transactionid</th>
                                                <th>Type</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Added_time</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php

                                                        // generic function to get page
                                                        function getPage($stmt, $pageNum, $rowsPerPage) {
                                                            $offset = ($pageNum - 1) * $rowsPerPage;
                                                            $rows = array();
                                                            $i = 0;
                                                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                                                array_push($rows, $row);
                                                                $i++;
                                                            }
                                                            return $rows;
                                                        }

                                                        // Set the number of rows to be returned on a page.
                                                        $rowsPerPage = 10000;

                                                        // Define and execute the query.  
                                                        // Note that the query is executed with a "scrollable" cursor.
                                                        $sql = "SELECT * FROM  WEB_Truewallet  ORDER BY id DESC";

                                                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                                        if (!$stmt)
                                                            die(print_r(sqlsrv_errors(), true));

                                                        // Get the total number of rows returned by the query.
                                                        $rowsReturned = sqlsrv_num_rows($stmt);
                                                        if ($rowsReturned === false)
                                                            die(print_r(sqlsrv_errors(), true));
                                                        elseif ($rowsReturned == 0) {
                                                            echo W_NOTHING_RETURNED;
                                                            //exit();
                                                        } else {
                                                            /* Calculate number of pages. */
                                                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                                        }

                                                        // Display the selected page of data.
                                                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                                                        foreach ($page as $row) {
                                                    ?>
                                            <tr>
                                                <td><?php echo $row[0]; ?></td>
                                                <td><?php echo $row[1]; ?></td>
                                                <td><?php echo $row[2]; ?></td>
                                                <td><span
                                                        class="
								                        <?php echo $label = ($row[3] == 'truewallet' ? ' badge badge-danger badge-pill' : ($row[3] == 'Wallet' ? ' badge badge-success badge-pill' : ($row[3] == 'counterservice711' ? ' badge badge-success badge-pill' : ($row[3] == 'ibank' ? ' badge badge-success badge-pill' : 'badge badge-secondary badge-pill'))));?>">
                                                        <?php echo $status = ($row[3] == 'truewallet' ? 'truewallet' : ($row[3] == 'Wallet' ? 'Wallet' : ($row[3] == 'counterservice711' ? '7-11' : ($row[3] == 'ibank' ? 'ibank': 'Unknow'))));?></span>
                                                </td>
                                                <td><?php echo $row[4]; ?></td>
                                                <td><span class="badge
								                    <?php echo $label = ($row[5] == 'check_waiting' ? ' badge-warning badge-pill' : $label = $row[5] == 'check_pass' ? ' badge-danger badge-pill' : ($row[5] == 'check_success' ? ' badge-success badge-pill' : 'badge-light badge-pill'));?>">
                                                    <?php echo $status = ($row[5] == 'check_waiting' ? 'รอตรวจสอบ' : $status = $row[5] == 'check_pass' ? 'ไม่ผ่าน' : ($row[5] == 'check_success' ? 'ผ่าน' : 'Unknow status'));?></span>
                                                </td>
                                                <td><?php echo date('d/m/Y '.' H:i', strtotime($row[6])); ?>
                                                </td>
                                            </tr>
                                            <?php	} ?>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab_default-2" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
									<div class="panel-container show">
                                        <div class="panel-content">
                                            <div class="panel-tag">
                                                ระบบตรวจสอบโปรโมชั่นเติมเงิน
                                            </div>
                                            <div class="demo">
                                                <button type="button" class="btn btn-lg btn-secondary waves-effect waves-themed">
                                                   <span class="fal fa-check mr-1"></span>
                                                    50
                                                </button>
                                                <button type="button" class="btn btn-lg btn-default waves-effect waves-themed">
                                                    <span class="fal fa-check mr-1"></span>
                                                    100
                                                </button>
                                                <button type="button" class="btn btn-lg btn-success waves-effect waves-themed">
                                                   <span class="fal fa-check mr-1"></span>
                                                    150
                                                </button>
                                                <button type="button" class="btn btn-lg btn-warning waves-effect waves-themed">
                                                    <span class="fal fa-check mr-1"></span>
													300
                                                </button>
                                                <button type="button" class="btn btn-lg btn-info waves-effect waves-themed">
                                                    <span class="fal fa-check mr-1"></span>
                                                    500
                                                </button>
                                                <button type="button" class="btn btn-lg btn-danger waves-effect waves-themed">
                                                   <span class="fal fa-check mr-1"></span>
                                                    1000
                                                </button>
                                                <button type="button" class="btn btn-lg btn-dark waves-effect waves-themed">
                                                   <span class="fal fa-check mr-1"></span>
                                                    1050
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!--row end -->
    </div>