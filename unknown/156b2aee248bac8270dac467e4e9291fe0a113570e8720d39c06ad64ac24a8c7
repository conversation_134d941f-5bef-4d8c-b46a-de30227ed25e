<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Item Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2/dist/alpine.min.js" defer></script>
    <script src="files/game_systems/script.js" defer></script>
</head>
<body class="bg-gray-100 p-4">
    <div x-data="itemGenerator()" class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-lg">
        <!-- Basic Settings -->
        <div class="mb-6">
            <h2 class="text-xl font-bold mb-4">Basic Settings</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium">Item ID:</label>
                    <input x-model="itemId" type="text" class="mt-1 p-2 border rounded w-full">
                </div>
                <div>
                    <label class="block text-sm font-medium">Item Type:</label>
                    <select x-model="itemType" @change="updateOptions()" class="mt-1 p-2 border rounded w-full">
                        <option value="Helm">Helm</option>
                        <option value="Suit">Suit</option>
                        <option value="Gloves">Gloves</option>
                        <option value="Boots">Boots</option>
                        <option value="Weapon">Weapon</option>
                        <option value="Bike">Bike</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Upgrade:</label>
                    <select x-model="upgrade" @change="calculateItemCode()" class="mt-1 p-2 border rounded w-full">
                        <option v-for="i in 21" :value="i" :key="i">{{ i }}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Extreme:</label>
                    <select x-model="extreme" @change="calculateItemCode()" class="mt-1 p-2 border rounded w-full">
                        <option v-for="i in 8" :value="i" :key="i">{{ i }}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Divine:</label>
                    <select x-model="divine" @change="calculateItemCode()" class="mt-1 p-2 border rounded w-full">
                        <option v-for="i in 16" :value="i" :key="i">{{ i }}</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Item Slots -->
        <div class="mb-6">
            <h2 class="text-xl font-bold mb-4">Item Slots</h2>
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label class="block text-sm font-medium">Slot 1:</label>
                    <select x-model="slot1" @change="updateSlot(1)" class="mt-1 p-2 border rounded w-full">
                        <option value="NOT">NOT</option>
                        <template x-for="opt in options" :key="opt">
                            <option :value="opt">{{ opt }}</option>
                        </template>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Slot 2:</label>
                    <select x-model="slot2" @change="updateSlot(2)" class="mt-1 p-2 border rounded w-full">
                        <option value="NOT">NOT</option>
                        <template x-for="opt in options" :key="opt">
                            <option :value="opt">{{ opt }}</option>
                        </template>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Slot 3:</label>
                    <select x-model="slot3" @change="updateSlot(3)" class="mt-1 p-2 border rounded w-full">
                        <option value="NOT">NOT</option>
                        <template x-for="opt in options" :key="opt">
                            <option :value="opt">{{ opt }}</option>
                        </template>
                    </select>
                </div>
            </div>
        </div>

        <!-- Craft Option -->
        <div class="mb-6">
            <h2 class="text-xl font-bold mb-4">Craft Option</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium">Craft Height:</label>
                    <select x-model="craftHeight" @change="updateCraft()" class="mt-1 p-2 border rounded w-full">
                        <option v-for="i in 8" :value="i" :key="i">{{ i }}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium">Craft Option:</label>
                    <select x-model="craftOption" @change="updateCraft()" class="mt-1 p-2 border rounded w-full">
                        <option value="EMPTY">EMPTY</option>
                        <option value="NOT">NOT</option>
                        <template x-for="opt in options" :key="opt">
                            <option :value="opt">{{ opt }}</option>
                        </template>
                    </select>
                </div>
            </div>
        </div>

        <!-- Advanced Settings -->
        <div class="mb-6">
            <h2 class="text-xl font-bold mb-4">Advanced Settings</h2>
            <div class="grid grid-cols-1 gap-2">
                <label class="inline-flex items-center">
                    <input type="radio" x-model="advanceSetting" value="0" class="form-radio" @change="calculateItemCode()"> Non Bound
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" x-model="advanceSetting" value="4096" class="form-radio" @change="calculateItemCode()"> Bound On Account (Extended)
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" x-model="advanceSetting" value="524288" class="form-radio" @change="calculateItemCode()"> Bound On Character
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" x-model="advanceSetting" value="1572864" class="form-radio" @change="calculateItemCode()"> Bound On Character (On equipped)
                </label>
            </div>
        </div>

        <!-- Generate -->
        <div class="mb-6">
            <h2 class="text-xl font-bold mb-4">Generate</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <button type="button" x-on:click.prevent="generateCode()" class="bg-blue-500 text-white p-2 rounded hover:bg-blue-600">Generate</button>
                </div>
                <div>
                    <label class="block text-sm font-medium">Character:</label>
                    <input x-model="characterName" type="text" class="mt-1 p-2 border rounded w-full">
                    <button type="button" x-on:click.prevent="sendToAccount()" class="mt-2 bg-green-500 text-white p-2 rounded hover:bg-green-600">Send</button>
                </div>
                <div>
                    <label class="block text-sm font-medium">Item Code:</label>
                    <input x-model="itemCode" type="text" class="mt-1 p-2 border rounded w-full" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium">Options Code:</label>
                    <input x-model="optionsCode" type="text" class="mt-1 p-2 border rounded w-full" readonly>
                </div>
            </div>
        </div>

        <!-- Search -->
        <div>
            <h2 class="text-xl font-bold mb-4">Search</h2>
            <div>
                <label class="block text-sm font-medium">Item Search:</label>
                <input x-model.debounce="searchQuery" type="text" class="mt-1 p-2 border rounded w-full" placeholder="Search items...">
                <select x-model="selectedItem" @change="setItemId()" class="mt-1 p-2 border rounded w-full">
                    <option v-for="item in filteredItems" :value="item.id" :key="item.id">{{ item.name }}</option>
                </select>
            </div>
        </div>
    </div>
</body>
</html>