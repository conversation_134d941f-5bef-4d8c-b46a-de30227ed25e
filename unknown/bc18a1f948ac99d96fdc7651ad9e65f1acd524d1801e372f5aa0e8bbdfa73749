<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGETICKETS; ?> <small><?php echo PT_MANAGETICKETS_DESC; ?></small></h1></div>

<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=helpdesk/a/tickets" class="btn btn-block btn-info">Back to the Manage Tickets page</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                $getResult = $_GET['result'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 5;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM WEB_H_Tickets WHERE PCustomerID = '$getResult'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>This user doesnt have tickets.</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover helpdesks">
                        <thead>
                            <tr>
                                <th>CustomerID</th>
                                <th>Ticket title</th>
                                <th>Content resume</th>
                                <th>Status</th>
                                <th>Create Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr class="<?php echo $bg = ($row[6] == '1' ? 'yellow' : ($row[6] == '2' ? 'info' : ($row[6] == '4' ? 'success' : ($row[6] == '3' ? 'active' : 'active')))); ?>">
                                    <td>
                                        <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo $row[1]; ?></a>
                                    </td>
                                    <td class="visible-lg visible-md">
                                        <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>">
                                            <?php
                                            // if quantity of characters is more than 15 show only first 15 characters else show title without cut
                                            if (strlen($row[3]) > 15) {
                                                echo substr($row[3], 0, 15) . ' ...';
                                            } else {
                                                echo $row[3];
                                            }
                                            ?>
                                        </a>
                                    </td>

                                    <td>
                                        <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo substr($row[4], 0, 15); ?> ...</a>
                                    </td>

                                    <td>
                                        <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"<?php echo $onlyClosed = ($row[6] == '3' ? ' style="color: #FF3535"' : ''); ?>><?php echo $status = ($row[6] == '1' ? T_WAITING : ($row[6] == '2' ? T_REPLIED : ($row[6] == '3' ? T_CLOSED : ($row[6] == '4' ? T_SOLVED : T_UNKNOWNSTATUS)))); ?></a>
                                    </td>
                                    <td class="visible-lg visible-md visible-sm">
                                        <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo date('d/m ' . T_AT . ' H:i', strtotime($row[9])); ?></a>
                                    </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/results&result=". $getResult ."&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/results&result=". $getResult ."&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/results&result=". $getResult ."&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>