

					
          <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                    <div class="col-lg-6">
                        <h2><font color="red">CAPELA</font><small> WarPoint</small></h2>
                        <table class="table table-striped no-margn ">
                            <thead>
                                <tr>
                                    <th>ชื่อ</th>
                                    <th>รอบ</th>
                                    <th>แต้มสะสม</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "EXECUTE SERVER02.dbo.cabal_tool_GetCharacter_WarRank 1";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td > <a href="#" class="label label-primary text-red bg-defaul padd-sm"><?php echo number_format($resChars['chac'])?></a></td>
                                            <td > <a href="#" class="label label-primary text-red bg-defaul padd-sm"><?php echo number_format($resChars['totel'])?></a></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                </div>
                <div class="col-lg-6">
                        <h2><font color="red">POCYON</font><small> WarPoint</small></h2>
                        <table class="table table-striped no-margn ">
                            <thead>
                                <tr>
                                    <th>ชื่อ</th>
                                    <th>รอบ</th>
                                    <th>แต้มสะสม</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "EXECUTE SERVER02.dbo.cabal_tool_GetCharacter_WarRank 2";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td > <a href="#" class="label label-primary text-red bg-defaul padd-sm"><?php echo number_format($resChars['chac'])?></a></td>
                                            <td > <a href="#" class="label label-primary text-red bg-defaul padd-sm"><?php echo number_format($resChars['totel'])?></a></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                       
                    
                </div>
                </div>
               
                           <a href="#" class="btn btn-block btn-danger">ผู้ที่จะได้รับตำแหน่ง Bringer จำต้องทำคะแนนดีที่สุดใน 7 รอบ</a>

                </div>
                </div>
                
              