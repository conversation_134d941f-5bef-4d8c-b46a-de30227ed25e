<?php
// Helper to load item id=>name from XML for inventory view
function getItemNameMap($xmlPath) {
    static $itemMap = null;
    if ($itemMap !== null) return $itemMap;
    $itemMap = [];
    if (!file_exists($xmlPath)) return $itemMap;
    $xml = simplexml_load_file($xmlPath);
    if (!$xml) return $itemMap;
    foreach ($xml->msg as $msg) {
        $id = (string)$msg['id'];
        if (strpos($id, 'item') === 0) {
            $num = substr($id, 4); // 'item123' => '123'
            $itemMap[$num] = (string)$msg['cont'];
        }
    }
    return $itemMap;
}
