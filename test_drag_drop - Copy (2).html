<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Drag & Drop Slot System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .slot-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .slot {
            width: 93px;
            height: 93px;
            border: 1px solid #555;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
        }
        
        .slot:hover {
            border-color: #007bff !important;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }
        
        .slot.drag-over {
            border-color: #007bff !important;
            border-width: 2px !important;
            background-color: rgba(0, 123, 255, 0.1) !important;
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
        }
        
        .slot.dragging {
            opacity: 0.5;
            transform: scale(0.95);
        }
        
        .slot.has-item {
            background: #222;
            cursor: pointer;
        }
        
        .slot.empty {
            background: #2e2e2e;
            cursor: default;
        }
        
        .slot-number {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: 11px;
            color: #bbb;
            z-index: 2;
        }
        
        .item-name {
            background: rgba(0,0,0,0.7);
            color: #fff;
            padding: 2px 4px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 2px;
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            z-index: 3;
        }
        
        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            word-wrap: break-word;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease;
        }
        
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; color: #212529; }
        .status-info { background-color: #17a2b8; }
        
        .instructions {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 ทดสอบระบบ Drag & Drop Slot</h1>
        
        <div class="instructions">
            <h3>📋 วิธีการทดสอบ:</h3>
            <ul>
                <li>🖱️ ลากไอเท็มจาก slot หนึ่งไปยังอีก slot หนึ่ง</li>
                <li>🔄 หากปลายทางมีไอเท็มอยู่แล้ว จะมีการสลับตำแหน่งกัน</li>
                <li>📦 หากปลายทางว่าง จะเป็นการย้ายไอเท็ม</li>
                <li>❌ ไม่สามารถลาก slot ว่างได้</li>
                <li>✅ จะมีการยืนยันก่อนสลับไอเท็ม</li>
            </ul>
        </div>
        
        <h2>Pool 1 - Test Items</h2>
        <div class="slot-grid" id="slotGrid">
            <!-- Slots will be generated by JavaScript -->
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="resetSlots()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                🔄 รีเซ็ต Slots
            </button>
        </div>
    </div>

    <script>
        // Test data
        let testItems = [
            { id: 1, name: "Sword +15", slot: 0 },
            { id: 2, name: "Shield +10", slot: 1 },
            { id: 3, name: "Helmet +7", slot: 2 },
            { id: 4, name: "Armor +12", slot: 5 },
            { id: 5, name: "Boots +8", slot: 7 }
        ];
        
        let dragData = null;
        
        function showStatusMessage(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            document.body.appendChild(statusDiv);
            
            setTimeout(() => {
                statusDiv.style.opacity = '0';
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 300);
            }, 3000);
        }
        
        function createSlots() {
            const grid = document.getElementById('slotGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 20; i++) {
                const item = testItems.find(item => item.slot === i);
                const slot = document.createElement('div');
                
                slot.className = `slot ${item ? 'has-item' : 'empty'}`;
                slot.dataset.slot = i;
                if (item) {
                    slot.dataset.itemId = item.id;
                    slot.draggable = true;
                } else {
                    slot.draggable = false;
                }
                
                slot.innerHTML = `
                    <div class="slot-number">${i}</div>
                    ${item ? `<div class="item-name">${item.name}</div>` : '<div style="color: #666;">Empty</div>'}
                `;
                
                // Event listeners
                slot.addEventListener('dragstart', dragStart);
                slot.addEventListener('dragover', dragOver);
                slot.addEventListener('dragleave', dragLeave);
                slot.addEventListener('drop', dropItem);
                slot.addEventListener('dragend', dragEnd);
                
                grid.appendChild(slot);
            }
        }
        
        function dragStart(e) {
            const slot = e.currentTarget;
            
            if (!slot.dataset.itemId) {
                e.preventDefault();
                return false;
            }
            
            slot.classList.add('dragging');
            
            dragData = {
                itemId: slot.dataset.itemId,
                slot: parseInt(slot.dataset.slot)
            };
            
            e.dataTransfer.effectAllowed = 'move';
        }
        
        function dragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            
            const target = e.currentTarget;
            target.classList.add('drag-over');
        }
        
        function dragLeave(e) {
            const target = e.currentTarget;
            target.classList.remove('drag-over');
        }
        
        function dropItem(e) {
            e.preventDefault();
            const target = e.currentTarget;
            target.classList.remove('drag-over');
            
            const targetSlot = parseInt(target.dataset.slot);
            
            if (!dragData || dragData.slot === targetSlot) {
                return;
            }
            
            const itemA = testItems.find(item => item.id == dragData.itemId);
            const itemB = testItems.find(item => item.slot === targetSlot);
            
            if (!itemA) {
                showStatusMessage('❌ ไม่พบไอเท็มที่ต้องการย้าย', 'error');
                return;
            }
            
            if (itemB) {
                // Swap items
                const confirmSwap = confirm(
                    `คุณต้องการสลับตำแหน่งไอเท็มหรือไม่?\n\n` +
                    `"${itemA.name}" (Slot ${dragData.slot}) ↔ "${itemB.name}" (Slot ${targetSlot})`
                );
                
                if (!confirmSwap) {
                    return;
                }
                
                const tempSlot = itemA.slot;
                itemA.slot = targetSlot;
                itemB.slot = tempSlot;
                
                showStatusMessage(`✅ สลับตำแหน่งไอเท็ม "${itemA.name}" และ "${itemB.name}" สำเร็จ`, 'success');
            } else {
                // Move item to empty slot
                itemA.slot = targetSlot;
                showStatusMessage(`✅ ย้ายไอเท็ม "${itemA.name}" ไปยัง Slot ${targetSlot} สำเร็จ`, 'success');
            }
            
            createSlots(); // Refresh display
        }
        
        function dragEnd(e) {
            document.querySelectorAll('.slot').forEach(s => {
                s.classList.remove('dragging', 'drag-over');
            });
            dragData = null;
        }
        
        function resetSlots() {
            testItems = [
                { id: 1, name: "Sword +15", slot: 0 },
                { id: 2, name: "Shield +10", slot: 1 },
                { id: 3, name: "Helmet +7", slot: 2 },
                { id: 4, name: "Armor +12", slot: 5 },
                { id: 5, name: "Boots +8", slot: 7 }
            ];
            createSlots();
            showStatusMessage('🔄 รีเซ็ต Slots เรียบร้อยแล้ว', 'info');
        }
        
        // Initialize
        createSlots();
        showStatusMessage('🎮 ระบบ Drag & Drop พร้อมใช้งาน', 'success');
    </script>
</body>
</html>
