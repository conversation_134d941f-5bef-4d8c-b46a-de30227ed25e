name: "C<PERSON>"

on:
  pull_request:
  push:
    branches:
      - "master"
  schedule:
    - cron: "42 3 * * *"

jobs:
  phpunit:
    name: "PHPUnit"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php-version:
          - "5.4"
          - "5.5"
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
        dependencies:
          - "highest"
        include:
          - dependencies: "lowest"
            php-version: "5.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
        with:
          fetch-depth: 2

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          coverage: "none"
          ini-values: "zend.assertions=1"

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v1"
        with:
          dependency-versions: "${{ matrix.dependencies }}"

      - name: "Run PHPUnit"
        run: "vendor/bin/phpunit"
