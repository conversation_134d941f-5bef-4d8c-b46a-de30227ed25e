<?php $user->restrictionUser(true, $conn); ?>
<?php $zpanel->checkSession(true); ?>
<?php
$getCharID = filter_input(INPUT_GET, 'charid', FILTER_VALIDATE_INT);
if ($getCharID === null || $getCharID === false) {
    echo '<script type="text/javascript">';
    echo 'setTimeout(function () { Swal.fire("Error", "รหัสตัวละครไม่ถูกต้อง", "error"); });';
    echo '</script>';
    exit;
}

$useridx = floor($getCharID / 16);

$usernum = $userLogin->recAllUserAccount('UserNum', $conn, $useridx);
$name = $userLogin->thaitrans($userLogin->recCharecter($getCharID, 'Name', $conn));
$Login = $userLogin->recCharecter($getCharID, 'Login', $conn);
$class = $userLogin->cabalstyle($userLogin->recCharecter($getCharID, 'Style', $conn));

if (isset($_POST['btn_chang'])) {

    if ($Login == 1) {
        echo '<script type="text/javascript">';
        echo 'setTimeout(function () { Swal.fire("Error", "ออกเกมส์ก่อน", "error"); });';
        echo '</script>';
        
    }else{

    $classchang = "EXECUTE " . DATABASE_WEB . ".dbo.WEB_cabal_skill_data_set ?";
    $classchangParams = [$getCharID];
    $classchangQuery = sqlsrv_query($conn, $classchang, $classchangParams);

        if ($classchangQuery) {
            echo '<script type="text/javascript">';
            echo 'setTimeout(function () { Swal.fire("Success !!!", "เพิ่มสกิล GM ให้ตัวละคร ' . $getCharID . ' เรียบร้อยแล้ว!", "success"); });';
            echo '</script>';
        } else {
            echo '<script type="text/javascript">';
            echo 'setTimeout(function () { Swal.fire("Error", "เพิ่มสกิล GM ผิดพลาด!", "error"); });';
            echo '</script>';
        }
    }
}
?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> charecter Manager
        <small>
            ระบบ จัดการตัวละคร
        </small>
    </h1>
</div>
<div class="alert alert-primary alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">How it works</span>
                <br>
                โปรดตรวจสอบให้แน่ใจก่อนทุกครั้ง ก่อนทำการ <code>แก้ไข</code> <code>อัพเดต</code> <code>ลบข้อมูล</code>
                เพื่อป้องการข้อผิดพลาด หรือป้องการการทำงานของระบบผิดพลาด
                ถ้าเกิดข้อผิดพลาดจะบางอย่างจะกลับมาเป็นปรกติไม่ได้.
                <br>
                <div class="d-flex mt-2 mb-1 fs-xs text-danger">
                    เพิ่มสกิล GM 144 145 146 147
                </div>
            </div>
        </div>
    </div>
</div>
<?php  if ($getCharID === NULL){  ?>
<div class="alert alert-danger alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-danger-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-danger-300 fa-flip-vertical"></i>
                <i class="fal fa-times icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">เกิดข้อมูลผิดพลาด</span>
                <br>
                ตรวจสอบไม่พอข้อมูลผู้เล้นนี้ คุณแน่ใจหรือไม่ว่าข้อมูลนี้ถูกต้อง
                ลองกลับไปตรวจสอบข้อมูลใหม่อีกครั้งก่อนทำการ <code>แก้ไข</code> <code>อัพเดต</code> <code>ลบข้อมูล</code>
                เพื่อป้องการข้อผิดพลาด หรือป้องการการทำงานของระบบผิดพลาด
                ถ้าเกิดข้อผิดพลาดจะบางอย่างจะกลับมาเป็นปรกติไม่ได้.
                <br>
            </div>
        </div>
    </div>
</div>
<?php } ?>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    เพิ่มสกิล GM <span class="fw-300"><i></i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="col-xl-12">
                        <div id="c_1" class="card border shadow-0 mb-g shadow-sm-hover"
                            data-filter-tags="oliver kopyov">
                            <div class="card-body border-faded border-top-0 border-left-0 border-right-0 rounded-top">
                                <div class="d-flex flex-row align-items-center">
                                    <?php if($Login == 0){ ?>
                                    <span class="status status-secondary mr-3">
                                        <?php } else { ?>
                                        <span class="status status-success mr-3">
                                            <?php } ?>
                                            <span class="rounded-circle profile-image d-block "
                                                style="background-image:url('assets/images/cabal/class/<?php echo $class['Class_Name']; ?>.png'); background-size: cover;"></span>
                                        </span>
                                        <div class="info-card-text flex-1">
                                            <a href="javascript:void(0);"
                                                class="fs-xl text-truncate text-truncate-lg text-info">
                                                ชื่อตัวละคร : <?php echo $name; ?>
                                            </a>
                                            <span class="text-truncate text-truncate-xl">ไอดีผู้ใช้งาน :
                                                <?php echo $userLogin->recAllUserAccount('UserNum',$conn,$useridx); ?>,
                                                <?php echo $usernum = $userLogin->recAllUserAccount('ID',$conn,$useridx); ?>
                                            </span>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="frame-heading">
                        <form role="form" method="post" enctype="multipart/form-data">
                            <div class="col-md-12">

                                <button type="submit" name="btn_chang"
                                    class="btn btn-primary ml-auto waves-effect waves-themed">
                                    <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง
                                </button>

                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>

        <!--row end -->
    </div>