<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

$data = json_decode(file_get_contents('php://input'), true);

$eventId      = (int) ($data['eventId'] ?? 0);
$slot         = (int) ($data['slot'] ?? 0);
$itemId       = (int) ($data['itemId'] ?? 0);
$itemOpt      = (int) ($data['itemOption'] ?? 0);
$duration     = (int) ($data['duration'] ?? 0);
$pricealz     = (int) ($data['PriceAlz'] ?? 0); // แก้ชื่อให้ตรงกับ JS
$itemPriceID  = (int) ($data['itemPriceID'] ?? 0); // แก้ชื่อ key ให้ตรงกัน
$point        = (int) ($data['point'] ?? 0);
$expire       = $data['expire'] ?? "1970-01-01 00:00:00";
$daily        = (int) ($data['daily'] ?? 0);
$weekly       = (int) ($data['weekly'] ?? 0);

sqlsrv_query($conn, "DELETE FROM EventData.dbo.cabal_ems_event_npcitemshop_table WHERE EventID = ? AND SlotIdx = ?", [$eventId, $slot]);

$query = "INSERT INTO " . DATABASE_EDATA . ".dbo.cabal_ems_event_npcitemshop_table
(EventID, WorldIdx, NpcIdx, SlotIdx, ItemKindIdx, ItemOption, ItemDurationIdx, PriceAlz, ItemPriceID, id, PricePoint, ExpirationTime, DailyLimit, WeeklyLimit)
VALUES (?, 1, 49, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$params = [
    $eventId,
    $slot,
    $itemId,
    $itemOpt,
    $duration,
    $pricealz,
    $itemPriceID,
    '00000000-0000-0000-0000-000000000000', // id ค่าคงที่ใส่ที่นี่
    $point,
    $expire,
    $daily,
    $weekly
];

$result = sqlsrv_query($conn, $query, $params);

if ($result === false) {
    $errors = sqlsrv_errors();
    echo json_encode(['error' => 'Query failed', 'details' => $errors]);
    exit;
}

echo json_encode(["status" => "saved"]);
?>
