<style>
.equip-slot {
  width: 100px;
  height: 100px;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 120% 120%; /* ขยายภาพให้ใหญ่ขึ้น */
  background-color: rgba(0, 0, 0, 0.3); /* ให้พื้นหลังโปร่งแสง */
  border-radius: 0.4rem;
  overflow: hidden;
  cursor: default;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  color: #fff;
  user-select: none;
}

.equip-slot.empty {
  background-color: rgba(68, 68, 68, 0.5);
  opacity: 0.5;
}

.equip-slot > div {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  font-size: 11px;
  padding: 2px 4px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0.4rem;
  border-bottom-right-radius: 0.4rem;
  user-select: text;
}

.equip-slot .label {
  position: absolute;
  top: 2px;
  left: 4px;
  font-size: 10px;
  color: #ccc;
  user-select: none;
  pointer-events: none;
}

</style>


<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
    <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
        <div class="card-header bg-warning text-dark py-3">
            <h5 class="mb-0"><i class="fas fa-boxes mr-2"></i>ตัวละคร (equipment View)</h5>
        </div>
        <div class="card-body" id="equipmentViewer">
            <div class="text-center text-muted">กำลังโหลดข้อมูลอุปกรณ์...</div>
        </div>
        <h3 class="mt-4">🔢 Equipment HexData รวมทั้งหมด</h3>
        <div style="position:relative;">
            <textarea id="equipmenthexAll" class="form-control mb-2" style="height:200px;" readonly></textarea>
            <div id="equipmenthexAllOverlay" style="pointer-events:none;position:absolute;left:0;top:0;width:100%;height:100%;border-radius:4px;display:none;"></div>
        </div>
        <button id="updateEquipmentBtn" class="btn btn-primary mb-4">Update Equipment</button>
    </div>
<?php } ?>

 <div class="modal fade" id="editEquipModal" tabindex="-1" role="dialog" aria-labelledby="editEquipModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editEquipModalLabel">แก้ไข Slot</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="editEquipForm">
                            <div class="form-group">
                                <label for="equipSlotNumber">Slot Number</label>
                                <input type="text" class="form-control" id="equipSlotNumber" readonly>
                            </div>
                            <div class="form-group">
                                <label for="equipItemIndex">Item Index</label>
                                <input type="text" class="form-control" id="equipItemIndex">
                            </div>
                            <div class="form-group">
                                <label for="equipSerial">Serial</label>
                                <input type="text" class="form-control" id="equipSerial">
                            </div>
                            <div class="form-group">
                                <label for="equipOption">Option</label>
                                <input type="text" class="form-control" id="equipOption">
                            </div>
                            <div class="form-group">
                                <label for="equipPeriod">Period</label>
                                <input type="text" class="form-control" id="equipPeriod">
                            </div>
                            <div class="form-group">
                                <label for="equipKindIdx">Kind Index</label>
                                <input type="text" class="form-control" id="equipKindIdx">
                            </div>
                            <div class="form-group">
                                <label for="equipHexPreview">Hex Preview</label>
                                <textarea class="form-control" id="equipHexPreview" readonly></textarea>
                            </div>
                            <div class="modal-footer">
                            <button type="button" class="btn btn-danger" id="deleteEquipSlotBtn">ลบข้อมูล</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                            <button type="submit" class="btn btn-success">บันทึก</button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


<script>
let equipmentResult = {};
const equipImageBasePath = 'assets/images/items/';
(function () {
        window.equipItemNameMap = <?php echo json_encode($itemNameMap, JSON_UNESCAPED_UNICODE); ?>;
        const equipCharIdx = <?php echo isset($characterData['CharacterIdx']) ? (int)$characterData['CharacterIdx'] : 0; ?>;

        $.getJSON('_app/php/equipment_data.php?ajax=1&charIdx=' + encodeURIComponent(equipCharIdx))
            .done(function(equipData) {
                equipmentResult = equipData;

                console.log('✅ Equipment Loaded:', equipmentResult);

                const equipmentTabSlots = [28, 3, 9, 7];
                const equipmentNav = $('<ul class="nav nav-tabs" id="equipmentTabNav" role="tablist"></ul>');
                const equipmentContent = $('<div class="tab-content border border-top-0 p-3" id="equipmentTabs"></div>');

                let globalEquipSlotIndex = 0;

                for (let tabIndex = 0; tabIndex < equipmentTabSlots.length; tabIndex++) {
                    const tabId = 'equip_tab_' + tabIndex;
                    equipmentNav.append(`
                        <li class="nav-item" role="presentation">
                            <a class="nav-link${tabIndex === 0 ? ' active' : ''}" id="${tabId}-tab" data-toggle="tab" href="#${tabId}" role="tab" aria-controls="${tabId}" aria-selected="${tabIndex === 0 ? 'true' : 'false'}">Tab ${tabIndex + 1}</a>
                        </li>
                    `);

                    const equipGrid = $('<div class="grid d-flex flex-wrap gap-2"></div>');

                    for (let slotInTab = 0; slotInTab < equipmentTabSlots[tabIndex]; slotInTab++, globalEquipSlotIndex++) {
                        const equipmentSlotData = equipmentResult[globalEquipSlotIndex] || {};
                        const equipmentSlotDiv = $(`
                        <div class="equip-slot text-white text-center d-flex flex-column justify-content-end align-items-center" 
                            data-slot="${globalEquipSlotIndex}" 
                            id="equip_slot_${globalEquipSlotIndex}">
                            <div class="slot-label" style="font-size:11px;"></div>
                        </div>
                        `);
                        equipmentSlotDiv.append(`<div class="label" style="position:absolute;top:0;left:0;font-size:10px;color:#ccc;">${globalEquipSlotIndex}</div>`);

                        const equipItemIndex = equipmentSlotData.itemIndex ? parseInt(equipmentSlotData.itemIndex) : 0;

                        if (equipItemIndex > 0 && !isNaN(equipItemIndex)) {
                            let imagePath = equipImageBasePath + equipItemIndex + '.png';
                            let image = new Image();
                            image.onload = function () {
                                equipmentSlotDiv.css({
                                    backgroundImage: `url('${imagePath}')`,
                                    backgroundSize: '200% 200%',
                                    backgroundPosition: 'center'
                                });
                            };
                            image.onerror = function () {
                                let fallbackPath = equipImageBasePath + 'default.png';
                                equipmentSlotDiv.css({
                                    backgroundImage: `url('${fallbackPath}')`,
                                    backgroundSize: '200% 200%',
                                    backgroundPosition: 'center'
                                });
                            };
                            image.src = imagePath;

                            let itemName = window.equipItemNameMap[equipItemIndex] || '';
                            equipmentSlotDiv.append(`<div style="font-size:11px;color:#fff;background:rgba(0,0,0,0.5);">${itemName}</div>`);
                        } else {
                            equipmentSlotDiv.css({
                                opacity: 0.5,
                                backgroundColor: '#444'
                            }).addClass('empty');
                            equipmentSlotDiv.append(`<div style="font-size:11px;color:#888;">(ว่าง)</div>`);
                        }

                        equipGrid.append(equipmentSlotDiv);
                    }

                    equipmentContent.append(
                        $(`<div class="tab-pane fade${tabIndex === 0 ? ' show active' : ''}" id="${tabId}" role="tabpanel" aria-labelledby="${tabId}-tab">`).append(equipGrid)
                    );
                }

                $('#equipmentViewer').empty().append(equipmentNav).append(equipmentContent);

            let hexCombined = '0x';
            const totalSlots = equipmentTabSlots.reduce((sum, count) => sum + count, 0);
for (let i = 0; i < totalSlots; i++) {
    const slotData = equipmentResult[i];
    if (slotData?.HexData) {
        hexCombined += slotData.HexData;
    }
    // ถ้าไม่มีข้อมูล slot นี้ จะไม่เติมอะไรลงไป (ตัดออก)
}
$('#equipmenthexAll').val(hexCombined);
    
            })
            .fail(function(jqxhr, textStatus, error) {
                console.error('❌ Equipment AJAX Failed:', textStatus, error);
                $('#equipmentViewer').html('<div class="alert alert-danger">โหลดข้อมูลอุปกรณ์ล้มเหลว: ' + textStatus + '</div>');
            });

        // ปุ่ม Update Equipment - ส่งข้อมูล HexData ที่แก้ไขกลับไปเซิร์ฟเวอร์
        $('#updateEquipmentBtn').on('click', function() {
            // อัปเดต HexData รวมทั้งหมดจาก equipmentResult
            let hexCombined = '0x';
            const totalSlots = [28,3,9,6].reduce((sum, count) => sum + count, 0);
            for (let i = 0; i < totalSlots; i++) {
                if (equipmentResult[i]?.HexData) {
                    hexCombined += equipmentResult[i].HexData;
                }
            }
            $('#equipmenthexAll').val(hexCombined);
            // อัปเดต Hex Preview ใน modal ถ้า modal เปิดอยู่
            if ($('#editEquipModal').hasClass('show') && currentEquipSlotIndex !== null) {
                const slotData = equipmentResult[currentEquipSlotIndex] || {};
                $('#equipHexPreview').val(slotData.HexData || '(ว่าง)');
            }
            // ส่งข้อมูลไปเซิร์ฟเวอร์
            $.ajax({
                url: '_app/php/equipment_data.php',
                method: 'POST',
                data: {
                    update_equipment: 1,
                    charIdx: equipCharIdx,
                    HexData: hexCombined
                },
                success: function(response) {
                    alert('อัปเดตข้อมูล Equipment เรียบร้อยแล้ว');
                    console.log('Update Response:', response);
                },
                error: function(xhr, status, error) {
                    alert('เกิดข้อผิดพลาดในการอัปเดตข้อมูล: ' + error);
                    console.error('Update Error:', error);
                }
            });
        });
    })();

let currentEquipSlotIndex = null;
// เปิด modal เมื่อคลิกขวาที่ slot
$(document).off('contextmenu.equip').on('contextmenu.equip', '.equip-slot', function(e) {
    e.preventDefault();
    currentEquipSlotIndex = $(this).data('slot');
    const data = equipmentResult[currentEquipSlotIndex] || {
        itemIndex: '',
        Serial: '',
        Option: '',
        Period: '',
        KindIdx: ''
    };
    $('#equipSlotLabel').text(currentEquipSlotIndex);
    $('#equipSlotNumber').val(currentEquipSlotIndex);
    $('#equipItemIndex').val(data.itemIndex || '0');
    $('#equipSerial').val(data.Serial || '0');
    $('#equipOption').val(data.Option || '0');
    $('#equipPeriod').val(data.Period || '0');
    $('#equipKindIdx').val(data.KindIdx || '0');
    // อัปเดต Hex preview แบบ real-time
    function updateModalHex() {
        let KindIdx = parseInt($('#equipKindIdx').val()) || 0;
        const itemIndex = parseInt($('#equipItemIndex').val()) || 0;
        const Serial = parseInt($('#equipSerial').val()) || 0;
        const Option = parseInt($('#equipOption').val()) || 0;
        const slotNum = parseInt($('#equipSlotNumber').val()) || 0;
        const Period = parseInt($('#equipPeriod').val()) || 0;
        KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        const hexData =
            bigIntToHexLE(KindIdx, 8) +
            bigIntToHexLE(Serial, 8) +
            bigIntToHexLE(Option, 8) +
            bigIntToHexLE(slotNum, 2) +
            bigIntToHexLE(Period, 4);
        $('#equipHexPreview').val(hexData.toUpperCase());
    }
    updateModalHex();
    $('#editEquipForm input').off('input.equiphex').on('input.equiphex', updateModalHex);
    // ตรวจสอบ modal ใน DOM ก่อนเปิด
    if ($('#editEquipModal').length) {
        $('#editEquipModal').modal('show');
    } else {
        alert('Modal ไม่พบใน DOM');
    }
});

    // ปิด modal เมื่อกดปุ่มยกเลิก หรือปุ่ม X (Bootstrap 4)
    $('#cancelEditSlot, #closeModalBtn').on('click', function() {
        $('#editSlotModal').modal('hide');
    });

    $('#editEquipForm').on('submit', function(e) {
    e.preventDefault();
    // อัปเดตข้อมูล slot ใน equipmentResult
    const slot = parseInt($('#equipSlotNumber').val());
    const itemIndex = parseInt($('#equipItemIndex').val()) || 0;
    const Serial = parseInt($('#equipSerial').val()) || 0;
    const Option = parseInt($('#equipOption').val()) || 0;
    const Period = parseInt($('#equipPeriod').val()) || 0;
    let KindIdx = parseInt($('#equipKindIdx').val()) || 0;
    KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
    const hexData = $('#equipHexPreview').val();
    equipmentResult[slot] = {
        KindIdx: KindIdx,
        itemIndex: itemIndex,
        Serial: Serial,
        Option: Option,
        Period: Period,
        HexData: hexData,
        _changed: true
    };
    // อัพเดท UI slot
    const slotDiv = $('#equip_slot_' + slot);
    slotDiv.empty().removeClass('empty');
    slotDiv.append(`<div class="label">${slot}</div>`);
    slotDiv.append(`<div style="font-size:11px;color:#fff;background:rgba(0,0,0,0.5);">${window.equipItemNameMap[itemIndex] || ''}</div>`);
    slotDiv.css({
        backgroundImage: itemIndex > 0 ? `url('${equipImageBasePath + itemIndex + ".png"}')` : '',
        backgroundColor: itemIndex > 0 ? '#222' : '#444',
        opacity: itemIndex > 0 ? 1 : 0.5
    });
    $('#editEquipModal').modal('hide');
    // อัปเดต HexData รวมทั้งหมดทันที (เรียง slot ตาม index)
    let hexCombined = '0x';
    const totalSlots = [28,3,9,6].reduce((sum, count) => sum + count, 0);
    for (let i = 0; i < totalSlots; i++) {
        if (equipmentResult[i]?.HexData) {
            hexCombined += equipmentResult[i].HexData;
        }
    }
    $('#equipmenthexAll').val(hexCombined);
});

// แปลงตัวเลขเป็น hex แบบ Little Endian
function bigIntToHexLE(value, byteLength) {
    const hex = value.toString(16).padStart(byteLength * 2, '0');
    const bytes = hex.match(/.{2}/g)?.reverse() || [];
    return bytes.join('');
}

// อัปเดต Hex รวมทั้งหมดใน textarea
function updateHexTextarea() {
    let hexCombined = '0x';
    const total = equipmentTabSlots.reduce((s, n) => s + n, 0);
    for (let i = 0; i < total; i++) {
        hexCombined += equipmentResult[i]?.HexData ?? '00'.repeat(60);
    }
    $('#equipmenthexAll').val(hexCombined);
}

// Fix: Ensure modal close button works for dynamically created modal
$(document).on('click', '#editEquipModal .close', function() {
    $('#editEquipModal').modal('hide');
});

// ตรวจสอบข้อมูลใน equipmentResult
// ไม่ต้องแสดง error ใน UI หาก equipmentResult ว่าง
// if (!equipmentResult || Object.keys(equipmentResult).length === 0) {
//     console.error('❌ ไม่มีข้อมูลใน equipmentResult');
// }

// Event: ลบข้อมูล slot
$(document).on('click', '#deleteEquipSlotBtn', function() {
    if (currentEquipSlotIndex !== null) {
        delete equipmentResult[currentEquipSlotIndex];
        $('#editEquipModal').modal('hide');
        // อัปเดต HexData รวมทั้งหมด
        let hexCombined = '0x';
        const totalSlots = [28,3,9,6].reduce((sum, count) => sum + count, 0);
        for (let i = 0; i < totalSlots; i++) {
            if (equipmentResult[i]?.HexData) {
                hexCombined += equipmentResult[i].HexData;
            }
        }
        $('#equipmenthexAll').val(hexCombined);
        // อัปเดต UI slot ให้เป็นว่าง
        const slotDiv = $('#equip_slot_' + currentEquipSlotIndex);
        slotDiv.css({backgroundImage:'',backgroundColor:'#444',opacity:0.5}).addClass('empty');
        slotDiv.find('div:not(.label)').remove();
        slotDiv.append('<div style="font-size:11px;color:#888;">(ว่าง)</div>');
    }
});
    </script>
                     
