<?php
/**
 * Notification System for Item Sending
 * Handles real-time notifications and notification history
 */

// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

/**
 * Get database connection using sqlsrv
 */
function getDbConnection() {
    global $dbinfo;
    
    try {
        $serverName = $dbinfo['host'];
        if (isset($dbinfo['port']) && $dbinfo['port']) {
            $serverName .= "," . $dbinfo['port'];
        }
        
        $connectionOptions = [
            "Database" => $dbinfo['database'],
            "Uid" => $dbinfo['username'],
            "PWD" => $dbinfo['password'],
            "CharacterSet" => "UTF-8"
        ];
        
        $connection = sqlsrv_connect($serverName, $connectionOptions);
        
        if ($connection === false) {
            $errors = sqlsrv_errors();
            $errorMessage = "Database connection failed: ";
            foreach ($errors as $error) {
                $errorMessage .= $error['message'] . " ";
            }
            throw new Exception($errorMessage);
        }
        
        return $connection;
    } catch (Exception $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed");
    }
}

/**
 * Create notification for item send
 */
function createNotification($connection, $data) {
    try {
        $notification_id = uniqid('notif_', true);
        $title = "Item Sent Successfully";
        $message = "Item sent to {$data['player_username']} via {$data['send_method']}";
        $type = 'item_send';
        $priority = 'normal';
        $created_at = date('Y-m-d H:i:s');
        $is_read = 0;
        $admin_username = $data['admin_username'] ?? 'System';
        
        $details = json_encode([
            'send_id' => $data['send_id'],
            'player_username' => $data['player_username'],
            'item_id' => $data['item_id'],
            'item_code' => $data['item_code'],
            'options_code' => $data['options_code'],
            'quantity' => $data['quantity'],
            'duration' => $data['duration'],
            'send_method' => $data['send_method'],
            'status' => $data['status']
        ]);
        
        $sql = "INSERT INTO notifications (
            notification_id, title, message, type, priority, details,
            admin_username, created_at, is_read, expires_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, DATEADD(day, 7, GETDATE()))";
        
        $params = array(
            &$notification_id, &$title, &$message, &$type, &$priority, &$details,
            &$admin_username, &$created_at, &$is_read
        );
        
        $stmt = sqlsrv_prepare($connection, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            $errors = sqlsrv_errors();
            $errorMessage = "Failed to create notification: ";
            foreach ($errors as $error) {
                $errorMessage .= $error['message'] . " ";
            }
            throw new Exception($errorMessage);
        }
        
        return $notification_id;
        
    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get notifications for admin
 */
function getNotifications($connection, $admin_username = null, $limit = 50, $unread_only = false) {
    try {
        $sql = "SELECT TOP (?) 
                    notification_id, title, message, type, priority, details,
                    admin_username, created_at, is_read, read_at, expires_at
                FROM notifications 
                WHERE expires_at > GETDATE()";
        
        $params = array(&$limit);
        
        if ($admin_username) {
            $sql .= " AND admin_username = ?";
            $params[] = &$admin_username;
        }
        
        if ($unread_only) {
            $sql .= " AND is_read = 0";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = sqlsrv_prepare($connection, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to get notifications");
        }
        
        $notifications = array();
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // Convert datetime objects to strings
            if ($row['created_at']) {
                $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
            }
            if ($row['read_at']) {
                $row['read_at'] = $row['read_at']->format('Y-m-d H:i:s');
            }
            if ($row['expires_at']) {
                $row['expires_at'] = $row['expires_at']->format('Y-m-d H:i:s');
            }
            
            // Parse details JSON
            if ($row['details']) {
                $row['details'] = json_decode($row['details'], true);
            }
            
            $notifications[] = $row;
        }
        
        return $notifications;
        
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Mark notification as read
 */
function markAsRead($connection, $notification_id) {
    try {
        $read_at = date('Y-m-d H:i:s');
        
        $sql = "UPDATE notifications SET is_read = 1, read_at = ? WHERE notification_id = ?";
        $params = array(&$read_at, &$notification_id);
        
        $stmt = sqlsrv_prepare($connection, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to mark notification as read");
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get notification statistics
 */
function getNotificationStats($connection, $admin_username = null) {
    try {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN type = 'item_send' THEN 1 ELSE 0 END) as item_sends,
                    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority
                FROM notifications 
                WHERE expires_at > GETDATE()";
        
        $params = array();
        
        if ($admin_username) {
            $sql .= " AND admin_username = ?";
            $params[] = &$admin_username;
        }
        
        $stmt = sqlsrv_prepare($connection, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to get notification statistics");
        }
        
        $stats = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting notification statistics: " . $e->getMessage());
        throw $e;
    }
}

// Handle different request methods
try {
    $connection = getDbConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create notification
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $action = $data['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $notification_id = createNotification($connection, $data);
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification created successfully',
                    'notification_id' => $notification_id
                ]);
                break;
                
            case 'mark_read':
                if (!isset($data['notification_id'])) {
                    throw new Exception('Missing notification_id');
                }
                markAsRead($connection, $data['notification_id']);
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get notifications
        $admin_username = $_GET['admin'] ?? null;
        $limit = (int)($_GET['limit'] ?? 50);
        $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
        $get_stats = isset($_GET['stats']) && $_GET['stats'] === 'true';
        
        if ($get_stats) {
            $stats = getNotificationStats($connection, $admin_username);
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
        } else {
            $notifications = getNotifications($connection, $admin_username, $limit, $unread_only);
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);
        }
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($connection)) {
        sqlsrv_close($connection);
    }
}
?>
