/*
Name: 			Layouts / Header Menu - Examples
Written by: 	<PERSON><PERSON> Themes - (http://www.okler.net)
Theme Version: 	1.7.0
*/

(function($) {

	'use strict';

	// Toggle Mega Sub Menu Expand Button
	var megaSubMenuToggleButton = function() {

		var $button = $('.mega-sub-nav-toggle');

		$button.on('click', function(){
			$(this).toggleClass('toggled');
		});

	};

	$(function() {
		megaSubMenuToggleButton();
	});

}).apply(this, [jQuery]);