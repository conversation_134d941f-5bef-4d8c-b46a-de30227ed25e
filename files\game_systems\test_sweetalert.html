<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SweetAlert2</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { margin: 10px; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test SweetAlert2 Compatibility</h1>
        <p>ทดสอบ SweetAlert2 เพื่อตรวจสอบความเข้ากันได้</p>
        
        <h3>Basic Alerts</h3>
        <button class="btn-primary" onclick="testBasicAlert()">Basic Alert</button>
        <button class="btn-success" onclick="testSuccessAlert()">Success Alert</button>
        <button class="btn-danger" onclick="testErrorAlert()">Error Alert</button>
        
        <h3>Loading Tests</h3>
        <button class="btn-warning" onclick="testLoadingOld()">Loading (onOpen)</button>
        <button class="btn-warning" onclick="testLoadingNew()">Loading (didOpen)</button>
        
        <h3>Toast Tests</h3>
        <button class="btn-primary" onclick="testToastOld()">Toast (onOpen)</button>
        <button class="btn-primary" onclick="testToastNew()">Toast (didOpen)</button>
        
        <h3>Item Send Simulation</h3>
        <button class="btn-success" onclick="simulateItemSendSuccess()">Simulate Success</button>
        <button class="btn-danger" onclick="simulateItemSendError()">Simulate Error</button>
        
        <div id="results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
            <h4>Test Results:</h4>
            <div id="resultsList"></div>
        </div>
    </div>

    <script>
        function addResult(test, status, error = null) {
            const resultsList = document.getElementById('resultsList');
            const div = document.createElement('div');
            div.style.margin = '5px 0';
            div.innerHTML = `
                <span style="color: ${status === 'success' ? 'green' : 'red'};">
                    ${status === 'success' ? '✅' : '❌'} ${test}
                </span>
                ${error ? `<br><small style="color: red;">Error: ${error}</small>` : ''}
            `;
            resultsList.appendChild(div);
        }
        
        function testBasicAlert() {
            try {
                Swal.fire('Hello!', 'This is a basic alert', 'info');
                addResult('Basic Alert', 'success');
            } catch (error) {
                addResult('Basic Alert', 'error', error.message);
            }
        }
        
        function testSuccessAlert() {
            try {
                Swal.fire({
                    icon: 'success',
                    title: 'สำเร็จ!',
                    text: 'ส่งไอเทมเรียบร้อยแล้ว',
                    timer: 2000,
                    showConfirmButton: false
                });
                addResult('Success Alert', 'success');
            } catch (error) {
                addResult('Success Alert', 'error', error.message);
            }
        }
        
        function testErrorAlert() {
            try {
                Swal.fire({
                    icon: 'error',
                    title: 'ผิดพลาด!',
                    text: 'ไม่สามารถส่งไอเทมได้',
                    timer: 3000,
                    showConfirmButton: false
                });
                addResult('Error Alert', 'success');
            } catch (error) {
                addResult('Error Alert', 'error', error.message);
            }
        }
        
        function testLoadingOld() {
            try {
                Swal.fire({
                    title: 'กำลังส่งไอเทม...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    onOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                setTimeout(() => {
                    Swal.close();
                    addResult('Loading (onOpen)', 'success');
                }, 2000);
            } catch (error) {
                addResult('Loading (onOpen)', 'error', error.message);
            }
        }
        
        function testLoadingNew() {
            try {
                Swal.fire({
                    title: 'กำลังส่งไอเทม...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                setTimeout(() => {
                    Swal.close();
                    addResult('Loading (didOpen)', 'success');
                }, 2000);
            } catch (error) {
                addResult('Loading (didOpen)', 'error', error.message);
            }
        }
        
        function testToastOld() {
            try {
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    onOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                    }
                });
                
                Toast.fire({
                    icon: 'info',
                    title: 'Toast with onOpen'
                });
                
                addResult('Toast (onOpen)', 'success');
            } catch (error) {
                addResult('Toast (onOpen)', 'error', error.message);
            }
        }
        
        function testToastNew() {
            try {
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                    }
                });
                
                Toast.fire({
                    icon: 'info',
                    title: 'Toast with didOpen'
                });
                
                addResult('Toast (didOpen)', 'success');
            } catch (error) {
                addResult('Toast (didOpen)', 'error', error.message);
            }
        }
        
        function simulateItemSendSuccess() {
            try {
                // Show loading
                Swal.fire({
                    title: 'กำลังส่งไอเทม...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    onOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // Simulate API call
                setTimeout(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'สำเร็จ!',
                        text: 'ส่งไอเทมให้ test_player เรียบร้อยแล้ว',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    addResult('Item Send Success Simulation', 'success');
                }, 1500);
                
            } catch (error) {
                addResult('Item Send Success Simulation', 'error', error.message);
            }
        }
        
        function simulateItemSendError() {
            try {
                // Show loading
                Swal.fire({
                    title: 'กำลังส่งไอเทม...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    onOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // Simulate API error
                setTimeout(() => {
                    Swal.fire({
                        icon: 'error',
                        title: 'ผิดพลาด!',
                        text: 'ไม่สามารถส่งไอเทมให้ test_player ได้',
                        timer: 3000,
                        showConfirmButton: false
                    });
                    
                    addResult('Item Send Error Simulation', 'success');
                }, 1500);
                
            } catch (error) {
                addResult('Item Send Error Simulation', 'error', error.message);
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', function() {
            addResult('Page Loaded', 'success');
            
            // Check SweetAlert2 version
            if (typeof Swal !== 'undefined') {
                addResult('SweetAlert2 Loaded', 'success');
                
                // Try to detect version
                try {
                    const version = Swal.version || 'Unknown';
                    addResult(`SweetAlert2 Version: ${version}`, 'success');
                } catch (e) {
                    addResult('SweetAlert2 Version Detection', 'error', 'Cannot detect version');
                }
            } else {
                addResult('SweetAlert2 Loaded', 'error', 'SweetAlert2 not found');
            }
        });
    </script>
</body>
</html>
