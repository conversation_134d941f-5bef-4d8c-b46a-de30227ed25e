<?php
session_start();
if (!isset($_SESSION['userLogin'])) {
    exit("❌ ไม่มีการ login");
}

require_once("../../../_app/dbinfo.inc.php");
require_once("../../../_app/general_config.inc.php");

require_once("../../../_app/php/zpanel.class.php");
$zpanel = new zpanel();

// userLogged class
require_once("../../../_app/php/userLogin.class.php");
$userLogin = new userLogged();

// ตรวจสอบประเภทฟอร์ม
$formType = $_POST['form_type'] ?? 'shoppool_msg';

if ($formType === 'shoppool_msg') {
    // ฟอร์มจากฟอร์มที่ 2: อัปเดต Script1 ของ NPC
    $Poolid = filter_var($_POST['PoolID'] ?? 0, FILTER_VALIDATE_INT);
    $Markernum = filter_var($_POST['Marker'] ?? 0, FILTER_VALIDATE_INT);
    $MsgName = trim($_POST['msg_thai'] ?? '');

    //var_dump($Markernum);
    if (!is_int($Poolid) || $Poolid < 0 || $Poolid > 7 || empty($MsgName)) {
        exit("❌ ข้อมูล msg_thai ไม่ถูกต้อง");
    }

    $win874_npc = @iconv("UTF-8", "Windows-874//IGNORE", $MsgName);
    if ($win874_npc === false || strlen($win874_npc) === 0) {
        exit("❌ แปลงรหัส NPC Script ล้มเหลว");
    }

    $sql = "UPDATE " . DATABASE_SV . ".dbo.cabal_cashService_msg_pool_table 
            SET Msg = ?, Marker = ?
            WHERE PoolID = ?";

    $stmt = sqlsrv_prepare($conn, $sql, [
        [$win874_npc, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_STREAM(SQLSRV_ENC_BINARY), SQLSRV_SQLTYPE_VARBINARY(strlen($win874_npc))],
        [$Markernum, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT],
        [$Poolid, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT]
    ]);

    if (!$stmt || !sqlsrv_execute($stmt)) {
        exit("❌ อัปเดต Script ล้มเหลว อาจเกิดจากข้อมูลซ้ำหรือข้อผิดพลาดในการเชื่อมต่อ");
    }

    echo "✅ อัปเดต Script1 เรียบร้อยแล้ว";
    exit;
}


if ($formType === 'shoppool_msg2') {
    // ฟอร์มจากฟอร์มที่ 2: อัปเดต Script1 ของ NPC
    $Poolid = filter_var($_POST['PoolID'] ?? 0, FILTER_VALIDATE_INT);
    $Tabnum = filter_var($_POST['TabID'] ?? 0, FILTER_VALIDATE_INT);
    $Markernum = filter_var($_POST['Marker'] ?? 0, FILTER_VALIDATE_INT);
    $MsgName = trim($_POST['msg_thai'] ?? '');

    var_dump($Poolid);
    var_dump($Tabnum);
    var_dump($Markernum);
    var_dump($MsgName);
    // ตรวจสอบความถูกต้อง
    if (!is_int($Poolid) || $Poolid < 0 || $Poolid > 7 ||
        !is_int($Tabnum) || $Tabnum < 0 || $Tabnum > 10 ||
        empty($MsgName)) {
        exit("❌ ข้อมูล msg_thai ไม่ถูกต้อง");
    }

    $win874_npc = @iconv("UTF-8", "Windows-874//IGNORE", $MsgName);
    if ($win874_npc === false || strlen($win874_npc) === 0) {
        exit("❌ แปลงรหัส NPC Script ล้มเหลว");
    }

    $sql = "UPDATE " . DATABASE_SV . ".dbo.cabal_cashService_msg_tab_table 
            SET Msg = ?, TabID = ?, Marker = ?
            WHERE PoolID = ?";

    $stmt = sqlsrv_prepare($conn, $sql, [
        [$win874_npc, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_STREAM(SQLSRV_ENC_BINARY), SQLSRV_SQLTYPE_VARBINARY(strlen($win874_npc))],
        [$Tabnum, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT],
        [$Markernum, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT],
        [$Poolid, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT]
    ]);

    if (!$stmt || !sqlsrv_execute($stmt)) {
        exit("❌ อัปเดต Script ล้มเหลว อาจเกิดจากข้อมูลซ้ำหรือข้อผิดพลาดในการเชื่อมต่อ");
    }

    echo "✅ อัปเดต Script2 เรียบร้อยแล้ว";
    exit;
}
?>