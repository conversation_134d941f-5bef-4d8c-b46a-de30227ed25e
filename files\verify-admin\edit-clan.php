<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGECLANS; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getClanID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get clan by ID
                $selectClan = "SELECT * FROM ".DATABASE_SV.".dbo.Guild WHERE GuildNo = '$getClanID'";
                $selectClanParam = array();
                $selectClanOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectClanQuery = sqlsrv_query($conn, $selectClan, $selectClanParam, $selectClanOpt);
                $selectClanRows = sqlsrv_num_rows($selectClanQuery);

                if (!$selectClanRows) {
                    $returnWarning = W_CLAN_NOT_FOUND;
                } else {
                    while ($resClan = sqlsrv_fetch_array($selectClanQuery, SQLSRV_FETCH_ASSOC)) {

                        // form edit action
                        $formClanEdit = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                        if (isset($formClanEdit['btn-edit'])) {
                            $maxClanMembers = (int)$_POST['maxclanmembers'];
                            
                            // condition
                            if (empty($formClanEdit['clanname'])) {
                                $returnWarning = W_EMPTY_CLANNAME;
                            } else if (empty($formClanEdit['clantag'])) {
                                $returnWarning = W_EMPTY_CLANTAG;
                            } else if (!is_int($maxClanMembers)) {
                                $returnWarning = W_MAXMEMBERS_INT;
                            } else {

                                // update clan
                                $updateClan = "UPDATE ClanData SET "
                                        . "ClanName = '$formClanEdit[clanname]', "
                                        . "ClanTag = '$formClanEdit[clantag]', "
                                        . "MaxClanMembers = '$formClanEdit[maxclanmembers]' "
                                        . "WHERE ClanID = '$getClanID'";
                                $updateClanParam = array();
                                $updateClanQuery = sqlsrv_query($conn, $updateClan, $updateClanParam);

                                if (sqlsrv_rows_affected($updateClanQuery)) {

                                    // generate web log
                                    $zpanel->generateWebLog($conn, '2', $getCustomerID, 'clan updated', 'Clan updated by admin');
                                    $returnSuccess = S_CLAN_UPDATE;
                                } else {
                                    $returnError = E_FAILED_UPDATE;
                                }
                            }
                        }
                        ?>
                        <?php if (isset($returnSuccess)) { ?>
                            <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                        <?php } elseif (isset($returnWarning)) { ?>
                            <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                        <?php } elseif (isset($returnError)) { ?>
                            <div class="alert alert-danger"><?php echo $returnError; ?></div>
                        <?php } ?>
                        <form method="post" enctype="multipart/form-data" action="">
                            <h2 class="text-red"><?php echo T_CLANINFO; ?> <small>ClanID: <?php echo $resClan['ClanID']; ?></small></h2>
                            <hr>
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <h4 class="text-red"><?php echo T_CLANNAME; ?></h4>
                                    <input class="form-control" type="text" name="clanname" value="<?php echo $resClan['ClanName']; ?>">
                                </div>

                                <div class="form-group">
                                    <h4 class="text-red"><?php echo T_CLANTAG; ?></h4>
                                    <input class="form-control" type="text" name="clantag" value="<?php echo $resClan['ClanTag']; ?>">
                                </div>

                                <div class="form-group">
                                    <h4 class="text-red"><?php echo T_CLANMAXMEMBERS; ?></h4>
                                    <input class="form-control" type="text" name="maxclanmembers" value="<?php echo $resClan['MaxClanMembers']; ?>">
                                </div>

                                <input type="submit" class="btn btn-success btn-block" name="btn-edit" value="<?php echo B_EDIT; ?>">
                            </div>
                        </form>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
</div>