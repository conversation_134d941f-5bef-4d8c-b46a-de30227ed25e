<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multiple Rounds System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 900px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-case { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .quantity-display { font-size: 24px; font-weight: bold; color: #007bff; }
        .rounds-info { background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test Multiple Rounds System</h1>
        <p>ทดสอบการส่งไอเทมหลายรอบตาม Quantity ที่กำหนด</p>
        
        <div class="rounds-info">
            <h4>📋 วิธีการทำงาน:</h4>
            <ul>
                <li><strong>Quantity = 1</strong> → ส่ง 1 รอบ (1 ชิ้น)</li>
                <li><strong>Quantity = 5</strong> → ส่ง 5 รอบ (5 ชิ้น)</li>
                <li><strong>Quantity = 10</strong> → ส่ง 10 รอบ (10 ชิ้น)</li>
                <li>แต่ละรอบจะส่งไอเทม 1 ชิ้น ผ่าน stored procedure</li>
            </ul>
        </div>
        
        <!-- Quick Test Buttons -->
        <div class="test-case">
            <h3>⚡ Quick Tests</h3>
            <button onclick="testQuantity(1, 'inventory')">1 รอบ (Cash Inventory)</button>
            <button onclick="testQuantity(3, 'mail')">3 รอบ (Mail System)</button>
            <button onclick="testQuantity(5, 'warehouse')">5 รอบ (Event Inventory)</button>
            <button onclick="testQuantity(10, 'inventory')">10 รอบ (Cash Inventory)</button>
            <button onclick="testQuantity(20, 'mail')">20 รอบ (Mail System)</button>
        </div>
        
        <!-- Custom Test Form -->
        <div class="test-case">
            <h3>🎛️ Custom Test</h3>
            <form id="customTestForm">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group">
                        <label for="playerUsername">Player Username:</label>
                        <input type="text" id="playerUsername" value="test_player" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="sendMethod">Send Method:</label>
                        <select id="sendMethod" required>
                            <option value="inventory">Cash Inventory</option>
                            <option value="mail">Mail System</option>
                            <option value="warehouse">Event Inventory</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="quantity">Quantity (จำนวนรอบที่ต้องการส่ง):</label>
                    <input type="range" id="quantity" min="1" max="50" value="5" oninput="updateQuantityDisplay()">
                    <div class="quantity-display" id="quantityDisplay">5 รอบ</div>
                </div>
                
                <button type="submit">🚀 Test Custom Quantity</button>
            </form>
        </div>
        
        <!-- Stress Test -->
        <div class="test-case">
            <h3>💪 Stress Tests</h3>
            <button onclick="runStressTest()">🔥 Run Stress Test (Multiple Quantities)</button>
            <button onclick="testLargeQuantity()">📈 Test Large Quantity (50 rounds)</button>
            <button onclick="testAllMethods()">🎯 Test All Methods</button>
        </div>
        
        <!-- Results -->
        <div id="results"></div>
    </div>

    <script>
        function updateQuantityDisplay() {
            const quantity = document.getElementById('quantity').value;
            document.getElementById('quantityDisplay').textContent = `${quantity} รอบ`;
        }
        
        function showResult(type, title, content) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Time: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function sendTestItem(data) {
            try {
                const startTime = Date.now();
                
                const response = await fetch('files/game_systems/send_item.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                result.duration = duration; // Add duration to result
                return result;
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        async function testQuantity(quantity, method) {
            const testData = {
                playerUsername: `test_${quantity}rounds_${method}`,
                itemCode: '0000007B00000000',
                optionsCode: '0000000000000000',
                quantity: quantity,
                duration: 31,
                sendMethod: method,
                adminUsername: 'test_admin'
            };
            
            showResult('info', `Testing ${quantity} Rounds (${method.toUpperCase()})`, 
                `Starting test...\nExpected: ${quantity} rounds\nMethod: ${method}\nPlayer: ${testData.playerUsername}`);
            
            const result = await sendTestItem(testData);
            
            if (result.success) {
                const successInfo = `✅ SUCCESS!
Expected Rounds: ${quantity}
Actual Quantity: ${result.data.quantity}
Method: ${method}
Duration: ${result.duration}ms
Status: ${result.data.status}

Final Results:
- Item Delivered: ${result.final_results?.item_delivered ? 'YES' : 'NO'}
- Notification Sent: ${result.final_results?.notification_sent ? 'YES' : 'NO'}
- Database Logged: ${result.final_results?.database_logged ? 'YES' : 'NO'}
- Transaction Completed: ${result.final_results?.transaction_completed ? 'YES' : 'NO'}`;
                
                showResult('success', `${quantity} Rounds Test PASSED`, successInfo);
            } else {
                showResult('error', `${quantity} Rounds Test FAILED`, 
                    `Error: ${result.error}\nDetails: ${JSON.stringify(result, null, 2)}`);
            }
        }
        
        // Custom form submission
        document.getElementById('customTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const quantity = parseInt(document.getElementById('quantity').value);
            const method = document.getElementById('sendMethod').value;
            const player = document.getElementById('playerUsername').value;
            
            const testData = {
                playerUsername: player,
                itemCode: '0000007B00000000',
                optionsCode: '0000000000000000',
                quantity: quantity,
                duration: 31,
                sendMethod: method,
                adminUsername: 'test_admin'
            };
            
            showResult('info', `Custom Test: ${quantity} Rounds`, 
                `Testing custom configuration...\nQuantity: ${quantity}\nMethod: ${method}\nPlayer: ${player}`);
            
            const result = await sendTestItem(testData);
            
            if (result.success) {
                showResult('success', `Custom Test PASSED (${quantity} rounds)`, 
                    `Successfully sent ${result.data.quantity} items in ${quantity} rounds\nDuration: ${result.duration}ms`);
            } else {
                showResult('error', `Custom Test FAILED`, JSON.stringify(result, null, 2));
            }
        });
        
        async function runStressTest() {
            const testCases = [
                { quantity: 1, method: 'inventory' },
                { quantity: 2, method: 'mail' },
                { quantity: 3, method: 'warehouse' },
                { quantity: 5, method: 'inventory' },
                { quantity: 7, method: 'mail' },
                { quantity: 10, method: 'warehouse' },
                { quantity: 15, method: 'inventory' }
            ];
            
            showResult('info', 'Starting Stress Test', 
                `Running ${testCases.length} test cases with different quantities...`);
            
            for (let i = 0; i < testCases.length; i++) {
                const testCase = testCases[i];
                
                showResult('info', `Stress Test ${i + 1}/${testCases.length}`, 
                    `Testing ${testCase.quantity} rounds with ${testCase.method}...`);
                
                await testQuantity(testCase.quantity, testCase.method);
                
                // Wait 2 seconds between tests
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            showResult('success', 'Stress Test Completed', 
                `All ${testCases.length} test cases completed. Check individual results above.`);
        }
        
        async function testLargeQuantity() {
            showResult('warning', 'Large Quantity Test Warning', 
                'Testing 50 rounds - this may take a while...');
            
            await testQuantity(50, 'inventory');
        }
        
        async function testAllMethods() {
            const methods = ['inventory', 'mail', 'warehouse'];
            const quantity = 5;
            
            showResult('info', 'Testing All Methods', 
                `Testing ${quantity} rounds for each method...`);
            
            for (const method of methods) {
                await testQuantity(quantity, method);
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
            
            showResult('success', 'All Methods Test Completed', 
                'Tested all 3 methods with 5 rounds each.');
        }
        
        // Auto-load message
        window.addEventListener('load', function() {
            showResult('info', 'Multiple Rounds Test System Ready', 
                'Ready to test multiple rounds functionality. Each quantity = number of rounds to send.');
        });
    </script>
</body>
</html>
