<?php $user->restrictionUser(true, $conn); ?>

<?php
// Function to get detailed mail analytics
function getMailAnalytics($conn, $days = 7) {
    $analytics = array();
    
    try {
        // Daily mail activity for the last N days
        $sql = "SELECT 
                    CAST(DeliveryTime AS DATE) as mail_date,
                    COUNT(*) as total_mails,
                    SUM(CASE WHEN Alz > 0 THEN 1 ELSE 0 END) as mails_with_alz,
                    SUM(CASE WHEN ItemKindIdx > 0 THEN 1 ELSE 0 END) as mails_with_items,
                    SUM(Alz) as total_alz
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(day, -?, GETDATE())
                GROUP BY CAST(DeliveryTime AS DATE)
                ORDER BY mail_date DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['daily_activity'][] = $row;
            }
        }
        
        // Top item types sent
        $sql = "SELECT TOP 10 
                    ItemKindIdx,
                    COUNT(*) as count,
                    SUM(CASE WHEN IsReceivedItem = 1 THEN 1 ELSE 0 END) as received_count
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE ItemKindIdx > 0
                GROUP BY ItemKindIdx
                ORDER BY count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['top_items'][] = $row;
            }
        }
        
        // Hourly distribution
        $sql = "SELECT 
                    DATEPART(hour, DeliveryTime) as hour_of_day,
                    COUNT(*) as mail_count
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(day, -7, GETDATE())
                GROUP BY DATEPART(hour, DeliveryTime)
                ORDER BY hour_of_day";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['hourly_distribution'][] = $row;
            }
        }
        
        // Mail status distribution
        $sql = "SELECT 
                    CASE 
                        WHEN IsReceivedItem = 1 AND IsReceivedAlz = 1 THEN 'Fully Received'
                        WHEN IsReceivedItem = 1 AND IsReceivedAlz = 0 THEN 'Item Received Only'
                        WHEN IsReceivedItem = 0 AND IsReceivedAlz = 1 THEN 'Alz Received Only'
                        ELSE 'Not Received'
                    END as status,
                    COUNT(*) as count
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table
                GROUP BY IsReceivedItem, IsReceivedAlz";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['status_distribution'][] = $row;
            }
        }
        
    } catch (Exception $e) {
        error_log("Mail analytics error: " . $e->getMessage());
    }
    
    return $analytics;
}

$days = isset($_GET['days']) ? (int)$_GET['days'] : 7;
$analytics = getMailAnalytics($conn, $days);
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-analytics"></i> การวิเคราะห์ Mail
    </h1>
    <div class="subheader-block">
        <div class="btn-group" role="group">
            <a href="?url=manager_mail/mail-analytics&days=7" class="btn btn-sm <?php echo $days == 7 ? 'btn-primary' : 'btn-outline-primary'; ?>">7 วัน</a>
            <a href="?url=manager_mail/mail-analytics&days=30" class="btn btn-sm <?php echo $days == 30 ? 'btn-primary' : 'btn-outline-primary'; ?>">30 วัน</a>
            <a href="?url=manager_mail/mail-analytics&days=90" class="btn btn-sm <?php echo $days == 90 ? 'btn-primary' : 'btn-outline-primary'; ?>">90 วัน</a>
        </div>
        <a href="?url=manager_mail/mail-statistics" class="btn btn-success btn-sm ml-2">
            <i class="fal fa-chart-bar"></i> กลับสถิติ
        </a>
    </div>
</div>

<div class="row">
    <!-- Daily Activity Chart -->
    <div class="col-xl-8">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมรายวัน (<?php echo $days; ?> วันที่ผ่านมา)</h3>
            </div>
            <div class="card-body">
                <canvas id="dailyActivityChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Status Distribution -->
    <div class="col-xl-4">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">สถานะการรับเมลล์</h3>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Hourly Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การกระจายตามชั่วโมง</h3>
            </div>
            <div class="card-body">
                <canvas id="hourlyChart" height="150"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Top Items -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">ไอเท็มที่ส่งมากที่สุด (Top 10)</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>รหัสไอเท็ม</th>
                                <th>จำนวนส่ง</th>
                                <th>จำนวนรับ</th>
                                <th>อัตราการรับ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($analytics['top_items'])): ?>
                                <?php foreach ($analytics['top_items'] as $item): ?>
                                    <tr>
                                        <td><?php echo $item['ItemKindIdx']; ?></td>
                                        <td><?php echo number_format($item['count']); ?></td>
                                        <td><?php echo number_format($item['received_count']); ?></td>
                                        <td>
                                            <?php 
                                            $rate = $item['count'] > 0 ? ($item['received_count'] / $item['count']) * 100 : 0;
                                            echo number_format($rate, 1) . '%';
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="4" class="text-center">ไม่มีข้อมูล</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Daily Activity Chart
const dailyCtx = document.getElementById('dailyActivityChart').getContext('2d');
const dailyData = <?php echo json_encode($analytics['daily_activity'] ?? []); ?>;

new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: dailyData.map(d => d.mail_date).reverse(),
        datasets: [{
            label: 'เมลล์ทั้งหมด',
            data: dailyData.map(d => d.total_mails).reverse(),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'เมลล์ที่มี Alz',
            data: dailyData.map(d => d.mails_with_alz).reverse(),
            borderColor: 'rgb(255, 205, 86)',
            backgroundColor: 'rgba(255, 205, 86, 0.2)',
            tension: 0.1
        }, {
            label: 'เมลล์ที่มีไอเท็ม',
            data: dailyData.map(d => d.mails_with_items).reverse(),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusData = <?php echo json_encode($analytics['status_distribution'] ?? []); ?>;

new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: statusData.map(d => d.status),
        datasets: [{
            data: statusData.map(d => d.count),
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Hourly Distribution Chart
const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
const hourlyData = <?php echo json_encode($analytics['hourly_distribution'] ?? []); ?>;

new Chart(hourlyCtx, {
    type: 'bar',
    data: {
        labels: hourlyData.map(d => d.hour_of_day + ':00'),
        datasets: [{
            label: 'จำนวนเมลล์',
            data: hourlyData.map(d => d.mail_count),
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
