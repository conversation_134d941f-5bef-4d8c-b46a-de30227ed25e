<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-download"></i> ส่งออกข้อมูลตัวละคร
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_charecter/character-statistics" class="btn btn-primary btn-sm">
            <i class="fal fa-chart-bar"></i> กลับสถิติ
        </a>
    </div>
</div>

<div class="row">
    <!-- Export Form -->
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">เลือกข้อมูลที่ต้องการส่งออก</h3>
            </div>
            <div class="card-body">
                <form id="exportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dataType">ประเภทข้อมูล</label>
                                <select class="form-control" id="dataType" name="data_type" required>
                                    <option value="">-- เลือกประเภทข้อมูล --</option>
                                    <option value="character_list">รายชื่อตัวละคร</option>
                                    <option value="character_stats">สถิติตัวละคร</option>
                                    <option value="online_players">ผู้เล่นออนไลน์</option>
                                    <option value="new_characters">ตัวละครใหม่</option>
                                    <option value="top_players">ผู้เล่นอันดับต้น</option>
                                    <option value="class_distribution">การกระจายคลาส</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="exportFormat">รูปแบบไฟล์</label>
                                <select class="form-control" id="exportFormat" name="format" required>
                                    <option value="">-- เลือกรูปแบบ --</option>
                                    <option value="csv">CSV (Excel)</option>
                                    <option value="json">JSON</option>
                                    <option value="excel">Excel (XLS)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateFrom">วันที่เริ่มต้น</label>
                                <input type="date" class="form-control" id="dateFrom" name="date_from" 
                                       value="<?php echo date('Y-m-d', strtotime('-30 days')); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateTo">วันที่สิ้นสุด</label>
                                <input type="date" class="form-control" id="dateTo" name="date_to" 
                                       value="<?php echo date('Y-m-d'); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="levelMin">เลเวลต่ำสุด</label>
                                <input type="number" class="form-control" id="levelMin" name="level_min" 
                                       value="1" min="1" max="999">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="levelMax">เลเวลสูงสุด</label>
                                <input type="number" class="form-control" id="levelMax" name="level_max" 
                                       value="999" min="1" max="999">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="recordLimit">จำนวนรายการสูงสุด</label>
                        <select class="form-control" id="recordLimit" name="limit">
                            <option value="100">100 รายการ</option>
                            <option value="500">500 รายการ</option>
                            <option value="1000" selected>1,000 รายการ</option>
                            <option value="5000">5,000 รายการ</option>
                            <option value="0">ทั้งหมด (อาจใช้เวลานาน)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="includeDetails" checked>
                            <label class="custom-control-label" for="includeDetails">
                                รวมรายละเอียดเพิ่มเติม (Alz, PlayTime, World)
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-success">
                            <i class="fal fa-download"></i> ส่งออกข้อมูล
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewData()">
                            <i class="fal fa-eye"></i> ดูตัวอย่าง
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fal fa-undo"></i> รีเซ็ต
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Export Info & Quick Actions -->
    <div class="col-xl-4">
        <div class="card mb-3">
            <div class="card-header">
                <h3 class="card-title">ข้อมูลการส่งออก</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>ประเภทข้อมูลที่สามารถส่งออกได้:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fal fa-users text-primary"></i> <strong>รายชื่อตัวละคร:</strong> ข้อมูลพื้นฐานตัวละคร</li>
                        <li><i class="fal fa-chart-bar text-success"></i> <strong>สถิติตัวละคร:</strong> สรุปข้อมูลสถิติ</li>
                        <li><i class="fal fa-circle text-info"></i> <strong>ผู้เล่นออนไลน์:</strong> ตัวละครที่ออนไลน์</li>
                        <li><i class="fal fa-star text-warning"></i> <strong>ตัวละครใหม่:</strong> ตัวละครที่สร้างใหม่</li>
                        <li><i class="fal fa-trophy text-danger"></i> <strong>ผู้เล่นอันดับต้น:</strong> ตัวละครเลเวลสูง</li>
                        <li><i class="fal fa-layer-group text-secondary"></i> <strong>การกระจายคลาส:</strong> สถิติตามคลาส</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <small>
                        <i class="fal fa-info-circle"></i>
                        การส่งออกข้อมูลจำนวนมากอาจใช้เวลาสักครู่ กรุณารอสักครู่
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Quick Export Buttons -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ส่งออกด่วน</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="quickExport('character_list', 'csv', 100)">
                        <i class="fal fa-users"></i> ตัวละคร 100 รายการ (CSV)
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="quickExport('online_players', 'csv', 0)">
                        <i class="fal fa-circle"></i> ผู้เล่นออนไลน์ทั้งหมด (CSV)
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="quickExport('new_characters', 'csv', 50)">
                        <i class="fal fa-star"></i> ตัวละครใหม่ 50 รายการ (CSV)
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="quickExport('top_players', 'json', 100)">
                        <i class="fal fa-trophy"></i> Top 100 Players (JSON)
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="quickExport('character_stats', 'excel', 0)">
                        <i class="fal fa-chart-bar"></i> สถิติรวม (Excel)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ตัวอย่างข้อมูล</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">กำลังโหลด...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ปิด</button>
                <button type="button" class="btn btn-success" onclick="proceedWithExport()">
                    <i class="fal fa-download"></i> ดำเนินการส่งออก
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('exportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    performExport();
});

function performExport() {
    const formData = new FormData(document.getElementById('exportForm'));
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        params.append(key, value);
    }
    
    // Add additional parameters
    params.append('type', document.getElementById('exportFormat').value);
    params.append('data', document.getElementById('dataType').value);
    params.append('include_details', document.getElementById('includeDetails').checked ? '1' : '0');
    
    // Show loading
    showLoading('กำลังส่งออกข้อมูล...');
    
    // Create download link
    const url = 'files/manager_charecter/export/character-export.php?' + params.toString();
    
    // Create temporary link and click it
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Hide loading after a delay
    setTimeout(() => {
        hideLoading();
        showSuccess('การส่งออกข้อมูลเสร็จสิ้น');
    }, 2000);
}

function quickExport(dataType, format, limit) {
    const params = new URLSearchParams({
        'type': format,
        'data': dataType,
        'limit': limit,
        'date_from': document.getElementById('dateFrom').value,
        'date_to': document.getElementById('dateTo').value,
        'level_min': 1,
        'level_max': 999,
        'include_details': '1'
    });
    
    showLoading('กำลังส่งออกข้อมูล...');
    
    const url = 'files/manager_charecter/export/character-export.php?' + params.toString();
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
        hideLoading();
        showSuccess('การส่งออกข้อมูลเสร็จสิ้น');
    }, 2000);
}

function previewData() {
    const dataType = document.getElementById('dataType').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    
    if (!dataType) {
        alert('กรุณาเลือกประเภทข้อมูล');
        return;
    }
    
    $('#previewModal').modal('show');
    
    // Simulate preview data loading
    setTimeout(() => {
        const previewContent = document.getElementById('previewContent');
        previewContent.innerHTML = `
            <div class="alert alert-info">
                <strong>ตัวอย่างข้อมูล:</strong> ${getDataTypeName(dataType)}<br>
                <strong>ช่วงวันที่:</strong> ${dateFrom} ถึง ${dateTo}
            </div>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="thead-light">
                        <tr>
                            ${getPreviewHeaders(dataType)}
                        </tr>
                    </thead>
                    <tbody>
                        ${getPreviewRows(dataType)}
                        <tr>
                            <td colspan="100%" class="text-center text-muted">... และอีก X รายการ</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="alert alert-warning">
                <small>นี่เป็นเพียงตัวอย่างข้อมูล ข้อมูลจริงจะแสดงเมื่อทำการส่งออก</small>
            </div>
        `;
    }, 1000);
}

function getDataTypeName(dataType) {
    const names = {
        'character_list': 'รายชื่อตัวละคร',
        'character_stats': 'สถิติตัวละคร',
        'online_players': 'ผู้เล่นออนไลน์',
        'new_characters': 'ตัวละครใหม่',
        'top_players': 'ผู้เล่นอันดับต้น',
        'class_distribution': 'การกระจายคลาส'
    };
    return names[dataType] || dataType;
}

function getPreviewHeaders(dataType) {
    const headers = {
        'character_list': '<th>CharacterIdx</th><th>Name</th><th>Level</th><th>Class</th><th>Alz</th>',
        'character_stats': '<th>Metric</th><th>Value</th><th>Description</th>',
        'online_players': '<th>Name</th><th>Level</th><th>World</th><th>Channel</th>',
        'new_characters': '<th>Name</th><th>Level</th><th>Class</th><th>Create Date</th>',
        'top_players': '<th>Rank</th><th>Name</th><th>Level</th><th>Alz</th><th>Playtime</th>',
        'class_distribution': '<th>Class</th><th>Count</th><th>Percentage</th><th>Avg Level</th>'
    };
    return headers[dataType] || '<th>Column 1</th><th>Column 2</th><th>Column 3</th>';
}

function getPreviewRows(dataType) {
    const rows = {
        'character_list': '<tr><td>12345</td><td>TestPlayer</td><td>150</td><td>Warrior</td><td>1,000,000</td></tr>',
        'character_stats': '<tr><td>Total Characters</td><td>5,432</td><td>จำนวนตัวละครทั้งหมด</td></tr>',
        'online_players': '<tr><td>OnlinePlayer</td><td>200</td><td>1</td><td>5</td></tr>',
        'new_characters': '<tr><td>NewPlayer</td><td>1</td><td>Blader</td><td>2024-01-01 10:30:00</td></tr>',
        'top_players': '<tr><td>1</td><td>TopPlayer</td><td>250</td><td>50,000,000</td><td>500 ชม.</td></tr>',
        'class_distribution': '<tr><td>Warrior</td><td>1,234</td><td>25.5%</td><td>125.3</td></tr>'
    };
    return rows[dataType] || '<tr><td>Sample Data 1</td><td>Sample Data 2</td><td>Sample Data 3</td></tr>';
}

function proceedWithExport() {
    $('#previewModal').modal('hide');
    performExport();
}

function showLoading(message) {
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    
    overlay.innerHTML = `
        <div class="bg-white p-4 rounded text-center">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showSuccess(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fal fa-check-circle"></i> ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Set max date to today
document.getElementById('dateTo').max = new Date().toISOString().split('T')[0];
document.getElementById('dateFrom').max = new Date().toISOString().split('T')[0];

// Validate date range
document.getElementById('dateFrom').addEventListener('change', function() {
    document.getElementById('dateTo').min = this.value;
});

document.getElementById('dateTo').addEventListener('change', function() {
    document.getElementById('dateFrom').max = this.value;
});

// Validate level range
document.getElementById('levelMin').addEventListener('change', function() {
    document.getElementById('levelMax').min = this.value;
});

document.getElementById('levelMax').addEventListener('change', function() {
    document.getElementById('levelMin').max = this.value;
});
</script>
