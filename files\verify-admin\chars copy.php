<?php $user->restrictionUser(true, $conn); ?>
<header class="page-header">
    <h2><?php echo PT_MANAGECHARS; ?></h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span><?php echo PT_MANAGECHARS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<div class="row">
    <div class="col-lg-12">
        <section class="panel">

            <div class="panel-body">
                <div class="col-lg-12" style="margin: 10px 5px;">
                    <form method="post" action="?url=manager/cresults" class="form-inline pull-right">
                        <div class="form-group">
                            <input type="text" class="form-control" name="search" placeholder="Search by เลขตัวละคร">
                            <input type="submit" class="btn btn-info" name="btn_search" value="Search">
                        </div>
                    </form>
                </div>

                <div class="panel panel-default">
                    <div class="panel-body no-padd" style="padding-bottom: 0;">
                        <div class="col-lg-12 no-padd">
                            <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
                            <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                            <?php } ?>
                            <table class="table table-bordered table-striped mb-none" id="datatable-ajax" data-url="files/verify-admin/test.php">
                                <thead>
                                    <tr>
                                        <th>Character</th>
                                        <th>Name</th>
                                        <th>LEV</th>
                                        <th>Alz</th>
                                        <th>แมพ</th>
                                        <th>อาชีพ</th>
                                        <th>Rank</th>
                                        <th>แนล</th>
                                        <th>ประเทศ</th>
                                        <th>Warid</th>
                                        <th><?php echo T_ACTION; ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                        //header('Content-Type: text/html; charset=windows-874');
                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 100;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY LoginTime DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) { ?>
                                    <tr <?php
                            $selectUsersDataChar = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx = '$row[0]'";
                            $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersDataChar, array());
                            $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                            $style = $userLogin->decode_style($row[12]);
                            $usernum = floor($selectUsersDataFetch['CharacterIdx']/8);
                            $htmlimg = '<img src="assets/images/icons/vb.png">';

                            $selectUsersDataID = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = '$usernum'";
                            $selectUsersDataIDQuery = sqlsrv_query($conn, $selectUsersDataID, array());
                            $selectUsersDataIDFetch = sqlsrv_fetch_array($selectUsersDataIDQuery, SQLSRV_FETCH_ASSOC);

                            if ($selectUsersDataIDFetch['AuthType'] == '2' ||  $selectUsersDataIDFetch['AuthType'] == '3' ||  $selectUsersDataIDFetch['AuthType'] == '4') {   
								echo ' class="bg-red text-white"';
                            }
                            ?>>
                                        
                                        <td><?php echo $row[0]; ?></td>
                                        <td><?php echo $userLogin->thaitrans($selectUsersDataFetch['Name']); ?></td>
                                        <td><?php echo $row[2]; ?></td>
                                        <td><?php echo $row[9]; ?></td>
                                        <td><?php echo $row[10]; ?></td>
                                        <td><img src="assets/images/cabal/class/<?php echo $style['Class_Name']; ?>.gif"
                                                alt="--" class="img-circle" style="width:18px; height: 18px;"></td>
                                        <td><?php echo $row[25]; ?></td>
                                        <td><img src="assets/images/cabal/<?php echo  $userLogin->nation($row[30]); ?>.gif"
                                                data-toggle="tooltip"
                                                data-title="<?php echo  $userLogin->nation($row[30]); ?>!"
                                                title="<?php echo  $userLogin->nation($row[30]); ?>!" class="img-circle"
                                                style="width:18px; height: 18px;"></td>
                                        <td><?php echo $row[38]; ?></td>
                                        <td><?php echo $warid = $userLogin->chartowar($row[0]); ?></td>
                                        <td> <a class="text-info"
                                                href="?url=manager/see-chars&charid=<?php echo $row[0]; ?>"
                                                data-toggle="tooltip" data-placement="left" title=""
                                                data-original-title="รายระเอียด"><i class="fa fa-info-circle"></i></a>
                                            <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                            <?php } ?>
                                            <?php if ($selectUsersDataIDFetch['AuthType'] == '2' || $selectUsersDataIDFetch['AuthType'] == '3' || $selectUsersDataIDFetch['AuthType'] == '4') { ?>
                                            <a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=unban-wait"
                                                data-toggle="tooltip" data-placement="left" title=""
                                                data-original-title="โดนแบน"><span class="text-danger"><i
                                                        class="fa fa-ban"></i></span></a>
                                            <?php } else { ?>
                                            <a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=wait"
                                                data-toggle="tooltip" data-placement="left" title=""
                                                data-original-title="ต้องการแบน"><span class="text-success"><i
                                                        class="fa fa-check"></i></span></a>
                                            <?php } ?>


                                        </td>
                                    </tr>
                                    <?php
                        }
                        ?>
                                </tbody>
                            </table>
                          
                        </div>
                    </div>
                </div>
            </div>
    </div>
    </section>
    <script type="text/javascript">
    $(document).ready(function() {
        $('.pagination').pagination({
            items: < ? php echo $rowsReturned; ? > ,
            itemsOnPage : < ? php echo $rowsPerPage; ? > ,
            cssStyle : 'light-theme',
            currentPage: < ? php echo $pageNum; ? > ,
            hrefTextPrefix : '?url=manager/chars&pageNum='
        });
    });
    </script>
