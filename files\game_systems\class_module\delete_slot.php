<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

$data = json_decode(file_get_contents('php://input'), true);
$eventId = intval($data['eventId']);
$slot = intval($data['slot']);

$query = "DELETE FROM EventData.dbo.cabal_ems_event_npcitemshop_table WHERE EventID = ? AND SlotIdx = ?";
$params = [$eventId, $slot];
sqlsrv_query($conn, $query, $params);

echo json_encode(["status" => "deleted"]);
?>