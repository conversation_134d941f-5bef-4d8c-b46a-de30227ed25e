<?php $user->restrictionUser(true, $conn); ?>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager Mail item <span class="fw-300"><i>จัดการเมลล์ไอเท็มแลกแคส</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="table-responsive-lg">
                    <!-- datatable start -->
                    <table class="table table-bordered table-hover table-striped w-100">
                        <thead>
                            <tr>
                                <th>ReceivedMailID</th>
                                <th>DeliveryTime</th>
                                <th>ItemKindIdx</th>
                                <th>ItemOption</th>
                                <th>SenderCharIdx</th>
                                <th>ใช้งาน</th>
                                <th>Action</th>
                                <!--<th><?php echo T_ACTION; ?></th>-->
                            </tr>
                        </thead>
                        <tbody>
                            <?php

                                // generic function to get page
                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                    $rows = array();
                                    $i = 0;
                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                        array_push($rows, $row);
                                        $i++;
                                    }
                                    return $rows;
                                }

                                // Set the number of rows to be returned on a page.
                                $rowsPerPage = 20;

                                // Define and execute the query.  
                                // Note that the query is executed with a "scrollable" cursor.
                                $sql = "SELECT * FROM  [".DATABASE_SV."].[dbo].cabal_mail_received_table where ReceiverCharIdx = '8' AND ItemKindIdx = '3090' ORDER BY DeliveryTime DESC";

                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                if (!$stmt)
                                    die(print_r(sqlsrv_errors(), true));

                                // Get the total number of rows returned by the query.
                                $rowsReturned = sqlsrv_num_rows($stmt);
                                if ($rowsReturned === false)
                                    die(print_r(sqlsrv_errors(), true));
                                elseif ($rowsReturned == 0) {
                                    echo W_NOTHING_RETURNED;
                                    //exit();
                                } else {
                                    /* Calculate number of pages. */
                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                }

                                // Display the selected page of data.
                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                foreach ($page as $row) {
                            ?>
                            <tr>
                                <td><?php echo $row[1]; ?></td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
                                <td><?php echo $row[10]; ?></td>
                                <td><?php echo $row[11]; ?></td>
                                <td><?php echo $row[14]; ?></td>
                                <td><span
                                        class="
								<?php echo $label = ($row[9] == '1' ? ' badge badge-danger badge-pill' : ($row[9] == '0' ? ' badge badge-success badge-pill' : 'badge badge-secondary badge-pill'));?>">
                                        <?php echo $status = ($row[9] == '1' ? 'ใช้งานแล้ว' : ($row[9] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
                                </td>
                                <td>
                                    <form method="post" name="j_add_itemtime" action="">
                                        <div class="j_alert"></div>
                                        <input type="hidden" name="mailid" value="<?php echo $row[1]; ?>">
                                        <input type="hidden" name="charid" value="<?php echo $row[14]; ?>">
                                        <button type="submit"
                                            class="btn btn-primary btn-sm waves-effect waves-themed">ส่งไอเท็ม</button>

                                    </form>
                                </td>

                            </tr>
                            <?php	} ?>
                        </tbody>

                    </table>
                    <!-- datatable end -->
                </div>
            </div>
        </div>
    </div>
</div>