
<?php
include '../../_app/dbinfo.inc.php';
include '../../_app/general_config.inc.php';
require '../../_app/php/userLogin.class.php';
$userLogin = new userLogged();
$userLogin->exitHome(); // when you click in "Log out"
//header('Content-Type: text/html; charset=windows-874');
                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $data = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($data, $row);
                                $i++;
                            }
                            return $data;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 100000;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY LoginTime,LogoutTime desc";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);
            
                        foreach ($page as $row) { 

                            $name = $userLogin->thaitrans($row[1]);
                            $style = $userLogin->decode_style($row[12]);
                            $nation = $userLogin->nation($row[30]);
                            $warid = $userLogin->chartowar($row[0]);
                            $usernum = floor($row[0]/8);

                            $selectUsersDataID = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = '$usernum'";
                            $selectUsersDataIDQuery = sqlsrv_query($conn, $selectUsersDataID, array());
                            $selectUsersDataIDFetch = sqlsrv_fetch_array($selectUsersDataIDQuery, SQLSRV_FETCH_ASSOC);

                            if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { 
                             } 
                            if ($selectUsersDataIDFetch['AuthType'] == '2' || $selectUsersDataIDFetch['AuthType'] == '3' || $selectUsersDataIDFetch['AuthType'] == '4') { 
                                $ckban = '<a href="?url=manager/see-player&id='.$usernum.'&ban=unban-wait"><span class="text-danger"><i class="fa fa-ban"></i></span></a>';
                                } else {
                                $ckban = '<a href="?url=manager/see-player&id='.$usernum.'&ban=wait"><span class="text-success"><i class="fa fa-check"></i></span></a>';
                            } 
                            $ckchar = '<a href="?url=manager/see-chars&charid='.$row[0].'"><i class="fa fa-info-circle"></i></a>';
                            $action = ' '.$ckchar.' '.$ckban.'';
                            $data[] = array(
                                $row[0],
                                $name,
                                $row[2],
                                $row[9],
                                $row[10],
                                $style['Class_Name'],
                                $row[25],
                                $row[28],
                                $nation,
                                $warid,
                                $action,
                            );
                        }
                        $response = array(
                            "aaData" => $data
                        );
                       echo json_encode($response,JSON_UNESCAPED_UNICODE);
  ?>


