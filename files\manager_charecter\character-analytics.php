<?php
$user->restrictionUser(true, $conn);

// Enable error reporting for debugging (remove in production)
if (isset($_GET['debug'])) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
?>

<?php
// Function to get detailed character analytics
function getCharacterAnalytics($conn, $days = 7) {
    $analytics = array();

    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    if (!$conn) {
        error_log("Database connection is null in getCharacterAnalytics");
        return $analytics;
    }

    try {
        // Daily character creation for the last N days
        $sql = "SELECT
                    CAST(CreateDate AS DATE) as create_date,
                    COUNT(*) as new_characters
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())
                GROUP BY CAST(CreateDate AS DATE)
                ORDER BY create_date DESC";

        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['daily_creation'][] = $row;
            }
        } else {
            error_log("Daily creation query failed: " . print_r(sqlsrv_errors(), true));
        }
        
        // Daily login activity
        $sql = "SELECT 
                    CAST(LoginTime AS DATE) as login_date,
                    COUNT(DISTINCT CharacterIdx) as unique_logins,
                    COUNT(*) as total_logins
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LoginTime >= DATEADD(day, -?, GETDATE())
                GROUP BY CAST(LoginTime AS DATE)
                ORDER BY login_date DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['daily_logins'][] = $row;
            }
        }
        
        // Hourly online distribution
        $sql = "SELECT 
                    DATEPART(hour, LoginTime) as hour_of_day,
                    COUNT(*) as login_count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LoginTime >= DATEADD(day, -7, GETDATE())
                GROUP BY DATEPART(hour, LoginTime)
                ORDER BY hour_of_day";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['hourly_distribution'][] = $row;
            }
        }
        
        // Level progression analysis
        $sql = "SELECT 
                    LEV as level,
                    COUNT(*) as character_count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LEV BETWEEN 1 AND 200
                GROUP BY LEV
                ORDER BY LEV";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['level_progression'][] = $row;
            }
        }
        
        // World distribution
        $sql = "SELECT 
                    WorldIdx,
                    COUNT(*) as character_count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE WorldIdx IS NOT NULL
                GROUP BY WorldIdx
                ORDER BY character_count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['world_distribution'][] = $row;
            }
        }
        
        // Playtime analysis
        $sql = "SELECT
                    CASE
                        WHEN PlayTime < 3600 THEN '< 1 hour'
                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                        WHEN PlayTime BETWEEN 36001 AND 180000 THEN '10-50 hours'
                        WHEN PlayTime BETWEEN 180001 AND 720000 THEN '50-200 hours'
                        WHEN PlayTime > 720000 THEN '200+ hours'
                        ELSE 'Unknown'
                    END as playtime_range,
                    COUNT(*) as count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                GROUP BY
                    CASE
                        WHEN PlayTime < 3600 THEN '< 1 hour'
                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                        WHEN PlayTime BETWEEN 36001 AND 180000 THEN '10-50 hours'
                        WHEN PlayTime BETWEEN 180001 AND 720000 THEN '50-200 hours'
                        WHEN PlayTime > 720000 THEN '200+ hours'
                        ELSE 'Unknown'
                    END
                ORDER BY count DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['playtime_distribution'][] = $row;
            }
        }

        // เพิ่มการวิเคราะห์ครบถ้วน - Level Analysis (ตามช่วงวัน)
        $sql = "SELECT
                    COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                    COUNT(CASE WHEN LEV BETWEEN 51 AND 100 THEN 1 END) as level_51_100,
                    COUNT(CASE WHEN LEV BETWEEN 101 AND 150 THEN 1 END) as level_101_150,
                    COUNT(CASE WHEN LEV BETWEEN 151 AND 200 THEN 1 END) as level_151_200,
                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                    MAX(LEV) as max_level,
                    MIN(LEV) as min_level,
                    COUNT(*) as total_characters
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['level_analysis'] = $row;
            }
        }

        // Wealth Analysis (Alz) - ตามช่วงวัน
        $sql = "SELECT
                    COUNT(CASE WHEN Alz = 0 THEN 1 END) as no_alz,
                    COUNT(CASE WHEN Alz BETWEEN 1 AND 1000000 THEN 1 END) as alz_1m,
                    COUNT(CASE WHEN Alz BETWEEN 1000001 AND 10000000 THEN 1 END) as alz_10m,
                    COUNT(CASE WHEN Alz BETWEEN 10000001 AND 100000000 THEN 1 END) as alz_100m,
                    COUNT(CASE WHEN Alz BETWEEN 100000001 AND 1000000000 THEN 1 END) as alz_1b,
                    COUNT(CASE WHEN Alz > 1000000000 THEN 1 END) as alz_1b_plus,
                    AVG(CAST(Alz AS BIGINT)) as avg_alz,
                    MAX(Alz) as max_alz,
                    SUM(CAST(Alz AS BIGINT)) as total_alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['wealth_analysis'] = $row;
            }
        }

        // Activity Analysis (PlayTime) - ตามช่วงวัน
        $sql = "SELECT
                    COUNT(CASE WHEN PlayTime = 0 THEN 1 END) as never_played,
                    COUNT(CASE WHEN PlayTime BETWEEN 1 AND 3600 THEN 1 END) as played_1h,
                    COUNT(CASE WHEN PlayTime BETWEEN 3601 AND 36000 THEN 1 END) as played_10h,
                    COUNT(CASE WHEN PlayTime BETWEEN 36001 AND 360000 THEN 1 END) as played_100h,
                    COUNT(CASE WHEN PlayTime BETWEEN 360001 AND 3600000 THEN 1 END) as played_1000h,
                    COUNT(CASE WHEN PlayTime > 3600000 THEN 1 END) as played_1000h_plus,
                    AVG(CAST(PlayTime AS FLOAT)) as avg_playtime,
                    MAX(PlayTime) as max_playtime,
                    SUM(CAST(PlayTime AS BIGINT)) as total_playtime
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['activity_analysis'] = $row;
            }
        }

        // Character Creation Analysis - ตามช่วงวัน (ลดความซับซ้อน)
        $sql = "SELECT
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 1 THEN 1 END) as created_today,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 7 THEN 1 END) as created_week,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 30 THEN 1 END) as created_month,
                    COUNT(*) as created_in_period,
                    CONVERT(VARCHAR, MIN(CreateDate), 103) as oldest_character,
                    CONVERT(VARCHAR, MAX(CreateDate), 103) as newest_character
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['creation_analysis'] = $row;
            }
        }

        // Character Progression Analysis - ตามช่วงวัน
        $sql = "SELECT
                    COUNT(CASE WHEN LEV >= 1 AND LEV <= 10 THEN 1 END) as beginner,
                    COUNT(CASE WHEN LEV >= 11 AND LEV <= 50 THEN 1 END) as novice,
                    COUNT(CASE WHEN LEV >= 51 AND LEV <= 100 THEN 1 END) as intermediate,
                    COUNT(CASE WHEN LEV >= 101 AND LEV <= 150 THEN 1 END) as advanced,
                    COUNT(CASE WHEN LEV >= 151 AND LEV <= 200 THEN 1 END) as expert,
                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as master
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['progression_analysis'] = $row;
            }
        }

        // Honor Class Analysis - คำนวณจาก Reputation Points
        $sql = "SELECT
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                        ELSE 'No Class'
                    END as honor_class,
                    COUNT(*) as count,
                    AVG(CAST(Reputation AS BIGINT)) as avg_reputation
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())
                GROUP BY
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                        ELSE 'No Class'
                    END
                ORDER BY
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 0
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 0
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 1
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 2
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 3
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 4
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 5
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 6
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 7
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 8
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 9
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 10
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 11
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 12
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 13
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 14
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 15
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 16
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 17
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 18
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 19
                        WHEN Reputation >= 2000000000 THEN 20
                        ELSE 0
                    END";
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['honor_distribution'][] = $row;
            }
        }
        
    } catch (Exception $e) {
        error_log("Character analytics error: " . $e->getMessage());
    }
    
    return $analytics;
}

$days = isset($_GET['days']) ? (int)$_GET['days'] : 7;
$analytics = getCharacterAnalytics($conn, $days);

// Debug information
if (isset($_GET['debug'])) {
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;'>";
    echo "<h4>Debug Information:</h4>";
    echo "Days: " . $days . "<br>";
    echo "Analytics data count: " . count($analytics) . "<br>";
    if (!empty($analytics)) {
        echo "Daily creation records: " . (isset($analytics['daily_creation']) ? count($analytics['daily_creation']) : 0) . "<br>";
        echo "Daily login records: " . (isset($analytics['daily_logins']) ? count($analytics['daily_logins']) : 0) . "<br>";
        echo "Playtime distribution records: " . (isset($analytics['playtime_distribution']) ? count($analytics['playtime_distribution']) : 0) . "<br>";
    }
    echo "</div>";
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-analytics"></i> การวิเคราะห์ตัวละคร
    </h1>
    <div class="subheader-block">
        <div class="btn-group" role="group">
            <a href="?url=manager_charecter/character-analytics&days=7" class="btn btn-sm <?php echo $days == 7 ? 'btn-primary' : 'btn-outline-primary'; ?>">7 วัน</a>
            <a href="?url=manager_charecter/character-analytics&days=30" class="btn btn-sm <?php echo $days == 30 ? 'btn-primary' : 'btn-outline-primary'; ?>">30 วัน</a>
            <a href="?url=manager_charecter/character-analytics&days=90" class="btn btn-sm <?php echo $days == 90 ? 'btn-primary' : 'btn-outline-primary'; ?>">90 วัน</a>
        </div>
        <a href="?url=manager_charecter/character-statistics" class="btn btn-success btn-sm ml-2">
            <i class="fal fa-chart-bar"></i> กลับสถิติ
        </a>
    </div>
</div>

<div class="row">
    <!-- Daily Activity Chart -->
    <div class="col-xl-8">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมรายวัน (<?php echo $days; ?> วันที่ผ่านมา)</h3>
            </div>
            <div class="card-body">
                <canvas id="dailyActivityChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Playtime Distribution -->
    <div class="col-xl-4">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การกระจายเวลาเล่น</h3>
            </div>
            <div class="card-body">
                <canvas id="playtimeChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Character Analysis Section -->
<div class="row">
    <!-- Level Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-chart-line text-success"></i> การวิเคราะห์ระดับ (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['level_analysis'])): ?>
                    <?php $level = $analytics['level_analysis']; ?>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h5 class="text-success"><?php echo number_format($level['level_1_50']); ?></h5>
                                    <small>Level 1-50</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h5 class="text-info"><?php echo number_format($level['level_101_150']); ?></h5>
                                    <small>Level 101-150</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h5 class="text-warning"><?php echo number_format($level['level_200_plus']); ?></h5>
                                    <small>Level 200+</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p><strong>ตัวละครทั้งหมด:</strong> <?php echo number_format($level['total_characters'] ?? 0); ?></p>
                        <?php if (($level['avg_level'] ?? 0) > 0): ?>
                            <p><strong>เลเวลเฉลี่ย:</strong> <?php echo number_format($level['avg_level'], 1); ?></p>
                        <?php endif; ?>
                        <?php if (($level['max_level'] ?? 0) > 0): ?>
                            <p><strong>เลเวลสูงสุด:</strong> <?php echo number_format($level['max_level']); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Wealth Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-coins text-warning"></i> การวิเคราะห์ความมั่งคั่ง (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['wealth_analysis'])): ?>
                    <?php $wealth = $analytics['wealth_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>ไม่มี Alz</td>
                                <td><span class="badge badge-secondary"><?php echo number_format($wealth['no_alz'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>1M-10M Alz</td>
                                <td><span class="badge badge-info"><?php echo number_format($wealth['alz_10m'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>100M-1B Alz</td>
                                <td><span class="badge badge-danger"><?php echo number_format($wealth['alz_1b'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>1B+ Alz</td>
                                <td><span class="badge badge-dark"><?php echo number_format($wealth['alz_1b_plus'] ?? 0); ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <?php if (($wealth['avg_alz'] ?? 0) > 0): ?>
                            <p><strong>Alz เฉลี่ย:</strong> <?php echo number_format($wealth['avg_alz']); ?></p>
                        <?php endif; ?>
                        <?php if (($wealth['total_alz'] ?? 0) > 0): ?>
                            <p><strong>Alz รวม:</strong> <?php echo number_format($wealth['total_alz']); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Activity Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-clock text-primary"></i> การวิเคราะห์กิจกรรม (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['activity_analysis'])): ?>
                    <?php $activity = $analytics['activity_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>ไม่เคยเล่น</td>
                                <td><span class="badge badge-secondary"><?php echo number_format($activity['never_played'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 1-10 ชม.</td>
                                <td><span class="badge badge-info"><?php echo number_format($activity['played_10h'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 100-1000 ชม.</td>
                                <td><span class="badge badge-danger"><?php echo number_format($activity['played_1000h'] ?? 0); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 1000+ ชม.</td>
                                <td><span class="badge badge-dark"><?php echo number_format($activity['played_1000h_plus'] ?? 0); ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <?php if (($activity['avg_playtime'] ?? 0) > 0): ?>
                            <p><strong>เวลาเล่นเฉลี่ย:</strong> <?php echo number_format($activity['avg_playtime'] / 3600, 1); ?> ชม.</p>
                        <?php endif; ?>
                        <?php if (($activity['total_playtime'] ?? 0) > 0): ?>
                            <p><strong>เวลาเล่นรวม:</strong> <?php echo number_format($activity['total_playtime'] / 3600); ?> ชม.</p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Honor Class Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-crown text-warning"></i> การกระจาย Honor Class (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['honor_distribution'])): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Honor Class</th>
                                    <th>จำนวน</th>
                                    <th>Avg Reputation</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $totalHonor = 0;
                                foreach ($analytics['honor_distribution'] as $honor) {
                                    $totalHonor += $honor['count'];
                                }

                                foreach ($analytics['honor_distribution'] as $honor):
                                    $percentage = $totalHonor > 0 ? ($honor['count'] / $totalHonor) * 100 : 0;
                                    $honorClass = $honor['honor_class'];

                                    // กำหนดสี
                                    $colorClass = 'secondary';
                                    if (strpos($honorClass, 'Class') !== false) {
                                        $classNum = (int)str_replace('Class ', '', $honorClass);
                                        if ($classNum == 0) $colorClass = 'secondary';
                                        elseif ($classNum >= 1 && $classNum <= 5) $colorClass = 'success';
                                        elseif ($classNum >= 6 && $classNum <= 10) $colorClass = 'info';
                                        elseif ($classNum >= 11 && $classNum <= 15) $colorClass = 'warning';
                                        elseif ($classNum >= 16 && $classNum <= 19) $colorClass = 'danger';
                                        elseif ($classNum >= 20) $colorClass = 'dark';
                                    }
                                ?>
                                    <tr>
                                        <td><span class="badge badge-<?php echo $colorClass; ?>"><?php echo htmlspecialchars($honorClass); ?></span></td>
                                        <td><?php echo number_format($honor['count']); ?> <small>(<?php echo number_format($percentage, 1); ?>%)</small></td>
                                        <td>
                                            <?php
                                            $avgRep = $honor['avg_reputation'] ?? 0;
                                            if ($avgRep >= 1000000000) {
                                                echo number_format($avgRep / 1000000000, 1) . 'B';
                                            } elseif ($avgRep >= 1000000) {
                                                echo number_format($avgRep / 1000000, 1) . 'M';
                                            } elseif ($avgRep >= 1000) {
                                                echo number_format($avgRep / 1000, 1) . 'K';
                                            } elseif ($avgRep > 0) {
                                                echo number_format($avgRep);
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Character Progression -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-chart-bar text-success"></i> การวิเคราะห์ความก้าวหน้า (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['progression_analysis'])): ?>
                    <?php $progression = $analytics['progression_analysis']; ?>
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="text-success"><?php echo number_format($progression['beginner'] ?? 0); ?></h6>
                                    <small>Beginner (1-10)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="text-info"><?php echo number_format($progression['novice'] ?? 0); ?></h6>
                                    <small>Novice (11-50)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h6 class="text-warning"><?php echo number_format($progression['intermediate'] ?? 0); ?></h6>
                                    <small>Intermediate (51-100)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <h6 class="text-danger"><?php echo number_format($progression['advanced'] ?? 0); ?></h6>
                                    <small>Advanced (101-150)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="text-primary"><?php echo number_format($progression['expert'] ?? 0); ?></h6>
                                    <small>Expert (151-200)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-dark">
                                <div class="card-body">
                                    <h6 class="text-dark"><?php echo number_format($progression['master'] ?? 0); ?></h6>
                                    <small>Master (200+)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Creation Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-calendar text-info"></i> การวิเคราะห์การสร้างตัวละคร (<?php echo $days; ?> วันที่ผ่านมา)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($analytics['creation_analysis'])): ?>
                    <?php $creation = $analytics['creation_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>สร้างในช่วง <?php echo $days; ?> วัน</td>
                                <td><span class="badge badge-primary"><?php echo number_format($creation['created_in_period'] ?? 0); ?></span></td>
                            </tr>
                            <?php if ($days >= 1): ?>
                            <tr>
                                <td>สร้างวันนี้</td>
                                <td><span class="badge badge-success"><?php echo number_format($creation['created_today'] ?? 0); ?></span></td>
                            </tr>
                            <?php endif; ?>
                            <?php if ($days >= 7): ?>
                            <tr>
                                <td>สร้างสัปดาห์นี้</td>
                                <td><span class="badge badge-info"><?php echo number_format($creation['created_week'] ?? 0); ?></span></td>
                            </tr>
                            <?php endif; ?>
                            <?php if ($days >= 30): ?>
                            <tr>
                                <td>สร้างเดือนนี้</td>
                                <td><span class="badge badge-warning"><?php echo number_format($creation['created_month'] ?? 0); ?></span></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                    <div class="mt-3">
                        <?php if (!empty($creation['oldest_character'])): ?>
                            <p><strong>ตัวละครเก่าสุด:</strong> <?php echo htmlspecialchars($creation['oldest_character']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($creation['newest_character'])): ?>
                            <p><strong>ตัวละครใหม่สุด:</strong> <?php echo htmlspecialchars($creation['newest_character']); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Hourly Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การกระจายตามชั่วโมง</h3>
            </div>
            <div class="card-body">
                <canvas id="hourlyChart" height="150"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Level Progression -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การกระจายตามเลเวล</h3>
            </div>
            <div class="card-body">
                <canvas id="levelChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- World Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การกระจายตามเวิลด์</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>เวิลด์</th>
                                <th>จำนวนตัวละคร</th>
                                <th>เปอร์เซ็นต์</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($analytics['world_distribution'])): ?>
                                <?php 
                                $total = array_sum(array_column($analytics['world_distribution'], 'character_count'));
                                foreach ($analytics['world_distribution'] as $world): 
                                ?>
                                    <tr>
                                        <td>World <?php echo $world['WorldIdx']; ?></td>
                                        <td><?php echo number_format($world['character_count']); ?></td>
                                        <td>
                                            <?php 
                                            $percentage = $total > 0 ? ($world['character_count'] / $total) * 100 : 0;
                                            echo number_format($percentage, 1) . '%';
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="3" class="text-center">ไม่มีข้อมูล</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity Summary -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">สรุปกิจกรรมล่าสุด</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>วันที่</th>
                                <th>ตัวละครใหม่</th>
                                <th>เข้าเกม</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($analytics['daily_creation']) && isset($analytics['daily_logins'])): ?>
                                <?php 
                                // Merge creation and login data by date
                                $merged_data = array();
                                foreach ($analytics['daily_creation'] as $creation) {
                                    $createDate = $creation['create_date'];
                                    if ($createDate instanceof DateTime) {
                                        $date = $createDate->format('Y-m-d');
                                    } elseif (is_string($createDate)) {
                                        $date = date('Y-m-d', strtotime($createDate));
                                    } else {
                                        $date = date('Y-m-d');
                                    }
                                    $merged_data[$date]['new_characters'] = $creation['new_characters'];
                                }
                                foreach ($analytics['daily_logins'] as $login) {
                                    $loginDate = $login['login_date'];
                                    if ($loginDate instanceof DateTime) {
                                        $date = $loginDate->format('Y-m-d');
                                    } elseif (is_string($loginDate)) {
                                        $date = date('Y-m-d', strtotime($loginDate));
                                    } else {
                                        $date = date('Y-m-d');
                                    }
                                    $merged_data[$date]['unique_logins'] = $login['unique_logins'];
                                }
                                
                                krsort($merged_data); // Sort by date descending
                                $count = 0;
                                foreach ($merged_data as $date => $data):
                                    if ($count >= 7) break; // Show only last 7 days
                                    $count++;
                                ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($date)); ?></td>
                                        <td><?php echo number_format($data['new_characters'] ?? 0); ?></td>
                                        <td><?php echo number_format($data['unique_logins'] ?? 0); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="3" class="text-center">ไม่มีข้อมูล</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Error handling และ debugging
console.log('🔍 Character Analytics Script Loading...');

// ตรวจสอบ dependencies
if (typeof Chart === 'undefined') {
    console.error('❌ Chart.js is not loaded');
    alert('Chart.js library is not loaded. Please check your internet connection or contact administrator.');
}

if (typeof jQuery === 'undefined') {
    console.error('❌ jQuery is not loaded');
}

// Daily Activity Chart
try {
    const dailyCtx = document.getElementById('dailyActivityChart');
    if (!dailyCtx) {
        console.error('❌ dailyActivityChart canvas not found');
        throw new Error('Canvas element not found');
    }
    const dailyCtx2d = dailyCtx.getContext('2d');

<?php
// แปลงข้อมูลวันที่สำหรับ JavaScript
$creationDataForJS = [];
if (isset($analytics['daily_creation'])) {
    foreach ($analytics['daily_creation'] as $creation) {
        $createDate = $creation['create_date'];
        if ($createDate instanceof DateTime) {
            $dateStr = $createDate->format('Y-m-d');
        } elseif (is_string($createDate)) {
            $dateStr = date('Y-m-d', strtotime($createDate));
        } else {
            $dateStr = date('Y-m-d');
        }
        $creationDataForJS[] = [
            'create_date' => $dateStr,
            'new_characters' => $creation['new_characters']
        ];
    }
}

$loginDataForJS = [];
if (isset($analytics['daily_logins'])) {
    foreach ($analytics['daily_logins'] as $login) {
        $loginDate = $login['login_date'];
        if ($loginDate instanceof DateTime) {
            $dateStr = $loginDate->format('Y-m-d');
        } elseif (is_string($loginDate)) {
            $dateStr = date('Y-m-d', strtotime($loginDate));
        } else {
            $dateStr = date('Y-m-d');
        }
        $loginDataForJS[] = [
            'login_date' => $dateStr,
            'unique_logins' => $login['unique_logins']
        ];
    }
}
?>

const creationData = <?php echo json_encode($creationDataForJS); ?>;
const loginData = <?php echo json_encode($loginDataForJS); ?>;

console.log('📊 Data loaded:', {
    creationData: creationData,
    loginData: loginData,
    creationCount: creationData.length,
    loginCount: loginData.length
});

// Prepare data for chart
const dates = [...new Set([
    ...creationData.map(d => d.create_date),
    ...loginData.map(d => d.login_date)
])].sort().reverse();

const creationCounts = dates.map(date => {
    const found = creationData.find(d => d.create_date === date);
    return found ? found.new_characters : 0;
});

const loginCounts = dates.map(date => {
    const found = loginData.find(d => d.login_date === date);
    return found ? found.unique_logins : 0;
});

// สร้าง chart พร้อม error handling
const dailyChart = new Chart(dailyCtx2d, {
    type: 'line',
    data: {
        labels: dates.map(d => new Date(d).toLocaleDateString('th-TH')),
        datasets: [{
            label: 'ตัวละครใหม่',
            data: creationCounts,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'เข้าเกม',
            data: loginCounts,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Playtime Distribution Chart
const playtimeCtx = document.getElementById('playtimeChart').getContext('2d');
const playtimeData = <?php echo json_encode($analytics['playtime_distribution'] ?? []); ?>;

new Chart(playtimeCtx, {
    type: 'doughnut',
    data: {
        labels: playtimeData.map(d => d.playtime_range),
        datasets: [{
            data: playtimeData.map(d => d.count),
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Hourly Distribution Chart
const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
const hourlyData = <?php echo json_encode($analytics['hourly_distribution'] ?? []); ?>;

new Chart(hourlyCtx, {
    type: 'bar',
    data: {
        labels: hourlyData.map(d => d.hour_of_day + ':00'),
        datasets: [{
            label: 'จำนวนการเข้าเกม',
            data: hourlyData.map(d => d.login_count),
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Level Progression Chart
const levelCtx = document.getElementById('levelChart').getContext('2d');
const levelData = <?php echo json_encode($analytics['level_progression'] ?? []); ?>;

// Group levels into ranges for better visualization
const levelRanges = {};
if (levelData && levelData.length > 0) {
    levelData.forEach(item => {
        const level = parseInt(item.level);
        let range;
        if (level <= 50) range = '1-50';
        else if (level <= 100) range = '51-100';
        else if (level <= 150) range = '101-150';
        else if (level <= 200) range = '151-200';
        else range = '200+';

        levelRanges[range] = (levelRanges[range] || 0) + parseInt(item.character_count);
    });
}

new Chart(levelCtx, {
    type: 'bar',
    data: {
        labels: Object.keys(levelRanges),
        datasets: [{
            label: 'จำนวนตัวละคร',
            data: Object.values(levelRanges),
            backgroundColor: 'rgba(255, 205, 86, 0.8)',
            borderColor: 'rgba(255, 205, 86, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

console.log('✅ All charts initialized successfully');

} catch (error) {
    console.error('❌ Error initializing charts:', error);

    // แสดงข้อความแทนที่ charts
    const errorMessage = `
        <div style="text-align: center; padding: 50px; color: #dc3545;">
            <i class="fal fa-exclamation-triangle fa-3x mb-3"></i>
            <h5>ไม่สามารถโหลดกราฟได้</h5>
            <p>กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ตหรือติดต่อผู้ดูแลระบบ</p>
            <small>Error: ${error.message}</small>
        </div>
    `;

    // แทนที่ canvas ด้วยข้อความ error
    const canvases = ['dailyActivityChart', 'playtimeChart', 'levelChart'];
    canvases.forEach(canvasId => {
        const canvas = document.getElementById(canvasId);
        if (canvas) {
            canvas.parentElement.innerHTML = errorMessage;
        }
    });
}

// เพิ่ม fallback สำหรับ browsers ที่ไม่รองรับ Canvas
if (!document.createElement('canvas').getContext) {
    console.error('❌ Canvas not supported in this browser');
    const fallbackMessage = `
        <div style="text-align: center; padding: 50px; color: #ffc107;">
            <i class="fal fa-browser fa-3x mb-3"></i>
            <h5>เบราว์เซอร์ไม่รองรับการแสดงกราฟ</h5>
            <p>กรุณาใช้เบราว์เซอร์ที่ทันสมัยกว่านี้</p>
        </div>
    `;

    const canvases = ['dailyActivityChart', 'playtimeChart', 'levelChart'];
    canvases.forEach(canvasId => {
        const canvas = document.getElementById(canvasId);
        if (canvas) {
            canvas.parentElement.innerHTML = fallbackMessage;
        }
    });
}
</script>
