<?php
$serverName = "140.99.98.46,1433";
$connectionOptions = [
    "Database" => "Server01",
    "Uid" => "sa",
    "PWD" => "mataPassWdcw51EWxc",
    "CharacterSet" => "UTF-8"
];
$conn = sqlsrv_connect($serverName, $connectionOptions);
if ($conn === false) {
    die("Connection failed: " . print_r(sqlsrv_errors(), true));
}

function littleEndianHexToBigInt($hex) {
    $bytes = str_split($hex, 2);
    $reversed = implode('', array_reverse($bytes));
    return hexdec($reversed);
}

function bigIntToLittleEndianHex($value, $lengthBytes) {
    $hex = dechex($value);
    $hex = str_pad($hex, $lengthBytes * 2, '0', STR_PAD_LEFT);
    $bytes = str_split($hex, 2);
    return implode('', array_reverse($bytes));
}

function splitInventory($characterId, $binaryData) {
    $items = [];
    $hex = bin2hex($binaryData);
    $maxLength = strlen($hex);
    $pos = 0;

    while (($pos + 60) <= $maxLength) {
        $chunk = substr($hex, $pos, 60);
        if ($chunk === str_repeat('00', 60)) {
            $pos += 60;
            continue;
        }

        $KindIdx    = littleEndianHexToBigInt(substr($chunk, 0, 16));
        $ItemIndex  = $KindIdx & 0x6000fff;
        $Serial     = littleEndianHexToBigInt(substr($chunk, 16, 16));
        $Option     = littleEndianHexToBigInt(substr($chunk, 32, 16));
        $Slot       = littleEndianHexToBigInt(substr($chunk, 48, 4));
        $Period     = littleEndianHexToBigInt(substr($chunk, 52, 8));

        $hexData =
            bigIntToLittleEndianHex($KindIdx, 8) .
            bigIntToLittleEndianHex($Serial, 8) .
            bigIntToLittleEndianHex($Option, 8) .
            bigIntToLittleEndianHex($Slot, 2) .
            bigIntToLittleEndianHex($Period, 4);

        $items[$Slot] = [
            'itemIndex' => $ItemIndex,
            'HexData'   => strtoupper($hexData)
        ];
        $pos += 60;
    }

    return $items;
}

if (isset($_GET['ajax'])) {
    $items = [];
    $query = "SELECT CharacterIdx, Data FROM cabal_inventory_table WHERE CharacterIdx = 16";
    $stmt = sqlsrv_query($conn, $query);

    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $items = splitInventory($row['CharacterIdx'], $row['Data']);
    }

    echo json_encode($items);
    exit;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Inventory Grid Viewer</title>
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <style>
        body { font-family: sans-serif; background: #1c1f2e; color: white; padding: 20px; }
        h2 { margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: repeat(8, 1fr); gap: 10px; margin-top: 10px; }
        .slot {
            background:rgb(149, 151, 161);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #444;
            font-size: 13px;
        }
        .slot.empty {
            background: #11131d;
            color: #888;
            opacity: 0.5;
        }
        .slot .label { font-weight: bold; margin-bottom: 5px; font-size: 12px; }
        #hexAll {
            width: 100%;
            height: 100px;
            margin-top: 30px;
            font-family: monospace;
        }
        .ui-tabs .ui-tabs-nav li a { font-size: 14px; }
    </style>
</head>
<body>
    <h2>🎮 Inventory Slot Viewer (แบ่งแท็บละ 64 ช่อง)</h2>
    <div id="tabPanels">
        <ul id="tabNav"></ul>
    </div>
    <h3>🔢 รวม HexData ทั้งหมด</h3>
    <textarea id="hexAll" readonly></textarea>

<script>
$.getJSON('inventory_grid_tabs.php?ajax=1', function(data) {
    const slotMap = data;
    const tabNav = $('#tabNav');
    const tabPanels = $('#tabPanels');
    const hexChunks = [];

    const slotsPerTab = 64;
    const maxSlot = Math.max(...Object.keys(slotMap).map(n => parseInt(n)));
    const tabCount = Math.ceil((maxSlot + 1) / slotsPerTab);

    for (let t = 0; t < tabCount; t++) {
        const start = t * slotsPerTab;
        const end = start + slotsPerTab - 1;
        const tabId = 'tab_' + t;

        tabNav.append(`<li><a href="#${tabId}">Tab ${start}-${end}</a></li>`);

        const grid = $('<div class="grid"></div>');
        for (let s = start; s <= end; s++) {
            const slot = $('<div class="slot"></div>');
            slot.append(`<div class="label">Slot ${s}</div>`);

            if (slotMap[s]) {
                slot.append(`<div>ItemIndex: ${slotMap[s].itemIndex}</div>`);
                //slot.append(`<div>Hex: ${slotMap[s].HexData}</div>`);
                hexChunks.push(slotMap[s].HexData);
            } else {
                slot.addClass('empty');
                slot.append(`<div>ว่าง</div>`);
            }

            grid.append(slot);
        }

        tabPanels.append(`<div id="${tabId}">${grid.prop('outerHTML')}</div>`);
    }

    $('#hexAll').val(hexChunks.join('').toUpperCase());
    $('#tabPanels').tabs();
});
</script>
</body>
</html>