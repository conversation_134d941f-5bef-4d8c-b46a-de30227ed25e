.style-switcher {
    background: #171717;
    width: 260px;
    position: fixed;
    top: 110px;
    bottom: 0;
    height: 100%;
    z-index: 1008;
    border-radius: 0;
    right: -260px
}

.style-switcher .style-switcher-wrap {
    height: 100%;
    margin: 0;
    overflow-y: auto;
    padding: 15px 20px 135px;
    position: relative
}

.style-switcher h4 {
    background: #171717;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    line-height: 30px;
    margin: 0 0 7px;
    padding: 0
}

.style-switcher .style-switcher-open {
    background-color: #171717;
    border-radius: 6px 0 0 6px;
    color: #fff;
    display: block;
    font-size: 13px;
    height: 34px;
    left: -33px;
    line-height: 34px;
    position: absolute;
    text-align: center;
    text-decoration: none;
    top: 35px;
    width: 34px
}

.style-switcher h5 {
    color: #999;
    margin: 0 0 2px 0;
    padding: 0;
    line-height: 30px;
    font-size: 13px;
    font-weight: 400;
    text-transform: none
}

.style-switcher .form-control {
    background-color: #fff!important;
    border: 1px solid #ccc!important
}

.style-switcher .colorpicker-element {
    margin: -3px 0 7px
}

.style-switcher ul.options {
    list-style: none;
    margin: -3px 0 10px 0;
    padding: 0;
    overflow: hidden
}

.style-switcher ul.options li {
    float: left;
    margin: 2px
}

.style-switcher ul.options li a {
    display: block;
    width: 15px;
    height: 15px;
    cursor: pointer
}

.style-switcher ul.options li a.pattern {
    background-repeat: repeat;
    background-position: 0 0;
    background-color: transparent
}

.style-switcher .color-picker {
    display: none;
    margin-bottom: 15px
}

.style-switcher .options-links {
    padding: 0 0 10px
}

.style-switcher .options-links a {
    background-color: #000;
    color: #ccc;
    font-size: 12px;
    margin-bottom: 0;
    margin-right: 5px;
    padding: 5px 12px;
    text-decoration: none;
    border-radius: 4px
}

.style-switcher .options-links a:last-child {
    margin-right: 0
}

.style-switcher .options-links a:hover {
    background-color: #262626
}

.style-switcher .options-links a.active {
    background: #fff;
    color: #666
}

.style-switcher .style-switcher-buttons {
    border-top: 1px solid #000;
    margin-top: 15px;
    padding-top: 15px;
    text-align: center
}

.style-switcher .style-switcher-buttons a {
    padding: 8px;
    font-size: 13px;
    display: inline-block
}

.style-switcher .style-switcher-buttons .reset {
    width: 74px
}

.style-switcher .style-switcher-buttons .get-css {
    width: 137px;
    font-weight: 700
}

.style-switcher .style-switcher-buttons .get-css i {
    font-size: 14px;
    margin-right: 5px
}

textarea.get-css {
    height: 325px;
    width: 100%;
    resize: none;
    cursor: text
}

.style-switcher-color-picker {
    z-index: 1009!important
}

html.boxed .style-switcher {
    top: 0!important
}

html.header-fixed .style-switcher {
    top: 56px!important
}

html.sidebar-light:not(.dark) .style-switcher {
    background: #f6f6f6
}

html.sidebar-light:not(.dark) .style-switcher:before {
    content: " ";
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    width: 5px;
    background: #e2e3e6;
    position: absolute
}

html.sidebar-light:not(.dark) .style-switcher .style-switcher-open {
    background: #f6f6f6 none repeat scroll 0 0;
    color: #777;
    border-left: 3px solid #e2e3e6;
    border-top: 3px solid #e2e3e6;
    border-bottom: 3px solid #e2e3e6;
    height: 39px;
    width: 38px
}

html.sidebar-light:not(.dark) .style-switcher h4 {
    background: 0 0;
    color: #777
}

html.sidebar-light:not(.dark) .style-switcher .options-links a {
    background-color: #ececec;
    color: #777
}

html.sidebar-light:not(.dark) .style-switcher .options-links a:hover {
    background-color: #f2f2f2
}

html.sidebar-light:not(.dark) .style-switcher .options-links a.active {
    background: #333;
    color: #fff
}

html.sidebar-light:not(.dark) .style-switcher .style-switcher-buttons {
    border-top: 1px solid #ececec
}

html.modern .style-switcher {
    top: 70px
}

html.modern .style-switcher .style-switcher-open {
    top: 125px
}

html.simple-sticky-header-enabled .style-switcher {
    top: 60px
}