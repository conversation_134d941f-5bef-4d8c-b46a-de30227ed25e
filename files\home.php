<?php $zpanel->checkSession(true); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class='subheader-icon fal fa-chart-area'></i> Dashboard <span class='fw-300'>หน้ารวมข้อมูล</span>
    </h1>
</div>
<?php if ($userLogin->recUserAccount('IsDeveloper', $conn)) { ?>
<div class="alert alert-info alert-dismissible" role="alert">
    <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span>
        <span class="sr-only">Close</span></button> <?php echo T_WELCOMEMSG_ADM; ?>
</div>
<?php } else { ?>
<div class="alert alert-info alert-dismissible" role="alert">
    <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span>
        <span class="sr-only">Close</span></button> <?php echo T_WELCOMEMSG_USR; ?>
</div>
<?php  } ?>


<div class="row">
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">ID :
                    <?php echo number_format($userLogin->countuser($conn)); ?>
                    <small class="m-0 l-h-n">สมาชิกทั้งหมด</small>
                </h3>
            </div>
            <i class="fal fa-user position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n1"
                style="font-size:6rem"></i>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    Player :<?php echo number_format($userLogin->countcharecter($conn)); ?>
                    <small class="m-0 l-h-n">ตัวละครทัังหมด</small>
                </h3>
            </div>
            <i class="fal fa-gamepad-alt position-absolute pos-right pos-bottom opacity-15  mb-n1 mr-n4"
                style="font-size: 6rem;"></i>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-success-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <span class="countloginchar">0</span> Player
                    <small class="m-0 l-h-n">Player Online</small>
                </h3>
            </div>
            <i class="fal fa-lightbulb position-absolute pos-right pos-bottom opacity-15 mb-n5 mr-n6"
                style="font-size: 8rem;"></i>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($userLogin->countuserbanned($conn)); ?> User
                    <small class="m-0 l-h-n">User Banned</small>
                </h3>
            </div>
            <i class="fal fa-globe position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n4"
                style="font-size: 6rem;"></i>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div id="panel-4" class="panel">
            <div class="panel-hdr">
                <h2>
                    Player Online <span class="fw-300"><i>RealTime</i></span>
                </h2>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100">
                        <thead class="bg-warning-200">
                            <tr>
								<th>ID</th>
								<th>UserNum</th>
								<th>จำนวน Login</th>
								<th>IP ล่าสุด</th>
                                <th>CharacterIdx</th>
                                <th>Name</th>
								<th>Class</th>
                                <th>Level</th>
                                <th>Alz</th>
                                <th>Nation</th>
                                <th>Map</th>
                                <th>ChannelIdx</th>
                                <th>LoginTime</th>
								<th>PlayTime</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody class="notification2 dropdown-menu-login2">
                        </tbody>
                    </table>
                    <!-- datatable end -->
                </div>
            </div>
        </div>
    </div>
</div>