# 🔧 Troubleshooting Guide - การแก้ไขปัญหาการส่งไอเทม

คู่มือแก้ไขปัญหาเมื่อส่งไอเทมไม่ได้

## 🚨 ปัญหาที่พบบ่อย

### 1. **ปุ่ม Send Item ไม่ทำงาน**

**อาการ:** คลิกปุ่ม Send Item แล้วไม่มีอะไรเกิดขึ้น

**สาเหตุ:**
- ไม่มี event listener สำหรับปุ่ม
- ปุ่มถูก disable
- JavaScript error

**วิธีแก้:**
```javascript
// ตรวจสอบใน browser console
console.log(document.getElementById('sendItemBtn'));

// ตรวจสอบว่ามี event listener หรือไม่
// ควรเห็น event listener ใน Elements tab ของ DevTools
```

### 2. **Database Connection Failed**

**อาการ:** Error "Database connection failed"

**สาเหตุ:**
- sqlsrv extension ไม่ได้ติดตั้ง
- การตั้งค่าฐานข้อมูลผิด
- SQL Server ไม่ทำงาน

**วิธีแก้:**
1. ตรวจสอบ sqlsrv extension:
```php
<?php
if (extension_loaded('sqlsrv')) {
    echo "sqlsrv loaded";
} else {
    echo "sqlsrv NOT loaded";
}
?>
```

2. ตรวจสอบการตั้งค่าใน `../../_app/dbinfo.inc.php`:
```php
$dbinfo = [
    'host' => 'localhost',  // หรือ IP ของ SQL Server
    'port' => '1433',       // Port ของ SQL Server
    'database' => 'your_db_name',
    'username' => 'your_username',
    'password' => 'your_password'
];
```

### 3. **Table 'item_sends' doesn't exist**

**อาการ:** Error เกี่ยวกับตารางไม่มีอยู่

**วิธีแก้:**
รัน SQL script เพื่อสร้างตาราง:
```bash
sqlcmd -S your_server -d your_database -i database_setup_sqlserver.sql
```

### 4. **JSON Parse Error**

**อาการ:** ไม่สามารถ parse response เป็น JSON ได้

**สาเหตุ:**
- PHP error ทำให้ response ไม่ใช่ JSON
- HTML error page แทน JSON

**วิธีแก้:**
1. ตรวจสอบ PHP error log
2. เปิด browser DevTools → Network tab
3. ดู response ที่ได้จริง

### 5. **CORS Error**

**อาการ:** Cross-origin request blocked

**วิธีแก้:**
ตรวจสอบ headers ใน send_item.php:
```php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
```

## 🛠️ เครื่องมือ Debug

### 1. **test_send_item.php**
ไฟล์ทดสอบระบบทั้งหมด:
- ตรวจสอบการเชื่อมต่อฐานข้อมูล
- ทดสอบการสร้างตาราง
- ทดสอบการ insert ข้อมูล

**วิธีใช้:**
```
http://your-server/path/to/test_send_item.php
```

### 2. **debug_send.html**
หน้าเว็บสำหรับทดสอบการส่งไอเทม:
- ฟอร์มทดสอบ
- แสดงผล response
- ทดสอบการเชื่อมต่อ

**วิธีใช้:**
```
http://your-server/path/to/debug_send.html
```

### 3. **Browser DevTools**
ตรวจสอบ JavaScript errors:
1. เปิด F12 → Console tab
2. ดู error messages
3. ตรวจสอบ Network requests

## 📋 Checklist การแก้ไขปัญหา

### ขั้นตอนที่ 1: ตรวจสอบพื้นฐาน
- [ ] sqlsrv extension ติดตั้งแล้ว
- [ ] ไฟล์ config มีการตั้งค่าถูกต้อง
- [ ] SQL Server ทำงานอยู่
- [ ] ตารางถูกสร้างแล้ว

### ขั้นตอนที่ 2: ทดสอบการเชื่อมต่อ
- [ ] รัน test_send_item.php
- [ ] ตรวจสอบผลลัพธ์ทุกข้อ
- [ ] แก้ไขปัญหาที่พบ

### ขั้นตอนที่ 3: ทดสอบ API
- [ ] ใช้ debug_send.html ทดสอบ
- [ ] ตรวจสอบ request/response
- [ ] ดู error logs

### ขั้นตอนที่ 4: ทดสอบ Frontend
- [ ] ตรวจสอบ JavaScript console
- [ ] ตรวจสอบ event listeners
- [ ] ทดสอบการส่งข้อมูล

## 🔍 การ Debug แบบละเอียด

### 1. **ตรวจสอบ PHP Errors**
เพิ่มใน send_item.php:
```php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```

### 2. **ตรวจสอบ JavaScript Errors**
เพิ่มใน advanced-editor.php:
```javascript
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});
```

### 3. **ตรวจสอบ Network Requests**
ใน browser DevTools:
1. Network tab
2. ดู request headers
3. ดู response body
4. ตรวจสอบ status code

## 📞 การขอความช่วยเหลือ

เมื่อขอความช่วยเหลือ ให้แนบข้อมูลเหล่านี้:

1. **ผลลัพธ์จาก test_send_item.php**
2. **Error messages จาก browser console**
3. **PHP error logs**
4. **Screenshot ของปัญหา**
5. **ข้อมูล environment:**
   - PHP version
   - SQL Server version
   - Web server (Apache/IIS/Nginx)
   - Operating System

## 🎯 Quick Fixes

### ปัญหา: ปุ่มไม่ทำงาน
```javascript
// เพิ่มใน browser console
document.getElementById('sendItemBtn').addEventListener('click', function() {
    console.log('Button clicked!');
    sendItem();
});
```

### ปัญหา: Database connection
```php
// ทดสอบการเชื่อมต่อ
$connection = sqlsrv_connect("server,port", [
    "Database" => "db_name",
    "Uid" => "username", 
    "PWD" => "password"
]);
var_dump($connection);
```

### ปัญหา: JSON response
```php
// เพิ่มใน send_item.php
header('Content-Type: application/json');
echo json_encode(['test' => 'response']);
exit;
```

## 🚀 หลังจากแก้ไขแล้ว

1. **ทดสอบการส่งไอเทม**
2. **ตรวจสอบข้อมูลในฐานข้อมูล**
3. **ทดสอบ notification system**
4. **ทดสอบ error handling**

---

**หมายเหตุ:** หากยังแก้ไขไม่ได้ ให้ตรวจสอบ server error logs และ contact system administrator
