<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

$data = json_decode(file_get_contents('php://input'), true);

$id = intval($data['id']);
$name = $data['name'];
$title = $data['title'];
$desc = $data['desc'];

$query1 = "INSERT INTO EventData.dbo.cabal_ems_event_table (EventID, EventType, BeginDate, EndDate, LastModifiedDate, UseFlag, Name, LogMessages, worldIndex, npcIndex, participates, ServerIndex, resetDay, resetHour, resetMinute)
VALUES (?, 2, '2024-01-01 00:00:00.000', '2030-12-25 00:00:00.000', '2025-01-01 00:00:00.000', 1, ?, 'Created via PHP editor', 1, 49, 15, 1, 0, 0, 0)";
sqlsrv_query($conn, $query1, [$id, $name]);

$query2 = "INSERT INTO EventData.dbo.cabal_ems_event_npcscript_table (EventID, WorldIdx, NpcIdx, Script1, Script2, Script3, Script4, Script5, ScriptDesc)
VALUES (?, 1, 49, ?, NULL, NULL, NULL, NULL, ?)";
sqlsrv_query($conn, $query2, [$id, $title, $desc]);

echo json_encode(["status" => "created"]);
