<div class="row">
    <div class="col-lg-12">
            <div class="panel-body">
            <header class="panel">
                            <h2 class="panel-title"><?php echo PT_DOWNLOADS; ?> <small><?php echo PT_DOWNLOADS_DESC; ?></small></h2>
                            </header>
                <div class="panel panel-default">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>File name</th>
                            <th>รายระเอียด</th>
                            <th>ขนาดไฟล์</th>
                            <th>ลิ้ง</th>
                            <th>อัพเดดวันที่</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $selectDownloads = "SELECT * FROM WEB_Downloads";
                        $selectDownloadsParam = array();
                        $selectDownloadsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectDownloadsQuery = sqlsrv_query($conn, $selectDownloads, $selectDownloadsParam, $selectDownloadsOpt);
                        if (sqlsrv_num_rows($selectDownloadsQuery)) {
                            while ($resDownload = sqlsrv_fetch_array($selectDownloadsQuery, SQLSRV_FETCH_ASSOC)) {
                                ?>
                                <tr>
                                    <td><?php echo $resDownload['name']; ?></td>
                                    <td><?php echo $resDownload['description']; ?></td>
                                    <td><?php echo $resDownload['size'] . ' ' . strtoupper($resDownload['size_type']); ?></td>
                                    <td>
                                        <a class="btn btn-success btn-block" tabindex="-1" href="http://<?php echo $resDownload['url']; ?>" target="_blank"><i class="fa fa-download"></i>    Download</a>
                                    </td>
                                    <td><?php echo date('d/m/Y \a\t\ H:i', strtotime($resDownload['dateupdated'])); ?></td>
                                </tr>
                                <?php
                            }
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>