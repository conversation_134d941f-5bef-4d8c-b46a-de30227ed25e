<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_CONVERSIONRATE; ?> <small><?php echo PT_CONVERSIONRATE_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                if (isset($_POST['btn_savechange'])) {
                    // variable
                    $inputGameCash = strip_tags(trim($_POST['input_gamecash']));
                    $inputRate = strip_tags(trim($_POST['input_rate']));

                    // condition
                    if (empty($inputGameDollars)) {
                        $returnWarning = W_MGD_EMPTY_GAMEDOLLAR;
                    } else if (empty($inputRate)) {
                        $returnWarning = W_MGD_EMPTY_RATE;
                    } else {
                        
                        // update
                        $updateConversion = "UPDATE DataGPConvertRates SET GamePoints = '$inputGameCash', ConversionRate = '$inputRate'";
                        $updateConversionParam = array();
                        $updateConversionQuery = sqlsrv_query($conn, $updateConversion, $updateConversionParam);
                        
                        if(sqlsrv_rows_affected($updateConversionQuery)){
                            $returnSuccess = S_CONVERSION_UPDATED;
                        }
                        
                    }
                }
                
                // get conversion info
                $selectConversionInfo = "SELECT * FROM DataGPConvertRates";
                $selectConversionInfoParam = array();
                $selectConversionInfoQuery = sqlsrv_query($conn, $selectConversionInfo, $selectConversionInfoParam);
                $selectConversionInfoFetch = sqlsrv_fetch_array($selectConversionInfoQuery, SQLSRV_FETCH_ASSOC);
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
                <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="input_gamedollar" class="control-label"><?php echo T_GD_GAMECASH; ?>: <small class="text-red"><?php echo T_GD_GAMECASH_DESC; ?></small></label>
                        <input type="text" name="input_gamecash" class="form-control" id="input_gamedollar" pattern="[0-9]+$" value="<?php echo $selectConversionInfoFetch['GamePoints']; ?>">
                        <span class="help-block text-red"><small><?php echo T_GD_HELPBLOCK . ' ' . H_ONLY_NUMBER; ?></small></span>
                    </div>
                    <div class="form-group">
                        <label for="input_rate" class="control-label"><?php echo T_GD_RATE; ?>: <small class="text-red"><?php echo T_GD_RATE_DESC; ?></small></label>
                        <input type="text" name="input_rate" class="form-control" id="input_rate" pattern="[0-9]+$" value="<?php echo $selectConversionInfoFetch['ConversionRate']; ?>">
                        <span class="help-block text-red"><small><?php echo T_GD_RATE_HELPBLOCK . ' ' . H_ONLY_NUMBER; ?></small></span>
                    </div>
                    <div class="form-group">
                        <input type="submit" name="btn_savechange" class="btn btn-block btn-success" value="<?php echo B_SAVECHANGES; ?>">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>