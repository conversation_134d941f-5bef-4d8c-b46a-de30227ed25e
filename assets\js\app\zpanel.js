$(function () {
  /*
   * Developed by FDEV
   * Copyright 2015
   * zPANEL All rights reserved
   *
   * zpanel.js jQuery Controller
   * All returns is in english only
   */

  // global variables
  var url = "home/switch/zpanel.php";
  var baseUrl = "home.php";

  // dismiss alert
  var j_dismiss = $(".j_dismiss");
  j_dismiss.delay(5000).fadeOut("slow");

  // hidden inputs
  var j_hidden = $(".j_hidden");

  //Ban  add ip
  $('form[name="j_add_banip"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_banip";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>วัาว!</strong> Ban IP  successfully!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> เกิดข้อผิดพลาดขึ้น</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_add_banned"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_banned";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>วัาว!</strong> Banned User  successfully!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> เกิดข้อผิดพลาดขึ้น</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  $('form[name="j_add_vipcard"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_vipcard";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>successfully::</strong>Card VIP</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> เกิดข้อผิดพลาดขึ้น</div>'
            )
            .fadeOut(100);
        } else if (data == "error-passadmin") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>Error:: </strong> Password Admin ผิดพลาด</div>'
            )
            .fadeOut(100);
        } else if (data == "error-novipid") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>Error:: </strong>ผู้ใช้นี้ไม่ใช่ VIP Code</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  // ext premium acc
  $('form[name="j_ext_premium"]').submit(function () {
    var formData = $(this).serialize() + "&action=ext_premium";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>Wow!</strong> Premium extended!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>Ooops!</strong> Occurs an error</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // add timeonline acc
  $('form[name="j_add_protrue"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_protrue";
    var formVar = $(this);
    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success:50") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 50 บาท",
            type: "success",
          });
        } else if (data == "success:90") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 90 บาท",
            type: "success",
          });
        } else if (data == "success:150") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 150 บาท",
            type: "success",
          });
        } else if (data == "success:300") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 300 บาท",
            type: "success",
          });
        } else if (data == "success:500") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 500 บาท",
            type: "success",
          });
        } else if (data == "success:1000") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ บัตร 1000 บาท",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error :: ระบบมีข้อมูลผิดพลาดบางอย่าง",
            text: "ข้อมูลไม่บางอย่างผิดพลาด ???? คุณได้ทำรายการช้ำ, หรือมีข้อผิดพลาดในส่วนอื่น",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // แลกไอเท็ม+เวลาออนไลน์
  $('form[name="j_add_itemtime"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_itemtime";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check2") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // add Reward acc
  $('form[name="j_add_reward"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_Reward";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          new PNotify({
            title: " ระบบส่งข้อมูลสำเร็จ!",
            text: "ระบบได้ส่งไอเท็มให้เรียบร้อยแล้ว.",
            type: "success",
          });

          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "success:200") {
          new PNotify({
            title: " ระบบส่งข้อมูลสำเร็จ!",
            text: "ระบบได้ส่งไอเท็ม 200 ให้เรียบร้อยแล้ว.",
            type: "success",
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "success:400") {
          new PNotify({
            title: " ระบบส่งข้อมูลสำเร็จ!",
            text: "ระบบได้ส่งไอเท็ม 400 ให้เรียบร้อยแล้ว..",
            type: "success",
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "success:600") {
          new PNotify({
            title: " ระบบส่งข้อมูลสำเร็จ!",
            text: "ระบบได้ส่งไอเท็ม 600 ให้เรียบร้อยแล้ว..",
            type: "success",
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "Error:check") {
          new PNotify({
            title: " ข้อมูลผิดพลาด!",
            text: "ระบบไม่สามารถส่งไอเท็มได้.",
            type: "error",
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "Error:check2") {
          new PNotify({
            title: " ข้อมูลผิดพลาด!",
            text: "ระบบไม่สามารถส่งไอเท็มได้.",
            type: "error",
          });
          window.setTimeout(function () {
            location.reload();
          }, 5000);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // facebook share
  $('form[name="j_add_facebookshare"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_facebookshare";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ กิจกรรม Facebook Share",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  Facebook Share",
            type: "error",
          });
        } else if (data == "Error:check-id") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด-ไม่มีไอดีนี้",
            type: "error",
          });
        } else if (data == "Error:check-sh") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ข้อมูลระบบผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check-authid") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ตรวจไม่พบไอดี",
            type: "error",
          });
        } else if (data == "Error:check-fb") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ตรวจไม่พบข้อมูลแชร์",
            type: "error",
          });
        } else if (data == "Error:check-status") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ไอดีนี้รับของกิจกรรมไปแล้ว",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  $('form[name="j_add_waritem"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_waritem";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check-id") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด-ไม่มีไอดีนี้",
            type: "error",
          });
        } else if (data == "Error:check-sh") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ข้อมูลระบบผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check-authid") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ตรวจไม่พบไอดี",
            type: "error",
          });
        } else if (data == "Error:check-fb") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ตรวจไม่พบข้อมูล",
            type: "error",
          });
        } else if (data == "Error:check-status") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  ไอดีนี้รับของกิจกรรมไปแล้ว",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  //voucher chack
  $('form[name="j_add_Voucheritem"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_voucheritem";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ แลกแต้ม Voucher Cash",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  แลกแต้ม Voucher Cash",
            type: "error",
          });
        } else if (data == "Error:check2") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  แลกแต้ม Voucher Cash",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // reward check
  $('form[name="j_add_rewarditem"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_rewarditem";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ แลกแต้ม Reward",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  แลกแต้ม Reward",
            type: "error",
          });
        } else if (data == "Error:check2") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด  แลกแต้ม Reward",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  // reward
  $('form[name="j_select_Reward"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_reward";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong> ส่งข้อมูลเรียบร้อยแล้ว!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>ERROE!</strong>แต้ม Reward ไม่พอ</div>'
            )
            .fadeOut(100);
        } else if (data == "error2") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>ERROE3!</strong> แต้ม Reward ไม่พอ</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  // Voucher
  $('form[name="j_select_Voucher"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_Voucher";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "successp1") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง Pack1 ถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "successp2") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง Pack2 ถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "successp3") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง Pack3 ถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "successp4") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง Pack4 ถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "successp5") {
          Swal.fire({
            icon: "success",
            title: "success",
            text: "ระบบได้ส่งข้อมูลถึง Pack5 ถึง GM เรียบร้อยแล้ว!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "error") {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "ข้อมูลผิดพลาด!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "errorcash") {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Cash ไม่พอที่จะชื้อ Voucher !",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "errordata") {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "ข้อมูลผิดพลาดติดต่อ ระบบ database ไม่ได้!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        } else if (data == "emptyerror") {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "ไม่มีข้อมูล!",
            timer: 3000,
          });
          window.setTimeout(function () {
            location.reload();
          }, 3000);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
  // add votes point
  $('form[name="j_add_Vote"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_Vote";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong> แลกแต้มขุดให้เรียบร้อยแล้ว!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>ERROE!</strong>แต้มขุดไม่พอแลก</div>'
            )
            .fadeOut(100);
        } else if (data == "error2") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>ERROE2!</strong> แต้มขุดไม่พอแลก</div>'
            )
            .fadeOut(100);
        } else if (data == "error3") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>ERROE3!</strong> แต้มขุดไม่พอแลก</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  // add menu perms
  $('form[name="j_menuperm"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_menuperm";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong> Menu Permissions has been registered to this Player!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Occurs an error</div>'
            )
            .fadeOut(100);
        } else if (data == "empty") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Please, กรอก Fill ไอดีนี้</div>'
            )
            .fadeOut(100);
        } else if (data == "onlyint") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> CustomerID is only numbers!</div>'
            )
            .fadeOut(100);
        } else if (data == "already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player already have permissions!</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_extraperm"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_extraperm";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong> Extra Permissions has been registered to this Player!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Occurs an error</div>'
            )
            .fadeOut(100);
        } else if (data == "empty") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Please, กรอก field ไอดีนี้</div>'
            )
            .fadeOut(100);
        } else if (data == "onlyint") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> CustomerID is only numbers!</div>'
            )
            .fadeOut(100);
        } else if (data == "already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player already have permissions!</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_add_title"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_title";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong>อัพเดด Title เรียบร้อยแล้ว!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Occurs an error</div>'
            )
            .fadeOut(100);
        } else if (data == "empty") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Please, กรอก field นี้</div>'
            )
            .fadeOut(100);
        } else if (data == "already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player already have permissions!</div>'
            )
            .fadeOut(100);
        } else if (data == "login-already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player Login Game!</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_del_title"]').submit(function () {
    var formData = $(this).serialize() + "&action=del_title";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>SUCCESS!</strong>อัพเดด Title เรียบร้อยแล้ว!</div>'
            )
            .fadeOut(100);
        } else if (data == "error") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Occurs an error</div>'
            )
            .fadeOut(100);
        } else if (data == "empty") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> Please, กรอก field นี้</div>'
            )
            .fadeOut(100);
        } else if (data == "already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player already have permissions!</div>'
            )
            .fadeOut(100);
        } else if (data == "login-already") {
          formVar
            .find(".j_alert")
            .fadeIn(100)
            .delay(3000)
            .html(
              '<div class="alert alert-info"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>อ๊ะ!</strong> This player Login Game!</div>'
            )
            .fadeOut(100);
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_add_waritemscore"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_itemwaruser";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ",
            type: "success",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check2") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });

  $('form[name="j_add_thailotto"]').submit(function () {
    var formData = $(this).serialize() + "&action=add_itemthailotto";
    var formVar = $(this);

    $.ajax({
      url: url,
      data: formData,
      type: "POST",
      beforeSend: function () {
        formVar.find(".load").fadeIn("slow");
      },
      success: function (data) {
        if (data == "success") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Success",
            text: "ระบบทำรายการสำเร็จ",
            type: "success",
          });
        } else if (data == "Error") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "ระบบทำรายการไม่สำเร็จเกิดข้อผิดพลาด",
            type: "error",
          });
        } else if (data == "Error:check") {
          formVar.find(".j_alert").fadeOut("slow");
          new PNotify({
            title: "Error",
            text: "รับไอเท็มไปแล้ว",
            type: "error",
          });
        }
      },
      complete: function () {
        formVar.find(".load").fadeOut("slow");
      },
    });
    return false;
  });
});
