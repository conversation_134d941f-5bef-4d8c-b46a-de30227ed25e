# Item Sending System - PHP Database Integration

ระบบส่งไอเทมที่เชื่อมต่อกับฐานข้อมูล SQL Server สำหรับเกม Cabal Online

## ไฟล์ที่สำคัญ

### 1. **advanced-editor.php**
- หน้าเว็บหลักสำหรับการส่งไอเทม
- มี UI สำหรับกรอกข้อมูลไอเทมและผู้เล่น
- เชื่อมต่อกับ PHP API เพื่อส่งข้อมูลไปยังฐานข้อมูล

### 2. **send_item.php**
- PHP API สำหรับรับข้อมูลการส่งไอเทมและบันทึกลงฐานข้อมูล SQL Server
- ใช้ sqlsrv extension สำหรับเชื่อมต่อฐานข้อมูล
- ตรวจสอบความถูกต้องของข้อมูล
- บันทึก log การทำงานของ admin

### 3. **config.php** (ไม่ใช้แล้ว)
- ระบบใช้ไฟล์ config ที่มีอยู่แล้ว:
  - `../../_app/dbinfo.inc.php` - การตั้งค่าฐานข้อมูล
  - `../../_app/general_config.inc.php` - การตั้งค่าทั่วไป

### 4. **database_setup_sqlserver.sql**
- SQL script สำหรับ SQL Server เพื่อสร้างตารางที่จำเป็น
- สร้าง Views และ Stored Procedures
- ข้อมูลตัวอย่างสำหรับทดสอบ

## การติดตั้ง

### 1. **ตั้งค่าฐานข้อมูล SQL Server**
```sql
-- รัน SQL script เพื่อสร้างตาราง
sqlcmd -S your_server -d your_database -i database_setup_sqlserver.sql
```

### 2. **ตรวจสอบไฟล์ config ที่มีอยู่**
ตรวจสอบว่าไฟล์ config มีการตั้งค่าฐานข้อมูล SQL Server:
- `../../_app/dbinfo.inc.php` - ต้องมีข้อมูลการเชื่อมต่อ SQL Server
- `../../_app/general_config.inc.php` - การตั้งค่าทั่วไป

ตัวอย่างใน `dbinfo.inc.php`:
```php
$dbinfo = [
    'host' => 'your_sql_server',
    'port' => '1433',
    'database' => 'your_database',
    'username' => 'your_username',
    'password' => 'your_password'
];
```

### 3. **ตั้งค่า Web Server**
- วางไฟล์ทั้งหมดในโฟลเดอร์ web server (เช่น htdocs, www)
- ตรวจสอบว่า PHP มี extension sqlsrv และ pdo_sqlsrv
- ติดตั้ง Microsoft SQL Server Driver for PHP หากยังไม่มี

## โครงสร้างฐานข้อมูล

### ตาราง `item_sends`
เก็บข้อมูลการส่งไอเทมทั้งหมด
- `id` - Primary key
- `player_id` - ID ของผู้เล่นจากตาราง account
- `player_username` - ชื่อผู้เล่น
- `item_id` - ID ของไอเทม
- `item_name` - ชื่อไอเทม
- `quantity` - จำนวน
- `send_method` - วิธีการส่ง (inventory, mail, warehouse)
- `upgrade_level` - ระดับอัพเกรด
- `craft_level` - ระดับ craft
- `extreme` - ค่า extreme
- `divine` - ค่า divine
- `item_code` - รหัสไอเทมที่คำนวณแล้ว
- `options_code` - รหัส options ที่คำนวณแล้ว
- `admin_username` - ชื่อ admin ที่ส่ง
- `created_at` - วันที่สร้าง
- `status` - สถานะ (pending, sent_to_inventory, sent_to_mail, etc.)

### ตาราง `admin_logs`
เก็บ log การทำงานของ admin
- `id` - Primary key
- `admin_username` - ชื่อ admin
- `action` - การกระทำ
- `target_player` - ผู้เล่นเป้าหมาย
- `details` - รายละเอียด (JSON format)
- `ip_address` - IP address
- `created_at` - วันที่

### ตาราง `items`
ฐานข้อมูลไอเทม (ถ้ามี)
- `id` - Item ID
- `name` - ชื่อไอเทม
- `full_id` - Full ID
- `category` - หมวดหมู่
- `type` - ประเภท

## การใช้งาน

### 1. **ส่งไอเทมผ่าน Web Interface**
1. เปิดหน้า `advanced-editor.php`
2. กรอกข้อมูลไอเทม (ID, ชื่อ, จำนวน, etc.)
3. กรอกชื่อผู้เล่น
4. เลือกวิธีการส่ง (Inventory, Mail, Warehouse)
5. คลิก "Send Item"

### 2. **ตรวจสอบ Log**
```sql
-- ดู log การส่งไอเทมล่าสุด
SELECT * FROM item_sends ORDER BY created_at DESC LIMIT 10;

-- ดู log การทำงานของ admin
SELECT * FROM admin_logs ORDER BY created_at DESC LIMIT 10;

-- ดูสถิติการส่งไอเทม
SELECT 
    admin_username,
    COUNT(*) as total_sends,
    DATE(created_at) as send_date
FROM item_sends 
GROUP BY admin_username, DATE(created_at)
ORDER BY send_date DESC;
```

## API Endpoints

### POST `/send_item.php`
ส่งไอเทมไปยังผู้เล่น

**Request Body:**
```json
{
    "playerUsername": "player_name",
    "itemId": 123,
    "itemName": "Test Item",
    "quantity": 1,
    "sendMethod": "mail",
    "upgradeLevel": 0,
    "craftLevel": 0,
    "extreme": 0,
    "divine": 0,
    "itemCode": "00000123",
    "optionsCode": "00000000",
    "sendNotification": true,
    "logTransaction": true,
    "adminUsername": "admin"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Item sent successfully",
    "data": {
        "send_id": 123,
        "player": "player_name",
        "item_name": "Test Item",
        "quantity": 1,
        "method": "mail",
        "status": "sent_to_mail",
        "timestamp": "2024-01-01 12:00:00"
    }
}
```

## การแก้ไขปัญหา

### 1. **Database Connection Error**
- ตรวจสอบการตั้งค่าในไฟล์ `../../_app/dbinfo.inc.php`
- ตรวจสอบว่า SQL Server ทำงานอยู่และ port 1433 เปิดอยู่
- ตรวจสอบ username/password และ database name
- ตรวจสอบว่า PHP มี sqlsrv extension

### 2. **Player Not Found**
- ตรวจสอบว่าตาราง `account` มีข้อมูลผู้เล่น
- ตรวจสอบการเชื่อมต่อกับฐานข้อมูลเกม
- ตรวจสอบ case sensitivity ของ username

### 3. **Permission Denied**
- ตรวจสอบ file permissions
- ตรวจสอบ database privileges สำหรับ SQL Server user
- ตรวจสอบ firewall settings

## การพัฒนาเพิ่มเติม

### 1. **เพิ่มระบบ Authentication**
- เพิ่มการ login สำหรับ admin
- ตรวจสอบสิทธิ์ในการส่งไอเทม

### 2. **เพิ่มระบบ Notification**
- ส่งอีเมลแจ้งเตือนเมื่อส่งไอเทม
- แจ้งเตือนใน game

### 3. **เพิ่ม Dashboard**
- สถิติการส่งไอเทม
- รายงานการใช้งาน
- การจัดการ admin

## ความปลอดภัย

1. **Input Validation** - ตรวจสอบข้อมูลที่รับเข้ามาทั้งหมด
2. **SQL Injection Protection** - ใช้ Prepared Statements
3. **CORS Protection** - จำกัดการเข้าถึงจาก domain อื่น
4. **Error Logging** - บันทึก error ทั้งหมดเพื่อการ debug
5. **Transaction Support** - ใช้ database transaction เพื่อความปลอดภัย

## License

MIT License - ใช้งานได้อย่างอิสระ
