<?php $user->restrictionUser(true, $conn); ?>

<!-- Load CSS fixes -->
<link rel="stylesheet" href="assets/css/modern-browser-fixes.css">

<!-- Load JS fixes -->
<script src="assets/js/theme-settings-fix.js"></script>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-browser"></i> ทดสอบการแก้ไข Browser Warnings
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบการแก้ไข CSS Deprecation และ Theme Settings</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    ทดสอบการแก้ไขปัญหา CSS deprecation warnings และ theme settings warnings
                </div>
                
                <h5>🔧 การทดสอบ CSS Deprecation</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">ปัญหาที่แก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-times text-danger"></i> <code>-ms-high-contrast</code> deprecation</li>
                                    <li><i class="fal fa-times text-danger"></i> Theme settings empty warning</li>
                                    <li><i class="fal fa-times text-danger"></i> IE-specific CSS issues</li>
                                    <li><i class="fal fa-times text-danger"></i> Accessibility warnings</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">วิธีแก้ไขที่ทำ</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> ใช้ <code>forced-colors</code> แทน</li>
                                    <li><i class="fal fa-check text-success"></i> Auto-initialize theme settings</li>
                                    <li><i class="fal fa-check text-success"></i> Modern CSS fallbacks</li>
                                    <li><i class="fal fa-check text-success"></i> WCAG 2.1 compliance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🎨 ทดสอบ High Contrast Mode</h5>
                <div class="alert alert-secondary">
                    <p>ทดสอบการทำงานของ High Contrast Mode ใน modern browsers:</p>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="testHighContrast()">
                            <i class="fal fa-adjust"></i> ทดสอบ High Contrast
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testForcedColors()">
                            <i class="fal fa-palette"></i> ทดสอบ Forced Colors
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="testAccessibility()">
                            <i class="fal fa-universal-access"></i> ทดสอบ Accessibility
                        </button>
                    </div>
                </div>
                
                <div id="contrast-test-result" class="mt-3"></div>
                
                <h5 class="mt-4">⚙️ ทดสอบ Theme Settings</h5>
                <div class="alert alert-secondary">
                    <p>ทดสอบการทำงานของ Theme Settings:</p>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="themeSettingsFix.health()">
                            <i class="fal fa-heartbeat"></i> Health Check
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="themeSettingsFix.show()">
                            <i class="fal fa-eye"></i> Show Settings
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="themeSettingsFix.reset()">
                            <i class="fal fa-redo"></i> Reset Settings
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="themeSettingsFix.export()">
                            <i class="fal fa-download"></i> Export Settings
                        </button>
                    </div>
                </div>
                
                <div id="theme-test-result" class="mt-3"></div>
                
                <h5 class="mt-4">🌐 ทดสอบ Browser Compatibility</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>Feature</th>
                                <th>Support</th>
                                <th>Fallback</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="browser-support-table">
                            <!-- จะถูกเติมด้วย JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">📋 Console Messages</h5>
                <div class="alert alert-info">
                    <h6>ตรวจสอบ Console (F12) เพื่อดู:</h6>
                    <ul class="mb-0">
                        <li>✅ ไม่มี CSS deprecation warnings</li>
                        <li>✅ Theme settings loaded successfully</li>
                        <li>✅ Modern browser features detected</li>
                        <li>✅ Accessibility features enabled</li>
                    </ul>
                </div>
                
                <h5 class="mt-4">🔧 การใช้งาน</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>CSS Fixes</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>เพิ่มในไฟล์ HTML:</strong></p>
                                <pre><code>&lt;link rel="stylesheet" href="assets/css/modern-browser-fixes.css"&gt;</code></pre>
                                
                                <p class="mt-3"><strong>ฟีเจอร์ที่ได้:</strong></p>
                                <ul class="small">
                                    <li>Modern high contrast support</li>
                                    <li>CSS Grid fallbacks</li>
                                    <li>Accessibility improvements</li>
                                    <li>Print styles</li>
                                    <li>Dark mode support</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Theme Settings Fix</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>เพิ่มในไฟล์ HTML:</strong></p>
                                <pre><code>&lt;script src="assets/js/theme-settings-fix.js"&gt;&lt;/script&gt;</code></pre>
                                
                                <p class="mt-3"><strong>ฟังก์ชันที่ได้:</strong></p>
                                <ul class="small">
                                    <li>Auto-initialize settings</li>
                                    <li>Error recovery</li>
                                    <li>Export/Import settings</li>
                                    <li>Health monitoring</li>
                                    <li>Debug tools</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข <code>-ms-high-contrast</code> deprecation</li>
                                <li>✅ ใช้ <code>forced-colors</code> สำหรับ modern browsers</li>
                                <li>✅ เพิ่ม CSS fallbacks</li>
                                <li>✅ ปรับปรุง accessibility</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข theme settings warning</li>
                                <li>✅ Auto-initialize default settings</li>
                                <li>✅ เพิ่ม error handling</li>
                                <li>✅ เพิ่ม debug tools</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // ทดสอบ browser support
    checkBrowserSupport();
    
    // แสดงสถานะ theme settings
    setTimeout(function() {
        if (typeof themeSettingsFix !== 'undefined') {
            document.getElementById('theme-test-result').innerHTML = 
                '<div class="alert alert-success"><i class="fal fa-check"></i> Theme Settings Fix loaded successfully</div>';
        }
    }, 1000);
});

function testHighContrast() {
    const result = document.getElementById('contrast-test-result');
    
    // ทดสอบ CSS media queries
    const tests = [
        {
            name: 'Forced Colors (Modern)',
            query: '(forced-colors: active)',
            modern: true
        },
        {
            name: 'High Contrast (Legacy)',
            query: '(-ms-high-contrast: active)',
            modern: false
        },
        {
            name: 'Prefers Reduced Motion',
            query: '(prefers-reduced-motion: reduce)',
            modern: true
        },
        {
            name: 'Prefers Color Scheme Dark',
            query: '(prefers-color-scheme: dark)',
            modern: true
        }
    ];
    
    let html = '<h6>High Contrast & Accessibility Tests:</h6>';
    
    tests.forEach(test => {
        const supported = window.matchMedia(test.query).matches;
        const icon = supported ? 'fa-check text-success' : 'fa-times text-muted';
        const badge = test.modern ? 'badge-primary' : 'badge-secondary';
        
        html += `
            <div class="d-flex justify-content-between align-items-center py-1">
                <span>
                    <i class="fal ${icon}"></i> ${test.name}
                    <span class="badge ${badge} ml-2">${test.modern ? 'Modern' : 'Legacy'}</span>
                </span>
                <span class="text-muted">${supported ? 'Active' : 'Inactive'}</span>
            </div>
        `;
    });
    
    result.innerHTML = `<div class="alert alert-info">${html}</div>`;
}

function testForcedColors() {
    const result = document.getElementById('contrast-test-result');
    
    if (window.matchMedia('(forced-colors: active)').matches) {
        result.innerHTML = '<div class="alert alert-success"><i class="fal fa-check"></i> Forced Colors Mode is active</div>';
    } else {
        result.innerHTML = '<div class="alert alert-info"><i class="fal fa-info"></i> Forced Colors Mode is not active (normal)</div>';
    }
}

function testAccessibility() {
    const result = document.getElementById('contrast-test-result');
    
    const features = [
        {
            name: 'Focus Indicators',
            test: () => {
                const style = getComputedStyle(document.querySelector('.btn:focus') || document.createElement('div'));
                return style.outline !== 'none';
            }
        },
        {
            name: 'Reduced Motion Support',
            test: () => window.matchMedia('(prefers-reduced-motion)').media !== 'not all'
        },
        {
            name: 'High Contrast Support',
            test: () => window.matchMedia('(forced-colors)').media !== 'not all'
        },
        {
            name: 'Color Scheme Support',
            test: () => window.matchMedia('(prefers-color-scheme)').media !== 'not all'
        }
    ];
    
    let html = '<h6>Accessibility Features:</h6>';
    
    features.forEach(feature => {
        const supported = feature.test();
        const icon = supported ? 'fa-check text-success' : 'fa-times text-danger';
        
        html += `
            <div class="d-flex justify-content-between align-items-center py-1">
                <span><i class="fal ${icon}"></i> ${feature.name}</span>
                <span class="text-muted">${supported ? 'Supported' : 'Not Supported'}</span>
            </div>
        `;
    });
    
    result.innerHTML = `<div class="alert alert-info">${html}</div>`;
}

function checkBrowserSupport() {
    const table = document.getElementById('browser-support-table');
    
    const features = [
        {
            name: 'CSS Custom Properties',
            test: () => CSS.supports('color', 'var(--test)'),
            fallback: 'Static values'
        },
        {
            name: 'CSS Grid',
            test: () => CSS.supports('display', 'grid'),
            fallback: 'Flexbox'
        },
        {
            name: 'Forced Colors',
            test: () => window.matchMedia('(forced-colors)').media !== 'not all',
            fallback: '-ms-high-contrast'
        },
        {
            name: 'LocalStorage',
            test: () => typeof Storage !== 'undefined',
            fallback: 'Session storage'
        },
        {
            name: 'Fetch API',
            test: () => typeof fetch !== 'undefined',
            fallback: 'XMLHttpRequest'
        }
    ];
    
    let html = '';
    
    features.forEach(feature => {
        const supported = feature.test();
        const statusClass = supported ? 'text-success' : 'text-warning';
        const statusIcon = supported ? 'fa-check' : 'fa-exclamation-triangle';
        const statusText = supported ? 'Supported' : 'Fallback';
        
        html += `
            <tr>
                <td>${feature.name}</td>
                <td class="${statusClass}">
                    <i class="fal ${statusIcon}"></i> ${supported ? 'Yes' : 'No'}
                </td>
                <td>${feature.fallback}</td>
                <td class="${statusClass}">${statusText}</td>
            </tr>
        `;
    });
    
    table.innerHTML = html;
}
</script>

<style>
/* ทดสอบ CSS ใหม่ */
.test-high-contrast {
    background: ButtonFace;
    color: ButtonText;
    border: 1px solid ButtonText;
}

@media (forced-colors: active) {
    .test-high-contrast {
        forced-color-adjust: none;
    }
}

.test-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

@supports not (display: grid) {
    .test-grid {
        display: flex;
        flex-wrap: wrap;
    }
    
    .test-grid > * {
        flex: 1 1 45%;
        margin: 0.5rem;
    }
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.small {
    font-size: 0.875rem;
}
</style>
