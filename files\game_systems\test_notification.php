<?php
/**
 * Test notification system
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

// Define database constants if not defined
if (!defined('DATABASE_CCA')) {
    define('DATABASE_CCA', $dbinfo['database']);
}
if (!defined('DATABASE_SV')) {
    define('DATABASE_SV', $dbinfo['database']);
}
if (!defined('DATABASE_ACC')) {
    define('DATABASE_ACC', $dbinfo['database']);
}

echo "<h2>Testing Notification System</h2>";

try {
    // Connect to database
    $serverName = $dbinfo['host'];
    if (isset($dbinfo['port']) && $dbinfo['port']) {
        $serverName .= "," . $dbinfo['port'];
    }
    
    $connectionOptions = [
        "Database" => $dbinfo['database'],
        "Uid" => $dbinfo['username'],
        "PWD" => $dbinfo['password'],
        "CharacterSet" => "UTF-8"
    ];
    
    $connection = sqlsrv_connect($serverName, $connectionOptions);
    
    if ($connection === false) {
        $errors = sqlsrv_errors();
        $errorMessage = "Database connection failed: ";
        foreach ($errors as $error) {
            $errorMessage .= $error['message'] . " ";
        }
        throw new Exception($errorMessage);
    }
    
    echo "✅ Database connection successful<br>";
    
    // Test 1: Check if notifications table exists
    echo "<h3>1. Check notifications table</h3>";
    $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'notifications'";
    $stmt = sqlsrv_query($connection, $sql);
    if ($stmt && $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        if ($row['count'] > 0) {
            echo "✅ notifications table exists<br>";
            
            // Check table structure
            $sql = "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'notifications'";
            $stmt = sqlsrv_query($connection, $sql);
            echo "<strong>Table structure:</strong><br>";
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                echo "- {$row['COLUMN_NAME']} ({$row['DATA_TYPE']})<br>";
            }
        } else {
            echo "❌ notifications table does NOT exist<br>";
            echo "Please run database_setup_sqlserver.sql first<br>";
        }
    }
    
    // Test 2: Test notification creation
    echo "<h3>2. Test notification creation</h3>";
    
    // Include createNotification function
    function createNotification($connection, $data) {
        try {
            $notification_id = uniqid('notif_', true);
            $title = "Item Sent Successfully";
            $message = "Item sent to {$data['player_username']} via {$data['send_method']}";
            $type = 'item_send';
            $priority = 'normal';
            $created_at = date('Y-m-d H:i:s');
            $is_read = 0;
            $admin_username = $data['admin_username'] ?? 'System';
            
            $details = json_encode([
                'send_id' => $data['send_id'],
                'player_username' => $data['player_username'],
                'item_id' => $data['item_id'],
                'item_code' => $data['item_code'],
                'options_code' => $data['options_code'],
                'quantity' => $data['quantity'],
                'duration' => $data['duration'],
                'send_method' => $data['send_method'],
                'status' => $data['status']
            ]);
            
            $sql = "INSERT INTO notifications (
                notification_id, title, message, type, priority, details,
                admin_username, created_at, is_read, expires_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, DATEADD(day, 7, GETDATE()))";
            
            $params = array(
                $notification_id, $title, $message, $type, $priority, $details,
                $admin_username, $created_at, $is_read
            );
            
            echo "SQL: $sql<br>";
            echo "Params: " . print_r($params, true) . "<br>";
            
            $stmt = sqlsrv_prepare($connection, $sql, $params);
            
            if ($stmt === false) {
                $errors = sqlsrv_errors();
                echo "❌ Prepare failed: " . print_r($errors, true) . "<br>";
                return null;
            }
            
            if (!sqlsrv_execute($stmt)) {
                $errors = sqlsrv_errors();
                echo "❌ Execute failed: " . print_r($errors, true) . "<br>";
                return null;
            }
            
            echo "✅ Notification created successfully: $notification_id<br>";
            return $notification_id;
            
        } catch (Exception $e) {
            echo "❌ Exception: " . $e->getMessage() . "<br>";
            return null;
        }
    }
    
    // Test data
    $test_data = [
        'send_id' => 999,
        'player_username' => 'test_player',
        'item_id' => 123,
        'item_code' => '0000007B00000000',
        'options_code' => '0000000000000000',
        'quantity' => 1,
        'duration' => 31,
        'send_method' => 'mail',
        'status' => 'sent_to_mail',
        'admin_username' => 'test_admin'
    ];
    
    $notification_id = createNotification($connection, $test_data);
    
    if ($notification_id) {
        echo "<h3>3. Verify notification in database</h3>";
        $sql = "SELECT * FROM notifications WHERE notification_id = ?";
        $stmt = sqlsrv_prepare($connection, $sql, array($notification_id));
        
        if ($stmt && sqlsrv_execute($stmt)) {
            $notification = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
            if ($notification) {
                echo "✅ Notification found in database:<br>";
                echo "<pre>" . print_r($notification, true) . "</pre>";
                
                // Clean up test notification
                $delete_sql = "DELETE FROM notifications WHERE notification_id = ?";
                $delete_stmt = sqlsrv_prepare($connection, $delete_sql, array($notification_id));
                if ($delete_stmt && sqlsrv_execute($delete_stmt)) {
                    echo "✅ Test notification cleaned up<br>";
                }
            } else {
                echo "❌ Notification not found in database<br>";
            }
        }
    }
    
    // Test 3: Test notification API
    echo "<h3>4. Test notification API</h3>";
    echo "You can test the notification API with this JavaScript:<br>";
    echo "<pre>";
    echo "fetch('notification_system.php?admin=test_admin&limit=5')
    .then(r => r.json())
    .then(console.log);";
    echo "</pre>";
    
    sqlsrv_close($connection);
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h3>5. Next Steps</h3>";
echo "<ul>";
echo "<li>If notifications table doesn't exist, run database_setup_sqlserver.sql</li>";
echo "<li>If notification creation fails, check the error messages above</li>";
echo "<li>Test the notification API using the JavaScript code</li>";
echo "<li>Check browser console for any JavaScript errors</li>";
echo "</ul>";
?>
