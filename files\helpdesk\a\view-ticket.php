<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code. 
 */
$user->restrictionUser(true, $conn);
?>
<div class="page-header"><h1><?php echo PT_MANAGETICKETS; ?> <small><?php echo PT_MANAGETICKETS_DESC; ?></small></h1></div>

<?php
// variable
$getTID = filter_input(INPUT_GET, 'tid', FILTER_VALIDATE_INT);

// get ticket by the id
$selectTicket = "SELECT * FROM WEB_H_Tickets WHERE id = '$getTID'";
$selectTicketParam = array();
$selectTicketOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
$selectTicketQuery = sqlsrv_query($conn, $selectTicket, $selectTicketParam, $selectTicketOpt);
$selectTicketRows = sqlsrv_num_rows($selectTicketQuery);

if ($selectTicketRows) {
    $selectTicketFetch = sqlsrv_fetch_array($selectTicketQuery, SQLSRV_FETCH_ASSOC);

    // select category
    $selectCategory = "SELECT * FROM WEB_H_Category WHERE id = '$selectTicketFetch[category]'";
    $selectCategoryQuery = sqlsrv_query($conn, $selectCategory, array());
    $selectCategoryFetch = sqlsrv_fetch_array($selectCategoryQuery, SQLSRV_FETCH_ASSOC);
    ?>
    <div class="row">
        <div class="panel panel-default">
            <div class="panel-body">
                <h2 class="text-green"><?php echo $selectTicketFetch['title']; ?></h2>
                <p><strong><?php echo T_CUSTOMERID; ?>:</strong> <?php echo $selectTicketFetch['PCustomerID']; ?> &middot; <strong><?php echo T_CREATEDIN; ?></strong> <?php echo date('d/m ' . T_AT . ' H:i', strtotime($selectTicketFetch['createdate'])); ?> &middot; <strong><?php echo T_CATEGORY; ?>: </strong><?php echo $selectCategoryFetch['name']; ?></p>
                <hr>
                <?php echo $selectTicketFetch['content']; ?>
                <hr>
                <a href="#replyForm" class="btn btn-sm btn-info btn-block" style="margin-bottom: 10px;"><?php echo T_GOTOREPLYFORM; ?></a>
                <?php
                $selectReply = "SELECT * FROM WEB_H_Reply WHERE TicketID = '$getTID' ORDER BY datereply DESC";
                $selectReplyParam = array();
                $selectReplyOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectReplyQuery = sqlsrv_query($conn, $selectReply, $selectReplyParam, $selectReplyOpt);

                if (sqlsrv_num_rows($selectReplyQuery)) {
                    while ($resReply = sqlsrv_fetch_array($selectReplyQuery, SQLSRV_FETCH_ASSOC)) {
                        ?>
                        <blockquote>
                            <h4><strong class="text-red">RE:</strong> <?php echo $selectTicketFetch['title']; ?> <?php echo $identReply = ($resReply['UserNum'] == $selectTicketFetch['PCustomerID'] ? '<strong>(' . T_AUTHOR . ')</strong>' : ($resReply['UserNum'] == $selectTicketFetch['ACustomerID'] ? '<strong class="text-red">(' . T_ADMIN . ')</strong>' : '(desconhecido)')); ?></h4>
                            <p><i class="glyphicon glyphicon-calendar"></i> <strong><?php echo T_REPLIEDIN; ?></strong> <?php echo date('d/m ' . T_AT . ' H:i', strtotime($resReply['datereply'])); ?> <?php if ($resReply['attach']) { ?>&middot; <i class="glyphicon glyphicon-paperclip"></i> <?php echo T_ATTACH; ?>: <a href="http://<?php echo $resReply['attach']; ?>" target="_blank">Link to attach</a><?php } ?></p>
                            <hr>
                            <?php echo $resReply['content']; ?>
                        </blockquote>
                        <hr>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
    <div class="row" id="replyForm">
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="col-lg-12">
                    <?php
                    $formReply = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                    if (isset($formReply['btn_reply'])) {
                        // variables
                        // to this form we use filter_input_array to get all inputs with security
                        // conditions
                        if (empty($formReply['content'])) {
                            $returnWarning = W_H_REPLY_CONTENT_EMPTY;
                        } else if (strlen($formReply['content']) <= 12) {
                            $returnWarning = W_H_REPLY_CONTENT_LENGHT;
                        } else {

                            // variables
                            // convert all text from text editor to get HTML format
                            //$content = htmlspecialchars($formReply['content']); - not needed
                            $status = ($formReply['status'] == '-1' ? $formReply['status'] = '2' : $formReply['status']);
                            // insert reply
                            $insertReply = "INSERT INTO WEB_H_Reply (TicketID, UserNum, content, attach) VALUES (?, ?, ?, ?)";
                            $insertReplyParam = array($getTID, $getCustomerID, $formReply['content'], $formReply['attach']);
                            $insertReplyQuery = sqlsrv_query($conn, $insertReply, $insertReplyParam);

                            // update ticket
                            $updateStatus = "UPDATE WEB_H_Tickets SET status = '$status' WHERE id = '$getTID'";
                            $updateStatusParam = array();
                            $updateStatusQuery = sqlsrv_query($conn, $updateStatus, $updateStatusParam);

                            // simple verify to secutity
                            // update only if user logged is really a admin (DEV)
                            if ($userLogin->recUserAccount('IsDeveloper', $conn)) {
                                // update ticket
                                $updateTicket = "UPDATE WEB_H_Tickets SET ACustomerID = '$getCustomerID' WHERE id = '$getTID'";
                                $updateTicketParam = array();
                                $updateTicketQuery = sqlsrv_query($conn, $updateTicket, $updateTicketParam);
                            }

                            if (sqlsrv_rows_affected($insertReplyQuery)) {
                                header('Location: ?url=helpdesk/a/view-ticket&tid=' . $getTID . '&action=send#replyForm');
                            } else {
                                $returnError = E_REPLY;
                            }
                        }
                    }
                    if (isset($getAction) && $getAction == 'send') {
                        $returnSuccess = S_REPLY;
                    }
                    ?>
                    <?php if ($selectTicketFetch['status'] == '3') { ?>
                        <div class="alert alert-danger"><?php echo T_ADM_TICKET_CLOSED; ?></div>
                    <?php } else if ($selectTicketFetch['status'] == '4') { ?>
                        <div class="alert alert-success"><?php echo T_ADM_TICKET_SOLVED; ?></div>
                    <?php } ?>
                    <?php if (isset($returnSuccess)) { ?>
                        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                    <?php } elseif (isset($returnWarning)) { ?>
                        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                    <?php } elseif (isset($returnError)) { ?>
                        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                    <?php } ?>
                    <form name="formReply" method="post" action="" role="form">
                        <h2 class="text-red"><?php echo T_REPLY; ?></h2>
                        <div class="form-group">
                            <textarea class="form-control" name="content" placeholder="Reply the question ..." style="height: 200px"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="attach" class="text-red"><?php echo T_ATTACH; ?> <small><?php echo T_OPTIONAL; ?></small></label>
                            <input type="text" id="attach" name="attach" class="form-control" placeholder="Link, image or hosted file ...">
                            <span class="help-block text-red">without "http://" or "https://"!</span>
                        </div>

                        <div class="form-group">
                            <label class="text-red"><?php echo T_LABEL_TICKETSTATUS; ?></label>
                            <select name="status" class="form-control">
                                <option value="-1"><?php echo T_SELECT_STATUS; ?></option>
                                <option value="4"<?php echo $selectedStatusSolved = ($selectTicketFetch['status'] == '4' ? ' selected="selected"' : ''); ?>><?php echo B_SOLVED; ?></option>
                                <option value="3"<?php echo $selectedStatusClosed = ($selectTicketFetch['status'] == '3' ? ' selected="selected"' : ''); ?>><?php echo B_CLOSED; ?></option>
                            </select>
                            <span class="help-block text-red"><?php echo T_HB_CHANGESTATUS; ?></span>
                        </div>

                        <div class="form-group">
                            <input type="submit" class="btn btn-success" name="btn_reply" value="<?php echo B_SEND; ?>">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php } else { ?>
    <div class="row">
        <div class="col-lg-12">
            <span class="alert alert-warning block">Ticket n?o existe!</span>
        </div>
    </div>
<?php } ?>
