<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bell"></i> ทดสอบระบบการแจ้งเตือน
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบการแจ้งเตือนในมุมขวาบนและ Notification Area</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>เพิ่มแล้ว:</strong> ระบบการแจ้งเตือนแบบ Toast ในมุมขวาบน + Notification Area
                </div>
                
                <h5>🔔 ประเภทการแจ้งเตือน</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-danger text-center">
                            <div class="card-body">
                                <i class="fal fa-exclamation-triangle text-danger fa-2x mb-2"></i>
                                <h6>Danger</h6>
                                <button class="btn btn-danger btn-sm" onclick="testNotification('danger')">
                                    ทดสอบ
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning text-center">
                            <div class="card-body">
                                <i class="fal fa-exclamation text-warning fa-2x mb-2"></i>
                                <h6>Warning</h6>
                                <button class="btn btn-warning btn-sm" onclick="testNotification('warning')">
                                    ทดสอบ
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info text-center">
                            <div class="card-body">
                                <i class="fal fa-info-circle text-info fa-2x mb-2"></i>
                                <h6>Info</h6>
                                <button class="btn btn-info btn-sm" onclick="testNotification('info')">
                                    ทดสอบ
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success text-center">
                            <div class="card-body">
                                <i class="fal fa-check-circle text-success fa-2x mb-2"></i>
                                <h6>Success</h6>
                                <button class="btn btn-success btn-sm" onclick="testNotification('success')">
                                    ทดสอบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแจ้งเตือน</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="testSingleNotification()">
                            <i class="fal fa-bell"></i> ทดสอบการแจ้งเตือนเดียว
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testMultipleNotifications()">
                            <i class="fal fa-bells"></i> ทดสอบการแจ้งเตือนหลายรายการ
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testLongMessage()">
                            <i class="fal fa-align-left"></i> ทดสอบข้อความยาว
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="openCharacterMonitor()">
                            <i class="fal fa-external-link"></i> เปิด Character Monitor
                        </button>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 คุณสมบัติของระบบการแจ้งเตือน</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>🍞 Toast Notifications (มุมขวาบน)</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>✅ แสดงในมุมขวาบนของหน้าจอ</li>
                                    <li>✅ หายไปอัตโนมัติหลังจาก 5 วินาที</li>
                                    <li>✅ สามารถปิดด้วยตนเองได้</li>
                                    <li>✅ แสดงได้สูงสุด 3 รายการ</li>
                                    <li>✅ มี animation slide-in/out</li>
                                    <li>✅ แสดงไอคอนตามประเภท</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>📋 Notification Area</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>✅ เก็บประวัติการแจ้งเตือนทั้งหมด</li>
                                    <li>✅ แสดงอัตโนมัติเมื่อมีการแจ้งเตือน</li>
                                    <li>✅ สามารถซ่อน/แสดงได้</li>
                                    <li>✅ เก็บได้สูงสุด 20 รายการ</li>
                                    <li>✅ แสดงเวลาที่แจ้งเตือน</li>
                                    <li>✅ สามารถลบทีละรายการได้</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔧 การแก้ไขที่ทำ</h5>
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. เพิ่ม Toast Notifications
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ฟังก์ชันใหม่:</strong></p>
                                <pre><code>function showToastNotification(message, type, time) {
    // สร้าง toast container
    // สร้าง toast element
    // แสดงด้วย animation
    // ลบอัตโนมัติหลังจาก 5 วินาที
}</code></pre>
                                
                                <p><strong>CSS ใหม่:</strong></p>
                                <ul>
                                    <li>.toast-container - ตำแหน่งมุมขวาบน</li>
                                    <li>.toast-notification - สไตล์ของ toast</li>
                                    <li>.toast-danger, .toast-warning, etc. - สีตามประเภท</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. ปรับปรุง Notification Area
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การปรับปรุง:</strong></p>
                                <ul>
                                    <li>แสดง notification area อัตโนมัติเมื่อมีการแจ้งเตือน</li>
                                    <li>อัพเดทปุ่ม toggle ให้ตรงกับสถานะ</li>
                                    <li>เพิ่ม animation สำหรับ notification items</li>
                                </ul>
                                
                                <p><strong>ฟังก์ชันที่ปรับปรุง:</strong></p>
                                <pre><code>function addNotificationToArea(message, type, time) {
    // เพิ่ม notification ใน area
    // แสดง area อัตโนมัติ
    // แสดง toast notification
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 วิธีใช้งาน</h5>
                <div class="alert alert-info">
                    <h6><i class="fal fa-info-circle"></i> การใช้งานระบบการแจ้งเตือน:</h6>
                    <ol class="mb-0">
                        <li>เปิด Character Monitor</li>
                        <li>เมื่อมีการแจ้งเตือน จะแสดงทั้ง Toast (มุมขวาบน) และ Notification Area</li>
                        <li>Toast จะหายไปอัตโนมัติหลังจาก 5 วินาที</li>
                        <li>Notification Area จะเก็บประวัติการแจ้งเตือนไว้</li>
                        <li>สามารถซ่อน/แสดง Notification Area ได้ด้วยปุ่ม</li>
                        <li>สามารถลบการแจ้งเตือนทีละรายการหรือทั้งหมดได้</li>
                    </ol>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ เพิ่ม Toast Notifications ในมุมขวาบน</li>
                                <li>✅ แสดง Notification Area อัตโนมัติ</li>
                                <li>✅ เพิ่ม CSS สำหรับ Toast</li>
                                <li>✅ ปรับปรุง addNotificationToArea</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Toast หายไปอัตโนมัติ</li>
                                <li>✅ จำกัดจำนวน Toast (3 รายการ)</li>
                                <li>✅ Animation slide-in/out</li>
                                <li>✅ สีและไอคอนตามประเภท</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// ฟังก์ชันสำหรับทดสอบการแจ้งเตือน
function testNotification(type) {
    const messages = {
        'danger': '❌ เกิดข้อผิดพลาดในระบบ - กรุณาตรวจสอบ',
        'warning': '⚠️ พบการเข้าใช้งานที่น่าสงสัย',
        'info': 'ℹ️ ระบบได้รับการอัพเดทเรียบร้อยแล้ว',
        'success': '✅ การดำเนินการสำเร็จ'
    };
    
    const message = messages[type] || 'ทดสอบการแจ้งเตือน';
    const time = new Date().toLocaleString('th-TH');
    
    // เรียกใช้ฟังก์ชันจาก Character Monitor
    if (typeof addNotificationToArea === 'function') {
        addNotificationToArea(message, type, time);
    } else {
        // สร้าง mock notification ถ้าไม่มีฟังก์ชัน
        showMockToast(message, type, time);
    }
}

function testSingleNotification() {
    testNotification('info');
}

function testMultipleNotifications() {
    testNotification('success');
    setTimeout(() => testNotification('warning'), 500);
    setTimeout(() => testNotification('danger'), 1000);
    setTimeout(() => testNotification('info'), 1500);
}

function testLongMessage() {
    const longMessage = '📊 ระบบได้ตรวจพบการสร้างตัวละครใหม่จำนวนมากในช่วงเวลาสั้น ๆ กรุณาตรวจสอบกิจกรรมที่อาจเป็นการใช้งานที่ผิดปกติ และดำเนินการตามขั้นตอนความปลอดภัยที่กำหนด';
    const time = new Date().toLocaleString('th-TH');
    
    if (typeof addNotificationToArea === 'function') {
        addNotificationToArea(longMessage, 'warning', time);
    } else {
        showMockToast(longMessage, 'warning', time);
    }
}

function showMockToast(message, type, time) {
    // สร้าง mock toast สำหรับทดสอบ
    let toastContainer = document.getElementById('mock-toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'mock-toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        `;
        document.body.appendChild(toastContainer);
    }
    
    const toast = document.createElement('div');
    toast.style.cssText = `
        min-width: 300px;
        max-width: 400px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        margin-bottom: 10px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease-in-out;
        border-left: 4px solid ${type === 'danger' ? '#dc3545' : type === 'warning' ? '#ffc107' : type === 'success' ? '#28a745' : '#17a2b8'};
    `;
    
    const typeClass = type === 'danger' ? 'text-danger' : type === 'warning' ? 'text-warning' : type === 'success' ? 'text-success' : 'text-info';
    const icon = type === 'danger' ? 'fa-exclamation-triangle' : type === 'warning' ? 'fa-exclamation' : type === 'success' ? 'fa-check-circle' : 'fa-info-circle';
    
    toast.innerHTML = `
        <div style="display: flex; align-items: center; padding: 0.5rem 0.75rem; background-color: rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.05);">
            <i class="fal ${icon} ${typeClass}" style="margin-right: 8px;"></i>
            <strong style="margin-right: auto;">การแจ้งเตือน</strong>
            <small style="color: #6c757d;">${time || new Date().toLocaleTimeString('th-TH')}</small>
            <button onclick="this.closest('div').parentElement.remove()" style="background: none; border: none; font-size: 1.25rem; margin-left: 8px; cursor: pointer;">&times;</button>
        </div>
        <div style="padding: 0.75rem; word-wrap: break-word;">
            ${message}
        </div>
    `;
    
    toastContainer.insertBefore(toast, toastContainer.firstChild);
    
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
.card {
    margin-bottom: 1rem;
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
