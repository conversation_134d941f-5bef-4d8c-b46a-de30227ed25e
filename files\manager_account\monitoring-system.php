<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Monitoring System Styles */
.monitoring-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.monitoring-dashboard::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.security-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 10px;
    padding: 15px;
    color: white;
    margin-bottom: 15px;
    animation: pulse-alert 2s infinite;
}

@keyframes pulse-alert {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.audit-log-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.log-entry {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.log-entry:hover {
    background-color: #f8f9fa;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.log-action {
    font-weight: 600;
    margin: 5px 0;
}

.log-details {
    font-size: 13px;
    color: #777;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online { background: #5cb85c; }
.status-offline { background: #d9534f; }
.status-warning { background: #f0ad4e; }
.status-banned { background: #333; }

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-ban {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-unban {
    background: linear-gradient(135deg, #5cb85c, #449d44);
    color: white;
}

.btn-reset {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
    color: white;
}

.btn-view {
    background: linear-gradient(135deg, #0088cc, #0066aa);
    color: white;
}

.monitoring-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #667eea;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.real-time-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 8px;
    height: 8px;
    background: #5cb85c;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.filter-controls {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .monitoring-stats {
        grid-template-columns: 1fr;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .action-buttons {
        justify-content: center;
    }
}
</style>

<header class="page-header">
    <h2>ระบบจัดการและตรวจสอบ (Monitoring & Management) <span class="real-time-indicator" title="อัปเดตแบบ Real-time"></span></h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Monitoring System</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Get filter parameters
$filterType = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$filterStatus = isset($_GET['status']) ? $_GET['status'] : 'all';
$filterTime = isset($_GET['time']) ? $_GET['time'] : '24';

// Security monitoring queries
$selectSecurityAlerts = "SELECT 
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -1, GETDATE()) 
          AND UserNum IN (
              SELECT UserNum FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
              WHERE LoginTime >= DATEADD(hour, -1, GETDATE())
              GROUP BY UserNum 
              HAVING COUNT(*) > 5
          ) THEN 1 END) as suspicious_logins,
    COUNT(CASE WHEN AuthType = 2 THEN 1 END) as banned_accounts,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -24, GETDATE()) THEN 1 END) as recent_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";

$securityQuery = sqlsrv_query($conn, $selectSecurityAlerts);
if ($securityQuery === false) {
    $securityStats = array(
        'suspicious_logins' => 0,
        'banned_accounts' => 0,
        'online_accounts' => 0,
        'recent_logins' => 0
    );
} else {
    $securityStats = sqlsrv_fetch_array($securityQuery, SQLSRV_FETCH_ASSOC);
}

// Recent account activities
$selectRecentActivities = "SELECT TOP 20
    ID,
    UserNum,
    LoginTime,
    LogoutTime,
    LastIp,
    Login,
    AuthType,
    PlayTime
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(hour, -$filterTime, GETDATE())
    ORDER BY LoginTime DESC";

$activitiesQuery = sqlsrv_query($conn, $selectRecentActivities);
$recentActivities = array();
if ($activitiesQuery !== false) {
    while ($row = sqlsrv_fetch_array($activitiesQuery, SQLSRV_FETCH_ASSOC)) {
        $recentActivities[] = $row;
    }
}

// Handle admin actions
if (isset($_POST['action']) && isset($_POST['user_id'])) {
    $action = $_POST['action'];
    $userId = $_POST['user_id'];
    $adminId = $_SESSION['user_id'] ?? 'admin';
    
    switch ($action) {
        case 'ban':
            $banQuery = "UPDATE [".DATABASE_ACC."].[dbo].cabal_auth_table SET AuthType = 2 WHERE UserNum = ?";
            $banResult = sqlsrv_query($conn, $banQuery, array($userId));
            if ($banResult) {
                // Log the action
                logAdminAction($conn, $adminId, 'BAN_USER', "Banned user ID: $userId");
                $message = "ผู้เล่นถูกแบนเรียบร้อยแล้ว";
                $messageType = "success";
            }
            break;
            
        case 'unban':
            $unbanQuery = "UPDATE [".DATABASE_ACC."].[dbo].cabal_auth_table SET AuthType = 1 WHERE UserNum = ?";
            $unbanResult = sqlsrv_query($conn, $unbanQuery, array($userId));
            if ($unbanResult) {
                logAdminAction($conn, $adminId, 'UNBAN_USER', "Unbanned user ID: $userId");
                $message = "ผู้เล่นถูกปลดแบนเรียบร้อยแล้ว";
                $messageType = "success";
            }
            break;
            
        case 'reset_password':
            // In a real implementation, you would generate a new password
            logAdminAction($conn, $adminId, 'RESET_PASSWORD', "Reset password for user ID: $userId");
            $message = "รีเซ็ตรหัสผ่านเรียบร้อยแล้ว";
            $messageType = "info";
            break;
    }
}

function logAdminAction($conn, $adminId, $action, $details) {
    // In a real implementation, you would have a dedicated audit log table
    // For now, we'll just simulate logging
    $logQuery = "INSERT INTO WEB_AdminLog (AdminID, Action, Details, Timestamp) VALUES (?, ?, ?, GETDATE())";
    // This would be executed if the table exists
}
?>

<?php if (isset($message)): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="close" data-dismiss="alert">
        <span>&times;</span>
    </button>
</div>
<?php endif; ?>

<div class="row">
    <!-- Monitoring Dashboard -->
    <div class="col-md-12">
        <div class="monitoring-dashboard">
            <h3><i class="fa fa-shield"></i> Security Monitoring Dashboard</h3>
            <p>ระบบตรวจสอบความปลอดภัยและจัดการบัญชีผู้เล่นแบบ Real-time</p>

            <?php if ($securityStats['suspicious_logins'] > 0): ?>
            <div class="security-alert">
                <i class="fa fa-exclamation-triangle"></i>
                <strong>แจ้งเตือนความปลอดภัย:</strong>
                พบการเข้าสู่ระบบที่น่าสงสัย <?php echo $securityStats['suspicious_logins']; ?> รายการ
                <button class="btn btn-sm btn-light ml-2" onclick="investigateAlerts()">
                    ตรวจสอบ
                </button>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="row">
    <!-- Monitoring Statistics -->
    <div class="col-md-12">
        <div class="monitoring-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fa fa-users"></i>
                </div>
                <div class="stat-value"><?php echo number_format($securityStats['online_accounts']); ?></div>
                <div class="stat-label">ออนไลน์ปัจจุบัน</div>
                <span class="status-indicator status-online"></span>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fa fa-ban"></i>
                </div>
                <div class="stat-value"><?php echo number_format($securityStats['banned_accounts']); ?></div>
                <div class="stat-label">บัญชีที่ถูกแบน</div>
                <span class="status-indicator status-banned"></span>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fa fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value"><?php echo number_format($securityStats['suspicious_logins']); ?></div>
                <div class="stat-label">กิจกรรมน่าสงสัย</div>
                <span class="status-indicator status-warning"></span>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fa fa-clock-o"></i>
                </div>
                <div class="stat-value"><?php echo number_format($securityStats['recent_logins']); ?></div>
                <div class="stat-label">เข้าสู่ระบบ 24 ชม.</div>
                <span class="status-indicator status-online"></span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filter Controls -->
    <div class="col-md-12">
        <div class="filter-controls">
            <form method="GET" action="">
                <input type="hidden" name="url" value="manager_account/monitoring-system">

                <div class="filter-group">
                    <span class="filter-label">ประเภทกิจกรรม:</span>
                    <div class="btn-group" role="group">
                        <a href="?url=manager_account/monitoring-system&filter=all&status=<?php echo $filterStatus; ?>&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterType == 'all' ? 'btn-primary' : 'btn-default'; ?>">
                            ทั้งหมด
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=login&status=<?php echo $filterStatus; ?>&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterType == 'login' ? 'btn-primary' : 'btn-default'; ?>">
                            เข้าสู่ระบบ
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=suspicious&status=<?php echo $filterStatus; ?>&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterType == 'suspicious' ? 'btn-primary' : 'btn-default'; ?>">
                            น่าสงสัย
                        </a>
                    </div>
                </div>

                <div class="filter-group">
                    <span class="filter-label">สถานะ:</span>
                    <div class="btn-group" role="group">
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=all&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterStatus == 'all' ? 'btn-primary' : 'btn-default'; ?>">
                            ทั้งหมด
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=online&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterStatus == 'online' ? 'btn-primary' : 'btn-default'; ?>">
                            ออนไลน์
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=banned&time=<?php echo $filterTime; ?>"
                           class="btn btn-sm <?php echo $filterStatus == 'banned' ? 'btn-primary' : 'btn-default'; ?>">
                            ถูกแบน
                        </a>
                    </div>
                </div>

                <div class="filter-group">
                    <span class="filter-label">ช่วงเวลา:</span>
                    <div class="btn-group" role="group">
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=<?php echo $filterStatus; ?>&time=1"
                           class="btn btn-sm <?php echo $filterTime == '1' ? 'btn-primary' : 'btn-default'; ?>">
                            1 ชั่วโมง
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=<?php echo $filterStatus; ?>&time=24"
                           class="btn btn-sm <?php echo $filterTime == '24' ? 'btn-primary' : 'btn-default'; ?>">
                            24 ชั่วโมง
                        </a>
                        <a href="?url=manager_account/monitoring-system&filter=<?php echo $filterType; ?>&status=<?php echo $filterStatus; ?>&time=168"
                           class="btn btn-sm <?php echo $filterTime == '168' ? 'btn-primary' : 'btn-default'; ?>">
                            7 วัน
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Activities -->
    <div class="col-md-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">กิจกรรมล่าสุด</h2>
            </header>
            <div class="panel-body">
                <div class="audit-log-container">
                    <?php if (empty($recentActivities)): ?>
                        <div class="text-center py-4">
                            <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่พบกิจกรรมในช่วงเวลาที่เลือก</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentActivities as $activity): ?>
                            <div class="log-entry">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="log-timestamp">
                                            <?php echo $activity['LoginTime'] ? date('Y-m-d H:i:s', strtotime($activity['LoginTime'])) : 'N/A'; ?>
                                        </div>
                                        <div class="log-action">
                                            <span class="status-indicator status-<?php echo $activity['Login'] == 1 ? 'online' : 'offline'; ?>"></span>
                                            ผู้เล่น: <strong><?php echo htmlspecialchars($activity['ID']); ?></strong>
                                            <?php if ($activity['Login'] == 1): ?>
                                                <span class="label label-success">ออนไลน์</span>
                                            <?php elseif ($activity['AuthType'] == 2): ?>
                                                <span class="label label-danger">ถูกแบน</span>
                                            <?php else: ?>
                                                <span class="label label-default">ออฟไลน์</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="log-details">
                                            IP: <?php echo htmlspecialchars($activity['LastIp'] ?? 'N/A'); ?> |
                                            เวลาเล่น: <?php echo round(($activity['PlayTime'] ?? 0) / 60, 1); ?> ชั่วโมง
                                            <?php if ($activity['LogoutTime']): ?>
                                                | ออกจากระบบ: <?php echo date('H:i:s', strtotime($activity['LogoutTime'])); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <div class="action-buttons">
                                            <button class="btn-action btn-view" onclick="viewUserDetails(<?php echo $activity['UserNum']; ?>)">
                                                <i class="fa fa-eye"></i> ดูรายละเอียด
                                            </button>

                                            <?php if ($activity['AuthType'] != 2): ?>
                                                <button class="btn-action btn-ban" onclick="confirmAction('ban', <?php echo $activity['UserNum']; ?>, '<?php echo htmlspecialchars($activity['ID']); ?>')">
                                                    <i class="fa fa-ban"></i> แบน
                                                </button>
                                            <?php else: ?>
                                                <button class="btn-action btn-unban" onclick="confirmAction('unban', <?php echo $activity['UserNum']; ?>, '<?php echo htmlspecialchars($activity['ID']); ?>')">
                                                    <i class="fa fa-check"></i> ปลดแบน
                                                </button>
                                            <?php endif; ?>

                                            <button class="btn-action btn-reset" onclick="confirmAction('reset_password', <?php echo $activity['UserNum']; ?>, '<?php echo htmlspecialchars($activity['ID']); ?>')">
                                                <i class="fa fa-key"></i> รีเซ็ตรหัส
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Action Confirmation Modal -->
<div class="modal fade" id="actionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">ยืนยันการดำเนินการ</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="actionMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn">ยืนยัน</button>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">รายละเอียดผู้เล่น</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>กำลังโหลดข้อมูล...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Monitoring System JavaScript
let currentAction = null;
let currentUserId = null;
let currentUserName = null;

function confirmAction(action, userId, userName) {
    currentAction = action;
    currentUserId = userId;
    currentUserName = userName;

    let actionText = '';
    switch(action) {
        case 'ban':
            actionText = 'แบนผู้เล่น';
            break;
        case 'unban':
            actionText = 'ปลดแบนผู้เล่น';
            break;
        case 'reset_password':
            actionText = 'รีเซ็ตรหัสผ่าน';
            break;
    }

    document.getElementById('actionMessage').innerHTML =
        `คุณต้องการ<strong>${actionText}</strong> ผู้เล่น <strong>${userName}</strong> หรือไม่?`;

    $('#actionModal').modal('show');
}

document.getElementById('confirmActionBtn').addEventListener('click', function() {
    if (currentAction && currentUserId) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = currentAction;

        const userIdInput = document.createElement('input');
        userIdInput.type = 'hidden';
        userIdInput.name = 'user_id';
        userIdInput.value = currentUserId;

        form.appendChild(actionInput);
        form.appendChild(userIdInput);
        document.body.appendChild(form);

        form.submit();
    }
});

function viewUserDetails(userId) {
    $('#userDetailsModal').modal('show');

    // Reset content
    document.getElementById('userDetailsContent').innerHTML = `
        <div class="text-center">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
            <p>กำลังโหลดข้อมูล...</p>
        </div>
    `;

    // Fetch user details via AJAX
    fetch(`?url=manager_account/get-user-details&user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                document.getElementById('userDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-triangle"></i> ${data.error}
                    </div>
                `;
                return;
            }

            const user = data.user;
            const characters = data.characters;
            const financial = data.financial;
            const stats = data.stats;

            let charactersHtml = '';
            if (characters.length > 0) {
                charactersHtml = characters.map(char => `
                    <tr>
                        <td>${char.CharName}</td>
                        <td>Level ${char.LEV}</td>
                        <td>${getNationText(char.Nation)}</td>
                        <td>${getClassText(char.Class)}</td>
                    </tr>
                `).join('');
            } else {
                charactersHtml = '<tr><td colspan="4" class="text-center">ไม่มีตัวละคร</td></tr>';
            }

            document.getElementById('userDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fa fa-user"></i> ข้อมูลบัญชี</h5>
                        <table class="table table-bordered table-striped">
                            <tr><td><strong>ชื่อผู้เล่น</strong></td><td>${user.ID}</td></tr>
                            <tr><td><strong>User ID</strong></td><td>${user.UserNum}</td></tr>
                            <tr><td><strong>สถานะ</strong></td><td><span class="label label-${getStatusClass(user.AuthType, user.Login)}">${user.Status}</span></td></tr>
                            <tr><td><strong>วันที่สร้าง</strong></td><td>${user.createDate || 'N/A'}</td></tr>
                            <tr><td><strong>เข้าสู่ระบบล่าสุด</strong></td><td>${user.LoginTime || 'N/A'}</td></tr>
                            <tr><td><strong>IP ล่าสุด</strong></td><td>${user.LastIp || 'N/A'}</td></tr>
                        </table>

                        <h5><i class="fa fa-money"></i> ข้อมูลทางการเงิน</h5>
                        <table class="table table-bordered table-striped">
                            <tr><td><strong>Cash</strong></td><td>${Number(financial.Cash || 0).toLocaleString()}</td></tr>
                            <tr><td><strong>Cash Bonus</strong></td><td>${Number(financial.CashBonus || 0).toLocaleString()}</td></tr>
                            <tr><td><strong>Cash รวม</strong></td><td>${Number(financial.CashTotal || 0).toLocaleString()}</td></tr>
                            <tr><td><strong>Reward Points</strong></td><td>${Number(financial.Reward || 0).toLocaleString()}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fa fa-bar-chart"></i> สถิติการเล่น</h5>
                        <table class="table table-bordered table-striped">
                            <tr><td><strong>เวลาเล่นรวม</strong></td><td>${stats.playTimeHours} ชั่วโมง</td></tr>
                            <tr><td><strong>จำนวนตัวละคร</strong></td><td>${stats.totalCharacters}</td></tr>
                            <tr><td><strong>Level สูงสุด</strong></td><td>${stats.maxLevel}</td></tr>
                            <tr><td><strong>Cash รวม</strong></td><td>${Number(stats.totalCash).toLocaleString()}</td></tr>
                        </table>

                        <h5><i class="fa fa-users"></i> ตัวละคร</h5>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>ชื่อ</th>
                                        <th>Level</th>
                                        <th>Nation</th>
                                        <th>Class</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${charactersHtml}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('userDetailsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการโหลดข้อมูล: ${error.message}
                </div>
            `;
        });
}

function getStatusClass(authType, login) {
    if (authType == 2) {
        return 'danger';
    } else if (login == 1) {
        return 'success';
    } else {
        return 'default';
    }
}

function getNationText(nationId) {
    const nations = {
        1: 'Capella',
        2: 'Procyon'
    };
    return nations[nationId] || 'Unknown';
}

function getClassText(classId) {
    const classes = {
        1: 'Warrior',
        2: 'Blader',
        3: 'Wizard',
        4: 'Force Archer',
        5: 'Force Shielder',
        6: 'Force Blader',
        7: 'Gladiator',
        8: 'Force Gunner',
        9: 'Dark Mage'
    };
    return classes[classId] || 'Unknown';
}

function investigateAlerts() {
    showNotification('กำลังตรวจสอบการแจ้งเตือนความปลอดภัย...', 'info');

    // In a real implementation, this would open a detailed security report
    setTimeout(function() {
        showNotification('พบการเข้าสู่ระบบจาก IP ที่แตกต่างกันในเวลาสั้น ๆ', 'warning');
    }, 2000);
}

function refreshMonitoring() {
    const indicator = document.querySelector('.real-time-indicator');
    if (indicator) {
        indicator.style.background = '#f0ad4e';
        indicator.style.animation = 'pulse 0.5s infinite';
    }

    showNotification('กำลังรีเฟรชข้อมูลการตรวจสอบ...', 'info');

    setTimeout(function() {
        if (indicator) {
            indicator.style.background = '#5cb85c';
            indicator.style.animation = 'pulse 2s infinite';
        }
        showNotification('ข้อมูลได้รับการอัปเดตแล้ว', 'success');
        window.location.reload();
    }, 2000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Update real-time indicators
    const indicators = document.querySelectorAll('.real-time-indicator');
    indicators.forEach(indicator => {
        indicator.style.background = '#f0ad4e';
        setTimeout(() => {
            indicator.style.background = '#5cb85c';
        }, 500);
    });
}, 30000);

// Initialize tooltips
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
