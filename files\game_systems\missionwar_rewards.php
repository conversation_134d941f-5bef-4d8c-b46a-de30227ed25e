<?php
// missionwar_rewards_ui.php - UI for managing WEB_MissionWar_rewards
?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Mission War Systems
        <small>
            แก้ไข อัพเดท เพิ่ม ลบ ข้อมูลระบบ War Data
        </small>
        <div class="message" id="resultNotice" style="display:none;"></div>
    </h1>
</div>
<div class="alert alert-primary alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">(วิธีใช้งาน)</span>
                <br>
                <code>Mission War Time Slots</code><code>STartTime</code> <code>EndTime</code>ไว้กำหนดช่วงเวลาในการจัดกิจกรรม Mission War ค่า <code>00:00:00.0000000</code> คือทุกช่วงเวลา
                <br>
                <code>Mission War Time Slots</code><code>TierMin</code> <code>TierMax</code> กำหนดคะแนนขั้นต่ำและสูงสุดของผู้เล่นที่ต้องทำให้ได้ก่อนถึงจะสามารถมีสิทธิ์รับรางวัล
                <br>
                <code>Mission War Time Slots</code><code>สถานะกิจกรรม</code> กำหนดกิจกรรม Mission War ว่าเปิดหรือปิดอยู่
                <br>
                <code>Mission War Rewards</code><code>SlotID</code> ต้องตรงกับ <code>SlotID</code> ใน <code>Mission War Time Slots</code> เพื่อให้ผู้เล่นสามารถรับรางวัลได้
                <br>
                <code>Mission War Rewards</code><code>ItemKindIdx</code> รหัสไอเท็มที่จะส่งให้ผู้เล่น
                <br>
                <code>Mission War Rewards</code> <code>Alz</code>สามารถเพิ่มการส่งเงิน Alz ให้ผู้เล่นได้
                <br>
                <code>Mission War Rewards</code> <code>สถานะกิจกรรม</code> กำหนดกิจกรรม Mission War ว่าเปิดหรือปิดอยู่ ต้องกำหนดจาก Mission War Time Slots 
                <br>
                <code>ทดสอบการส่งเมลล์</code> <code>ทดสอบ</code> สามารถทดสอบการส่งเมลล์รางวัลให้ผู้เล่นได้ โดยกรอก <code>CharacterIdx</code> ของผู้เล่นที่ต้องการส่งรางวัล 
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6 col-xl-6 order-lg-1 order-xl-1">
        <div class="card shadow-sm border-0 mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span><i class="fas fa-gift mr-2"></i>Mission War Rewards</span>
                <div>
                    <button class="btn btn-warning btn-sm" id="testSendMailBtn"><i class="fas fa-envelope"></i> ทดสอบส่งเมลล์รางวัล</button>
                    <button class="btn btn-success btn-sm mr-2" id="addRewardBtn"><i class="fas fa-plus"></i> เพิ่มรางวัลใหม่</button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive mb-0">
                    <table class="table table-sm table-hover mb-0" id="rewardsTable">
                        <thead class="thead-light">
                            <tr>
                                <th>RewardID</th>
                                <th>SlotID</th>
                                <th>ItemKindIdx</th>
                                <th>ItemOption</th>
                                <th>ItemDurationIdx</th>
                                <th>Alz</th>
                                <th>สถานะกิจกรรม</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-xl-6 order-lg-2 order-xl-2">
        <div class="card shadow-sm border-0 mb-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <span><i class="fas fa-clock mr-2"></i>Mission War Time Slots</span>
                <button class="btn btn-primary btn-sm" id="addTimeSlotBtn"><i class="fas fa-plus"></i> เพิ่ม Time Slot</button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive mb-0">
                    <table class="table table-sm table-hover mb-0" id="timeSlotsTable">
                        <thead class="thead-light">
                            <tr>
                                <th>SlotID</th>
                                <th>StartTime</th>
                                <th>EndTime</th>
                                <th>TierMin</th>
                                <th>TierMax</th>
                                <th>สถานะกิจกรรม</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Rewards -->
<div class="modal fade" id="rewardModal" tabindex="-1" role="dialog" aria-labelledby="rewardModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="rewardModalLabel">เพิ่ม/แก้ไขรางวัล</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="rewardForm">
      <div class="modal-body">
        <input type="hidden" id="RewardID" name="RewardID">
        <div class="form-group">
            <label for="SlotID">SlotID</label>
            <input type="number" class="form-control" id="SlotID" name="SlotID" required>
        </div>
        <div class="form-group">
            <label for="ItemKindIdx">ItemKindIdx</label>
            <div class="input-group">
                <input type="number" class="form-control" id="ItemKindIdx" name="ItemKindIdx" required readonly placeholder="รหัสที่ใช้บันทึก">
                <div class="input-group-append">
                    <select id="ItemKindType" class="form-control">
                        <option value="normal">ธรรมดา</option>
                        <option value="bound">ผูกมัดไอดี (+4096)</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="ItemOption">ItemOption</label>
            <input type="number" class="form-control" id="ItemOption" name="ItemOption" required>
        </div>
        <div class="form-group">
            <label for="ItemDurationIdx">ItemDurationIdx</label>
            <input type="number" class="form-control" id="ItemDurationIdx" name="ItemDurationIdx" required>
        </div>
        <div class="form-group">
            <label for="Alz">Alz</label>
            <input type="number" class="form-control" id="Alz" name="Alz" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
        <button type="submit" class="btn btn-primary">บันทึก</button>
      </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal: Time Slot -->
<div class="modal fade" id="timeSlotModal" tabindex="-1" role="dialog" aria-labelledby="timeSlotModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="timeSlotModalLabel">เพิ่ม/แก้ไข Time Slot</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="timeSlotForm">
      <div class="modal-body">
        <input type="hidden" id="TimeSlotID" name="SlotID">
        <div class="form-group">
            <label for="TimeSlotIDInput">SlotID</label>
            <input type="number" class="form-control" id="TimeSlotIDInput" name="SlotID" required>
        </div>
        <div class="form-group">
            <label for="StartTime">StartTime</label>
            <input type="time" step="1" class="form-control" id="StartTime" name="StartTime" required>
        </div>
        <div class="form-group">
            <label for="EndTime">EndTime</label>
            <input type="time" step="1" class="form-control" id="EndTime" name="EndTime" required>
        </div>
        <div class="form-group">
            <label for="TierMin">TierMin</label>
            <input type="number" class="form-control" id="TierMin" name="TierMin" required>
        </div>
        <div class="form-group">
            <label for="TierMax">TierMax</label>
            <input type="number" class="form-control" id="TierMax" name="TierMax" required>
        </div>
        <!-- Toggle: IsActive -->
        <div class="form-group">
            <label for="IsActive">เปิดใช้งานกิจกรรม</label>
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="IsActive" name="IsActive">
                <label class="custom-control-label" for="IsActive">เปิด/ปิด</label>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
        <button type="submit" class="btn btn-primary">บันทึก</button>
      </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal: Test Send Mail -->
<div class="modal fade" id="testSendMailModal" tabindex="-1" role="dialog" aria-labelledby="testSendMailLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="testSendMailLabel">ทดสอบส่งเมลล์รางวัล Mission War</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="testSendMailForm">
      <div class="modal-body">
        <div class="form-group">
            <label for="TestCharacterIdx">CharacterIdx</label>
            <input type="number" class="form-control" id="TestCharacterIdx" name="CharacterIdx" required value="">
        </div>
        <div class="form-group">
            <label for="TestAlz">Alz</label>
            <input type="number" class="form-control" id="TestAlz" name="Alz" required value="0" readonly>
        </div>
        <div class="form-group">
            <label for="TestItemKindIdx">ItemKindIdx</label>
            <input type="number" class="form-control" id="TestItemKindIdx" name="ItemKindIdx" required value="1" readonly>
        </div>
        <div class="form-group">
            <label for="TestItemOption">ItemOption</label>
            <input type="number" class="form-control" id="TestItemOption" name="ItemOption" required value="0" readonly>
        </div>
        <div class="form-group">
            <label for="TestItemDurationIdx">ItemDurationIdx</label>
            <input type="number" class="form-control" id="TestItemDurationIdx" name="ItemDurationIdx" required value="0" readonly>
        </div>
        <div class="form-group">
            <label for="TestMailExpiredDate">MailExpiredDate</label>
            <input type="number" class="form-control" id="TestMailExpiredDate" name="MailExpiredDate" required value="1" readonly>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
        <button type="submit" class="btn btn-warning">ส่งเมลล์ทดสอบ</button>
      </div>
      </form>
    </div>
  </div>
</div>
<script>
function getSlotColorClass(slotId) {
    // Detect dark mode: system or manual toggle (mod-skin-dark)
    let isDark = false;
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        isDark = true;
    }
    if (document.body.classList.contains('mod-skin-dark')) {
        isDark = true;
    }
    switch (parseInt(slotId)) {
        case 2: return isDark ? 'bg-success text-white' : 'table-success';
        case 3: return isDark ? 'bg-info text-white' : 'table-info';
        case 4: return isDark ? 'bg-warning text-dark' : 'table-warning';
        case 5: return isDark ? 'bg-danger text-white' : 'table-danger';
        case 6: return isDark ? 'bg-primary text-white' : 'table-primary';
        case 7: return isDark ? 'bg-secondary text-white' : 'table-secondary';
        case 8: return isDark ? 'bg-light text-dark' : 'table-light';
        case 9: return isDark ? 'bg-dark text-white' : 'table-dark text-white';
        default: return isDark ? 'bg-dark text-white' : '';
    }
}

// Listen for skin toggle and reload slot colors
$(document).on('click', '#skin-dark', function() {
    setTimeout(function() {
        loadRewards();
        loadTimeSlots();
    }, 300);
});
function loadRewards() {
    $.get('_app/php/missionwar_rewards_data.php?action=list', function(data) {
        let rewards = JSON.parse(data);
        let html = '';
        rewards.forEach(function(r) {
            let slotColor = getSlotColorClass(r.SlotID);
            let slotActive = true;
            let statusText = '';
            if (window.timeSlotsData) {
                let slot = window.timeSlotsData.find(s => s.SlotID == r.SlotID);
                if (slot && slot.IsActive == 0) {
                    slotActive = false;
                    statusText = '<span class="badge badge-danger">ปิดกิจกรรม</span>';
                } else {
                    statusText = '<span class="badge badge-success">เปิดกิจกรรม</span>';
                }
            }
            html += `<tr class='${slotColor}'>
                <td>${r.RewardID}</td>
                <td>${r.SlotID}</td>
                <td>${r.ItemKindIdx}</td>
                <td>${r.ItemOption}</td>
                <td>${r.ItemDurationIdx}</td>
                <td>${r.Alz}</td>
                <td>${statusText}</td>
                <td>
                    <button class='btn btn-sm btn-info editBtn' data-id='${r.RewardID}'>แก้ไข</button>
                    <button class='btn btn-sm btn-danger deleteBtn' data-id='${r.RewardID}'>ลบ</button>
                </td>
            </tr>`;
        });
        $('#rewardsTable tbody').html(html);
    });
}
function loadTimeSlots(callback) {
    $.get('_app/php/missionwar_time_slots_data.php?action=list', function(data) {
        let slots = [];
        try { slots = JSON.parse(data); } catch(e) { slots = []; }
        window.timeSlotsData = slots;
        let html = '';
        slots.forEach(function(s) {
            let slotColor = getSlotColorClass(s.SlotID);
            let statusText = s.IsActive == 1 ? '<span class="badge badge-success">เปิดกิจกรรม</span>' : '<span class="badge badge-danger">ปิดกิจกรรม</span>';
            html += `<tr class='${slotColor}'>
                <td>${s.SlotID}</td>
                <td>${s.StartTime ?? ''}</td>
                <td>${s.EndTime ?? ''}</td>
                <td>${s.TierMin ?? ''}</td>
                <td>${s.TierMax ?? ''}</td>
                <td>${statusText}</td>
                <td>
                    <button class='btn btn-sm btn-info editTimeSlotBtn' data-id='${s.SlotID}'>แก้ไข</button>
                    <button class='btn btn-sm btn-danger deleteTimeSlotBtn' data-id='${s.SlotID}'>ลบ</button>
                </td>
            </tr>`;
        });
        $('#timeSlotsTable tbody').html(html);
        if (typeof callback === 'function') callback();
    });
}

$(function() {
    // ปุ่มทดสอบส่งเมลล์รางวัล
    $('#testSendMailBtn').click(function() {
        $('#testSendMailForm')[0].reset();
        $('#testSendMailModal').modal('show');
    });
    $('#testSendMailForm').submit(function(e) {
        e.preventDefault();
        let data = {
            CharacterIdx: $('#TestCharacterIdx').val(),
            Alz: $('#TestAlz').val(),
            ItemKindIdx: $('#TestItemKindIdx').val(),
            ItemOption: $('#TestItemOption').val(),
            ItemDurationIdx: $('#TestItemDurationIdx').val(),
            MailExpiredDate: $('#TestMailExpiredDate').val()
        };
        $.ajax({
            url: '_app/php/missionwar_send_mail_test.php',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(res) {
                $('#testSendMailModal').modal('hide');
                Swal.fire({
                    icon: 'success',
                    title: 'ส่งเมลล์สำเร็จ!',
                    text: res && res.message ? res.message : 'ส่งเมลล์รางวัลสำเร็จ'
                });
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด',
                    text: xhr.responseText || 'ไม่สามารถส่งเมลล์รางวัลได้'
                });
            }
        });
    });
    loadTimeSlots(function() {
        loadRewards();
    });
    $('#addRewardBtn').click(function() {
        $('#rewardForm')[0].reset();
        $('#RewardID').val('');
        $('#ItemKindType').val('normal');
        // ตั้งค่าเริ่มต้น ItemKindIdx เป็นค่าว่างหรือ 0 (ไม่คำนวณ)
        $('#ItemKindIdx').val('');
        $('#rewardModal').modal('show');
    });
    $(document).on('click', '.editBtn', function() {
        let id = $(this).data('id');
        $.get('_app/php/missionwar_rewards_data.php?action=get&RewardID=' + id, function(data) {
            let r = JSON.parse(data);
            $('#RewardID').val(r.RewardID);
            $('#SlotID').val(r.SlotID);
            // Always show DB value in both input fields
            $('#ItemKindIdx').val(r.ItemKindIdx);
            $('#ItemKindIdx_raw').val(r.ItemKindIdx);         
            $('#ItemOption').val(r.ItemOption);
            $('#ItemDurationIdx').val(r.ItemDurationIdx);
            $('#Alz').val(r.Alz);
            $('#rewardModal').modal('show');
            // Do not auto-calculate, just show DB value
        });
    });
    $(document).on('click', '.deleteBtn', function() {
        let id = $(this).data('id');
        Swal.fire({
            title: 'ยืนยันการลบ',
            text: 'คุณต้องการลบข้อมูลรางวัลนี้ใช่หรือไม่?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'ลบ',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                $.get('_app/php/missionwar_rewards_data.php?action=delete&RewardID=' + id, function() {
                    loadRewards();
                    Swal.fire('ลบสำเร็จ!', 'ข้อมูลรางวัลถูกลบแล้ว', 'success');
                });
            }
        });
    });
    $('#rewardForm').submit(function(e) {
        e.preventDefault();
        // Use the value shown in input (already calculated)
        let itemKindIdx = parseInt($('#ItemKindIdx').val()) || 0;
        let data = {
            RewardID: $('#RewardID').val(),
            SlotID: $('#SlotID').val(),
            ItemKindIdx: itemKindIdx,
            ItemOption: $('#ItemOption').val(),
            ItemDurationIdx: $('#ItemDurationIdx').val(),
            Alz: $('#Alz').val()
        };
        let action = data.RewardID ? 'edit' : 'add';
        $.ajax({
            url: '_app/php/missionwar_rewards_data.php?action=' + action,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#rewardModal').modal('hide');
                loadRewards();
                Swal.fire({
                    icon: 'success',
                    title: 'สำเร็จ!',
                    text: action === 'edit' ? 'แก้ไขรางวัลสำเร็จ' : 'เพิ่มรางวัลใหม่สำเร็จ'
                });
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถบันทึกข้อมูลรางวัลได้'
                });
            }
        });
    });
    $('#addTimeSlotBtn').click(function() {
        $('#timeSlotForm')[0].reset();
        $('#TimeSlotID').val('');
        $('#TimeSlotIDInput').val('');
        $('#IsActive').prop('checked', true); // default เปิด
        $('#timeSlotModal').modal('show');
    });
    $(document).on('click', '.editTimeSlotBtn', function() {
        let id = $(this).data('id');
        $.get('_app/php/missionwar_time_slots_data.php?action=get&SlotID=' + id, function(data) {
            let s = JSON.parse(data);
            $('#TimeSlotID').val(s.SlotID);
            $('#TimeSlotIDInput').val(s.SlotID);
            if (s.StartTime) {
                let t = s.StartTime.split('.')[0];
                $('#StartTime').val(t.length === 8 ? t : '00:00:00');
            }
            if (s.EndTime) {
                let t = s.EndTime.split('.')[0];
                $('#EndTime').val(t.length === 8 ? t : '00:00:00');
            }
            $('#TierMin').val(s.TierMin);
            $('#TierMax').val(s.TierMax);
            // set toggle after modal is shown
            $('#timeSlotModal').modal('show');
            setTimeout(function() {
                $('#IsActive').prop('checked', s.IsActive == 1);
            }, 200);
        });
    });
    $(document).on('click', '.deleteTimeSlotBtn', function() {
        let id = $(this).data('id');
        Swal.fire({
            title: 'ยืนยันการลบ',
            text: 'คุณต้องการลบ Time Slot นี้ใช่หรือไม่?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'ลบ',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                $.get('_app/php/missionwar_time_slots_data.php?action=delete&SlotID=' + id, function() {
                    loadTimeSlots();
                    Swal.fire('ลบสำเร็จ!', 'Time Slot ถูกลบแล้ว', 'success');
                });
            }
        });
    });
    $('#timeSlotForm').submit(function(e) {
        e.preventDefault();
        let start = $('#StartTime').val();
        let end = $('#EndTime').val();
        // Convert to DB format (add .0000000)
        if (start && start.length === 8) start += '.0000000';
        if (end && end.length === 8) end += '.0000000';
        let data = {
            SlotID: $('#TimeSlotIDInput').val(),
            StartTime: start,
            EndTime: end,
            TierMin: $('#TierMin').val(),
            TierMax: $('#TierMax').val(),
            IsActive: $('#IsActive').is(':checked') ? 1 : 0
        };
        let action = $('#TimeSlotID').val() ? 'edit' : 'add';
        if(action === 'edit') data.SlotID = $('#TimeSlotID').val();
        $.ajax({
            url: '_app/php/missionwar_time_slots_data.php?action=' + action,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#timeSlotModal').modal('hide');
                loadTimeSlots();
                Swal.fire({
                    icon: 'success',
                    title: 'สำเร็จ!',
                    text: action === 'edit' ? 'แก้ไข Time Slot สำเร็จ' : 'เพิ่ม Time Slot ใหม่สำเร็จ'
                });
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด',
                    text: 'ไม่สามารถบันทึกข้อมูล Time Slot ได้'
                });
            }
        });
    });
// Ensure SweetAlert2 is loaded
if (typeof Swal === 'undefined') {
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11';
    document.head.appendChild(script);
}

    // Ensure modal close buttons work for both modals
    $(document).on('click', '#rewardModal .close, #rewardModal [data-dismiss="modal"]', function() {
        $('#rewardModal').modal('hide');
    });
    $(document).on('click', '#timeSlotModal .close, #timeSlotModal [data-dismiss="modal"]', function() {
        $('#timeSlotModal').modal('hide');
    });
    // ลบ event เดิมที่คำนวณจาก ItemKindIdx
    $('#ItemKindType').off('change');
    // คำนวณแบบเรียลไทม์จาก raw และ type เท่านั้น
    function updateItemKindIdx() {
        let raw = parseInt($('#ItemKindIdx_raw').val()) || 0;

        let typeVal = $('#ItemKindType').val() === 'bound' ? 4096 : 0;
        $('#ItemKindIdx').val(raw + typeVal);
    }

    $(document).ready(function() {
        // เปลี่ยน input เป็น 2 ช่อง: raw กับ result
        var $rawInput = $('<input type="number" class="form-control" id="ItemKindIdx_raw" placeholder="รหัสไอเท็มจริง">');
        $('#ItemKindIdx').before($rawInput);
        $('#ItemKindIdx').attr('readonly', true).attr('placeholder', 'รหัสที่ใช้บันทึก');
        // ซ่อน label เดิม
        $('#ItemKindIdx').prev('label').hide();
        // อัปเดตค่าทุกครั้งที่เปลี่ยน raw หรือ dropdown
        $('#ItemKindIdx_raw, #ItemKindType').on('input change', updateItemKindIdx);
        // เมื่อเปิด modal edit ให้เซ็ต raw และ result จาก database โดยตรง
        $(document).on('click', '.editBtn', function() {
            setTimeout(function() {
                let val = parseInt($('#ItemKindIdx').val()) || 0;
                $('#ItemKindIdx_raw').val(val); // โชว์ค่าเดิมจาก database
                $('#ItemKindIdx').val(val);     // โชว์ค่าเดิมจาก database
            }, 300);
        });
        // เมื่อเปิด modal add ให้เคลียร์ raw
        $('#addRewardBtn').click(function() {
            $('#ItemKindIdx_raw').val('');
            updateItemKindIdx();
        });
    });
});
</script>
