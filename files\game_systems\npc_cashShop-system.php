<style>
#poolGrid {
    display: flex;
    flex-wrap: wrap;
    /* 🧩 เพิ่มบรรทัดใหม่เมื่อเกินพื้นที่ */
    gap: 10px;
    max-width: 510px;
    /* ✨ Optional: ล็อคขนาดพอดี 5 ช่อง */
}

.pool-item {
    width: 93px;
    height: 93px;
    border-radius: 4px;
    padding: 10px;
    cursor: pointer;
    background: rgb(66, 66, 70);
    text-align: center;
    user-select: none;
    margin: 2px;
    border: 1px solid #555;
    box-sizing: border-box;
}

.pool-item.empty {
    background: #eee;
    color: #999;
    font-style: italic;
}

/* Drag and Drop Styles */
.slot {
    transition: all 0.2s ease;
    overflow: hidden !important;
    position: relative !important;
}

.slot:hover {
    border-color: #007bff !important;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.slot.drag-over {
    border-color: #007bff !important;
    border-width: 2px !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.slot.dragging {
    opacity: 0.5;
    transform: scale(0.95);
}

/* เพิ่ม CSS สำหรับ slot ที่มีไอเท็ม */
.slot[data-serial] {
    cursor: grab !important;
}

.slot[data-serial]:active {
    cursor: grabbing !important;
}

.slot:not([data-serial]) {
    cursor: default !important;
}

/* เพิ่ม animation สำหรับ drag feedback */
.slot.dragging {
    opacity: 0.6 !important;
    transform: scale(0.95) !important;
    border-color: #007bff !important;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.5) !important;
}

.slot.drag-over {
    border-color: #28a745 !important;
    border-width: 3px !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.5) !important;
    transform: scale(1.05) !important;
}
</style>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-store"></i> Cash Shop Editor <sup class="badge badge-danger">LIVE</sup>
        <small>Edit and organize in-game shop tabs, slots, and items</small>
    </h1>
</div>
<div class="row">
    <div class="col-xl-8">
        <div id="panel-cashshop" class="panel">
            <div class="panel-hdr bg-primary-700 bg-success-gradient">
                <h2>Shop Tabs & Items</h2>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <ul class="nav nav-tabs" id="shopTabs" role="tablist"></ul>
                    <div class="tab-content border border-top-0 p-3" id="tabContentArea"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4">
        <div id="panel-1" class="panel">
            <div class="panel-hdr bg-primary-700 bg-success-gradient">
                <h2>
                   แก้ไข Pool Msg
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="row">
                        <div class="col-xl-12 order-2 order-xl-1">
                            <div class="form-group">
                                <div class="message" id="resultNotice" style="display:none;"></div>
                                <?php 
                                    $PoolidSc = [];
                                    $sql = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_cashService_msg_pool_table";
                                    $stmt = sqlsrv_query($conn, $sql);
                                    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                        $PoolidSc[] = [
                                            'PoolID' => $row['PoolID'],
                                            'Msg' => $row['Msg'],
                                            'Marker' => $row['Marker']
                                        ];
                                    }
                                    if (count($PoolidSc)): 
                                    ?>
                                    <div class="form-group">
                                        <label for="PoolSelect"><strong>เลือก Pool:</strong></label>
                                        <select id="PoolSelect" class="form-control" onchange="onPoolSelectChange()">
                                            <option value="">-- กรุณาเลือก --</option>
                                            <?php foreach ($PoolidSc as $PoolSc): ?>
                                                <option value="<?= $PoolSc['PoolID'] ?>"
                                                    data-win874="<?= htmlspecialchars($PoolSc['Msg']) ?>">
                                                   Pool = <?= $PoolSc['PoolID'] ?> →
                                                   Msg = <?= htmlspecialchars($userLogin->thaitrans($PoolSc['Msg'])) ?> → 
                                                   Marker = <?= $PoolSc['Marker'] ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="markerselect"><strong>เลือก Marker:</strong></label>
                                        <select id="markerselect" class="form-control">
                                                <option value="0">ไม่แสดง</option>
                                                <option value="1">HOT</option>
                                                <option value="2">NEW</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="thaiText"><strong>ชื่อใหม่ภาษาไทย:</strong></label>
                                        <textarea id="thaiText" class="form-control" rows="2" placeholder="พิมพ์ชื่อใหม่ที่นี่"></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="d-block"><strong>🔣 อักษรพิเศษ:</strong></label>
                                        <div class="btn-group" role="group">
                                            <?php foreach (['|', '*', '๏', '~', '`', '๛', '_', '@'] as $ch): ?>
                                                <button type="button" class="btn btn-info waves-effect waves-themed" onclick="insertChar('<?= $ch ?>')">
                                                    <?= $ch ?>
                                                </button>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button class="btn btn-danger btn-block" onclick="sendNameToServer()">🚀 ยืนยันแก้ไข</button>
                                    </div>

                                    <?php else: ?>
                                        <div class="alert alert-danger mt-3">
                                            ❌ ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้ระบุ <code>?usernum=</code>
                                        </div>
                                    <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="panel-2" class="panel">
            <div class="panel-hdr bg-primary-700 bg-success-gradient">
                <h2>
                    แก้ไข Tab Msg
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="row">
                        <div class="col-xl-12 order-2 order-xl-1">
                            <div class="form-group">
                                <div class="message" id="resultNotice2" style="display:none;"></div>
                                <?php 
                                    $TabidSc = [];
                                    $sql = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_cashService_msg_tab_table";
                                    $stmt = sqlsrv_query($conn, $sql);
                                    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                        $TabidSc[] = [
                                            'PoolID' => $row['PoolID'],
                                            'TabID' => $row['TabID'],
                                            'Msg' => $row['Msg'],
                                            'Marker' => $row['Marker']
                                        ];
                                    }
                                    if (count($TabidSc)): 
                                    ?>
                                    <div class="form-group">
                                        <label for="PoolSelect2"><strong>เลือก Pool:</strong></label>
                                        <select id="PoolSelect2" class="form-control" onchange="onPoolSelectChange2()">
                                            <option value="">-- กรุณาเลือก --</option>
                                            <?php foreach ($TabidSc as $TabSc): ?>
                                                <option value="<?= $TabSc['PoolID'] ?>"
                                                    data-win874="<?= htmlspecialchars($TabSc['Msg']) ?>">
                                                   Pool = <?= $TabSc['PoolID'] ?> →
                                                   Tab = <?= $TabSc['TabID'] ?> →
                                                   Msg = <?= htmlspecialchars($userLogin->thaitrans($TabSc['Msg'])) ?> → 
                                                   Marker = <?= $TabSc['Marker'] ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="Tabselect2"><strong>เลือก Tab:</strong></label>
                                        <select id="Tabselect2" class="form-control">
                                                <option value="0">0</option>
                                                <option value="1">1</option>
                                                <option value="2">2</option>
                                                <option value="3">3</option>
                                                <option value="4">4</option>
                                                <option value="4">5</option>
                                                <option value="6">6</option>
                                                <option value="7">7</option>
                                                <option value="8">8</option>
                                                <option value="9">9</option>
                                                <option value="10">10</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="markerselect2"><strong>เลือก Marker:</strong></label>
                                        <select id="markerselect2" class="form-control">
                                                <option value="0">ไม่แสดง</option>
                                                <option value="1">HOT</option>
                                                <option value="2">NEW</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="thaiText2"><strong>ชื่อใหม่ภาษาไทย:</strong></label>
                                        <textarea id="thaiText2" class="form-control" rows="2" placeholder="พิมพ์ชื่อใหม่ที่นี่"></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="d-block"><strong>🔣 อักษรพิเศษ:</strong></label>
                                        <div class="btn-group" role="group">
                                            <?php foreach (['|', '*', '๏', '~', '`', '๛', '_', '@'] as $ch): ?>
                                                <button type="button" class="btn btn-info waves-effect waves-themed" onclick="insertChar2('<?= $ch ?>')">
                                                    <?= $ch ?>
                                                </button>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button class="btn btn-danger btn-block" onclick="sendNameToServer2()">🚀 ยืนยันแก้ไข</button>
                                    </div>

                                    <?php else: ?>
                                        <div class="alert alert-danger mt-3">
                                            ❌ ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้ระบุ <code>?usernum=</code>
                                        </div>
                                    <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>


<!-- Add/Edit Modal -->
<div class="modal fade" id="itemModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form id="itemForm" method="post">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Shop Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="SerialNum" name="SerialNum" value="0">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ItemName">Item Name</label>
                            <input type="text" id="ItemName" name="ItemName" class="form-control" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="Cash"><code>Cash</code></label>
                            <input type="number" id="Cash" name="Cash" class="form-control" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SlotID">Slot ID</label>
                            <input type="number" id="SlotID" name="SlotID" class="form-control" min="0" max="127"
                                required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="TabID">Tab ID</label>
                            <input type="number" id="TabID" name="TabID" class="form-control" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="PoolID">Pool ID</label>
                            <input type="number" id="PoolID" name="PoolID" class="form-control" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ItemKind">Item Kind</label>
                            <input type="number" id="ItemKind" name="ItemKind" class="form-control" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ItemOption">Item Option</label>
                            <input type="number" id="ItemOption" name="ItemOption" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ItemPeriod">Item Period</label>
                            <input type="number" id="ItemPeriod" name="ItemPeriod" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="Marker">Marker</label>
                            <input type="number" id="Marker" name="Marker" class="form-control" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="DescriptionID">Description ID</label>
                            <input type="number" id="DescriptionID" name="DescriptionID" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="PeriodItemID">Period Item ID</label>
                            <input type="number" id="PeriodItemID" name="PeriodItemID" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ItemGroup">Item Group</label>
                            <input type="number" id="ItemGroup" name="ItemGroup" class="form-control" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="Forcegem"><code>Forcegem</code></label>
                            <input type="number" id="Forcegem" name="Forcegem" class="form-control" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ProductID">Product ID</label>
                            <input type="text" id="ProductID" name="ProductID" class="form-control">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="DiscountRate">Discount Rate</label>
                            <input type="number" id="DiscountRate" name="DiscountRate" class="form-control" min="0"
                                max="100" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="LevelMin">Level Min</label>
                            <input type="number" id="LevelMin" name="LevelMin" class="form-control" min="-128" max="127"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="LevelMax">Level Max</label>
                            <input type="number" id="LevelMax" name="LevelMax" class="form-control" min="-128" max="127"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="HonorMin">Honor Min -18</label>
                            <input type="number" id="HonorMin" name="HonorMin" value="-18" class="form-control" min="-2147483648"
                                max="2147483647" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="HonorMax">Honor Max 20</label>
                            <input type="number" id="HonorMax" name="HonorMax" value="20" class="form-control" min="-2147483648"
                                max="2147483647" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="IsPremium">Is Premium</label>
                            <select id="IsPremium" name="IsPremium" class="form-control" required>
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="IsWinIWarNation">Is Win IWar Nation</label>
                            <select id="IsWinIWarNation" name="IsWinIWarNation" class="form-control" required>
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleDateBegin">Sale Date Begin</label>
                            <input type="date" id="SaleDateBegin" name="SaleDateBegin" class="form-control">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleDateEnd">Sale Date End</label>
                            <input type="date" id="SaleDateEnd" name="SaleDateEnd" class="form-control">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="DailySaleTimeBegin">Daily Sale Time Begin</label>
                            <input type="time" id="DailySaleTimeBegin" name="DailySaleTimeBegin" class="form-control"
                                step="1">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="DailySaleTimeEnd">Daily Sale Time End</label>
                            <input type="time" id="DailySaleTimeEnd" name="DailySaleTimeEnd" class="form-control"
                                step="1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleCountServer">Sale Count Server</label>
                            <input type="number" id="SaleCountServer" name="SaleCountServer" class="form-control"
                                min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleCountDaily">Sale Count Daily</label>
                            <input type="number" id="SaleCountDaily" name="SaleCountDaily" class="form-control" min="0"
                                required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleCountUser">Sale Count User</label>
                            <input type="number" id="SaleCountUser" name="SaleCountUser" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ResetCount">Reset Count</label>
                            <input type="number" id="ResetCount" name="ResetCount" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="ResetCost">Reset Cost</label>
                            <input type="number" id="ResetCost" name="ResetCost" class="form-control" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="PackageItemID">Package Item ID</label>
                            <input type="number" id="PackageItemID" name="PackageItemID" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleTimeBegin">Sale Time Begin</label>
                            <input type="time" id="SaleTimeBegin" name="SaleTimeBegin" class="form-control" step="1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleTimeEnd">Sale Time End</label>
                            <input type="time" id="SaleTimeEnd" name="SaleTimeEnd" class="form-control" step="1">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="TradeCount">Trade Count</label>
                            <input type="number" id="TradeCount" name="TradeCount" class="form-control" min="0"
                                required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="Alz"><code>Alz</code></label>
                            <input type="number" id="Alz" name="Alz" value="0" class="form-control" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="SaleCountCharacter">Sale Count Character</label>
                            <input type="number" id="SaleCountCharacter" name="SaleCountCharacter" class="form-control"
                                min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="IsDailyLimitResetUser">Is Daily Limit Reset User</label>
                            <select id="IsDailyLimitResetUser" name="IsDailyLimitResetUser" class="form-control"
                                required>
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="IsDailyLimitResetCharacter">Is Daily Limit Reset
                                Character</label>
                            <select id="IsDailyLimitResetCharacter" name="IsDailyLimitResetCharacter"
                                class="form-control" required>
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger mr-auto" id="deleteBtn"
                        style="display:none">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="saveBtn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="files/game_systems/assets/shopcash.js?v=<?php echo date('s'); ?>" type="text/javascript"></script>
<script>
// แผนที่แปลง windows-874 เป็น Unicode (สำหรับ Pool/Tab Message Editor เท่านั้น)
const win874ToUnicodeMap = {
    0xA1: 0x0E01,
    0xA2: 0x0E02,
    0xA3: 0x0E03,
    0xA4: 0x0E04,
    0xA5: 0x0E05,
    0xA6: 0x0E06,
    0xA7: 0x0E07,
    0xA8: 0x0E08,
    0xA9: 0x0E09,
    0xAA: 0x0E0A,
    0xAB: 0x0E0B,
    0xAC: 0x0E0C,
    0xAD: 0x0E0D,
    0xAE: 0x0E0E,
    0xAF: 0x0E0F,
    0xB0: 0x0E10,
    0xB1: 0x0E11,
    0xB2: 0x0E12,
    0xB3: 0x0E13,
    0xB4: 0x0E14,
    0xB5: 0x0E15,
    0xB6: 0x0E16,
    0xB7: 0x0E17,
    0xB8: 0x0E18,
    0xB9: 0x0E19,
    0xBA: 0x0E1A,
    0xBB: 0x0E1B,
    0xBC: 0x0E1C,
    0xBD: 0x0E1D,
    0xBE: 0x0E1E,
    0xBF: 0x0E1F,
    0xC0: 0x0E20,
    0xC1: 0x0E21,
    0xC2: 0x0E22,
    0xC3: 0x0E23,
    0xC4: 0x0E24,
    0xC5: 0x0E25,
    0xC6: 0x0E26,
    0xC7: 0x0E27,
    0xC8: 0x0E28,
    0xC9: 0x0E29,
    0xCA: 0x0E2A,
    0xCB: 0x0E2B,
    0xCC: 0x0E2C,
    0xCD: 0x0E2D,
    0xCE: 0x0E2E,
    0xCF: 0x0E2F,
    0xD0: 0x0E30,
    0xD1: 0x0E31,
    0xD2: 0x0E32,
    0xD3: 0x0E33,
    0xD4: 0x0E34,
    0xD5: 0x0E35,
    0xD6: 0x0E36,
    0xD7: 0x0E37,
    0xD8: 0x0E38,
    0xD9: 0x0E39,
    0xDA: 0x0E3A,
    0xDF: 0x0E3F,
    0xE0: 0x0E40,
    0xE1: 0x0E41,
    0xE2: 0x0E42,
    0xE3: 0x0E43,
    0xE4: 0x0E44,
    0xE5: 0x0E45,
    0xE6: 0x0E46,
    0xE7: 0x0E47,
    0xE8: 0x0E48,
    0xE9: 0x0E49,
    0xEA: 0x0E4A,
    0xEB: 0x0E4B,
    0xEC: 0x0E4C,
    0xED: 0x0E4D,
    0xEE: 0x0E4E,
    0xEF: 0x0E4F,
    0xF0: 0x0E50,
    0xF1: 0x0E51,
    0xF2: 0x0E52,
    0xF3: 0x0E53,
    0xF4: 0x0E54,
    0xF5: 0x0E55,
    0xF6: 0x0E56,
    0xF7: 0x0E57,
    0xF8: 0x0E58,
    0xF9: 0x0E59,
    0xFA: 0x0E5A,
    0xFB: 0x0E5B
};

function win874ToUnicode(str) {
    let result = "";
    for (let i = 0; i < str.length; i++) {
        let code = str.charCodeAt(i);
        if (code >= 0xA1 && code <= 0xFB && win874ToUnicodeMap[code]) {
            result += String.fromCharCode(win874ToUnicodeMap[code]);
        } else {
            result += str[i];
        }
    }
    return result;
}

function onPoolSelectChange() {
    const selects = ['PoolSelect', 'PoolSelect'];
    selects.forEach((id) => {
        const select = document.getElementById(id);
        if (!select) return;
        const option = select.options[select.selectedIndex];
        const win874Name = option?.getAttribute('data-win874') || "";
        const targetId = id === 'PoolSelect' ? 'thaiText' : 'thaiText';
        document.getElementById(targetId).value = win874ToUnicode(win874Name);
    });
}
function onPoolSelectChange2() {
    const selects = ['PoolSelect2', 'PoolSelect2'];
    selects.forEach((id) => {
        const select = document.getElementById(id);
        if (!select) return;
        const option = select.options[select.selectedIndex];
        const win874Name = option?.getAttribute('data-win874') || "";
        const targetId = id === 'PoolSelect2' ? 'thaiText2' : 'thaiText2';
        document.getElementById(targetId).value = win874ToUnicode(win874Name);
    });
}


function insertChar(Evid) {
    const input = document.getElementById("thaiText");
    const start = input.selectionStart;
    const end = input.selectionEnd;
    const text = input.value;

    input.value = text.slice(0, start) + Evid + text.slice(end);
    input.focus();
    input.setSelectionRange(start + Evid.length, start + Evid.length);
}

function insertChar2(Evid) {
    const input = document.getElementById("thaiText2");
    const start = input.selectionStart;
    const end = input.selectionEnd;
    const text = input.value;

    input.value = text.slice(0, start) + Evid + text.slice(end);
    input.focus();
    input.setSelectionRange(start + Evid.length, start + Evid.length);
}

function sendNameToServer() {
    const msg = document.getElementById("thaiText").value.trim();
    const select = document.getElementById("PoolSelect");
    const marselect = document.getElementById("markerselect");
    const poolid = select.value;
    const mkselect = marselect.value; // ✅ แก้ตรงนี้
    const resultBox = document.getElementById("resultNotice");

    if (!poolid) {
        resultBox.innerText = "❌ กรุณาเลือก poolid ก่อน";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    if (msg === '') {
        resultBox.innerText = "❌ กรุณากรอกชื่อใหม่";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    const form = new FormData();
    form.append("form_type", "shoppool_msg");
    form.append("msg_thai", msg);
    form.append("PoolID", poolid);
    form.append("Marker", mkselect);
    fetch("files/game_systems/class_module/class_module/npc_cashShop-update.php", {
            method: "POST",
            body: form
        })
        .then(res => res.text())
        .then(result => {
            resultBox.innerText = result;
            resultBox.className = result.includes("✅") ? "message success" : "message error";
            resultBox.style.display = "block";

            if (result.includes("✅")) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        })
        .catch(err => {
            resultBox.innerText = "❌ เกิดข้อผิดพลาดในการส่งข้อมูล\n" + err;
            resultBox.className = "message error";
            resultBox.style.display = "block";
        });
}

function sendNameToServer2() {
    const msg = document.getElementById("thaiText2").value.trim();
    const select = document.getElementById("PoolSelect2");
    const marselect = document.getElementById("markerselect2");
    const tabselect = document.getElementById("Tabselect2");
    const poolid = select.value;
    const mkselect = marselect.value; // ✅ แก้ตรงนี้
    const tabid = tabselect.value; // ✅ แก้ตรงนี้
    const resultBox = document.getElementById("resultNotice2");

    if (!poolid) {
        resultBox.innerText = "❌ กรุณาเลือก poolid ก่อน";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    if (msg === '') {
        resultBox.innerText = "❌ กรุณากรอกชื่อใหม่";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    const form = new FormData();
    form.append("form_type", "shoppool_msg2");
    form.append("msg_thai", msg);
    form.append("PoolID", poolid);
    form.append("TabID", tabid);
    form.append("Marker", mkselect);
    fetch("files/game_systems/class_module/npc_cashShop-update.php", {
            method: "POST",
            body: form
        })
        .then(res => res.text())
        .then(result => {
            resultBox.innerText = result;
            resultBox.className = result.includes("✅") ? "message success" : "message error";
            resultBox.style.display = "block";

            if (result.includes("✅")) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        })
        .catch(err => {
            resultBox.innerText = "❌ เกิดข้อผิดพลาดในการส่งข้อมูล\n" + err;
            resultBox.className = "message error";
            resultBox.style.display = "block";
        });
}
</script>
<!-- JavaScript สำหรับ Cash Shop Drag & Drop จะโหลดจากไฟล์ shopcash.js แทน -->
