<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-users"></i> Manage-Account & Character
        <small>ระบบตรวจสอบข้อมูลผู้เล่น</small>
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_charecter/character-statistics" class="btn btn-primary btn-sm">
            <i class="fal fa-chart-bar"></i> สถิติตัวละคร
        </a>
    </div>
</div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>Player All <span class="fw-300"><i>Table</i></span></h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-sm table-bordered w-100">
                        <thead>
                            <tr>
                                <th>CharacterIdx</th>
                                <th>ชื่อ</th>
                                <th>War</th>
                                <th>Class#LEV</th>
                                <th>Alz</th>
                                <th>สร้างตัว</th>
                                <th>เข้าเกมส์</th>
                                <th>PlayTime</th>
                                <th>แนล#Map</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
<?php
$sql = "SELECT TOP 100 CharacterIdx, Name, LEV, Alz, WorldIdx, CreateDate, LoginTime, PlayTime, ChannelIdx, Style
        FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY CreateDate DESC";
$result = sqlsrv_query($conn, $sql);
if ($result === false) die(print_r(sqlsrv_errors(), true));

while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
    $idx = floor($row['CharacterIdx'] / 16);
    $name = htmlspecialchars($userLogin->thaitrans($row['Name'] ?? ''));
    $warid = htmlspecialchars($userLogin->chartowar($row['CharacterIdx']));
    $lev = (int) ($row['LEV'] ?? 0);
    $alz = number_format((int) ($row['Alz'] ?? 0));
    $createDate = $row['CreateDate'] ?? null;
    $loginDate = $row['LoginTime'] ?? null;
    $playtime = (int) ($row['PlayTime'] ?? 0);
    $channel = (int) ($row['ChannelIdx'] ?? 0);
    $worldid = (int) ($row['WorldIdx'] ?? 0);
    $style = (int) ($row['Style'] ?? 0);
    $class = $userLogin->cabalstyle($style);
    $classDisplay = htmlspecialchars($class['Class_Name'] ?? 'Unknown') . '#' . $lev;
    ?>
    <tr>
        <td><?php echo (int) $row['CharacterIdx']; ?></td>
        <td><?php echo $name; ?></td>
        <td><?php echo $warid; ?></td>
        <td><span class="badge badge-danger badge-pill"><?php echo $classDisplay; ?></span></td>
        <td><?php echo $alz; ?></td>
        <td><?php echo $createDate ? date('d-m-Y H:i', strtotime($createDate)) : ''; ?></td>
        <td><?php echo $loginDate ? date('d-m-Y H:i', strtotime($loginDate)) : ''; ?></td>
        <td><?php echo $playtime; ?></td>
        <td><?php echo $channel . '#' . $worldid; ?></td>
        <td>
            <a href="?url=manager_account/manage-account-edit&id=<?php echo $idx; ?>"
               class="btn btn-outline-warning btn-sm btn-icon rounded-circle waves-effect waves-themed">
               <i class="fal fa-info-circle"></i>
            </a>
        </td>
    </tr>
<?php }
sqlsrv_free_stmt($result);
sqlsrv_close($conn);
?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>