<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bell"></i> ทดสอบการแก้ไขการแจ้งเตือนซ้อนกัน
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา High Alz Character แจ้งเตือนซ้อนกัน</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fal fa-exclamation-triangle"></i>
                    <strong>ปัญหา:</strong> การแจ้งเตือน "High Alz character" แสดงซ้อนกันและซ้ำ
                </div>
                
                <h5>❌ ปัญหาที่เกิดขึ้น</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">Alert Issues</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-times text-danger"></i> Toast notifications ซ้อนกัน</li>
                                    <li><i class="fal fa-times text-danger"></i> แจ้งเตือนซ้ำสำหรับตัวละครเดียวกัน</li>
                                    <li><i class="fal fa-times text-danger"></i> ไม่มีการจำกัดจำนวน alerts</li>
                                    <li><i class="fal fa-times text-danger"></i> แสดงผล alerts เก่าซ้ำ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Solutions Applied</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> เพิ่มเงื่อนไข CreateDate ใน SQL</li>
                                    <li><i class="fal fa-check text-success"></i> ใช้ Set เก็บ alerts ที่แสดงแล้ว</li>
                                    <li><i class="fal fa-check text-success"></i> ปรับปรุงการจัดตำแหน่ง toast</li>
                                    <li><i class="fal fa-check text-success"></i> เพิ่ม animation และ auto-remove</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแจ้งเตือน</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-danger btn-sm" onclick="testAlert('high', 'Test High Priority Alert')">
                            <i class="fal fa-exclamation-triangle"></i> Test High Alert
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testAlert('medium', 'Test Medium Priority Alert')">
                            <i class="fal fa-exclamation"></i> Test Medium Alert
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testAlert('info', 'Test Info Alert')">
                            <i class="fal fa-info"></i> Test Info Alert
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="testMultipleAlerts()">
                            <i class="fal fa-layer-group"></i> Test Multiple Alerts
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="clearAllToasts()">
                            <i class="fal fa-trash"></i> Clear All
                        </button>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 ทดสอบ API Alerts</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testAPIAlerts()">
                        <i class="fal fa-bell"></i> Test API Alerts
                    </button>
                    <button class="btn btn-info" onclick="testHighAlzQuery()">
                        <i class="fal fa-coins"></i> Test High Alz Query
                    </button>
                </div>
                
                <div id="api-test-results"></div>
                
                <h5 class="mt-4">🔧 การแก้ไขที่ทำ</h5>
                
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. แก้ไข SQL Query ใน API
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>เปลี่ยนจาก:</strong></p>
                                <pre><code>WHERE Alz > 100000000</code></pre>
                                
                                <p><strong>เป็น:</strong></p>
                                <pre><code>WHERE Alz > 100000000
AND CreateDate >= DATEADD(day, -1, GETDATE())</code></pre>
                                
                                <p><strong>ผลลัพธ์:</strong> แสดงเฉพาะตัวละครที่สร้างใหม่ใน 24 ชั่วโมงที่ผ่านมา</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. ป้องกันการแสดงผลซ้ำใน JavaScript
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>เพิ่ม Set เก็บ alerts ที่แสดงแล้ว:</strong></p>
                                <pre><code>let shownAlerts = new Set();

function showNewAlerts(alerts) {
    alerts.forEach(alert => {
        const alertKey = alert.type + '_' + alert.data.CharacterIdx;
        if (!shownAlerts.has(alertKey)) {
            shownAlerts.add(alertKey);
            // แสดง alert
        }
    });
}</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. ปรับปรุงการแสดงผล Toast
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ใหม่:</strong></p>
                                <ul>
                                    <li>ตรวจสอบ toast ซ้ำก่อนแสดง</li>
                                    <li>จัดตำแหน่งแบบ stack</li>
                                    <li>Auto-remove หลัง 8 วินาที</li>
                                    <li>Smooth animation</li>
                                    <li>ปรับตำแหน่งอัตโนมัติเมื่อปิด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 ผลลัพธ์</h5>
                <div class="alert alert-success">
                    <h6><i class="fal fa-check-circle"></i> สิ่งที่ได้รับการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ไม่มีการแจ้งเตือนซ้ำ</li>
                                <li>✅ Toast ไม่ซ้อนกันแบบไม่เป็นระเบียบ</li>
                                <li>✅ แสดงเฉพาะตัวละครใหม่</li>
                                <li>✅ จำกัดจำนวน alerts</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Animation ที่สวยงาม</li>
                                <li>✅ Auto-remove และ repositioning</li>
                                <li>✅ Memory management</li>
                                <li>✅ Better user experience</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy functions from character-monitor for testing
let shownAlerts = new Set();

function showToast(message, type = 'info') {
    // ตรวจสอบว่ามี toast เดียวกันอยู่แล้วหรือไม่
    const existingToasts = document.querySelectorAll('.toast-notification');
    for (let toast of existingToasts) {
        if (toast.textContent.includes(message.substring(0, 50))) {
            return; // ไม่แสดง toast ซ้ำ
        }
    }
    
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed toast-notification`;
    
    // คำนวณตำแหน่ง top ตาม toast ที่มีอยู่
    const existingCount = existingToasts.length;
    const topPosition = 20 + (existingCount * 80);
    
    toast.style.cssText = `top: ${topPosition}px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;`;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="close ml-2" onclick="this.parentElement.parentElement.remove(); repositionToasts();">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 8 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('fade-out');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                    repositionToasts();
                }
            }, 300);
        }
    }, 8000);
}

function repositionToasts() {
    const toasts = document.querySelectorAll('.toast-notification');
    toasts.forEach((toast, index) => {
        const topPosition = 20 + (index * 80);
        toast.style.top = topPosition + 'px';
    });
}

function testAlert(severity, message) {
    const alert = {
        type: 'test_' + severity,
        message: message + ' - ' + new Date().toLocaleTimeString(),
        severity: severity,
        data: { CharacterIdx: Math.random() }
    };
    
    const alertKey = alert.type + '_' + alert.data.CharacterIdx;
    
    if (!shownAlerts.has(alertKey)) {
        shownAlerts.add(alertKey);
        
        if (severity === 'high') {
            showToast(alert.message, 'danger');
        } else if (severity === 'medium') {
            showToast(alert.message, 'warning');
        } else {
            showToast(alert.message, 'info');
        }
    }
}

function testMultipleAlerts() {
    testAlert('high', 'High Priority Alert 1');
    setTimeout(() => testAlert('medium', 'Medium Priority Alert 2'), 500);
    setTimeout(() => testAlert('info', 'Info Alert 3'), 1000);
    setTimeout(() => testAlert('high', 'High Priority Alert 4'), 1500);
}

function clearAllToasts() {
    const toasts = document.querySelectorAll('.toast-notification');
    toasts.forEach(toast => {
        toast.classList.add('fade-out');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    });
    shownAlerts.clear();
}

async function testAPIAlerts() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> Testing API alerts...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=alerts', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (data.success ? 'check' : 'times') + '"></i> API Alerts Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        html += '<p><strong>Alert Count:</strong> ' + (data.data ? data.data.length : 0) + '</p>';
        
        if (data.success && data.data && data.data.length > 0) {
            html += '<p><strong>Alerts:</strong></p><ul>';
            data.data.forEach(alert => {
                html += '<li>' + alert.message + ' (' + alert.severity + ')</li>';
            });
            html += '</ul>';
            
            // แสดง alerts จริง
            data.data.forEach(alert => {
                const alertKey = alert.type + '_' + (alert.data.CharacterIdx || alert.data.Name);
                if (!shownAlerts.has(alertKey)) {
                    shownAlerts.add(alertKey);
                    if (alert.severity === 'high') {
                        showToast(alert.message, 'danger');
                    } else if (alert.severity === 'medium') {
                        showToast(alert.message, 'warning');
                    }
                }
            });
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> API Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testHighAlzQuery() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> Testing High Alz query...</div>';
    
    try {
        // ทดสอบ query โดยตรง
        const response = await fetch('?url=manager_charecter/api/character-data&action=alerts&debug=1', {
            method: 'GET',
            credentials: 'same-origin'
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-info">';
        html += '<h6><i class="fal fa-database"></i> High Alz Query Test</h6>';
        html += '<p>Query ใหม่จะแสดงเฉพาะตัวละครที่:</p>';
        html += '<ul>';
        html += '<li>มี Alz > 100,000,000</li>';
        html += '<li>สร้างใน 24 ชั่วโมงที่ผ่านมา</li>';
        html += '<li>จำกัดแค่ 3 ตัวละคร</li>';
        html += '</ul>';
        
        if (data.success && data.data) {
            const highAlzAlerts = data.data.filter(alert => alert.type === 'high_alz');
            html += '<p><strong>High Alz Alerts Found:</strong> ' + highAlzAlerts.length + '</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Query Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}
</script>

<style>
/* Toast Notifications */
.toast-notification {
    transition: all 0.3s ease-in-out;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
}

.toast-notification.fade-out {
    opacity: 0;
    transform: translateX(100%);
}

.toast-notification .close {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.7;
}

.toast-notification .close:hover {
    opacity: 1;
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
