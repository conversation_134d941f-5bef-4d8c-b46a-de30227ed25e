<?php
// ทดสอบการ insert วันหมดอายุ
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

echo "<h2>ทดสอบการ Insert วันหมดอายุ</h2>";

if (!$conn) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>";
    exit;
}

// สร้าง table ถ้าไม่มี
$createSql = "
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_Code' AND xtype='U')
CREATE TABLE [dbo].[WEB_Redeem_Code] (
    [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
    [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
    [quantity] int NULL,
    [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
    [datecreated] datetime DEFAULT getdate() NULL,
    [expiry_date] datetime NULL
)";

$createStmt = sqlsrv_query($conn, $createSql);
if ($createStmt) {
    echo "<p style='color: green;'>✅ Table พร้อมใช้งาน</p>";
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถสร้าง table ได้: " . print_r(sqlsrv_errors(), true) . "</p>";
    exit;
}

echo "<h3>ทดสอบการ Insert แบบต่างๆ</h3>";

// ทดสอบ 1: Insert ไม่มีวันหมดอายุ
echo "<h4>1. Insert ไม่มีวันหมดอายุ</h4>";
$testCode1 = 'TEST-NO-EXPIRY-' . rand(1000, 9999);
$sql1 = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated) VALUES (?, ?, ?, '1', GETDATE())";
$params1 = [$testCode1, '1:0:31', 1];

$stmt1 = sqlsrv_query($conn, $sql1, $params1);
if ($stmt1) {
    echo "<p style='color: green;'>✅ Insert สำเร็จ: $testCode1</p>";
} else {
    echo "<p style='color: red;'>❌ Insert ล้มเหลว: " . print_r(sqlsrv_errors(), true) . "</p>";
}

// ทดสอบ 2: Insert มีวันหมดอายุ (รูปแบบ string)
echo "<h4>2. Insert มีวันหมดอายุ (string format)</h4>";
$testCode2 = 'TEST-WITH-EXPIRY-' . rand(1000, 9999);
$expiryDate = '2025-12-31 23:59:59';
$sql2 = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '1', GETDATE(), ?)";
$params2 = [$testCode2, '1:0:31', 1, $expiryDate];

$stmt2 = sqlsrv_query($conn, $sql2, $params2);
if ($stmt2) {
    echo "<p style='color: green;'>✅ Insert สำเร็จ: $testCode2 (หมดอายุ: $expiryDate)</p>";
} else {
    echo "<p style='color: red;'>❌ Insert ล้มเหลว: " . print_r(sqlsrv_errors(), true) . "</p>";
}

// ทดสอบ 3: Insert มีวันหมดอายุ (รูปแบบ DateTime object)
echo "<h4>3. Insert มีวันหมดอายุ (DateTime object)</h4>";
$testCode3 = 'TEST-DATETIME-' . rand(1000, 9999);
$expiryDateTime = new DateTime('2025-06-30 12:00:00');
$sql3 = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '1', GETDATE(), ?)";
$params3 = [$testCode3, '1:0:31', 1, $expiryDateTime];

$stmt3 = sqlsrv_query($conn, $sql3, $params3);
if ($stmt3) {
    echo "<p style='color: green;'>✅ Insert สำเร็จ: $testCode3 (หมดอายุ: " . $expiryDateTime->format('Y-m-d H:i:s') . ")</p>";
} else {
    echo "<p style='color: red;'>❌ Insert ล้มเหลว: " . print_r(sqlsrv_errors(), true) . "</p>";
}

// ทดสอบ 4: Insert มีวันหมดอายุ (NULL)
echo "<h4>4. Insert มีวันหมดอายุ (NULL)</h4>";
$testCode4 = 'TEST-NULL-EXPIRY-' . rand(1000, 9999);
$sql4 = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '1', GETDATE(), ?)";
$params4 = [$testCode4, '1:0:31', 1, null];

$stmt4 = sqlsrv_query($conn, $sql4, $params4);
if ($stmt4) {
    echo "<p style='color: green;'>✅ Insert สำเร็จ: $testCode4 (หมดอายุ: NULL)</p>";
} else {
    echo "<p style='color: red;'>❌ Insert ล้มเหลว: " . print_r(sqlsrv_errors(), true) . "</p>";
}

// แสดงข้อมูลที่เพิ่งเพิ่ม
echo "<h3>ข้อมูลที่เพิ่งเพิ่ม</h3>";
$selectSql = "SELECT TOP 10 * FROM WEB_Redeem_Code WHERE code LIKE 'TEST-%' ORDER BY id DESC";
$selectStmt = sqlsrv_query($conn, $selectSql);

if ($selectStmt) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Code</th><th>Items</th><th>Quantity</th><th>Status</th><th>Date Created</th><th>Expiry Date</th></tr>";
    
    while ($row = sqlsrv_fetch_array($selectStmt, SQLSRV_FETCH_ASSOC)) {
        $datecreated = $row['datecreated'] instanceof DateTime ? $row['datecreated']->format('Y-m-d H:i:s') : $row['datecreated'];
        $expiry_date = $row['expiry_date'] instanceof DateTime ? $row['expiry_date']->format('Y-m-d H:i:s') : ($row['expiry_date'] ?? 'NULL');
        
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['code'] . "</td>";
        echo "<td>" . $row['items'] . "</td>";
        echo "<td>" . $row['quantity'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $datecreated . "</td>";
        echo "<td>" . $expiry_date . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถดึงข้อมูลได้</p>";
}

// ทำความสะอาดข้อมูลทดสอบ
echo "<h3>ทำความสะอาดข้อมูลทดสอบ</h3>";
$cleanupSql = "DELETE FROM WEB_Redeem_Code WHERE code LIKE 'TEST-%'";
$cleanupStmt = sqlsrv_query($conn, $cleanupSql);

if ($cleanupStmt) {
    echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
} else {
    echo "<p style='color: orange;'>⚠️ ไม่สามารถลบข้อมูลทดสอบได้</p>";
}

echo "<h3>สรุป</h3>";
echo "<p>✅ การทดสอบเสร็จสิ้น</p>";
echo "<p>📝 รูปแบบวันที่ที่แนะนำ: <code>YYYY-MM-DD HH:MM:SS</code></p>";
echo "<p>🔗 กลับไปทดสอบระบบหลัก: <a href='redeem_code_generator.php'>Redeem Code Generator</a></p>";
?>
