<?php $user->restrictionUser(true, $conn); ?>
<script type="text/javascript" charset="utf8" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-2.0.3.js"></script>
<div class="row">
                    <div class="col-lg-12">
                        <section class="panel">
                            <header class="panel-heading">
                            <h2><?php echo PT_MANAGECLANS; ?> <small><?php echo PT_MANAGECLANS_DESC; ?></h2>
                            </header>
				                <div class="panel-body">
    <div class="col-lg-12" style="margin: 10px 5px;">
        <form method="post" action="?url=manager/claresults" class="form-inline pull-right">
            <div class="form-group">
                <input type="text" class="form-control" name="search" placeholder="Search by Clan Name">
                <input type="submit" class="btn btn-info" name="btn_search" value="Search">
            </div>
        </form>
    </div>
    <div class="panel panel-default">
        <div class="panel-body no-padd" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
            <table class="table table-bordered table-striped mb-none" id="datatable-default">
                    <thead>
                        <tr>
                            <th>ClanID</th>
                            <th>ชื่อกิลล์</th>
                            <th>กิลล์เลเวล</th>
                            <th>กิลล์ พ้อย</th>
                            <th>วันที่สร้าง</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 1000;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM ".DATABASE_SV.".dbo.Guild";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            $selectOwner = "SELECT * FROM ".DATABASE_SV.".dbo.Guild WHERE GuildNo = '$row[0]'";
                            $selectOwnerParam = array();
                            $selectOwnerQuery = sqlsrv_query($conn, $selectOwner, $selectOwnerParam);
                            $selectOwnerFetch = sqlsrv_fetch_array($selectOwnerQuery, SQLSRV_FETCH_ASSOC);
                            ?>
                                     <tr>
                                        <td><?php echo $row[0]; ?></td>
                                        <td><?php echo $userLogin->thaitrans($row[2]); ?></td>
                                        <td><?php echo $row[10]; ?></td>
                                        <td><?php echo $row[11]; ?></td>
                                        <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[4])); ?></td>
                                    <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                            <?php echo B_ACTION; ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a href="?url=manager/see-clan&id=<?php echo $row[0]; ?>"><?php echo B_SEEALLINFO; ?></a></li>
                                            <li><a href="?url=manager/edit-clan&id=<?php echo $row[0]; ?>"><?php echo B_EDIT; ?></a></li>
                                            <li><a href="?url=manager/see-clan&id=<?php echo $row[0]; ?>&delete=wait"><?php echo B_DELETE; ?></a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=manager/clans&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }
                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=manager/clans&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum <  $numOfPages) {
                                $nextPageLink = "?url=manager/clans&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
$(document).ready(function(){
$('.pagination').pagination({
        items: <?php echo $rowsReturned;?>,
        itemsOnPage: <?php echo $rowsPerPage;?>,
        cssStyle: 'light-theme',
		currentPage : <?php echo $pageNum;?>,
		hrefTextPrefix : '?url=manager/clans&pageNum='
    });
	});
</script>