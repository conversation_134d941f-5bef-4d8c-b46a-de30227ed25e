<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-plug"></i> ทดสอบ API Direct Access
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา API ส่งคืน HTML แทน JSON</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fal fa-exclamation-triangle"></i>
                    <strong>ปัญหา:</strong> API กำลังส่งคืน HTML แทน JSON ทำให้เกิด error "Unexpected token '<'"
                </div>
                
                <h5>🔍 การตรวจสอบ API</h5>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testAPI('live_stats')">
                        <i class="fal fa-chart-line"></i> Test Live Stats
                    </button>
                    <button class="btn btn-info" onclick="testAPI('recent_activities')">
                        <i class="fal fa-history"></i> Test Recent Activities
                    </button>
                    <button class="btn btn-warning" onclick="testAPI('alerts')">
                        <i class="fal fa-bell"></i> Test Alerts
                    </button>
                    <button class="btn btn-success" onclick="testAPI('online_stats')">
                        <i class="fal fa-users"></i> Test Online Stats
                    </button>
                </div>
                
                <div id="api-test-results"></div>
                
                <h5 class="mt-4">📊 API Endpoints</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>Action</th>
                                <th>URL</th>
                                <th>Parameters</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>live_stats</td>
                                <td>files/manager_charecter/api/character-data.php?action=live_stats</td>
                                <td>-</td>
                                <td><span id="status-live_stats" class="badge badge-secondary">ยังไม่ทดสอบ</span></td>
                            </tr>
                            <tr>
                                <td>recent_activities</td>
                                <td>files/manager_charecter/api/character-data.php?action=recent_activities</td>
                                <td>limit (optional)</td>
                                <td><span id="status-recent_activities" class="badge badge-secondary">ยังไม่ทดสอบ</span></td>
                            </tr>
                            <tr>
                                <td>alerts</td>
                                <td>files/manager_charecter/api/character-data.php?action=alerts</td>
                                <td>-</td>
                                <td><span id="status-alerts" class="badge badge-secondary">ยังไม่ทดสอบ</span></td>
                            </tr>
                            <tr>
                                <td>online_stats</td>
                                <td>files/manager_charecter/api/character-data.php?action=online_stats</td>
                                <td>-</td>
                                <td><span id="status-online_stats" class="badge badge-secondary">ยังไม่ทดสอบ</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">🔧 การแก้ไขที่ทำ</h5>
                <div class="alert alert-info">
                    <h6>เปลี่ยน API URLs จาก routing เป็น direct access:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>ก่อนแก้ไข:</strong></p>
                            <pre><code>?url=manager_charecter/api/character-data&action=live_stats</code></pre>
                        </div>
                        <div class="col-md-6">
                            <p><strong>หลังแก้ไข:</strong></p>
                            <pre><code>files/manager_charecter/api/character-data.php?action=live_stats</code></pre>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบ Direct API Access</h5>
                <?php
                // ทดสอบ API โดยตรง
                echo "<h6>ทดสอบ API ภายใน PHP:</h6>";
                
                try {
                    // Include API functions
                    require_once('api/character-data.php');
                    
                    echo "<div class='alert alert-success'>";
                    echo "<strong>✅ API File:</strong> โหลดได้สำเร็จ<br>";
                    
                    // Test live stats
                    if (function_exists('getLiveStats')) {
                        $liveStats = getLiveStats($conn);
                        echo "<strong>✅ getLiveStats:</strong> ฟังก์ชันมีอยู่<br>";
                        echo "<strong>Result:</strong> " . (is_array($liveStats) ? "Array (" . count($liveStats) . " items)" : "Not array") . "<br>";
                    } else {
                        echo "<strong>❌ getLiveStats:</strong> ฟังก์ชันไม่มี<br>";
                    }
                    
                    // Test recent activities
                    if (function_exists('getRecentActivities')) {
                        $activities = getRecentActivities($conn, 5);
                        echo "<strong>✅ getRecentActivities:</strong> ฟังก์ชันมีอยู่<br>";
                        echo "<strong>Result:</strong> " . (is_array($activities) ? "Array (" . count($activities) . " items)" : "Not array") . "<br>";
                    } else {
                        echo "<strong>❌ getRecentActivities:</strong> ฟังก์ชันไม่มี<br>";
                    }
                    
                    // Test alerts
                    if (function_exists('getCharacterAlerts')) {
                        $alerts = getCharacterAlerts($conn);
                        echo "<strong>✅ getCharacterAlerts:</strong> ฟังก์ชันมีอยู่<br>";
                        echo "<strong>Result:</strong> " . (is_array($alerts) ? "Array (" . count($alerts) . " items)" : "Not array") . "<br>";
                    } else {
                        echo "<strong>❌ getCharacterAlerts:</strong> ฟังก์ชันไม่มี<br>";
                    }
                    
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<strong>❌ API Test Error:</strong> " . $e->getMessage();
                    echo "</div>";
                }
                ?>
                
                <h5 class="mt-4">📋 Troubleshooting</h5>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. API ส่งคืน HTML แทน JSON
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>สาเหตุ:</strong></p>
                                <ul>
                                    <li>Routing system ไม่รู้จัก path</li>
                                    <li>API file ไม่มีอยู่</li>
                                    <li>PHP error ทำให้ส่งคืน error page</li>
                                    <li>Authentication ล้มเหลว</li>
                                </ul>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <ol>
                                    <li>ใช้ direct file path แทน routing</li>
                                    <li>ตรวจสอบ API file มีอยู่</li>
                                    <li>ตรวจสอบ PHP errors</li>
                                    <li>ทดสอบ API แยกต่างหาก</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. JavaScript JSON Parse Error
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>Error Message:</strong></p>
                                <pre><code>SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON</code></pre>
                                
                                <p><strong>สาเหตุ:</strong> API ส่งคืน HTML page แทน JSON</p>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <ul>
                                    <li>ตรวจสอบ response ก่อน JSON.parse()</li>
                                    <li>ใช้ direct API path</li>
                                    <li>เพิ่ม error handling</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <ul class="mb-0">
                        <li>✅ เปลี่ยนจาก routing เป็น direct file access</li>
                        <li>✅ ใช้ path: <code>files/manager_charecter/api/character-data.php</code></li>
                        <li>✅ เก็บ credentials และ headers</li>
                        <li>✅ เพิ่ม error handling สำหรับ HTML response</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testAPI(action) {
    const resultDiv = document.getElementById('api-test-results');
    const statusSpan = document.getElementById('status-' + action);
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ ' + action + '...</div>';
    statusSpan.textContent = 'กำลังทดสอบ...';
    statusSpan.className = 'badge badge-warning';
    
    try {
        const url = 'files/manager_charecter/api/character-data.php?action=' + action + (action === 'recent_activities' ? '&limit=5' : '');
        
        const response = await fetch(url, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            }
        });
        
        const responseText = await response.text();
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> API Test: ' + action + '</h6>';
        html += '<p><strong>URL:</strong> ' + url + '</p>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Content-Type:</strong> ' + (response.headers.get('content-type') || 'Not set') + '</p>';
        
        if (responseText.trim().startsWith('{') || responseText.trim().startsWith('[')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>JSON Valid:</strong> ✅ Yes</p>';
                html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                
                if (data.success && data.data) {
                    if (Array.isArray(data.data)) {
                        html += '<p><strong>Data Count:</strong> ' + data.data.length + '</p>';
                    } else {
                        html += '<p><strong>Data Type:</strong> ' + typeof data.data + '</p>';
                    }
                }
                
                statusSpan.textContent = 'สำเร็จ';
                statusSpan.className = 'badge badge-success';
                
                html += '<details><summary>Response Data</summary><pre>' + JSON.stringify(data, null, 2) + '</pre></details>';
                
            } catch (parseError) {
                html += '<p><strong>JSON Valid:</strong> ❌ No - ' + parseError.message + '</p>';
                statusSpan.textContent = 'JSON Error';
                statusSpan.className = 'badge badge-danger';
            }
        } else {
            html += '<p><strong>Response Type:</strong> ' + (responseText.includes('<!DOCTYPE') ? 'HTML Document' : 'Unknown') + '</p>';
            html += '<p><strong>JSON Valid:</strong> ❌ No (Not JSON)</p>';
            statusSpan.textContent = 'HTML Response';
            statusSpan.className = 'badge badge-danger';
        }
        
        if (responseText.length > 0) {
            html += '<details><summary>Raw Response (' + responseText.length + ' chars)</summary><pre>' + responseText.substring(0, 1000) + (responseText.length > 1000 ? '...' : '') + '</pre></details>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> API Test Failed: ' + action + '</h6><p>Error: ' + error.message + '</p></div>';
        statusSpan.textContent = 'Error';
        statusSpan.className = 'badge badge-danger';
    }
}

// ทดสอบทุก API เมื่อโหลดหน้า
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        testAPI('live_stats');
    }, 1000);
});
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

details {
    margin-top: 0.5rem;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

summary:hover {
    text-decoration: underline;
}
</style>
