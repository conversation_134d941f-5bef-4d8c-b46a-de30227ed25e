<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-globe"></i> ทดสอบการแก้ไข DataTables CORS
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบการโหลดไฟล์ภาษาไทย DataTables</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    ทดสอบการแก้ไขปัญหา CORS ของ DataTables เมื่อโหลดไฟล์ภาษาไทยจาก CDN
                </div>
                
                <h5>🔧 การทดสอบ</h5>
                
                <!-- Test Table 1: ใช้ไฟล์ในเครื่อง -->
                <h6>1. ตารางทดสอบ - ใช้ไฟล์ภาษาไทยในเครื่อง</h6>
                <div class="table-responsive">
                    <table id="test-table-local" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>ชื่อ</th>
                                <th>อีเมล</th>
                                <th>วันที่สร้าง</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php for ($i = 1; $i <= 50; $i++): ?>
                            <tr>
                                <td><?php echo $i; ?></td>
                                <td>ผู้ใช้ <?php echo $i; ?></td>
                                <td>user<?php echo $i; ?>@example.com</td>
                                <td><?php echo date('Y-m-d', strtotime("-$i days")); ?></td>
                                <td>
                                    <?php if ($i % 3 == 0): ?>
                                        <span class="badge badge-success">ใช้งาน</span>
                                    <?php elseif ($i % 2 == 0): ?>
                                        <span class="badge badge-warning">รอการยืนยัน</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">ระงับ</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Test Table 2: ใช้ CDN (จะเกิด CORS error) -->
                <h6 class="mt-4">2. ตารางทดสอบ - ใช้ CDN (จะเกิด CORS error)</h6>
                <div class="alert alert-warning">
                    <i class="fal fa-exclamation-triangle"></i>
                    ตารางนี้จะพยายามโหลดจาก CDN และจะเกิด CORS error ใน localhost
                </div>
                <div class="table-responsive">
                    <table id="test-table-cdn" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>รหัส</th>
                                <th>ชื่อสินค้า</th>
                                <th>ราคา</th>
                                <th>จำนวน</th>
                                <th>หมวดหมู่</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php for ($i = 1; $i <= 30; $i++): ?>
                            <tr>
                                <td>P<?php echo str_pad($i, 3, '0', STR_PAD_LEFT); ?></td>
                                <td>สินค้า <?php echo $i; ?></td>
                                <td><?php echo number_format(rand(100, 5000)); ?> บาท</td>
                                <td><?php echo rand(1, 100); ?></td>
                                <td>หมวด <?php echo ($i % 5) + 1; ?></td>
                            </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Test Results -->
                <h5 class="mt-4">📊 ผลการทดสอบ</h5>
                <div id="test-results">
                    <div class="alert alert-secondary">
                        <i class="fal fa-clock"></i> กำลังทดสอบ...
                    </div>
                </div>
                
                <!-- Debug Tools -->
                <h5 class="mt-4">🔧 เครื่องมือ Debug</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary btn-sm" onclick="runCORSTest()">
                        <i class="fal fa-play"></i> ทดสอบ CORS
                    </button>
                    <button class="btn btn-info btn-sm" onclick="debugDataTables()">
                        <i class="fal fa-bug"></i> Debug DataTables
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="fixDataTablesCORS()">
                        <i class="fal fa-wrench"></i> แก้ไข CORS
                    </button>
                    <button class="btn btn-success btn-sm" onclick="checkLanguageFiles()">
                        <i class="fal fa-check"></i> ตรวจสอบไฟล์ภาษา
                    </button>
                </div>
                
                <div id="debug-output" class="mt-3"></div>
                
                <!-- Instructions -->
                <h5 class="mt-4">📋 วิธีแก้ไขปัญหา CORS</h5>
                <div class="alert alert-success">
                    <h6>✅ วิธีแก้ไขที่ทำแล้ว:</h6>
                    <ol>
                        <li><strong>สร้างไฟล์ภาษาไทยในเครื่อง:</strong> <code>assets/js/datatables-thai.json</code></li>
                        <li><strong>แก้ไข URL ในไฟล์ต่างๆ:</strong> เปลี่ยนจาก CDN เป็น local file</li>
                        <li><strong>สร้าง Global Configuration:</strong> <code>assets/js/datatables-config.js</code></li>
                        <li><strong>เพิ่ม Error Handling:</strong> จัดการกรณีโหลดไฟล์ไม่ได้</li>
                    </ol>
                    
                    <h6>🔧 ไฟล์ที่แก้ไขแล้ว:</h6>
                    <ul>
                        <li><code>files/manager_account/manage-account.php</code></li>
                        <li><code>files/dungeon_systems/manage-elite-dungeon.php</code></li>
                        <li><code>files/dungeon_systems/manage-dungeonranking-Party.php</code></li>
                        <li><code>files/dungeon_systems/manage-dungeonpoint.php</code></li>
                        <li><code>files/dungeon_systems/manage-dungeonranking-single.php</code></li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6>💡 เคล็ดลับ:</h6>
                    <ul class="mb-0">
                        <li>ใช้ <code>createThaiDataTable()</code> สำหรับสร้างตารางใหม่</li>
                        <li>เรียก <code>fixDataTablesCORS()</code> หากยังมีปัญหา CORS</li>
                        <li>ใช้ <code>debugDataTables()</code> เพื่อตรวจสอบการตั้งค่า</li>
                        <li>ตรวจสอบ Console เพื่อดู error messages</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Load DataTables และ Config -->
<script src="assets/js/datatables-config.js"></script>

<script>
$(document).ready(function() {
    // Test Table 1: ใช้ไฟล์ในเครื่อง
    $('#test-table-local').DataTable({
        language: {
            url: 'assets/js/datatables-thai.json'
        },
        pageLength: 10,
        responsive: true,
        order: [[0, 'asc']]
    });
    
    // Test Table 2: ใช้ CDN (จะเกิด CORS error)
    $('#test-table-cdn').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/th.json'
        },
        pageLength: 10,
        responsive: true,
        order: [[0, 'asc']],
        error: function(xhr, error, code) {
            console.error('CORS Error in test table:', error);
            updateTestResults('cdn-error', 'CORS Error ตามที่คาดไว้');
        }
    });
    
    // รอสักครู่แล้วทดสอบ
    setTimeout(runCORSTest, 2000);
});

function runCORSTest() {
    const results = [];
    
    // ทดสอบตาราง 1
    const table1 = $('#test-table-local').DataTable();
    if (table1) {
        results.push({
            name: 'ตารางใช้ไฟล์ในเครื่อง',
            status: 'success',
            message: 'โหลดสำเร็จ'
        });
    }
    
    // ทดสอบตาราง 2
    const table2 = $('#test-table-cdn').DataTable();
    if (table2) {
        results.push({
            name: 'ตารางใช้ CDN',
            status: 'warning',
            message: 'อาจมี CORS error (ตรวจสอบ Console)'
        });
    }
    
    // ทดสอบการโหลดไฟล์ภาษา
    fetch('assets/js/datatables-thai.json')
        .then(response => {
            if (response.ok) {
                results.push({
                    name: 'ไฟล์ภาษาไทยในเครื่อง',
                    status: 'success',
                    message: 'โหลดได้ปกติ'
                });
            } else {
                results.push({
                    name: 'ไฟล์ภาษาไทยในเครื่อง',
                    status: 'error',
                    message: 'โหลดไม่ได้'
                });
            }
            displayTestResults(results);
        })
        .catch(error => {
            results.push({
                name: 'ไฟล์ภาษาไทยในเครื่อง',
                status: 'error',
                message: 'เกิดข้อผิดพลาด: ' + error.message
            });
            displayTestResults(results);
        });
}

function displayTestResults(results) {
    let html = '<h6>ผลการทดสอบ:</h6>';
    
    results.forEach(result => {
        const alertClass = result.status === 'success' ? 'alert-success' : 
                          result.status === 'warning' ? 'alert-warning' : 'alert-danger';
        const icon = result.status === 'success' ? 'fa-check' : 
                    result.status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times';
        
        html += `
            <div class="alert ${alertClass} alert-sm">
                <i class="fal ${icon}"></i>
                <strong>${result.name}:</strong> ${result.message}
            </div>
        `;
    });
    
    document.getElementById('test-results').innerHTML = html;
}

function updateTestResults(type, message) {
    const resultsDiv = document.getElementById('test-results');
    const alertClass = type.includes('error') ? 'alert-danger' : 'alert-success';
    
    resultsDiv.innerHTML += `
        <div class="alert ${alertClass} alert-sm">
            <i class="fal fa-info-circle"></i>
            ${message}
        </div>
    `;
}

function checkLanguageFiles() {
    const output = document.getElementById('debug-output');
    output.innerHTML = '<h6>ตรวจสอบไฟล์ภาษา:</h6>';
    
    // ทดสอบไฟล์ในเครื่อง
    fetch('assets/js/datatables-thai.json')
        .then(response => response.json())
        .then(data => {
            output.innerHTML += `
                <div class="alert alert-success">
                    <strong>✅ ไฟล์ในเครื่อง:</strong> โหลดได้ปกติ<br>
                    <small>พบข้อมูล: ${Object.keys(data).length} keys</small>
                </div>
            `;
        })
        .catch(error => {
            output.innerHTML += `
                <div class="alert alert-danger">
                    <strong>❌ ไฟล์ในเครื่อง:</strong> ${error.message}
                </div>
            `;
        });
    
    // ทดสอบ CDN
    fetch('//cdn.datatables.net/plug-ins/1.13.7/i18n/th.json')
        .then(response => response.json())
        .then(data => {
            output.innerHTML += `
                <div class="alert alert-warning">
                    <strong>⚠️ CDN:</strong> โหลดได้ (แต่อาจมี CORS ใน localhost)<br>
                    <small>พบข้อมูล: ${Object.keys(data).length} keys</small>
                </div>
            `;
        })
        .catch(error => {
            output.innerHTML += `
                <div class="alert alert-danger">
                    <strong>❌ CDN:</strong> CORS Error - ${error.message}
                </div>
            `;
        });
}
</script>

<style>
.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

#debug-output {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}
</style>
