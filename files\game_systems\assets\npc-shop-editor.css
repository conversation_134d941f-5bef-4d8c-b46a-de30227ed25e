
/* Main Content Layout */
.main-content {
    gap: 20px;
    align-items: start;
    height: 110vh;
    padding: 20px 0;
}

/* Shop Grid Container */
.shop-grid-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 100%;

}

/* Shop Grid Styles */
.shop-grid {
    display: grid;
    grid-template-columns: repeat(8, 55px);
    grid-template-rows: repeat(16, 55px);
    gap: 1px;

}

.slot-button {
    background-color: #1f1e1e8c;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    width: 55px;
    height: 55px;
    text-align: center;
    line-height: 1.1;
    padding: 1px;
    margin:2px;
    border:1px solid #555;
    border-radius:8px;
    cursor:pointer;
}

.slot-button:hover {
    background-color: #5d5f5f;
    border-color: #95a5a6;
    transform: scale(1.05);
}

.slot-button.occupied {
    background-color: #00878b;
    border-color: #00f7ff;
}

.slot-button.occupied:hover {
    background-color: #a8e6cf;
}

.slot-button.dragging {
    opacity: 0.6;
    transform: scale(0.95);
}

.slot-button.drag-over {
    background-color: #f39c12;
    border-color: #e67e22;
}

.slot-number {
    font-size: 12px;
    color: #ffffff;
    line-height: 1;
}

.slot-item {
    font-size: 12px;
    color: #b6ab11;
    margin-top: 1px;
    word-break: break-all;
    line-height: 1;
    max-width: 55px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Controls Panel */
.controls-panel {
    background-color: #fafafa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}
.shop-controls-panel {
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
    margin-bottom: 5px;
}

.panel-header h2 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.3em;
}

.file-upload-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-text {
    color: #27ae60;
    font-weight: 500;
    font-size: 11px;
    white-space: nowrap;
}

.controls-panel h3 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.current-pool-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #ecf0f1;
    border-radius: 5px;
    font-size: 13px;
}

.current-pool-info span {
    font-weight: 600;
    color: #2c3e50;
}

/* Item Search Section */
.item-search-section {
    flex: 1;
}

.search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
    font-size: 13px;
    margin-bottom: 8px;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.item-results {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
    background-color: white;
}

.item-result {
    padding: 6px 10px;
    cursor: pointer;
    border-bottom: 1px solid #ecf0f1;
    font-size: 12px;
    line-height: 1.4;
}

.item-result:hover {
    background-color: #f8f9fa;
}

.item-result:last-child {
    border-bottom: none;
}

/* Actions Section */
.actions-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    animation: slideIn 0.3s ease;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #95a5a6;
    line-height: 1;
}


/* Form Styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

select.form-control {
    cursor: pointer;
}

/* Pool ID List */
.pool-id-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
}

.pool-id-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #ecf0f1;
}

.pool-id-item:hover {
    background-color: #f8f9fa;
}

.pool-id-item.selected {
    background-color: #3498db;
    color: white;
}

.pool-id-item:last-child {
    border-bottom: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Tooltip */
.tooltip {
    position: absolute;
    background-color: #2c3e50;
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 12px;
    white-space: pre-wrap;
    z-index: 1000;
    pointer-events: none;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Select2 Custom Styling */
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    height: 38px;
    line-height: 36px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    padding: 0 8px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #333;
    padding-left: 0;
    padding-right: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 8px;
}

.select2-dropdown {
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px;
}

.select2-container--default .select2-results__option {
    padding: 8px;
    line-height: 1.4;
}

.select2-container--default .select2-results__option--highlighted[data-selected] {
    background-color: #007bff;
    color: white;
}

.select2-container--default .select2-results__option[data-selected=true] {
    background-color: #e9ecef;
    color: #333;
}

/* Dark mode support for Select2 */
.modal-content.dark .select2-container--default .select2-selection--single {
    background-color: #2a2a2a;
    border-color: #555;
    color: #fff;
}

.modal-content.dark .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #fff;
}

.modal-content.dark .select2-dropdown {
    background-color: #2a2a2a;
    border-color: #555;
}

.modal-content.dark .select2-container--default .select2-search--dropdown .select2-search__field {
    background-color: #333;
    border-color: #555;
    color: #fff;
}

.modal-content.dark .select2-container--default .select2-results__option {
    background-color: #2a2a2a;
    color: #fff;
}

.modal-content.dark .select2-container--default .select2-results__option--highlighted[data-selected] {
    background-color: #007bff;
}

.modal-content.dark .select2-container--default .select2-results__option[data-selected=true] {
    background-color: #444;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 1px;
    }
    .main-content {
        grid-template-columns: 1fr;
        gap: 15px;
        height: auto;
        padding: 10px 0;
    }
    
    .shop-grid-container {
        padding: 1px;
    }
    
    .shop-grid {
        grid-template-columns: repeat(8, 55px);
        grid-template-rows: repeat(16, 55px);
    }
    .controls-panel {
        max-height: none;
        height: auto;
    }
    
    .panel-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1px;
    }
    
    .main-content {
        padding: 1px 0;
    }
    
    .shop-grid {
        grid-template-columns: repeat(8, 55px);
        grid-template-rows: repeat(16, 55px);
        
    }
    
    .slot-number {
        font-size: 12px;
    }
    
    .slot-item {
        font-size: 12px;
    }
    
    .current-pool-info {
        flex-direction: column;
        gap: 1px;
        text-align: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 1px;
    }
}

/* Scrollbar Styling */
.item-results::-webkit-scrollbar,
.pool-id-list::-webkit-scrollbar {
    width: 8px;
}

.item-results::-webkit-scrollbar-track,
.pool-id-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.item-results::-webkit-scrollbar-thumb,
.pool-id-list::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 4px;
}

.item-results::-webkit-scrollbar-thumb:hover,
.pool-id-list::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}
