<?php $user->restrictionUser(true, $conn); ?>

<?php
// Function to get recent mail activities
function getRecentMailActivities($conn, $limit = 50) {
    $activities = array();
    
    try {
        // Recent received mails
        $sql = "SELECT TOP ? 
                    'received' as type,
                    ReceivedMailID as mail_id,
                    ReceiverCharIdx as char_idx,
                    DeliveryTime,
                    Alz,
                    ItemKindIdx,
                    ItemOption,
                    ItemDurationIdx,
                    IsReceivedItem,
                    IsReceivedAlz,
                    SenderCharIdx,
                    SenderCharName,
                    Title,
                    Content
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                ORDER BY DeliveryTime DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $activities[] = $row;
            }
        }
        
    } catch (Exception $e) {
        error_log("Recent mail activities error: " . $e->getMessage());
    }
    
    return $activities;
}

// Function to get mail alerts (suspicious activities)
function getMailAlerts($conn) {
    $alerts = array();
    
    try {
        // Large Alz transfers
        $sql = "SELECT TOP 10 
                    'large_alz' as alert_type,
                    ReceivedMailID,
                    ReceiverCharIdx,
                    SenderCharName,
                    Alz,
                    DeliveryTime
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE Alz > 1000000 AND DeliveryTime >= DATEADD(hour, -24, GETDATE())
                ORDER BY Alz DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = $row;
            }
        }
        
        // High frequency senders
        $sql = "SELECT TOP 5 
                    'high_frequency' as alert_type,
                    SenderCharName,
                    COUNT(*) as mail_count,
                    MAX(DeliveryTime) as last_sent
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(hour, -1, GETDATE())
                GROUP BY SenderCharName
                HAVING COUNT(*) > 10
                ORDER BY mail_count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = $row;
            }
        }
        
    } catch (Exception $e) {
        error_log("Mail alerts error: " . $e->getMessage());
    }
    
    return $alerts;
}

$recentActivities = getRecentMailActivities($conn, 30);
$alerts = getMailAlerts($conn);
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-radar"></i> ตรวจสอบความเคลื่อนไหว Mail
    </h1>
    <div class="subheader-block">
        <div class="badge badge-success" id="statusBadge">
            <i class="fal fa-circle"></i> Online
        </div>
        <button class="btn btn-primary btn-sm" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
            <i class="fal fa-play"></i> เริ่มอัพเดทอัตโนมัติ
        </button>
        <a href="?url=manager_mail/mail-statistics" class="btn btn-info btn-sm">
            <i class="fal fa-chart-bar"></i> สถิติ
        </a>
    </div>
</div>

<!-- Alerts Section -->
<?php if (!empty($alerts)): ?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <h5><i class="fal fa-exclamation-triangle"></i> การแจ้งเตือน</h5>
            <div class="row">
                <?php foreach ($alerts as $alert): ?>
                    <div class="col-md-6 mb-2">
                        <?php if ($alert['alert_type'] == 'large_alz'): ?>
                            <div class="alert alert-danger alert-sm">
                                <strong>การโอน Alz จำนวนมาก:</strong><br>
                                ผู้ส่ง: <?php echo htmlspecialchars($alert['SenderCharName']); ?><br>
                                จำนวน: <?php echo number_format($alert['Alz']); ?> Alz<br>
                                เวลา: <?php echo date('d/m/Y H:i', strtotime($alert['DeliveryTime'])); ?>
                            </div>
                        <?php elseif ($alert['alert_type'] == 'high_frequency'): ?>
                            <div class="alert alert-warning alert-sm">
                                <strong>การส่งเมลล์ความถี่สูง:</strong><br>
                                ผู้ส่ง: <?php echo htmlspecialchars($alert['SenderCharName']); ?><br>
                                จำนวน: <?php echo $alert['mail_count']; ?> เมลล์ในชั่วโมงที่ผ่านมา
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Recent Activities -->
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมล่าสุด</h3>
                <div class="card-toolbar">
                    <span class="badge badge-info" id="lastUpdate">อัพเดทล่าสุด: <?php echo date('H:i:s'); ?></span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                    <table class="table table-sm table-hover mb-0" id="activitiesTable">
                        <thead class="thead-light sticky-top">
                            <tr>
                                <th>เวลา</th>
                                <th>ประเภท</th>
                                <th>ผู้ส่ง</th>
                                <th>ผู้รับ</th>
                                <th>Alz</th>
                                <th>ไอเท็ม</th>
                                <th>สถานะ</th>
                                <th>หัวข้อ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentActivities as $activity): ?>
                                <tr class="activity-row" data-time="<?php echo strtotime($activity['DeliveryTime']); ?>">
                                    <td class="text-nowrap">
                                        <?php echo date('H:i:s', strtotime($activity['DeliveryTime'])); ?>
                                        <br><small class="text-muted"><?php echo date('d/m/Y', strtotime($activity['DeliveryTime'])); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">รับ</span>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['SenderCharName'] ?? 'N/A'); ?></td>
                                    <td><?php echo $activity['char_idx']; ?></td>
                                    <td>
                                        <?php if ($activity['Alz'] > 0): ?>
                                            <span class="text-success"><?php echo number_format($activity['Alz']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($activity['ItemKindIdx'] > 0): ?>
                                            <span class="text-warning"><?php echo $activity['ItemKindIdx']; ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($activity['IsReceivedItem'] == 1 && $activity['IsReceivedAlz'] == 1): ?>
                                            <span class="badge badge-success">รับแล้ว</span>
                                        <?php elseif ($activity['IsReceivedItem'] == 1 || $activity['IsReceivedAlz'] == 1): ?>
                                            <span class="badge badge-warning">รับบางส่วน</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">ยังไม่รับ</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-truncate" style="max-width: 150px;">
                                        <?php echo htmlspecialchars($activity['Title'] ?? ''); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Live Stats -->
    <div class="col-xl-4">
        <div class="card mb-3">
            <div class="card-header">
                <h3 class="card-title">สถิติสด</h3>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h3 text-primary" id="todayCount">-</div>
                        <div class="text-muted small">เมลล์วันนี้</div>
                    </div>
                    <div class="col-6">
                        <div class="h3 text-success" id="hourCount">-</div>
                        <div class="text-muted small">เมลล์ชั่วโมงนี้</div>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-warning" id="unreadCount">-</div>
                        <div class="text-muted small">ยังไม่อ่าน</div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-info" id="alzTotal">-</div>
                        <div class="text-muted small">Alz วันนี้</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">สถานะระบบ</h3>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>การเชื่อมต่อฐานข้อมูล</span>
                    <span class="badge badge-success">ปกติ</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>อัพเดทอัตโนมัติ</span>
                    <span class="badge badge-secondary" id="autoRefreshStatus">ปิด</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>อัพเดทล่าสุด</span>
                    <span class="text-muted" id="lastUpdateTime"><?php echo date('H:i:s'); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

function toggleAutoRefresh() {
    const btn = document.getElementById('autoRefreshBtn');
    const status = document.getElementById('autoRefreshStatus');
    
    if (isAutoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        btn.innerHTML = '<i class="fal fa-play"></i> เริ่มอัพเดทอัตโนมัติ';
        btn.className = 'btn btn-primary btn-sm';
        status.textContent = 'ปิด';
        status.className = 'badge badge-secondary';
        isAutoRefreshEnabled = false;
    } else {
        autoRefreshInterval = setInterval(refreshData, 10000); // Refresh every 10 seconds
        btn.innerHTML = '<i class="fal fa-pause"></i> หยุดอัพเดทอัตโนมัติ';
        btn.className = 'btn btn-warning btn-sm';
        status.textContent = 'เปิด';
        status.className = 'badge badge-success';
        isAutoRefreshEnabled = true;
        refreshData(); // Initial refresh
    }
}

async function refreshData() {
    try {
        // Update timestamp
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleTimeString();
        document.getElementById('lastUpdate').textContent = 'อัพเดทล่าสุด: ' + new Date().toLocaleTimeString();

        // Fetch live stats
        const statsResponse = await fetch('files/manager_mail/api/mail-data.php?action=live_stats');
        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            if (statsData.success) {
                updateLiveStatsDisplay(statsData.data);
            }
        }

        // Fetch recent activities
        const activitiesResponse = await fetch('files/manager_mail/api/mail-data.php?action=recent_activities&limit=10');
        if (activitiesResponse.ok) {
            const activitiesData = await activitiesResponse.json();
            if (activitiesData.success) {
                updateActivitiesTable(activitiesData.data);
            }
        }

        // Fetch alerts
        const alertsResponse = await fetch('files/manager_mail/api/mail-data.php?action=alerts');
        if (alertsResponse.ok) {
            const alertsData = await alertsResponse.json();
            if (alertsData.success && alertsData.data.length > 0) {
                showNewAlerts(alertsData.data);
            }
        }

    } catch (error) {
        console.error('Error refreshing data:', error);
        document.getElementById('statusBadge').innerHTML = '<i class="fal fa-exclamation-triangle"></i> Error';
        document.getElementById('statusBadge').className = 'badge badge-danger';
    }
}

// Initialize live stats
function updateLiveStats() {
    // This would typically fetch data via AJAX
    // For demonstration, we'll use placeholder values
    document.getElementById('todayCount').textContent = '<?php echo count($recentActivities); ?>';
    document.getElementById('hourCount').textContent = '<?php echo count(array_filter($recentActivities, function($a) { return strtotime($a["DeliveryTime"]) > strtotime("-1 hour"); })); ?>';
    document.getElementById('unreadCount').textContent = '<?php echo count(array_filter($recentActivities, function($a) { return $a["IsReceivedItem"] == 0 && $a["IsReceivedAlz"] == 0; })); ?>';
    document.getElementById('alzTotal').textContent = '<?php echo number_format(array_sum(array_column($recentActivities, "Alz"))); ?>';
}

function updateLiveStatsDisplay(stats) {
    if (stats.today_count !== undefined) {
        document.getElementById('todayCount').textContent = stats.today_count.toLocaleString();
    }
    if (stats.hour_count !== undefined) {
        document.getElementById('hourCount').textContent = stats.hour_count.toLocaleString();
    }
    if (stats.unread_count !== undefined) {
        document.getElementById('unreadCount').textContent = stats.unread_count.toLocaleString();
    }
    if (stats.alz_total !== undefined) {
        document.getElementById('alzTotal').textContent = stats.alz_total.toLocaleString();
    }
}

function updateActivitiesTable(activities) {
    const tbody = document.querySelector('#activitiesTable tbody');
    if (!tbody || activities.length === 0) return;

    // Add new activities to the top
    activities.forEach(activity => {
        const existingRow = tbody.querySelector(`[data-mail-id="${activity.mail_id}"]`);
        if (existingRow) return; // Skip if already exists

        const row = document.createElement('tr');
        row.className = 'activity-row new-activity';
        row.setAttribute('data-mail-id', activity.mail_id);
        row.setAttribute('data-time', new Date(activity.time).getTime() / 1000);

        const time = new Date(activity.time);
        const timeStr = time.toLocaleTimeString();
        const dateStr = time.toLocaleDateString();

        row.innerHTML = `
            <td class="text-nowrap">
                ${timeStr}
                <br><small class="text-muted">${dateStr}</small>
            </td>
            <td><span class="badge badge-primary">รับ</span></td>
            <td>${activity.sender || 'N/A'}</td>
            <td>${activity.receiver}</td>
            <td>
                ${activity.alz > 0 ? `<span class="text-success">${activity.alz.toLocaleString()}</span>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                ${activity.item_code > 0 ? `<span class="text-warning">${activity.item_code}</span>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                ${getStatusBadge(activity.is_received_item, activity.is_received_alz)}
            </td>
            <td class="text-truncate" style="max-width: 150px;">
                ${activity.title || ''}
            </td>
        `;

        tbody.insertBefore(row, tbody.firstChild);
    });

    // Remove old rows (keep only latest 50)
    const rows = tbody.querySelectorAll('tr');
    if (rows.length > 50) {
        for (let i = 50; i < rows.length; i++) {
            rows[i].remove();
        }
    }

    // Highlight new activities
    setTimeout(() => {
        document.querySelectorAll('.new-activity').forEach(row => {
            row.classList.remove('new-activity');
        });
    }, 3000);
}

function getStatusBadge(isReceivedItem, isReceivedAlz) {
    if (isReceivedItem == 1 && isReceivedAlz == 1) {
        return '<span class="badge badge-success">รับแล้ว</span>';
    } else if (isReceivedItem == 1 || isReceivedAlz == 1) {
        return '<span class="badge badge-warning">รับบางส่วน</span>';
    } else {
        return '<span class="badge badge-secondary">ยังไม่รับ</span>';
    }
}

function showNewAlerts(alerts) {
    // Show alerts in a toast or notification area
    alerts.forEach(alert => {
        if (alert.severity === 'high') {
            showToast(alert.message, 'danger');
        } else if (alert.severity === 'medium') {
            showToast(alert.message, 'warning');
        }
    });
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Initialize
updateLiveStats();

// Highlight new activities
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.activity-row');
    const now = Math.floor(Date.now() / 1000);
    
    rows.forEach(row => {
        const time = parseInt(row.dataset.time);
        if (now - time < 300) { // Less than 5 minutes ago
            row.style.backgroundColor = '#fff3cd';
        }
    });
});
</script>

<style>
.new-activity {
    background-color: #fff3cd !important;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.activity-row:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
}

.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}

.thead-light th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Status indicators */
.badge-success { background-color: #28a745; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-secondary { background-color: #6c757d; }
.badge-primary { background-color: #007bff; }
.badge-danger { background-color: #dc3545; }
.badge-info { background-color: #17a2b8; }

/* Live stats animations */
.h3, .h4 {
    transition: all 0.3s ease;
}

.h3:hover, .h4:hover {
    transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .subheader-block {
        margin-top: 10px;
    }

    .subheader-block .btn {
        margin-bottom: 5px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .flow-step {
        margin-bottom: 15px;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
