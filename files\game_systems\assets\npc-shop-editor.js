// NPC Shop Editor JavaScript
class NPCShopEditor {
    constructor() {
        this.itemData = new Map(); // itemId -> itemName
        this.shopEntries = new Map(); // slotId -> NpcShopEntry
        this.currentPoolId = null;
        this.importedPoolData = new Map(); // poolId -> rows[]
        this.isDirty = false;
        this.dragSource = null;
        
        this.init();
    }

    init() {
        this.createShopGrid();
        this.bindEvents();
        this.updateUI();
        
        // Auto-load sample XML data for testing
        this.loadSampleData();
    }

    createShopGrid() {
        const grid = document.getElementById('shopGrid');
        grid.innerHTML = '';
        
        for (let i = 0; i < 128; i++) {
            const button = document.createElement('button');
            button.className = 'slot-button';
            button.dataset.slot = i;
            button.innerHTML = `<div class="slot-number">${i}</div><div class="slot-item">-</div>`;
            
            // Add event listeners
            button.addEventListener('contextmenu', (e) => this.handleSlotRightClick(e, i));
            button.addEventListener('mousedown', (e) => this.handleSlotMouseDown(e, i));
            button.addEventListener('dragover', (e) => this.handleDragOver(e));
            button.addEventListener('drop', (e) => this.handleDrop(e, i));
            button.addEventListener('dragenter', (e) => this.handleDragEnter(e));
            button.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            button.addEventListener('mouseenter', (e) => this.showTooltip(e, i));
            button.addEventListener('mouseleave', (e) => this.hideTooltip(e));
            
            button.draggable = true;
            button.addEventListener('dragstart', (e) => this.handleDragStart(e, i));
            button.addEventListener('dragend', (e) => this.handleDragEnd(e));
            
            grid.appendChild(button);
        }
    }

    bindEvents() {
        // File input for XML
        document.getElementById('xmlFile').addEventListener('change', (e) => this.handleXmlUpload(e));
        
        // File input for import
        document.getElementById('importFile').addEventListener('change', (e) => this.handleImport(e));
        
        // Import/Export buttons
        document.getElementById('importBtn').addEventListener('click', () => {
            document.getElementById('importFile').click();
        });
        
        document.getElementById('exportBtn').addEventListener('click', () => this.exportShop());
        
        // Change Pool ID
        document.getElementById('changePoolBtn').addEventListener('click', () => this.showPoolSelection());
        
        // Item search
        document.getElementById('itemSearch').addEventListener('input', (e) => this.filterItems(e.target.value));
        
        // Modal events
        this.bindModalEvents();
    }

    bindModalEvents() {
        // Edit modal
        const editModal = document.getElementById('editModal');
        document.querySelector('#editModal .close').addEventListener('click', () => this.hideModal('editModal'));
        document.getElementById('cancelEditBtn').addEventListener('click', () => this.hideModal('editModal'));
        document.getElementById('saveSlotBtn').addEventListener('click', () => this.saveSlot());
        document.getElementById('clearSlotBtn').addEventListener('click', () => this.clearSlot());
        
        // Pool ID modal
        const poolModal = document.getElementById('poolIdModal');
        document.querySelector('#poolIdModal .close').addEventListener('click', () => this.hideModal('poolIdModal'));
        document.getElementById('cancelPoolBtn').addEventListener('click', () => this.hideModal('poolIdModal'));
        document.getElementById('selectPoolBtn').addEventListener('click', () => this.selectPool());
        
        // Close modals on outside click
        [editModal, poolModal].forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });
    }

    async handleXmlUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.xml')) {
                throw new Error('Please select an XML file (*.xml)');
            }
            
            // Check file size (limit to 100MB)
            if (file.size > 100 * 1024 * 1024) {
                throw new Error('File is too large. Please select a file smaller than 100MB.');
            }
            
            const text = await file.text();
            
            // Validate XML content
            if (!text.trim()) {
                throw new Error('The selected file is empty');
            }
            
            this.parseItemData(text);
            document.getElementById('loadedXmlStatus').textContent = `Loaded: ${file.name}`;
            this.updateItemSearch();
            this.showNotification(`Successfully loaded ${this.itemData.size} items from ${file.name}!`, 'success');
        } catch (error) {
            console.error('Error loading XML:', error);
            document.getElementById('loadedXmlStatus').textContent = 'Error loading XML';
            this.showNotification('Failed to load XML file: ' + error.message, 'error');
        }
        
        // Reset the file input
        event.target.value = '';
    }

    parseItemData(xmlText) {
        // Basic XML parsing to extract item data
        this.itemData.clear();
        
        try {
            // Clean up the XML text first
            xmlText = xmlText.trim();
            
            // Fix common XML issues before parsing
            xmlText = this.sanitizeXmlContent(xmlText);
            
            // Log first few lines for debugging
            const firstLines = xmlText.split('\n').slice(0, 5).join('\n');
            console.log('XML file preview:', firstLines);
            
            // Use DOMParser for proper XML parsing
            const parser = new DOMParser();
            const doc = parser.parseFromString(xmlText, 'text/xml');
            
            // Check for parse errors
            const parserError = doc.querySelector('parsererror');
            if (parserError) {
                console.error('XML Parse Error:', parserError.textContent);
                throw new Error('Invalid XML format: ' + parserError.textContent);
            }
            
            // Debug: Log the root element
            console.log('XML root element:', doc.documentElement.tagName);
            
            // Find item_msg section and extract items
            const itemMsgNode = doc.querySelector('item_msg');
            if (!itemMsgNode) {
                // Try alternative xpath
                const allElements = doc.querySelectorAll('*');
                console.log('Available XML elements:', Array.from(allElements).map(el => el.tagName));
                throw new Error('No <item_msg> section found in XML file. Available elements: ' + Array.from(allElements).map(el => el.tagName).join(', '));
            }
            
            const msgNodes = itemMsgNode.querySelectorAll('msg[id^="item"]');
            if (msgNodes.length === 0) {
                // Debug: check all msg nodes
                const allMsgNodes = itemMsgNode.querySelectorAll('msg');
                console.log('Found msg nodes:', allMsgNodes.length);
                if (allMsgNodes.length > 0) {
                    console.log('Sample msg node:', allMsgNodes[0].outerHTML);
                }
                throw new Error(`No item messages found in <item_msg> section. Found ${allMsgNodes.length} msg nodes total.`);
            }
            
            msgNodes.forEach(node => {
                const id = node.getAttribute('id');
                const cont = node.getAttribute('cont');
                if (id && cont && id.startsWith('item')) {
                    const itemId = parseInt(id.substring(4));
                    if (!isNaN(itemId)) {
                        // Decode HTML entities in the content
                        const decodedContent = this.decodeHtmlEntities(cont);
                        this.itemData.set(itemId, decodedContent);
                    }
                }
            });
            
            if (this.itemData.size === 0) {
                throw new Error('No valid items found in XML file');
            }
            
            console.log(`Successfully loaded ${this.itemData.size} items from XML`);
            
        } catch (error) {
            console.error('Error parsing XML:', error);
            throw new Error(`Failed to parse XML: ${error.message}`);
        }
    }

    sanitizeXmlContent(xmlText) {
        // Process each cont attribute individually to avoid regex issues
        return xmlText.replace(/cont="([^"]*)"/g, (match, content) => {
            // Escape XML special characters in the content
            let sanitized = content
                // First escape ampersands that aren't already part of entities
                .replace(/&(?!amp;|lt;|gt;|quot;|apos;|#\d+;|#x[0-9a-fA-F]+;)/g, '&amp;')
                // Then escape other special characters
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&apos;');
            
            return `cont="${sanitized}"`;
        });
    }

    decodeHtmlEntities(text) {
        // Create a temporary element to decode HTML entities
        const textarea = document.createElement('textarea');
        textarea.innerHTML = text;
        return textarea.value;
    }

    async handleImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            this.parseShopData(text);
            this.showNotification('Shop data imported successfully!', 'success');
        } catch (error) {
            console.error('Error importing shop data:', error);
            this.showNotification('Failed to import shop data: ' + error.message, 'error');
        }
        
        // Reset the file input
        event.target.value = '';
    }

    parseShopData(text) {
        const lines = text.split('\n');
        this.importedPoolData.clear();
        
        let i = 0;
        while (i < lines.length) {
            const line = lines[i].trim();
            if (line.startsWith('[Shop]')) {
                i++;
                // Parse shop entries until next section or end
                while (i < lines.length && !lines[i].trim().startsWith('[') && lines[i].trim()) {
                    const cols = lines[i].split('\t');
                    if (cols.length > 1) {
                        const poolId = cols[1].trim();
                        if (!this.importedPoolData.has(poolId)) {
                            this.importedPoolData.set(poolId, []);
                        }
                        this.importedPoolData.get(poolId).push(lines[i]);
                    }
                    i++;
                }
            } else {
                i++;
            }
        }
        
        if (this.importedPoolData.size === 0) {
            throw new Error('No [Shop] blocks with Pool_ID found in file');
        }
        
        // Show pool selection if multiple pools
        const poolIds = Array.from(this.importedPoolData.keys());
        if (poolIds.length === 1) {
            this.loadPoolData(poolIds[0]);
        } else {
            this.showPoolSelection();
        }
        
        document.getElementById('changePoolBtn').disabled = false;
    }

    showPoolSelection() {
        if (this.importedPoolData.size === 0) {
            this.showNotification('No imported shop data available. Please import first.', 'warning');
            return;
        }
        
        const poolList = document.getElementById('poolIdList');
        poolList.innerHTML = '';
        
        Array.from(this.importedPoolData.keys()).forEach(poolId => {
            const item = document.createElement('div');
            item.className = 'pool-id-item';
            item.textContent = poolId;
            item.addEventListener('click', () => this.selectPoolInModal(item, poolId));
            poolList.appendChild(item);
        });
        
        this.showModal('poolIdModal');
    }

    selectPoolInModal(element, poolId) {
        // Remove previous selection
        document.querySelectorAll('.pool-id-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to clicked item
        element.classList.add('selected');
        element.dataset.poolId = poolId;
        
        document.getElementById('selectPoolBtn').disabled = false;
    }

    selectPool() {
        const selected = document.querySelector('.pool-id-item.selected');
        if (selected) {
            this.loadPoolData(selected.dataset.poolId);
            this.hideModal('poolIdModal');
        }
    }

    loadPoolData(poolId) {
        this.shopEntries.clear();
        
        if (this.importedPoolData.has(poolId)) {
            const rows = this.importedPoolData.get(poolId);
            rows.forEach(line => {
                const entry = this.parseShopEntry(line);
                if (entry) {
                    this.shopEntries.set(entry.slotId, entry);
                }
            });
        }
        
        this.currentPoolId = poolId;
        this.isDirty = false;
        this.updateShopGrid();
        this.updateUI();
        this.showNotification(`Shop data for Pool_ID ${poolId} loaded.`, 'success');
    }

    parseShopEntry(line) {
        const cols = line.split('\t');
        if (cols.length < 22) return null;
        
        return {
            index: parseInt(cols[0]) || 0,
            poolId: cols[1],
            tabId: parseInt(cols[2]) || 0,
            slotId: parseInt(cols[3]) || 0,
            itemKind: parseInt(cols[4]) || 0,
            itemOpt: parseInt(cols[5]) || 0,
            durationIdx: parseInt(cols[6]) || 0,
            minLevel: parseInt(cols[7]) || 0,
            maxLevel: parseInt(cols[8]) || 0,
            guildMinLevel: parseInt(cols[9]) || 0,
            reputation: parseInt(cols[10]) || 0,
            maxReputation: parseInt(cols[11]) || 0,
            onlyPremium: parseInt(cols[12]) || 0,
            onlyWin: parseInt(cols[13]) || 0,
            alzPrice: parseInt(cols[14]) || 0,
            wExpPrice: parseInt(cols[15]) || 0,
            apPrice: parseInt(cols[16]) || 0,
            dpPrice: parseInt(cols[17]) || 0,
            itemPrice: parseInt(cols[18]) || 0,
            forcegemPrice: parseInt(cols[19]) || 0,
            dLimit: parseInt(cols[20]) || 0,
            wLimit: parseInt(cols[21]) || 0
        };
    }

    updateShopGrid() {
        const slots = document.querySelectorAll('.slot-button');
        slots.forEach((button, index) => {
            const entry = this.shopEntries.get(index);
            const slotItem = button.querySelector('.slot-item');
            
            if (entry && entry.itemKind !== 0) {
                button.classList.add('occupied');
                slotItem.textContent = entry.itemKind.toString();
            } else {
                button.classList.remove('occupied');
                slotItem.textContent = '-';
            }
        });
    }

    updateItemSearch() {
        this.filterItems(document.getElementById('itemSearch').value);
    }

    filterItems(searchTerm) {
        const results = document.getElementById('itemResults');
        results.innerHTML = '';
        
        if (this.itemData.size === 0) {
            results.innerHTML = '<div class="item-result">No item data loaded</div>';
            return;
        }
        
        const filtered = Array.from(this.itemData.entries())
            .filter(([id, name]) => name.toLowerCase().includes(searchTerm.toLowerCase()))
            .slice(0, 100); // Limit results
        
        if (filtered.length === 0) {
            results.innerHTML = '<div class="item-result">No items found</div>';
            return;
        }
        
        filtered.forEach(([id, name]) => {
            const item = document.createElement('div');
            item.className = 'item-result';
            item.textContent = `${id} - ${name}`;
            item.dataset.itemId = id;
            results.appendChild(item);
        });
    }

    handleSlotRightClick(event, slotId) {
        event.preventDefault();
        this.showEditModal(slotId);
    }

    showEditModal(slotId) {
        const entry = this.shopEntries.get(slotId);
        
        document.getElementById('editSlotNumber').textContent = slotId;
        
        // Store the entry data for use after Select2 initialization
        this.currentEditEntry = entry;
        this.currentEditSlotId = slotId;
        
        // Populate item dropdown
        this.populateItemDropdown();
        
        document.getElementById('editModal').dataset.slotId = slotId;
        this.showModal('editModal');
    }

    populateItemDropdown() {
        const select = document.getElementById('editItemSelect');
        
        // Destroy existing Select2 instance if it exists
        if (typeof $ !== 'undefined' && $.fn.select2) {
            const $select = $('#editItemSelect');
            if ($select.hasClass('select2-hidden-accessible')) {
                try {
                    $select.select2('destroy');
                } catch (e) {
                    console.warn('Error destroying Select2 instance during populate:', e);
                }
            }
        }
        
        // Clear the select first
        select.innerHTML = '<option value="">Select an item...</option>';
        
        console.log('Populating item dropdown, itemData size:', this.itemData.size);
        
        if (this.itemData.size > 0) {
            console.log('Adding items to dropdown...');
            
            // For large datasets, only add first 100 items initially
            // The rest will be loaded via AJAX-like functionality in Select2
            const sortedItems = Array.from(this.itemData.entries())
                .sort((a, b) => a[1].localeCompare(b[1]));
            
            // Add only first 100 items initially for better performance
            const initialItems = sortedItems.slice(0, 100);
            
            // If we have a current entry, make sure its item is included in the initial items
            if (this.currentEditEntry && this.currentEditEntry.itemKind && this.currentEditEntry.itemKind !== 0) {
                const currentItemId = this.currentEditEntry.itemKind;
                const currentItemName = this.itemData.get(currentItemId);
                
                if (currentItemName && !initialItems.some(([id]) => id == currentItemId)) {
                    // Add the current item to the beginning of the list
                    initialItems.unshift([currentItemId, currentItemName]);
                    console.log('Added current item to dropdown:', currentItemId, currentItemName);
                }
            }
            
            initialItems.forEach(([id, name]) => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${id} - ${name}`;
                select.appendChild(option);
            });
            
            console.log('Initial items added to dropdown:', initialItems.length);
            
        } else {
            console.warn('No item data available for dropdown');
            // Add a message if no items are loaded
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No item data loaded. Please load XML file first.';
            option.disabled = true;
            select.appendChild(option);
        }
        
        // Initialize Select2 with AJAX-like search for better performance
        this.initializeSelect2WithAjax();
        
        // Set form values after Select2 is initialized
        this.setEditFormValues();
    }

    initializeSelect2WithAjax() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            const $select = $('#editItemSelect');
            
            // Destroy existing Select2 instance if it exists
            if ($select.hasClass('select2-hidden-accessible')) {
                try {
                    $select.select2('destroy');
                } catch (e) {
                    console.warn('Error destroying Select2 instance:', e);
                }
            }
            
            console.log('Initializing Select2 with AJAX-like search for', this.itemData.size, 'items');
            
            try {
                $select.select2({
                    placeholder: 'Type to search items...',
                    allowClear: true,
                    dropdownParent: $('#editModal'),
                    width: '100%',
                    minimumInputLength: 2,
                    ajax: {
                        delay: 250,
                        transport: (params, success, failure) => {
                            // Custom transport to search through our local itemData
                            const term = (params.data.term || '').toLowerCase();
                            const page = params.data.page || 1;
                            const pageSize = 50;
                            
                            // Filter items based on search term
                            let filteredItems = [];
                            if (term.length >= 2) {
                                filteredItems = Array.from(this.itemData.entries())
                                    .filter(([id, name]) => 
                                        name.toLowerCase().includes(term) || 
                                        id.toString().includes(term)
                                    )
                                    .sort((a, b) => a[1].localeCompare(b[1]));
                            } else {
                                // Show first 100 items if no search term
                                filteredItems = Array.from(this.itemData.entries())
                                    .sort((a, b) => a[1].localeCompare(b[1]))
                                    .slice(0, 100);
                            }
                            
                            // Paginate results
                            const start = (page - 1) * pageSize;
                            const end = start + pageSize;
                            const pageItems = filteredItems.slice(start, end);
                            
                            const results = pageItems.map(([id, name]) => ({
                                id: id,
                                text: `${id} - ${name}`
                            }));
                            
                            success({
                                results: results,
                                pagination: {
                                    more: end < filteredItems.length
                                }
                            });
                        },
                        processResults: function (data) {
                            return data;
                        }
                    },
                    language: {
                        noResults: function () {
                            return 'No items found - try different keywords';
                        },
                        inputTooShort: function() {
                            return 'Type at least 2 characters to search';
                        },
                        searching: function () {
                            return 'Searching items...';
                        },
                        loadingMore: function() {
                            return 'Loading more results...';
                        }
                    }
                });
                
                console.log('Select2 with AJAX search initialized successfully');
                
                $select.on('select2:select', function(e) {
                    console.log('Item selected:', e.params.data);
                });
                
            } catch (error) {
                console.error('Error initializing Select2 with AJAX:', error);
                console.log('Falling back to basic Select2');
                this.initializeSelect2();
            }
        } else {
            console.warn('Select2 not available, falling back to regular select');
        }
    }

    initializeSelect2() {
        // Initialize Select2 for the item select dropdown
        if (typeof $ !== 'undefined' && $.fn.select2) {
            const $select = $('#editItemSelect');
            
            // Destroy existing Select2 instance if it exists
            if ($select.hasClass('select2-hidden-accessible')) {
                try {
                    $select.select2('destroy');
                } catch (e) {
                    console.warn('Error destroying Select2 instance:', e);
                }
            }
            
            console.log('Initializing Select2 with', $select.find('option').length, 'options');
            
            // Test if Select2 is working by checking a simple case first
            if ($select.find('option').length > 1000) {
                console.log('Large dataset detected, using optimized configuration');
            }
            
            try {
                // Initialize Select2 with basic configuration first
                $select.select2({
                    placeholder: 'Type to search items...',
                    allowClear: true,
                    dropdownParent: $('#editModal'),
                    width: '100%',
                    minimumInputLength: 2, // Require at least 2 characters to search
                    closeOnSelect: true,
                    ajax: false, // Use static data
                    language: {
                        noResults: function () {
                            return 'No items found - try typing more characters';
                        },
                        inputTooShort: function() {
                            return 'Type at least 2 characters to search';
                        },
                        searching: function () {
                            return 'Searching items...';
                        }
                    }
                });
                
                console.log('Select2 initialized successfully');
                
                // Test opening the dropdown programmatically
                $select.on('select2:open', function() {
                    console.log('Select2 dropdown opened');
                    const resultsContainer = document.querySelector('.select2-results__options');
                    if (resultsContainer) {
                        console.log('Results container found, visible options:', resultsContainer.children.length);
                    } else {
                        console.warn('Results container not found');
                    }
                });
                
                $select.on('select2:select', function(e) {
                    console.log('Item selected:', e.params.data);
                });
                
            } catch (error) {
                console.error('Error initializing Select2:', error);
                console.log('Falling back to regular select element');
            }
        } else {
            console.warn('Select2 not available, using regular select');
        }
    }

    filterEditItems(searchTerm) {
        // This method is no longer needed with Select2, but keeping for backward compatibility
        // Select2 handles filtering internally
        console.log('filterEditItems called with:', searchTerm, 'but Select2 handles filtering automatically');
    }

    saveSlot() {
        const slotId = parseInt(document.getElementById('editModal').dataset.slotId);
        const itemKind = parseInt($('#editItemSelect').val()) || 0; // Use jQuery to get Select2 value
        const alzPrice = parseInt(document.getElementById('editAlzPrice').value) || 0;
        const dpPrice = parseInt(document.getElementById('editDpPrice').value) || 0;
        const forcegemPrice = parseInt(document.getElementById('editGemPrice').value) || 0;
        const itemOpt = parseInt(document.getElementById('editItemOpt').value) || 0;
        
        if (itemKind === 0) {
            this.shopEntries.delete(slotId);
        } else {
            const entry = this.shopEntries.get(slotId) || {
                index: slotId,
                poolId: this.currentPoolId || '1',
                tabId: 0,
                slotId: slotId,
                durationIdx: 0,
                minLevel: 0,
                maxLevel: 0,
                guildMinLevel: 0,
                reputation: -19,
                maxReputation: 20,
                onlyPremium: 0,
                onlyWin: 0,
                wExpPrice: 0,
                apPrice: 0,
                itemPrice: 0,
                forcegemPrice: 0,
                dLimit: 0,
                wLimit: 0
            };
            
            entry.itemKind = itemKind;
            entry.alzPrice = alzPrice;
            entry.dpPrice = dpPrice;
            entry.forcegemPrice = forcegemPrice;
            entry.itemOpt = itemOpt;
            
            this.shopEntries.set(slotId, entry);
        }
        
        this.isDirty = true;
        this.updateShopGrid();
        this.updateUI();
        this.hideModal('editModal');
    }

    clearSlot() {
        const slotId = parseInt(document.getElementById('editModal').dataset.slotId);
        this.shopEntries.delete(slotId);
        this.isDirty = true;
        this.updateShopGrid();
        this.updateUI();
        this.hideModal('editModal');
    }

    // Drag and Drop functionality
    handleSlotMouseDown(event, slotId) {
        if (event.button !== 0) return; // Only left mouse button
        const entry = this.shopEntries.get(slotId);
        if (!entry || entry.itemKind === 0) return;
        
        // The draggable attribute and dragstart event will handle the rest
    }

    handleDragStart(event, slotId) {
        const entry = this.shopEntries.get(slotId);
        if (!entry || entry.itemKind === 0) {
            event.preventDefault();
            return;
        }
        
        this.dragSource = slotId;
        event.target.classList.add('dragging');
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', slotId.toString());
    }

    handleDragEnd(event) {
        event.target.classList.remove('dragging');
        this.dragSource = null;
    }

    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    }

    handleDragEnter(event) {
        event.preventDefault();
        event.target.classList.add('drag-over');
    }

    handleDragLeave(event) {
        event.target.classList.remove('drag-over');
    }

    handleDrop(event, targetSlotId) {
        event.preventDefault();
        event.target.classList.remove('drag-over');
        
        const sourceSlotId = parseInt(event.dataTransfer.getData('text/plain'));
        
        if (sourceSlotId === targetSlotId) return;
        
        const sourceEntry = this.shopEntries.get(sourceSlotId);
        const targetEntry = this.shopEntries.get(targetSlotId);
        
        if (!sourceEntry) return;
        
        if (targetEntry && targetEntry.itemKind !== 0) {
            // Swap entries
            const tempEntry = { ...sourceEntry };
            
            // Copy target to source
            sourceEntry.itemKind = targetEntry.itemKind;
            sourceEntry.itemOpt = targetEntry.itemOpt;
            sourceEntry.alzPrice = targetEntry.alzPrice;
            sourceEntry.dpPrice = targetEntry.dpPrice;
            sourceEntry.forcegemPrice = targetEntry.forcegemPrice;
            // ... copy other relevant fields
            
            // Copy temp (original source) to target
            targetEntry.itemKind = tempEntry.itemKind;
            targetEntry.itemOpt = tempEntry.itemOpt;
            targetEntry.alzPrice = tempEntry.alzPrice;
            targetEntry.dpPrice = tempEntry.dpPrice;
            targetEntry.forcegemPrice = tempEntry.forcegemPrice;
            // ... copy other relevant fields
        } else {
            // Move source to target
            sourceEntry.slotId = targetSlotId;
            sourceEntry.index = targetSlotId;
            this.shopEntries.set(targetSlotId, sourceEntry);
            this.shopEntries.delete(sourceSlotId);
        }
        
        this.isDirty = true;
        this.updateShopGrid();
        this.updateUI();
    }

    showTooltip(event, slotId) {
        const entry = this.shopEntries.get(slotId);
        if (!entry || entry.itemKind === 0) return;
        
        const itemName = this.getItemName(entry.itemKind);
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.innerHTML = `Slot ${slotId}\nItemKind: ${entry.itemKind}\n${itemName || '(Unknown item)'}`;
        
        document.body.appendChild(tooltip);
        
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.right + 10 + 'px';
        tooltip.style.top = rect.top + 'px';
        
        event.target.tooltip = tooltip;
    }

    hideTooltip(event) {
        if (event.target.tooltip) {
            document.body.removeChild(event.target.tooltip);
            event.target.tooltip = null;
        }
    }

    getItemName(itemKind) {
        // Try direct lookup
        if (this.itemData.has(itemKind)) {
            return this.itemData.get(itemKind);
        }
        
        // Try fallbacks for character bind (itemKind - 524288)
        const charBind = itemKind - 524288;
        if (charBind > 0 && this.itemData.has(charBind)) {
            return this.itemData.get(charBind);
        }
        
        // Try fallbacks for account bind (itemKind - 4096)
        const accountBind = itemKind - 4096;
        if (accountBind > 0 && this.itemData.has(accountBind)) {
            return this.itemData.get(accountBind);
        }
        
        return '';
    }

    exportShop() {
        if (this.shopEntries.size === 0) {
            this.showNotification('No shop data to export', 'warning');
            return;
        }
        
        const lines = [];
        lines.push('[Shop]\tPool_ID\tTabID\tSlotID\tItemKind\tItemOpt\tDurationIdx\tMinLevel\tMaxLevel\tGuildMinLevel\tReputation\tMaxReputation\tOnlyPremium\tOnlyWin\tAlzPrice\tWExpPrice\tAPPrice\tDPPrice\tItemPrice\tForcegemPrice\tD_Limit\tW_Limit');
        
        Array.from(this.shopEntries.values())
            .sort((a, b) => a.slotId - b.slotId)
            .forEach(entry => {
                lines.push(this.entryToString(entry));
            });
        
        const content = lines.join('\n');
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'shop_export.scp';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.isDirty = false;
        this.updateUI();
        this.showNotification('Shop exported successfully!', 'success');
    }

    entryToString(entry) {
        return [
            entry.index,
            entry.poolId,
            entry.tabId,
            entry.slotId,
            entry.itemKind,
            entry.itemOpt,
            entry.durationIdx,
            entry.minLevel,
            entry.maxLevel,
            entry.guildMinLevel,
            entry.reputation,
            entry.maxReputation,
            entry.onlyPremium,
            entry.onlyWin,
            entry.alzPrice,
            entry.wExpPrice,
            entry.apPrice,
            entry.dpPrice,
            entry.itemPrice,
            entry.forcegemPrice,
            entry.dLimit,
            entry.wLimit
        ].join('\t');
    }

    updateUI() {
        document.getElementById('currentPoolId').textContent = 
            `Current Pool_ID: ${this.currentPoolId || '(none)'}`;
        
        document.getElementById('exportBtn').disabled = this.shopEntries.size === 0;
        
        // You could add a dirty indicator here if needed
        if (this.isDirty) {
            document.title = 'NPC Shop Editor *';
        } else {
            document.title = 'NPC Shop Editor';
        }
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.add('show');
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.remove('show');
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '15px 20px';
        notification.style.borderRadius = '5px';
        notification.style.color = 'white';
        notification.style.zIndex = '10000';
        notification.style.maxWidth = '300px';
        notification.style.animation = 'slideInFromRight 0.3s ease';
        
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#27ae60';
                break;
            case 'error':
                notification.style.backgroundColor = '#e74c3c';
                break;
            case 'warning':
                notification.style.backgroundColor = '#f39c12';
                break;
            default:
                notification.style.backgroundColor = '#3498db';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutToRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    async loadSampleData() {
        try {
            // Try to load the sample XML file for testing
            const response = await fetch('files/npc_shopeditor/import/sample-cabal_msg.xml');
            if (response.ok) {
                const xmlText = await response.text();
                this.parseItemData(xmlText);
                document.getElementById('loadedXmlStatus').textContent = 'Loaded: sample-cabal_msg.xml';
                this.updateItemSearch();
                console.log('Sample XML data loaded successfully');
            } else {
                console.log('Sample XML file not available, user will need to upload XML manually');
            }
        } catch (error) {
            console.log('Could not auto-load sample data:', error.message);
            // This is not a critical error, user can still upload XML manually
        }
    }

    setEditFormValues() {
        // Set form values after Select2 is initialized
        setTimeout(() => {
            const entry = this.currentEditEntry;
            const $select = $('#editItemSelect');
            
            if (entry && entry.itemKind && entry.itemKind !== 0) {
                console.log('Setting form values for existing entry:', entry);
                
                // For AJAX-based Select2, we need to create the option if it doesn't exist
                const currentItemName = this.itemData.get(entry.itemKind);
                if (currentItemName) {
                    // Check if the option already exists
                    if ($select.find(`option[value="${entry.itemKind}"]`).length === 0) {
                        // Create and add the option
                        const newOption = new Option(`${entry.itemKind} - ${currentItemName}`, entry.itemKind, true, true);
                        $select.append(newOption);
                        console.log('Added missing option for current item:', entry.itemKind, currentItemName);
                    }
                }
                
                // Set the value and trigger change
                $select.val(entry.itemKind).trigger('change');
                document.getElementById('editAlzPrice').value = entry.alzPrice || 0;
                document.getElementById('editDpPrice').value = entry.dpPrice || 0;
                document.getElementById('editGemPrice').value = entry.forcegemPrice || 0;
                document.getElementById('editItemOpt').value = entry.itemOpt || 0;
                
                console.log('Form values set successfully');
            } else {
                console.log('Setting form values for empty slot');
                $select.val('').trigger('change');
                document.getElementById('editAlzPrice').value = 0;
                document.getElementById('editDpPrice').value = 0;
                document.getElementById('editGemPrice').value = 0;
                document.getElementById('editItemOpt').value = 0;
            }
        }, 200); // Increased timeout to ensure Select2 is fully initialized
    }
}

// Animation styles for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInFromRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutToRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NPCShopEditor();
});
