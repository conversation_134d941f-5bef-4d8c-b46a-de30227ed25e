<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?> <small><?php echo PT_MANAGEPLAYERS_DESC; ?></small></h1></div>

<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=manager/clans" class="btn btn-block btn-info">Back to the Clans page</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 20;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM ".DATABASE_SV.".dbo.Guild WHERE GuildNo LIKE '$getResult%' OR GuildName LIKE '$getResult%'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Clan not found</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th>ClanID</th>
                            <th>ชื่อกิลล์</th>
                            <th>กิลล์เลเวล</th>
                            <th>กิลล์ พ้อย</th>
                            <th>วันที่สร้าง</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr>
                                        <td><?php echo $row[0]; ?></td>
                                        <td><?php echo $row[2]; ?></td>
                                        <td><?php echo $row[10]; ?></td>
                                        <td><?php echo $row[11]; ?></td>
                                        <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[4])); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                                    <?php echo B_ACTION; ?> <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" role="menu">
                                                    <li><a href="?url=manager/see-clan&id=<?php echo $row[0]; ?>"><?php echo B_SEEALLINFO; ?></a></li>
                                                    <li><a href="?url=manager/edit-clan&id=<?php echo $row[0]; ?>"><?php echo B_EDIT; ?></a></li>
                                                    <li><a href="?url=manager/see-clan&id=<?php echo $row[0]; ?>&delete=wait"><?php echo B_DELETE; ?></a></li>
                                                </ul>
                                            </div>
                                        </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/tickets&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>