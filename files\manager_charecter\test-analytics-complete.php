<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-analytics"></i> ทดสอบการวิเคราะห์ครบถ้วนใน Character Analytics
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">เพิ่มการวิเคราะห์ตัวละครครบถ้วนในหน้า Character Analytics</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>เพิ่มแล้ว:</strong> การวิเคราะห์ตัวละครครบถ้วน 6 หมวดหมู่ในหน้า character-analytics
                </div>
                
                <h5>📊 การวิเคราะห์ที่เพิ่มใน Character Analytics</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-line"></i> Level Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Level 1-50, 51-100, 101-150</li>
                                    <li>• Level 151-200, 200+</li>
                                    <li>• เลเวลเฉลี่ย, สูงสุด, ต่ำสุด</li>
                                    <li>• แสดงในรูปแบบการ์ด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-coins"></i> Wealth Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• ไม่มี Alz</li>
                                    <li>• 1M-10M Alz</li>
                                    <li>• 100M-1B Alz</li>
                                    <li>• 1B+ Alz (เศรษฐี)</li>
                                    <li>• Alz เฉลี่ย</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fal fa-clock"></i> Activity Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• ไม่เคยเล่น</li>
                                    <li>• เล่น 1-10 ชม.</li>
                                    <li>• เล่น 100-1000 ชม.</li>
                                    <li>• เล่น 1000+ ชม.</li>
                                    <li>• เวลาเล่นเฉลี่ย</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-crown"></i> Honor Class Distribution</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Honor Class 0-20</li>
                                    <li>• จำนวนและเปอร์เซ็นต์</li>
                                    <li>• Average Reputation Points</li>
                                    <li>• สีตาม Honor Class</li>
                                    <li>• คำนวณจาก Reputation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-dark">
                            <div class="card-header bg-dark text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-bar"></i> Progression Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Beginner (1-10)</li>
                                    <li>• Novice (11-50)</li>
                                    <li>• Intermediate (51-100)</li>
                                    <li>• Advanced (101-150)</li>
                                    <li>• Expert (151-200)</li>
                                    <li>• Master (200+)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fal fa-calendar"></i> Creation Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>• สร้างวันนี้</li>
                                    <li>• สร้างสัปดาห์นี้</li>
                                    <li>• สร้างเดือนนี้</li>
                                    <li>• สร้างปีนี้</li>
                                    <li>• ตัวละครเก่าสุด/ใหม่สุด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔍 ตัวอย่างข้อมูลจาก Database</h5>
                <?php
                try {
                    // ทดสอบ Level Analysis
                    $sql = "SELECT 
                                COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                                COUNT(CASE WHEN LEV BETWEEN 51 AND 100 THEN 1 END) as level_51_100,
                                COUNT(CASE WHEN LEV BETWEEN 101 AND 150 THEN 1 END) as level_101_150,
                                COUNT(CASE WHEN LEV BETWEEN 151 AND 200 THEN 1 END) as level_151_200,
                                COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                                AVG(CAST(LEV AS FLOAT)) as avg_level
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        echo "<div class='row'>";
                        echo "<div class='col-md-6'>";
                        echo "<h6>📈 Level Analysis Preview:</h6>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm table-striped'>";
                        echo "<tr><td>Level 1-50</td><td><span class='badge badge-success'>" . number_format($row['level_1_50']) . "</span></td></tr>";
                        echo "<tr><td>Level 51-100</td><td><span class='badge badge-info'>" . number_format($row['level_51_100']) . "</span></td></tr>";
                        echo "<tr><td>Level 101-150</td><td><span class='badge badge-warning'>" . number_format($row['level_101_150']) . "</span></td></tr>";
                        echo "<tr><td>Level 151-200</td><td><span class='badge badge-danger'>" . number_format($row['level_151_200']) . "</span></td></tr>";
                        echo "<tr><td>Level 200+</td><td><span class='badge badge-dark'>" . number_format($row['level_200_plus']) . "</span></td></tr>";
                        echo "<tr><td><strong>เลเวลเฉลี่ย</strong></td><td><strong>" . number_format($row['avg_level'], 1) . "</strong></td></tr>";
                        echo "</table></div></div>";
                        
                        // ทดสอบ Honor Class Distribution
                        $sql = "SELECT 
                                    CASE 
                                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                                        ELSE 'Other Classes'
                                    END as honor_class,
                                    COUNT(*) as count
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                GROUP BY 
                                    CASE 
                                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                                        ELSE 'Other Classes'
                                    END
                                ORDER BY count DESC";
                        
                        $result2 = sqlsrv_query($conn, $sql);
                        
                        if ($result2) {
                            echo "<div class='col-md-6'>";
                            echo "<h6>👑 Honor Class Preview:</h6>";
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm table-striped'>";
                            echo "<thead><tr><th>Honor Class</th><th>จำนวน</th></tr></thead>";
                            echo "<tbody>";
                            
                            while ($row2 = sqlsrv_fetch_array($result2, SQLSRV_FETCH_ASSOC)) {
                                $honorClass = $row2['honor_class'];
                                $count = $row2['count'];
                                
                                // กำหนดสี
                                $colorClass = 'secondary';
                                if (strpos($honorClass, 'Class') !== false) {
                                    $classNum = (int)str_replace('Class ', '', $honorClass);
                                    if ($classNum == 0) $colorClass = 'secondary';
                                    elseif ($classNum >= 1 && $classNum <= 5) $colorClass = 'success';
                                    elseif ($classNum >= 6 && $classNum <= 10) $colorClass = 'info';
                                    elseif ($classNum >= 11 && $classNum <= 15) $colorClass = 'warning';
                                    elseif ($classNum >= 16 && $classNum <= 19) $colorClass = 'danger';
                                    elseif ($classNum >= 20) $colorClass = 'dark';
                                }
                                
                                echo "<tr>";
                                echo "<td><span class='badge badge-{$colorClass}'>" . htmlspecialchars($honorClass) . "</span></td>";
                                echo "<td>" . number_format($count) . "</td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table></div></div>";
                        }
                        
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบการแสดงผล</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterAnalytics()">
                        <i class="fal fa-analytics"></i> เปิดหน้า Character Analytics
                    </button>
                    <button class="btn btn-info" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้า Character Statistics
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการเพิ่มฟีเจอร์</h5>
                <div class="accordion" id="featuresAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. SQL Queries ที่เพิ่ม
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>SQL Queries ใหม่:</strong></p>
                                <ul>
                                    <li><strong>Level Analysis:</strong> COUNT CASE WHEN สำหรับแต่ละช่วงเลเวล</li>
                                    <li><strong>Wealth Analysis:</strong> COUNT CASE WHEN สำหรับแต่ละช่วง Alz</li>
                                    <li><strong>Activity Analysis:</strong> COUNT CASE WHEN สำหรับแต่ละช่วง PlayTime</li>
                                    <li><strong>Honor Class:</strong> CASE WHEN คำนวณจาก Reputation Points</li>
                                    <li><strong>Progression:</strong> COUNT CASE WHEN สำหรับแต่ละระดับ</li>
                                    <li><strong>Creation:</strong> DATEDIFF สำหรับแต่ละช่วงเวลา</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. การแสดงผลที่เพิ่ม
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>UI Components ใหม่:</strong></p>
                                <ul>
                                    <li><strong>Level Analysis:</strong> การ์ดแสดงจำนวนแต่ละช่วงเลเวล</li>
                                    <li><strong>Wealth Analysis:</strong> ตารางแสดงการกระจาย Alz</li>
                                    <li><strong>Activity Analysis:</strong> ตารางแสดงการกระจาย PlayTime</li>
                                    <li><strong>Honor Class:</strong> ตารางพร้อมสีและเปอร์เซ็นต์</li>
                                    <li><strong>Progression:</strong> การ์ดแสดงระดับความก้าวหน้า</li>
                                    <li><strong>Creation:</strong> ตารางแสดงการสร้างตัวละคร</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. ความแตกต่างจาก Character Statistics
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>Character Analytics vs Character Statistics:</strong></p>
                                <ul>
                                    <li><strong>Analytics:</strong> เน้นการวิเคราะห์เชิงลึก + กราฟ + ช่วงเวลา</li>
                                    <li><strong>Statistics:</strong> เน้นข้อมูลสถิติครบถ้วน + Top Players</li>
                                    <li><strong>Analytics:</strong> มีการเลือกช่วงเวลา (7/30/90 วัน)</li>
                                    <li><strong>Statistics:</strong> แสดงข้อมูลทั้งหมด</li>
                                    <li><strong>Analytics:</strong> มี Charts และ Graphs</li>
                                    <li><strong>Statistics:</strong> มี Honor Titles และ Nation analysis</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการเพิ่มการวิเคราะห์:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ เพิ่ม 6 หมวดการวิเคราะห์</li>
                                <li>✅ SQL Queries ครบถ้วน</li>
                                <li>✅ การแสดงผลที่สวยงาม</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Honor Class จาก Reputation</li>
                                <li>✅ สีและ Badge ที่เหมาะสม</li>
                                <li>✅ ข้อมูลครบถ้วนและแม่นยำ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterAnalytics() {
    window.open('?url=manager_charecter/character-analytics', '_blank');
}

function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function refreshPage() {
    location.reload();
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
