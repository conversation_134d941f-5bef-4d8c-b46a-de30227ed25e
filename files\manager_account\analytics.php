<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.panel-featured-primary {
    border-left-color: #0088cc;
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Labels and Badges */
.label {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.label-success {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.label-warning {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.label-danger {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.label-info {
    background: linear-gradient(135deg, #5bc0de, #31b0d5);
}

.label-default {
    background: linear-gradient(135deg, #777, #555);
}

/* Advanced Analytics Styles */
.analytics-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    margin-bottom: 25px;
}

.analytics-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-item label {
    font-weight: 600;
    margin: 0;
}

.prediction-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    text-align: center;
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.prediction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.prediction-card h4 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.prediction-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 15px 0;
}

.prediction-card small {
    font-size: 12px;
    opacity: 0.9;
    display: block;
    margin-bottom: 10px;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.trend-up {
    background: rgba(92, 184, 92, 0.2);
    color: #5cb85c;
}

.trend-down {
    background: rgba(217, 83, 79, 0.2);
    color: #d9534f;
}

.trend-stable {
    background: rgba(240, 173, 78, 0.2);
    color: #f0ad4e;
}

.behavior-heatmap {
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.real-time-indicator {
    position: relative;
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #5cb85c;
    border-radius: 50%;
    animation: pulse 2s infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }

    .analytics-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-item {
        justify-content: space-between;
    }
}
</style>

<header class="page-header">
    <h2>วิเคราะห์ข้อมูลขั้นสูง (Advanced Analytics) <span class="real-time-indicator" title="อัปเดตแบบ Real-time"></span></h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Advanced Analytics</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Admin analytics - overview of all accounts
$timeRange = isset($_GET['range']) ? $_GET['range'] : '30';
$analysisType = isset($_GET['analysis']) ? $_GET['analysis'] : 'overview';
$predictiveMode = isset($_GET['predictive']) ? $_GET['predictive'] : 'off';

// Analysis types
$analysisTypes = [
    'overview' => 'ภาพรวม',
    'behavior' => 'พฤติกรรมผู้เล่น',
    'financial' => 'การเงิน',
    'retention' => 'การกลับมาเล่น',
    'predictive' => 'การทำนาย'
];

// Total accounts statistics
$selectTotalAccounts = "SELECT
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_accounts,
    COUNT(CASE WHEN AuthType = 2 THEN 1 END) as banned_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -1, GETDATE()) THEN 1 END) as new_today,
    COUNT(CASE WHEN createDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as new_week,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -1, GETDATE()) THEN 1 END) as active_today,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -7, GETDATE()) THEN 1 END) as active_week,
    AVG(CAST(PlayTime as FLOAT)) as avg_playtime,
    SUM(CAST(PlayTime as BIGINT)) as total_playtime
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$totalAccountsQuery = sqlsrv_query($conn, $selectTotalAccounts);
if ($totalAccountsQuery === false) {
    // Use default values if query fails
    $totalStats = array(
        'total_accounts' => 0,
        'online_accounts' => 0,
        'banned_accounts' => 0,
        'new_today' => 0,
        'new_week' => 0,
        'active_today' => 0,
        'active_week' => 0,
        'avg_playtime' => 0,
        'total_playtime' => 0
    );
} else {
    $totalStats = sqlsrv_fetch_array($totalAccountsQuery, SQLSRV_FETCH_ASSOC);
}

// Financial overview
$selectFinancialOverview = "SELECT
    SUM(CAST(Cash as BIGINT)) as total_cash,
    SUM(CAST(CashBonus as BIGINT)) as total_cash_bonus,
    SUM(CAST(CashTotal as BIGINT)) as total_cash_all,
    SUM(CAST(Rpoint as BIGINT)) as total_rewards,
    AVG(CAST(Cash as FLOAT)) as avg_cash,
    COUNT(CASE WHEN Cash > 0 THEN 1 END) as accounts_with_cash
    FROM [".DATABASE_CCA."].[dbo].CashAccount";
$financialQuery = sqlsrv_query($conn, $selectFinancialOverview);
if ($financialQuery === false) {
    // Use default values if query fails
    $financialStats = array(
        'total_cash' => 0,
        'total_cash_bonus' => 0,
        'total_cash_all' => 0,
        'total_rewards' => 0,
        'avg_cash' => 0,
        'accounts_with_cash' => 0
    );
} else {
    $financialStats = sqlsrv_fetch_array($financialQuery, SQLSRV_FETCH_ASSOC);
}

// Character statistics
$selectCharacterStats = "SELECT
    COUNT(*) as total_characters,
    COUNT(DISTINCT CharacterIdx/8) as accounts_with_chars,
    AVG(CAST(LEV as FLOAT)) as avg_level,
    MAX(LEV) as max_level,
    COUNT(CASE WHEN LEV >= 100 THEN 1 END) as high_level_chars,
    COUNT(CASE WHEN Nation = 1 THEN 1 END) as capella_chars,
    COUNT(CASE WHEN Nation = 2 THEN 1 END) as procyon_chars
    FROM [".DATABASE_SV."].[dbo].cabal_character_table
    WHERE LEV > 0";
$characterQuery = sqlsrv_query($conn, $selectCharacterStats);
if ($characterQuery === false) {
    // Use default values if query fails
    $characterStats = array(
        'total_characters' => 0,
        'accounts_with_chars' => 0,
        'avg_level' => 0,
        'max_level' => 0,
        'high_level_chars' => 0,
        'capella_chars' => 0,
        'procyon_chars' => 0
    );
} else {
    $characterStats = sqlsrv_fetch_array($characterQuery, SQLSRV_FETCH_ASSOC);
}

// Login activity by hour (last 7 days)
$selectHourlyActivity = "SELECT
    DATEPART(hour, LoginTime) as hour_of_day,
    COUNT(*) as login_count
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table
    WHERE LoginTime >= DATEADD(day, -7, GETDATE())
    GROUP BY DATEPART(hour, LoginTime)
    ORDER BY hour_of_day";
$hourlyActivityQuery = sqlsrv_query($conn, $selectHourlyActivity);
$hourlyActivity = array();
if ($hourlyActivityQuery === false) {
    // Use default values if query fails
    for ($i = 0; $i < 24; $i++) {
        $hourlyActivity[$i] = 0;
    }
} else {
    while ($row = sqlsrv_fetch_array($hourlyActivityQuery, SQLSRV_FETCH_ASSOC)) {
        $hourlyActivity[$row['hour_of_day']] = $row['login_count'];
    }
}

// Daily registrations (last 30 days)
$selectDailyRegistrations = "SELECT 
    CAST(createDate as DATE) as reg_date,
    COUNT(*) as new_accounts
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE createDate >= DATEADD(day, -30, GETDATE())
    GROUP BY CAST(createDate as DATE)
    ORDER BY reg_date DESC";
$dailyRegQuery = sqlsrv_query($conn, $selectDailyRegistrations);
$dailyRegistrations = array();
if ($dailyRegQuery !== false) {
    while ($row = sqlsrv_fetch_array($dailyRegQuery, SQLSRV_FETCH_ASSOC)) {
        $dailyRegistrations[] = $row;
    }
}

// Top active players
$selectTopPlayers = "SELECT TOP 10 
    ID, 
    PlayTime,
    LoginTime,
    CASE WHEN Login = 1 THEN 'ออนไลน์' ELSE 'ออฟไลน์' END as status
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE PlayTime > 0
    ORDER BY PlayTime DESC";
$topPlayersQuery = sqlsrv_query($conn, $selectTopPlayers);
$topPlayers = array();
if ($topPlayersQuery !== false) {
    while ($row = sqlsrv_fetch_array($topPlayersQuery, SQLSRV_FETCH_ASSOC)) {
        $topPlayers[] = $row;
    }
}

// Security alerts
$selectSecurityAlerts = "SELECT 
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -1, GETDATE()) 
          AND UserNum IN (
              SELECT UserNum FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
              WHERE LoginTime >= DATEADD(hour, -1, GETDATE())
              GROUP BY UserNum, LastIp 
              HAVING COUNT(DISTINCT LastIp) > 1
          ) THEN 1 END) as suspicious_logins,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 0 AND 5 
          AND LoginTime >= DATEADD(day, -1, GETDATE()) THEN 1 END) as night_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$securityQuery = sqlsrv_query($conn, $selectSecurityAlerts);
if ($securityQuery === false) {
    // Use default values if query fails
    $securityStats = array(
        'suspicious_logins' => 0,
        'night_logins' => 0
    );
} else {
    $securityStats = sqlsrv_fetch_array($securityQuery, SQLSRV_FETCH_ASSOC);
}

// Advanced Analytics Queries
// Player Retention Analysis
$selectRetentionStats = "SELECT
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -1, GETDATE()) THEN 1 END) as daily_retention,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -7, GETDATE()) THEN 1 END) as weekly_retention,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -30, GETDATE()) THEN 1 END) as monthly_retention,
    COUNT(CASE WHEN createDate <= DATEADD(day, -30, GETDATE())
          AND LoginTime >= DATEADD(day, -7, GETDATE()) THEN 1 END) as veteran_active
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$retentionQuery = sqlsrv_query($conn, $selectRetentionStats);
if ($retentionQuery === false) {
    $retentionStats = array(
        'daily_retention' => 0,
        'weekly_retention' => 0,
        'monthly_retention' => 0,
        'veteran_active' => 0
    );
} else {
    $retentionStats = sqlsrv_fetch_array($retentionQuery, SQLSRV_FETCH_ASSOC);
}

// Predictive Analytics (Simple trend calculation)
$selectTrendData = "SELECT
    DATEPART(week, LoginTime) as week_num,
    COUNT(DISTINCT UserNum) as unique_players,
    COUNT(*) as total_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table
    WHERE LoginTime >= DATEADD(week, -8, GETDATE())
    GROUP BY DATEPART(week, LoginTime)
    ORDER BY week_num";
$trendQuery = sqlsrv_query($conn, $selectTrendData);
$trendData = array();
if ($trendQuery !== false) {
    while ($row = sqlsrv_fetch_array($trendQuery, SQLSRV_FETCH_ASSOC)) {
        $trendData[] = $row;
    }
}

// Calculate trend direction
$trendDirection = 'stable';
$trendPercentage = 0;
if (count($trendData) >= 2) {
    $recent = end($trendData);
    $previous = prev($trendData);
    if ($previous && $previous['unique_players'] > 0) {
        $trendPercentage = (($recent['unique_players'] - $previous['unique_players']) / $previous['unique_players']) * 100;
        if ($trendPercentage > 5) {
            $trendDirection = 'up';
        } elseif ($trendPercentage < -5) {
            $trendDirection = 'down';
        }
    }
}

// Behavior Analysis
$selectBehaviorStats = "SELECT
    AVG(DATEDIFF(minute, LoginTime, ISNULL(LogoutTime, GETDATE()))) as avg_session_duration,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 6 AND 12 THEN 1 END) as morning_players,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 12 AND 18 THEN 1 END) as afternoon_players,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 18 AND 24 THEN 1 END) as evening_players,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 0 AND 6 THEN 1 END) as night_players
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table
    WHERE LoginTime >= DATEADD(day, -7, GETDATE())";
$behaviorQuery = sqlsrv_query($conn, $selectBehaviorStats);
if ($behaviorQuery === false) {
    $behaviorStats = array(
        'avg_session_duration' => 0,
        'morning_players' => 0,
        'afternoon_players' => 0,
        'evening_players' => 0,
        'night_players' => 0
    );
} else {
    $behaviorStats = sqlsrv_fetch_array($behaviorQuery, SQLSRV_FETCH_ASSOC);
}
?>

<div class="row">
    <!-- Advanced Analytics Dashboard -->
    <div class="col-md-12">
        <div class="analytics-dashboard">
            <h3><i class="fa fa-chart-line"></i> Advanced Analytics Dashboard</h3>
            <div class="analytics-controls">
                <div class="control-item">
                    <label>ช่วงเวลา:</label>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="?url=manager_account/analytics&range=7&analysis=<?php echo $analysisType; ?>&predictive=<?php echo $predictiveMode; ?>"
                           class="btn <?php echo $timeRange == '7' ? 'btn-light' : 'btn-outline-light'; ?>">
                            7 วัน
                        </a>
                        <a href="?url=manager_account/analytics&range=30&analysis=<?php echo $analysisType; ?>&predictive=<?php echo $predictiveMode; ?>"
                           class="btn <?php echo $timeRange == '30' ? 'btn-light' : 'btn-outline-light'; ?>">
                            30 วัน
                        </a>
                        <a href="?url=manager_account/analytics&range=90&analysis=<?php echo $analysisType; ?>&predictive=<?php echo $predictiveMode; ?>"
                           class="btn <?php echo $timeRange == '90' ? 'btn-light' : 'btn-outline-light'; ?>">
                            90 วัน
                        </a>
                    </div>
                </div>

                <div class="control-item">
                    <label>ประเภทการวิเคราะห์:</label>
                    <select class="form-control form-control-sm" onchange="changeAnalysis(this.value)" style="width: auto;">
                        <?php foreach ($analysisTypes as $type => $label): ?>
                            <option value="<?php echo $type; ?>" <?php echo $analysisType == $type ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="control-item">
                    <label>โหมดทำนาย:</label>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="?url=manager_account/analytics&range=<?php echo $timeRange; ?>&analysis=<?php echo $analysisType; ?>&predictive=off"
                           class="btn <?php echo $predictiveMode == 'off' ? 'btn-light' : 'btn-outline-light'; ?>">
                            ปิด
                        </a>
                        <a href="?url=manager_account/analytics&range=<?php echo $timeRange; ?>&analysis=<?php echo $analysisType; ?>&predictive=on"
                           class="btn <?php echo $predictiveMode == 'on' ? 'btn-light' : 'btn-outline-light'; ?>">
                            เปิด
                        </a>
                    </div>
                </div>

                <div class="control-item">
                    <button class="btn btn-light btn-sm" onclick="refreshAnalytics()">
                        <i class="fa fa-refresh"></i> รีเฟรช
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($predictiveMode == 'on'): ?>
<div class="row">
    <!-- Predictive Analytics Cards -->
    <div class="col-xl-3 col-lg-6">
        <div class="prediction-card">
            <h4><i class="fa fa-crystal-ball"></i> การทำนายผู้เล่น</h4>
            <div class="prediction-value">
                <?php
                $predictedPlayers = $totalStats['active_week'] * (1 + ($trendPercentage / 100));
                echo number_format($predictedPlayers, 0);
                ?>
            </div>
            <small>ผู้เล่นที่คาดว่าจะมีสัปดาห์หน้า</small>
            <div class="trend-indicator trend-<?php echo $trendDirection; ?>">
                <i class="fa fa-arrow-<?php echo $trendDirection == 'up' ? 'up' : ($trendDirection == 'down' ? 'down' : 'right'); ?>"></i>
                <?php echo abs(round($trendPercentage, 1)); ?>%
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6">
        <div class="prediction-card">
            <h4><i class="fa fa-money"></i> การทำนายรายได้</h4>
            <div class="prediction-value">
                <?php
                $predictedRevenue = $financialStats['total_cash'] * 1.05; // Simple 5% growth prediction
                echo number_format($predictedRevenue, 0);
                ?>
            </div>
            <small>Cash ที่คาดว่าจะมีเดือนหน้า</small>
            <div class="trend-indicator trend-up">
                <i class="fa fa-arrow-up"></i> 5.0%
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6">
        <div class="prediction-card">
            <h4><i class="fa fa-users"></i> Retention Rate</h4>
            <div class="prediction-value">
                <?php
                $retentionRate = $totalStats['total_accounts'] > 0 ?
                    ($retentionStats['weekly_retention'] / $totalStats['total_accounts']) * 100 : 0;
                echo round($retentionRate, 1);
                ?>%
            </div>
            <small>อัตราการกลับมาเล่น (7 วัน)</small>
            <div class="trend-indicator trend-<?php echo $retentionRate > 30 ? 'up' : ($retentionRate > 15 ? 'stable' : 'down'); ?>">
                <i class="fa fa-heart"></i>
                <?php echo $retentionRate > 30 ? 'ดี' : ($retentionRate > 15 ? 'ปานกลาง' : 'ต้องปรับปรุง'); ?>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6">
        <div class="prediction-card">
            <h4><i class="fa fa-clock-o"></i> Session Duration</h4>
            <div class="prediction-value">
                <?php echo round($behaviorStats['avg_session_duration'], 0); ?>
            </div>
            <small>นาทีเฉลี่ยต่อเซสชัน</small>
            <div class="trend-indicator trend-<?php echo $behaviorStats['avg_session_duration'] > 60 ? 'up' : 'stable'; ?>">
                <i class="fa fa-clock-o"></i>
                <?php echo $behaviorStats['avg_session_duration'] > 60 ? 'ยาว' : 'ปกติ'; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Account Overview -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($totalStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $totalStats['online_accounts']; ?> ออนไลน์</span>
                            <span class="text-danger"><?php echo $totalStats['banned_accounts']; ?> ถูกแบน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['new_today']; ?></strong>
                                <span class="text-secondary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['new_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-gamepad"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['active_today']; ?></strong>
                                <span class="text-tertiary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['active_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">แจ้งเตือนความปลอดภัย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $securityStats['suspicious_logins']; ?></strong>
                                <span class="text-quaternary">รายการ</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-warning"><?php echo $securityStats['night_logins']; ?> เข้าสู่ระบบกลางคืน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Hourly Activity Chart -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">กิจกรรมการเข้าสู่ระบบตามชั่วโมง (7 วันล่าสุด)</h2>
            </header>
            <div class="panel-body">
                <canvas id="hourlyActivityChart" height="100"></canvas>
            </div>
        </section>
    </div>

    <!-- Financial Overview -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ภาพรวมทางการเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_cash_all']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash รวมทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['accounts_with_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">บัญชีที่มี Cash</p>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_rewards']); ?></span>
                            <p class="text-xs text-muted mb-none">Reward Points รวม</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['avg_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash เฉลี่ย</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Behavior Analysis -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การวิเคราะห์พฤติกรรมผู้เล่น</h2>
            </header>
            <div class="panel-body">
                <div class="behavior-heatmap">
                    <canvas id="behaviorChart" height="150"></canvas>
                </div>
                <div class="mt-lg">
                    <div class="row text-center">
                        <div class="col-xs-3">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($behaviorStats['morning_players']); ?></span>
                            <p class="text-xs text-muted mb-none">เช้า (6-12)</p>
                        </div>
                        <div class="col-xs-3">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($behaviorStats['afternoon_players']); ?></span>
                            <p class="text-xs text-muted mb-none">บ่าย (12-18)</p>
                        </div>
                        <div class="col-xs-3">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($behaviorStats['evening_players']); ?></span>
                            <p class="text-xs text-muted mb-none">เย็น (18-24)</p>
                        </div>
                        <div class="col-xs-3">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($behaviorStats['night_players']); ?></span>
                            <p class="text-xs text-muted mb-none">กลางคืน (0-6)</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Character Statistics -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติตัวละคร</h2>
            </header>
            <div class="panel-body">
                <canvas id="characterChart" height="150"></canvas>
                <div class="mt-lg">
                    <div class="row text-center">
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($characterStats['total_characters']); ?></span>
                            <p class="text-xs text-muted mb-none">ตัวละครทั้งหมด</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo round($characterStats['avg_level'], 1); ?></span>
                            <p class="text-xs text-muted mb-none">Level เฉลี่ย</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo $characterStats['max_level']; ?></span>
                            <p class="text-xs text-muted mb-none">Level สูงสุด</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Top Active Players -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ผู้เล่นที่ใช้งานมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ผู้เล่น</th>
                                <th>เวลาเล่น (ชั่วโมง)</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topPlayers as $player): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($player['ID']); ?></td>
                                    <td><?php echo round($player['PlayTime'] / 60, 1); ?></td>
                                    <td>
                                        <span class="label label-<?php echo $player['status'] == 'ออนไลน์' ? 'success' : 'default'; ?>">
                                            <?php echo $player['status']; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Hourly Activity Chart
    var hourlyCtx = document.getElementById('hourlyActivityChart').getContext('2d');
    var hourlyData = {
        labels: [
            '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
            '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
            '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
            '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
        ],
        datasets: [{
            label: 'การเข้าสู่ระบบ',
            data: [
                <?php 
                $hourlyValues = array();
                for ($i = 0; $i < 24; $i++) {
                    $hourlyValues[] = isset($hourlyActivity[$i]) ? $hourlyActivity[$i] : 0;
                }
                echo implode(',', $hourlyValues);
                ?>
            ],
            borderColor: '#0088cc',
            backgroundColor: 'rgba(0, 136, 204, 0.1)',
            borderWidth: 2,
            fill: true
        }]
    };
    
    new Chart(hourlyCtx, {
        type: 'line',
        data: hourlyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Character Distribution Chart
    var characterCtx = document.getElementById('characterChart').getContext('2d');
    var characterData = {
        labels: ['Capella', 'Procyon', 'High Level (100+)'],
        datasets: [{
            data: [
                <?php echo $characterStats['capella_chars']; ?>, 
                <?php echo $characterStats['procyon_chars']; ?>,
                <?php echo $characterStats['high_level_chars']; ?>
            ],
            backgroundColor: ['#0088cc', '#d9534f', '#5cb85c'],
            borderWidth: 0
        }]
    };
    
    new Chart(characterCtx, {
        type: 'doughnut',
        data: characterData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Behavior Analysis Chart
    var behaviorCtx = document.getElementById('behaviorChart').getContext('2d');
    var behaviorData = {
        labels: ['เช้า (6-12)', 'บ่าย (12-18)', 'เย็น (18-24)', 'กลางคืน (0-6)'],
        datasets: [{
            label: 'จำนวนผู้เล่น',
            data: [
                <?php echo $behaviorStats['morning_players']; ?>,
                <?php echo $behaviorStats['afternoon_players']; ?>,
                <?php echo $behaviorStats['evening_players']; ?>,
                <?php echo $behaviorStats['night_players']; ?>
            ],
            backgroundColor: [
                'rgba(255, 206, 84, 0.8)',   // Morning - Yellow
                'rgba(54, 162, 235, 0.8)',   // Afternoon - Blue
                'rgba(255, 99, 132, 0.8)',   // Evening - Red
                'rgba(75, 192, 192, 0.8)'    // Night - Teal
            ],
            borderColor: [
                'rgba(255, 206, 84, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(75, 192, 192, 1)'
            ],
            borderWidth: 2
        }]
    };

    new Chart(behaviorCtx, {
        type: 'radar',
        data: behaviorData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});

// Advanced Analytics Functions
function changeAnalysis(type) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('analysis', type);
    window.location.href = currentUrl.toString();
}

function refreshAnalytics() {
    // Add loading indicator
    const indicator = document.querySelector('.real-time-indicator');
    if (indicator) {
        indicator.style.background = '#f0ad4e';
        indicator.style.animation = 'pulse 0.5s infinite';
    }

    // Show notification
    showNotification('กำลังรีเฟรชข้อมูล...', 'info');

    // Simulate refresh (in real implementation, this would be an AJAX call)
    setTimeout(function() {
        if (indicator) {
            indicator.style.background = '#5cb85c';
            indicator.style.animation = 'pulse 2s infinite';
        }
        showNotification('ข้อมูลได้รับการอัปเดตแล้ว', 'success');

        // Refresh the page
        window.location.reload();
    }, 2000);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Auto-refresh for real-time data
setInterval(function() {
    // Update prediction cards if in predictive mode
    <?php if ($predictiveMode == 'on'): ?>
    updatePredictions();
    <?php endif; ?>
}, 60000); // Update every minute

function updatePredictions() {
    // In a real implementation, this would fetch new data via AJAX
    // For now, just animate the indicators
    const indicators = document.querySelectorAll('.real-time-indicator');
    indicators.forEach(indicator => {
        indicator.style.background = '#f0ad4e';
        setTimeout(() => {
            indicator.style.background = '#5cb85c';
        }, 500);
    });
}
</script>
