<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.panel-featured-primary {
    border-left-color: #0088cc;
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Labels and Badges */
.label {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.label-success {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.label-warning {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.label-danger {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.label-info {
    background: linear-gradient(135deg, #5bc0de, #31b0d5);
}

.label-default {
    background: linear-gradient(135deg, #777, #555);
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }
}
</style>

<header class="page-header">
    <h2>วิเคราะห์ข้อมูลบัญชีผู้เล่น (Admin)</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Analytics</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Admin analytics - overview of all accounts
$timeRange = isset($_GET['range']) ? $_GET['range'] : '30';

// Total accounts statistics
$selectTotalAccounts = "SELECT
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_accounts,
    COUNT(CASE WHEN AuthType = 2 THEN 1 END) as banned_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -1, GETDATE()) THEN 1 END) as new_today,
    COUNT(CASE WHEN createDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as new_week,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -1, GETDATE()) THEN 1 END) as active_today,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -7, GETDATE()) THEN 1 END) as active_week,
    AVG(CAST(PlayTime as FLOAT)) as avg_playtime,
    SUM(CAST(PlayTime as BIGINT)) as total_playtime
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$totalAccountsQuery = sqlsrv_query($conn, $selectTotalAccounts);
if ($totalAccountsQuery === false) {
    // Use default values if query fails
    $totalStats = array(
        'total_accounts' => 0,
        'online_accounts' => 0,
        'banned_accounts' => 0,
        'new_today' => 0,
        'new_week' => 0,
        'active_today' => 0,
        'active_week' => 0,
        'avg_playtime' => 0,
        'total_playtime' => 0
    );
} else {
    $totalStats = sqlsrv_fetch_array($totalAccountsQuery, SQLSRV_FETCH_ASSOC);
}

// Financial overview
$selectFinancialOverview = "SELECT
    SUM(CAST(Cash as BIGINT)) as total_cash,
    SUM(CAST(CashBonus as BIGINT)) as total_cash_bonus,
    SUM(CAST(CashTotal as BIGINT)) as total_cash_all,
    SUM(CAST(Reward as BIGINT)) as total_rewards,
    AVG(CAST(Cash as FLOAT)) as avg_cash,
    COUNT(CASE WHEN Cash > 0 THEN 1 END) as accounts_with_cash
    FROM [".DATABASE_CCA."].[dbo].cabal_cash_table";
$financialQuery = sqlsrv_query($conn, $selectFinancialOverview);
if ($financialQuery === false) {
    // Use default values if query fails
    $financialStats = array(
        'total_cash' => 0,
        'total_cash_bonus' => 0,
        'total_cash_all' => 0,
        'total_rewards' => 0,
        'avg_cash' => 0,
        'accounts_with_cash' => 0
    );
} else {
    $financialStats = sqlsrv_fetch_array($financialQuery, SQLSRV_FETCH_ASSOC);
}

// Character statistics
$selectCharacterStats = "SELECT
    COUNT(*) as total_characters,
    COUNT(DISTINCT CharacterIdx/8) as accounts_with_chars,
    AVG(CAST(LEV as FLOAT)) as avg_level,
    MAX(LEV) as max_level,
    COUNT(CASE WHEN LEV >= 100 THEN 1 END) as high_level_chars,
    COUNT(CASE WHEN Nation = 1 THEN 1 END) as capella_chars,
    COUNT(CASE WHEN Nation = 2 THEN 1 END) as procyon_chars
    FROM [".DATABASE_SV."].[dbo].cabal_character_table
    WHERE LEV > 0";
$characterQuery = sqlsrv_query($conn, $selectCharacterStats);
if ($characterQuery === false) {
    // Use default values if query fails
    $characterStats = array(
        'total_characters' => 0,
        'accounts_with_chars' => 0,
        'avg_level' => 0,
        'max_level' => 0,
        'high_level_chars' => 0,
        'capella_chars' => 0,
        'procyon_chars' => 0
    );
} else {
    $characterStats = sqlsrv_fetch_array($characterQuery, SQLSRV_FETCH_ASSOC);
}

// Login activity by hour (last 7 days)
$selectHourlyActivity = "SELECT
    DATEPART(hour, LoginTime) as hour_of_day,
    COUNT(*) as login_count
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table
    WHERE LoginTime >= DATEADD(day, -7, GETDATE())
    GROUP BY DATEPART(hour, LoginTime)
    ORDER BY hour_of_day";
$hourlyActivityQuery = sqlsrv_query($conn, $selectHourlyActivity);
$hourlyActivity = array();
if ($hourlyActivityQuery === false) {
    // Use default values if query fails
    for ($i = 0; $i < 24; $i++) {
        $hourlyActivity[$i] = 0;
    }
} else {
    while ($row = sqlsrv_fetch_array($hourlyActivityQuery, SQLSRV_FETCH_ASSOC)) {
        $hourlyActivity[$row['hour_of_day']] = $row['login_count'];
    }
}

// Daily registrations (last 30 days)
$selectDailyRegistrations = "SELECT 
    CAST(createDate as DATE) as reg_date,
    COUNT(*) as new_accounts
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE createDate >= DATEADD(day, -30, GETDATE())
    GROUP BY CAST(createDate as DATE)
    ORDER BY reg_date DESC";
$dailyRegQuery = sqlsrv_query($conn, $selectDailyRegistrations);
$dailyRegistrations = array();
if ($dailyRegQuery !== false) {
    while ($row = sqlsrv_fetch_array($dailyRegQuery, SQLSRV_FETCH_ASSOC)) {
        $dailyRegistrations[] = $row;
    }
}

// Top active players
$selectTopPlayers = "SELECT TOP 10 
    ID, 
    PlayTime,
    LoginTime,
    CASE WHEN Login = 1 THEN 'ออนไลน์' ELSE 'ออฟไลน์' END as status
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE PlayTime > 0
    ORDER BY PlayTime DESC";
$topPlayersQuery = sqlsrv_query($conn, $selectTopPlayers);
$topPlayers = array();
if ($topPlayersQuery !== false) {
    while ($row = sqlsrv_fetch_array($topPlayersQuery, SQLSRV_FETCH_ASSOC)) {
        $topPlayers[] = $row;
    }
}

// Security alerts
$selectSecurityAlerts = "SELECT 
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -1, GETDATE()) 
          AND UserNum IN (
              SELECT UserNum FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
              WHERE LoginTime >= DATEADD(hour, -1, GETDATE())
              GROUP BY UserNum, LastIp 
              HAVING COUNT(DISTINCT LastIp) > 1
          ) THEN 1 END) as suspicious_logins,
    COUNT(CASE WHEN DATEPART(hour, LoginTime) BETWEEN 0 AND 5 
          AND LoginTime >= DATEADD(day, -1, GETDATE()) THEN 1 END) as night_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$securityQuery = sqlsrv_query($conn, $selectSecurityAlerts);
if ($securityQuery === false) {
    // Use default values if query fails
    $securityStats = array(
        'suspicious_logins' => 0,
        'night_logins' => 0
    );
} else {
    $securityStats = sqlsrv_fetch_array($securityQuery, SQLSRV_FETCH_ASSOC);
}
?>

<div class="row">
    <!-- Time Range Selector -->
    <div class="col-md-12">
        <section class="panel">
            <div class="panel-body">
                <div class="btn-group" role="group">
                    <a href="?url=manager_account/analytics&range=7" 
                       class="btn <?php echo $timeRange == '7' ? 'btn-primary' : 'btn-default'; ?>">
                        7 วันล่าสุด
                    </a>
                    <a href="?url=manager_account/analytics&range=30" 
                       class="btn <?php echo $timeRange == '30' ? 'btn-primary' : 'btn-default'; ?>">
                        30 วันล่าสุด
                    </a>
                    <a href="?url=manager_account/analytics&range=90" 
                       class="btn <?php echo $timeRange == '90' ? 'btn-primary' : 'btn-default'; ?>">
                        90 วันล่าสุด
                    </a>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Account Overview -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($totalStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $totalStats['online_accounts']; ?> ออนไลน์</span>
                            <span class="text-danger"><?php echo $totalStats['banned_accounts']; ?> ถูกแบน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['new_today']; ?></strong>
                                <span class="text-secondary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['new_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-gamepad"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $totalStats['active_today']; ?></strong>
                                <span class="text-tertiary">วันนี้</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted"><?php echo $totalStats['active_week']; ?> สัปดาห์นี้</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">แจ้งเตือนความปลอดภัย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo $securityStats['suspicious_logins']; ?></strong>
                                <span class="text-quaternary">รายการ</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-warning"><?php echo $securityStats['night_logins']; ?> เข้าสู่ระบบกลางคืน</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Hourly Activity Chart -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">กิจกรรมการเข้าสู่ระบบตามชั่วโมง (7 วันล่าสุด)</h2>
            </header>
            <div class="panel-body">
                <canvas id="hourlyActivityChart" height="100"></canvas>
            </div>
        </section>
    </div>

    <!-- Financial Overview -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ภาพรวมทางการเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_cash_all']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash รวมทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['accounts_with_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">บัญชีที่มี Cash</p>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_rewards']); ?></span>
                            <p class="text-xs text-muted mb-none">Reward Points รวม</p>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['avg_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">Cash เฉลี่ย</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Character Statistics -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติตัวละคร</h2>
            </header>
            <div class="panel-body">
                <canvas id="characterChart" height="150"></canvas>
                <div class="mt-lg">
                    <div class="row text-center">
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($characterStats['total_characters']); ?></span>
                            <p class="text-xs text-muted mb-none">ตัวละครทั้งหมด</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo round($characterStats['avg_level'], 1); ?></span>
                            <p class="text-xs text-muted mb-none">Level เฉลี่ย</p>
                        </div>
                        <div class="col-xs-4">
                            <span class="text-lg text-weight-semibold"><?php echo $characterStats['max_level']; ?></span>
                            <p class="text-xs text-muted mb-none">Level สูงสุด</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Top Active Players -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ผู้เล่นที่ใช้งานมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ผู้เล่น</th>
                                <th>เวลาเล่น (ชั่วโมง)</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topPlayers as $player): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($player['ID']); ?></td>
                                    <td><?php echo round($player['PlayTime'] / 60, 1); ?></td>
                                    <td>
                                        <span class="label label-<?php echo $player['status'] == 'ออนไลน์' ? 'success' : 'default'; ?>">
                                            <?php echo $player['status']; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Hourly Activity Chart
    var hourlyCtx = document.getElementById('hourlyActivityChart').getContext('2d');
    var hourlyData = {
        labels: [
            '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
            '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
            '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
            '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
        ],
        datasets: [{
            label: 'การเข้าสู่ระบบ',
            data: [
                <?php 
                $hourlyValues = array();
                for ($i = 0; $i < 24; $i++) {
                    $hourlyValues[] = isset($hourlyActivity[$i]) ? $hourlyActivity[$i] : 0;
                }
                echo implode(',', $hourlyValues);
                ?>
            ],
            borderColor: '#0088cc',
            backgroundColor: 'rgba(0, 136, 204, 0.1)',
            borderWidth: 2,
            fill: true
        }]
    };
    
    new Chart(hourlyCtx, {
        type: 'line',
        data: hourlyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Character Distribution Chart
    var characterCtx = document.getElementById('characterChart').getContext('2d');
    var characterData = {
        labels: ['Capella', 'Procyon', 'High Level (100+)'],
        datasets: [{
            data: [
                <?php echo $characterStats['capella_chars']; ?>, 
                <?php echo $characterStats['procyon_chars']; ?>,
                <?php echo $characterStats['high_level_chars']; ?>
            ],
            backgroundColor: ['#0088cc', '#d9534f', '#5cb85c'],
            borderWidth: 0
        }]
    };
    
    new Chart(characterCtx, {
        type: 'doughnut',
        data: characterData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
