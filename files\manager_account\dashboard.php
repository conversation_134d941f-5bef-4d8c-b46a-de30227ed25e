<?php $user->restrictionUser(true, $conn); ?>

<style>
/* Dashboard Specific Styles */
.dashboard-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    margin-bottom: 20px;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 30px;
}

.activity-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.progress-custom {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar-custom {
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.6s ease;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse-slow {
    animation: pulse-slow 3s infinite;
}

@keyframes pulse-slow {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}
</style>

<?php
// Get comprehensive statistics
$getDashboardStats = function ($conn) {
    $stats = [
        'total_accounts' => 0,
        'online_players' => 0,
        'banned_accounts' => 0,
        'today_registrations' => 0,
        'this_week_registrations' => 0,
        'total_characters' => 0,
        'active_characters' => 0,
        'total_playtime' => 0,
        'recent_activities' => []
    ];
    
    try {
        // Total accounts
        $query = "SELECT COUNT(*) as total FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table";
        $result = sqlsrv_query($conn, $query);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_accounts'] = $row['total'];
        }
        
        // Online players
        $query = "SELECT COUNT(*) as online FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE Login = 1";
        $result = sqlsrv_query($conn, $query);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['online_players'] = $row['online'];
        }
        
        // Banned accounts
        $query = "SELECT COUNT(*) as banned FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE AuthType = 2";
        $result = sqlsrv_query($conn, $query);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['banned_accounts'] = $row['banned'];
        }
        
        // Today registrations
        $today = date('Y-m-d');
        $query = "SELECT COUNT(*) as today_reg FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE CAST(createDate AS DATE) = ?";
        $result = sqlsrv_query($conn, $query, [$today]);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['today_registrations'] = $row['today_reg'];
        }
        
        // This week registrations
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        $query = "SELECT COUNT(*) as week_reg FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE CAST(createDate AS DATE) >= ?";
        $result = sqlsrv_query($conn, $query, [$weekStart]);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['this_week_registrations'] = $row['week_reg'];
        }
        
        // Total characters (if character table exists)
        if (defined('DATABASE_SV')) {
            $query = "SELECT COUNT(*) as total_chars FROM [" . DATABASE_SV . "].[dbo].cabal_character_table";
            $result = sqlsrv_query($conn, $query);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['total_characters'] = $row['total_chars'];
            }
            
            // Active characters (logged in today)
            $query = "SELECT COUNT(*) as active_chars FROM [" . DATABASE_SV . "].[dbo].cabal_character_table WHERE Login = 1";
            $result = sqlsrv_query($conn, $query);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['active_characters'] = $row['active_chars'];
            }
        }
        
        // Total playtime
        $query = "SELECT SUM(PlayTime) as total_playtime FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table";
        $result = sqlsrv_query($conn, $query);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_playtime'] = $row['total_playtime'] ?? 0;
        }
        
        // Recent activities (last 10 logins)
        $query = "SELECT TOP 10 ID, LoginTime, LastIp FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table 
                  WHERE LoginTime IS NOT NULL ORDER BY LoginTime DESC";
        $result = sqlsrv_query($conn, $query);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['recent_activities'][] = $row;
            }
        }
        
    } catch (Exception $e) {
        // Handle errors silently for now
    }
    
    return $stats;
};

$dashboardStats = $getDashboardStats($conn);
?>

<div class="subheader fade-in-up">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-line"></i> Dashboard
        <small>
            ภาพรวมระบบจัดการบัญชีผู้เล่น
        </small>
    </h1>
    <div class="subheader-block">
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fal fa-sync-alt mr-2"></i>รีเฟรชข้อมูล
        </button>
    </div>
</div>

<!-- Main Statistics Cards -->
<div class="row fade-in-up">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
        <div class="dashboard-card pulse-slow">
            <div class="card-body text-center p-4">
                <div class="stat-icon mx-auto" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <i class="fal fa-users"></i>
                </div>
                <h2 class="mb-1 font-weight-bold text-primary"><?php echo number_format($dashboardStats['total_accounts']); ?></h2>
                <p class="text-muted mb-0">บัญชีทั้งหมด</p>
                <small class="text-success">
                    <i class="fal fa-arrow-up mr-1"></i>
                    +<?php echo number_format($dashboardStats['today_registrations']); ?> วันนี้
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
        <div class="dashboard-card pulse-slow">
            <div class="card-body text-center p-4">
                <div class="stat-icon mx-auto" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <i class="fal fa-circle"></i>
                </div>
                <h2 class="mb-1 font-weight-bold text-success"><?php echo number_format($dashboardStats['online_players']); ?></h2>
                <p class="text-muted mb-0">ผู้เล่นออนไลน์</p>
                <small class="text-info">
                    <?php 
                    $onlinePercentage = $dashboardStats['total_accounts'] > 0 ? 
                        round(($dashboardStats['online_players'] / $dashboardStats['total_accounts']) * 100, 1) : 0;
                    echo $onlinePercentage . '% ของทั้งหมด';
                    ?>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
        <div class="dashboard-card pulse-slow">
            <div class="card-body text-center p-4">
                <div class="stat-icon mx-auto" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);">
                    <i class="fal fa-ban"></i>
                </div>
                <h2 class="mb-1 font-weight-bold text-danger"><?php echo number_format($dashboardStats['banned_accounts']); ?></h2>
                <p class="text-muted mb-0">บัญชีถูกแบน</p>
                <small class="text-warning">
                    <?php 
                    $bannedPercentage = $dashboardStats['total_accounts'] > 0 ? 
                        round(($dashboardStats['banned_accounts'] / $dashboardStats['total_accounts']) * 100, 1) : 0;
                    echo $bannedPercentage . '% ของทั้งหมด';
                    ?>
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
        <div class="dashboard-card pulse-slow">
            <div class="card-body text-center p-4">
                <div class="stat-icon mx-auto" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <i class="fal fa-clock"></i>
                </div>
                <h2 class="mb-1 font-weight-bold text-info"><?php echo number_format(round($dashboardStats['total_playtime'] / 60)); ?></h2>
                <p class="text-muted mb-0">ชั่วโมงเล่นรวม</p>
                <small class="text-primary">
                    เฉลี่ย <?php echo $dashboardStats['total_accounts'] > 0 ? 
                        number_format(round(($dashboardStats['total_playtime'] / 60) / $dashboardStats['total_accounts'], 1)) : 0; ?> ชม./คน
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Activity Section -->
<div class="row fade-in-up">
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="chart-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fal fa-chart-bar mr-2 text-primary"></i>สถิติการใช้งานรายสัปดาห์
                </h4>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="changeChartPeriod('week')">สัปดาห์</button>
                    <button type="button" class="btn btn-outline-primary" onclick="changeChartPeriod('month')">เดือน</button>
                </div>
            </div>

            <!-- Progress Bars for Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">ผู้เล่นออนไลน์</span>
                        <span class="font-weight-bold"><?php echo $onlinePercentage; ?>%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: <?php echo $onlinePercentage; ?>%"></div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">บัญชีที่ถูกแบน</span>
                        <span class="font-weight-bold text-danger"><?php echo $bannedPercentage; ?>%</span>
                    </div>
                    <div class="progress-custom">
                        <div class="progress-bar-custom" style="width: <?php echo $bannedPercentage; ?>%; background: linear-gradient(90deg, #fc466b 0%, #3f5efb 100%);"></div>
                    </div>
                </div>
            </div>

            <!-- Chart Placeholder -->
            <div class="text-center py-5" style="background: #f8f9fa; border-radius: 10px;">
                <i class="fal fa-chart-line text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3">กราฟแสดงสถิติ</h5>
                <p class="text-muted">สามารถเพิ่ม Chart.js หรือ library อื่นๆ ได้ที่นี่</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="chart-container">
            <h4 class="mb-4">
                <i class="fal fa-history mr-2 text-success"></i>กิจกรรมล่าสุด
            </h4>

            <div style="max-height: 400px; overflow-y: auto;">
                <?php if (!empty($dashboardStats['recent_activities'])): ?>
                    <?php foreach ($dashboardStats['recent_activities'] as $activity): ?>
                    <div class="activity-item">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 mr-3">
                                <div class="rounded-circle bg-success d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fal fa-sign-in-alt text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 font-weight-bold"><?php echo htmlspecialchars($activity['ID']); ?></h6>
                                <p class="mb-1 text-muted small">เข้าสู่ระบบ</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-info">
                                        <i class="fal fa-clock mr-1"></i>
                                        <?php echo $activity['LoginTime'] ? date('d/m/Y H:i', strtotime($activity['LoginTime'])) : 'ไม่ทราบ'; ?>
                                    </small>
                                    <small class="text-muted">
                                        <i class="fal fa-map-marker-alt mr-1"></i>
                                        <?php echo htmlspecialchars($activity['LastIp'] ?? 'N/A'); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fal fa-inbox text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">ไม่มีกิจกรรมล่าสุด</h6>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-3">
                <a href="?url=manager_account/manage-account" class="btn btn-outline-primary btn-sm">
                    <i class="fal fa-list mr-1"></i>ดูรายการทั้งหมด
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row fade-in-up">
    <div class="col-12">
        <div class="chart-container">
            <h4 class="mb-4">
                <i class="fal fa-bolt mr-2 text-warning"></i>การดำเนินการด่วน
            </h4>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="?url=manager_account/manage-account" class="btn btn-outline-primary btn-block py-3">
                        <i class="fal fa-users d-block mb-2" style="font-size: 2rem;"></i>
                        จัดการบัญชีผู้เล่น
                    </a>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="?url=manager_account/statistics" class="btn btn-outline-success btn-block py-3">
                        <i class="fal fa-chart-bar d-block mb-2" style="font-size: 2rem;"></i>
                        สถิติขั้นสูง
                    </a>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="?url=manager_account/analytics" class="btn btn-outline-info btn-block py-3">
                        <i class="fal fa-chart-line d-block mb-2" style="font-size: 2rem;"></i>
                        วิเคราะห์ข้อมูล
                    </a>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="?url=manager_account/monitoring-system" class="btn btn-outline-warning btn-block py-3">
                        <i class="fal fa-shield-alt d-block mb-2" style="font-size: 2rem;"></i>
                        ระบบตรวจสอบ
                    </a>
                </div>
            </div>

            <!-- Additional Quick Actions Row -->
            <div class="row mt-3">
                <div class="col-lg-3 col-md-6 mb-3">
                    <button class="btn btn-outline-secondary btn-block py-3" onclick="showCreateAccountModal()">
                        <i class="fal fa-user-plus d-block mb-2" style="font-size: 2rem;"></i>
                        สร้างบัญชีใหม่
                    </button>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <button class="btn btn-outline-secondary btn-block py-3" onclick="showBulkActionsModal()">
                        <i class="fal fa-tasks d-block mb-2" style="font-size: 2rem;"></i>
                        การดำเนินการแบบกลุ่ม
                    </button>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <button class="btn btn-outline-secondary btn-block py-3" onclick="exportData()">
                        <i class="fal fa-download d-block mb-2" style="font-size: 2rem;"></i>
                        ส่งออกข้อมูล
                    </button>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="?url=manager_account/test-db" class="btn btn-outline-secondary btn-block py-3">
                        <i class="fal fa-database d-block mb-2" style="font-size: 2rem;"></i>
                        ทดสอบฐานข้อมูล
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    Swal.fire({
        title: 'กำลังรีเฟรชข้อมูล...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    setTimeout(() => {
        location.reload();
    }, 1000);
}

function changeChartPeriod(period) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Here you would typically update the chart data
    console.log('Changing chart period to:', period);
}

function showCreateAccountModal() {
    Swal.fire({
        title: 'สร้างบัญชีใหม่',
        text: 'ฟีเจอร์นี้อยู่ระหว่างการพัฒนา',
        icon: 'info',
        confirmButtonText: 'ตกลง'
    });
}

function showBulkActionsModal() {
    Swal.fire({
        title: 'การดำเนินการแบบกลุ่ม',
        text: 'ฟีเจอร์นี้อยู่ระหว่างการพัฒนา',
        icon: 'info',
        confirmButtonText: 'ตกลง'
    });
}

function exportData() {
    Swal.fire({
        title: 'ส่งออกข้อมูล',
        text: 'ฟีเจอร์นี้อยู่ระหว่างการพัฒนา',
        icon: 'info',
        confirmButtonText: 'ตกลง'
    });
}

// Auto refresh every 5 minutes
setInterval(function() {
    // Silently refresh data without full page reload
    console.log('Auto refreshing dashboard data...');
}, 300000);
</script>
