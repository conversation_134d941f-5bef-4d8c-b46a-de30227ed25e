<?php
// ไฟล์ debug สำหรับระบบ Redeem Code
session_start();
if (!isset($_SESSION['userLogin'])) {
    echo "ต้อง login ก่อน";
    exit;
}

require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

echo "<h2>Debug Redeem Code System</h2>";

// ทดสอบการเชื่อมต่อ
echo "<h3>1. ทดสอบการเชื่อมต่อฐานข้อมูล</h3>";
if ($conn) {
    echo "<p style='color: green;'>✅ เชื่อมต่อสำเร็จ</p>";
} else {
    echo "<p style='color: red;'>❌ เชื่อมต่อล้มเหลว</p>";
    exit;
}

// ทดสอบการสร้าง table
echo "<h3>2. ทดสอบการสร้าง Table</h3>";
function createTableIfNotExists($conn) {
    try {
        $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_Redeem_Code'";
        $checkStmt = sqlsrv_query($conn, $checkSql);
        
        if (!$checkStmt) {
            echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบ table ได้: " . print_r(sqlsrv_errors(), true) . "</p>";
            return false;
        }
        
        $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if (!$result) {
            echo "<p style='color: red;'>❌ ไม่สามารถดึงผลลัพธ์ได้: " . print_r(sqlsrv_errors(), true) . "</p>";
            return false;
        }
        
        if ($result['count'] == 0) {
            echo "<p style='color: orange;'>⚠️ Table ไม่มี กำลังสร้าง...</p>";
            $createSql = "CREATE TABLE [dbo].[WEB_Redeem_Code] (
                [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
                [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
                [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
                [quantity] int NULL,
                [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
                [datecreated] datetime DEFAULT getdate() NULL,
                [expiry_date] datetime NULL
            )";
            
            $createStmt = sqlsrv_query($conn, $createSql);
            if (!$createStmt) {
                echo "<p style='color: red;'>❌ ไม่สามารถสร้าง table ได้: " . print_r(sqlsrv_errors(), true) . "</p>";
                return false;
            }
            echo "<p style='color: green;'>✅ สร้าง table สำเร็จ</p>";
        } else {
            echo "<p style='color: green;'>✅ Table มีอยู่แล้ว</p>";
        }
        
        return true;
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        return false;
    }
}

$tableResult = createTableIfNotExists($conn);

// ทดสอบการสร้าง code
echo "<h3>3. ทดสอบการสร้าง Code</h3>";
function generateCodeByFormat($format) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

$testCode = generateCodeByFormat('TEST-XXXX-XXXX');
echo "<p>📝 ทดสอบสร้าง code: <code>$testCode</code></p>";

// ทดสอบการตรวจสอบ code ซ้ำ
echo "<h3>4. ทดสอบการตรวจสอบ Code ซ้ำ</h3>";
if ($tableResult) {
    $checkSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code WHERE code = ?";
    $checkStmt = sqlsrv_query($conn, $checkSql, [$testCode]);
    
    if ($checkStmt) {
        $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($result) {
            echo "<p style='color: green;'>✅ ตรวจสอบ code ซ้ำได้ (พบ " . $result['count'] . " รายการ)</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถดึงผลลัพธ์ได้: " . print_r(sqlsrv_errors(), true) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบ code ซ้ำได้: " . print_r(sqlsrv_errors(), true) . "</p>";
    }
}

// ทดสอบการเพิ่มข้อมูล
echo "<h3>5. ทดสอบการเพิ่มข้อมูล</h3>";
if ($tableResult) {
    $testItems = '1:0:31,10:0:31';
    $testQuantity = 1;
    
    $sql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '1', GETDATE(), ?)";
    $params = [$testCode, $testItems, $testQuantity, null];
    
    $stmt = sqlsrv_query($conn, $sql, $params);
    
    if ($stmt) {
        echo "<p style='color: green;'>✅ เพิ่มข้อมูลสำเร็จ</p>";
        
        // ทดสอบการดึงข้อมูล
        $selectSql = "SELECT TOP 1 * FROM WEB_Redeem_Code WHERE code = ? ORDER BY id DESC";
        $selectStmt = sqlsrv_query($conn, $selectSql, [$testCode]);
        
        if ($selectStmt && $row = sqlsrv_fetch_array($selectStmt, SQLSRV_FETCH_ASSOC)) {
            echo "<p>📋 ข้อมูลที่เพิ่ม:</p>";
            echo "<ul>";
            foreach ($row as $key => $value) {
                if ($value instanceof DateTime) {
                    $value = $value->format('Y-m-d H:i:s');
                }
                echo "<li><strong>$key:</strong> $value</li>";
            }
            echo "</ul>";
            
            // ลบข้อมูลทดสอบ
            $deleteSql = "DELETE FROM WEB_Redeem_Code WHERE id = ?";
            $deleteStmt = sqlsrv_query($conn, $deleteSql, [$row['id']]);
            if ($deleteStmt) {
                echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถดึงข้อมูลที่เพิ่งเพิ่มได้</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มข้อมูลได้: " . print_r(sqlsrv_errors(), true) . "</p>";
    }
}

// ทดสอบการทำงานของ API
echo "<h3>6. ทดสอบ API</h3>";
echo "<p>ทดสอบการเรียก API ด้วย AJAX:</p>";
?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testAPI() {
    console.log('Testing API...');
    
    $.post('class_module/redeem_code_api.php', {
        action: 'generate',
        count: 1,
        items: '1:0:31',
        quantity: 1,
        code_format: 'TEST-XXXX-XXXX'
    }, function(data) {
        console.log('API Response:', data);
        
        if (data.success) {
            $('#apiResult').html('<p style="color: green;">✅ API ทำงานสำเร็จ: สร้าง ' + data.codes.length + ' codes</p>');
            
            if (data.codes.length > 0) {
                $('#apiResult').append('<p>Code ที่สร้าง: <code>' + data.codes[0].code + '</code></p>');
            }
        } else {
            $('#apiResult').html('<p style="color: red;">❌ API ล้มเหลว: ' + (data.message || 'Unknown error') + '</p>');
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        console.error('Response:', xhr.responseText);
        $('#apiResult').html('<p style="color: red;">❌ AJAX Error: ' + error + '</p><pre>' + xhr.responseText + '</pre>');
    });
}

$(document).ready(function() {
    $('#testApiBtn').click(testAPI);
});
</script>

<button id="testApiBtn" class="btn btn-primary">ทดสอบ API</button>
<div id="apiResult"></div>

<?php
echo "<h3>7. สรุป</h3>";
if ($tableResult) {
    echo "<p style='color: green;'>✅ ระบบพื้นฐานทำงานได้</p>";
    echo "<p>🔗 ลองใช้งานระบบหลัก: <a href='redeem_code_generator.php'>Redeem Code Generator</a></p>";
} else {
    echo "<p style='color: red;'>❌ ระบบมีปัญหา กรุณาตรวจสอบการตั้งค่าฐานข้อมูล</p>";
}
?>

<style>
.btn {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 10px 0;
}
.btn:hover {
    background-color: #0056b3;
}
</style>
