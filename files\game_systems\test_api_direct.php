<?php
// ทดสอบ API โดยตรง
session_start();
$_SESSION['userLogin'] = true; // จำลอง login

require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

echo "<h2>ทดสอบ API โดยตรง</h2>";

// จำลองการส่งข้อมูล POST
$_POST = [
    'action' => 'generate',
    'count' => '1',
    'items' => '1:0:31',
    'quantity' => '1',
    'expiry_date' => '',
    'code_format' => 'XXXX-XXXX-XXXX-XXXX'
];

echo "<h3>ข้อมูลที่ส่ง:</h3>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h3>ผลลัพธ์จาก API:</h3>";

// เรียก API
ob_start();
include 'class_module/redeem_code_api.php';
$output = ob_get_clean();

echo "<pre>$output</pre>";

// ทดสอบการ decode JSON
echo "<h3>ทดสอบ JSON:</h3>";
$decoded = json_decode($output, true);
if ($decoded) {
    echo "<p style='color: green;'>✅ JSON ถูกต้อง</p>";
    echo "<pre>" . print_r($decoded, true) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ JSON ไม่ถูกต้อง</p>";
    echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
}
?>
