<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code. 
 */
$user->restrictionUser(true, $conn);?>
<section class="panel">
<div class="panel-body">
<h2><?php echo PT_MANAGETICKETS; ?> <small><?php echo PT_MANAGETICKETS_DESC; ?></small></h2>

<div class="row">
    <div class="col-sm-3">
        <div class="panel panel-default">
            <div class="panel-heading">
                <span>STATUS</span>
            </div>
            <div class="panel-body no-padd">
                <div class="list-group no-margn helpdesk-nav">
                    <a href="?url=helpdesk/a/tickets" class="list-group-item<?php
                    if (!isset($_GET['status'])) {
                        echo " on";
                    }
                    ?>"><span class="badge bg-primary text-white"><?php echo $countWaiting = ($helpdesk->countTicketsA($conn, '1') ? $helpdesk->countTicketsA($conn, '1') : '0'); ?></span><?php echo MP_WAITING; ?></a>
                    <a href="?url=helpdesk/a/tickets&status=4" class="list-group-item<?php
                    if ($_GET['status'] == '4') {
                        echo " on";
                    }
                    ?>"><span class="badge bg-danger text-white"><?php echo $countSolved = ($helpdesk->countTicketsA($conn, '4') ? $helpdesk->countTicketsA($conn, '4') : '0'); ?></span><?php echo MP_SOLVED; ?></a>
                    <a href="?url=helpdesk/a/tickets&status=2" class="list-group-item<?php
                    if ($_GET['status'] == '2') {
                        echo " on";
                    }
                    ?>"><span class="badge bg-warning text-white"><?php echo $countReplied = ($helpdesk->countTicketsA($conn, '2') ? $helpdesk->countTicketsA($conn, '2') : '0'); ?></span><?php echo MP_REPLIED; ?></a>
                    <a href="?url=helpdesk/a/tickets&status=3" class="list-group-item<?php
                    if ($_GET['status'] == '3') {
                        echo " on";
                    }
                    ?>"><span class="badge bg-dark text-white"><?php echo $countClosed = ($helpdesk->countTicketsA($conn, '3') ? $helpdesk->countTicketsA($conn, '3') : '0'); ?></span><?php echo MP_CLOSED; ?></a>
                </div>
            </div>
        </div>
         
        <div class="well well-sm">
            <span class="text-red text-bolder"><?php echo T_H_TICKETINFO; ?></span>
        </div>
    </div>

    <div class="col-sm-9">
        <div class="row">
            <div class="col-lg-12 col-xs-7">
                <form method="post" action="">
                    <?php
                    if (isset($_POST['btn_search'])) {
                        $tResult = $_POST['t_search'];
                        if (empty($tResult)) {
                            $tResult = '0';
                        }
                        header('Location: ?url=helpdesk/a/results&result=' . $tResult);
                    }
                    ?>
                    <div class="input-group">
                        <input type="search" name="t_search" class="form-control" placeholder="ค้นหา">
                        <span class="input-group-btn">
                            <input class="btn btn-red" name="btn_search" type="submit" value="Search"/>
                        </span>
                    </div>
                </form>
            </div>
        </div>

        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">

                <table class="table table-hover helpdesks">
                    <thead>
                        <tr>
                            <th><?php echo T_CUSTOMERID; ?></th>
                            <th><?php echo T_TICKET_TITLE; ?></th>
                            <th><?php echo T_TICKET_RESUME; ?></th>
                            <th><?php echo H_STATUS; ?></th>
                            <th><?php echo T_CREATEDIN; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 10;

                        // condition where
                        $getTicketStatus = $_GET['status'];
                        $whereCond = ($getTicketStatus == '1' ? "WHERE status = '1'" : ($getTicketStatus == '2' ? "WHERE status = '2'" : ($getTicketStatus == '3' ? "WHERE status = '3'" : ($getTicketStatus == '4' ? "WHERE status = '4'" : "WHERE status = '1'"))));

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM WEB_H_Tickets {$whereCond} ORDER BY createdate DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo '<div class="alert alert-warning flat"><strong>No have tickets yet.</strong></div>';
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr class="<?php echo $bg = ($row[6] == '1' ? '' : ($row[6] == '2' ? 'info' : ($row[6] == '4' ? 'success' : ($row[6] == '3' ? 'dark' : 'dark')))); ?>">
                                <td>
                                    <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo $row[1]; ?></a>
                                </td>
                                <td class="visible-lg visible-md">
                                    <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>">
                                        <?php
                                        // if quantity of characters is more than 15 show only first 15 characters else show title without cut
                                        if (strlen($row[3]) > 15) {
                                            echo substr($row[3], 0, 40) . ' ...';
                                        } else {
                                            echo $row[3];
                                        }
                                        ?>
                                    </a>
                                </td>

                                <td>
                                    <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo substr($row[4], 0, 40); ?> ...</a>
                                </td>

                                <td>
                                    <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"<?php echo $onlyClosed = ($row[6] == '3' ? ' style="color: #FF3535"' : ''); ?>><?php echo $status = ($row[6] == '1' ? T_WAITING : ($row[6] == '2' ? T_REPLIED : ($row[6] == '3' ? T_CLOSED : ($row[6] == '4' ? T_SOLVED : T_UNKNOWNSTATUS)))); ?></a>
                                </td>

                                <td class="visible-lg visible-md visible-sm">
                                    <a href="?url=helpdesk/a/view-ticket&tid=<?php echo $row[0]; ?>"><?php echo date('d/m ' . T_AT . ' H:i', strtotime($row[9])); ?></a>
                                </td>
                                
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/tickets&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
</div>
</section>