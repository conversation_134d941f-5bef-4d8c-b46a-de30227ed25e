<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Item Manager - Standalone</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .alert {
            border: none;
            border-radius: 10px;
        }
        #debugConsole {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        #debugConsole::-webkit-scrollbar {
            width: 8px;
        }
        #debugConsole::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        #debugConsole::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        #debugConsole::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .panel-hdr {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
        }
        .panel-content {
            padding: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="display-4">
                        <i class="fas fa-cube text-primary"></i> 
                        Advanced Item Manager
                    </h1>
                    <p class="lead text-muted">
                        Complete item management system with advanced calculations
                    </p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Basic Item Manager -->
            <div class="col-xl-6">
                <div class="panel">
                    <div class="panel-hdr">
                        <h2>
                            <i class="fas fa-plus-circle"></i> Basic Item Manager
                        </h2>
                    </div>
                    <div class="panel-content">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> How it works</h5>
                            <ul class="mb-0">
                                <li><strong>Code Item:</strong> รหัสไอเท็ม</li>
                                <li><strong>Binding:</strong> การผูกมัดไอเท็ม</li>
                                <li><strong>Hex/Dec:</strong> แปลงเลขฐาน 16/10</li>
                            </ul>
                        </div>

                        <form id="basicItemForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Base Item Code:</label>
                                    <input type="number" id="input_Item" class="form-control" placeholder="Enter item code">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Item Name:</label>
                                    <input type="text" id="input_ItemName" class="form-control" placeholder="Item name (optional)">
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <label class="form-label">Bind-ID Result:</label>
                                    <input type="text" id="codeoutput_bid" class="form-control" readonly placeholder="Item + 4096">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Bind-Char Result:</label>
                                    <input type="text" id="codeoutput_bchar" class="form-control" readonly placeholder="Item + 524288">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Bind-Equ Result:</label>
                                    <input type="text" id="codeoutput_bequ" class="form-control" readonly placeholder="Item + 1572864">
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-3">
                                    <label class="form-label">Hex to Dec:</label>
                                    <input type="text" id="hextodec" class="form-control" placeholder="Enter HEX">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Dec Result:</label>
                                    <input type="text" id="deccode" class="form-control" readonly placeholder="Decimal result">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Dec to Hex:</label>
                                    <input type="text" id="dectohex" class="form-control" placeholder="Enter DEC">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Hex Result:</label>
                                    <input type="text" id="hexcode" class="form-control" readonly placeholder="Hex result">
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-primary w-100" onclick="syncToAdvanced()">
                                        <i class="fas fa-arrow-right"></i> Sync to Advanced →
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-success w-100" onclick="clearBasicForm()">
                                        <i class="fas fa-eraser"></i> Clear Form
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Advanced Item Editor -->
            <div class="col-xl-6">
                <div class="panel">
                    <div class="panel-hdr">
                        <h2>
                            <i class="fas fa-cogs"></i> Advanced Item Editor
                        </h2>
                    </div>
                    <div class="panel-content">
                        <div id="advancedEditorContent">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                <p class="mt-2">Loading Advanced Item Editor...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Binding codes (same as manage-item.php)
        const bindingCodes = {
            none: 0,
            id: 4096,
            char: 524288,
            equ: 1572864
        };

        // Basic form functions (compatible with manage-item.php)
        function handleInputChange() {
            const inputItem = parseFloat(document.getElementById('input_Item').value) || 0;
            
            // Calculate binding codes
            const codeBid = inputItem + bindingCodes.id;
            const codeBchar = inputItem + bindingCodes.char;
            const codeBequ = inputItem + bindingCodes.equ;
            
            // Update outputs
            document.getElementById('codeoutput_bid').value = codeBid.toFixed(0);
            document.getElementById('codeoutput_bchar').value = codeBchar.toFixed(0);
            document.getElementById('codeoutput_bequ').value = codeBequ.toFixed(0);
            
            console.log('Binding calculations:', { inputItem, codeBid, codeBchar, codeBequ });
        }

        function handleHexDecConversion() {
            // Hex to Dec
            const hexValue = document.getElementById('hextodec').value.trim();
            if (hexValue) {
                try {
                    const decValue = parseInt(hexValue, 16);
                    document.getElementById('deccode').value = decValue;
                } catch (error) {
                    document.getElementById('deccode').value = '';
                }
            } else {
                document.getElementById('deccode').value = '';
            }
            
            // Dec to Hex
            const decValue = document.getElementById('dectohex').value.trim();
            if (decValue && !isNaN(decValue)) {
                try {
                    const hexValue = parseInt(decValue).toString(16).toUpperCase();
                    document.getElementById('hexcode').value = hexValue;
                } catch (error) {
                    document.getElementById('hexcode').value = '';
                }
            } else {
                document.getElementById('hexcode').value = '';
            }
        }

        function syncToAdvanced() {
            const itemCode = document.getElementById('input_Item').value;
            const itemName = document.getElementById('input_ItemName').value;
            
            if (itemCode && document.getElementById('testItemId')) {
                document.getElementById('testItemId').value = itemCode;
                if (itemName && document.getElementById('testItemSearch')) {
                    document.getElementById('testItemSearch').value = itemName;
                }
                console.log(`🔄 Synced to Advanced Editor: ${itemCode} - ${itemName}`);
                
                // Trigger advanced calculation if available
                if (typeof performRealTimeCalculation === 'function') {
                    performRealTimeCalculation();
                }
            } else {
                alert('Please enter an item code first');
            }
        }

        function clearBasicForm() {
            document.getElementById('input_Item').value = '';
            document.getElementById('input_ItemName').value = '';
            document.getElementById('codeoutput_bid').value = '';
            document.getElementById('codeoutput_bchar').value = '';
            document.getElementById('codeoutput_bequ').value = '';
            document.getElementById('hextodec').value = '';
            document.getElementById('deccode').value = '';
            document.getElementById('dectohex').value = '';
            document.getElementById('hexcode').value = '';
        }

        // Setup event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Basic form listeners
            document.getElementById('input_Item').addEventListener('input', handleInputChange);
            document.getElementById('hextodec').addEventListener('input', handleHexDecConversion);
            document.getElementById('dectohex').addEventListener('input', handleHexDecConversion);
            
            // Load Advanced Editor
            loadAdvancedEditor();
        });

        // Load Advanced Editor content
        async function loadAdvancedEditor() {
            console.log('🔄 Loading Advanced Editor content...');

            try {
                // Try different possible paths
                let response;
                const possiblePaths = [
                    'advanced-editor-content.php',
                    './advanced-editor-content.php',
                    'advanced-editor.php',
                    './advanced-editor.php'
                ];

                for (const path of possiblePaths) {
                    try {
                        console.log(`🔍 Trying to load from: ${path}`);
                        response = await fetch(path);
                        console.log(`📡 Response status: ${response.status} for ${path}`);
                        if (response.ok) {
                            console.log(`✅ Found Advanced Editor at: ${path}`);
                            break;
                        } else {
                            console.log(`❌ Failed with status ${response.status} for: ${path}`);
                        }
                    } catch (e) {
                        console.log(`❌ Exception loading from: ${path}`, e.message);
                    }
                }

                if (!response || !response.ok) {
                    throw new Error(`HTTP error! status: ${response?.status || 'unknown'}`);
                }

                const html = await response.text();
                console.log('📄 HTML loaded, length:', html.length);

                // Extract the main content (between container-fluid tags)
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Try different selectors to find content
                let container = doc.querySelector('.container-fluid');
                if (!container) {
                    container = doc.querySelector('body');
                    console.log('🔍 Using body as container');
                }
                if (!container) {
                    container = doc.documentElement;
                    console.log('🔍 Using documentElement as container');
                }

                if (container) {
                    // Get the content and inject it
                    let advancedContent = container.innerHTML;

                    // If container is body or documentElement, extract only the main content
                    if (container.tagName === 'BODY' || container.tagName === 'HTML') {
                        const mainContent = container.querySelector('.container-fluid, .container, main, .content');
                        if (mainContent) {
                            advancedContent = mainContent.innerHTML;
                            console.log('🔍 Extracted main content from', mainContent.className);
                        }
                    }

                    document.getElementById('advancedEditorContent').innerHTML = advancedContent;
                    console.log('✅ Content injected, length:', advancedContent.length);

                    // Load and execute the ItemManager script
                    const itemManagerScript = document.createElement('script');
                    itemManagerScript.src = 'js/item-manager.js';
                    itemManagerScript.onload = function() {
                        console.log('✅ ItemManager script loaded');

                        // Initialize ItemManager
                        if (typeof ItemManager !== 'undefined') {
                            window.itemManager = new ItemManager();
                            console.log('✅ ItemManager initialized');
                        }

                        // Execute any inline scripts from the loaded content
                        const scripts = doc.querySelectorAll('script');
                        scripts.forEach(script => {
                            if (script.textContent && !script.src) {
                                try {
                                    const newScript = document.createElement('script');
                                    newScript.textContent = script.textContent;
                                    document.head.appendChild(newScript);
                                } catch (error) {
                                    console.warn('Could not execute script:', error);
                                }
                            }
                        });

                        // Setup sync from advanced editor
                        setTimeout(() => {
                            setupAdvancedSync();
                        }, 1000);
                    };
                    itemManagerScript.onerror = function() {
                        console.warn('❌ Could not load ItemManager script, using fallback');
                        setupAdvancedSync();
                    };
                    document.head.appendChild(itemManagerScript);

                    console.log('✅ Advanced Editor content loaded successfully');

                } else {
                    console.warn('❌ Could not find any container in advanced editor');
                    console.log('Available elements:', doc.body?.children?.length || 0);
                    throw new Error('Could not find container in advanced editor');
                }

            } catch (error) {
                console.error('❌ Error loading Advanced Editor:', error);
                console.log('🔄 Loading inline Advanced Editor instead...');

                // Add debug info
                console.log('Response status:', response?.status);
                console.log('Response ok:', response?.ok);

                loadInlineAdvancedEditor();
            }
        }

        // Load inline Advanced Editor (fallback)
        function loadInlineAdvancedEditor() {
            const advancedEditorHTML = `
                <div class="alert alert-info mb-3">
                    <h5><i class="fas fa-info-circle"></i> Advanced Item Calculator</h5>
                    <p class="mb-0">Complete item code calculation with options, binding, and hex conversion.</p>
                </div>

                <form id="advancedForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Item Search:</label>
                            <input type="text" id="testItemSearch" class="form-control" placeholder="Search item name...">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Item ID:</label>
                            <input type="number" id="testItemId" class="form-control" placeholder="Item ID">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label class="form-label">Item Type:</label>
                            <select id="testItemType" class="form-control">
                                <option value="Weapon" selected>Weapon</option>
                                <option value="Helm">Helm</option>
                                <option value="Suit">Suit</option>
                                <option value="Gloves">Gloves</option>
                                <option value="Boots">Boots</option>
                                <option value="Bike">Bike</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Upgrade:</label>
                            <select id="testUpgrade" class="form-control">
                                <option value="0">+0</option>
                                <option value="5">+5</option>
                                <option value="10">+10</option>
                                <option value="15" selected>+15</option>
                                <option value="20">+20</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Binding:</label>
                            <select id="testBinding" class="form-control">
                                <option value="none">No Binding</option>
                                <option value="id">Bind to ID</option>
                                <option value="char" selected>Bind to Character</option>
                                <option value="equ">Bind on Equip</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Duration:</label>
                            <select id="testDuration" class="form-control">
                                <option value="31" selected>31->ถาวร</option>
                                <option value="17">17->30 วัน</option>
                                <option value="12">12->7 วัน</option>
                                <option value="9">9->1 วัน</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" onclick="calculateAdvanced()">
                                <i class="fas fa-calculator"></i> Calculate Advanced
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="clearAdvancedForm()">
                                <i class="fas fa-eraser"></i> Clear
                            </button>
                        </div>
                    </div>
                </form>

                <div id="advancedResults" class="mt-4">
                    <!-- Results will be displayed here -->
                </div>
            `;

            document.getElementById('advancedEditorContent').innerHTML = advancedEditorHTML;
            setupAdvancedCalculator();
            console.log('✅ Inline Advanced Editor loaded');
        }

        // Setup advanced calculator
        function setupAdvancedCalculator() {
            // Advanced calculation function
            window.calculateAdvanced = function() {
                const itemId = parseInt(document.getElementById('testItemId').value) || 0;
                const itemType = document.getElementById('testItemType').value;
                const upgrade = parseInt(document.getElementById('testUpgrade').value) || 0;
                const binding = document.getElementById('testBinding').value;
                const duration = parseInt(document.getElementById('testDuration').value) || 31;

                if (itemId === 0) {
                    alert('Please enter an Item ID');
                    return;
                }

                // Calculate binding code
                let bindingCode = 0;
                switch(binding) {
                    case 'id': bindingCode = bindingCodes.id; break;
                    case 'char': bindingCode = bindingCodes.char; break;
                    case 'equ': bindingCode = bindingCodes.equ; break;
                }

                // Calculate final item code
                const finalItemCode = itemId + bindingCode;
                const hexItemCode = finalItemCode.toString(16).toUpperCase();

                // Display results
                const resultsHTML = \`
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Advanced Calculation Results</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Final Results:</h6>
                                <p><strong>Item Code:</strong> <span style="background-color: #333; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">\${finalItemCode}</span></p>
                                <p><strong>Hex Code:</strong> <span style="background-color: #333; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">\${hexItemCode}</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Details:</h6>
                                <p><strong>Base Item:</strong> \${itemId}</p>
                                <p><strong>Binding:</strong> +\${bindingCode} (\${binding.toUpperCase()})</p>
                                <p><strong>Type:</strong> \${itemType}</p>
                                <p><strong>Upgrade:</strong> +\${upgrade}</p>
                            </div>
                        </div>
                    </div>
                \`;

                document.getElementById('advancedResults').innerHTML = resultsHTML;

                // Sync back to basic form
                if (document.getElementById('input_Item')) {
                    document.getElementById('input_Item').value = itemId;
                    handleInputChange();
                }

                console.log('Advanced calculation:', { itemId, finalItemCode, hexItemCode, binding, bindingCode });
            };

            window.clearAdvancedForm = function() {
                document.getElementById('testItemSearch').value = '';
                document.getElementById('testItemId').value = '';
                document.getElementById('testItemType').value = 'Weapon';
                document.getElementById('testUpgrade').value = '0';
                document.getElementById('testBinding').value = 'none';
                document.getElementById('testDuration').value = '31';
                document.getElementById('advancedResults').innerHTML = '';
            };

            // Setup sync
            setupAdvancedSync();
        }
        }

        // Setup sync from advanced editor to basic form
        function setupAdvancedSync() {
            const testItemId = document.getElementById('testItemId');
            const testItemSearch = document.getElementById('testItemSearch');

            if (testItemId) {
                testItemId.addEventListener('change', function() {
                    const itemCode = this.value;
                    if (itemCode && document.getElementById('input_Item')) {
                        document.getElementById('input_Item').value = itemCode;
                        handleInputChange();
                        console.log(`🔄 Synced from Advanced Editor: ${itemCode}`);
                    }
                });
            }

            if (testItemSearch) {
                testItemSearch.addEventListener('change', function() {
                    const itemName = this.value;
                    if (itemName && document.getElementById('input_ItemName')) {
                        document.getElementById('input_ItemName').value = itemName;
                        console.log(`🔄 Synced item name from Advanced Editor: ${itemName}`);
                    }
                });
            }

            console.log('✅ Advanced Editor sync setup complete');
        }

        // Sync from advanced editor to basic form
        function syncFromAdvanced(itemId) {
            if (itemId && document.getElementById('input_Item')) {
                document.getElementById('input_Item').value = itemId;
                handleInputChange();
                console.log(`🔄 Synced from Advanced Editor: ${itemId}`);
            }
        }

        // Global functions for debugging
        window.syncToAdvanced = syncToAdvanced;
        window.syncFromAdvanced = syncFromAdvanced;
        window.loadAdvancedEditor = loadAdvancedEditor;
        window.clearBasicForm = clearBasicForm;

        console.log('🔧 Standalone Item Manager loaded');
        console.log('Available functions: syncToAdvanced(), loadAdvancedEditor(), clearBasicForm()');
    </script>
