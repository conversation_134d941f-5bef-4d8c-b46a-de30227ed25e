<?php
ob_start();
session_start();

// require database connection
require('../../_app/dbinfo.inc.php');
require('../../_app/php/zpanel.class.php');
require('../../_app/general_config.inc.php');
$zpanel = new zpanel();

// post action
$action = strip_tags(trim($_POST['action']));

$params = [];
$options = ['Scrollable' => SQLSRV_CURSOR_KEYSET];

switch ($action) {

 case 'add_protrue'://ส่งโปรเติมเงิน
        $idcard = strip_tags(trim($_POST['cardid']));
        $getID = strip_tags(trim($_POST['userid']));

        $selectPremium2 = "SELECT * FROM ".DATABASE_WEB.".dbo.WEB_Tmoney WHERE user_no = '$getID' AND card_id = '$idcard' ";
        $selectPremiumQuery2 = sqlsrv_query($conn, $selectPremium2, array());
        $selectPremiumFetch2 = sqlsrv_fetch_array($selectPremiumQuery2, SQLSRV_FETCH_ASSOC);
		$playerid = $selectPremiumFetch2['user_no'];

		$selectPremium = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE ID = '$playerid'";
        $selectPremiumQuery = sqlsrv_query($conn, $selectPremium, array());
        $selectPremiumFetch = sqlsrv_fetch_array($selectPremiumQuery, SQLSRV_FETCH_ASSOC);
		$playeruser = $selectPremiumFetch['UserNum'];
        $amount = $selectPremiumFetch2['amount'];
		$amountstatus = $selectPremiumFetch2['status'];
		$amountcheck = $selectPremiumFetch2['checks'];
		$cardidcheck = $selectPremiumFetch2['card_id'];
		$passwordcardcheck = $selectPremiumFetch2['password'];

		if($amount == 0 && $amountstatus > 1){ echo 'Error:check'; 
		}elseif($amountstatus == 0 && $amountcheck == 1){ echo 'Error:check';
		}elseif($amountstatus > 1  && $amountcheck == 1){ echo 'Error:check';
		}elseif($getID !== $playerid){ echo 'Error:check'; 
		}else{ 
			
			if($amount == 1 && $amountstatus == 1 && $amountcheck == 0 ){ 
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward50 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:50';
                                    } else {
                                            echo 'Error:check';
                                    }
			}elseif($amount == 2 && $amountstatus == 1 && $amountcheck == 0){ 
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward90 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:90';
                                    } else {
                                            echo 'Error:check';
                                    }
			}elseif($amount == 3 && $amountstatus == 1 && $amountcheck == 0){ 
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward150 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:150';
                                    } else {
                                            echo 'Error:check';
                                    }
			}elseif($amount == 4 && $amountstatus == 1 && $amountcheck == 0){ 
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward300 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:300';
                                    } else {
                                            echo 'Error:check';
                                    }
			}elseif($amount == 5 && $amountstatus == 1 && $amountcheck == 0){ 
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward500 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:500';
                                    } else {
                                            echo 'Error:check';
                                    }
			}elseif($amount == 6 && $amountstatus == 1 && $amountcheck == 0){ 
				
				                $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward1000 ?";
								$RewardPlayerParams = array($playeruser);
								$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
											$result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Tmoney SET checks='1' WHERE user_no='".$playerid."' AND card_id = '".$idcard."'");	
                                            echo 'success:1000';
                                    } else {
                                            echo 'Error:check';
                                    }

			}else{ echo 'Error:check';
			}
		} 

break;

case 'add_itemtime'://แลกเวลสออนไลน์
        $idmail = strip_tags(trim($_POST['mailid']));
        $getID = strip_tags(trim($_POST['charid']));
        // select
        $selectPremium2 = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table WHERE ReceivedMailID = '$idmail' AND ReceiverCharIdx = '8'";
        $selectPremiumQuery2 = sqlsrv_query($conn, $selectPremium2, array());
        $selectPremiumFetch2 = sqlsrv_fetch_array($selectPremiumQuery2, SQLSRV_FETCH_ASSOC);
        $usernumSender = floor($selectPremiumFetch2['SenderCharIdx']/8);

        $adminid = $selectPremiumFetch2['ReceiverCharIdx'];
        $mailidx = $selectPremiumFetch2['ReceivedMailID'];
		$itemid = $selectPremiumFetch2['ItemKindIdx'];
		$itemop = $selectPremiumFetch2['ItemOption'];
		$itemIsReceiv = $selectPremiumFetch2['IsReceivedItem'];
		$cashitem = $itemop*10;

		$selectPremium = "SELECT * FROM [".DATABASE_CCA."].[dbo].CashAccount WHERE UserNum = '$usernumSender'";
        $selectPremiumQuery = sqlsrv_query($conn, $selectPremium, array());
        $selectPremiumFetch = sqlsrv_fetch_array($selectPremiumQuery, SQLSRV_FETCH_ASSOC);
				if($selectPremiumFetch){
							if($mailidx == $idmail && $itemid == 3090 && $itemIsReceiv == 0){ 

													$RewardPlayer = "UPDATE [".DATABASE_CCA."].[dbo].CashAccount SET CashBonus = CashBonus+? Where UserNum = ?";
													$RewardPlayerParams = array($cashitem,$usernumSender);
													$RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
													if ($RewardPlayerQuery) {
															$result = @sqlsrv_query($conn,"DELETE  [".DATABASE_SV."].[dbo].cabal_mail_received_table Where ReceivedMailID = '".$idmail."' AND ReceiverCharIdx = '8'");	
															echo 'success';
													} else {
															echo 'Error:check';
													}
						
							}else{ 
								echo 'Error:check2';
							}
		}
break;

case 'add_facebookshare'://กิจกรรม เฟสบุค
        $id = strip_tags(trim($_POST['id']));
        $getID = strip_tags(trim($_POST['charid']));

		    function useridname($conn,$ids= null){
                    {
                        $cond = ($ids ? "WHERE userid = '$ids'" : "WHERE userid = '$ids'");
                        $selectTickets = "SELECT * FROM [".DATABASE_WEB."].[dbo].WEB_Facebook_share {$cond}";
                        $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                        $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                        
                        if($selectTicketsRows){
                            return $selectTicketsRows;
                        }else{
                            return false;
                        }
                    }
                  }

		$idcount = useridname($conn,$getID) ? useridname($conn,$getID) : '0';

		if ($idcount > 1) {
				echo 'Error:check-id';
         }else{
		
        $selectUsersData = "SELECT * FROM  ".DATABASE_WEB.".dbo.WEB_Facebook_share WHERE userid = '$getID' AND id = '$id'";
        $selectUsersDataParam = array();
        $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
        $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
		$row_count = sqlsrv_num_rows($selectUsersDataQuery);
		$userid = $selectUsersDataFetch['userid'];
		$status = $selectUsersDataFetch['status'];
		
		if($status == 0){
            if ($selectUsersDataFetch){
                    $selectUsersAccData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE ID = '$userid'";
                    $selectUsersAccDataParam = array();
                    $selectUsersAccDataQuery = sqlsrv_query($conn, $selectUsersAccData, $selectUsersAccDataParam);
                    $selectUsersAccDataFetch = sqlsrv_fetch_array($selectUsersAccDataQuery, SQLSRV_FETCH_ASSOC);
                    $usernum = $selectUsersAccDataFetch['UserNum'];
                            if ($selectUsersAccDataFetch){
                                    $RewardPlayer = "EXECUTE [".DATABASE_WEB."].[dbo].WEB_Cabal_Facebook_Share ?";
                                    $RewardPlayerParams = array($usernum);
                                    $RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                        if ($RewardPlayerQuery) {
                                                $result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Facebook_share SET status='1' WHERE  id = '".$id."'");	
                                                echo 'success';
                                        } else {
                                                echo 'Error:check-sh';
                                        }
                                                    
                            
                            }else{
                                echo 'Error:check-authid';
                            }
                }else{
                echo 'Error:check-fb';
            }
		}else{
			echo 'Error:check-status';
		}
		 }
break;

case 'add_itemthailotto'://กิจกรรม lotto
    $row_id = strip_tags(trim($_POST['id']));
    $LottoStatus = strip_tags(trim($_POST['LottoStd']));

        $selectthailotto = "SELECT * FROM  ".DATABASE_WEB.".dbo.WEB_ThaiLotto WHERE id = '$row_id'";
        $selectthailottoQuery = sqlsrv_query($conn, $selectthailotto, array());
        $selectthailottoFetch = sqlsrv_fetch_array($selectthailottoQuery, SQLSRV_FETCH_ASSOC);
        $usernum = $selectthailottoFetch['UserNum'];
        $ids = $selectthailottoFetch['id'];
        $rowststus = $selectthailottoFetch['status'];
            if($rowststus == 0){
                $LottoPlayer = "EXECUTE [".DATABASE_WEB."].[dbo].WEB_Cabal_Lotto_Item ?,?";
                $LottoPlayerParams = array($usernum,$LottoStatus);
                $LottoPlayerQuery = sqlsrv_query($conn, $LottoPlayer, $LottoPlayerParams);
                if ($LottoPlayerQuery) {
                    if($LottoStatus == 'lose'){
                        $result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_ThaiLotto SET status='1' WHERE UserNum = '".$usernum."'");	
                        echo 'success';
                    }elseif ($LottoStatus == 'win2'){
                        $result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_ThaiLotto SET status='1' WHERE id = '".$row_id."'");	
                        echo 'success';
                    }elseif ($LottoStatus == 'win3'){
                        $result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_ThaiLotto SET status='1' WHERE id = '".$row_id."'");	
                        echo 'success';
                    }else{
                        echo 'Error';
                    }
                } else {
                    echo 'Error';
                }
            }else{
             echo 'Error';
            }
break;


case 'add_waritem'://กิจกรรม เฟสบุค
    $id = strip_tags(trim($_POST['id']));
    $getID = strip_tags(trim($_POST['charid']));

        function useridname($conn,$ids= null){
                {
                    $cond = ($ids ? "WHERE charidx = '$ids'" : "WHERE charidx = '$ids'");
                    $selectTickets = "SELECT * FROM [".DATABASE_WEB."].[dbo].WEB_Game_War_Item {$cond}";
                    $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                    $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                    
                    if($selectTicketsRows){
                        return $selectTicketsRows;
                    }else{
                        return false;
                    }
                }
              }

    $idcount = useridname($conn,$getID) ? useridname($conn,$getID) : '0';

    if ($idcount > 1) {
            echo 'Error:check-id';
     }else{
    
    $selectUsersData = "SELECT * FROM  ".DATABASE_WEB.".dbo.WEB_Game_War_Item WHERE charidx = '$getID' AND id = '$id'";
    $selectUsersDataParam = array();
    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
    $row_count = sqlsrv_num_rows($selectUsersDataQuery);
    $usernum = floor($selectUsersDataFetch['charidx']/8);
    $status = $selectUsersDataFetch['status'];
    
    if($status == 0){
        if ($selectUsersDataFetch){
                $selectUsersAccData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE UserNum = '$usernum'";
                $selectUsersAccDataParam = array();
                $selectUsersAccDataQuery = sqlsrv_query($conn, $selectUsersAccData, $selectUsersAccDataParam);
                $selectUsersAccDataFetch = sqlsrv_fetch_array($selectUsersAccDataQuery, SQLSRV_FETCH_ASSOC);
                $usernum = $selectUsersAccDataFetch['UserNum'];
                        if ($selectUsersAccDataFetch){
                                $RewardPlayer = "EXECUTE [".DATABASE_WEB."].[dbo].WEB_Cabal_Waritem ?";
                                $RewardPlayerParams = array($usernum);
                                $RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                                    if ($RewardPlayerQuery) {
                                            $result = @sqlsrv_query($conn,"UPDATE ".DATABASE_WEB.".dbo.WEB_Game_War_Item SET status='1' WHERE  id = '".$id."'");	
                                            echo 'success';
                                    } else {
                                            echo 'Error:check-sh';
                                    }
                                                
                        
                        }else{
                            echo 'Error:check-authid';
                        }
            }else{
            echo 'Error:check-fb';
        }
    }else{
        echo 'Error:check-status';
    }
     }
break;

case 'add_title'://title
        $charidx = strip_tags(trim($_POST['charsidx']));
        $title = strip_tags(trim($_POST['title']));
            if($charidx == ""){
                echo 'empty';
			}elseif($title == ""){
                echo 'empty';
            }else{
				$selectPlayerData = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = '$charidx'";
                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, array());
                $selectPlayerDataRows = sqlsrv_rows_affected($selectPlayerDataQuery);
				$selectPlayerDatafetch = sqlsrv_fetch_array($selectPlayerDataQuery);
				if ($selectPlayerDataRows) {
					if($selectPlayerDatafetch['Login'] == 1){
						echo 'login-already';
					    }else{
                            $result = @sqlsrv_query($conn,"EXEC [".DATABASE_WEB."].[dbo].WEB_cabal_title_data_set '".$charidx."','".$title."'");
                                if($result){ 
                                    echo 'success'; 
                                }else{
                                    echo 'error';
                                 }
					    }
				}else{
				echo 'error1';
			}	
        }
break;

case 'del_title'://title
        $charidx = strip_tags(trim($_POST['charsidx']));
        $title = strip_tags(trim($_POST['title']));
            if($charidx == ""){
                echo 'empty';
			}elseif($title == ""){
                echo 'empty';
            }else{
				$selectPlayerData = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = '$charidx'";
                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, array());
                $selectPlayerDataRows = sqlsrv_rows_affected($selectPlayerDataQuery);
				$selectPlayerDatafetch = sqlsrv_fetch_array($selectPlayerDataQuery);
				if ($selectPlayerDataRows) {
					if($selectPlayerDatafetch['Login'] == 1){
						echo 'login-already';
					    }else{
                            $result = @sqlsrv_query($conn,"EXEC [".DATABASE_WEB."].[dbo].WEB_cabal_title_data_del '".$charidx."','".$title."'");
                                if($result){ 
                                    echo 'success'; 
                                }else{
                                    echo 'error';
                                 }
					    }
				}else{
				echo 'error1';
			}	
        }
break;

case 'add_reward': // ส่งรีวาดจาก user
    $rewardid = strip_tags(trim($_POST['Rewardtype']));
    $userid = strip_tags(trim($_POST['userid']));

	  if (empty($rewardid)) {
           echo 'error';
        }else{

    $selectUsersData = "SELECT * FROM WEB_Reward_Item WHERE id = '$rewardid'";
    $selectUsersDataParam = array();
    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
    $itemid = $selectUsersDataFetch['itemidx'];
    $itemopp = $selectUsersDataFetch['itemopp'];
    $itemduration = $selectUsersDataFetch['itemduration'];
    $reward = $selectUsersDataFetch['reward_price'];
    if($selectUsersDataQuery){
        $selectUsersAccData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE ID = '$userid'";
        $selectUsersAccDataParam = array();
        $selectUsersAccDataQuery = sqlsrv_query($conn, $selectUsersAccData, $selectUsersAccDataParam);
        $selectUsersAccDataFetch = sqlsrv_fetch_array($selectUsersAccDataQuery, SQLSRV_FETCH_ASSOC);
        $usernum = $selectUsersAccDataFetch['UserNum'];
        if($selectUsersAccDataQuery)
            $selectUsersCashAccData = "SELECT * FROM  [". DATABASE_CCA ."].[dbo].CashAccount WHERE UserNum = '$usernum'";
            $selectUsersCashAccDataParam = array();
            $selectUsersCashAccDataQuery = sqlsrv_query($conn, $selectUsersCashAccData, $selectUsersCashAccDataParam);
            $selectUsersCashAccDataFetch = sqlsrv_fetch_array($selectUsersCashAccDataQuery, SQLSRV_FETCH_ASSOC);
            $accreward = $selectUsersCashAccDataFetch['Reward'];
                if($selectUsersCashAccDataQuery){
                        if($accreward < 0){
                            echo 'error'; 
                        }elseif($accreward < $reward){
                            echo 'error'; 
                        }else{
                        $updateFinancial = "EXECUTE WEB_Reward_Get ?, ?, ?, ?";
                        $updateFinancialParam = array($usernum,$userid,$rewardid,$reward);
                        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);
                            if (sqlsrv_rows_affected($updateFinancialQuery)) {
                                echo 'success';
                            } else {
                                echo 'error';
                            }
                    
            }
    }
}
		}
break;

case 'add_rewarditem': // รับรีวาดจาก user
    $rewardcheck = strip_tags(trim($_POST['rewardcheck']));
    $reward = strip_tags(trim($_POST['rewardid']));
    $userid = strip_tags(trim($_POST['userid']));

    $selectUsersckeckData = "SELECT * FROM WEB_Reward_chack WHERE idx = '$rewardcheck'";
    $selectUsersckeckDataParam = array();
    $selectUsersckeckDataQuery = sqlsrv_query($conn, $selectUsersckeckData, $selectUsersckeckDataParam);
    $selectUsersckeckDataFetch = sqlsrv_fetch_array($selectUsersckeckDataQuery, SQLSRV_FETCH_ASSOC);
    $statuscheck = $selectUsersckeckDataFetch['Reward_status'];

    $selectUsersData = "SELECT * FROM WEB_Reward_Item WHERE id = '$reward'";
    $selectUsersDataParam = array();
    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
    $itemid = $selectUsersDataFetch['itemidx'];
    $itemopp = $selectUsersDataFetch['itemopp'];
    $itemduration = $selectUsersDataFetch['itemduration'];
    $reward = $selectUsersDataFetch['reward_price'];
    if($selectUsersDataQuery){
        $selectUsersAccData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE ID = '$userid'";
        $selectUsersAccDataParam = array();
        $selectUsersAccDataQuery = sqlsrv_query($conn, $selectUsersAccData, $selectUsersAccDataParam);
        $selectUsersAccDataFetch = sqlsrv_fetch_array($selectUsersAccDataQuery, SQLSRV_FETCH_ASSOC);
        $usernum = $selectUsersAccDataFetch['UserNum'];
        if($selectUsersAccDataQuery)
            $selectUsersCashAccData = "SELECT * FROM  [". DATABASE_CCA ."].[dbo].CashAccount WHERE UserNum = '$usernum'";
            $selectUsersCashAccDataParam = array();
            $selectUsersCashAccDataQuery = sqlsrv_query($conn, $selectUsersCashAccData, $selectUsersCashAccDataParam);
            $selectUsersCashAccDataFetch = sqlsrv_fetch_array($selectUsersCashAccDataQuery, SQLSRV_FETCH_ASSOC);
            $accreward = $selectUsersCashAccDataFetch['Reward'];
                if($selectUsersCashAccDataQuery){
                        if($statuscheck == 1){
                            echo 'Error:check'; 
                        }else{
                        $updateFinancial = "EXECUTE [". DATABASE_CCA ."].[dbo].cabal_reward_send ?, ?, ?, ?, ?";
                        $updateFinancialParam = array($usernum,$itemid,$itemopp,$itemduration,$rewardcheck);
                        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);
                            if (sqlsrv_rows_affected($updateFinancialQuery)) {
                                echo 'success';
                            } else {
                                echo 'Error:check';
                            }       
                    
            }
    }
}
break;

case 'add_voucheritem': // รับรีวาดจาก user
    $Voucheckcheck = strip_tags(trim($_POST['Voucheckidx']));
    $userid = strip_tags(trim($_POST['userid']));

    $selectUsersckeckData = "SELECT * FROM WEB_Voucher_chack WHERE idx = '$Voucheckcheck'";
    $selectUsersckeckDataParam = array();
    $selectUsersckeckDataQuery = sqlsrv_query($conn, $selectUsersckeckData, $selectUsersckeckDataParam);
    $selectUsersckeckDataFetch = sqlsrv_fetch_array($selectUsersckeckDataQuery, SQLSRV_FETCH_ASSOC);
    $statuscheck = $selectUsersckeckDataFetch['Voucher_status'];
    $Voucherpack = $selectUsersckeckDataFetch['Voucher_pack'];
    if($selectUsersckeckDataQuery){
        $selectUsersAccData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE ID = '$userid'";
        $selectUsersAccDataParam = array();
        $selectUsersAccDataQuery = sqlsrv_query($conn, $selectUsersAccData, $selectUsersAccDataParam);
        $selectUsersAccDataFetch = sqlsrv_fetch_array($selectUsersAccDataQuery, SQLSRV_FETCH_ASSOC);
        $usernum = $selectUsersAccDataFetch['UserNum'];
        if($selectUsersAccDataQuery)
            $selectUsersCashAccData = "SELECT * FROM  [". DATABASE_CCA ."].[dbo].CashAccount WHERE UserNum = '$usernum'";
            $selectUsersCashAccDataParam = array();
            $selectUsersCashAccDataQuery = sqlsrv_query($conn, $selectUsersCashAccData, $selectUsersCashAccDataParam);
            $selectUsersCashAccDataFetch = sqlsrv_fetch_array($selectUsersCashAccDataQuery, SQLSRV_FETCH_ASSOC);
            $accreward = $selectUsersCashAccDataFetch['Reward'];
                if($selectUsersCashAccDataQuery){
                        if($statuscheck == 1){
                            echo 'Error:check'; 
                        }else{
                        $updateFinancial = "EXECUTE [". DATABASE_WEB ."].[dbo].WEB_VouCherPacks ?, ?, ?";
                        $updateFinancialParam = array($Voucheckcheck,$usernum,$Voucherpack);
                        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);
                            if (sqlsrv_rows_affected($updateFinancialQuery)) {
                                echo 'success';
                            } else {
                                echo 'Error:check';
                            }       
                    
            }
    }
}
break;

case 'ext_premium':
    $getID = strip_tags(trim($_POST['CustomerID']));
    $adminCID = strip_tags(trim($_POST['AdminCID']));

    // select
    $selectPremium = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_charge_auth WHERE UserNum = '$getID'";
    $selectPremiumQuery = sqlsrv_query($conn, $selectPremium, array());
    $selectPremiumFetch = sqlsrv_fetch_array($selectPremiumQuery, SQLSRV_FETCH_ASSOC);

    $monthPremium = date('Y-m-d H:i:s', strtotime("+12 month", strtotime($selectPremiumFetch['ExpireDate'])));

    // extend premium
    $exeExtPremium = "UPDATE ".DATABASE_ACC.".dbo.cabal_charge_auth SET ExpireDate = '$monthPremium' WHERE UserNum = '$getID'";
    //$exeExtPremiumParam = array($getID);
    $exeExtPremiumQuery = sqlsrv_query($conn, $exeExtPremium, array());

    if ($exeExtPremiumQuery) {
        echo 'success';
        // generate web log
        $zpanel->generateWebLog($conn, '3', $adminCID, 'premium extended', "premium extended to player by admin (PlayerID: {$getID}");
    } else {
        echo 'error';
    }
    break;

case 'add_banned':
        $inputdetail = strip_tags(trim($_POST['input_detail']));
        $inputpremise = strip_tags(trim($_POST['input_premise']));
        $inputauthType = strip_tags(trim($_POST['input_authType']));
        // condition
       // if (empty($inputdetail)) {
          //  $inputGC = 0;
       // }
        // get id
        $getID = strip_tags(trim($_POST['CustomerID']));
        $adminCID = strip_tags(trim($_POST['AdminCID']));
		//select
		$selectUsersData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE UserNum = '$getID'";
        $selectUsersDataParam = array();
        $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
        $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
		$idname = $selectUsersDataFetch['ID'];

        $updateFinancial = "EXECUTE [". DATABASE_WEB ."].[dbo].WEB_Cabal_Banned_User ?, ?";
        $updateFinancialParam = array($getID,$inputauthType);
        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);

        // generate web log
       $zpanel->generateWebLog($conn, '6', $adminCID, 'player banned', "banned : {$idname} รายระเอียด : {$inputdetail} หลักฐาน : {$inputpremise}");

        if (sqlsrv_rows_affected($updateFinancialQuery)) {
            echo 'success';
        } else {
            echo 'error';
        }
break;

case 'add_vipcard':
    $inputPassAdmin = strip_tags(trim($_POST['input_passadmin']));
    $inputcardvip = strip_tags(trim($_POST['input_cardvip']));
    $getID = strip_tags(trim($_POST['CustomerID']));
    $adminCID = strip_tags(trim($_POST['AdminCID']));
    // condition
    if (empty($inputPassAdmin)){
        echo 'error';
    } else{
    if ($inputcardvip == "00000000000000"){
        $inputGC = 6;
    }
    if ($inputcardvip == "11111111111111"){
        $inputGC = 5;
    }
    //select
    $selectUsersData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE UserNum = '$getID'";
    $selectUsersDataParam = array();
    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
    $idname = $selectUsersDataFetch['ID'];
            if ($selectUsersDataQuery){
                        $selectUsersPass = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = '$adminCID' AND PWDCOMPARE('$inputPassAdmin', Password) = 1";
                        $updateUserPassAccountParam = array();
                        $updateUserPassAccountQuery = sqlsrv_query($conn, $selectUsersPass, $updateUserPassAccountParam);
                        $updateUserPassAccountRows = sqlsrv_rows_affected($updateUserPassAccountQuery);
                    if (empty($updateUserPassAccountRows) == 0){

                        $selectUsersVipData = "SELECT * FROM  [". DATABASE_WEB ."].[dbo].WEB_Uservip_info WHERE UserNum = '$getID'";
                        $selectUsersVipDataParam = array();
                        $selectUsersVipDataQuery = sqlsrv_query($conn, $selectUsersVipData, $selectUsersVipDataParam);
                        $selectUsersVipDataFetch = sqlsrv_fetch_array($selectUsersVipDataQuery, SQLSRV_FETCH_ASSOC);
                        $selectUsersVipDataRows = sqlsrv_rows_affected($selectUsersVipDataQuery);
                            if (!empty($selectUsersVipDataRows) == 0){
                                echo 'error-novipid';
                            }else{
                        $updateFinancial = "EXECUTE [". DATABASE_WEB ."].[dbo].WEB_TruePremuim ?, ?, ?";
                        $updateFinancialParam = array($idname,$inputcardvip,$inputGC);
                        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);
                                if ($updateFinancialQuery){
                                     $zpanel->generateWebLog($conn, '3', $adminCID, 'Vip Card', "ไอดี : {$idname} CardCode : {$inputcardvip} Amount : {$inputGC}");
                                    echo 'success';
                                }else{
                                 echo 'error';
                                    }   
                                }

                    }else{
                        echo 'error-passadmin';
                    }
            }else{
                echo 'error';
            }  
}
break;


/*
case 'add_banip':
        $inputip = strip_tags(trim($_POST['plr-ip']));
        // condition
        if (empty($inputip)) {
            $inputGC = 0;
        }
        // get id
        $getID = strip_tags(trim($_POST['CustomerID']));
        $adminCID = strip_tags(trim($_POST['AdminCID']));
		//select
		$selectUsersData = "SELECT * FROM  [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE UserNum = '$getID'";
        $selectUsersDataParam = array();
        $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
        $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
		$idname = $selectUsersDataFetch['ID'];

        $updateFinancial = "EXECUTE [". DATABASE_ACC ."].[dbo].cabal_blockip ?, ?, ?";
        $updateFinancialParam = array($inputip,$inputip,$idname);
        $updateFinancialQuery = sqlsrv_query($conn, $updateFinancial, $updateFinancialParam);

        // generate web log
       $zpanel->generateWebLog($conn, '3', $adminCID, 'added gc gd', "added {$inputGC} GC and {$inputGD} GD to playerID: {$getID}");

        if (sqlsrv_rows_affected($updateFinancialQuery)) {
            echo 'success';
        } else {
            echo 'error';
        }
        break;
*/
case 'add_menuperm':
        $formMenuPerm = filter_input_array(INPUT_POST, FILTER_DEFAULT);

        if(empty($formMenuPerm['CustomerID'])){
            echo 'empty';
        }else if(!(int)$formMenuPerm['CustomerID']){
            echo 'onlyint';
        }else{
            $selectMenuPerms = "SELECT * FROM WEB_Perm_Menus WHERE CustomerID = '$formMenuPerm[CustomerID]'";
            $selectMenuPermsQuery = sqlsrv_query($conn, $selectMenuPerms, array(), array("Scrollable" => SQLSRV_CURSOR_KEYSET));
            if(sqlsrv_num_rows($selectMenuPermsQuery)){
                echo 'already';
            }else{
                // update menu perm
                $updateMenuPerm = "INSERT INTO WEB_Perm_Menus (CustomerID, server_admin, web_admin, management) VALUES ('$formMenuPerm[CustomerID]', '$formMenuPerm[server_admin]', '$formMenuPerm[web_admin]', '$formMenuPerm[management]')";
                $updateMenuPermQuery = sqlsrv_query($conn, $updateMenuPerm, array());
                if($updateMenuPermQuery){
                    echo 'success';
                }else{
                    echo 'error';
                }
            }
        }

        break;

case 'add_extraperm':
        $formExtraPerm = filter_input_array(INPUT_POST, FILTER_DEFAULT);

        if(empty($formExtraPerm['CustomerID'])){
            echo 'empty';
        }else if(!(int)$formExtraPerm['CustomerID']){
            echo 'onlyint';
        }else{
            $selectExtraPerms = "SELECT * FROM WEB_Perm_Extras WHERE CustomerID = '$formExtraPerm[CustomerID]'";
            $selectExtraPermsQuery = sqlsrv_query($conn, $selectExtraPerms, array(), array("Scrollable" => SQLSRV_CURSOR_KEYSET));
            if(sqlsrv_num_rows($selectExtraPermsQuery)){
                echo 'already';
            }else{
                // update menu perm
                $updateExtraPerm = "INSERT INTO WEB_Perm_Extras (CustomerID, ban_perm) VALUES ('$formExtraPerm[CustomerID]', '$formExtraPerm[ban_perm]')";
                $updateExtraPermQuery = sqlsrv_query($conn, $updateExtraPerm, array());
                if($updateExtraPermQuery){
                    echo 'success';
                }else{
                    echo 'error';
                }
            }
        }

        break;

case 'add_itemwaruser'://content bringer
    $charidx = strip_tags(trim($_POST['charsidx']));
	$mailwar = strip_tags(trim($_POST['mailsend']));
    $UserNum = floor($charidx/8);

	$itemwar1 = 2847;
	$itemwar1Otp = 5000000;
	$itemwar1Dure= 0;
	$alz1= 0;

	$itemwar2 = 2847;
	$itemwar2Otp = 5000000;
	$itemwar2Dure= 0;
	$alz2 = 3000000;

	$itemwar3 = 3888;
	$itemwar3Otp = 1;
	$itemwar3Dure= 0;

	$itemwar4 = 3087;
	$itemwar4Otp = 1;
	$itemwar4Dure= 0;

	$itemwar5 = 4052;
	$itemwar5Otp = 1;
	$itemwar5Dure= 0;

        if($charidx == ""){
            echo 'Error:check';
        }elseif(empty($charidx)){
            echo 'Error:check';
		}elseif(empty($mailwar)){
            echo 'Error:check';
        }else{
				$selectauth  = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '$UserNum'";
				$selectauthParam = array();
				$selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
				$selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
				$Useridx  = $selectauthFetch['UserNum'];
                 if($selectauthQuery){
					 if($mailwar == 1){
					    $Insertwarmail  = sqlsrv_query($conn,"INSERT INTO [" . DATABASE_SV . "].[dbo].cabal_mail_received_table (ReceiverCharIdx,IsCommitted,Type,Alz,ItemKindIdx,ItemOption,ItemDurationIdx,SenderCharIdx,Title) VALUES ($charidx,1,5,$alz1,$itemwar1,$itemwar1Otp,$itemwar1Dure,0,'War Reward1-gm send')",array());
                      echo 'success';
						}
					elseif ($mailwar == 2){
						$Insertwarmail  = sqlsrv_query($conn,"INSERT INTO [" . DATABASE_SV . "].[dbo].cabal_mail_received_table (ReceiverCharIdx,IsCommitted,Type,Alz,ItemKindIdx,ItemOption,ItemDurationIdx,SenderCharIdx,Title) VALUES ($charidx,1,5,$alz2,$itemwar2,$itemwar2Otp,$itemwar2Dure,0,'War Reward2-gm send')",array());
                      echo 'success';
					}
					elseif ($mailwar == 3){
						$Insertwarmail  = sqlsrv_query($conn,"INSERT INTO [" . DATABASE_SV . "].[dbo].cabal_mail_received_table (ReceiverCharIdx,IsCommitted,Type,Alz,ItemKindIdx,ItemOption,ItemDurationIdx,SenderCharIdx,Title) VALUES ($charidx,1,5,0,$itemwar3,$itemwar3Otp,$itemwar3Dure,0,'War Reward3-gm send')",array());
                      echo 'success';
					}
					elseif ($mailwar == 4){
						$Insert4warmail  = sqlsrv_query($conn,"INSERT INTO [" . DATABASE_SV . "].[dbo].cabal_mail_received_table (ReceiverCharIdx,IsCommitted,Type,Alz,ItemKindIdx,ItemOption,ItemDurationIdx,SenderCharIdx,Title) VALUES ($charidx,1,5,0,$itemwar4,$itemwar4Otp,$itemwar4Dure,0,'War Reward5-gm send')",array());
                      echo 'success';
					
					}else{
						echo 'Error:check';
						}
                  }else{
                   echo 'Error:check';
              }
        }
break;
    default:
        echo 'default';
        break;
}
?>