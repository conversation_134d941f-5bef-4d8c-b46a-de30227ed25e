<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <a href="?url=manager/players" class="btn btn-default" style="margin: 5px 0;">&laquo; <?php echo B_RETURNPLRPAGE; ?></a>
                <?php
                if (isset($_POST['btn_register'])) {
                    // variables access
                    $inputEmail = strip_tags(trim($_POST['input_email']));
                    $inputPassword = strip_tags(trim($_POST['input_password']));
                    $inputRePassword = strip_tags(trim($_POST['input_repass']));
                    // variables peronal
                    $inputName = strip_tags(trim($_POST['input_name']));
                    $inputLastName = strip_tags(trim($_POST['input_lastname']));
                    // variables game and permissions
                    $inputIsDev = strip_tags(trim($_POST['input_isdev']));
                    $inputGC = strip_tags(trim($_POST['input_gc']));
                    $inputGD = strip_tags(trim($_POST['input_gd']));

                    if (empty($inputEmail)) {
                        $returnWarning = W_R_EMAIL_EMPTY;
                    } else if (empty($inputPassword)) {
                        $returnWarning = W_R_PASS_EMPTY;
                    } else if ($inputPassword != $inputRePassword) {
                        $returnWarning = W_R_REPASS_DONT_MATCH;
                    } else if (empty($inputName)) {
                        $returnWarning = W_R_NAME_EMPTY;
                    } else if (empty($inputLastName)) {
                        $returnWarning = W_R_LASTNAME_EMPTY;
                    } else {

                        $getIP = '0.0.0.0';
                        $registerUser = "EXECUTE [" . DATABASE_NAME . "].[dbo].WZ_ACCOUNT_CREATE ?, ?, ?, 3, 0, 0";
                        $registerUserParam = array($getIP, $inputEmail, $inputPassword);
                        $registerUserQuery = sqlsrv_query($conn, $registerUser, $registerUserParam);

                        // get account from actual registered player
                        $selectActualPlayer = "SELECT * FROM Accounts WHERE email = '$inputEmail'";
                        $selectActualPlayerParam = array();
                        $selectActualPlayerQuery = sqlsrv_query($conn, $selectActualPlayer, $selectActualPlayerParam);
                        $selectActualPlayerFetch = sqlsrv_fetch_array($selectActualPlayerQuery);

                        // get CustomerID
                        $getActualPlrCID = $selectActualPlayerFetch['CustomerID'];

                        // register profile
                        $createPlayerProfile = "INSERT INTO WEB_User_Info (CustomerID, name, last_name) VALUES ('$getActualPlrCID','$inputName', '$inputLastName')";
                        $createPlayerProfileParam = array();
                        $createPlayerProfileQuery = sqlsrv_query($conn, $createPlayerProfile, $createPlayerProfileParam);

                        // conditions
                        if ($inputIsDev == '-1') {
                            $inputIsDev = '0';
                        }

                        if (empty($inputGC)) {
                            $inputGC = '0';
                        } else if (empty($inputGD)) {
                            $inputGD = '0';
                        }

                        // update Accounts
                        $updateActualPlrAcc = "UPDATE Accounts SET IsDeveloper = '$inputIsDev' WHERE CustomerID = '$getActualPlrCID'";
                        $updateActualPlrAccParam = array();
                        $updateActualPlrAccQuery = sqlsrv_query($conn, $updateActualPlrAcc, $updateActualPlrAccParam);

                        // update UsersData
                        $updateActualPlrAccData = "UPDATE UsersData SET IsDeveloper = '$inputIsDev', GamePoints = '$inputGC', GameDollars = '$inputGD' WHERE CustomerID = '$getActualPlrCID'";
                        $updateActualPlrAccDataParam = array();
                        $updateActualPlrAccDataQuery = sqlsrv_query($conn, $updateActualPlrAccData, $updateActualPlrAccDataParam);

                        if ($registerUserQuery) {
                            unset($inputEmail, $inputPassword, $inputRePassword, $inputName, $inputLastName);
                            $returnSuccess = S_REGISTERED_SUCCESS_A;
                        } else {
                            $returnError = E_REGISTER_ERROR;
                        }
                    }
                }
                ?>
                <?php if (isset($returnWarning)) { ?>
                    <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                <?php } elseif (isset($returnSuccess)) { ?>
                    <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                <?php } ?>
                <form role="form" method="post" enctype="multipart/form-data">
                    <h4><?php echo T_ACCESS_INFO; ?></h4>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-envelope"></i></span>
                        <input type="email" name="input_email" class="form-control" placeholder="<?php echo P_EMAIL_ADRESS; ?>" value="<?php if (isset($inputEmail)) echo $inputEmail; ?>">
                    </div>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-key"></i></span>
                        <input type="text" name="input_password" class="form-control" placeholder="<?php echo P_PASSWORD; ?>">
                    </div>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-key"></i></span>
                        <input type="text" name="input_repass" class="form-control" placeholder="<?php echo RT_CONFIRMPASS; ?>">
                    </div>
                    <hr>
                    <h4><?php echo T_PERSONAL_INFO; ?></h4>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                        <input type="text" name="input_name" class="form-control" placeholder="<?php echo RT_YOURNAME_TEXT; ?>" value="<?php if (isset($inputName)) echo $inputName; ?>">
                    </div>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                        <input type="text" name="input_lastname" class="form-control" placeholder="<?php echo RT_LAST_NAME_TEXT; ?>" value="<?php if (isset($inputLastName)) echo $inputLastName; ?>">
                    </div>
                    <hr>
                    <h4><?php echo T_GAME_PERM_INFO; ?></h4>
                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-star"></i></span>
                            <select name="input_isdev" class="form-control">
                                <option value="-1"><?php echo T_SELECTANPOST; ?></option>
                                <option value="126">DEV</option>
                                <option value="0">Player</option>
                            </select>
                        </div>
                        <span class="help-block"><small class="text-red"><?php echo T_HB_SELECTPOST; ?></small></span>
                    </div>

                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-money"></i></span>
                            <input class="form-control" name="input_gc" placeholder="<?php echo T_PH_CREATEPLR_GC; ?>">
                        </div>
                        <span class="help-block"><small class="text-red"><?php echo T_HB_GC; ?></small></span>
                    </div>

                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-money"></i></span>
                            <input class="form-control" name="input_gd" placeholder="<?php echo T_PH_CREATEPLR_GD; ?>">
                        </div>
                        <span class="help-block"><small class="text-red"><?php echo T_HB_GD; ?></small></span>
                    </div>

                    <input type="submit" name="btn_register" class="btn btn-success btn-block" value="<?php echo B_CREATE; ?>">
                </form>
            </div>
        </div>
    </div>
</div>
