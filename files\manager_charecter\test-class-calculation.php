<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-calculator"></i> ทดสอบการคำนวณคลาสตัวละคร
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">เปรียบเทียบการคำนวณคลาส</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    หน้านี้ใช้เปรียบเทียบการคำนวณคลาสแบบเก่า (bit operations) กับแบบใหม่ (cabalstyle function)
                </div>
                
                <?php
                // ดึงข้อมูล Style ที่แตกต่างกันจากฐานข้อมูล
                $sql = "SELECT DISTINCT TOP 20 Style, Name, LEV FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY Style";
                $result = sqlsrv_query($conn, $sql);
                
                if ($result) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-bordered table-hover">';
                    echo '<thead class="thead-dark">';
                    echo '<tr>';
                    echo '<th>Style Value</th>';
                    echo '<th>ชื่อตัวละคร</th>';
                    echo '<th>Level</th>';
                    echo '<th>วิธีเก่า (Bit Operations)</th>';
                    echo '<th>วิธีใหม่ (cabalstyle)</th>';
                    echo '<th>ตรงกันหรือไม่</th>';
                    echo '</tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    
                    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $style = $row['Style'];
                        $name = htmlspecialchars($row['Name']);
                        $level = $row['LEV'];
                        
                        // วิธีเก่า - Bit Operations
                        function getClassNameOld($style) {
                            if ($style & 1) return 'Warrior';
                            if ($style & 2) return 'Blader';
                            if ($style & 4) return 'Wizard';
                            if ($style & 8) return 'Force Archer';
                            if ($style & 16) return 'Force Shielder';
                            if ($style & 32) return 'Force Blader';
                            return 'Unknown';
                        }
                        
                        // วิธีใหม่ - cabalstyle function
                        $classInfo = $userLogin->cabalstyle($style);
                        $classNameNew = $classInfo['Class_Name'] ?? 'Unknown';
                        
                        // แปลงชื่อคลาสจากรหัสเป็นชื่อเต็ม
                        $fullClassNames = [
                            'WA' => 'Warrior',
                            'BL' => 'Blader', 
                            'WI' => 'Wizard',
                            'FA' => 'Force Archer',
                            'FS' => 'Force Shielder',
                            'FB' => 'Force Blader',
                            'GL' => 'Gladiator',
                            'FG' => 'Force Gunner',
                            'DM' => 'Dark Mage'
                        ];
                        
                        $classNameNewFull = $fullClassNames[$classNameNew] ?? $classNameNew;
                        $classNameOld = getClassNameOld($style);
                        
                        // เปรียบเทียบ
                        $isMatch = ($classNameOld === $classNameNewFull);
                        $matchClass = $isMatch ? 'table-success' : 'table-danger';
                        $matchIcon = $isMatch ? '<i class="fal fa-check text-success"></i>' : '<i class="fal fa-times text-danger"></i>';
                        
                        echo "<tr class='$matchClass'>";
                        echo "<td><code>$style</code></td>";
                        echo "<td>$name</td>";
                        echo "<td>$level</td>";
                        echo "<td><strong>$classNameOld</strong></td>";
                        echo "<td><strong>$classNameNewFull</strong> <small class='text-muted'>($classNameNew)</small></td>";
                        echo "<td>$matchIcon</td>";
                        echo "</tr>";
                    }
                    
                    echo '</tbody>';
                    echo '</table>';
                    echo '</div>';
                } else {
                    echo '<div class="alert alert-danger">ไม่สามารถดึงข้อมูลได้</div>';
                }
                ?>
                
                <h5 class="mt-4">🔍 การทดสอบค่า Style เฉพาะ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>ทดสอบค่า Style ตัวอย่าง</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $testStyles = [1, 2, 3, 4, 5, 6, 7, 8, 9, 16, 32, 64, 128, 256, 512, 1024];
                                
                                echo '<table class="table table-sm">';
                                echo '<thead><tr><th>Style</th><th>Binary</th><th>เก่า</th><th>ใหม่</th></tr></thead>';
                                echo '<tbody>';
                                
                                foreach ($testStyles as $testStyle) {
                                    $binary = decbin($testStyle);
                                    
                                    // วิธีเก่า
                                    function getClassOld($style) {
                                        if ($style & 1) return 'Warrior';
                                        if ($style & 2) return 'Blader';
                                        if ($style & 4) return 'Wizard';
                                        if ($style & 8) return 'Force Archer';
                                        if ($style & 16) return 'Force Shielder';
                                        if ($style & 32) return 'Force Blader';
                                        return 'Unknown';
                                    }
                                    
                                    // วิธีใหม่
                                    $classInfo = $userLogin->cabalstyle($testStyle);
                                    $newClass = $classInfo['Class_Name'] ?? 'Unknown';
                                    $fullNames = [
                                        'WA' => 'Warrior', 'BL' => 'Blader', 'WI' => 'Wizard',
                                        'FA' => 'Force Archer', 'FS' => 'Force Shielder', 'FB' => 'Force Blader',
                                        'GL' => 'Gladiator', 'FG' => 'Force Gunner', 'DM' => 'Dark Mage'
                                    ];
                                    $newClassFull = $fullNames[$newClass] ?? $newClass;
                                    
                                    $oldClass = getClassOld($testStyle);
                                    
                                    $rowClass = ($oldClass === $newClassFull) ? 'table-success' : 'table-warning';
                                    
                                    echo "<tr class='$rowClass'>";
                                    echo "<td><code>$testStyle</code></td>";
                                    echo "<td><code>$binary</code></td>";
                                    echo "<td>$oldClass</td>";
                                    echo "<td>$newClassFull</td>";
                                    echo "</tr>";
                                }
                                
                                echo '</tbody></table>';
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>คำอธิบายการคำนวณ</h6>
                            </div>
                            <div class="card-body">
                                <h6>🔧 วิธีเก่า (Bit Operations):</h6>
                                <ul class="small">
                                    <li><code>Style & 1</code> = Warrior</li>
                                    <li><code>Style & 2</code> = Blader</li>
                                    <li><code>Style & 4</code> = Wizard</li>
                                    <li><code>Style & 8</code> = Force Archer</li>
                                    <li><code>Style & 16</code> = Force Shielder</li>
                                    <li><code>Style & 32</code> = Force Blader</li>
                                </ul>
                                
                                <h6>⚡ วิธีใหม่ (cabalstyle):</h6>
                                <ul class="small">
                                    <li><code>battleStyle = Style & 7</code> (3 บิตแรก)</li>
                                    <li><code>extendedBattleStyle = (Style >> 23) & 1</code> (บิตที่ 23)</li>
                                    <li><code>classIndex = battleStyle | (extendedBattleStyle << 3)</code></li>
                                    <li>รองรับคลาสใหม่: Gladiator, Force Gunner, Dark Mage</li>
                                </ul>
                                
                                <div class="alert alert-warning alert-sm mt-3">
                                    <strong>หมายเหตุ:</strong> วิธีใหม่จะแม่นยำกว่าและรองรับคลาสเพิ่มเติม
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 สรุปการเปรียบเทียบ</h5>
                <?php
                // นับจำนวนที่ตรงกันและไม่ตรงกัน
                $sql = "SELECT Style FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                $result = sqlsrv_query($conn, $sql);
                
                $totalCount = 0;
                $matchCount = 0;
                $mismatchCount = 0;
                $classDistributionOld = [];
                $classDistributionNew = [];
                
                if ($result) {
                    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $style = $row['Style'];
                        $totalCount++;
                        
                        // วิธีเก่า
                        function getOldClass($style) {
                            if ($style & 1) return 'Warrior';
                            if ($style & 2) return 'Blader';
                            if ($style & 4) return 'Wizard';
                            if ($style & 8) return 'Force Archer';
                            if ($style & 16) return 'Force Shielder';
                            if ($style & 32) return 'Force Blader';
                            return 'Unknown';
                        }
                        
                        // วิธีใหม่
                        $classInfo = $userLogin->cabalstyle($style);
                        $newClass = $classInfo['Class_Name'] ?? 'Unknown';
                        $fullNames = [
                            'WA' => 'Warrior', 'BL' => 'Blader', 'WI' => 'Wizard',
                            'FA' => 'Force Archer', 'FS' => 'Force Shielder', 'FB' => 'Force Blader',
                            'GL' => 'Gladiator', 'FG' => 'Force Gunner', 'DM' => 'Dark Mage'
                        ];
                        $newClassFull = $fullNames[$newClass] ?? $newClass;
                        
                        $oldClass = getOldClass($style);
                        
                        // นับการกระจาย
                        $classDistributionOld[$oldClass] = ($classDistributionOld[$oldClass] ?? 0) + 1;
                        $classDistributionNew[$newClassFull] = ($classDistributionNew[$newClassFull] ?? 0) + 1;
                        
                        if ($oldClass === $newClassFull) {
                            $matchCount++;
                        } else {
                            $mismatchCount++;
                        }
                    }
                }
                
                $matchPercentage = $totalCount > 0 ? ($matchCount / $totalCount) * 100 : 0;
                $mismatchPercentage = $totalCount > 0 ? ($mismatchCount / $totalCount) * 100 : 0;
                ?>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-primary"><?php echo number_format($totalCount); ?></h5>
                                <p class="card-text">ตัวละครทั้งหมด</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-success"><?php echo number_format($matchCount); ?> (<?php echo number_format($matchPercentage, 1); ?>%)</h5>
                                <p class="card-text">ผลลัพธ์ตรงกัน</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-danger"><?php echo number_format($mismatchCount); ?> (<?php echo number_format($mismatchPercentage, 1); ?>%)</h5>
                                <p class="card-text">ผลลัพธ์ไม่ตรงกัน</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fal fa-check-circle"></i> สรุป:</h6>
                    <ul class="mb-0">
                        <li>✅ ระบบใหม่ใช้ฟังก์ชัน <code>cabalstyle()</code> ที่แม่นยำกว่า</li>
                        <li>✅ รองรับคลาสใหม่: Gladiator, Force Gunner, Dark Mage</li>
                        <li>✅ การคำนวณใช้ bit manipulation ที่ถูกต้องตามโครงสร้างข้อมูล</li>
                        <li>✅ แก้ไขปัญหาการแสดงคลาสซ้ำในสถิติ</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
</style>
