<?php $user->restrictionUser(true, $conn); ?>

<?php
// ฟังก์ชันสำรองสำหรับคำนวณคลาสจาก Style
function getClassNameFromStyle($style) {
    // คำนวณคลาสแบบเดียวกับ PHP cabalstyle function
    $battleStyle = $style & 7; // 3 บิตแรก
    $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
    $classIndex = $battleStyle | ($extendedBattleStyle << 3);

    $classNames = [
        1 => 'WA', // Warrior
        2 => 'BL', // Blader
        3 => 'WI', // Wizard
        4 => 'FA', // Force Archer
        5 => 'FS', // Force Shielder
        6 => 'FB', // Force Blader
        7 => 'GL', // Gladiator
        8 => 'FG', // Force Gunner
        9 => 'DM'  // Dark Mage
    ];

    return $classNames[$classIndex] ?? 'Unknown';
}

// ฟังก์ชันสำหรับ Honor Title ตาม Nation และ Honor Class
function getHonorTitle($honorClass, $nation) {
    $titles = [
        11 => [
            'Capella' => 'Praetor',
            'Procyon' => 'Knight',
            'Neutral' => 'Vagabond'
        ],
        12 => [
            'Capella' => 'Praetor of Silence',
            'Procyon' => 'Knight of Dawn',
            'Neutral' => 'Disciple'
        ],
        13 => [
            'Capella' => 'Praetor of Insight',
            'Procyon' => 'Knight of Fighting Spirit',
            'Neutral' => 'Spirit Ascetic'
        ],
        14 => [
            'Capella' => 'Praetor of Tolerance',
            'Procyon' => 'Knight of Passion',
            'Neutral' => 'Spirit Ascetic'
        ],
        15 => [
            'Capella' => 'Praetor of Glory',
            'Procyon' => 'Knight of Protection',
            'Neutral' => 'Spirit Ascetic'
        ],
        16 => [
            'Capella' => 'Praetor of Faith',
            'Procyon' => 'Knight of Valor',
            'Neutral' => 'Spirit Ascetic'
        ],
        17 => [
            'Capella' => 'Praetor of Will',
            'Procyon' => 'Knight of Devotion',
            'Neutral' => 'Spirit Ascetic'
        ],
        18 => [
            'Capella' => 'Praetor of Truth',
            'Procyon' => 'Knight of Judgment',
            'Neutral' => 'Spirit Ascetic'
        ],
        19 => [
            'Capella' => 'Praetor of Brilliance',
            'Procyon' => 'Knight of Tempest',
            'Neutral' => 'Spirit Ascetic'
        ],
        20 => [
            'Capella' => 'The Quaestor',
            'Procyon' => 'The Sovereign',
            'Neutral' => 'Spirit Ascetic'
        ]
    ];

    return $titles[$honorClass][$nation] ?? '';
}

// ฟังก์ชันสำหรับ Honor Stats ตาม Honor Class
function getHonorStats($honorClass) {
    $stats = [
        11 => '[HP+60]',
        12 => '[HP+120]',
        13 => '[HP+120, Resist Critical Damage+6%]',
        14 => '[HP+180, Resist Critical Damage+6%, Resist Crit Rate+3%]',
        15 => '[HP+180, Resist Critical Damage+18%, Resist Crit Rate+3%]',
        16 => '[HP+240, Resist Critical Damage+18%, Resist Crit Rate+6%, Resist Root+12%]',
        17 => '[HP+240, Resist Critical Damage+18%, Resist Crit Rate+6%, Resist Root+12%, Resist Stun+12%]',
        18 => '[HP+360, Resist Critical Damage+24%, Resist Crit Rate+8%, Resist Root+12%, Resist Stun+12%, Resist Knockback+12%]',
        19 => '[HP+480, Resist Critical Damage+24%, Resist Crit Rate+8%, Resist Root+12%, Resist Stun+12%, Resist Knockback+12%, Resist Down+12%]',
        20 => '[HP+600, Resist Critical Damage+36%, Resist Crit Rate+12%, Resist Root+18%, Resist Stun+18%, Resist Knockback+18%, Resist Down+18%]'
    ];

    return $stats[$honorClass] ?? '';
}

// Function to get character statistics
function getCharacterStatistics($conn, $userLogin) {
    $stats = array();
    
    try {
        // Total characters
        $sql = "SELECT COUNT(*) as total_characters FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_characters'] = $row['total_characters'] ?? 0;
        }
        
        // Online characters
        $sql = "SELECT COUNT(*) as online_characters FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE ChannelIdx > 0 AND Login = 1";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['online_characters'] = $row['online_characters'] ?? 0;
        }
        
        // Characters created today
        $sql = "SELECT COUNT(*) as created_today FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['created_today'] = $row['created_today'] ?? 0;
        }
        
        // Characters logged in today
        $sql = "SELECT COUNT(*) as login_today FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CAST(LoginTime AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['login_today'] = $row['login_today'] ?? 0;
        }
        
        // Average level
        $sql = "SELECT AVG(LEV) as avg_level FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE LEV > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['avg_level'] = round($row['avg_level'] ?? 0, 1);
        }
        
        // Total Alz
        $sql = "SELECT SUM(CAST(Alz AS BIGINT)) as total_alz FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_alz'] = $row['total_alz'] ?? 0;
        }
        
        // Total playtime
        $sql = "SELECT SUM(PlayTime) as total_playtime FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_playtime'] = $row['total_playtime'] ?? 0;
        }
        
        // Class distribution - ใช้ PHP เพื่อคำนวณคลาสอย่างถูกต้อง
        $sql = "SELECT Style, COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table GROUP BY Style ORDER BY count DESC";
        $result = sqlsrv_query($conn, $sql);
        $classDistribution = array();

        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                // ใช้ฟังก์ชัน cabalstyle หรือฟังก์ชันสำรอง
                if ($userLogin && method_exists($userLogin, 'cabalstyle')) {
                    $classInfo = $userLogin->cabalstyle($row['Style']);
                    $className = $classInfo['Class_Name'] ?? 'Unknown';
                } else {
                    // ฟังก์ชันสำรองกรณีไม่มี userLogin
                    $className = getClassNameFromStyle($row['Style']);
                }

                // แปลงชื่อคลาสจากรหัสเป็นชื่อเต็ม
                $fullClassNames = [
                    'WA' => 'Warrior',
                    'BL' => 'Blader',
                    'WI' => 'Wizard',
                    'FA' => 'Force Archer',
                    'FS' => 'Force Shielder',
                    'FB' => 'Force Blader',
                    'GL' => 'Gladiator',
                    'FG' => 'Force Gunner',
                    'DM' => 'Dark Mage'
                ];

                $fullClassName = $fullClassNames[$className] ?? $className;

                // รวมจำนวนตามคลาส
                if (isset($classDistribution[$fullClassName])) {
                    $classDistribution[$fullClassName] += $row['count'];
                } else {
                    $classDistribution[$fullClassName] = $row['count'];
                }
            }

            // แปลงเป็น array format เดิม
            foreach ($classDistribution as $className => $count) {
                $stats['class_distribution'][] = [
                    'class_name' => $className,
                    'count' => $count
                ];
            }

            // เรียงลำดับตามจำนวน
            usort($stats['class_distribution'], function($a, $b) {
                return $b['count'] - $a['count'];
            });
        }
        
        // Level distribution
        $sql = "SELECT 
                    CASE 
                        WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
                        WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
                        WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
                        WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
                        WHEN LEV > 200 THEN '200+'
                        ELSE '0'
                    END as level_range,
                    COUNT(*) as count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                GROUP BY 
                    CASE 
                        WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
                        WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
                        WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
                        WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
                        WHEN LEV > 200 THEN '200+'
                        ELSE '0'
                    END
                ORDER BY count DESC";
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['level_distribution'][] = $row;
            }
        }

        // Honor Class distribution - คำนวณจาก Reputation Points ตาม XML config
        $sql = "SELECT
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                        ELSE 'No Class'
                    END as honor_class,
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 0
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 0
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 1
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 2
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 3
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 4
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 5
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 6
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 7
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 8
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 9
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 10
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 11
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 12
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 13
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 14
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 15
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 16
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 17
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 18
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 19
                        WHEN Reputation >= 2000000000 THEN 20
                        ELSE 0
                    END as honor_value,
                    COUNT(*) as count,
                    AVG(CAST(Reputation AS BIGINT)) as avg_reputation_points
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                GROUP BY
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                        WHEN Reputation >= 2000000000 THEN 'Class 20'
                        ELSE 'No Class'
                    END,
                    CASE
                        WHEN Reputation IS NULL OR Reputation < -10000 THEN 0
                        WHEN Reputation >= -10000 AND Reputation < 10000 THEN 0
                        WHEN Reputation >= 10000 AND Reputation < 20000 THEN 1
                        WHEN Reputation >= 20000 AND Reputation < 40000 THEN 2
                        WHEN Reputation >= 40000 AND Reputation < 80000 THEN 3
                        WHEN Reputation >= 80000 AND Reputation < 150000 THEN 4
                        WHEN Reputation >= 150000 AND Reputation < 300000 THEN 5
                        WHEN Reputation >= 300000 AND Reputation < 600000 THEN 6
                        WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 7
                        WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 8
                        WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 9
                        WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 10
                        WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 11
                        WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 12
                        WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 13
                        WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 14
                        WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 15
                        WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 16
                        WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 17
                        WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 18
                        WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 19
                        WHEN Reputation >= 2000000000 THEN 20
                        ELSE 0
                    END
                ORDER BY honor_value";
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['honor_distribution'][] = [
                    'honor_class' => $row['honor_class'],
                    'honor_value' => $row['honor_value'],
                    'count' => $row['count'],
                    'avg_reputation_points' => $row['avg_reputation_points']
                ];
            }
        }

        // Nation distribution
        $sql = "SELECT
                    CASE
                        WHEN Nation = 0 THEN 'Neutral'
                        WHEN Nation = 1 THEN 'Capella'
                        WHEN Nation = 2 THEN 'Procyon'
                        ELSE 'GM'
                    END as nation_name,
                    Nation as nation_value,
                    COUNT(*) as count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                GROUP BY Nation
                ORDER BY Nation";
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['nation_distribution'][] = [
                    'nation_name' => $row['nation_name'],
                    'nation_value' => $row['nation_value'],
                    'count' => $row['count']
                ];
            }
        }

        // Character Analysis - เพิ่มการวิเคราะห์ตัวละครครบถ้วน

        // 1. Level Analysis
        $sql = "SELECT
                    COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                    COUNT(CASE WHEN LEV BETWEEN 51 AND 100 THEN 1 END) as level_51_100,
                    COUNT(CASE WHEN LEV BETWEEN 101 AND 150 THEN 1 END) as level_101_150,
                    COUNT(CASE WHEN LEV BETWEEN 151 AND 200 THEN 1 END) as level_151_200,
                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                    MAX(LEV) as max_level,
                    MIN(LEV) as min_level
                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['level_analysis'] = $row;
        }

        // 2. Wealth Analysis (Alz)
        $sql = "SELECT
                    COUNT(CASE WHEN Alz = 0 THEN 1 END) as no_alz,
                    COUNT(CASE WHEN Alz BETWEEN 1 AND 1000000 THEN 1 END) as alz_1m,
                    COUNT(CASE WHEN Alz BETWEEN 1000001 AND 10000000 THEN 1 END) as alz_10m,
                    COUNT(CASE WHEN Alz BETWEEN 10000001 AND 100000000 THEN 1 END) as alz_100m,
                    COUNT(CASE WHEN Alz BETWEEN 100000001 AND 1000000000 THEN 1 END) as alz_1b,
                    COUNT(CASE WHEN Alz > 1000000000 THEN 1 END) as alz_1b_plus,
                    AVG(CAST(Alz AS BIGINT)) as avg_alz,
                    MAX(Alz) as max_alz,
                    SUM(CAST(Alz AS BIGINT)) as total_alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['wealth_analysis'] = $row;
        }

        // 3. Activity Analysis (PlayTime)
        $sql = "SELECT
                    COUNT(CASE WHEN PlayTime = 0 THEN 1 END) as never_played,
                    COUNT(CASE WHEN PlayTime BETWEEN 1 AND 3600 THEN 1 END) as played_1h,
                    COUNT(CASE WHEN PlayTime BETWEEN 3601 AND 36000 THEN 1 END) as played_10h,
                    COUNT(CASE WHEN PlayTime BETWEEN 36001 AND 360000 THEN 1 END) as played_100h,
                    COUNT(CASE WHEN PlayTime BETWEEN 360001 AND 3600000 THEN 1 END) as played_1000h,
                    COUNT(CASE WHEN PlayTime > 3600000 THEN 1 END) as played_1000h_plus,
                    AVG(CAST(PlayTime AS FLOAT)) as avg_playtime,
                    MAX(PlayTime) as max_playtime,
                    SUM(CAST(PlayTime AS BIGINT)) as total_playtime
                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['activity_analysis'] = $row;
        }

        // 4. Character Creation Analysis
        $sql = "SELECT
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 1 THEN 1 END) as created_today,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 7 THEN 1 END) as created_week,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 30 THEN 1 END) as created_month,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 365 THEN 1 END) as created_year,
                    COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) > 365 THEN 1 END) as created_old,
                    CONVERT(VARCHAR, MIN(CreateDate), 103) as oldest_character,
                    CONVERT(VARCHAR, MAX(CreateDate), 103) as newest_character
                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['creation_analysis'] = $row;
        }

        // 5. World Distribution
        $sql = "SELECT
                    WorldIdx,
                    COUNT(*) as count,
                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                GROUP BY WorldIdx
                ORDER BY WorldIdx";
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats['world_distribution'][] = $row;
            }
        }

        // 6. Character Progression Analysis
        $sql = "SELECT
                    COUNT(CASE WHEN LEV >= 1 AND LEV <= 10 THEN 1 END) as beginner,
                    COUNT(CASE WHEN LEV >= 11 AND LEV <= 50 THEN 1 END) as novice,
                    COUNT(CASE WHEN LEV >= 51 AND LEV <= 100 THEN 1 END) as intermediate,
                    COUNT(CASE WHEN LEV >= 101 AND LEV <= 150 THEN 1 END) as advanced,
                    COUNT(CASE WHEN LEV >= 151 AND LEV <= 200 THEN 1 END) as expert,
                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as master
                FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['progression_analysis'] = $row;
        }

        // Top players by level
        $sql = "SELECT TOP 10 Name, LEV, Alz, PlayTime, Reputation, Nation FROM [".DATABASE_SV."].[dbo].cabal_character_table
                ORDER BY LEV DESC, Alz DESC";
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                // เพิ่มข้อมูล Honor Class และ Nation - คำนวณจาก Reputation Points
                $reputation = $row['Reputation'];
                $honorClass = '';
                $honorValue = 0;

                if ($reputation === null || $reputation < -10000) {
                    $honorClass = 'No Class';
                    $honorValue = 0;
                } elseif ($reputation >= -10000 && $reputation < 10000) {
                    $honorClass = 'Class 0';
                    $honorValue = 0;
                } elseif ($reputation >= 10000 && $reputation < 20000) {
                    $honorClass = 'Class 1';
                    $honorValue = 1;
                } elseif ($reputation >= 20000 && $reputation < 40000) {
                    $honorClass = 'Class 2';
                    $honorValue = 2;
                } elseif ($reputation >= 40000 && $reputation < 80000) {
                    $honorClass = 'Class 3';
                    $honorValue = 3;
                } elseif ($reputation >= 80000 && $reputation < 150000) {
                    $honorClass = 'Class 4';
                    $honorValue = 4;
                } elseif ($reputation >= 150000 && $reputation < 300000) {
                    $honorClass = 'Class 5';
                    $honorValue = 5;
                } elseif ($reputation >= 300000 && $reputation < 600000) {
                    $honorClass = 'Class 6';
                    $honorValue = 6;
                } elseif ($reputation >= 600000 && $reputation < 1200000) {
                    $honorClass = 'Class 7';
                    $honorValue = 7;
                } elseif ($reputation >= 1200000 && $reputation < 2500000) {
                    $honorClass = 'Class 8';
                    $honorValue = 8;
                } elseif ($reputation >= 2500000 && $reputation < 5000000) {
                    $honorClass = 'Class 9';
                    $honorValue = 9;
                } elseif ($reputation >= 5000000 && $reputation < 10000000) {
                    $honorClass = 'Class 10';
                    $honorValue = 10;
                } elseif ($reputation >= 10000000 && $reputation < 20000000) {
                    $honorClass = 'Class 11';
                    $honorValue = 11;
                } elseif ($reputation >= 20000000 && $reputation < 40000000) {
                    $honorClass = 'Class 12';
                    $honorValue = 12;
                } elseif ($reputation >= 40000000 && $reputation < 80000000) {
                    $honorClass = 'Class 13';
                    $honorValue = 13;
                } elseif ($reputation >= 80000000 && $reputation < 150000000) {
                    $honorClass = 'Class 14';
                    $honorValue = 14;
                } elseif ($reputation >= 150000000 && $reputation < 250000000) {
                    $honorClass = 'Class 15';
                    $honorValue = 15;
                } elseif ($reputation >= 250000000 && $reputation < 500000000) {
                    $honorClass = 'Class 16';
                    $honorValue = 16;
                } elseif ($reputation >= 500000000 && $reputation < 900000000) {
                    $honorClass = 'Class 17';
                    $honorValue = 17;
                } elseif ($reputation >= 900000000 && $reputation < 1400000000) {
                    $honorClass = 'Class 18';
                    $honorValue = 18;
                } elseif ($reputation >= 1400000000 && $reputation < 2000000000) {
                    $honorClass = 'Class 19';
                    $honorValue = 19;
                } elseif ($reputation >= 2000000000) {
                    $honorClass = 'Class 20';
                    $honorValue = 20;
                } else {
                    $honorClass = 'No Class';
                    $honorValue = 0;
                }

                $nation = '';
                switch($row['Nation']) {
                    case 0: $nation = 'Neutral'; break;
                    case 1: $nation = 'Capella'; break;
                    case 2: $nation = 'Procyon'; break;
                    default: $nation = 'GM';
                }

                $stats['top_players'][] = [
                    'Name' => $row['Name'],
                    'LEV' => $row['LEV'],
                    'Alz' => $row['Alz'],
                    'PlayTime' => $row['PlayTime'],
                    'HonorClass' => $honorClass,
                    'HonorValue' => $honorValue,
                    'ReputationPoints' => $reputation,
                    'Nation' => $nation
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Character statistics error: " . $e->getMessage());
    }
    
    return $stats;
}

// Get statistics
$characterStats = getCharacterStatistics($conn, $userLogin);
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-bar"></i> สถิติระบบตัวละคร
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_charecter/character-monitor" class="btn btn-warning btn-sm">
            <i class="fal fa-radar"></i> ตรวจสอบสด
        </a>
        <a href="?url=manager_charecter/character-analytics" class="btn btn-info btn-sm">
            <i class="fal fa-analytics"></i> การวิเคราะห์
        </a>
        <button class="btn btn-success btn-sm" onclick="refreshStats()">
            <i class="fal fa-sync"></i> รีเฟรช
        </button>
    </div>
</div>

<div class="row">
    <!-- Character Overview Stats -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-users text-primary"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">ตัวละครทั้งหมด</div>
                        <div class="h3 mb-0"><?php echo number_format($characterStats['total_characters'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-circle text-success"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">ออนไลน์ขณะนี้</div>
                        <div class="h3 mb-0"><?php echo number_format($characterStats['online_characters'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-level-up text-warning"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">เลเวลเฉลี่ย</div>
                        <div class="h3 mb-0"><?php echo $characterStats['avg_level'] ?? 0; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-coins text-info"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">Alz ทั้งหมด</div>
                        <div class="h3 mb-0"><?php echo number_format($characterStats['total_alz'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Activity -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมวันนี้</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-primary"><?php echo number_format($characterStats['created_today'] ?? 0); ?></div>
                            <div class="text-muted">ตัวละครใหม่</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-success"><?php echo number_format($characterStats['login_today'] ?? 0); ?></div>
                            <div class="text-muted">เข้าเกมวันนี้</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Playtime Stats -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">สถิติเวลาเล่น</h3>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="h2 text-info"><?php echo number_format(($characterStats['total_playtime'] ?? 0) / 3600, 0); ?></div>
                    <div class="text-muted">ชั่วโมงรวม</div>
                    <hr>
                    <div class="h4 text-warning"><?php echo number_format((($characterStats['total_playtime'] ?? 0) / 3600) / max($characterStats['total_characters'], 1), 1); ?></div>
                    <div class="text-muted">ชั่วโมงเฉลี่ยต่อตัวละคร</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Class Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-users-class text-primary"></i> การกระจายตามคลาสตัวละคร
                </h3>
                <div class="card-toolbar">
                    <small class="text-muted">แสดงสัดส่วนผู้เล่นในแต่ละคลาส</small>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info alert-sm mb-3">
                    <i class="fal fa-info-circle"></i>
                    <strong>คำอธิบาย:</strong> แสดงจำนวนและเปอร์เซ็นต์ของตัวละครในแต่ละคลาส เพื่อดูความนิยมของคลาสต่างๆ
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fal fa-sword"></i> คลาสตัวละคร</th>
                                <th><i class="fal fa-users"></i> จำนวนผู้เล่น</th>
                                <th><i class="fal fa-percentage"></i> สัดส่วน</th>
                                <th><i class="fal fa-chart-bar"></i> กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($characterStats['class_distribution'])): ?>
                                <?php foreach ($characterStats['class_distribution'] as $class): ?>
                                    <?php
                                    $percentage = ($characterStats['total_characters'] > 0) ?
                                        ($class['count'] / $characterStats['total_characters']) * 100 : 0;

                                    // กำหนดสีตามคลาส
                                    $classColors = [
                                        'Warrior' => 'danger',
                                        'Blader' => 'warning',
                                        'Wizard' => 'info',
                                        'Force Archer' => 'success',
                                        'Force Shielder' => 'primary',
                                        'Force Blader' => 'secondary'
                                    ];
                                    $colorClass = $classColors[$class['class_name']] ?? 'dark';

                                    // กำหนดไอคอนตามคลาส
                                    $classIcons = [
                                        'Warrior' => 'fal fa-sword',
                                        'Blader' => 'fal fa-swords',
                                        'Wizard' => 'fal fa-magic',
                                        'Force Archer' => 'fal fa-bow-arrow',
                                        'Force Shielder' => 'fal fa-shield',
                                        'Force Blader' => 'fal fa-blade'
                                    ];
                                    $iconClass = $classIcons[$class['class_name']] ?? 'fal fa-user';
                                    ?>
                                    <tr>
                                        <td>
                                            <i class="<?php echo $iconClass; ?> text-<?php echo $colorClass; ?>"></i>
                                            <strong><?php echo htmlspecialchars($class['class_name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $colorClass; ?>">
                                                <?php echo number_format($class['count']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($percentage, 1); ?>%</strong>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?php echo $colorClass; ?>"
                                                     role="progressbar"
                                                     style="width: <?php echo $percentage; ?>%"
                                                     title="<?php echo number_format($percentage, 1); ?>%">
                                                    <?php if ($percentage > 10): ?>
                                                        <?php echo number_format($percentage, 1); ?>%
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <!-- สรุปข้อมูล -->
                                <tr class="table-info">
                                    <td><strong><i class="fal fa-calculator"></i> รวมทั้งหมด</strong></td>
                                    <td>
                                        <span class="badge badge-dark">
                                            <?php echo number_format($characterStats['total_characters']); ?>
                                        </span>
                                    </td>
                                    <td><strong>100.0%</strong></td>
                                    <td>
                                        <small class="text-muted">
                                            คลาสที่นิยมที่สุด:
                                            <?php
                                            $mostPopular = $characterStats['class_distribution'][0] ?? null;
                                            echo $mostPopular ? $mostPopular['class_name'] : 'N/A';
                                            ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <tr><td colspan="4" class="text-center text-muted">ไม่มีข้อมูลการกระจายคลาส</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (isset($characterStats['class_distribution']) && !empty($characterStats['class_distribution'])): ?>
                <div class="mt-3">
                    <h6><i class="fal fa-lightbulb text-warning"></i> ข้อมูลเชิงลึก:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><i class="fal fa-crown text-warning"></i> คลาสยอดนิยม: <strong><?php echo $characterStats['class_distribution'][0]['class_name'] ?? 'N/A'; ?></strong></li>
                                <li><i class="fal fa-chart-line text-info"></i> ความหลากหลาย: <?php echo count($characterStats['class_distribution']); ?> คลาส</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><i class="fal fa-balance-scale text-success"></i> การกระจาย:
                                    <?php
                                    $maxPercent = ($characterStats['class_distribution'][0]['count'] / $characterStats['total_characters']) * 100;
                                    if ($maxPercent > 40) echo 'ไม่สมดุล';
                                    elseif ($maxPercent > 25) echo 'ค่อนข้างสมดุล';
                                    else echo 'สมดุลดี';
                                    ?>
                                </li>
                                <li><i class="fal fa-users text-primary"></i> เฉลี่ยต่อคลาส: <?php echo number_format($characterStats['total_characters'] / max(count($characterStats['class_distribution']), 1)); ?> คน</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Level Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-level-up text-success"></i> การกระจายตามระดับเลเวล
                </h3>
                <div class="card-toolbar">
                    <small class="text-muted">แสดงการกระจายผู้เล่นตามช่วงเลเวล</small>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info alert-sm mb-3">
                    <i class="fal fa-info-circle"></i>
                    <strong>คำอธิบาย:</strong> แสดงจำนวนผู้เล่นในแต่ละช่วงเลเวล เพื่อดูการกระจายของผู้เล่นใหม่และผู้เล่นเก่า
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fal fa-layer-group"></i> ช่วงเลเวล</th>
                                <th><i class="fal fa-users"></i> จำนวนผู้เล่น</th>
                                <th><i class="fal fa-percentage"></i> สัดส่วน</th>
                                <th><i class="fal fa-chart-bar"></i> กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($characterStats['level_distribution'])): ?>
                                <?php foreach ($characterStats['level_distribution'] as $level): ?>
                                    <?php
                                    $percentage = ($characterStats['total_characters'] > 0) ?
                                        ($level['count'] / $characterStats['total_characters']) * 100 : 0;

                                    // กำหนดสีตามช่วงเลเวล
                                    $levelColors = [
                                        '0' => 'secondary',
                                        '1-50' => 'success',
                                        '51-100' => 'info',
                                        '101-150' => 'warning',
                                        '151-200' => 'danger',
                                        '200+' => 'dark'
                                    ];
                                    $colorClass = $levelColors[$level['level_range']] ?? 'primary';

                                    // กำหนดไอคอนตามช่วงเลเวล
                                    $levelIcons = [
                                        '0' => 'fal fa-baby',
                                        '1-50' => 'fal fa-seedling',
                                        '51-100' => 'fal fa-leaf',
                                        '101-150' => 'fal fa-tree',
                                        '151-200' => 'fal fa-mountain',
                                        '200+' => 'fal fa-crown'
                                    ];
                                    $iconClass = $levelIcons[$level['level_range']] ?? 'fal fa-level-up';

                                    // กำหนดคำอธิบาย
                                    $levelDescriptions = [
                                        '0' => 'ยังไม่เริ่มเล่น',
                                        '1-50' => 'ผู้เล่นใหม่',
                                        '51-100' => 'ผู้เล่นระดับกลาง',
                                        '101-150' => 'ผู้เล่นมีประสบการณ์',
                                        '151-200' => 'ผู้เล่นระดับสูง',
                                        '200+' => 'ผู้เล่นระดับเทพ'
                                    ];
                                    $description = $levelDescriptions[$level['level_range']] ?? '';
                                    ?>
                                    <tr>
                                        <td>
                                            <i class="<?php echo $iconClass; ?> text-<?php echo $colorClass; ?>"></i>
                                            <strong><?php echo htmlspecialchars($level['level_range']); ?></strong>
                                            <br><small class="text-muted"><?php echo $description; ?></small>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $colorClass; ?>">
                                                <?php echo number_format($level['count']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($percentage, 1); ?>%</strong>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?php echo $colorClass; ?>"
                                                     role="progressbar"
                                                     style="width: <?php echo $percentage; ?>%"
                                                     title="<?php echo number_format($percentage, 1); ?>%">
                                                    <?php if ($percentage > 8): ?>
                                                        <?php echo number_format($percentage, 1); ?>%
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <!-- สรุปข้อมูล -->
                                <tr class="table-info">
                                    <td><strong><i class="fal fa-calculator"></i> รวมทั้งหมด</strong></td>
                                    <td>
                                        <span class="badge badge-dark">
                                            <?php echo number_format($characterStats['total_characters']); ?>
                                        </span>
                                    </td>
                                    <td><strong>100.0%</strong></td>
                                    <td>
                                        <small class="text-muted">
                                            ช่วงที่มีผู้เล่นมากที่สุด:
                                            <?php
                                            $mostPopularLevel = $characterStats['level_distribution'][0] ?? null;
                                            echo $mostPopularLevel ? $mostPopularLevel['level_range'] : 'N/A';
                                            ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <tr><td colspan="4" class="text-center text-muted">ไม่มีข้อมูลการกระจายเลเวล</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (isset($characterStats['level_distribution']) && !empty($characterStats['level_distribution'])): ?>
                <div class="mt-3">
                    <h6><i class="fal fa-lightbulb text-warning"></i> การวิเคราะห์:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <?php
                                $newbieCount = 0;
                                $veteranCount = 0;
                                foreach ($characterStats['level_distribution'] as $level) {
                                    if (in_array($level['level_range'], ['1-50', '51-100'])) {
                                        $newbieCount += $level['count'];
                                    }
                                    if (in_array($level['level_range'], ['151-200', '200+'])) {
                                        $veteranCount += $level['count'];
                                    }
                                }
                                $newbiePercent = ($characterStats['total_characters'] > 0) ? ($newbieCount / $characterStats['total_characters']) * 100 : 0;
                                $veteranPercent = ($characterStats['total_characters'] > 0) ? ($veteranCount / $characterStats['total_characters']) * 100 : 0;
                                ?>
                                <li><i class="fal fa-seedling text-success"></i> ผู้เล่นใหม่ (1-100): <strong><?php echo number_format($newbiePercent, 1); ?>%</strong></li>
                                <li><i class="fal fa-crown text-warning"></i> ผู้เล่นเก่า (151+): <strong><?php echo number_format($veteranPercent, 1); ?>%</strong></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><i class="fal fa-chart-line text-info"></i> ช่วงที่นิยม: <strong><?php echo $characterStats['level_distribution'][0]['level_range'] ?? 'N/A'; ?></strong></li>
                                <li><i class="fal fa-balance-scale text-primary"></i> การกระจาย:
                                    <?php
                                    if ($newbiePercent > 60) echo 'ผู้เล่นใหม่เยอะ';
                                    elseif ($veteranPercent > 40) echo 'ผู้เล่นเก่าเยอะ';
                                    else echo 'สมดุลดี';
                                    ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Reputation Class และ Nation Statistics -->
<div class="row">
    <!-- Honor Class Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-medal text-warning"></i> การกระจายตาม Honor Class
                </h3>
                <div class="card-toolbar">
                    <small class="text-muted">แสดงสัดส่วนผู้เล่นในแต่ละ Honor Class</small>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info alert-sm mb-3">
                    <i class="fal fa-info-circle"></i>
                    <strong>คำอธิบาย:</strong> Honor Class แสดงถึงระดับเกียรติยศของผู้เล่น จาก Class 1 ไปจนถึง Class 20 (ตาม <a href="https://cabal.fandom.com/wiki/Honor#Honor_Class" target="_blank">Cabal Wiki</a>)
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fal fa-medal"></i> Honor Class</th>
                                <th><i class="fal fa-star"></i> Avg Reputation</th>
                                <th><i class="fal fa-users"></i> จำนวนผู้เล่น</th>
                                <th><i class="fal fa-percentage"></i> สัดส่วน</th>
                                <th><i class="fal fa-chart-bar"></i> กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($characterStats['honor_distribution'])): ?>
                                <?php foreach ($characterStats['honor_distribution'] as $honor): ?>
                                    <?php
                                    $percentage = ($characterStats['total_characters'] > 0) ?
                                        ($honor['count'] / $characterStats['total_characters']) * 100 : 0;

                                    // กำหนดสีตาม Honor Class - จัดการ NULL และค่าผิดปกติ
                                    $honorValue = $honor['honor_value'];
                                    $honorClass = $honor['honor_class'];

                                    if ($honorValue == 0 || $honorClass == 'No Class') {
                                        $colorClass = 'secondary';
                                        $iconClass = 'fal fa-circle';
                                    } elseif ($honorValue >= 1 && $honorValue <= 5) {
                                        $colorClass = 'success';
                                        $iconClass = 'fal fa-star';
                                    } elseif ($honorValue >= 6 && $honorValue <= 10) {
                                        $colorClass = 'info';
                                        $iconClass = 'fal fa-star';
                                    } elseif ($honorValue >= 11 && $honorValue <= 15) {
                                        $colorClass = 'warning';
                                        $iconClass = 'fal fa-crown';
                                    } elseif ($honorValue >= 16 && $honorValue <= 19) {
                                        $colorClass = 'danger';
                                        $iconClass = 'fal fa-gem';
                                    } elseif ($honorValue == 20) {
                                        $colorClass = 'dark';
                                        $iconClass = 'fal fa-magic';
                                    } elseif ($honorValue > 20 || $honorClass == 'Class 20+') {
                                        $colorClass = 'dark';
                                        $iconClass = 'fal fa-crown';
                                    } else {
                                        $colorClass = 'secondary';
                                        $iconClass = 'fal fa-circle';
                                    }
                                    ?>
                                    <tr>
                                        <td>
                                            <i class="<?php echo $iconClass; ?> text-<?php echo $colorClass; ?>"></i>
                                            <strong><?php echo htmlspecialchars($honor['honor_class']); ?></strong>
                                            <small class="text-muted">(<?php echo $honor['honor_value']; ?>)</small>
                                        </td>
                                        <td>
                                            <?php if ($honor['avg_reputation_points'] > 0): ?>
                                                <span class="text-info">
                                                    <i class="fal fa-star"></i>
                                                    <?php
                                                    $avgPoints = $honor['avg_reputation_points'];
                                                    if ($avgPoints >= 1000000000) {
                                                        echo number_format($avgPoints / 1000000000, 1) . 'B';
                                                    } elseif ($avgPoints >= 1000000) {
                                                        echo number_format($avgPoints / 1000000, 1) . 'M';
                                                    } elseif ($avgPoints >= 1000) {
                                                        echo number_format($avgPoints / 1000, 1) . 'K';
                                                    } else {
                                                        echo number_format($avgPoints);
                                                    }
                                                    ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $colorClass; ?>">
                                                <?php echo number_format($honor['count']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($percentage, 1); ?>%</strong>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-<?php echo $colorClass; ?>"
                                                     style="width: <?php echo $percentage; ?>%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="5" class="text-center text-muted">ไม่มีข้อมูล Honor Class</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Nation Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-flag text-primary"></i> การกระจายตาม Nation
                </h3>
                <div class="card-toolbar">
                    <small class="text-muted">แสดงสัดส่วนผู้เล่นในแต่ละ Nation</small>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info alert-sm mb-3">
                    <i class="fal fa-info-circle"></i>
                    <strong>คำอธิบาย:</strong> Nation แสดงถึงฝ่ายที่ผู้เล่นสังกัด ได้แก่ Capella, Procyon หรือ Neutral
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fal fa-flag"></i> Nation</th>
                                <th><i class="fal fa-users"></i> จำนวนผู้เล่น</th>
                                <th><i class="fal fa-percentage"></i> สัดส่วน</th>
                                <th><i class="fal fa-chart-bar"></i> กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($characterStats['nation_distribution'])): ?>
                                <?php foreach ($characterStats['nation_distribution'] as $nation): ?>
                                    <?php
                                    $percentage = ($characterStats['total_characters'] > 0) ?
                                        ($nation['count'] / $characterStats['total_characters']) * 100 : 0;

                                    // กำหนดสีตาม Nation
                                    $nationColors = [
                                        'Neutral' => 'secondary',
                                        'Capella' => 'danger',
                                        'Procyon' => 'primary'
                                    ];
                                    $colorClass = $nationColors[$nation['nation_name']] ?? 'secondary';

                                    // กำหนดไอคอนตาม Nation
                                    $nationIcons = [
                                        'Neutral' => 'fal fa-circle',
                                        'Capella' => 'fal fa-flag',
                                        'Procyon' => 'fal fa-flag'
                                    ];
                                    $iconClass = $nationIcons[$nation['nation_name']] ?? 'fal fa-flag';
                                    ?>
                                    <tr>
                                        <td>
                                            <i class="<?php echo $iconClass; ?> text-<?php echo $colorClass; ?>"></i>
                                            <strong><?php echo htmlspecialchars($nation['nation_name']); ?></strong>
                                            <small class="text-muted">(<?php echo $nation['nation_value']; ?>)</small>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $colorClass; ?>">
                                                <?php echo number_format($nation['count']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($percentage, 1); ?>%</strong>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-<?php echo $colorClass; ?>"
                                                     style="width: <?php echo $percentage; ?>%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <!-- แสดงการวิเคราะห์ Nation Balance -->
                                <?php if (count($characterStats['nation_distribution']) >= 2): ?>
                                <tr class="table-info">
                                    <td colspan="4">
                                        <small class="text-muted">
                                            <i class="fal fa-balance-scale"></i>
                                            <strong>สมดุลของ Nation:</strong>
                                            <?php
                                            $capellaCount = 0;
                                            $procyonCount = 0;
                                            foreach ($characterStats['nation_distribution'] as $nation) {
                                                if ($nation['nation_name'] == 'Capella') $capellaCount = $nation['count'];
                                                if ($nation['nation_name'] == 'Procyon') $procyonCount = $nation['count'];
                                            }

                                            if ($capellaCount > 0 && $procyonCount > 0) {
                                                $ratio = $capellaCount / $procyonCount;
                                                if ($ratio >= 0.8 && $ratio <= 1.2) {
                                                    echo '<span class="text-success">สมดุลดี</span>';
                                                } elseif ($ratio > 1.2) {
                                                    echo '<span class="text-warning">Capella เหนือกว่า</span>';
                                                } else {
                                                    echo '<span class="text-warning">Procyon เหนือกว่า</span>';
                                                }
                                            } else {
                                                echo '<span class="text-muted">ไม่สามารถประเมินได้</span>';
                                            }
                                            ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            <?php else: ?>
                                <tr><td colspan="4" class="text-center text-muted">ไม่มีข้อมูล Nation</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Character Analysis Section -->
<div class="row">
    <!-- Level Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-chart-line text-success"></i> การวิเคราะห์ระดับ (Level)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['level_analysis'])): ?>
                    <?php $level = $characterStats['level_analysis']; ?>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h5 class="text-success"><?php echo number_format($level['level_1_50']); ?></h5>
                                    <small>Level 1-50</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h5 class="text-info"><?php echo number_format($level['level_101_150']); ?></h5>
                                    <small>Level 101-150</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h5 class="text-warning"><?php echo number_format($level['level_200_plus']); ?></h5>
                                    <small>Level 200+</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p><strong>เลเวลเฉลี่ย:</strong> <?php echo number_format($level['avg_level'], 1); ?></p>
                        <p><strong>เลเวลสูงสุด:</strong> <?php echo number_format($level['max_level']); ?></p>
                        <p><strong>เลเวลต่ำสุด:</strong> <?php echo number_format($level['min_level']); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Wealth Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-coins text-warning"></i> การวิเคราะห์ความมั่งคั่ง (Alz)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['wealth_analysis'])): ?>
                    <?php $wealth = $characterStats['wealth_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>ไม่มี Alz</td>
                                <td><span class="badge badge-secondary"><?php echo number_format($wealth['no_alz']); ?></span></td>
                            </tr>
                            <tr>
                                <td>1-1M Alz</td>
                                <td><span class="badge badge-success"><?php echo number_format($wealth['alz_1m']); ?></span></td>
                            </tr>
                            <tr>
                                <td>1M-10M Alz</td>
                                <td><span class="badge badge-info"><?php echo number_format($wealth['alz_10m']); ?></span></td>
                            </tr>
                            <tr>
                                <td>10M-100M Alz</td>
                                <td><span class="badge badge-warning"><?php echo number_format($wealth['alz_100m']); ?></span></td>
                            </tr>
                            <tr>
                                <td>100M-1B Alz</td>
                                <td><span class="badge badge-danger"><?php echo number_format($wealth['alz_1b']); ?></span></td>
                            </tr>
                            <tr>
                                <td>1B+ Alz</td>
                                <td><span class="badge badge-dark"><?php echo number_format($wealth['alz_1b_plus']); ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <p><strong>Alz เฉลี่ย:</strong> <?php echo number_format($wealth['avg_alz']); ?></p>
                        <p><strong>Alz รวม:</strong> <?php echo number_format($wealth['total_alz']); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Activity Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-clock text-primary"></i> การวิเคราะห์กิจกรรม (PlayTime)
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['activity_analysis'])): ?>
                    <?php $activity = $characterStats['activity_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>ไม่เคยเล่น</td>
                                <td><span class="badge badge-secondary"><?php echo number_format($activity['never_played']); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น < 1 ชม.</td>
                                <td><span class="badge badge-success"><?php echo number_format($activity['played_1h']); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 1-10 ชม.</td>
                                <td><span class="badge badge-info"><?php echo number_format($activity['played_10h']); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 10-100 ชม.</td>
                                <td><span class="badge badge-warning"><?php echo number_format($activity['played_100h']); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 100-1000 ชม.</td>
                                <td><span class="badge badge-danger"><?php echo number_format($activity['played_1000h']); ?></span></td>
                            </tr>
                            <tr>
                                <td>เล่น 1000+ ชม.</td>
                                <td><span class="badge badge-dark"><?php echo number_format($activity['played_1000h_plus']); ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <p><strong>เวลาเล่นเฉลี่ย:</strong> <?php echo number_format($activity['avg_playtime'] / 3600, 1); ?> ชม.</p>
                        <p><strong>เวลาเล่นรวม:</strong> <?php echo number_format($activity['total_playtime'] / 3600); ?> ชม.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Creation Analysis -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-calendar text-info"></i> การวิเคราะห์การสร้างตัวละคร
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['creation_analysis'])): ?>
                    <?php $creation = $characterStats['creation_analysis']; ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td>สร้างวันนี้</td>
                                <td><span class="badge badge-success"><?php echo number_format($creation['created_today']); ?></span></td>
                            </tr>
                            <tr>
                                <td>สร้างสัปดาห์นี้</td>
                                <td><span class="badge badge-info"><?php echo number_format($creation['created_week']); ?></span></td>
                            </tr>
                            <tr>
                                <td>สร้างเดือนนี้</td>
                                <td><span class="badge badge-warning"><?php echo number_format($creation['created_month']); ?></span></td>
                            </tr>
                            <tr>
                                <td>สร้างปีนี้</td>
                                <td><span class="badge badge-danger"><?php echo number_format($creation['created_year']); ?></span></td>
                            </tr>
                            <tr>
                                <td>สร้างเก่ากว่า 1 ปี</td>
                                <td><span class="badge badge-dark"><?php echo number_format($creation['created_old']); ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <?php if (!empty($creation['oldest_character'])): ?>
                            <p><strong>ตัวละครเก่าสุด:</strong> <?php echo htmlspecialchars($creation['oldest_character']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($creation['newest_character'])): ?>
                            <p><strong>ตัวละครใหม่สุด:</strong> <?php echo htmlspecialchars($creation['newest_character']); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- World Distribution -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-globe text-primary"></i> การกระจายตาม World
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['world_distribution'])): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>World</th>
                                    <th>จำนวนตัวละคร</th>
                                    <th>เลเวลเฉลี่ย</th>
                                    <th>ออนไลน์</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($characterStats['world_distribution'] as $world): ?>
                                    <tr>
                                        <td><strong>World <?php echo $world['WorldIdx']; ?></strong></td>
                                        <td><?php echo number_format($world['count']); ?></td>
                                        <td><?php echo number_format($world['avg_level'], 1); ?></td>
                                        <td><span class="badge badge-success"><?php echo number_format($world['online_count']); ?></span></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Character Progression -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fal fa-chart-bar text-success"></i> การวิเคราะห์ความก้าวหน้า
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($characterStats['progression_analysis'])): ?>
                    <?php $progression = $characterStats['progression_analysis']; ?>
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="text-success"><?php echo number_format($progression['beginner']); ?></h6>
                                    <small>Beginner (1-10)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="text-info"><?php echo number_format($progression['novice']); ?></h6>
                                    <small>Novice (11-50)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h6 class="text-warning"><?php echo number_format($progression['intermediate']); ?></h6>
                                    <small>Intermediate (51-100)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <h6 class="text-danger"><?php echo number_format($progression['advanced']); ?></h6>
                                    <small>Advanced (101-150)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="text-primary"><?php echo number_format($progression['expert']); ?></h6>
                                    <small>Expert (151-200)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-dark">
                                <div class="card-body">
                                    <h6 class="text-dark"><?php echo number_format($progression['master']); ?></h6>
                                    <small>Master (200+)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Players -->
    <div class="col-xl-8">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">ผู้เล่นอันดับต้น (Top 10)</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>อันดับ</th>
                                <th>ชื่อ</th>
                                <th>เลเวล</th>
                                <th>Alz</th>
                                <th>เวลาเล่น (ชม.)</th>
                                <th>Honor Class</th>
                                <th>Honor Title</th>
                                <th>Nation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($characterStats['top_players'])): ?>
                                <?php foreach ($characterStats['top_players'] as $index => $player): ?>
                                    <tr>
                                        <td>
                                            <?php 
                                            $rank = $index + 1;
                                            if ($rank == 1) echo '<i class="fal fa-trophy text-warning"></i> ';
                                            elseif ($rank == 2) echo '<i class="fal fa-medal text-secondary"></i> ';
                                            elseif ($rank == 3) echo '<i class="fal fa-award text-warning"></i> ';
                                            echo $rank;
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($userLogin->thaitrans($player['Name'])); ?></td>
                                        <td><span class="badge badge-primary"><?php echo $player['LEV']; ?></span></td>
                                        <td><?php echo number_format($player['Alz']); ?></td>
                                        <td><?php echo number_format($player['PlayTime'] / 3600, 1); ?></td>
                                        <td>
                                            <?php
                                            $honorClass = $player['HonorClass'] ?? 'Unknown';
                                            // กำหนดสีตาม Honor Class - รองรับ Class 20+
                                            $honorColor = 'secondary';
                                            if ($honorClass == 'No Class') {
                                                $honorColor = 'secondary';
                                            } elseif (strpos($honorClass, 'Class 20+') !== false) {
                                                $honorColor = 'dark';
                                            } elseif (strpos($honorClass, 'Class') !== false) {
                                                $classNum = (int)str_replace('Class ', '', $honorClass);
                                                if ($classNum >= 1 && $classNum <= 5) $honorColor = 'success';
                                                elseif ($classNum >= 6 && $classNum <= 10) $honorColor = 'info';
                                                elseif ($classNum >= 11 && $classNum <= 15) $honorColor = 'warning';
                                                elseif ($classNum >= 16 && $classNum <= 19) $honorColor = 'danger';
                                                elseif ($classNum == 20) $honorColor = 'dark';
                                            }
                                            ?>
                                            <span class="badge badge-<?php echo $honorColor; ?>"><?php echo $honorClass; ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            // แสดง Honor Title ตาม Nation และ Honor Class
                                            $nation = $player['Nation'] ?? 'Unknown';
                                            $honorValue = $player['HonorValue'] ?? 0;

                                            $honorTitle = getHonorTitle($honorValue, $nation);
                                            if ($honorTitle) {
                                                echo "<small class='text-primary'><i class='fal fa-crown'></i> " . htmlspecialchars($honorTitle) . "</small>";
                                            } else {
                                                echo "<span class='text-muted'>-</span>";
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $nation = $player['Nation'] ?? 'Unknown';
                                            $nationColors = [
                                                'Neutral' => 'secondary',
                                                'Capella' => 'danger',
                                                'Procyon' => 'primary'
                                            ];
                                            $nationColor = $nationColors[$nation] ?? 'secondary';
                                            ?>
                                            <span class="badge badge-<?php echo $nationColor; ?>"><?php echo $nation; ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="8" class="text-center">ไม่มีข้อมูล</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-xl-4">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การดำเนินการด่วน</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="?url=manager_charecter/manage-player" class="btn btn-primary">
                        <i class="fal fa-users"></i> ตัวละครทั้งหมด
                    </a>
                    <a href="?url=manager_charecter/manage-player-online" class="btn btn-success">
                        <i class="fal fa-circle"></i> ผู้เล่นออนไลน์
                    </a>
                    <a href="?url=manager_charecter/manage-player-lastlogin" class="btn btn-info">
                        <i class="fal fa-clock"></i> เข้าเกมล่าสุด
                    </a>
                    <a href="?url=manager_charecter/manage-player-banned" class="btn btn-danger">
                        <i class="fal fa-ban"></i> ถูกแบน
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    location.reload();
}

// Auto refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>

<style>
/* การปรับปรุงการแสดงผล */
.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.progress {
    background-color: #e9ecef;
    border-radius: 0.375rem;
}

.progress-bar {
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    transition: width 0.6s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.card-toolbar {
    margin-left: auto;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* สีสำหรับคลาสต่างๆ */
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
.text-success { color: #28a745 !important; }
.text-primary { color: #007bff !important; }
.text-secondary { color: #6c757d !important; }
.text-dark { color: #343a40 !important; }

.bg-danger { background-color: #dc3545 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-success { background-color: #28a745 !important; }
.bg-primary { background-color: #007bff !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-dark { background-color: #343a40 !important; }

.badge-danger { background-color: #dc3545; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-info { background-color: #17a2b8; }
.badge-success { background-color: #28a745; }
.badge-primary { background-color: #007bff; }
.badge-secondary { background-color: #6c757d; }
.badge-dark { background-color: #343a40; }

/* การปรับปรุงตาราง */
.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
}

.table-info {
    background-color: rgba(23, 162, 184, 0.1);
}

.thead-light th {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Responsive */
@media (max-width: 768px) {
    .card-title {
        font-size: 1rem;
    }

    .card-toolbar {
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .progress {
        height: 15px !important;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.7em;
    }
}

/* Animation สำหรับ progress bars */
@keyframes progressAnimation {
    from { width: 0%; }
    to { width: var(--progress-width); }
}

.progress-bar {
    animation: progressAnimation 1s ease-out;
}

/* Tooltip สำหรับข้อมูลเพิ่มเติม */
[title] {
    cursor: help;
}

/* การจัดแต่งไอคอน */
.fal {
    margin-right: 0.25rem;
}

.card-header .fal {
    margin-right: 0.5rem;
}
</style>
