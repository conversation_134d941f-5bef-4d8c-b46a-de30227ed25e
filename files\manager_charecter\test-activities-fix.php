<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-history"></i> ทดสอบการแก้ไขกิจกรรมล่าสุด
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหากิจกรรมล่าสุดไม่แสดง</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fal fa-exclamation-triangle"></i>
                    <strong>ปัญหา:</strong> ส่วน "กิจกรรมล่าสุด" ไม่แสดงผลในหน้า Character Monitor
                </div>
                
                <h5>🔍 การตรวจสอบปัญหา</h5>
                
                <?php
                // ทดสอบฟังก์ชัน getRecentCharacterActivities
                function getRecentCharacterActivitiesTest($conn, $limit = 10) {
                    $activities = array();
                    
                    try {
                        if (!$conn) {
                            return ['error' => 'Database connection is null'];
                        }
                        
                        $sql = "SELECT TOP ? 
                                    'created' as activity_type,
                                    CharacterIdx,
                                    Name,
                                    LEV,
                                    Style,
                                    CreateDate as activity_time,
                                    WorldIdx,
                                    Alz
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                ORDER BY CreateDate DESC";
                        
                        $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
                        if ($stmt && sqlsrv_execute($stmt)) {
                            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                $activities[] = $row;
                            }
                        } else {
                            return ['error' => 'Query failed: ' . print_r(sqlsrv_errors(), true)];
                        }
                        
                    } catch (Exception $e) {
                        return ['error' => 'Exception: ' . $e->getMessage()];
                    }
                    
                    return $activities;
                }
                
                $testActivities = getRecentCharacterActivitiesTest($conn, 10);
                ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">🔍 Database Test</h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($testActivities['error'])): ?>
                                    <div class="alert alert-danger alert-sm">
                                        <strong>❌ Error:</strong> <?php echo $testActivities['error']; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-success alert-sm">
                                        <strong>✅ Success:</strong> พบ <?php echo count($testActivities); ?> กิจกรรม
                                    </div>
                                    
                                    <?php if (!empty($testActivities)): ?>
                                        <p><strong>ตัวอย่างข้อมูล:</strong></p>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ชื่อ</th>
                                                        <th>เลเวล</th>
                                                        <th>วันที่สร้าง</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach (array_slice($testActivities, 0, 5) as $activity): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($activity['Name']); ?></td>
                                                            <td><?php echo $activity['LEV']; ?></td>
                                                            <td>
                                                                <?php 
                                                                $createDate = $activity['activity_time'];
                                                                if ($createDate instanceof DateTime) {
                                                                    echo $createDate->format('Y-m-d H:i:s');
                                                                } else {
                                                                    echo $createDate;
                                                                }
                                                                ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ การแก้ไขที่ทำ</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> เพิ่ม debug information</li>
                                    <li><i class="fal fa-check text-success"></i> ตรวจสอบ database connection</li>
                                    <li><i class="fal fa-check text-success"></i> เพิ่ม error handling</li>
                                    <li><i class="fal fa-check text-success"></i> แสดงข้อความเมื่อไม่มีข้อมูล</li>
                                    <li><i class="fal fa-check text-success"></i> ปรับปรุง updateActivitiesTable</li>
                                    <li><i class="fal fa-check text-success"></i> เพิ่ม fallback display</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบ API</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testActivitiesAPI()">
                        <i class="fal fa-history"></i> Test Activities API
                    </button>
                    <button class="btn btn-info" onclick="testDirectQuery()">
                        <i class="fal fa-database"></i> Test Direct Query
                    </button>
                    <button class="btn btn-success" onclick="testCharacterMonitor()">
                        <i class="fal fa-external-link"></i> Open Character Monitor
                    </button>
                </div>
                
                <div id="api-test-results"></div>
                
                <h5 class="mt-4">📊 สถิติฐานข้อมูล</h5>
                <div class="row">
                    <?php
                    try {
                        // Total characters
                        $sql = "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                        $result = sqlsrv_query($conn, $sql);
                        $totalChars = 0;
                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $totalChars = $row['total'];
                        }
                        
                        // Characters created today
                        $sql = "SELECT COUNT(*) as today FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
                        $result = sqlsrv_query($conn, $sql);
                        $todayChars = 0;
                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $todayChars = $row['today'];
                        }
                        
                        // Characters created this week
                        $sql = "SELECT COUNT(*) as week FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                WHERE CreateDate >= DATEADD(day, -7, GETDATE())";
                        $result = sqlsrv_query($conn, $sql);
                        $weekChars = 0;
                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $weekChars = $row['week'];
                        }
                        
                        // Latest character
                        $sql = "SELECT TOP 1 Name, CreateDate FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                ORDER BY CreateDate DESC";
                        $result = sqlsrv_query($conn, $sql);
                        $latestChar = null;
                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $latestChar = $row;
                        }
                    } catch (Exception $e) {
                        echo '<div class="alert alert-danger">Database error: ' . $e->getMessage() . '</div>';
                    }
                    ?>
                    
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary"><?php echo number_format($totalChars); ?></h4>
                                <small>ตัวละครทั้งหมด</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success"><?php echo number_format($todayChars); ?></h4>
                                <small>สร้างวันนี้</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info"><?php echo number_format($weekChars); ?></h4>
                                <small>สร้างสัปดาห์นี้</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6 class="text-warning"><?php echo $latestChar ? htmlspecialchars($latestChar['Name']) : 'N/A'; ?></h6>
                                <small>ตัวละครล่าสุด</small>
                                <?php if ($latestChar): ?>
                                    <br><small class="text-muted">
                                        <?php 
                                        $createDate = $latestChar['CreateDate'];
                                        if ($createDate instanceof DateTime) {
                                            echo $createDate->format('d/m/Y H:i');
                                        } else {
                                            echo date('d/m/Y H:i', strtotime($createDate));
                                        }
                                        ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔧 Troubleshooting Guide</h5>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. กิจกรรมล่าสุดไม่แสดง
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>สาเหตุที่เป็นไปได้:</strong></p>
                                <ul>
                                    <li>ฟังก์ชัน getRecentCharacterActivities ส่งคืน array ว่าง</li>
                                    <li>Database connection ล้มเหลว</li>
                                    <li>SQL query มีปัญหา</li>
                                    <li>ไม่มีข้อมูลในตาราง</li>
                                </ul>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <ol>
                                    <li>เพิ่ม debug mode: <code>?debug=1</code></li>
                                    <li>ตรวจสอบ database connection</li>
                                    <li>ตรวจสอบข้อมูลในตาราง</li>
                                    <li>เพิ่ม error handling</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. AJAX Update ไม่ทำงาน
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>ปัญหาที่เป็นไปได้:</strong></p>
                                <ul>
                                    <li>API endpoint ไม่ส่งข้อมูล</li>
                                    <li>JavaScript error</li>
                                    <li>updateActivitiesTable function มีปัญหา</li>
                                </ul>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <pre><code>// ตรวจสอบใน Console (F12)
console.log('Activities data:', activities);

// ทดสอบ API
fetch('?url=manager_charecter/api/character-data&action=recent_activities&limit=10')
  .then(response => response.json())
  .then(data => console.log(data));</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testActivitiesAPI() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> Testing Activities API...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=recent_activities&limit=10', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        console.log('API Response:', responseText);
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> Activities API Test Result</h6>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        
        if (responseText.trim().startsWith('{') || responseText.trim().startsWith('[')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>JSON Valid:</strong> ✅ Yes</p>';
                html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                
                if (data.success && data.data) {
                    html += '<p><strong>Activities Count:</strong> ' + data.data.length + '</p>';
                    if (data.data.length > 0) {
                        html += '<p><strong>Sample Activity:</strong> ' + data.data[0].name + ' (Level ' + data.data[0].level + ')</p>';
                    }
                }
                
                html += '<details><summary>Full Response</summary><pre>' + JSON.stringify(data, null, 2) + '</pre></details>';
            } catch (parseError) {
                html += '<p><strong>JSON Valid:</strong> ❌ No - ' + parseError.message + '</p>';
                html += '<details><summary>Raw Response</summary><pre>' + responseText + '</pre></details>';
            }
        } else {
            html += '<p><strong>Response Type:</strong> ' + (responseText.includes('<!DOCTYPE') ? 'HTML Document' : 'Unknown') + '</p>';
            html += '<details><summary>Raw Response</summary><pre>' + responseText.substring(0, 1000) + '</pre></details>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> API Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function testDirectQuery() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info">Direct query test completed. Check the Database Test section above for results.</div>';
}

function testCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor&debug=1', '_blank');
}
</script>

<style>
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

details {
    margin-top: 0.5rem;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

summary:hover {
    text-decoration: underline;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
