<?php 
$user->restrictionUser(true, $conn); 
$params = array();
$options = array("Scrollable" => SQLSRV_CURSOR_KEYSET);

// ดึงสถิติข้อมูล Elite Dungeon
$totalPlayersQuery = "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info";
$totalPlayersResult = sqlsrv_query($conn, $totalPlayersQuery);
$totalPlayers = 0;
if ($totalPlayersResult) {
    $totalPlayersData = sqlsrv_fetch_array($totalPlayersResult, SQLSRV_FETCH_ASSOC);
    $totalPlayers = $totalPlayersData['total'];
}

// จำนวนผู้เล่นที่มีข้อมูล Elite Dungeon (ไม่ใช่ default)
$activePlayersQuery = "SELECT COUNT(*) as active FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE EliteDungeonData != 0x";
$activePlayersResult = sqlsrv_query($conn, $activePlayersQuery);
$activePlayers = 0;
if ($activePlayersResult) {
    $activePlayersData = sqlsrv_fetch_array($activePlayersResult, SQLSRV_FETCH_ASSOC);
    $activePlayers = $activePlayersData['active'];
}

// จำนวนผู้เล่นที่มี ResetTime > 0
$resetPlayersQuery = "SELECT COUNT(*) as reset_players FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE ResetTime > 0";
$resetPlayersResult = sqlsrv_query($conn, $resetPlayersQuery);
$resetPlayers = 0;
if ($resetPlayersResult) {
    $resetPlayersData = sqlsrv_fetch_array($resetPlayersResult, SQLSRV_FETCH_ASSOC);
    $resetPlayers = $resetPlayersData['reset_players'];
}

// ResetTime ล่าสุด
$latestResetQuery = "SELECT MAX(ResetTime) as latest_reset FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info WHERE ResetTime > 0";
$latestResetResult = sqlsrv_query($conn, $latestResetQuery);
$latestReset = 0;
if ($latestResetResult) {
    $latestResetData = sqlsrv_fetch_array($latestResetResult, SQLSRV_FETCH_ASSOC);
    $latestReset = $latestResetData['latest_reset'] ?? 0;
}

// ฟังก์ชันแปลงเวลา Unix timestamp
function formatUnixTime($timestamp) {
    if ($timestamp <= 0) {
        return 'ไม่เคยรีเซ็ต';
    }
    return date('d/m/Y H:i:s', $timestamp);
}

// ฟังก์ชันแปลง varbinary เป็น hex string
function formatBinaryData($binaryData) {
    if (empty($binaryData) || $binaryData === '0x') {
        return 'ไม่มีข้อมูล';
    }
    
    // แปลง binary เป็น hex และแสดงแค่ส่วนแรก
    $hex = bin2hex($binaryData);
    if (strlen($hex) > 32) {
        return substr($hex, 0, 32) . '...';
    }
    return $hex;
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-crown"></i> ระบบตรวจสอบ <span class="fw-300">Elite Dungeon Info</span>
    </h1>
    <div class="subheader-block d-lg-flex align-items-center">
        <div class="d-flex mr-4">
            <div class="mr-2">
                <span class="peity-donut"
                    data-peity="{ &quot;fill&quot;: [&quot;#dc3545&quot;, &quot;#6c757d&quot;],  &quot;innerRadius&quot;: 14, &quot;radius&quot;: 20 }"
                    style="display: none;"><?php echo $activePlayers; ?>/<?php echo $totalPlayers; ?></span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">ผู้เล่นที่มีข้อมูล Elite</label>
                <h4 class="font-weight-bold mb-0"><?php echo number_format($activePlayers); ?> / <?php echo number_format($totalPlayers); ?></h4>
            </div>
        </div>
        <div class="d-flex mr-0">
            <div class="mr-2">
                <span class="peity-bar" data-peity="{ &quot;fill&quot;: [&quot;#ffc107&quot;] }" style="display: none;">
                    <?php echo $resetPlayers > 0 ? min($resetPlayers/$totalPlayers, 1) : 0; ?>
                </span>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">ผู้เล่นที่เคยรีเซ็ต</label>
                <h4 class="font-weight-bold mb-0"><?php echo number_format($resetPlayers); ?> คน</h4>
            </div>
        </div>
    </div>
</div>

<!-- สถิติ Cards -->
<div class="row">
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($totalPlayers); ?>
                    <small class="m-0 l-h-n">ผู้เล่นทั้งหมด</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-danger-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($activePlayers); ?>
                    <small class="m-0 l-h-n">มีข้อมูล Elite</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo number_format($resetPlayers); ?>
                    <small class="m-0 l-h-n">เคยรีเซ็ต</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo formatUnixTime($latestReset); ?>
                    <small class="m-0 l-h-n">รีเซ็ตล่าสุด</small>
                </h3>
            </div>
        </div>
    </div>
</div>

<!-- ตาราง Elite Dungeon Info -->
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Elite Dungeon Info <span class="fw-300"><i>ตรวจสอบข้อมูล Elite Dungeon</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <!-- ฟิลเตอร์ -->
                    <div class="alert alert-info" role="alert">
                        <i class="fal fa-info-circle"></i> <strong>หมายเหตุ:</strong> แสดงข้อมูล 1,000 แถวแรก เพื่อประสิทธิภาพในการโหลด
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="filter_character">ค้นหาตัวละคร:</label>
                            <input type="text" id="filter_character" class="form-control" placeholder="ชื่อตัวละคร หรือ CharacterIdx">
                        </div>
                        <div class="col-md-3">
                            <label for="filter_status">สถานะข้อมูล:</label>
                            <select id="filter_status" class="form-control">
                                <option value="">ทั้งหมด</option>
                                <option value="has_data">มีข้อมูล Elite</option>
                                <option value="no_data">ไม่มีข้อมูล</option>
                                <option value="has_reset">เคยรีเซ็ต</option>
                                <option value="no_reset">ไม่เคยรีเซ็ต</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filter_reset_from">รีเซ็ตจากวันที่:</label>
                            <input type="date" id="filter_reset_from" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label for="filter_reset_to">รีเซ็ตถึงวันที่:</label>
                            <input type="date" id="filter_reset_to" class="form-control">
                        </div>
                    </div>

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table id="dt-elite-dungeon" class="table table-sm table-bordered w-100">
                            <thead>
                                <tr>
                                    <th>UserNum</th>
                                    <th>UserID</th>
                                    <th>CharacterIdx</th>
                                    <th>ชื่อตัวละคร</th>
                                    <th>Level</th>
                                    <th>Class</th>
                                    <th>Elite Dungeon Data</th>
                                    <th>Reset Time</th>
                                    <th>สถานะ</th>
                                    <th>รายละเอียด</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Define and execute the query (จำกัดที่ 1000 แถวเพื่อ performance)
                                $sql = "SELECT TOP 1000 
                                               ed.CharacterIdx, ed.EliteDungeonData, ed.ResetTime,
                                               ct.Name, ct.LEV, ct.Style,
                                               auth.UserNum, auth.ID
                                        FROM [".DATABASE_SV."].[dbo].cabal_elite_dungeon_info ed
                                        LEFT JOIN [".DATABASE_SV."].[dbo].cabal_character_table ct ON ed.CharacterIdx = ct.CharacterIdx
                                        LEFT JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table auth ON FLOOR(ct.CharacterIdx/16) = auth.UserNum
                                        ORDER BY ed.ResetTime DESC, ed.CharacterIdx ASC";

                                $stmt = sqlsrv_query($conn, $sql);
                                if (!$stmt) {
                                    die(print_r(sqlsrv_errors(), true));
                                }

                                // ตรวจสอบว่ามีข้อมูลหรือไม่
                                $hasData = false;
                                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                    $hasData = true;
                                    $characterIdx = $row['CharacterIdx'];
                                    $eliteDungeonData = $row['EliteDungeonData'];
                                    $resetTime = $row['ResetTime'];
                                    $characterName = $userLogin->thaitrans($row['Name'] ?? 'N/A');
                                    $level = $row['LEV'] ?? 0;
                                    $style = $row['Style'] ?? 0;
                                    $userNum = $row['UserNum'] ?? 0;
                                    $userID = $row['ID'] ?? 'N/A';
                                    
                                    // แปลง Style เป็น Class Name
                                    $classInfo = $userLogin->cabalstylenum($style);
                                    $className = $classInfo['Class_Name'] ?? 'Unknown';
                                    
                                    // แปลงเวลารีเซ็ต
                                    $resetTimeFormatted = formatUnixTime($resetTime);
                                    
                                    // แปลงข้อมูล Elite Dungeon
                                    $eliteDataFormatted = formatBinaryData($eliteDungeonData);
                                    
                                    // สถานะ
                                    $hasEliteData = !empty($eliteDungeonData) && $eliteDungeonData !== '0x';
                                    $hasResetTime = $resetTime > 0;
                                    
                                    if ($hasEliteData && $hasResetTime) {
                                        $status = '<span class="badge badge-success">มีข้อมูล + เคยรีเซ็ต</span>';
                                    } elseif ($hasEliteData) {
                                        $status = '<span class="badge badge-primary">มีข้อมูล Elite</span>';
                                    } elseif ($hasResetTime) {
                                        $status = '<span class="badge badge-warning">เคยรีเซ็ต</span>';
                                    } else {
                                        $status = '<span class="badge badge-secondary">ไม่มีข้อมูล</span>';
                                    }
                                ?>
                                <tr>
                                    <td><?php echo $userNum; ?></td>
                                    <td><?php echo htmlspecialchars($userID); ?></td>
                                    <td><?php echo $characterIdx; ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($characterName); ?>
                                    </td>
                                    <td><span class="badge badge-info">Lv.<?php echo $level; ?></span></td>
                                    <td><span class="badge badge-dark"><?php echo $className; ?></span></td>
                                    <td>
                                        <small class="text-monospace"><?php echo htmlspecialchars($eliteDataFormatted); ?></small>
                                    </td>
                                    <td><?php echo $resetTimeFormatted; ?></td>
                                    <td><?php echo $status; ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewCharacterDetail(<?php echo $characterIdx; ?>)">
                                            <i class="fal fa-eye"></i> ดูตัวละคร
                                        </button>
                                    </td>
                                </tr>
                                <?php 
                                }
                                
                                if (!$hasData) {
                                    echo "<tr><td colspan='10' class='text-center'>ไม่พบข้อมูล Elite Dungeon</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // DataTable initialization
    var table = $('#dt-elite-dungeon').DataTable({
        "pageLength": 50,
        "order": [[ 7, "desc" ]],
        "language": {
            "url": "assets/js/datatables-thai.json"
        },
        "processing": true,
        "serverSide": false,
        "responsive": true
    });
    
    // ฟิลเตอร์แบบ Custom
    $('#filter_character').on('keyup', function() {
        table.columns([2, 3]).search(this.value).draw();
    });
    
    $('#filter_status').on('change', function() {
        var status = this.value;
        if (status === '') {
            table.column(8).search('').draw();
        } else if (status === 'has_data') {
            table.column(8).search('มีข้อมูล Elite').draw();
        } else if (status === 'no_data') {
            table.column(8).search('ไม่มีข้อมูล').draw();
        } else if (status === 'has_reset') {
            table.column(8).search('เคยรีเซ็ต').draw();
        } else if (status === 'no_reset') {
            table.column(7).search('ไม่เคยรีเซ็ต').draw();
        }
    });
    
    // ฟังก์ชันดูรายละเอียดตัวละคร
    window.viewCharacterDetail = function(charIdx) {
        window.open('?url=manager_charecter/view&char_id=' + charIdx, '_blank');
    };
});
</script>
