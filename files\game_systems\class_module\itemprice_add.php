<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

header('Content-Type: application/json');

$data = json_decode(file_get_contents("php://input"), true);

if (!$data || !isset($data['ItemPriceID'], $data['ItemKindIdx'], $data['ItemOption'], $data['ItemCount'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input']);
    exit;
}

$params = [
    (int)$data['ItemPriceID'],
    (int)$data['ItemKindIdx'],
    (int)$data['ItemOption'],
    (int)$data['ItemCount']
];

// เพิ่มข้อมูลลงตาราง โดยไม่ระบุ ID เพราะเป็น IDENTITY
$sql = "INSERT INTO EventData.dbo.cabal_ems_event_npcitemshop_itemprice_table 
        (ItemPriceID, ItemKindIdx, ItemOption, ItemCount) VALUES (?, ?, ?, ?)";

$stmt = sqlsrv_query($conn, $sql, $params);

if ($stmt === false) {
    http_response_code(500);
    echo json_encode([
        'error' => 'SQL insert failed',
        'details' => sqlsrv_errors()
    ]);
    exit;
}

echo json_encode(['status' => 'added']);
