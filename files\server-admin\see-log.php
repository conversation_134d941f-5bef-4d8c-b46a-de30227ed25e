<?php 
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_SERVERLOG; ?> <small><?php echo PT_SERVERLOG_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getLogID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get server log by ID
                $selectWebLogID = "SELECT * FROM DBG_SrvLogInfo WHERE RecordID = '$getLogID'";
                $selectWebLogParam = array();
                $selectWebLogOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectWebLogQuery = sqlsrv_query($conn, $selectWebLogID, $selectWebLogParam, $selectWebLogOpt);
                $selectWebLogRows = sqlsrv_num_rows($selectWebLogQuery);

                if ($selectWebLogRows) {
                    while ($resServerLog = sqlsrv_fetch_array($selectWebLogQuery, SQLSRV_FETCH_ASSOC)) {
                        ?>
                        <div class="col-lg-6">
                            <h4 class="text-red">Player</h4>
                            <p><?php echo $resServerLog['Gamertag']; ?></p>
                            <hr>
                            <h4 class="text-red">Player action</h4>
                            <p><?php echo $resServerLog['Msg']; ?></p>
                            <hr>
                            <h4 class="text-red">Message</h4>
                            <p><?php echo $resServerLog['Data']; ?></p>
                        </div>

                        <div class="col-lg-6">
                            <h4 class="text-red">Report Time</h4>
                            <p><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resServerLog['ReportTime'])); ?></p>
                            <hr>
                            <h4 class="text-red">Player CustomerID</h4>
                            <p><?php echo $resServerLog['CustomerID']; ?></p>
                            <hr>
                            <h4 class="text-red">Player IP</h4>
                            <p><?php echo $resServerLog['CustomerIP']; ?></p>
                            <hr>
                            <h4 class="text-red">CheatID</h4>
                            <p><?php echo $resServerLog['CheatID']; ?></p>
                        </div>
                        <?php
                    }
                }else{
                    $returnWarning = W_NOTHING_RETURNED;
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>