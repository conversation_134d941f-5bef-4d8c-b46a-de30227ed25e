<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Update Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .slot {
            width: 93px;
            height: 93px;
            border: 1px solid #555;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            display: inline-block;
            margin: 2px;
            background: #2e2e2e;
        }
        
        .slot.has-item {
            background: #222;
            cursor: grab;
        }
        
        .slot.drag-over {
            border-color: #28a745 !important;
            border-width: 3px !important;
            background-color: rgba(40, 167, 69, 0.1) !important;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.5) !important;
            transform: scale(1.05) !important;
        }
        
        .slot.dragging {
            opacity: 0.6 !important;
            transform: scale(0.95) !important;
            border-color: #007bff !important;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.5) !important;
        }
        
        .slot-number {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: 11px;
            color: #bbb;
            z-index: 5;
            text-shadow: 1px 1px 2px #000;
        }
        
        .item-name {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            z-index: 4;
            padding: 2px;
            color: #fff;
            font-size: 9px;
            font-weight: bold;
            text-align: center;
            text-shadow: 1px 1px 2px #000;
        }
        
        .debug {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .nav-tabs .nav-link {
            color: #ccc;
        }
        
        .nav-tabs .nav-link.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        
        .tab-content {
            background: #333;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ทดสอบระบบ Tab Update</h1>
        
        <div class="debug" id="debugInfo">
            Debug info will appear here...
        </div>
        
        <!-- Nav tabs -->
        <ul class="nav nav-tabs" id="poolTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pool1-tab" data-bs-toggle="tab" data-bs-target="#pool1" type="button" role="tab">
                    Pool 1 (Weapons)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pool2-tab" data-bs-toggle="tab" data-bs-target="#pool2" type="button" role="tab">
                    Pool 2 (Armor)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pool3-tab" data-bs-toggle="tab" data-bs-target="#pool3" type="button" role="tab">
                    Pool 3 (Accessories)
                </button>
            </li>
        </ul>
        
        <!-- Tab panes -->
        <div class="tab-content" id="tabContent">
            <div class="tab-pane fade show active" id="pool1" role="tabpanel">
                <div id="pool1-slots"></div>
            </div>
            <div class="tab-pane fade" id="pool2" role="tabpanel">
                <div id="pool2-slots"></div>
            </div>
            <div class="tab-pane fade" id="pool3" role="tabpanel">
                <div id="pool3-slots"></div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="resetTest()" class="btn btn-primary">🔄 Reset Test</button>
            <button onclick="simulateSwap()" class="btn btn-success">🔄 Simulate Cross-Pool Swap</button>
        </div>
        
        <div style="background: #333; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h5>📝 การทดสอบ:</h5>
            <ul>
                <li>ลากไอเท็มจาก Pool หนึ่งไปอีก Pool หนึ่ง</li>
                <li>ระบบจะอัปเดตเฉพาะ Pool ที่เกี่ยวข้อง</li>
                <li>Tab จะเปลี่ยนไปยัง Pool ที่มีการเปลี่ยนแปลง</li>
                <li>ดู debug log เพื่อติดตามการทำงาน</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test data
        let testItems = {
            '1': [
                { SerialNum: '1', ItemName: 'Sword +15', SlotID: 0, PoolID: '1', ItemKind: 1 },
                { SerialNum: '2', ItemName: 'Axe +10', SlotID: 1, PoolID: '1', ItemKind: 2 }
            ],
            '2': [
                { SerialNum: '3', ItemName: 'Helmet +7', SlotID: 0, PoolID: '2', ItemKind: 3 },
                { SerialNum: '4', ItemName: 'Armor +12', SlotID: 1, PoolID: '2', ItemKind: 4 }
            ],
            '3': [
                { SerialNum: '5', ItemName: 'Ring +5', SlotID: 0, PoolID: '3', ItemKind: 5 },
                { SerialNum: '6', ItemName: 'Necklace +8', SlotID: 1, PoolID: '3', ItemKind: 6 }
            ]
        };
        
        let dragData = null;
        let currentPoolID = '1';
        
        function debugLog(message) {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debugDiv.scrollTop = debugDiv.scrollHeight;
            console.log(message);
        }
        
        function createSlots(poolId) {
            const container = document.getElementById(`pool${poolId}-slots`);
            container.innerHTML = '';
            
            for (let i = 0; i < 10; i++) {
                const item = testItems[poolId].find(item => parseInt(item.SlotID) === i);
                const slot = document.createElement('div');
                
                slot.className = `slot ${item ? 'has-item' : ''}`;
                slot.dataset.slot = i;
                slot.dataset.pool = poolId;
                
                if (item) {
                    slot.dataset.serial = item.SerialNum;
                    slot.draggable = true;
                } else {
                    slot.draggable = false;
                }
                
                slot.innerHTML = `
                    <div class="slot-number">${i}</div>
                    ${item ? `<div class="item-name">${item.ItemName}</div>` : '<div style="color: #666; text-align: center; margin-top: 35px;">Empty</div>'}
                `;
                
                // Event listeners
                slot.addEventListener('dragstart', handleDragStart);
                slot.addEventListener('dragover', handleDragOver);
                slot.addEventListener('dragleave', handleDragLeave);
                slot.addEventListener('drop', handleDrop);
                slot.addEventListener('dragend', handleDragEnd);
                
                container.appendChild(slot);
            }
        }
        
        function handleDragStart(e) {
            const slot = e.currentTarget;
            if (!slot.dataset.serial) {
                e.preventDefault();
                return false;
            }
            
            slot.classList.add('dragging');
            dragData = {
                serial: slot.dataset.serial,
                slot: parseInt(slot.dataset.slot),
                pool: slot.dataset.pool
            };
            
            debugLog(`Drag started: ${dragData.serial} from Pool ${dragData.pool} Slot ${dragData.slot}`);
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            e.currentTarget.classList.add('drag-over');
        }
        
        function handleDragLeave(e) {
            e.currentTarget.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            const target = e.currentTarget;
            target.classList.remove('drag-over');
            
            const targetSlot = parseInt(target.dataset.slot);
            const targetPool = target.dataset.pool;
            
            if (!dragData || (dragData.slot === targetSlot && dragData.pool === targetPool)) {
                return;
            }
            
            debugLog(`Drop: target Pool ${targetPool} Slot ${targetSlot}`);
            
            // จำลองการสลับไอเท็ม
            simulateItemSwap(dragData.pool, dragData.slot, targetPool, targetSlot);
        }
        
        function handleDragEnd(e) {
            document.querySelectorAll('.slot').forEach(s => {
                s.classList.remove('dragging', 'drag-over');
            });
            dragData = null;
        }
        
        function simulateItemSwap(fromPool, fromSlot, toPool, toSlot) {
            debugLog(`Simulating swap: Pool ${fromPool} Slot ${fromSlot} → Pool ${toPool} Slot ${toSlot}`);
            
            const itemA = testItems[fromPool].find(item => parseInt(item.SlotID) === fromSlot);
            const itemB = testItems[toPool].find(item => parseInt(item.SlotID) === toSlot);
            
            if (!itemA) {
                debugLog('Error: Source item not found');
                return;
            }
            
            if (itemB) {
                // Swap items
                debugLog(`Swapping: ${itemA.ItemName} ↔ ${itemB.ItemName}`);
                
                // Update item data
                itemA.SlotID = toSlot;
                itemA.PoolID = toPool;
                itemB.SlotID = fromSlot;
                itemB.PoolID = fromPool;
                
                // Move items between pools
                testItems[fromPool] = testItems[fromPool].filter(item => item.SerialNum !== itemA.SerialNum);
                testItems[toPool] = testItems[toPool].filter(item => item.SerialNum !== itemB.SerialNum);
                testItems[toPool].push(itemA);
                testItems[fromPool].push(itemB);
            } else {
                // Move item to empty slot
                debugLog(`Moving: ${itemA.ItemName} to empty slot`);
                
                itemA.SlotID = toSlot;
                itemA.PoolID = toPool;
                
                testItems[fromPool] = testItems[fromPool].filter(item => item.SerialNum !== itemA.SerialNum);
                testItems[toPool].push(itemA);
            }
            
            // Update affected pools
            const affectedPools = new Set([fromPool, toPool]);
            for (const poolId of affectedPools) {
                updateSpecificPool(poolId);
            }
            
            // Show the target pool tab
            showActiveTab(toPool);
        }
        
        function updateSpecificPool(poolId) {
            debugLog(`Updating Pool ${poolId}`);
            createSlots(poolId);
        }
        
        function showActiveTab(poolId) {
            debugLog(`Switching to Pool ${poolId} tab`);
            
            // Update current pool
            currentPoolID = poolId;
            
            // Use Bootstrap tab API
            const targetTab = document.getElementById(`pool${poolId}-tab`);
            if (targetTab) {
                const tab = new bootstrap.Tab(targetTab);
                tab.show();
            }
        }
        
        function resetTest() {
            testItems = {
                '1': [
                    { SerialNum: '1', ItemName: 'Sword +15', SlotID: 0, PoolID: '1', ItemKind: 1 },
                    { SerialNum: '2', ItemName: 'Axe +10', SlotID: 1, PoolID: '1', ItemKind: 2 }
                ],
                '2': [
                    { SerialNum: '3', ItemName: 'Helmet +7', SlotID: 0, PoolID: '2', ItemKind: 3 },
                    { SerialNum: '4', ItemName: 'Armor +12', SlotID: 1, PoolID: '2', ItemKind: 4 }
                ],
                '3': [
                    { SerialNum: '5', ItemName: 'Ring +5', SlotID: 0, PoolID: '3', ItemKind: 5 },
                    { SerialNum: '6', ItemName: 'Necklace +8', SlotID: 1, PoolID: '3', ItemKind: 6 }
                ]
            };
            
            createSlots('1');
            createSlots('2');
            createSlots('3');
            showActiveTab('1');
            
            document.getElementById('debugInfo').innerHTML = '';
            debugLog('Test reset');
        }
        
        function simulateSwap() {
            debugLog('Simulating cross-pool swap...');
            simulateItemSwap('1', 0, '2', 2); // Move Sword from Pool 1 Slot 0 to Pool 2 Slot 2
        }
        
        // Initialize
        createSlots('1');
        createSlots('2');
        createSlots('3');
        debugLog('Tab update test initialized');
    </script>
</body>
</html>
