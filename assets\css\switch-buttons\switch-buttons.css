

.switch-button{
	display:inline-block;}

.switch-button input{
	visibility:hidden;
	position:absolute;
	margin:0px;}

.switch-button label{
	position:relative;
	background-color:#CCC;
	border:1px solid #ccc;
	vertical-align:middle;
	width:50px;
	height:25px;
	margin:0px;
	cursor:pointer;
	-webkit-border-radius: 60px;
	-moz-border-radius: 60px;
	-ms-border-radius: 60px;
	-o-border-radius: 60px;
	border-radius: 60px;
	-webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;}


.switch-button label:after{
	background-color:#fff;
	height:100%;
	position:absolute;
	top:0px;
	left:0px;
	width:50%;
	content:"";
	-webkit-border-radius: 60px;
	-moz-border-radius: 60px;
	-ms-border-radius: 60px;
	-o-border-radius: 60px;
	border-radius: 60px;
	-webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;}

.switch-button input:checked + label{
	background-color:#27C24C;
	border-color:#27C24C;}
	
.switch-button input:checked + label:after{
	margin-left:50%;
	-webkit-transition: all 0.4s;
	-moz-transition: all 0.4s;
	-o-transition: all 0.4s;
	transition: all 0.4s;}
	


/****Sizes***/	
.switch-button.xs label{
	width:30px;
	height:15px;}


.switch-button.sm label{
	width:40px;
	height:20px;}
	
	
.switch-button.lg label{
	width:60px;
	height:30px;}
	
	
.switch-button.xlg label{
	width:80px;
	height:40px;}


/****Colors***/

.switch-button.primary input:checked + label{
	background-color:#8d82b5;
	border-color:#8d82b5;}
	
.switch-button.info input:checked + label{
	background-color:#5bc0de;
	border-color:#5bc0de;}
	
.switch-button.warning input:checked + label{
	background-color:#f0ad4e;
	border-color:#f0ad4e;}
	
.switch-button.danger input:checked + label{
	background-color:#d9534f;
	border-color:#d9534f;}