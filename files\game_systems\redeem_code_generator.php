<?php $zpanel->checkSession(true); ?>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-ticket-alt"></i> ระบบสร้างรหัส Redeem Code อัตโนมัติ</h5>
    </div>
    <div class="card-body">
        <!-- ฟอร์มสร้าง Code -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">สร้าง Redeem Code ใหม่</h6>
                    </div>
                    <div class="card-body">
                        <form id="generateCodeForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="codeCount">จำนวน Code ที่ต้องการสร้าง:</label>
                                        <input type="number" id="codeCount" class="form-control" min="1" max="1000" value="1" required>
                                        <small class="text-muted">สูงสุด 1,000 codes ต่อครั้ง</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="quantity">Quantity (จำนวนไอเท็มต่อ Code):</label>
                                        <input type="number" id="quantity" class="form-control" min="1" value="1" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="items">รายการไอเท็ม (รูปแบบ: itemid:option:duration):</label>
                                <textarea id="items" class="form-control" rows="3" placeholder="ตัวอย่าง: 1:0:31,10:0:31,1214:0:31" required></textarea>
                                <small class="text-muted">แยกแต่ละไอเท็มด้วยเครื่องหมาย , (comma)</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="expiryDate">วันหมดอายุ (ไม่บังคับ):</label>
                                        <input type="datetime-local" id="expiryDate" class="form-control">
                                        <small class="text-muted">หากไม่กำหนด Code จะไม่หมดอายุ</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="codePrefix">รูปแบบ Code:</label>
                                        <select id="codePrefix" class="form-control">
                                            <option value="XXXX-XXXX-XXXX-XXXX">XXXX-XXXX-XXXX-XXXX</option>
                                            <option value="XXXXXXXX-XXXX-XXXX">XXXXXXXX-XXXX-XXXX</option>
                                            <option value="XXXXXXXXXXXXXXXXXXXX">20 ตัวอักษร (ไม่มีขีด)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-magic"></i> สร้าง Redeem Code
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">ตัวอย่างรูปแบบไอเท็ม</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>รูปแบบ:</strong> itemid:option:duration</p>
                        <hr>
                        <p><strong>ตัวอย่าง:</strong></p>
                        <ul class="list-unstyled">
                            <li><code>1:0:31</code> - Upgrade Core (High)</li>
                            <li><code>10:0:31</code> - Upgrade Core (Medium)</li>
                            <li><code>1214:0:31</code> - ไอเท็มพิเศษ</li>
                        </ul>
                        <hr>
                        <p><strong>หมายเหตุ:</strong></p>
                        <ul class="small">
                            <li>itemid = รหัสไอเท็ม</li>
                            <li>option = ตัวเลือกไอเท็ม (มักเป็น 0)</li>
                            <li>duration = ระยะเวลา (31 = ถาวร)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- แสดงผลลัพธ์ -->
        <div id="resultSection" style="display: none;" class="mt-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">ผลลัพธ์การสร้าง Code</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button id="downloadCodes" class="btn btn-primary">
                                <i class="fas fa-download"></i> ดาวน์โหลด Codes (TXT)
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <span id="codeStats" class="badge badge-info"></span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="codesTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Code</th>
                                    <th>Items</th>
                                    <th>Quantity</th>
                                    <th>วันที่สร้าง</th>
                                    <th>วันหมดอายุ</th>
                                    <th>สถานะ</th>
                                </tr>
                            </thead>
                            <tbody id="codesTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- รายการ Code ที่มีอยู่ -->
        <div class="card mt-4">
            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0">รายการ Redeem Code ทั้งหมด</h6>
                <div>
                    <button id="downloadAllCodes" class="btn btn-sm btn-primary mr-2">
                        <i class="fas fa-download"></i> ดาวน์โหลด Code ทั้งหมด
                    </button>
                    <button id="downloadUnusedCodes" class="btn btn-sm btn-success mr-2">
                        <i class="fas fa-download"></i> ดาวน์โหลด Code ที่ยังไม่ใช้
                    </button>
                    <div class="btn-group mr-2" role="group">
                        <button type="button" class="btn btn-sm btn-danger dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-trash-alt"></i> ลบ Code
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" id="deleteAllUnusedCodes">
                                <i class="fas fa-trash"></i> ลบ Code ที่ยังไม่ใช้ทั้งหมด
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="#" id="deleteAllCodes">
                                <i class="fas fa-trash-alt"></i> ลบ Code ทั้งหมด (ทั้งใช้และไม่ใช้)
                            </a>
                        </div>
                    </div>
                    <button id="refreshCodes" class="btn btn-sm btn-light">
                        <i class="fas fa-sync"></i> รีเฟรช
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="allCodesTable">
                        <thead class="thead-light">
                            <tr>
                                <th>ID</th>
                                <th>Code</th>
                                <th>Items</th>
                                <th>Quantity</th>
                                <th>วันที่สร้าง</th>
                                <th>วันหมดอายุ</th>
                                <th>สถานะ</th>
                                <th>การจัดการ</th>
                            </tr>
                        </thead>
                        <tbody id="allCodesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Log การใช้งาน Code -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Log การใช้งาน Redeem Code</h6>
                <div>
                    <a href="files/game_systems/debug_usage_logs.php" target="_blank" class="btn btn-sm btn-warning mr-2">
                        <i class="fas fa-bug"></i> Debug
                    </a>
                    <button id="downloadUsageLogs" class="btn btn-sm btn-light mr-2">
                        <i class="fas fa-download"></i> ดาวน์โหลด Log
                    </button>
                    <button id="deleteAllLogs" class="btn btn-sm btn-danger mr-2">
                        <i class="fas fa-trash-alt"></i> ลบ Log ทั้งหมด
                    </button>
                    <button id="refreshLogs" class="btn btn-sm btn-light">
                        <i class="fas fa-sync"></i> รีเฟรช Log
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="usageLogsTable">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Code ID</th>
                                <th>Code</th>
                                <th>User Num</th>
                                <th>วันที่ใช้</th>
                                <th>การจัดการ</th>
                            </tr>
                        </thead>
                        <tbody id="usageLogsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // โหลดรายการ codes ที่มีอยู่
    loadAllCodes();
    loadUsageLogs();

    // Event handlers
    $('#generateCodeForm').submit(function(e) {
        e.preventDefault();
        generateCodes();
    });

    $('#refreshCodes').click(function() {
        loadAllCodes();
    });

    $('#refreshLogs').click(function() {
        loadUsageLogs();
    });
    
    $('#downloadCodes').click(function() {
        downloadCodes();
    });

    $('#downloadAllCodes').click(function() {
        downloadAllCodes();
    });

    $('#downloadUnusedCodes').click(function() {
        downloadUnusedCodes();
    });

    $('#deleteAllUnusedCodes').click(function(e) {
        e.preventDefault();
        deleteAllUnusedCodes();
    });

    $('#deleteAllCodes').click(function(e) {
        e.preventDefault();
        deleteAllCodes();
    });

    $('#downloadUsageLogs').click(function() {
        downloadUsageLogs();
    });

    $('#deleteAllLogs').click(function() {
        deleteAllUsageLogs();
    });
});

function generateCodes() {
    const formData = {
        action: 'generate',
        count: $('#codeCount').val(),
        items: $('#items').val().trim(),
        quantity: $('#quantity').val(),
        expiry_date: $('#expiryDate').val(),
        code_format: $('#codePrefix').val()
    };

    console.log('Sending data:', formData);

    // ตรวจสอบข้อมูล
    if (!formData.items) {
        alert('กรุณากรอกรายการไอเท็ม');
        return;
    }

    if (formData.count < 1 || formData.count > 1000) {
        alert('จำนวน Code ต้องอยู่ระหว่าง 1-1000');
        return;
    }

    // ตรวจสอบรูปแบบวันที่
    if (formData.expiry_date) {
        const expiryDate = new Date(formData.expiry_date);
        const now = new Date();

        if (isNaN(expiryDate.getTime())) {
            alert('รูปแบบวันที่ไม่ถูกต้อง');
            return;
        }

        if (expiryDate <= now) {
            alert('วันหมดอายุต้องเป็นวันที่ในอนาคต');
            return;
        }

        // ส่งในรูปแบบ ISO string ที่ PHP สามารถแปลงได้
        formData.expiry_date = formData.expiry_date.replace('T', ' ') + ':00';
    }

    // แสดง loading
    $('#generateCodeForm button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังสร้าง...');

    $.post('files/game_systems/class_module/redeem_code_api_simple.php', formData, function(data) {
        console.log('Response received:', data);

        if (data.success) {
            displayGeneratedCodes(data.codes);
            loadAllCodes(); // รีเฟรชรายการทั้งหมด
            alert(`สร้าง ${data.codes.length} codes สำเร็จ!`);
        } else {
            console.error('API Error:', data.message);
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถสร้าง codes ได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        console.error('Response Text:', xhr.responseText);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#generateCodeForm button[type="submit"]').prop('disabled', false).html('<i class="fas fa-magic"></i> สร้าง Redeem Code');
    });
}

function displayGeneratedCodes(codes) {
    const tbody = $('#codesTableBody');
    tbody.empty();
    
    codes.forEach(function(code) {
        const expiryText = code.expiry_date ? code.expiry_date : 'ไม่หมดอายุ';
        tbody.append(`
            <tr>
                <td>${code.id}</td>
                <td><code>${code.code}</code></td>
                <td><small>${code.items}</small></td>
                <td>${code.quantity}</td>
                <td>${code.datecreated}</td>
                <td>${expiryText}</td>
                <td><span class="badge badge-success">ยังไม่ใช้งาน</span></td>
            </tr>
        `);
    });
    
    $('#codeStats').text(`สร้างแล้ว ${codes.length} codes`);
    $('#resultSection').show();
    
    // เก็บ codes สำหรับดาวน์โหลด
    window.generatedCodes = codes;
}

function loadAllCodes() {
    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'list'
    }, function(data) {
        if (data.success) {
            displayAllCodes(data.codes);
        }
    }, 'json');
}

function displayAllCodes(codes) {
    const tbody = $('#allCodesTableBody');
    tbody.empty();

    if (codes.length === 0) {
        tbody.append('<tr><td colspan="8" class="text-center">ไม่มีข้อมูล</td></tr>');
        return;
    }

    // เก็บข้อมูล codes ไว้ใช้สำหรับดาวน์โหลด
    window.allCodes = codes;

    codes.forEach(function(code) {
        const statusText = code.status == '0' ? 'ยังไม่ใช้งาน' : 'ใช้งานแล้ว';
        const statusClass = code.status == '0' ? 'success' : 'secondary';
        const expiryText = code.expiry_date ? code.expiry_date : 'ไม่หมดอายุ';

        tbody.append(`
            <tr>
                <td>${code.id}</td>
                <td><code>${code.code}</code></td>
                <td><small>${code.items}</small></td>
                <td>${code.quantity}</td>
                <td>${code.datecreated}</td>
                <td>${expiryText}</td>
                <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                <td>
                    ${code.status == '0' ? `
                        <button class="btn btn-sm btn-warning mr-1" onclick="editCode(${code.id}, '${code.code}', '${code.items}', ${code.quantity}, '${code.expiry_date || ''}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCode(${code.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : '<span class="text-muted">ใช้แล้ว</span>'}
                </td>
            </tr>
        `);
    });
}

function deleteCode(id) {
    if (!confirm('คุณต้องการลบ Code นี้หรือไม่?')) {
        return;
    }
    
    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'delete',
        id: id
    }, function(data) {
        if (data.success) {
            loadAllCodes();
            alert('ลบ Code สำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json');
}

function downloadCodes() {
    if (!window.generatedCodes || window.generatedCodes.length === 0) {
        alert('ไม่มี codes ให้ดาวน์โหลด');
        return;
    }

    let content = 'Redeem Codes Generated on ' + new Date().toLocaleString() + '\n';
    content += '='.repeat(50) + '\n\n';

    window.generatedCodes.forEach(function(code, index) {
        content += `${index + 1}. ${code.code}\n`;
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'redeem_codes_' + new Date().getTime() + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function downloadUnusedCodes() {
    if (!window.allCodes || window.allCodes.length === 0) {
        alert('ไม่มีข้อมูล codes');
        return;
    }

    // กรอง codes ที่ยังไม่ใช้งาน (status = '0')
    const unusedCodes = window.allCodes.filter(code => code.status == '0');

    if (unusedCodes.length === 0) {
        alert('ไม่มี codes ที่ยังไม่ได้ใช้งาน');
        return;
    }

    let content = 'Unused Redeem Codes - Downloaded on ' + new Date().toLocaleString() + '\n';
    content += '='.repeat(60) + '\n\n';
    content += `Total Unused Codes: ${unusedCodes.length}\n\n`;

    unusedCodes.forEach(function(code, index) {
        content += `${index + 1}. ${code.code}\n`;
        content += `   Items: ${code.items}\n`;
        content += `   Quantity: ${code.quantity}\n`;
        content += `   Created: ${code.datecreated}\n`;
        if (code.expiry_date) {
            content += `   Expires: ${code.expiry_date}\n`;
        }
        content += '\n';
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'unused_redeem_codes_' + new Date().getTime() + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`ดาวน์โหลด ${unusedCodes.length} codes ที่ยังไม่ได้ใช้งานสำเร็จ!`);
}

function downloadAllCodes() {
    if (!window.allCodes || window.allCodes.length === 0) {
        alert('ไม่มีข้อมูล codes');
        return;
    }

    const usedCodes = window.allCodes.filter(code => code.status == '1');
    const unusedCodes = window.allCodes.filter(code => code.status == '0');

    let content = 'All Redeem Codes - Downloaded on ' + new Date().toLocaleString() + '\n';
    content += '='.repeat(60) + '\n\n';
    content += `Total Codes: ${window.allCodes.length}\n`;
    content += `Used Codes: ${usedCodes.length}\n`;
    content += `Unused Codes: ${unusedCodes.length}\n\n`;

    // ส่วน Codes ที่ยังไม่ใช้งาน
    if (unusedCodes.length > 0) {
        content += 'UNUSED CODES (ยังไม่ใช้งาน)\n';
        content += '-'.repeat(40) + '\n';
        unusedCodes.forEach(function(code, index) {
            content += `${index + 1}. ${code.code}\n`;
            content += `   Items: ${code.items}\n`;
            content += `   Quantity: ${code.quantity}\n`;
            content += `   Created: ${code.datecreated}\n`;
            if (code.expiry_date) {
                content += `   Expires: ${code.expiry_date}\n`;
            }
            content += '\n';
        });
        content += '\n';
    }

    // ส่วน Codes ที่ใช้แล้ว
    if (usedCodes.length > 0) {
        content += 'USED CODES (ใช้งานแล้ว)\n';
        content += '-'.repeat(40) + '\n';
        usedCodes.forEach(function(code, index) {
            content += `${index + 1}. ${code.code}\n`;
            content += `   Items: ${code.items}\n`;
            content += `   Quantity: ${code.quantity}\n`;
            content += `   Created: ${code.datecreated}\n`;
            if (code.expiry_date) {
                content += `   Expires: ${code.expiry_date}\n`;
            }
            content += '\n';
        });
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'all_redeem_codes_' + new Date().getTime() + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`ดาวน์โหลด ${window.allCodes.length} codes ทั้งหมดสำเร็จ! (ใช้แล้ว: ${usedCodes.length}, ยังไม่ใช้: ${unusedCodes.length})`);
}

function deleteAllUnusedCodes() {
    if (!window.allCodes || window.allCodes.length === 0) {
        alert('ไม่มีข้อมูล codes');
        return;
    }

    const unusedCodes = window.allCodes.filter(code => code.status == '0');

    if (unusedCodes.length === 0) {
        alert('ไม่มี codes ที่ยังไม่ได้ใช้งานให้ลบ');
        return;
    }

    const confirmMessage = `คุณต้องการลบ codes ที่ยังไม่ได้ใช้งานทั้งหมด ${unusedCodes.length} รายการหรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้!`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // แสดง loading
    $('#deleteAllUnusedCodes').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังลบ...');

    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'delete_all_unused'
    }, function(data) {
        if (data.success) {
            loadAllCodes(); // รีเฟรชรายการ
            alert(`ลบ codes ที่ยังไม่ได้ใช้งานสำเร็จ ${data.deleted_count} รายการ`);
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#deleteAllUnusedCodes').prop('disabled', false).html('<i class="fas fa-trash-alt"></i> ลบ Code ที่ยังไม่ใช้ทั้งหมด');
    });
}

function editCode(id, code, items, quantity, expiryDate) {
    // สร้าง modal หรือ form สำหรับแก้ไข
    const newItems = prompt('แก้ไขรายการไอเท็ม (รูปแบบ: itemid:option:duration):', items);
    if (newItems === null) return; // ยกเลิก

    const newQuantity = prompt('แก้ไขจำนวน:', quantity);
    if (newQuantity === null) return; // ยกเลิก

    const newExpiryDate = prompt('แก้ไขวันหมดอายุ (รูปแบบ: YYYY-MM-DD HH:MM:SS หรือเว้นว่างไว้):', expiryDate || '');
    if (newExpiryDate === null) return; // ยกเลิก

    // ตรวจสอบข้อมูล
    if (!newItems.trim()) {
        alert('กรุณากรอกรายการไอเท็ม');
        return;
    }

    if (isNaN(newQuantity) || newQuantity < 1) {
        alert('จำนวนต้องเป็นตัวเลขมากกว่า 0');
        return;
    }

    // ตรวจสอบรูปแบบไอเท็ม
    const itemList = newItems.split(',');
    for (let item of itemList) {
        item = item.trim();
        if (!/^\d+:\d+:\d+$/.test(item)) {
            alert(`รูปแบบไอเท็มไม่ถูกต้อง: ${item}`);
            return;
        }
    }

    // ส่งข้อมูลไปอัปเดต
    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'edit',
        id: id,
        items: newItems.trim(),
        quantity: parseInt(newQuantity),
        expiry_date: newExpiryDate.trim() || null
    }, function(data) {
        if (data.success) {
            loadAllCodes(); // รีเฟรชรายการ
            alert('แก้ไขข้อมูลสำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถแก้ไขได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    });
}

function deleteAllCodes() {
    if (!window.allCodes || window.allCodes.length === 0) {
        alert('ไม่มีข้อมูล codes');
        return;
    }

    const usedCodes = window.allCodes.filter(code => code.status == '1');
    const unusedCodes = window.allCodes.filter(code => code.status == '0');
    const totalCodes = window.allCodes.length;

    const confirmMessage = `⚠️ คำเตือน: การลบ Code ทั้งหมด ⚠️\n\n` +
                          `จำนวน Code ทั้งหมด: ${totalCodes} รายการ\n` +
                          `- Code ที่ใช้แล้ว: ${usedCodes.length} รายการ\n` +
                          `- Code ที่ยังไม่ใช้: ${unusedCodes.length} รายการ\n\n` +
                          `การดำเนินการนี้จะลบ Code ทั้งหมดอย่างถาวร!\n` +
                          `ไม่สามารถยกเลิกหรือกู้คืนได้!\n\n` +
                          `คุณแน่ใจหรือไม่ที่ต้องการดำเนินการต่อ?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // ยืนยันอีกครั้ง
    const finalConfirm = `กรุณายืนยันอีกครั้ง!\n\nคุณต้องการลบ Code ทั้งหมด ${totalCodes} รายการ อย่างถาวรหรือไม่?\n\nพิมพ์ "DELETE ALL" เพื่อยืนยัน:`;
    const userInput = prompt(finalConfirm);

    if (userInput !== "DELETE ALL") {
        alert('ยกเลิกการลบ Code ทั้งหมด');
        return;
    }

    // แสดง loading
    $('#deleteAllCodes').closest('.btn-group').find('.dropdown-toggle')
        .prop('disabled', true)
        .html('<i class="fas fa-spinner fa-spin"></i> กำลังลบ...');

    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'delete_all_codes'
    }, function(data) {
        if (data.success) {
            loadAllCodes(); // รีเฟรชรายการ
            alert(`ลบ Code ทั้งหมดสำเร็จ!\n\nรายละเอียด:\n- ลบทั้งหมด: ${data.deleted_count} รายการ\n- Code ที่ใช้แล้ว: ${data.used_count} รายการ\n- Code ที่ยังไม่ใช้: ${data.unused_count} รายการ`);
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#deleteAllCodes').closest('.btn-group').find('.dropdown-toggle')
            .prop('disabled', false)
            .html('<i class="fas fa-trash-alt"></i> ลบ Code');
    });
}

function loadUsageLogs() {
    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'list_usage_logs'
    }, function(data) {
        console.log('Usage logs response:', data);
        if (data.success) {
            displayUsageLogs(data.logs);
        } else {
            console.error('Failed to load usage logs:', data.message);
            $('#usageLogsTableBody').html('<tr><td colspan="6" class="text-center text-danger">เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถโหลดข้อมูลได้') + '</td></tr>');
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error loading usage logs:', error);
        console.error('Response Text:', xhr.responseText);
        $('#usageLogsTableBody').html('<tr><td colspan="6" class="text-center text-danger">เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error + '</td></tr>');
    });
}

function displayUsageLogs(logs) {
    const tbody = $('#usageLogsTableBody');
    tbody.empty();

    console.log('Displaying usage logs:', logs);

    if (!logs || logs.length === 0) {
        tbody.append('<tr><td colspan="6" class="text-center">ไม่มีข้อมูล Log การใช้งาน</td></tr>');
        return;
    }

    // เก็บข้อมูล logs ไว้ใช้สำหรับดาวน์โหลด
    window.usageLogs = logs;

    logs.forEach(function(log, index) {
        console.log(`Log ${index}:`, log);
        const usedDate = log.dateused || log.used_date || log.datecreated || '-';

        tbody.append(`
            <tr>
                <td>${log.id || '-'}</td>
                <td>${log.CodeID || '-'}</td>
                <td><code>${log.code || '-'}</code></td>
                <td>${log.UserNum || '-'}</td>
                <td>${usedDate}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deleteUsageLog(${log.id || 0})">
                        <i class="fas fa-trash"></i> ลบ
                    </button>
                </td>
            </tr>
        `);
    });
}

function deleteUsageLog(id) {
    if (!confirm('คุณต้องการลบ Log การใช้งานนี้หรือไม่?')) {
        return;
    }

    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'delete_usage_log',
        id: id
    }, function(data) {
        if (data.success) {
            loadUsageLogs();
            alert('ลบ Log การใช้งานสำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json');
}

function deleteAllUsageLogs() {
    if (!window.usageLogs || window.usageLogs.length === 0) {
        alert('ไม่มีข้อมูล Log การใช้งาน');
        return;
    }

    const confirmMessage = `คุณต้องการลบ Log การใช้งานทั้งหมด ${window.usageLogs.length} รายการหรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้!`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // แสดง loading
    $('#deleteAllLogs').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังลบ...');

    $.post('files/game_systems/class_module/redeem_code_api_simple.php', {
        action: 'delete_all_usage_logs'
    }, function(data) {
        if (data.success) {
            loadUsageLogs(); // รีเฟรชรายการ
            alert(`ลบ Log การใช้งานทั้งหมดสำเร็จ ${data.deleted_count} รายการ`);
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
        }
    }, 'json').fail(function(xhr, status, error) {
        console.error('AJAX Error:', error);
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
    }).always(function() {
        $('#deleteAllLogs').prop('disabled', false).html('<i class="fas fa-trash-alt"></i> ลบ Log ทั้งหมด');
    });
}

function downloadUsageLogs() {
    if (!window.usageLogs || window.usageLogs.length === 0) {
        alert('ไม่มีข้อมูล Log การใช้งาน');
        return;
    }

    let content = 'Redeem Code Usage Logs - Downloaded on ' + new Date().toLocaleString() + '\n';
    content += '='.repeat(80) + '\n\n';
    content += `Total Usage Records: ${window.usageLogs.length}\n\n`;

    window.usageLogs.forEach(function(log, index) {
        content += `${index + 1}. Log ID: ${log.id || 'N/A'}\n`;
        content += `   Code ID: ${log.CodeID || 'N/A'}\n`;
        content += `   Code: ${log.code || 'N/A'}\n`;
        content += `   User Num: ${log.UserNum || 'N/A'}\n`;
        content += `   Used Date: ${log.dateused || log.used_date || 'N/A'}\n`;
        content += '\n';
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'redeem_code_usage_logs_' + new Date().getTime() + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`ดาวน์โหลด Log การใช้งาน ${window.usageLogs.length} รายการสำเร็จ!`);
}
</script>
