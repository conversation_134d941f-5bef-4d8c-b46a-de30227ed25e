<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-shield-check"></i> ทดสอบการแก้ไขแบบไม่ใช้ Session Management
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหาเด้งออกจากระบบโดยลบ Session Checking</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ลบ session management ใน JavaScript ออกเพื่อป้องกันการเด้งออกจากระบบ
                </div>
                
                <h5>🔧 การแก้ไขที่ทำ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ สิ่งที่ลบออก</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-times text-danger"></i> ฟังก์ชัน handleSessionExpired()</li>
                                    <li><i class="fal fa-times text-danger"></i> ฟังก์ชัน checkSessionStatus()</li>
                                    <li><i class="fal fa-times text-danger"></i> setInterval สำหรับ session check</li>
                                    <li><i class="fal fa-times text-danger"></i> การตรวจสอบ 401/403 status</li>
                                    <li><i class="fal fa-times text-danger"></i> การตรวจสอบ HTML response</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ สิ่งที่เก็บไว้</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> AJAX calls ผ่าน routing system</li>
                                    <li><i class="fal fa-check text-success"></i> Error handling พื้นฐาน</li>
                                    <li><i class="fal fa-check text-success"></i> การแจ้งเตือนเมื่อมี error</li>
                                    <li><i class="fal fa-check text-success"></i> Auto refresh functionality</li>
                                    <li><i class="fal fa-check text-success"></i> Notification system</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📝 รายละเอียดการแก้ไข</h5>
                
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. ลบ Session Management Functions
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ฟังก์ชันที่ลบออก:</strong></p>
                                <pre><code>// ลบออกแล้ว
function handleSessionExpired() { ... }
function checkSessionStatus() { ... }
setInterval(checkSessionStatus, 300000);
setTimeout(checkSessionStatus, 60000);</code></pre>
                                
                                <p><strong>เหตุผล:</strong> ฟังก์ชันเหล่านี้ทำให้เกิดการเด้งออกจากระบบ</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. ลบการตรวจสอบ Session ใน AJAX
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ก่อนแก้ไข:</strong></p>
                                <pre><code>if (responseText.includes('<!DOCTYPE')) {
    handleSessionExpired();
    return;
}
if (response.status === 401 || response.status === 403) {
    handleSessionExpired();
    return;
}</code></pre>
                                
                                <p><strong>หลังแก้ไข:</strong></p>
                                <pre><code>// ลบการตรวจสอบ session ออก
// เก็บเฉพาะ error logging
console.error('API returned invalid JSON:', responseText);</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. ปรับปรุง Error Handling
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>Error Handling ใหม่:</strong></p>
                                <pre><code>} catch (error) {
    console.error('Error fetching stats:', error);
    addNotificationToArea(
        '❌ ไม่สามารถดึงข้อมูลสถิติได้', 
        'danger', 
        new Date().toLocaleString('th-TH')
    );
}</code></pre>
                                
                                <p><strong>ผลลัพธ์:</strong> แสดงการแจ้งเตือนแทนการเด้งออกจากระบบ</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบระบบ</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="testAutoRefresh()">
                            <i class="fal fa-sync"></i> ทดสอบ Auto Refresh
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testAPICall()">
                            <i class="fal fa-plug"></i> ทดสอบ API Call
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="openCharacterMonitor()">
                            <i class="fal fa-external-link"></i> เปิด Character Monitor
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testErrorHandling()">
                            <i class="fal fa-exclamation-triangle"></i> ทดสอบ Error Handling
                        </button>
                    </div>
                </div>
                
                <div id="test-results" class="mt-3"></div>
                
                <h5 class="mt-4">📊 ผลการทดสอบ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>✅ สิ่งที่ควรทำงาน</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>กดปุ่ม "เริ่มอัพเดทอัตโนมัติ" ไม่เด้งออก</li>
                                    <li>AJAX calls ทำงานปกติ</li>
                                    <li>การแจ้งเตือนแสดงผลได้</li>
                                    <li>ข้อมูลอัพเดทได้</li>
                                    <li>ไม่มี JavaScript errors</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>⚠️ สิ่งที่ต้องระวัง</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>ไม่มีการตรวจสอบ session อัตโนมัติ</li>
                                    <li>ถ้า session หมดจริง ต้องรีเฟรชหน้าเอง</li>
                                    <li>อาจได้รับ error แต่ไม่เด้งออก</li>
                                    <li>ต้องสังเกต error ใน Console</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 วิธีใช้งาน</h5>
                <div class="alert alert-info">
                    <h6><i class="fal fa-info-circle"></i> คำแนะนำการใช้งาน:</h6>
                    <ol class="mb-0">
                        <li>เปิด Character Monitor ตามปกติ</li>
                        <li>กดปุ่ม "เริ่มอัพเดทอัตโนมัติ" ได้เลย</li>
                        <li>ถ้าเกิด error จะแสดงการแจ้งเตือนแทนการเด้งออก</li>
                        <li>หากมีปัญหา ให้รีเฟรชหน้าเว็บ</li>
                        <li>ตรวจสอบ Console (F12) หา errors</li>
                    </ol>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ลบ session management ใน JavaScript</li>
                                <li>✅ ลบการตรวจสอบ session ใน AJAX</li>
                                <li>✅ เก็บ error handling พื้นฐาน</li>
                                <li>✅ แสดงการแจ้งเตือนแทนการเด้งออก</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Auto refresh ทำงานได้ปกติ</li>
                                <li>✅ ไม่เด้งออกจากระบบ</li>
                                <li>✅ AJAX calls ผ่าน routing</li>
                                <li>✅ Notification system ทำงาน</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAutoRefresh() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = `
        <div class="alert alert-info">
            <h6><i class="fal fa-sync fa-spin"></i> ทดสอบ Auto Refresh</h6>
            <p>การทดสอบนี้จะ:</p>
            <ol>
                <li>เปิด Character Monitor ในแท็บใหม่</li>
                <li>ให้คุณกดปุ่ม "เริ่มอัพเดทอัตโนมัติ"</li>
                <li>ตรวจสอบว่าไม่เด้งออกจากระบบ</li>
            </ol>
            <p><strong>หมายเหตุ:</strong> ถ้าไม่เด้งออก แสดงว่าการแก้ไขสำเร็จ</p>
        </div>
    `;
    
    setTimeout(() => {
        window.open('?url=manager_charecter/character-monitor', '_blank');
    }, 1000);
}

async function testAPICall() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ API call...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=live_stats', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            }
        });
        
        const responseText = await response.text();
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'warning') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'exclamation-triangle') + '"></i> API Call Test Result</h6>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Response Type:</strong> ' + (responseText.trim().startsWith('{') ? 'JSON' : 'HTML/Other') + '</p>';
        
        if (response.ok && responseText.trim().startsWith('{')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>API Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                html += '<p><strong>ผลการทดสอบ:</strong> API ทำงานปกติ ไม่ทำให้เด้งออกจากระบบ</p>';
            } catch (e) {
                html += '<p><strong>JSON Parse:</strong> ❌ Failed</p>';
            }
        } else {
            html += '<p><strong>ผลการทดสอบ:</strong> API อาจมีปัญหา แต่ไม่ทำให้เด้งออกจากระบบ</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="fal fa-exclamation-triangle"></i> API Test Error</h6>
                <p><strong>Error:</strong> ${error.message}</p>
                <p><strong>ผลการทดสอบ:</strong> เกิด error แต่ไม่ทำให้เด้งออกจากระบบ ✅</p>
            </div>
        `;
    }
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}

function testErrorHandling() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h6><i class="fal fa-check-circle"></i> Error Handling Test</h6>
            <p>การจัดการ error ใหม่:</p>
            <ul>
                <li>✅ ไม่มีการตรวจสอบ session ที่ทำให้เด้งออก</li>
                <li>✅ แสดงการแจ้งเตือนใน notification area</li>
                <li>✅ Log error ใน Console</li>
                <li>✅ ไม่ redirect ไปหน้า login</li>
                <li>✅ ไม่หยุดการทำงานของระบบ</li>
            </ul>
            <p><strong>สรุป:</strong> ระบบจะทำงานต่อไปแม้เกิด error</p>
        </div>
    `;
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
