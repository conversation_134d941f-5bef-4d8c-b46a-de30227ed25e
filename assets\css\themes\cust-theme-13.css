/* #THEME COLOR (variable overrides)
========================================================================== */
/* #GLOBAL IMPORTS
========================================================================== */
/* #IMPORTS ~~
========================================================================== */
/*---------------------------------------------------
    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) 
  -------------------------------- -------------------
    LESS ELEMENTS made by Dmitry Fadeyev (http://fadeyev.net)
    SASS port by Samuel Beek (http://samuelbeek.com) 
  ---------------------------------------------------*/
/*------------------------
    Usage

  $color-white: hexToRGBString(#fff) => "255,255,255"
  $color-white: hexToRGBString(rgb(255,255,255)) => "255,255,255"
  $color-white: hexToRGBString(rgba(#fff,1)) => "255,255,255"
  
------------------------*/
/*------------------------
    Usage

    h1 {
      font-size: rem(32);
    }

    OR:

    h1 {
      font-size: rem(32px);
    }
------------------------*/
/*------------------------
  FADE IN
  e.g. @include fadeIn( 2s );
------------------------*/
/*------------------------
mixin that calculates if text needs to be light or dark
depending on the background color passed.

From this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast

usage:
@include text-contrast($bgcolor)
      
Color brightness is determined by the following formula: 
((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000
------------------------*/
/*------------------------
 color factory 
  eg: contrast-ink($contrastvalue)
------------------------*/
/*------------------------
 color factory 
  eg: @include paint($blue-grey-50, bg-blue-grey-50);
------------------------*/
/* backface visibility */
/* generate theme button */
/*  THEME COLORs
========================================================================== */
/* Looks good on chrome default color profile */
/* We will manually convert these primary colors to rgb for the dark mode option of the theme */
/* looks good in sRGB but washed up on chrome default 
$color-primary:						#826bb0;
$color-success:						#31cb55;
$color-info:						#5e93ec;
$color-warning:						#eec559;
$color-danger:						#dc4b92;
$color-fusion:						darken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */
/*  Color Polarity
========================================================================== */
/*  PAINTBUCKET MIXER
========================================================================== */
/* the grays */
/* the sapphires */
/* the emeralds */
/* the amethyths */
/* the topaz */
/* the rubies */
/* the graphites */
/*  Define universal border difition (div outlines, etc)
========================================================================== */
/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)
========================================================================== */
/* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */
/* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/
/* global var used for spacing*/
/* Uniform Padding variable */
/* Heads up! This is a global scoped variable - changing may impact the whole template */
/*   BOOTSTRAP OVERRIDES (bootstrap variables)
========================================================================== */
/* usage: theme-colors("primary"); */
/* forms */
/*$input-height:							calc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */
/* links */
/* checkbox */
/*$custom-file-height-inner:				calc(2.25rem - 1px);*/
/* not part of bootstrap variable */
/* custom checkbox */
/* custom range */
/* custom file */
/* badge */
/* cards */
/*border radius*/
/* alert */
/* toast */
/* breadcrumb */
/* input button */
/* nav link */
/* nav, tabs, pills */
/* tables */
/* dropdowns */
/* dropdowns sizes */
/* popovers */
/* tooltips */
/* modal */
/* reference guide
http://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/
8px = 0.5rem
9px = 0.5625rem
10px = 0.625rem
11px = 0.6875rem
12px = 0.75rem
13px = 0.8125rem
14px = 0.875rem
15px = 0.9375rem
16px = 1rem (base)
17px = 1.0625rem
18px = 1.125rem
19px = 1.1875rem
20px = 1.25rem
21px = 1.3125rem
22px = 1.375rem
24px = 1.5rem
25px = 1.5625rem
26px = 1.625rem
28px = 1.75rem
30px = 1.875rem
32px = 2rem
34px = 2.125rem
36px = 2.25rem
38px = 2.375rem
40px = 2.5rem
*/
/* Fonts */
/* carousel */
/*  BASE VARS
========================================================================== */
/* font vars below will auto change to rem values using function rem($value)*/
/* 11px   */
/* 12px   */
/* 12.5px */
/* 14px   */
/* 15px   */
/* 16px   */
/* 28px   */
/*  Font Family
========================================================================== */
/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */
/*  ANIMATIONS
========================================================================== */
/* this addresses all animation related to nav hide to nav minify */
/*  Z-INDEX declearation
========================================================================== */
/* we adjust bootstrap z-index to be higher than our higest z-index*/
/*  CUSTOM ICON PREFIX 
========================================================================== */
/*  PRINT CSS (landscape or portrait)
========================================================================== */
/* landscape or portrait */
/* auto, letter */
/*  Common Element Variables
========================================================================== */
/* Z-index decleartion "birds eye view"
========================================================================== */
/*  Components
========================================================================== */
/*  PAGE HEADER STUFF
========================================================================== */
/* colors */
/* height */
/* logo */
/* try not to go beywond the width of $main_nav_width value */
/* you may need to change this depending on your logo design */
/* adjust this as you see fit : left, right, center */
/* icon font size (not button) */
/* search input box */
/* suggestion: #ccced0*/
/* btn */
/* dropdown: app list */
/* badge */
/* COMPONENTS & MODS */
/*  NAVIGATION STUFF

Guide:

aside.page-sidebar ($nav-width, $nav-background)
	.page-logo
	.primary-nav
		.info-card
		ul.nav-menu
			li
				a (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)
					icon 
					span
					collapse-sign 
					
				ul.nav-menu-sub-one  
					li
						a ($nav-level-1... $nav-sub-link-height)
							span
							collapse-sign

						ul.nav-menu-sub-two
							li
								a ($nav-level-2... $nav-sub-link-height)
									span

		p.nav-title ($nav-title-*...)


========================================================================== */
/* main navigation */
/* left panel */
/* nav footer */
/* nav parent level-0 */
/* nav icon sizes */
/* badge default */
/* all child */
/* nav title */
/* nav Minify */
/* when the menu pops on hover */
/* navigation Width */
/* partial visibility of the menu */
/* top navigation */
/* nav Info Card (appears below the logo) */
/* width is auto */
/* nav DL labels for all child */
/* will be pulled to left as a negative value */
/*   MISC Settings
========================================================================== */
/* List Table */
/*   PAGE SETTINGS
========================================================================== */
/*   PAGE BREADCRUMB 
========================================================================== */
/*   PAGE COMPONENT PANELS 
========================================================================== */
/*   PAGE COMPONENT PROGRESSBARS 
========================================================================== */
/*   PAGE COMPONENT MESSENGER 
========================================================================== */
/*   FOOTER
========================================================================== */
/*   GLOBALS
========================================================================== */
/* ACCESSIBILITIES */
/* PLACEHOLDER 
============================================= 

EXAMPLE:

%bg-image {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.image-one {
    @extend %bg-image;
    background-image:url(/img/image-one.jpg");
}

RESULT:

.image-one, .image-two {
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

*/
.page-logo, .page-sidebar, .nav-footer, .bg-brand-gradient {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(51, 211, 225, 0.18)), to(transparent));
  background-image: linear-gradient(270deg, rgba(51, 211, 225, 0.18), transparent);
  background-color: #318c38; }

/*
%shadow-hover {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);
  }
}
*/
.btn-default {
  background-color: #f5f5f5;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f5f5f5), to(#f1f1f1));
  background-image: linear-gradient(to top, #f5f5f5, #f1f1f1);
  color: #444; }
  .btn-default:hover {
    border: 1px solid #c6c6c6; }
  .btn-default:focus {
    border-color: #90df97 !important; }
  .active.btn-default {
    background: #7dd984;
    color: #fff; }

.header-function-fixed .btn-switch[data-class="header-function-fixed"], .nav-function-fixed .btn-switch[data-class="nav-function-fixed"], .nav-function-minify .btn-switch[data-class="nav-function-minify"], .nav-function-hidden .btn-switch[data-class="nav-function-hidden"], .nav-function-top .btn-switch[data-class="nav-function-top"], .footer-function-fixed .btn-switch[data-class="footer-function-fixed"], .nav-mobile-push .btn-switch[data-class="nav-mobile-push"], .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"], .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"], .mod-main-boxed .btn-switch[data-class="mod-main-boxed"], .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"], .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"], .mod-pace-custom .btn-switch[data-class="mod-pace-custom"], .mod-bigger-font .btn-switch[data-class="mod-bigger-font"], .mod-high-contrast .btn-switch[data-class="mod-high-contrast"], .mod-color-blind .btn-switch[data-class="mod-color-blind"], .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"], .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"], .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"], .mod-disable-animation .btn-switch[data-class="mod-disable-animation"], .mod-nav-link .btn-switch[data-class="mod-nav-link"], .mod-nav-dark .btn-switch[data-class="mod-nav-dark"], .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] {
  color: #fff;
  background: #55ce5f !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"]:after, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"]:after, .nav-function-minify .btn-switch[data-class="nav-function-minify"]:after, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"]:after, .nav-function-top .btn-switch[data-class="nav-function-top"]:after, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"]:after, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"]:after, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"]:after, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"]:after, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"]:after, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"]:after, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"]:after, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"]:after, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"]:after, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"]:after, .mod-color-blind .btn-switch[data-class="mod-color-blind"]:after, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"]:after, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"]:after, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"]:after, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"]:after, .mod-nav-link .btn-switch[data-class="mod-nav-link"]:after, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"]:after, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"]:after {
    background: #fff !important;
    color: #55ce5f !important; }
  .header-function-fixed .btn-switch[data-class="header-function-fixed"] + .onoffswitch-title, .nav-function-fixed .btn-switch[data-class="nav-function-fixed"] + .onoffswitch-title, .nav-function-minify .btn-switch[data-class="nav-function-minify"] + .onoffswitch-title, .nav-function-hidden .btn-switch[data-class="nav-function-hidden"] + .onoffswitch-title, .nav-function-top .btn-switch[data-class="nav-function-top"] + .onoffswitch-title, .footer-function-fixed .btn-switch[data-class="footer-function-fixed"] + .onoffswitch-title, .nav-mobile-push .btn-switch[data-class="nav-mobile-push"] + .onoffswitch-title, .nav-mobile-no-overlay .btn-switch[data-class="nav-mobile-no-overlay"] + .onoffswitch-title, .nav-mobile-slide-out .btn-switch[data-class="nav-mobile-slide-out"] + .onoffswitch-title, .mod-main-boxed .btn-switch[data-class="mod-main-boxed"] + .onoffswitch-title, .mod-fixed-bg .btn-switch[data-class="mod-fixed-bg"] + .onoffswitch-title, .mod-clean-page-bg .btn-switch[data-class="mod-clean-page-bg"] + .onoffswitch-title, .mod-pace-custom .btn-switch[data-class="mod-pace-custom"] + .onoffswitch-title, .mod-bigger-font .btn-switch[data-class="mod-bigger-font"] + .onoffswitch-title, .mod-high-contrast .btn-switch[data-class="mod-high-contrast"] + .onoffswitch-title, .mod-color-blind .btn-switch[data-class="mod-color-blind"] + .onoffswitch-title, .mod-hide-nav-icons .btn-switch[data-class="mod-hide-nav-icons"] + .onoffswitch-title, .mod-hide-info-card .btn-switch[data-class="mod-hide-info-card"] + .onoffswitch-title, .mod-lean-subheader .btn-switch[data-class="mod-lean-subheader"] + .onoffswitch-title, .mod-disable-animation .btn-switch[data-class="mod-disable-animation"] + .onoffswitch-title, .mod-nav-link .btn-switch[data-class="mod-nav-link"] + .onoffswitch-title, .mod-nav-dark .btn-switch[data-class="mod-nav-dark"] + .onoffswitch-title, .mod-panel-icon .btn-switch[data-class="mod-panel-icon"] + .onoffswitch-title {
    color: #55ce5f; }

.nav-mobile-slide-out #nmp .onoffswitch-title, .nav-mobile-slide-out #nmno .onoffswitch-title, .nav-function-top #mnl .onoffswitch-title, .nav-function-minify #mnl .onoffswitch-title, .mod-hide-nav-icons #mnl .onoffswitch-title, .nav-function-top #nfh .onoffswitch-title {
  color: #d58100; }

.nav-mobile-slide-out #nmp .onoffswitch-title-desc, .nav-mobile-slide-out #nmno .onoffswitch-title-desc, .nav-function-top #mnl .onoffswitch-title-desc, .nav-function-minify #mnl .onoffswitch-title-desc, .mod-hide-nav-icons #mnl .onoffswitch-title-desc, .nav-function-top #nfh .onoffswitch-title-desc {
  color: #ec9f28; }

.dropdown-icon-menu > ul > li .btn, .header-btn {
  border: 1px solid #dedede;
  color: #a6a6a6; }
  .dropdown-icon-menu > ul > li .btn:hover, .header-btn:hover {
    border-color: #55ce5f;
    background: #7dd984;
    color: #fff; }

.nav-mobile-slide-out #nmp:after,
.nav-mobile-slide-out #nmno:after, .nav-function-top #mnl:after,
.nav-function-minify #mnl:after,
.mod-hide-nav-icons #mnl:after, .nav-function-top #nfh:after {
  background: #f9d999;
  border: 1px solid #d2910d;
  color: #1f1f1f; }

/* #GLOBAL IMPORTS
========================================================================== */
/*@import '_imports/_global-import';*/
/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)
                DOC: you can disable unused _modules
========================================================================== */
/* contains root variables to be used with css (see docs) */
/* html and body base styles */
html body {
  background-color: #fff; }
  html body a {
    color: #55ce5f;
    background-color: transparent; }
    html body a:hover {
      color: #69d472; }

.header-icon {
  color: #666666; }
  .header-icon:not(.btn) > [class*='fa-']:first-child,
  .header-icon:not(.btn) > .ni:first-child {
    color: #55ce5f; }
  .header-icon:not(.btn):hover > [class*='fa-']:only-child,
  .header-icon:not(.btn):hover > .ni {
    color: #404040; }
  .header-icon:not(.btn)[data-toggle="dropdown"] {
    /* header dropdowns */
    /* note: important rules to override popper's inline classes */
    /* end header dropdowns */ }
    .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] {
      color: #404040; }
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > [class*='fa-']:first-child,
      .header-icon:not(.btn)[data-toggle="dropdown"][aria-expanded="true"] > .ni:first-child {
        color: #404040 !important; }
    .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
      border-color: #ccc; }
  .header-icon:hover {
    color: #404040; }

.page-header {
  background-color: #fff; }

#search-field {
  background: transparent;
  border: 1px solid transparent; }

.notification li.unread {
  background: #fbe9c4; }

.notification li > :first-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06); }
  .notification li > :first-child:hover {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(29, 33, 41, 0.03)), to(rgba(29, 33, 41, 0.04)));
    background-image: linear-gradient(rgba(29, 33, 41, 0.03), rgba(29, 33, 41, 0.04)); }

.notification .name {
  color: #222222; }

.notification .msg-a,
.notification .msg-b {
  color: #555555; }

.notification.notification-layout-2 li {
  background: #f9f9f9; }
  .notification.notification-layout-2 li.unread {
    background: #fff; }
  .notification.notification-layout-2 li > :first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04); }

.notification.notification-layout-2:hover {
  cursor: pointer; }

.app-list-item {
  color: #666666; }
  .app-list-item:hover {
    border: 1px solid #e3e3e3; }
  .app-list-item:active {
    border-color: #55ce5f; }

@media (min-width: 992px) {
  .header-function-fixed.nav-function-top .page-header {
    -webkit-box-shadow: 0px 0px 28px 2px rgba(42, 147, 51, 0.13);
            box-shadow: 0px 0px 28px 2px rgba(42, 147, 51, 0.13); } }

.nav-title {
  color: #51c25b; }

.nav-menu li.open > a {
  color: white; }

.nav-menu li.active {
  /* arrow that appears next to active/selected items */ }
  .nav-menu li.active > a {
    color: white;
    background-color: rgba(255, 255, 255, 0.04);
    -webkit-box-shadow: inset 3px 0 0 #55ce5f;
            box-shadow: inset 3px 0 0 #55ce5f; }
    .nav-menu li.active > a:hover > [class*='fa-'],
    .nav-menu li.active > a:hover > .ni {
      color: #96c099; }
  .nav-menu li.active > ul {
    display: block; }
  .nav-menu li.active:not(.open) > a:before {
    color: #24b3a4; }

.nav-menu li a {
  color: #a4dfa9; }
  .nav-menu li a .dl-ref.label {
    color: rgba(255, 255, 255, 0.7); }
  .nav-menu li a > [class*='fa-'],
  .nav-menu li a > .ni {
    color: #59c562; }
  .nav-menu li a.collapsed .nav-menu-btn-sub-collapse {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg); }
  .nav-menu li a:hover {
    color: white;
    background-color: rgba(0, 0, 0, 0.1); }
    .nav-menu li a:hover .badge {
      color: #fff; }
    .nav-menu li a:hover > [class*='fa-'],
    .nav-menu li a:hover > .ni {
      color: #96c099; }
    .nav-menu li a:hover > .badge {
      -webkit-box-shadow: 0 0 0 1px rgba(59, 170, 69, 0.8);
              box-shadow: 0 0 0 1px rgba(59, 170, 69, 0.8);
      border: 1px solid rgba(59, 170, 69, 0.8); }
  .nav-menu li a:focus {
    color: white; }
    .nav-menu li a:focus .badge {
      color: #fff; }

.nav-menu li b.collapse-sign {
  color: #69d472; }

.nav-menu li > ul {
  background-color: rgba(0, 0, 0, 0.1); }
  .nav-menu li > ul li a {
    color: #92d997; }
    .nav-menu li > ul li a > [class*='fa-'],
    .nav-menu li > ul li a > .ni {
      color: #59c562; }
    .nav-menu li > ul li a > .badge {
      color: #fff;
      background-color: #c139fd; }
    .nav-menu li > ul li a:hover {
      color: white;
      background-color: rgba(0, 0, 0, 0.1); }
      .nav-menu li > ul li a:hover > .nav-link-text > [class*='fa-'],
      .nav-menu li > ul li a:hover > .nav-link-text > .ni {
        color: #96c099; }
  .nav-menu li > ul li.active > a {
    color: white;
    background-color: transparent; }
    .nav-menu li > ul li.active > a > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a > .nav-link-text > .ni {
      color: white; }
    .nav-menu li > ul li.active > a:hover > .nav-link-text > [class*='fa-'],
    .nav-menu li > ul li.active > a:hover > .nav-link-text > .ni {
      color: #96c099; }
  .nav-menu li > ul li > ul li.active > a {
    color: white; }
  .nav-menu li > ul li > ul li a {
    color: #8ad690; }
    .nav-menu li > ul li > ul li a:hover {
      color: white; }
    .nav-menu li > ul li > ul li a > .badge {
      color: #fff;
      background-color: #c139fd;
      border: 1px solid #525252; }

/* nav clean elements */
.nav-menu-clean {
  background: #fff; }
  .nav-menu-clean li a {
    color: #525252 !important; }
    .nav-menu-clean li a span {
      color: #525252 !important; }
    .nav-menu-clean li a:hover {
      background-color: #f4f4f4 !important; }

/* nav bordered elements */
.nav-menu-bordered {
  border: 1px solid rgba(0, 0, 0, 0.08); }
  .nav-menu-bordered li a {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08); }

.nav-filter input[type="text"] {
  background: rgba(0, 0, 0, 0.4);
  color: #fff; }
  .nav-filter input[type="text"]:not(:focus) {
    border-color: rgba(0, 0, 0, 0.1); }
  .nav-filter input[type="text"]:focus {
    border-color: #42bd4c; }

.info-card {
  color: #fff; }
  .info-card .info-card-text {
    text-shadow: #000 0 1px; }

@media (min-width: 992px) {
  .nav-function-top {
    /* correct search field color */ }
    .nav-function-top #search-field {
      color: #fff; }
    .nav-function-top:not(.header-function-fixed) #nff {
      position: relative; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title {
        color: #d58100; }
      .nav-function-top:not(.header-function-fixed) #nff .onoffswitch-title-desc {
        color: #ec9f28; }
      .nav-function-top:not(.header-function-fixed) #nff:after {
        background: #f9d999;
        border: 1px solid #d2910d;
        color: #1f1f1f; }
    .nav-function-top .page-header {
      background-image: -webkit-gradient(linear, right top, left top, from(rgba(51, 211, 225, 0.18)), to(transparent));
      background-image: linear-gradient(270deg, rgba(51, 211, 225, 0.18), transparent);
      background-color: #318c38;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(42, 147, 51, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(42, 147, 51, 0.13); }
      .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child,
      .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child {
        color: #7dd984; }
        .nav-function-top .page-header .header-icon:not(.btn) > [class*='fa-']:first-child:hover,
        .nav-function-top .page-header .header-icon:not(.btn) > .ni:first-child:hover {
          color: #a4e5aa; }
      .nav-function-top .page-header .badge.badge-icon {
        -webkit-box-shadow: 0 0 0 1px #41c84c;
                box-shadow: 0 0 0 1px #41c84c; }
    .nav-function-top .page-sidebar {
      background: #fff;
      -webkit-box-shadow: 0px 0px 14px 0px rgba(42, 147, 51, 0.13);
              box-shadow: 0px 0px 14px 0px rgba(42, 147, 51, 0.13); }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li.active > a:before {
        color: #24b3a4; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .ni,
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > [class*='fa-'] {
        color: inherit; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > a > .collapse-sign {
        color: #82d489; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li a {
        color: #318c38; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul {
        background: #3aa743; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li a {
          color: #a4dfa9; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li ul {
          background: #3aa743; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul li:hover > a {
          background: rgba(0, 0, 0, 0.1);
          color: #fff; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:after {
          background: transparent; }
        .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
          color: #3aa743; }
      .nav-function-top .page-sidebar .primary-nav .nav-menu > li:hover > a {
        color: #55ce5f;
        background: transparent; } }

@media (min-width: 992px) {
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu li.active.open > a:before {
    color: #24b3a4; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a > .nav-link-text {
    background: trasparent; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul {
    background-color: #318c38; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav .nav-menu > li > a + ul:before {
      color: #318c38; }
  .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover {
    overflow: visible; }
    .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a {
      background: #369b3f;
      color: #fff; }
      .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child {
        background: #318c38; }
        .nav-function-minify:not(.nav-function-top) .page-sidebar .primary-nav:hover .nav-menu > li:hover > a > .nav-link-text:last-child:before {
          color: #318c38; }
  .nav-function-minify:not(.nav-function-top) .page-header [data-class="nav-function-minify"] {
    background: #525252;
    border-color: #383838 !important;
    color: #fff !important; } }

.nav-footer .nav-footer-buttons > li > a {
  color: #70cd77; }

.nav-function-fixed .nav-footer {
  background: #318c38; }
  .nav-function-fixed .nav-footer:before {
    background: rgba(62, 178, 72, 0.2);
    background: -webkit-gradient(linear, left top, right top, from(#318c38), color-stop(50%, #4ac054), color-stop(50%, #4ac054), to(#318c38));
    background: linear-gradient(to right, #318c38 0%, #4ac054 50%, #4ac054 50%, #318c38 100%); }

@media (min-width: 992px) {
  .nav-function-minify .nav-footer {
    background-color: #2e8535; }
    .nav-function-minify .nav-footer [data-class="nav-function-minify"] {
      color: #59c562; }
    .nav-function-minify .nav-footer:hover {
      background-color: #35973d; }
      .nav-function-minify .nav-footer:hover [data-class="nav-function-minify"] {
        color: #96c099; } }

.page-content-wrapper {
  background-color: #fafdfb; }

.subheader-icon {
  color: #96c099; }

.subheader-title {
  color: #525252;
  text-shadow: #fff 0 1px; }
  .subheader-title small {
    color: #858585; }

.page-footer {
  background: #fff;
  color: #4d4d4d; }

.accordion .card .card-header {
  background-color: #f7f9fa; }
  .accordion .card .card-header .card-title {
    color: #55ce5f; }
    .accordion .card .card-header .card-title.collapsed {
      color: #858585; }

.accordion.accordion-clean .card-header {
  background: #fff; }

.accordion.accordion-hover .card-header {
  background: #fff; }
  .accordion.accordion-hover .card-header:hover .card-title.collapsed {
    color: #fff;
    background-color: #7dd984; }

.accordion.accordion-hover .card-title:not(.collapsed) {
  color: #fff;
  background-color: #55ce5f; }

/* 	DEV NOTE: The reason why we had to add this layer for alert colors is because BS4 
	does not allow you to add your own alert colors via variable control rather 
	through a systemetic agent that changes the theme colors. 

	REF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218
*/
.alert-primary {
  color: #607662;
  background-color: #f1f9f2;
  border-color: #cde4cf; }

.alert-success {
  color: #45a165;
  background-color: #f7fdf9;
  border-color: #a3ebbc; }

.alert-danger {
  color: #a102e7;
  background-color: #f7e5ff;
  border-color: #e19efe; }

.alert-warning {
  color: #8a5f09;
  background-color: #fbe7bf;
  border-color: #f3b943; }

.alert-info {
  color: #0aafbd;
  background-color: #e3fbfd;
  border-color: #82eff8; }

.alert-secondary {
  color: #525252;
  background-color: #fdfdfd;
  border-color: #dedede; }

.badge.badge-icon {
  background-color: #c139fd;
  color: #fff;
  -webkit-box-shadow: 0 0 0 1px #fff;
          box-shadow: 0 0 0 1px #fff; }

/* btn switch */
.btn-switch {
  background: #6b6b6b;
  color: white; }
  .btn-switch:hover {
    color: white; }
  .btn-switch:after {
    color: white; }
  .btn-switch.active {
    color: #fff;
    background: #55ce5f; }
    .btn-switch.active:before {
      color: rgba(0, 0, 0, 0.8); }
    .btn-switch.active:after {
      background: #fff;
      color: #55ce5f; }

/* button used to close filter and mobile search */
.btn-search-close {
  color: #fff; }

/* buttons used in the header section of the page */
.header-btn[data-class='mobile-nav-on'] {
  border-color: #b107fc;
  background-color: #b920fd;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#b920fd), to(#a102e7));
  background-image: linear-gradient(to top, #b920fd, #a102e7);
  color: #fff; }

/* dropdown btn */
/* used on info card pulldown filter */
.pull-trigger-btn {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.4);
  color: #fff !important;
  -webkit-box-shadow: 0px 0px 2px rgba(85, 206, 95, 0.3);
          box-shadow: 0px 0px 2px rgba(85, 206, 95, 0.3); }
  .pull-trigger-btn:hover {
    background: #55ce5f;
    border-color: #41c84c; }

/* btn misc */
.btn-outline-default {
  color: #212529;
  border-color: #E5E5E5; }
  .btn-outline-default:hover, .btn-outline-default:not(:disabled):not(.disabled):active, .btn-outline-default:not(:disabled):not(.disabled).active,
  .show > .btn-outline-default.dropdown-toggle {
    color: #212529;
    background-color: #f9f9f9;
    border-color: #E5E5E5; }
  .btn-outline-default.disabled, .btn-outline-default:disabled {
    color: #212529; }

/* btn shadows */
.btn-primary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(85, 206, 95, 0.5);
          box-shadow: 0 2px 6px 0 rgba(85, 206, 95, 0.5); }

.btn-secondary {
  -webkit-box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5);
          box-shadow: 0 2px 6px 0 rgba(108, 117, 125, 0.5); }

.btn-success {
  -webkit-box-shadow: 0 2px 6px 0 rgba(29, 201, 88, 0.5);
          box-shadow: 0 2px 6px 0 rgba(29, 201, 88, 0.5); }

.btn-info {
  -webkit-box-shadow: 0 2px 6px 0 rgba(33, 226, 243, 0.5);
          box-shadow: 0 2px 6px 0 rgba(33, 226, 243, 0.5); }

.btn-warning {
  -webkit-box-shadow: 0 2px 6px 0 rgba(241, 172, 33, 0.5);
          box-shadow: 0 2px 6px 0 rgba(241, 172, 33, 0.5); }

.btn-danger {
  -webkit-box-shadow: 0 2px 6px 0 rgba(193, 57, 253, 0.5);
          box-shadow: 0 2px 6px 0 rgba(193, 57, 253, 0.5); }

.btn-light {
  -webkit-box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 6px 0 rgba(255, 255, 255, 0.5); }

.btn-dark {
  -webkit-box-shadow: 0 2px 6px 0 rgba(82, 82, 82, 0.5);
          box-shadow: 0 2px 6px 0 rgba(82, 82, 82, 0.5); }

.btn-icon-light {
  color: rgba(255, 255, 255, 0.7) !important;
  border-color: transparent !important; }
  .btn-icon-light:not(.active):not(:active):not(:hover):not(:focus) {
    color: rgba(255, 255, 255, 0.7) !important; }
  .btn-icon-light:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.2) !important; }

/* bootstrap buttons */
.btn-link {
  color: #55ce5f; }
  .btn-link:hover {
    color: #36ba41; }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #55ce5f; }

.card-header {
  background-color: #f7f9fa; }

.carousel-control-prev:hover {
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

.carousel-control-next:hover {
  background: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.25)), color-stop(45%, rgba(0, 0, 0, 0)));
  background: linear-gradient(to left, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 45%); }

/* dropdown menu multi-level */
.dropdown-menu .dropdown-menu {
  background: #fff; }

.dropdown-menu .dropdown-multilevel:hover > .dropdown-item:not(.disabled) {
  background: #f8f9fa;
  color: #36ba41; }

.dropdown-item:hover, .dropdown-item:focus {
  color: #36ba41;
  background-color: #f8f9fa; }

.dropdown-item.active, .dropdown-item:active {
  color: #30a73a;
  background-color: #e0f6e1; }

.chat-segment-get .chat-message {
  background: #f1f0f0; }

.chat-segment-sent .chat-message {
  background: #1dc958; }

/* transparent modal */
.modal-transparent .modal-content {
  -webkit-box-shadow: 0 1px 15px 1px rgba(42, 147, 51, 0.3);
          box-shadow: 0 1px 15px 1px rgba(42, 147, 51, 0.3); }

.modal-transparent .modal-content {
  background: rgba(28, 59, 31, 0.85); }

.panel {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  /* panel fullscreen */
  /* panel locked */ }
  .panel.panel-fullscreen {
    /* make panel header bigger */ }
    .panel.panel-fullscreen .panel-hdr {
      -webkit-box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(37, 127, 44, 0.1);
              box-shadow: 0 0.125rem 0.125rem -0.0625rem rgba(37, 127, 44, 0.1); }
  .panel.panel-locked:not(.panel-fullscreen) .panel-hdr:active h2:before {
    color: #c139fd; }

/* panel tag can be used globally */
.panel-tag {
  background: #eef7fd; }

/* panel header */
.panel-hdr {
  background: #fff; }

/* panel tap highlight */
.panel-sortable:not(.panel-locked) .panel-hdr:active {
  border-top-color: rgba(125, 217, 132, 0.7);
  border-left-color: rgba(85, 206, 95, 0.7);
  border-right-color: rgba(85, 206, 95, 0.7); }
  .panel-sortable:not(.panel-locked) .panel-hdr:active + .panel-container {
    border-color: transparent rgba(85, 206, 95, 0.7) rgba(65, 200, 76, 0.7); }

/*.panel-sortable .panel-hdr:active,
.panel-sortable .panel-hdr:active + .panel-container {
	@include transition-border(0.4s, ease-out);
}*/
.panel-sortable.panel-locked .panel-hdr:active {
  border-top-color: #d16bfe;
  border-left-color: #dc3545;
  border-right-color: #dc3545; }
  .panel-sortable.panel-locked .panel-hdr:active + .panel-container {
    border-color: transparent #dc3545 #dc3545; }

/* panel toolbar (sits inside panel header) */
.panel-toolbar .btn-panel {
  /* add default colors for action buttons */ }
  .panel-toolbar .btn-panel[data-action="panel-collapse"], .panel-toolbar .btn-panel.js-panel-collapse {
    background: #1dc958; }
  .panel-toolbar .btn-panel[data-action="panel-fullscreen"], .panel-toolbar .btn-panel.js-panel-fullscreen {
    background: #f1ac21; }
  .panel-toolbar .btn-panel[data-action="panel-close"], .panel-toolbar .btn-panel.js-panel-close {
    background: #c139fd; }

/* placeholder */
.panel-placeholder {
  background-color: #e4f2e5; }
  .panel-placeholder:before {
    background: #e4f2e5; }

.mod-panel-clean .panel-hdr {
  background: #fff;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f7f7f7), to(#fff));
  background-image: linear-gradient(to bottom, #f7f7f7, #fff); }

@media only screen and (max-width: 420px) {
  /* making mobile spacing a little narrow */
  .panel .panel-hdr {
    color: #060606; } }

.popover .arrow {
  border-color: inherit; }

.menu-item,
label.menu-open-button {
  background: #55ce5f;
  color: #fff !important; }
  .menu-item:hover,
  label.menu-open-button:hover {
    background: #36ba41; }

.app-shortcut-icon {
  background: #ecf0f1;
  color: #ecf0f1; }

.menu-open:checked + .menu-open-button {
  background: #525252; }

/* nav tabs panel */
.nav-tabs-clean .nav-item .nav-link.active {
  border-bottom: 1px solid #55ce5f;
  color: #55ce5f; }

.nav-tabs-clean .nav-item .nav-link:hover {
  color: #55ce5f; }

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #55ce5f; }

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #36ba41;
  background-color: #41c84c; }

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #69d472; }

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #a4e5aa;
  background-color: #a4e5aa;
  border-color: #a4e5aa; }

.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d; }
  .custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
    background-color: #e9ecef; }

.custom-control-label::before {
  background-color: #fff;
  border: #adb5bd solid 2px; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #41c84c;
  background-color: #55ce5f; }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e"); }

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #a4e5aa; }

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: #a4e5aa; }

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #a4e5aa; }

.custom-switch .custom-control-label::after {
  background-color: #adb5bd; }

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff; }

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #a4e5aa; }

.custom-select {
  color: #495057;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  border: 1px solid #ced4da; }
  .custom-select:focus {
    border-color: #55ce5f; }
    .custom-select:focus::-ms-value {
      color: #495057;
      background-color: #fff; }
  .custom-select:disabled {
    color: #6c757d;
    background-color: #e9ecef; }

.custom-file-input:focus ~ .custom-file-label {
  border-color: #55ce5f; }

.custom-file-input[disabled] ~ .custom-file-label,
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef; }

.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse); }

.custom-file-label {
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da; }
  .custom-file-label::after {
    color: #495057;
    background-color: #e9ecef; }

.custom-range {
  background-color: transparent; }
  .custom-range::-webkit-slider-thumb {
    background-color: #55ce5f;
    border: 0; }
    .custom-range::-webkit-slider-thumb:active {
      background-color: #7dd984; }
  .custom-range::-webkit-slider-runnable-track {
    background-color: #dee2e6; }
  .custom-range::-moz-range-thumb {
    background-color: #55ce5f;
    border: 0; }
    .custom-range::-moz-range-thumb:active {
      background-color: #7dd984; }
  .custom-range::-moz-range-track {
    background-color: #dee2e6; }
  .custom-range::-ms-thumb {
    background-color: #55ce5f;
    border: 0; }
    .custom-range::-ms-thumb:active {
      background-color: #7dd984; }
  .custom-range::-ms-fill-lower {
    background-color: #dee2e6; }
  .custom-range::-ms-fill-upper {
    background-color: #dee2e6; }
  .custom-range:disabled::-webkit-slider-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-moz-range-thumb {
    background-color: #adb5bd; }
  .custom-range:disabled::-ms-thumb {
    background-color: #adb5bd; }

.page-link {
  color: #55ce5f;
  background-color: #fff;
  border: 1px solid #dee2e6;
  /*&:focus {
    outline: $pagination-focus-outline;
  }*/ }
  .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6; }

.page-item.active .page-link {
  color: #fff;
  background-color: #55ce5f; }

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff; }

.pagination .page-item:first-child:not(.active) .page-link,
.pagination .page-item:last-child:not(.active) .page-link,
.pagination .page-item.disabled .page-link {
  background: #e0f6e1; }

.pagination .page-link:hover {
  background-color: #55ce5f !important;
  color: #fff; }

.list-group-item {
  border: 1px solid rgba(var(--theme-rgb-primary), 0.15); }
  .list-group-item.active {
    background-color: #55ce5f;
    border-color: #55ce5f; }

/* backgrounds */
.bg-white {
  background-color: #fff;
  color: #666666; }

.bg-faded {
  background-color: #f7f9fa; }

.bg-offwhite-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(#fafafa));
  background-image: linear-gradient(to top, #fff, #fafafa); }

.bg-subtlelight {
  background-color: white; }

.bg-subtlelight-fade {
  background-color: #fff;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#fff), to(white));
  background-image: linear-gradient(to top, #fff, white); }

.bg-highlight {
  background-color: #fbe9c4; }

.bg-gray-50 {
  background-color: #f9f9f9; }

.bg-gray-100 {
  background-color: #f8f9fa; }

.bg-gray-200 {
  background-color: #e9ecef; }

.bg-gray-300 {
  background-color: #dee2e6; }

.bg-gray-400 {
  background-color: #ced4da; }

.bg-gray-500 {
  background-color: #adb5bd; }

.bg-gray-600 {
  background-color: #6c757d; }

.bg-gray-700 {
  background-color: #495057; }

.bg-gray-800 {
  background-color: #343a40; }

.bg-gray-900 {
  background-color: #212529; }

/* borders */
.border-faded {
  border: 1px solid rgba(31, 31, 31, 0.07); }

/* hover any bg */
/* inherits the parent background on hover */
.hover-bg {
  background: #fff; }

/* states */
.state-selected {
  background: #e7fcfe !important; }

/* demo window */
.demo-window {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12); }
  .demo-window:before {
    background: #e5e5e5; }
  .demo-window:after,
  .demo-window .demo-window-content:before,
  .demo-window .demo-window-content:after {
    background: #ccc; }

.bg-trans-gradient {
  background: linear-gradient(250deg, #3ec9d6, #60c368); }

.notes {
  background: #f9f4b5; }

/* disclaimer class */
.disclaimer {
  color: #a2a2a2; }

/* online status */
.status {
  position: relative; }
  .status:before {
    background: #525252;
    border: 2px solid #fff; }
  .status.status-success:before {
    background: #1dc958; }
  .status.status-danger:before {
    background: #c139fd; }
  .status.status-warning:before {
    background: #f1ac21; }

/* display frame */
.frame-heading {
  color: #a3a3a3; }

.frame-wrap {
  background: white; }

/* time stamp */
.time-stamp {
  color: #787878; }

/* data-hasmore */
[data-hasmore] {
  color: #fff; }
  [data-hasmore]:before {
    background: rgba(0, 0, 0, 0.4); }

/* code */
code {
  background: #fafafa; }

/* select background */
::-moz-selection {
  background: #525252;
  color: #fff; }
::selection {
  background: #525252;
  color: #fff; }

::-moz-selection {
  background: #525252;
  color: #fff; }

@media only screen and (max-width: 992px) {
  .page-wrapper {
    background: #fff; }
    .page-wrapper .page-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.09); }
    .page-wrapper .page-content {
      color: #222; }
      .page-wrapper .page-content .p-g {
        padding: 1.5rem; }
    .page-wrapper .page-footer {
      border-top: 1px solid rgba(0, 0, 0, 0.09); }
  /* Off canvas */
  .nav-mobile-slide-out .page-wrapper .page-content {
    background: #fafdfb; }
  /* mobile nav show & hide button */
  /* general */
  .mobile-nav-on .page-sidebar {
    border-right: 1px solid rgba(0, 0, 0, 0.03);
    -webkit-box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52);
            box-shadow: 0 3px 35px 3px rgba(0, 0, 0, 0.52); }
  .mobile-nav-on .page-content-overlay {
    background: rgba(0, 0, 0, 0.09); } }

@media only screen and (max-width: 576px) {
  /* here we turn on mobile font for smaller screens */
  /*body {
		font-family: $mobile-page-font !important;
	}*/
  /* mobile nav search */
  .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field {
    background: #fff; }
    .mobile-search-on:not(.mobile-nav-on) .search .app-forms #search-field:focus {
      border-color: #55ce5f; } }

/* text area */
[contenteditable="true"]:empty:not(:focus):before {
  content: attr(data-placeholder);
  color: #929292; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

[contenteditable="true"]::-moz-selection {
  background: rgba(0, 132, 255, 0.2);
  color: #000; }

/* add background to focused inpur prepend and append */
.form-control:focus ~ .input-group-prepend {
  background: #55ce5f; }

.has-length .input-group-text {
  border-color: #55ce5f; }
  .has-length .input-group-text + .input-group-text {
    border-left: 1px solid rgba(0, 0, 0, 0.1); }

.has-length .input-group-text:not([class^="bg-"]):not([class*=" bg-"]) {
  background: #55ce5f;
  color: #fff !important; }

/* help block and validation feedback texts*/
.help-block {
  color: #929292; }

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #55ce5f; }

.settings-panel h5 {
  color: #525252; }

.settings-panel .list {
  color: #666666; }
  .settings-panel .list:hover {
    color: #333333;
    background: rgba(255, 255, 255, 0.7); }

.settings-panel .expanded:before {
  border-bottom-color: #5f5f5f; }

@media only screen and (max-width: 992px) {
  .mobile-view-activated #nff,
  .mobile-view-activated #nfm,
  .mobile-view-activated #nfh,
  .mobile-view-activated #nft,
  .mobile-view-activated #mmb {
    position: relative; }
    .mobile-view-activated #nff .onoffswitch-title,
    .mobile-view-activated #nfm .onoffswitch-title,
    .mobile-view-activated #nfh .onoffswitch-title,
    .mobile-view-activated #nft .onoffswitch-title,
    .mobile-view-activated #mmb .onoffswitch-title {
      color: #d58100 !important; }
    .mobile-view-activated #nff .onoffswitch-title-desc,
    .mobile-view-activated #nfm .onoffswitch-title-desc,
    .mobile-view-activated #nfh .onoffswitch-title-desc,
    .mobile-view-activated #nft .onoffswitch-title-desc,
    .mobile-view-activated #mmb .onoffswitch-title-desc {
      color: #ec9f28 !important; }
    .mobile-view-activated #nff:after,
    .mobile-view-activated #nfm:after,
    .mobile-view-activated #nfh:after,
    .mobile-view-activated #nft:after,
    .mobile-view-activated #mmb:after {
      background: #f9d999;
      border: 1px solid #d2910d;
      color: #1f1f1f; } }

/* Hierarchical Navigation */
.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul {
  /* addressing all second, third children */ }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul:before {
    border-left: 1px solid #369b3f; }
  .mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) ul.nav-menu:not(.nav-menu-compact) > li > ul > li a:after {
    background-color: #59c562; }

.bg-primary-50 {
  background-color: #b8ebbc;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-100 {
  background-color: #a4e5aa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-200 {
  background-color: #90df97;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-300 {
  background-color: #7dd984;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-400 {
  background-color: #69d472;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-500 {
  background-color: #55ce5f;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-600 {
  background-color: #41c84c;
  color: rgba(0, 0, 0, 0.8); }
  .bg-primary-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-primary-700 {
  background-color: #36ba41;
  color: white; }
  .bg-primary-700:hover {
    color: white; }

.bg-primary-800 {
  background-color: #30a73a;
  color: white; }
  .bg-primary-800:hover {
    color: white; }

.bg-primary-900 {
  background-color: #2a9333;
  color: white; }
  .bg-primary-900:hover {
    color: white; }

.color-primary-50 {
  color: #b8ebbc; }

.color-primary-100 {
  color: #a4e5aa; }

.color-primary-200 {
  color: #90df97; }

.color-primary-300 {
  color: #7dd984; }

.color-primary-400 {
  color: #69d472; }

.color-primary-500 {
  color: #55ce5f; }

.color-primary-600 {
  color: #41c84c; }

.color-primary-700 {
  color: #36ba41; }

.color-primary-800 {
  color: #30a73a; }

.color-primary-900 {
  color: #2a9333; }

.bg-success-50 {
  background-color: #7aeca1;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-100 {
  background-color: #63e991;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-200 {
  background-color: #4de581;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-300 {
  background-color: #37e272;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-400 {
  background-color: #21df62;
  color: rgba(0, 0, 0, 0.8); }
  .bg-success-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-success-500 {
  background-color: #1dc958;
  color: white; }
  .bg-success-500:hover {
    color: white; }

.bg-success-600 {
  background-color: #1ab34e;
  color: white; }
  .bg-success-600:hover {
    color: white; }

.bg-success-700 {
  background-color: #179c44;
  color: white; }
  .bg-success-700:hover {
    color: white; }

.bg-success-800 {
  background-color: #13863b;
  color: white; }
  .bg-success-800:hover {
    color: white; }

.bg-success-900 {
  background-color: #107031;
  color: white; }
  .bg-success-900:hover {
    color: white; }

.color-success-50 {
  color: #7aeca1; }

.color-success-100 {
  color: #63e991; }

.color-success-200 {
  color: #4de581; }

.color-success-300 {
  color: #37e272; }

.color-success-400 {
  color: #21df62; }

.color-success-500 {
  color: #1dc958; }

.color-success-600 {
  color: #1ab34e; }

.color-success-700 {
  color: #179c44; }

.color-success-800 {
  color: #13863b; }

.color-success-900 {
  color: #107031; }

.bg-info-50 {
  background-color: #9af2fa;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-100 {
  background-color: #82eff8;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-200 {
  background-color: #6aebf7;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-300 {
  background-color: #51e8f6;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-400 {
  background-color: #39e5f4;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-500 {
  background-color: #21e2f3;
  color: rgba(0, 0, 0, 0.8); }
  .bg-info-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-info-600 {
  background-color: #0ddbee;
  color: white; }
  .bg-info-600:hover {
    color: white; }

.bg-info-700 {
  background-color: #0cc5d5;
  color: white; }
  .bg-info-700:hover {
    color: white; }

.bg-info-800 {
  background-color: #0aafbd;
  color: white; }
  .bg-info-800:hover {
    color: white; }

.bg-info-900 {
  background-color: #0998a5;
  color: white; }
  .bg-info-900:hover {
    color: white; }

.color-info-50 {
  color: #9af2fa; }

.color-info-100 {
  color: #82eff8; }

.color-info-200 {
  color: #6aebf7; }

.color-info-300 {
  color: #51e8f6; }

.color-info-400 {
  color: #39e5f4; }

.color-info-500 {
  color: #21e2f3; }

.color-info-600 {
  color: #0ddbee; }

.color-info-700 {
  color: #0cc5d5; }

.color-info-800 {
  color: #0aafbd; }

.color-info-900 {
  color: #0998a5; }

.bg-warning-50 {
  background-color: #f9d999;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-100 {
  background-color: #f7d081;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-200 {
  background-color: #f6c769;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-300 {
  background-color: #f4be51;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-300:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-400 {
  background-color: #f3b539;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-400:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-500 {
  background-color: #f1ac21;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-500:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-600 {
  background-color: #eaa10f;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-600:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-700 {
  background-color: #d2910d;
  color: rgba(0, 0, 0, 0.8); }
  .bg-warning-700:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-warning-800 {
  background-color: #ba800c;
  color: white; }
  .bg-warning-800:hover {
    color: white; }

.bg-warning-900 {
  background-color: #a2700a;
  color: white; }
  .bg-warning-900:hover {
    color: white; }

.color-warning-50 {
  color: #f9d999; }

.color-warning-100 {
  color: #f7d081; }

.color-warning-200 {
  color: #f6c769; }

.color-warning-300 {
  color: #f4be51; }

.color-warning-400 {
  color: #f3b539; }

.color-warning-500 {
  color: #f1ac21; }

.color-warning-600 {
  color: #eaa10f; }

.color-warning-700 {
  color: #d2910d; }

.color-warning-800 {
  color: #ba800c; }

.color-warning-900 {
  color: #a2700a; }

.bg-danger-50 {
  background-color: #e9b7fe;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-50:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-100 {
  background-color: #e19efe;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-100:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-200 {
  background-color: #d985fe;
  color: rgba(0, 0, 0, 0.8); }
  .bg-danger-200:hover {
    color: rgba(0, 0, 0, 0.8); }

.bg-danger-300 {
  background-color: #d16bfe;
  color: white; }
  .bg-danger-300:hover {
    color: white; }

.bg-danger-400 {
  background-color: #c952fd;
  color: white; }
  .bg-danger-400:hover {
    color: white; }

.bg-danger-500 {
  background-color: #c139fd;
  color: white; }
  .bg-danger-500:hover {
    color: white; }

.bg-danger-600 {
  background-color: #b920fd;
  color: white; }
  .bg-danger-600:hover {
    color: white; }

.bg-danger-700 {
  background-color: #b107fc;
  color: white; }
  .bg-danger-700:hover {
    color: white; }

.bg-danger-800 {
  background-color: #a102e7;
  color: white; }
  .bg-danger-800:hover {
    color: white; }

.bg-danger-900 {
  background-color: #9002ce;
  color: white; }
  .bg-danger-900:hover {
    color: white; }

.color-danger-50 {
  color: #e9b7fe; }

.color-danger-100 {
  color: #e19efe; }

.color-danger-200 {
  color: #d985fe; }

.color-danger-300 {
  color: #d16bfe; }

.color-danger-400 {
  color: #c952fd; }

.color-danger-500 {
  color: #c139fd; }

.color-danger-600 {
  color: #b920fd; }

.color-danger-700 {
  color: #b107fc; }

.color-danger-800 {
  color: #a102e7; }

.color-danger-900 {
  color: #9002ce; }

.bg-fusion-50 {
  background-color: #929292;
  color: white; }
  .bg-fusion-50:hover {
    color: white; }

.bg-fusion-100 {
  background-color: #858585;
  color: white; }
  .bg-fusion-100:hover {
    color: white; }

.bg-fusion-200 {
  background-color: #787878;
  color: white; }
  .bg-fusion-200:hover {
    color: white; }

.bg-fusion-300 {
  background-color: #6b6b6b;
  color: white; }
  .bg-fusion-300:hover {
    color: white; }

.bg-fusion-400 {
  background-color: #5f5f5f;
  color: white; }
  .bg-fusion-400:hover {
    color: white; }

.bg-fusion-500 {
  background-color: #525252;
  color: white; }
  .bg-fusion-500:hover {
    color: white; }

.bg-fusion-600 {
  background-color: #454545;
  color: white; }
  .bg-fusion-600:hover {
    color: white; }

.bg-fusion-700 {
  background-color: #383838;
  color: white; }
  .bg-fusion-700:hover {
    color: white; }

.bg-fusion-800 {
  background-color: #2c2c2c;
  color: white; }
  .bg-fusion-800:hover {
    color: white; }

.bg-fusion-900 {
  background-color: #1f1f1f;
  color: white; }
  .bg-fusion-900:hover {
    color: white; }

.color-fusion-50 {
  color: #929292; }

.color-fusion-100 {
  color: #858585; }

.color-fusion-200 {
  color: #787878; }

.color-fusion-300 {
  color: #6b6b6b; }

.color-fusion-400 {
  color: #5f5f5f; }

.color-fusion-500 {
  color: #525252; }

.color-fusion-600 {
  color: #454545; }

.color-fusion-700 {
  color: #383838; }

.color-fusion-800 {
  color: #2c2c2c; }

.color-fusion-900 {
  color: #1f1f1f; }

.color-white {
  color: #fff; }

.color-black {
  color: #222222; }

.bg-primary-gradient {
  background-image: linear-gradient(250deg, rgba(42, 147, 51, 0.7), transparent); }

.bg-danger-gradient {
  background-image: linear-gradient(250deg, rgba(144, 2, 206, 0.7), transparent); }

.bg-info-gradient {
  background-image: linear-gradient(250deg, rgba(9, 152, 165, 0.7), transparent); }

.bg-warning-gradient {
  background-image: linear-gradient(250deg, rgba(162, 112, 10, 0.7), transparent); }

.bg-success-gradient {
  background-image: linear-gradient(250deg, rgba(16, 112, 49, 0.7), transparent); }

.bg-fusion-gradient {
  background-image: linear-gradient(250deg, rgba(31, 31, 31, 0.7), transparent); }

.btn-primary {
  color: #212529;
  background-color: #55ce5f;
  border-color: #55ce5f;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-primary:hover {
    color: #fff;
    background-color: #39c444;
    border-color: #36ba41; }
  .btn-primary:focus, .btn-primary.focus {
    color: #fff;
    background-color: #39c444;
    border-color: #36ba41;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(77, 181, 87, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(77, 181, 87, 0.5); }
  .btn-primary.disabled, .btn-primary:disabled {
    color: #212529;
    background-color: #55ce5f;
    border-color: #55ce5f; }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
  .show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #36ba41;
    border-color: #33b03d; }
    .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(77, 181, 87, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(77, 181, 87, 0.5); }

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62; }
  .btn-secondary:focus, .btn-secondary.focus {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }
  .btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b; }
    .btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5); }

.btn-success {
  color: #fff;
  background-color: #1dc958;
  border-color: #1dc958;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-success:hover {
    color: #fff;
    background-color: #18a849;
    border-color: #179c44; }
  .btn-success:focus, .btn-success.focus {
    color: #fff;
    background-color: #18a849;
    border-color: #179c44;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(63, 209, 113, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(63, 209, 113, 0.5); }
  .btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #1dc958;
    border-color: #1dc958; }
  .btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
  .show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #179c44;
    border-color: #159140; }
    .btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(63, 209, 113, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(63, 209, 113, 0.5); }

.btn-info {
  color: #212529;
  background-color: #21e2f3;
  border-color: #21e2f3;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-info:hover {
    color: #212529;
    background-color: #0cd0e2;
    border-color: #0cc5d5; }
  .btn-info:focus, .btn-info.focus {
    color: #212529;
    background-color: #0cd0e2;
    border-color: #0cc5d5;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 198, 213, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(33, 198, 213, 0.5); }
  .btn-info.disabled, .btn-info:disabled {
    color: #212529;
    background-color: #21e2f3;
    border-color: #21e2f3; }
  .btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
  .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #0cc5d5;
    border-color: #0bbac9; }
    .btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 198, 213, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(33, 198, 213, 0.5); }

.btn-warning {
  color: #212529;
  background-color: #f1ac21;
  border-color: #f1ac21;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-warning:hover {
    color: #212529;
    background-color: #de990e;
    border-color: #d2910d; }
  .btn-warning:focus, .btn-warning.focus {
    color: #212529;
    background-color: #de990e;
    border-color: #d2910d;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(210, 152, 34, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(210, 152, 34, 0.5); }
  .btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #f1ac21;
    border-color: #f1ac21; }
  .btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
  .show > .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #d2910d;
    border-color: #c6880c; }
    .btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(210, 152, 34, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(210, 152, 34, 0.5); }

.btn-danger {
  color: #fff;
  background-color: #c139fd;
  border-color: #c139fd;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-danger:hover {
    color: #fff;
    background-color: #b513fd;
    border-color: #b107fc; }
  .btn-danger:focus, .btn-danger.focus {
    color: #fff;
    background-color: #b513fd;
    border-color: #b107fc;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(202, 87, 253, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(202, 87, 253, 0.5); }
  .btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #c139fd;
    border-color: #c139fd; }
  .btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
  .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #b107fc;
    border-color: #aa02f4; }
    .btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(202, 87, 253, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(202, 87, 253, 0.5); }

.btn-light {
  color: #212529;
  background-color: #fff;
  border-color: #fff;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-light:hover {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6; }
  .btn-light:focus, .btn-light.focus {
    color: #212529;
    background-color: #ececec;
    border-color: #e6e6e6;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }
  .btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
  .show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #e6e6e6;
    border-color: #dfdfdf; }
    .btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(222, 222, 223, 0.5); }

.btn-dark {
  color: #fff;
  background-color: #525252;
  border-color: #525252;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); }
  .btn-dark:hover {
    color: #fff;
    background-color: #3f3f3f;
    border-color: #383838; }
  .btn-dark:focus, .btn-dark.focus {
    color: #fff;
    background-color: #3f3f3f;
    border-color: #383838;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 108, 108, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(108, 108, 108, 0.5); }
  .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #525252;
    border-color: #525252; }
  .btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
  .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #383838;
    border-color: #323232; }
    .btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 108, 108, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(108, 108, 108, 0.5); }

.btn-outline-primary {
  color: #55ce5f;
  border-color: #55ce5f; }
  .btn-outline-primary:hover {
    color: #212529;
    background-color: #55ce5f;
    border-color: #55ce5f; }
  .btn-outline-primary:focus, .btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(85, 206, 95, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(85, 206, 95, 0.5); }
  .btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #55ce5f;
    background-color: transparent; }
  .btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-primary.dropdown-toggle {
    color: #212529;
    background-color: #55ce5f;
    border-color: #55ce5f; }
    .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-primary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(85, 206, 95, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(85, 206, 95, 0.5); }

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d; }
  .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
  .btn-outline-secondary:focus, .btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }
  .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent; }
  .btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
  .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d; }
    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-secondary.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5); }

.btn-outline-success {
  color: #1dc958;
  border-color: #1dc958; }
  .btn-outline-success:hover {
    color: #fff;
    background-color: #1dc958;
    border-color: #1dc958; }
  .btn-outline-success:focus, .btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(29, 201, 88, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(29, 201, 88, 0.5); }
  .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #1dc958;
    background-color: transparent; }
  .btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
  .show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #1dc958;
    border-color: #1dc958; }
    .btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-success.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(29, 201, 88, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(29, 201, 88, 0.5); }

.btn-outline-info {
  color: #21e2f3;
  border-color: #21e2f3; }
  .btn-outline-info:hover {
    color: #212529;
    background-color: #21e2f3;
    border-color: #21e2f3; }
  .btn-outline-info:focus, .btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 226, 243, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(33, 226, 243, 0.5); }
  .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #21e2f3;
    background-color: transparent; }
  .btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
  .show > .btn-outline-info.dropdown-toggle {
    color: #212529;
    background-color: #21e2f3;
    border-color: #21e2f3; }
    .btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-info.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(33, 226, 243, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(33, 226, 243, 0.5); }

.btn-outline-warning {
  color: #f1ac21;
  border-color: #f1ac21; }
  .btn-outline-warning:hover {
    color: #212529;
    background-color: #f1ac21;
    border-color: #f1ac21; }
  .btn-outline-warning:focus, .btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(241, 172, 33, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(241, 172, 33, 0.5); }
  .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #f1ac21;
    background-color: transparent; }
  .btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
  .show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #f1ac21;
    border-color: #f1ac21; }
    .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-warning.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(241, 172, 33, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(241, 172, 33, 0.5); }

.btn-outline-danger {
  color: #c139fd;
  border-color: #c139fd; }
  .btn-outline-danger:hover {
    color: #fff;
    background-color: #c139fd;
    border-color: #c139fd; }
  .btn-outline-danger:focus, .btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(193, 57, 253, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(193, 57, 253, 0.5); }
  .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #c139fd;
    background-color: transparent; }
  .btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
  .show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #c139fd;
    border-color: #c139fd; }
    .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-danger.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(193, 57, 253, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(193, 57, 253, 0.5); }

.btn-outline-light {
  color: #fff;
  border-color: #fff; }
  .btn-outline-light:hover {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
  .btn-outline-light:focus, .btn-outline-light.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }
  .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #fff;
    background-color: transparent; }
  .btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
  .show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #fff;
    border-color: #fff; }
    .btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-light.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5); }

.btn-outline-dark {
  color: #525252;
  border-color: #525252; }
  .btn-outline-dark:hover {
    color: #fff;
    background-color: #525252;
    border-color: #525252; }
  .btn-outline-dark:focus, .btn-outline-dark.focus {
    -webkit-box-shadow: 0 0 0 0.2rem rgba(82, 82, 82, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(82, 82, 82, 0.5); }
  .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #525252;
    background-color: transparent; }
  .btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
  .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #525252;
    border-color: #525252; }
    .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
    .show > .btn-outline-dark.dropdown-toggle:focus {
      -webkit-box-shadow: 0 0 0 0.2rem rgba(82, 82, 82, 0.5);
              box-shadow: 0 0 0 0.2rem rgba(82, 82, 82, 0.5); }

.border-primary {
  border-color: #55ce5f !important; }

.border-secondary {
  border-color: #6c757d !important; }

.border-success {
  border-color: #1dc958 !important; }

.border-info {
  border-color: #21e2f3 !important; }

.border-warning {
  border-color: #f1ac21 !important; }

.border-danger {
  border-color: #c139fd !important; }

.border-light {
  border-color: #fff !important; }

.border-dark {
  border-color: #525252 !important; }

.text-primary {
  color: #55ce5f !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #30a73a !important; }

.text-secondary {
  color: #6c757d !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important; }

.text-success {
  color: #1dc958 !important; }

a.text-success:hover, a.text-success:focus {
  color: #13863b !important; }

.text-info {
  color: #21e2f3 !important; }

a.text-info:hover, a.text-info:focus {
  color: #0aafbd !important; }

.text-warning {
  color: #f1ac21 !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #ba800c !important; }

.text-danger {
  color: #c139fd !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #a102e7 !important; }

.text-light {
  color: #fff !important; }

a.text-light:hover, a.text-light:focus {
  color: #d9d9d9 !important; }

.text-dark {
  color: #525252 !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #2c2c2c !important; }

.bg-primary {
  background-color: #55ce5f !important; }

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #36ba41 !important; }

.bg-secondary {
  background-color: #6c757d !important; }

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #545b62 !important; }

.bg-success {
  background-color: #1dc958 !important; }

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #179c44 !important; }

.bg-info {
  background-color: #21e2f3 !important; }

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #0cc5d5 !important; }

.bg-warning {
  background-color: #f1ac21 !important; }

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #d2910d !important; }

.bg-danger {
  background-color: #c139fd !important; }

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #b107fc !important; }

.bg-light {
  background-color: #fff !important; }

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #e6e6e6 !important; }

.bg-dark {
  background-color: #525252 !important; }

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #383838 !important; }

:root {
  --theme-primary: #55ce5f;
  --theme-secondary: #6c757d;
  --theme-success: #1dc958;
  --theme-info: #21e2f3;
  --theme-warning: #f1ac21;
  --theme-danger: #c139fd;
  --theme-light: #fff;
  --theme-dark: #525252;
  --theme-rgb-primary: 85,206,95;
  --theme-rgb-success: 29,201,88;
  --theme-rgb-info: 33,226,243;
  --theme-rgb-warning: 241,172,33;
  --theme-rgb-danger: 193,57,253;
  --theme-rgb-fusion: 81.75,81.75,81.75;
  --theme-primary-50: #b8ebbc;
  --theme-primary-100: #a4e5aa;
  --theme-primary-200: #90df97;
  --theme-primary-300: #7dd984;
  --theme-primary-400: #69d472;
  --theme-primary-500: #55ce5f;
  --theme-primary-600: #41c84c;
  --theme-primary-700: #36ba41;
  --theme-primary-800: #30a73a;
  --theme-primary-900: #2a9333;
  --theme-success-50: #7aeca1;
  --theme-success-100: #63e991;
  --theme-success-200: #4de581;
  --theme-success-300: #37e272;
  --theme-success-400: #21df62;
  --theme-success-500: #1dc958;
  --theme-success-600: #1ab34e;
  --theme-success-700: #179c44;
  --theme-success-800: #13863b;
  --theme-success-900: #107031;
  --theme-info-50: #9af2fa;
  --theme-info-100: #82eff8;
  --theme-info-200: #6aebf7;
  --theme-info-300: #51e8f6;
  --theme-info-400: #39e5f4;
  --theme-info-500: #21e2f3;
  --theme-info-600: #0ddbee;
  --theme-info-700: #0cc5d5;
  --theme-info-800: #0aafbd;
  --theme-info-900: #0998a5;
  --theme-warning-50: #f9d999;
  --theme-warning-100: #f7d081;
  --theme-warning-200: #f6c769;
  --theme-warning-300: #f4be51;
  --theme-warning-400: #f3b539;
  --theme-warning-500: #f1ac21;
  --theme-warning-600: #eaa10f;
  --theme-warning-700: #d2910d;
  --theme-warning-800: #ba800c;
  --theme-warning-900: #a2700a;
  --theme-danger-50: #e9b7fe;
  --theme-danger-100: #e19efe;
  --theme-danger-200: #d985fe;
  --theme-danger-300: #d16bfe;
  --theme-danger-400: #c952fd;
  --theme-danger-500: #c139fd;
  --theme-danger-600: #b920fd;
  --theme-danger-700: #b107fc;
  --theme-danger-800: #a102e7;
  --theme-danger-900: #9002ce;
  --theme-fusion-50: #929292;
  --theme-fusion-100: #858585;
  --theme-fusion-200: #787878;
  --theme-fusion-300: #6b6b6b;
  --theme-fusion-400: #5f5f5f;
  --theme-fusion-500: #525252;
  --theme-fusion-600: #454545;
  --theme-fusion-700: #383838;
  --theme-fusion-800: #2c2c2c;
  --theme-fusion-900: #1f1f1f; }

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #55ce5f; }

.ct-series-a .ct-slice-pie, .ct-series-a .ct-slice-donut-solid, .ct-series-a .ct-area {
  fill: #55ce5f; }

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #c139fd; }

.ct-series-b .ct-slice-pie, .ct-series-b .ct-slice-donut-solid, .ct-series-b .ct-area {
  fill: #c139fd; }

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #f1ac21; }

.ct-series-c .ct-slice-pie, .ct-series-c .ct-slice-donut-solid, .ct-series-c .ct-area {
  fill: #f1ac21; }

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #21e2f3; }

.ct-series-d .ct-slice-pie, .ct-series-d .ct-slice-donut-solid, .ct-series-d .ct-area {
  fill: #21e2f3; }

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #525252; }

.ct-series-e .ct-slice-pie, .ct-series-e .ct-slice-donut-solid, .ct-series-e .ct-area {
  fill: #525252; }

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: #1dc958; }

.ct-series-f .ct-slice-pie, .ct-series-f .ct-slice-donut-solid, .ct-series-f .ct-area {
  fill: #1dc958; }

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: #21e2f3; }

.ct-series-g .ct-slice-pie, .ct-series-g .ct-slice-donut-solid, .ct-series-g .ct-area {
  fill: #21e2f3; }

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: #2a9333; }

.ct-series-h .ct-slice-pie, .ct-series-h .ct-slice-donut-solid, .ct-series-h .ct-area {
  fill: #2a9333; }

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: #e19efe; }

.ct-series-i .ct-slice-pie, .ct-series-i .ct-slice-donut-solid, .ct-series-i .ct-area {
  fill: #e19efe; }

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: #f6c769; }

.ct-series-j .ct-slice-pie, .ct-series-j .ct-slice-donut-solid, .ct-series-j .ct-area {
  fill: #f6c769; }

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: #9002ce; }

.ct-series-k .ct-slice-pie, .ct-series-k .ct-slice-donut-solid, .ct-series-k .ct-area {
  fill: #9002ce; }

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: #6b6b6b; }

.ct-series-l .ct-slice-pie, .ct-series-l .ct-slice-donut-solid, .ct-series-l .ct-area {
  fill: #6b6b6b; }

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: #37e272; }

.ct-series-m .ct-slice-pie, .ct-series-m .ct-slice-donut-solid, .ct-series-m .ct-area {
  fill: #37e272; }

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: #51e8f6; }

.ct-series-n .ct-slice-pie, .ct-series-n .ct-slice-donut-solid, .ct-series-n .ct-area {
  fill: #51e8f6; }

.ct-series-o .ct-point, .ct-series-o .ct-line, .ct-series-o .ct-bar, .ct-series-o .ct-slice-donut {
  stroke: #7dd984; }

.ct-series-o .ct-slice-pie, .ct-series-o .ct-slice-donut-solid, .ct-series-o .ct-area {
  fill: #7dd984; }

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border-color: #E5E5E5; }

.select2-dropdown {
  border-color: #E5E5E5; }

.select2-search--dropdown:before {
  color: #55ce5f; }

.select2-results__message {
  color: #55ce5f !important; }

.select2-container--open .select2-dropdown--above {
  border-color: #55ce5f; }

.select2-container--open .select2-dropdown--below {
  border-color: #55ce5f; }

.select2-container--default .select2-search--dropdown .select2-search__field {
  color: #495057;
  background-color: #fff;
  border-color: #E5E5E5;
  -webkit-box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025);
          box-shadow: inset 0 0.25rem 0.125rem 0 rgba(33, 37, 41, 0.025); }
  .select2-container--default .select2-search--dropdown .select2-search__field:focus {
    border-color: #cccccc; }

.select2-container--default .select2-results__group {
  padding: 0.5rem 0;
  color: #8e8e8e; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background: #ebf9ed;
  color: #2a9333; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #55ce5f;
  color: #fff; }

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
  border-color: #55ce5f; }

.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #55ce5f; }

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: #ebf9ed;
  border-color: #69d472;
  color: #2a9333; }
  .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    color: #7dd984; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {
      color: #55ce5f; }
    .select2-container--default .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:active {
      -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset; }

.select2-container--default .select2-selection--single .select2-selection__clear {
  color: #c139fd; }
  .select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: #b920fd; }

.select2-results__message {
  color: #c139fd; }

.sorting_asc,
.sorting_desc,
.even .sorting_1 {
  background-color: rgba(85, 206, 95, 0.03); }

.odd .sorting_1 {
  background-color: rgba(85, 206, 95, 0.04); }

.table-dark .sorting_asc,
.table-dark .sorting_desc,
.table-dark .even .sorting_1 {
  background-color: rgba(241, 172, 33, 0.15); }

.table-dark .odd .sorting_1 {
  background-color: rgba(241, 172, 33, 0.15); }

table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  background-color: #55ce5f; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  background-color: #1dc958; }

.dataTables_empty {
  color: #c139fd; }

.dataTables_wrapper tr.child td.child .dtr-details:before {
  color: #21df62; }

.dataTables_wrapper tr.child td.child .dtr-details:after {
  background: #4de581; }

div.dt-autofill-background {
  opacity: 0.2;
  background-color: #000; }

div.dt-autofill-handle {
  background: #55ce5f; }

div.dt-autofill-select {
  background-color: #55ce5f; }

/* FixedColumns */
.DTFC_LeftHeadWrapper:before,
.DTFC_LeftBodyWrapper:before,
.DTFC_LeftFootWrapper:before {
  background: #c139fd; }

/* KeyTable */
table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
  -webkit-box-shadow: inset 0 0 0px 1px #55ce5f;
          box-shadow: inset 0 0 0px 1px #55ce5f;
  background: rgba(85, 206, 95, 0.1); }

table.dataTable:not(.table-dark) tr.dtrg-group td {
  background: #fff; }

tr.dt-rowReorder-moving {
  outline-color: #1dc958; }

table.dt-rowReorder-float {
  outline-color: #55ce5f; }

/* Select */
table.dataTable.table-bordered .selected td {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable.table-bordered td.selected {
  border-color: rgba(0, 0, 0, 0.1); }

table.dataTable tbody > tr.selected,
table.dataTable tbody > tr > .selected {
  -webkit-box-shadow: inset 0 0 0px 1px #55ce5f;
          box-shadow: inset 0 0 0px 1px #55ce5f;
  background: rgba(85, 206, 95, 0.1); }

.datepicker table tr td.old,
.datepicker table tr td.new {
  color: #ababab; }

.datepicker table tr td.active:active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active,
.datepicker table tr td.selected,
.datepicker table tr td.selected.highlighted,
.datepicker table tr td span.active.active,
.datepicker table tr td span.focused {
  background-color: #69d472;
  border-color: #55ce5f;
  color: #fff; }

.datepicker table tr td.active:active:hover,
.datepicker table tr td.active.highlighted:active:hover,
.datepicker table tr td.active.active:hover,
.datepicker table tr td.active.highlighted.active:hover,
.datepicker table tr td.active:active:focus,
.datepicker table tr td.active.highlighted:active:focus,
.datepicker table tr td.active.active:focus,
.datepicker table tr td.active.highlighted.active:focus,
.datepicker table tr td.active:active.focus,
.datepicker table tr td.active.highlighted:active.focus,
.datepicker table tr td.active.active.focus,
.datepicker table tr td.active.highlighted.active.focus,
.datepicker table tr td.selected:active:hover,
.datepicker table tr td.selected.highlighted:active:hover,
.datepicker table tr td.selected.active:hover,
.datepicker table tr td.selected.highlighted.active:hover,
.datepicker table tr td.selected:active:focus,
.datepicker table tr td.selected.highlighted:active:focus,
.datepicker table tr td.selected.active:focus,
.datepicker table tr td.selected.highlighted.active:focus,
.datepicker table tr td.selected:active.focus,
.datepicker table tr td.selected.highlighted:active.focus,
.datepicker table tr td.selected.active.focus,
.datepicker table tr td.selected.highlighted.active.focus,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.highlighted:hover {
  background-color: #41c84c;
  border-color: #36ba41;
  color: #fff; }

.datepicker.datepicker-inline {
  border: 1px solid #ebedf2; }

.datepicker thead th.prev, .datepicker thead th.datepicker-switch, .datepicker thead th.next {
  color: #a1a8c3; }

.daterangepicker table tr td.old,
.daterangepicker table tr td.new {
  color: #ababab; }

.daterangepicker table tr td.active:active,
.daterangepicker table tr td.active.highlighted:active,
.daterangepicker table tr td.active.active,
.daterangepicker table tr td.active.highlighted.active,
.daterangepicker table tr td.selected,
.daterangepicker table tr td.selected.highlighted,
.daterangepicker table tr td span.active.active,
.daterangepicker table tr td span.focused {
  background-color: #69d472;
  color: #fff; }

.daterangepicker table tr td.active:active:hover,
.daterangepicker table tr td.active.highlighted:active:hover,
.daterangepicker table tr td.active.active:hover,
.daterangepicker table tr td.active.highlighted.active:hover,
.daterangepicker table tr td.active:active:focus,
.daterangepicker table tr td.active.highlighted:active:focus,
.daterangepicker table tr td.active.active:focus,
.daterangepicker table tr td.active.highlighted.active:focus,
.daterangepicker table tr td.active:active.focus,
.daterangepicker table tr td.active.highlighted:active.focus,
.daterangepicker table tr td.active.active.focus,
.daterangepicker table tr td.active.highlighted.active.focus,
.daterangepicker table tr td.selected:active:hover,
.daterangepicker table tr td.selected.highlighted:active:hover,
.daterangepicker table tr td.selected.active:hover,
.daterangepicker table tr td.selected.highlighted.active:hover,
.daterangepicker table tr td.selected:active:focus,
.daterangepicker table tr td.selected.highlighted:active:focus,
.daterangepicker table tr td.selected.active:focus,
.daterangepicker table tr td.selected.highlighted.active:focus,
.daterangepicker table tr td.selected:active.focus,
.daterangepicker table tr td.selected.highlighted:active.focus,
.daterangepicker table tr td.selected.active.focus,
.daterangepicker table tr td.selected.highlighted.active.focus,
.daterangepicker table tr td.selected:hover,
.daterangepicker table tr td.selected.highlighted:hover {
  background-color: #41c84c;
  color: #fff; }

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
  border-color: #a1a8c3; }

.daterangepicker .in-range.available {
  background-color: #f7d081; }

.daterangepicker .off.ends.in-range.available {
  background-color: #f9d999; }

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: #f4be51; }

.daterangepicker .calendar-table table thead tr th.month {
  color: #a1a8c3; }

.daterangepicker .ranges li.active {
  background-color: #55ce5f; }

.irs--flat .irs-bar,
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single,
.irs--flat .irs-handle > i:first-child {
  background-color: #55ce5f; }

.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  border-top-color: #55ce5f; }

.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child {
  background-color: #41c84c; }

.irs--big .irs-bar {
  background-color: #7dd984;
  border-color: #55ce5f;
  background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), color-stop(30%, #7dd984), to(#55ce5f));
  background: linear-gradient(to bottom, #ffffff 0%, #7dd984 30%, #55ce5f 100%); }

.irs--big .irs-from,
.irs--big .irs-to,
.irs--big .irs-single {
  background: #55ce5f; }

.irs--modern .irs-bar {
  background: #1ab34e;
  background: -webkit-gradient(linear, left top, left bottom, from(#21df62), to(#1ab34e));
  background: linear-gradient(to bottom, #21df62 0%, #1ab34e 100%); }

.irs--modern .irs-from,
.irs--modern .irs-to,
.irs--modern .irs-single {
  background-color: #1dc958; }

.irs--modern .irs-from:before,
.irs--modern .irs-to:before,
.irs--modern .irs-single:before {
  border-top-color: #1dc958; }

.irs--sharp .irs-bar,
.irs--sharp .irs-handle,
.irs--sharp .irs-from,
.irs--sharp .irs-to,
.irs--sharp .irs-single {
  background-color: #c139fd; }

.irs--sharp .irs-handle > i:first-child,
.irs--sharp .irs-from:before,
.irs--sharp .irs-to:before,
.irs--sharp .irs-single:before {
  border-top-color: #c139fd; }

.irs--sharp .irs-min,
.irs--sharp .irs-max {
  background-color: #a102e7; }

.irs--round .irs-handle {
  border-color: #21e2f3; }

.irs--round .irs-bar,
.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single {
  background-color: #21e2f3; }

.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before {
  border-top-color: #21e2f3; }

body:not(.mod-pace-custom) .pace .pace-progress {
  background: #55ce5f; }

.mod-pace-custom .pace {
  background: #fff; }
  .mod-pace-custom .pace .pace-progress {
    background-color: #55ce5f;
    background-image: linear-gradient(135deg, #55ce5f 0%, #55ce5f 25%, #36ba41 25%, #36ba41 50%, #55ce5f 50%, #55ce5f 75%, #36ba41 75%, #36ba41 100%); }

.mod-pace-custom.pace-running .page-content:before {
  background-color: #fafdfb; }

/* #Reset userselect
========================================================================== */
#myapp-0 {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }

#myapp-13 {
  -webkit-box-shadow: 0 0 0 3px #000000;
          box-shadow: 0 0 0 3px #000000; }

/*# sourceMappingURL=cust-theme-13.css.map */
