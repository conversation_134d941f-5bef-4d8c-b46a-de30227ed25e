/**
 * Theme Settings Fix
 * แก้ไขปัญหา "Theme settings is empty or does not exist" warning
 */

(function() {
    'use strict';
    
    // ตั้งค่า default theme settings
    var defaultThemeSettings = {
        themeOptions: 'nav-function-fixed nav-function-minify nav-function-top header-function-fixed',
        themeURL: '',
        version: '1.0.0',
        lastUpdated: new Date().toISOString()
    };
    
    // ฟังก์ชันสำหรับ initialize theme settings
    function initializeThemeSettings() {
        try {
            // ตรวจสอบว่ามี localStorage หรือไม่
            if (typeof Storage === 'undefined') {
                console.warn('LocalStorage is not supported in this browser');
                return false;
            }
            
            // ดึงการตั้งค่าปัจจุบัน
            var currentSettings = localStorage.getItem('themeSettings');
            
            if (!currentSettings || currentSettings === '' || currentSettings === 'null') {
                // ถ้าไม่มีการตั้งค่า ให้ใช้ค่า default
                localStorage.setItem('themeSettings', JSON.stringify(defaultThemeSettings));
                console.log('%c✔ Default theme settings initialized', 'color: #28a745; font-weight: bold;');
                return true;
            } else {
                // ตรวจสอบว่าการตั้งค่าที่มีอยู่ถูกต้องหรือไม่
                try {
                    var settings = JSON.parse(currentSettings);
                    
                    // ตรวจสอบว่ามี themeOptions หรือไม่
                    if (!settings.themeOptions || settings.themeOptions.trim() === '') {
                        settings.themeOptions = defaultThemeSettings.themeOptions;
                        localStorage.setItem('themeSettings', JSON.stringify(settings));
                        console.log('%c✔ Theme options restored to default', 'color: #ffc107; font-weight: bold;');
                    }
                    
                    return true;
                } catch (e) {
                    // ถ้า JSON ไม่ถูกต้อง ให้ reset เป็นค่า default
                    localStorage.setItem('themeSettings', JSON.stringify(defaultThemeSettings));
                    console.log('%c✔ Corrupted theme settings fixed', 'color: #dc3545; font-weight: bold;');
                    return true;
                }
            }
        } catch (error) {
            console.error('Error initializing theme settings:', error);
            return false;
        }
    }
    
    // ฟังก์ชันสำหรับ reset theme settings
    function resetThemeSettings() {
        try {
            localStorage.setItem('themeSettings', JSON.stringify(defaultThemeSettings));
            console.log('%c✔ Theme settings reset to default', 'color: #17a2b8; font-weight: bold;');
            
            // Reload หน้าเพื่อให้การตั้งค่าใหม่มีผล
            if (confirm('Theme settings have been reset. Reload the page to apply changes?')) {
                window.location.reload();
            }
        } catch (error) {
            console.error('Error resetting theme settings:', error);
        }
    }
    
    // ฟังก์ชันสำหรับ export theme settings
    function exportThemeSettings() {
        try {
            var settings = localStorage.getItem('themeSettings');
            if (settings) {
                var blob = new Blob([settings], { type: 'application/json' });
                var url = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = 'theme-settings-' + new Date().toISOString().split('T')[0] + '.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                console.log('%c✔ Theme settings exported', 'color: #28a745; font-weight: bold;');
            }
        } catch (error) {
            console.error('Error exporting theme settings:', error);
        }
    }
    
    // ฟังก์ชันสำหรับ import theme settings
    function importThemeSettings(file) {
        try {
            var reader = new FileReader();
            reader.onload = function(e) {
                try {
                    var settings = JSON.parse(e.target.result);
                    localStorage.setItem('themeSettings', JSON.stringify(settings));
                    console.log('%c✔ Theme settings imported', 'color: #28a745; font-weight: bold;');
                    
                    if (confirm('Theme settings imported successfully. Reload the page to apply changes?')) {
                        window.location.reload();
                    }
                } catch (error) {
                    alert('Invalid theme settings file');
                    console.error('Error parsing imported settings:', error);
                }
            };
            reader.readAsText(file);
        } catch (error) {
            console.error('Error importing theme settings:', error);
        }
    }
    
    // ฟังก์ชันสำหรับดู current theme settings
    function showCurrentThemeSettings() {
        try {
            var settings = localStorage.getItem('themeSettings');
            if (settings) {
                var parsed = JSON.parse(settings);
                console.group('%c📋 Current Theme Settings', 'color: #007bff; font-weight: bold; font-size: 14px;');
                console.log('Theme Options:', parsed.themeOptions || 'Not set');
                console.log('Theme URL:', parsed.themeURL || 'Not set');
                console.log('Version:', parsed.version || 'Unknown');
                console.log('Last Updated:', parsed.lastUpdated || 'Unknown');
                console.log('Raw Data:', parsed);
                console.groupEnd();
                return parsed;
            } else {
                console.log('%c⚠️ No theme settings found', 'color: #ffc107; font-weight: bold;');
                return null;
            }
        } catch (error) {
            console.error('Error showing theme settings:', error);
            return null;
        }
    }
    
    // ฟังก์ชันสำหรับตรวจสอบ theme health
    function checkThemeHealth() {
        console.group('%c🔍 Theme Health Check', 'color: #6f42c1; font-weight: bold; font-size: 14px;');
        
        // ตรวจสอบ localStorage
        if (typeof Storage !== 'undefined') {
            console.log('%c✔ LocalStorage: Supported', 'color: #28a745;');
        } else {
            console.log('%c❌ LocalStorage: Not supported', 'color: #dc3545;');
        }
        
        // ตรวจสอบ theme settings
        var settings = localStorage.getItem('themeSettings');
        if (settings && settings !== '' && settings !== 'null') {
            try {
                var parsed = JSON.parse(settings);
                console.log('%c✔ Theme Settings: Valid JSON', 'color: #28a745;');
                
                if (parsed.themeOptions && parsed.themeOptions.trim() !== '') {
                    console.log('%c✔ Theme Options: Set', 'color: #28a745;');
                } else {
                    console.log('%c⚠️ Theme Options: Empty or missing', 'color: #ffc107;');
                }
            } catch (e) {
                console.log('%c❌ Theme Settings: Invalid JSON', 'color: #dc3545;');
            }
        } else {
            console.log('%c❌ Theme Settings: Not found or empty', 'color: #dc3545;');
        }
        
        // ตรวจสอบ CSS files
        var themeCSS = document.getElementById('mytheme');
        if (themeCSS) {
            console.log('%c✔ Theme CSS: Loaded (' + themeCSS.href + ')', 'color: #28a745;');
        } else {
            console.log('%c⚠️ Theme CSS: Not loaded', 'color: #ffc107;');
        }
        
        // ตรวจสอบ body classes
        var bodyClasses = document.body.className;
        if (bodyClasses && bodyClasses.trim() !== '') {
            console.log('%c✔ Body Classes: ' + bodyClasses, 'color: #28a745;');
        } else {
            console.log('%c⚠️ Body Classes: Empty', 'color: #ffc107;');
        }
        
        console.groupEnd();
    }
    
    // Initialize เมื่อ DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeThemeSettings);
    } else {
        initializeThemeSettings();
    }
    
    // Expose functions to global scope สำหรับ debugging
    window.themeSettingsFix = {
        init: initializeThemeSettings,
        reset: resetThemeSettings,
        export: exportThemeSettings,
        import: importThemeSettings,
        show: showCurrentThemeSettings,
        health: checkThemeHealth,
        setDefault: function(options) {
            if (options && typeof options === 'object') {
                defaultThemeSettings = Object.assign(defaultThemeSettings, options);
                localStorage.setItem('themeSettings', JSON.stringify(defaultThemeSettings));
                console.log('%c✔ Default theme settings updated', 'color: #28a745; font-weight: bold;');
            }
        }
    };
    
    // Auto-fix เมื่อเกิด error
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('themeSettings')) {
            console.warn('Theme settings error detected, attempting auto-fix...');
            initializeThemeSettings();
        }
    });
    
    // เพิ่ม CSS สำหรับ console styling
    if (typeof console !== 'undefined' && console.log) {
        console.log(
            '%c🎨 Theme Settings Fix Loaded', 
            'color: #fff; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); padding: 8px 12px; border-radius: 4px; font-weight: bold;'
        );
        console.log(
            '%cUse themeSettingsFix.health() to check theme status', 
            'color: #6c757d; font-style: italic;'
        );
    }
    
})();
