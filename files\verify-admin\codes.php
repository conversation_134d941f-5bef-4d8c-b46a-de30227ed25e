<?php $user->restrictionUser(true, $conn); ?>
<div class="row">
                    <div class="col-lg-12">
                        <section class="panel">
                            <header class="panel-heading">
                            <h2><?php echo PT_MANAGECODES; ?> <small><?php echo PT_MANAGECODES_DESC; ?></h2>
                            </header>
				                <div class="panel-body">
    <div class="col-lg-12" style="margin: 10px 5px;">
        <form method="post" action="?url=manager/presults" class="form-inline pull-right">
            <div class="form-group">
                <input type="text" class="form-control" name="search" placeholder="Search by Email or CustomerID">
                <input type="submit" class="btn btn-info" name="btn_search" value="Search">
            </div>
        </form>
    </div>
    <div class="panel panel-default">
        <div class="panel-body no-padd" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
                <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                <?php } ?>
                <table class="table no-margn">
                    <thead>
                        <tr>
                            <th>CodeID</th>
                            <th>CODE</th>
                            <th>Status</th>
                            <th>Created In</th>
                            <!--<th><?php echo T_ACTION; ?></th>-->
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 10;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM WEB_Redeem_Code ORDER BY datecreated DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
                                <td><span class="label<?php echo $label = ($row[4] == '0' ? ' label-danger' : ($row[4] == '0' ? ' label-success' : 'label-default'));?>"><?php echo $status = ($row[4] == '0' ? 'Used' : ($row[4] == '1' ? 'Available' : 'Unknow status'));?></span></td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=manager/codes&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=manager/codes&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=manager/codes&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <a href="?url=manager/create-code" class="btn btn-info btn-block flat">Create Code</a>
    </div>
</div>