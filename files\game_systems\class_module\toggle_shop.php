<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

$eventId = isset($_GET['eventId']) ? intval($_GET['eventId']) : 0;
$newFlag = isset($_GET['newFlag']) ? intval($_GET['newFlag']) : 0;


$query = "UPDATE EventData.dbo.cabal_ems_event_table SET UseFlag = ? WHERE EventID = ?";
sqlsrv_query($conn, $query, [$newFlag, $eventId]);

echo json_encode(["status" => "updated"]);
