<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
?>
<header class="page-header page-header-left-inline-breadcrumb">
    <h2 class="font-weight-bold text-6"><?php echo PT_WEB_DOWNLOADS; ?></h2>
    <div class="right-wrapper">
        <ol class="breadcrumbs">
            <li><span><?php echo PT_WEB_DOWNLOADS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fas fa-chevron-left"></i></a>
    </div>
</header>
<!-- start: page -->
<div class="row">
    <div class="col">

        <div class="card card-modern">
            <div class="card-body">
                <div class="datatables-header-footer-wrapper">
                    <div class="datatable-header">
                        <div class="row align-items-center mb-3">
                            <div class="col-12 col-lg-auto mb-3 mb-lg-0">
                                <a href="?url=web-admin/create-download"
                                    class="btn btn-primary btn-md font-weight-semibold btn-py-2 px-4">+ Add
                                    ดาวโหลด</a>
                            </div>
                            <div class="col-8 col-lg-auto ml-auto mb-3 mb-lg-0">
                                <div class="d-flex align-items-lg-center flex-column flex-lg-row">
                                    <label class="ws-nowrap mr-3 mb-0">Filter By:</label>
                                    <select class="form-control select-style-1 filter-by" name="filter-by">
                                        <option value="all" selected>All</option>
                                        <option value="1">File name</option>
                                        <option value="2">Description</option>
                                        <option value="3">File Size</option>
                                        <option value="4">Uploaded in</option>
                                        <option value="5">ACTION</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-4 col-lg-auto pl-lg-1 mb-3 mb-lg-0">
                                <div class="d-flex align-items-lg-center flex-column flex-lg-row">
                                    <label class="ws-nowrap mr-3 mb-0">Show:</label>
                                    <select class="form-control select-style-1 results-per-page"
                                        name="results-per-page">
                                        <option value="12" selected>12</option>
                                        <option value="24">24</option>
                                        <option value="36">36</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-lg-auto pl-lg-1">
                                <div class="search search-style-1 search-style-1-lg mx-lg-auto">
                                    <div class="input-group">
                                        <input type="text" class="search-term form-control" name="search-term"
                                            id="search-term" placeholder="Search Customer">
                                        <span class="input-group-append">
                                            <button class="btn btn-default" type="submit"><i
                                                    class="bx bx-search"></i></button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table class="table table-ecommerce-simple table-striped mb-0" id="datatable-ecommerce-list"
                        style="min-width: 750px;">
                        <thead>
                            <tr>
                                <th width="3%"><input type="checkbox" name="select-all"
                                        class="select-all checkbox-style-1 p-relative top-2" value="" /></th>
                                <th width="28%">File name</th>
                                <th width="28%">Description</th>
                                <th width="18%">File Size</th>
                                <th width="25%">Uploaded in</th>
                                <th width="8%">ACTION</th>
                            </tr>
                        </thead>
                        <tbody> <?php
                        $selectDownloads = "SELECT * FROM WEB_Downloads";
                        $selectDownloadsParam = array();
                        $selectDownloadsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectDownloadsQuery = sqlsrv_query($conn, $selectDownloads, $selectDownloadsParam, $selectDownloadsOpt);
                        if (sqlsrv_num_rows($selectDownloadsQuery)) {
                            while ($resDownload = sqlsrv_fetch_array($selectDownloadsQuery, SQLSRV_FETCH_ASSOC)) {
                                ?>
                            <tr>
                                <td width="30"><input type="checkbox" name="checkboxRow1"
                                        class="checkbox-style-1 p-relative top-2" value="" /></td>
                                <td><?php echo $resDownload['name']; ?></td>
                                <td><?php echo $resDownload['description']; ?></td>
                                <td><?php echo $resDownload['size'] . ' ' . strtoupper($resDownload['size_type']); ?>
                                </td>
                                <td><?php echo date('d/m/Y \a\t\ H:i', strtotime($resDownload['dateupdated'])); ?></td>
                                <td>
                                    <div class="dropdown clearfix">
                                        <button class="btn btn-default dropdown-toggle" type="button"
                                            id="dropdownMenuDivider" data-toggle="dropdown" aria-expanded="true">
                                            Action
                                            <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenuDivider">
                                            <li role="presentation"><a class="dropdown-item text-1"
                                                    href="?url=web-admin/edit-download&downid=<?php echo $resDownload['id']; ?>">Edit
                                                    download</a></li>
                                            <li role="presentation"><a class="dropdown-item text-1"
                                                    href="?url=web-admin/delete-download&downid=<?php echo $resDownload['id']; ?>&delete=wait">Delete
                                                    download</a></li>
                                        </ul>
                                    </div>

                                </td>
                            </tr>
                            <?php } }?>
                        </tbody>
                    </table>
                    <hr class="solid mt-5 opacity-4">
                    <div class="datatable-footer">
                        <div class="row align-items-center justify-content-between mt-3">
                            <div class="col-md-auto order-1 mb-3 mb-lg-0">
                                <div class="d-flex align-items-stretch">
                                    <select class="form-control select-style-1 bulk-action mr-3" name="bulk-action"
                                        style="min-width: 170px;">
                                        <option value="" selected>Bulk Actions</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <a href="#"
                                        class="bulk-action-apply btn btn-light btn-px-4 py-3 border font-weight-semibold text-color-dark text-3">Apply</a>
                                </div>
                            </div>
                            <div class="col-lg-auto text-center order-3 order-lg-2">
                                <div class="results-info-wrapper"></div>
                            </div>
                            <div class="col-lg-auto order-2 order-lg-3 mb-3 mb-lg-0">
                                <div class="pagination-wrapper"></div>
                            </div>
                        </div>
                    </div>
                    </table>
                </div>
            </div>

        </div>
    </div>
    <!-- end: page -->