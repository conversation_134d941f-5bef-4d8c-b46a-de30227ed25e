<?php

/**
| Copyright 2015
| Developed by FDEV
| All rights reserved.
| NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
if (isset($_GET['action']) && 'update' == $_GET['action']) {
 $returnSuccess = S_CONFIG_UPDATED . "   Created date is " . date("m-d-Y h:i:s");
}
?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-credit-card-front"></i> <?php echo PT_WEB_CONFIG; ?><sup
            class="badge badge-primary fw-500">ADDON</sup>
        <small>
            <?php echo PT_WEB_CONFIG; ?>
        </small>
    </h1>
</div>
<?php
if (isset($_POST['btn_savechange'])) {
 // get all inputs in one array
 $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);

 // condition
 if (empty($configForm['input_title'])) {
  $returnWarning = W_EMPTY_TITLE;
 } elseif (empty($configForm['input_appname'])) {
  $returnWarning = W_EMPTY_APPNAME;
 } else {

  // date now
  $getDateNow = date('Y-m-d H:i:s');

  // update title
  $updateConfigTitle      = "UPDATE WEB_Config SET value = '$configForm[input_title]' WHERE config_name = 'title'";
  $updateConfigTitleQuery = sqlsrv_query($conn, $updateConfigTitle, array());
  $updateConfigTitleRows  = sqlsrv_rows_affected($updateConfigTitleQuery);

  // we have some updates because the table structure
  if ($updateConfigTitleRows) {

   // update description
   $updateConfigDesc      = "UPDATE WEB_Config SET value = '$configForm[input_desc]' WHERE config_name = 'description'";
   $updateConfigDescQuery = sqlsrv_query($conn, $updateConfigDesc, array());
   $updateConfigDescRows  = sqlsrv_rows_affected($updateConfigDescQuery);

   // update appname
   $updateConfigAppname      = "UPDATE WEB_Config SET value = '$configForm[input_appname]' WHERE config_name = 'app_name'";
   $updateConfigAppnameQuery = sqlsrv_query($conn, $updateConfigAppname, array());
   $updateConfigAppnameRows  = sqlsrv_rows_affected($updateConfigAppnameQuery);

   if ($updateConfigDonateRows) {
    // update last_update column
    $updateLastUpdate      = "UPDATE WEB_Config SET value = '$getDateNow' WHERE config_name = 'last_update'";
    $updateLastUpdateQuery = sqlsrv_query($conn, $updateLastUpdate, array());
    $updateLastUpdateRows  = sqlsrv_rows_affected($updateLastUpdateQuery);

      if (sqlsrv_rows_affected($updateLastUpdateQuery)):
         echo '<script>
              Swal.fire(
                "SUCCESS !",
                "อัพเดตระบบให้เรียบร้อยแล้ว.",
                "success"
             )   
         </script>';
        else:
            echo '<script>
              Swal.fire(
              "ERROR !",
              "Error ไม่สามารถทำรายการได้.",
            "error"
           )   
         </script>';
      endif;
   }
  } else {
   $returnError = E_FAILED_UPDATE;
  }
 }
}
?>
<?php if (isset($returnSuccess)) { ?>
<div class="alert alert-success" role="alert"><?php echo $returnSuccess; ?></div>
<?php } elseif (isset($returnWarning)) { ?>
<div class="alert alert-warning" role="alert"><?php echo $returnWarning; ?></div>
<?php } elseif (isset($returnError)) { ?>
<div class="alert alert-danger" role="alert"><?php echo $returnError; ?></div>
<?php } ?>
<!-- start: page -->

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    <?php echo T_HEADER_INFO; ?> <span class="fw-300"><i><?php echo T_HEADER_INFO_DESC; ?></i></span>
                </h2>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="panel-tag">จัดการการแสดง ชื่อ Website Title </div>
                    <form class="ecommerce-form action-buttons-fixed" method="post">
                        <div class="form-group row">
                            <label for="input_title"
                                class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_TITLE; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-modern" name="input_title"
                                        id="input_title"
                                        value="<?php echo $zpanel->getConfigByValue('title', 'value', $conn); ?>"
                                        required />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="input_desc"
                                class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_DESC; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-modern" name="input_desc"
                                        id="input_desc"
                                        value="<?php echo $zpanel->getConfigByValue('description', 'value', $conn); ?>"
                                        required />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="input_appname"
                                class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_APPNAME; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-modern" name="input_appname"
                                        id="input_appname"
                                        value="<?php echo (!$zpanel->getConfigByValue('app_name', 'value', $conn) ? 'zPANEL' : $zpanel->getConfigByValue('app_name', 'value', $conn)); ?>" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-12 col-lg-6">
                                <button type="submit" name="btn_savechange" value=""
                                    class="btn btn-primary waves-effect waves-themed" data-loading-text="Loading...">
                                    <i class="bx bx-save text-4 mr-2"></i> <?php echo B_SAVECHANGES; ?>
                                </button>
                            </div>
                        </div>
                        <div class="panel-tag">จัดการระบบ ปรับปรุงระบบ เปิดระบบ {สีเขียว} ปิดระบบ {สีเทา}</div>
                        <div class="panel-content">
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_maintenance"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right"><?php echo T_WEB_MAINTENANCE_MODE; ?></label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_maintenance" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('maintenance', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_maintenance"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-tag">จัดการระบบ ระบบสมาชิก เปิดระบบ {สีเขียว} ปิดระบบ {สีเทา}</div>
                        <div class="panel-content">
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_register"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right"><?php echo T_WEB_REGISTER; ?></label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_register" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('register', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_register"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_ChangePassword"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                เปลียนพาสเวิส</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_ChangePassword" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('ChangePassword', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_ChangePassword"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-tag">จัดการระบบ ช็อป เปิดระบบ {สีเขียว} ปิดระบบ {สีเทา}</div>
                        <div class="panel-content">
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_store"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                Shop</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_store" type="checkbox" disabled
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('store', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_store"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Voucher"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                ระบบบัตรแคส</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Voucher" type="checkbox" onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Voucher', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Voucher"></label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Reward"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                Reward</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Reward" type="checkbox" disabled
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Reward', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Reward"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_donate"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                เติมเงิน</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_donate" type="checkbox" onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Donate', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_donate"></label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>


                        <div class="panel-tag">จัดการระบบ ผู้เล่น เปิดระบบ {สีเขียว} ปิดระบบ {สีเทา}</div>
                        <div class="panel-content">
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Creatcharector"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ อัพ
                                                Status</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_UpStatus" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('UpStatus', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_UpStatus"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_ResetStatus"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                รีเช็ต Status</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_ResetStatus" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('ResetStatus', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_ResetStatus"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_DeleteChar"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                ลบตัวละคร</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_DeleteChar" type="checkbox" disabled
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('DeleteChar', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_DeleteChar"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Creatcharector"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบสร้างตัวละคร</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Creatcharector" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Creatcharector', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Creatcharector"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_ChangeSkill"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                อัพเดทสกิล</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_ChangeSkill" type="checkbox" disabled
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('ChangeSkill', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_ChangeSkill"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_ChangeNation"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                เปลียนประเทศ</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_ChangeNation" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('ChangeNation', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_ChangeNation"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-tag">จัดการระบบ เกมส์ เปิดระบบ {สีเขียว} ปิดระบบ {สีเทา}</div>
                        <div class="panel-content">
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Event_ItemDaily"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ Game
                                                Daily Time</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Event_ItemDaily" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Event_ItemDaily', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Event_ItemDaily"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Event_GachaPong"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ
                                                Game Spin</label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Event_GachaPong" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Event_GachaPong', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Event_GachaPong"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Event_Thailotto"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ Game
                                                ทายหวย
                                            </label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Event_Thailotto" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Event_Thailotto', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Event_Thailotto"></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="frame-wrap demo">
                                        <div class="demo">
                                            <label for="checkbox_Event_Football"
                                                class="col-form-label col-6 col-lg-3 form-label text-lg-right">ระบบ Game
                                                ทายหวย
                                            </label>
                                            <div class="switch-button showcase-switch-button">
                                                <input id="checkbox_Event_Football" type="checkbox"
                                                    onchange="updateDatabase()"
                                                    <?php if ($zpanel->getConfigByValue('Event_Football', 'value', $conn) == '0') { echo ''; } else { echo 'checked'; }  ?>>
                                                <label for="checkbox_Event_Football"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <!-- modal -->
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function updateDatabase() {
    var checkboxValue_donate = document.getElementById("checkbox_donate").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_donate=" + checkboxValue_donate);

    var checkboxValue_register = document.getElementById("checkbox_register").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_register=" + checkboxValue_register);

    var checkboxValue_ChangePassword = document.getElementById("checkbox_ChangePassword").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_ChangePassword=" + checkboxValue_ChangePassword);

    var checkboxValue_maintenance = document.getElementById("checkbox_maintenance").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_maintenance=" + checkboxValue_maintenance);

    var checkboxValue_store = document.getElementById("checkbox_store").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_store=" + checkboxValue_store);

    var checkboxValue_Voucher = document.getElementById("checkbox_Voucher").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_Voucher=" + checkboxValue_Voucher);

    var checkboxValue_Reward = document.getElementById("checkbox_Reward").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_Reward=" + checkboxValue_Reward);

    var checkboxValue_ChangeNation = document.getElementById("checkbox_ChangeNation").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_ChangeNation=" + checkboxValue_ChangeNation);

    var checkboxValue_DeleteChar = document.getElementById("checkbox_DeleteChar").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_DeleteChar=" + checkboxValue_DeleteChar);

    var checkboxValue_ResetStatus = document.getElementById("checkbox_ResetStatus").checked ? 1 : 0;
    var checkboxValue_UpStatus = document.getElementById("checkbox_UpStatus").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_ResetStatus=" + checkboxValue_ResetStatus);

    var checkboxValue_UpStatus = document.getElementById("checkbox_UpStatus").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_UpStatus=" + checkboxValue_UpStatus);

    var checkboxValue_Creatcharector = document.getElementById("checkbox_Creatcharector").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkboxValue_Creatcharector=" + checkboxValue_Creatcharector);

    var checkbox_Event_ItemDaily = document.getElementById("checkbox_Event_ItemDaily").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkbox_Event_ItemDaily=" + checkbox_Event_ItemDaily);

    var checkbox_Event_GachaPong = document.getElementById("checkbox_Event_GachaPong").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkbox_Event_GachaPong=" + checkbox_Event_GachaPong);

    var checkbox_Event_Thailotto = document.getElementById("checkbox_Event_Thailotto").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkbox_Event_Thailotto=" + checkbox_Event_Thailotto);

    var checkbox_Event_Football = document.getElementById("checkbox_Event_Football").checked ? 1 : 0;
    // ส่งค่า checkbox ไปยังเซิร์ฟเวอร์
    // ใช้ Ajax เพื่อส่งข้อมูลไปยังเซิร์ฟเวอร์โดยไม่ต้องรีเฟรชหน้าเว็บ
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "files/web-admin/update_checkbox.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
            document.getElementById("statusMessage").innerText = xhr.responseText;
        }
    };
    xhr.send("checkbox_Event_Football=" + checkbox_Event_Football);
}
</script>