<?php $user->restrictionUser(true, $conn); ?>

<?php
// Function to get mail statistics
function getMailStatistics($conn) {
    $stats = array();
    
    try {
        // Total mails sent
        $sql = "SELECT COUNT(*) as total_sent FROM [".DATABASE_SV."].[dbo].cabal_mail_sent_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_sent'] = $row['total_sent'] ?? 0;
        }
        
        // Total mails received
        $sql = "SELECT COUNT(*) as total_received FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_received'] = $row['total_received'] ?? 0;
        }
        
        // Mails sent today
        $sql = "SELECT COUNT(*) as sent_today FROM [".DATABASE_SV."].[dbo].cabal_mail_sent_table 
                WHERE CAST(DeliveryTime AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['sent_today'] = $row['sent_today'] ?? 0;
        }
        
        // Mails received today
        $sql = "SELECT COUNT(*) as received_today FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE CAST(DeliveryTime AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['received_today'] = $row['received_today'] ?? 0;
        }
        
        // Unread mails
        $sql = "SELECT COUNT(*) as unread_mails FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE IsReceivedItem = 0 AND IsReceivedAlz = 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['unread_mails'] = $row['unread_mails'] ?? 0;
        }
        
        // Mails with items
        $sql = "SELECT COUNT(*) as mails_with_items FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE ItemKindIdx > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['mails_with_items'] = $row['mails_with_items'] ?? 0;
        }
        
        // Mails with Alz
        $sql = "SELECT COUNT(*) as mails_with_alz FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE Alz > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['mails_with_alz'] = $row['mails_with_alz'] ?? 0;
        }
        
        // Total Alz sent
        $sql = "SELECT SUM(Alz) as total_alz_sent FROM [".DATABASE_SV."].[dbo].cabal_mail_sent_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_alz_sent'] = $row['total_alz_sent'] ?? 0;
        }
        
        // Deleted mails (sent)
        $sql = "SELECT COUNT(*) as deleted_sent FROM [".DATABASE_WEB."].[dbo].cabal_mail_sent_deleted_logs";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['deleted_sent'] = $row['deleted_sent'] ?? 0;
        }
        
        // Deleted mails (received)
        $sql = "SELECT COUNT(*) as deleted_received FROM [".DATABASE_WEB."].[dbo].cabal_mail_received_deleted_logs";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['deleted_received'] = $row['deleted_received'] ?? 0;
        }
        
    } catch (Exception $e) {
        error_log("Mail statistics error: " . $e->getMessage());
    }
    
    return $stats;
}

// Get statistics
$mailStats = getMailStatistics($conn);
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-bar"></i> สถิติระบบ Mail
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_mail/mail-monitor" class="btn btn-warning btn-sm">
            <i class="fal fa-radar"></i> ตรวจสอบสด
        </a>
        <a href="?url=manager_mail/mail-analytics" class="btn btn-info btn-sm">
            <i class="fal fa-analytics"></i> การวิเคราะห์
        </a>
        <a href="?url=manager_mail/mail-settings" class="btn btn-secondary btn-sm">
            <i class="fal fa-cogs"></i> ตั้งค่า
        </a>
        <button class="btn btn-success btn-sm" onclick="refreshStats()">
            <i class="fal fa-sync"></i> รีเฟรช
        </button>
    </div>
</div>

<div class="row">
    <!-- Mail Overview Stats -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-paper-plane text-primary"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">เมลล์ที่ส่งทั้งหมด</div>
                        <div class="h3 mb-0"><?php echo number_format($mailStats['total_sent'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-inbox text-success"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">เมลล์ที่รับทั้งหมด</div>
                        <div class="h3 mb-0"><?php echo number_format($mailStats['total_received'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-envelope text-warning"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">เมลล์ที่ยังไม่อ่าน</div>
                        <div class="h3 mb-0"><?php echo number_format($mailStats['unread_mails'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card border-0 mb-g">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="icon-stack display-4 flex-shrink-0">
                        <i class="fal fa-coins text-info"></i>
                    </div>
                    <div class="ml-3">
                        <div class="text-muted small">Alz ที่ส่งทั้งหมด</div>
                        <div class="h3 mb-0"><?php echo number_format($mailStats['total_alz_sent'] ?? 0); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Today's Activity -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมวันนี้</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-primary"><?php echo number_format($mailStats['sent_today'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์ที่ส่งวันนี้</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-success"><?php echo number_format($mailStats['received_today'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์ที่รับวันนี้</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mail Content Stats -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">สถิติเนื้อหาเมลล์</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-warning"><?php echo number_format($mailStats['mails_with_items'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์ที่มีไอเท็ม</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-info"><?php echo number_format($mailStats['mails_with_alz'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์ที่มี Alz</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Deleted Mails Stats -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">เมลล์ที่ถูกลบ</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-danger"><?php echo number_format($mailStats['deleted_sent'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์ส่งที่ถูกลบ</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h2 text-danger"><?php echo number_format($mailStats['deleted_received'] ?? 0); ?></div>
                            <div class="text-muted">เมลล์รับที่ถูกลบ</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">การดำเนินการด่วน</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="?url=manager_mail/see-mailreceiver" class="btn btn-primary">
                        <i class="fal fa-inbox"></i> ดูเมลล์ที่รับ
                    </a>
                    <a href="?url=manager_mail/see-mailsend" class="btn btn-success">
                        <i class="fal fa-paper-plane"></i> ดูเมลล์ที่ส่ง
                    </a>
                    <a href="?url=manager_mail/see-mailreceiver-delete" class="btn btn-warning">
                        <i class="fal fa-trash"></i> ดูเมลล์รับที่ถูกลบ
                    </a>
                    <a href="?url=manager_mail/see-mailsend-delete" class="btn btn-danger">
                        <i class="fal fa-trash"></i> ดูเมลล์ส่งที่ถูกลบ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    location.reload();
}

// Auto refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
