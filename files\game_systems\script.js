function itemGenerator() {
    return {
        // State
        itemId: '',
        itemType: 'Helm',
        upgrade: 0,
        extreme: 0,
        divine: 0,
        advanceSetting: '0',
        craftHeight: 0,
        craftOption: 'EMPTY',
        slot1: 'NOT',
        slot2: 'NOT',
        slot3: 'NOT',
        characterName: '',
        itemCode: '',
        optionsCode: '',
        searchQuery: '',
        selectedItem: '',
        options: [],
        filteredItems: [],
        // Static map (ตัวอย่าง, ควรเติมให้ครบ)
        itemMap: {
            'Helm': [{
                    hex: '1',
                    name: 'MP'
                }, {
                    hex: '2',
                    name: 'DEF'
                }, {
                    hex: '3',
                    name: 'DEF RATE'
                }, {
                    hex: '4',
                    name: 'CRIT DAMAGE'
                },
                {
                    hex: '5',
                    name: 'CRIT RATE'
                }, {
                    hex: '6',
                    name: '2SLOT DROP'
                }, {
                    hex: '7',
                    name: 'SKILL EXP'
                }, {
                    hex: '8',
                    name: 'SWORD SKILL AMP'
                },
                {
                    hex: '9',
                    name: '<PERSON><PERSON><PERSON> SKILL AMP'
                }, {
                    hex: 'A',
                    name: 'ALL ATTACK UP'
                }, {
                    hex: 'B',
                    name: 'MAX HP STEAL'
                }, {
                    hex: 'C',
                    name: 'MAX MP STEAL'
                },
                {
                    hex: 'D',
                    name: 'ALZ DROP AMMOUNT'
                }, {
                    hex: 'E',
                    name: '1 SLOT ITEM DROP'
                }, {
                    hex: 'F',
                    name: 'ALL SKILL AMP'
                }
            ],
            'Suit': [{
                    hex: '1',
                    name: 'HP'
                }, {
                    hex: '2',
                    name: 'DEF'
                }, {
                    hex: '3',
                    name: 'DEF RATE'
                }, {
                    hex: '4',
                    name: 'MP'
                },
                {
                    hex: '5',
                    name: 'HP AUTO HEAL'
                }, {
                    hex: '6',
                    name: '2 SLOTS ITEM DROP'
                }, {
                    hex: '7',
                    name: 'SKILL EXP'
                }, {
                    hex: '8',
                    name: 'SWORD SKILL AMP'
                },
                {
                    hex: '9',
                    name: 'MAGIC SKILL AMP'
                }, {
                    hex: 'A',
                    name: 'ALL ATTACK UP'
                }, {
                    hex: 'B',
                    name: 'MP AUTO HEAL'
                }, {
                    hex: 'C',
                    name: 'MAX CRIT RATE'
                },
                {
                    hex: 'D',
                    name: 'ALZ DROP AMOUNT'
                }, {
                    hex: 'E',
                    name: 'FLEE RATE'
                }, {
                    hex: 'F',
                    name: 'ALL SKILL AMP'
                }
            ],
            // ...เติมให้ครบแบบเดียวกัน...
        },
        // Init
        init() {
            this.updateOptions();
        },
        updateOptions() {
            this.options = this.itemMap[this.itemType] || [];
            this.slot1 = 'NOT';
            this.slot2 = 'NOT';
            this.slot3 = 'NOT';
            this.craftOption = 'EMPTY';
        },
        updateSlot(slotNum) {
            // ไม่ต้องทำอะไรพิเศษ slot1/2/3 จะอัปเดตอัตโนมัติ
        },
        updateCraft() {
            // ไม่ต้องทำอะไร craftOption, craftHeight จะอัปเดตอัตโนมัติ
        },
        calculateItemCode() {
            // คำนวณ itemCode (ตัวอย่าง logic)
            let id = parseInt(this.itemId) || 0;
            let adv = parseInt(this.advanceSetting) || 0;
            let upg = parseInt(this.upgrade) || 0;
            let ext = parseInt(this.extreme) || 0;
            let div = parseInt(this.divine) || 0;
            this.itemCode = id + adv + (upg * 8192) + (ext * **********) + (div * ***********);
        },
        generateCode() {
            this.calculateItemCode();
            // ตัวอย่าง optionsCode (ควรเติม logic จริง)
            let opt1 = this.slot1 !== 'NOT' ? this.slot1 : '';
            let opt2 = this.slot2 !== 'NOT' ? this.slot2 : '';
            let opt3 = this.slot3 !== 'NOT' ? this.slot3 : '';
            let all = opt1 + opt2 + opt3;
            this.optionsCode = all;
        },
        sendToAccount() {
            this.generateCode();
            let self = this;
            fetch('_app/php/item_editor_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        item_id: this.itemId,
                        item_type: this.itemType,
                        upgrade: this.upgrade,
                        extreme: this.extreme,
                        divine: this.divine,
                        advance_setting: this.advanceSetting,
                        craft: '', // เพิ่ม logic craft ถ้าต้องการ
                        craft_height: this.craftHeight,
                        craft_option: this.craftOption,
                        slot1: this.slot1,
                        slot2: this.slot2,
                        slot3: this.slot3,
                        account_name: this.characterName
                    })
                })
                .then(r => r.json())
                .then(res => {
                    if (res.success) {
                        self.itemCode = res.item_code;
                        self.optionsCode = res.options_code;
                        alert('ส่งไอเท็มสำเร็จ!');
                    } else {
                        alert('ผิดพลาด: ' + res.message);
                    }
                });
        },
        setItemId() {
            this.itemId = this.selectedItem;
        }
    }
}
document.addEventListener('alpine:init', () => {
    Alpine.data('itemGenerator', () => ({
        itemId: '',
        itemType: 'Helm',
        upgrade: 0,
        extreme: 0,
        divine: 0,
        advanceSetting: 0,
        slot1: 'NOT',
        slot2: 'NOT',
        slot3: 'NOT',
        craftHeight: 0,
        craftOption: 'EMPTY',
        characterName: '',
        itemCode: '',
        optionsCode: '',
        searchQuery: '',
        selectedItem: '',
        options: ['MP', 'DEF', 'DEF RATE', 'CRIT DAMAGE', 'CRIT RATE', '2SLOT DROP', 'SKILL EXP', 'SWORD SKILL AMP', 'MAGIC SKILL AMP', 'ALL ATTACK UP', 'MAX HP STEAL', 'MAX MP STEAL', 'ALZ DROP AMMOUNT', '1 SLOT ITEM DROP', 'ALL SKILL AMP', 'EMPTY', 'NOT'],

        itemMapName: {
            "Helm": ["MP", "DEF", "DEF RATE", "CRIT DAMAGE", "CRIT RATE", "2SLOT DROP", "SKILL EXP", "SWORD SKILL AMP", "MAGIC SKILL AMP", "ALL ATTACK UP", "MAX HP STEAL", "MAX MP STEAL", "ALZ DROP AMMOUNT", "1 SLOT ITEM DROP", "ALL SKILL AMP"],
            "Suit": ["HP", "DEF", "DEF RATE", "MP", "HP AUTO HEAL", "2 SLOTS ITEM DROP", "SKILL EXP", "SWORD SKILL AMP", "MAGIC SKILL AMP", "ALL ATTACK UP", "MP AUTO HEAL", "MAX CRIT RATE", "ALZ DROP AMOUNT", "FLEE RATE", "ALL SKILL AMP"],
            "Gloves": ["DEF", "ATTACK RATE", "DEF RATE", "ATTACK", "HP AUTO HEAL", "2 SLOTS ITEM DROP", "SKILL EXP", "SWORD SKILL AMP", "MAGIC SKILL AMP", "ALL ATTACK UP", "MP AUTO HEAL", "MAX CRIT RATE", "HP STEAL", "MP STEAL", "ALL SKILL AMP"],
            "Boots": ["DEF", "DEF RATE", "HP", "MP", "HP AUTO HEAL", "2 SLOTS ITEM DROP", "SKILL EXP", "SWORD SKILL AMP", "MAGIC SKILL AMP", "ALL ATTACK UP", "MP AUTO HEAL", "MAX HP STEAL", "ALZ DROP AMMOUNT", "FLEE RATE", "ALL SKILL AMP"],
            "Weapon": ["ATTACK", "MAGIC ATTACK", "ATTACK RATE", "CRIT DAMAGE", "CRIT RATE", "2 SLOTS ITEM DROP", "SKILL EXP", "SWORD SKILL AMP", "MAGIC SKILL AMP", "ALL ATTACK UP", "MIN DAMAGE", "ADD DAMAGE", "ALZ DROP", "1 SLOT DROP", "ALL SKILL AMP"],
            "Bike": ["HP", "ATTACK", "MAGIC ATTACK", "CRIT DAMAGE", "CRIT RATE", "MAX CRIT RATE", "SWORD SKILL AMP", "MAGIC SKILL AMP", "RESIST CRIT RATE", "RESIST CRIT DAMAGE", "RESIST SKILL AMP", "ALL ATTACK UP", "ALL SKILL AMP", "NON VALID", "NON VALID"]
        },

        itemDict: {
            "Upgrade Core (Medium)": "10",
            "Forcium Great Sword": "100",
            // ... (truncated for brevity, add full itemDict from your Python code)
        },

        filteredItems() {
            return Object.entries(this.itemDict)
                .filter(([name]) => name.toLowerCase().includes(this.searchQuery.toLowerCase()))
                .map(([name, id]) => ({
                    name,
                    id
                }));
        },

        updateOptions() {
            this.options = [...this.itemMapName[this.itemType], 'EMPTY', 'NOT'];
            this.slot1 = 'NOT';
            this.slot2 = 'NOT';
            this.slot3 = 'NOT';
            this.craftOption = 'EMPTY';
        },

        updateSlot(slotNum) {
            const slots = [this.slot1, this.slot2, this.slot3];
            this.calculateItemCode();
        },

        updateCraft() {
            this.calculateItemCode();
        },

        calculateItemCode() {
            let itemCode = parseInt(this.itemId) || 0;
            itemCode += parseInt(this.advanceSetting) || 0;
            itemCode += parseInt(this.upgrade) * 8192 || 0;
            itemCode += parseInt(this.extreme) * ********** || 0;
            itemCode += parseInt(this.divine) * *********** || 0;
            this.itemCode = itemCode;

            let optionsCode = '';
            const slots = [this.slot1, this.slot2, this.slot3].filter(s => s !== 'NOT' && s !== 'EMPTY');
            const craft = this.craftOption !== 'EMPTY' && this.craftOption !== 'NOT' ? this.getCraftCode() : '00';

            if (slots.length === 0) {
                optionsCode = craft !== '00' ? `000000${craft}` : '00000000';
            } else {
                let allOptions = slots.join('');
                let optonCount = '';
                for (let i = 0; i < allOptions.length; i++) {
                    if (allOptions[i] === '0') {
                        optonCount = '00' + optonCount;
                    } else {
                        let count = allOptions.split(allOptions[i]).length - 1;
                        allOptions = allOptions.replace(new RegExp(allOptions[i], 'g'), '0');
                        optonCount += count + allOptions[i];
                    }
                }
                if (optonCount.length === 2) optonCount = '0000' + optonCount;
                else if (optonCount.length === 4) optonCount = '00' + optonCount;
                else if (optonCount.length === 8) optonCount = optonCount.substring(2);

                optionsCode = optonCount;
                if (slots.length === 1) {
                    optionsCode = craft !== '00' ? `1${optionsCode.substring(1)}${craft}` : `10${optionsCode}`;
                } else if (slots.length === 2) {
                    optionsCode = craft !== '00' ? `2${optionsCode.substring(1)}${craft}` : `20${optionsCode}`;
                } else if (slots.length === 3) {
                    optionsCode = craft !== '00' ? `3${optionsCode.substring(1)}${craft}` : `30${optionsCode}`;
                }
            }
            this.optionsCode = parseInt(optionsCode, 16);
        },

        getCraftCode() {
            const heightMap = {
                1: '9',
                2: 'A',
                3: 'B',
                4: 'C',
                5: 'D',
                6: 'E',
                7: 'F'
            };
            const height = heightMap[this.craftHeight] || '0';
            return `${height}${this.getSlotType(this.itemType, this.options.indexOf(this.craftOption))}`;
        },

        getSlotType(itemType, index) {
            return index !== -1 && index !== 15 ? this.itemMapName[itemType][index][0] : '0';
        },

        setItemId() {
            this.itemId = this.selectedItem;
            this.calculateItemCode();
        },

        async sendToAccount() {
            const accountId = await this.getUserNumAccount(this.characterName);
            if (!accountId) {
                alert('Invalid Character');
                return;
            }
            await this.generateCode();
            const response = await fetch('backend.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `accountId=${accountId}&itemCode=${this.itemCode}&optionsCode=${this.optionsCode}`
            });
            const result = await response.text();
            alert(result);
        },

        async getUserNumAccount(characterName) {
            const response = await fetch(`backend.php?action=getUserNum&characterName=${characterName}`);
            const result = await response.json();
            return result.usernum || 0;
        },

        generateCode() {
            if (!this.itemId || isNaN(this.itemId)) {
                alert('Invalid ID');
                return;
            }
            this.calculateItemCode();
        }
    }));
});