<?php $user->restrictionUser(true, $conn); ?>
<header class="page-header">
						<h2><?php echo PT_MANAGEPLAYERS; ?></h2>
					
						<div class="right-wrapper pull-right">
							<ol class="breadcrumbs">
								<li>
									<a href="index.php">
										<i class="fa fa-home"></i>
									</a>
								</li>
								<li><span><?php echo PT_MANAGEPLAYERS_DESC; ?></span></li>
							</ol>
							<a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
						</div>
					</header>

                    <section class="panel">
							<header class="panel-heading">
								<div class="panel-actions">
									<a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
									<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
								</div>
						
								<h2 class="panel-title">ตรวจสอบตัวละคร แบน ยืนยันไอดี</h2>
							</header>
							<div class="panel-body">
								<div class="table-responsive">
                                        <form method="post" action="?url=manager/presults" class="form-inline pull-right">
                                                <div class="form-group">
                                                    <input type="text" class="form-control" name="search" placeholder="ค้นหา ID IP Email Phone">
                                                    <input type="submit" class="btn btn-info" name="btn_search" value="Search">
                                                </div>
                                        </form>
                                        <hr>
                                        <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
                                            <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                                        <?php } ?>
									<table class="table table-bordered table-striped table-condensed mb-none">
										<thead>
                                            <tr>
                                                <th>UserNum</th>
                                                <th>ID</th>
                                                <th>LastIp</th>
                                                <th>Email</th>
                                                <th>IP</th>
                                                <th>Phone</th>
                                                <th><?php echo T_ACTION; ?></th>
                                            </tr>
										</thead>
										<tbody>
										<?php
 
                                                // generic function to get page
                                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                                    $rows = array();
                                                    $i = 0;
                                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                                        array_push($rows, $row);
                                                        $i++;
                                                    }
                                                    return $rows;
                                                }

                                                // Set the number of rows to be returned on a page.
                                                $rowsPerPage =20;

                                                // Define and execute the query.  
                                                // Note that the query is executed with a "scrollable" cursor.
                                                $sql = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table ORDER BY UserNum DESC";

                                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                                if (!$stmt)
                                                    die(print_r(sqlsrv_errors(), true));

                                                // Get the total number of rows returned by the query.
                                                $rowsReturned = sqlsrv_num_rows($stmt);
                                                if ($rowsReturned === false)
                                                    die(print_r(sqlsrv_errors(), true));
                                                elseif ($rowsReturned == 0) {
                                                    echo W_NOTHING_RETURNED;
                                                    //exit();
                                                } else {
                                                    /* Calculate number of pages. */
                                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                                }

                                                // Display the selected page of data.
                                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                                foreach ($page as $row) {  ?>
                                                    <tr
                                                    <?php
                                                    $selectUsersData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$row[0]'";
                                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, array());
                                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                                                    if ($selectUsersDataFetch['AuthType'] == '2' ||  $selectUsersDataFetch['AuthType'] == '3' ||  $selectUsersDataFetch['AuthType'] == '4') {   
                                                        echo ' class="bg-red text-white"';
                                                    }
                                                    ?>>
                                                        <td><?php echo $row[0]; ?></td>
                                                        <td><?php echo $row[1]; ?></td>
                                                        <td><?php echo $row[10]; ?></td>
                                                        <td><?php echo $row[14]; ?></td>
                                                        <td><?php echo $row[15]; ?></td>
                                                        <td><?php echo $row[19]; ?></td>
                                                        <td>        <a class="text-info" href="?url=manager/see-player&id=<?php echo $row[0]; ?>" data-toggle="tooltip" data-placement="left" title="" data-original-title="รายระเอียด"><i class="fa fa-info-circle"></i></a>
                                                                    <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                                                    <?php if ($selectUsersDataFetch['AuthType'] == '2' || $selectUsersDataFetch['AuthType'] == '3' || $selectUsersDataFetch['AuthType'] == '4') { ?>
                                                                        <a class="text-danger" href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=unban-wait" data-toggle="tooltip" data-placement="left" title="" data-original-title="โดนแบน"><i class="fa fa-ban"></i></a>
                                                                    <?php } else { ?>
                                                                        <a  class="text-success" href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=wait" data-toggle="tooltip" data-placement="left" title="" data-original-title="ต้องการแบนด่วน"><i class="fa fa-check"></i></a>
                                                                    <?php } ?>
                                                                    <?php } ?>
                                                                    <?php 
                                                                             // get data info
                                                                        $selectPlayerData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_charge_auth WHERE UserNum = '".$row[0]."'";
                                                                        $selectPlayerDataParam = array();
                                                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                                                        $selectPlayerDataFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);


                                                                        // Variables Date
                                                                        $start_date = date('d-m-Y', strtotime($selectPlayerDataFetch['ExpireDate']));
                                                                        $expire_date = date('d-m-Y', strtotime($selectPlayerDataFetch['ExpireDate']));
                                                                        $today_date = date('d-m-Y');

                                                                        /* Start Date */
                                                                        $start_explode = explode('-', $start_date);
                                                                        $start_day = $start_explode[0];
                                                                        $start_month = $start_explode[1];
                                                                        $start_year = $start_explode[2];

                                                                        /* Expire Date */
                                                                        $expire_explode = explode('-', $expire_date);
                                                                        $expire_day = $expire_explode[0];
                                                                        $expire_month = $expire_explode[1];
                                                                        $expire_year = $expire_explode[2];

                                                                        /* Today Date */
                                                                        $today_explode = explode('-', $today_date);
                                                                        $today_day = $today_explode[0];
                                                                        $today_month = $today_explode[1];
                                                                        $today_year = $today_explode[2];

                                                                        $start = gregoriantojd($start_month, $start_day, $start_year);
                                                                        $expire = gregoriantojd($expire_month, $expire_day, $expire_year);
                                                                        $today = gregoriantojd($today_month, $today_day, $today_year);

                                                                        $date_current = $expire - $today; //หาวันที่ยังเหลืออยู่
                                                                    
                                                                    
                                                                    if ($date_current > 0) { ?>
                                                                        <a class="text-warning" href="?url=manager/see-player&id=<?php echo $row[0]; ?>" data-toggle="tooltip" data-placement="left" title="" data-original-title="ไอดีนี้ unlock แล้ว"><i class="fa fa-unlock-alt"></i></a>
                                                                    <?php } else { ?>
                                                                        <a class="text-muted" href="?url=manager/see-player&id=<?php echo $row[0]; ?>" data-toggle="tooltip" data-placement="left" title="" data-original-title="ไอดีนี้ถูก lock อยู่"><i class="fa fa-lock"></i></a>
                                                                    <?php } ?>
                                                            
                                                        </td>
                                                    </tr>
                                                    <?php
                                                }
                                                ?>
						
										</tbody>
									</table>
								</div>
                            </div>
                            <div class="panel-body">
                            <div class="col-lg-12 text-center">
                                <ul class="pagination">
                                    <script type="text/javascript">
                                        $(document).ready(function(){
                                        $('.pagination').pagination({
                                                items: <?php echo $rowsReturned;?>,
                                                itemsOnPage: <?php echo $rowsPerPage;?>,
                                                cssStyle: 'light-theme',
                                                currentPage : <?php echo $pageNum;?>,
                                                hrefTextPrefix : '?url=manager/players&pageNum='
                                            });
                                            });
                                        </script>
                                        <?php
                                        sqlsrv_close($conn);
                                        ?>
                                </ul>
                            </div>
                        </div>
						</section>                
                      