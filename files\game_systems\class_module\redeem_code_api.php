<?php
session_start();
if (!isset($_SESSION['userLogin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่มีการ login']);
    exit;
}

require_once("../../../_app/dbinfo.inc.php");
require_once("../../../_app/general_config.inc.php");

$conn = db_connect();

// รับข้อมูลจาก POST
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'generate':
        generateCodes($conn);
        break;
    
    case 'list':
        listCodes($conn);
        break;
    
    case 'delete':
        deleteCode($conn);
        break;
    
    default:
        echo json_encode(['success' => false, 'message' => 'Action ไม่ถูกต้อง']);
        break;
}

function generateCodes($conn) {
    try {
        $count = intval($_POST['count'] ?? 1);
        $items = trim($_POST['items'] ?? '');
        $quantity = intval($_POST['quantity'] ?? 1);
        $expiry_date = $_POST['expiry_date'] ?? '';
        $code_format = $_POST['code_format'] ?? 'XXXX-XXXX-XXXX-XXXX';

        // ตรวจสอบข้อมูล
        if ($count < 1 || $count > 1000) {
            echo json_encode(['success' => false, 'message' => 'จำนวน Code ต้องอยู่ระหว่าง 1-1000']);
            return;
        }

        if (empty($items)) {
            echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายการไอเท็ม']);
            return;
        }

        if ($quantity < 1) {
            echo json_encode(['success' => false, 'message' => 'Quantity ต้องมากกว่า 0']);
            return;
        }

        // ตรวจสอบรูปแบบไอเท็ม
        $itemList = explode(',', $items);
        foreach ($itemList as $item) {
            $item = trim($item);
            if (!preg_match('/^\d+:\d+:\d+$/', $item)) {
                echo json_encode(['success' => false, 'message' => "รูปแบบไอเท็มไม่ถูกต้อง: $item (ต้องเป็น itemid:option:duration)"]);
                return;
            }
        }

        // ตรวจสอบและสร้าง table ถ้าไม่มี
        $tableResult = createTableIfNotExists($conn);
        if (!$tableResult) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถสร้างหรือตรวจสอบ table ได้']);
            return;
        }
        
        $generatedCodes = [];
        $successCount = 0;
        
        for ($i = 0; $i < $count; $i++) {
            $code = generateUniqueCode($conn, $code_format);

            if ($code === false) {
                // ไม่สามารถสร้าง unique code ได้
                continue;
            }

            $sql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '1', GETDATE(), ?)";
            $params = [$code, $items, $quantity, !empty($expiry_date) ? $expiry_date : null];

            $stmt = sqlsrv_query($conn, $sql, $params);

            if ($stmt) {
                $successCount++;

                // ดึงข้อมูลที่เพิ่งสร้าง
                $selectSql = "SELECT TOP 1 * FROM WEB_Redeem_Code WHERE code = ? ORDER BY id DESC";
                $selectStmt = sqlsrv_query($conn, $selectSql, [$code]);

                if ($selectStmt && $row = sqlsrv_fetch_array($selectStmt, SQLSRV_FETCH_ASSOC)) {
                    // แปลง DateTime objects เป็น string
                    if (isset($row['datecreated']) && $row['datecreated'] instanceof DateTime) {
                        $row['datecreated'] = $row['datecreated']->format('Y-m-d H:i:s');
                    }
                    if (isset($row['expiry_date']) && $row['expiry_date'] instanceof DateTime) {
                        $row['expiry_date'] = $row['expiry_date']->format('Y-m-d H:i:s');
                    }
                    $generatedCodes[] = $row;
                }
            } else {
                // Log SQL error for debugging
                $errors = sqlsrv_errors();
                error_log("SQL Error in redeem_code_api.php: " . print_r($errors, true));
            }
        }
        
        echo json_encode([
            'success' => true, 
            'message' => "สร้าง $successCount codes สำเร็จ",
            'codes' => $generatedCodes
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function generateUniqueCode($conn, $format) {
    $maxAttempts = 100;
    $attempts = 0;

    while ($attempts < $maxAttempts) {
        $code = generateCodeByFormat($format);

        if (!$code) {
            $attempts++;
            continue;
        }

        // ตรวจสอบว่า code ซ้ำหรือไม่
        $checkSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code WHERE code = ?";
        $checkStmt = sqlsrv_query($conn, $checkSql, [$code]);

        if ($checkStmt) {
            $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
            if ($result && $result['count'] == 0) {
                return $code;
            }
        } else {
            // หาก query ล้มเหลว ให้ log error และลองใหม่
            $errors = sqlsrv_errors();
            error_log("SQL Error in generateUniqueCode: " . print_r($errors, true));
        }

        $attempts++;
    }

    return false; // ไม่สามารถสร้าง unique code ได้
}

function generateCodeByFormat($format) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

function listCodes($conn) {
    try {
        // ตรวจสอบและสร้าง table ถ้าไม่มี
        createTableIfNotExists($conn);
        
        $sql = "SELECT TOP 100 * FROM WEB_Redeem_Code ORDER BY datecreated DESC";
        $stmt = sqlsrv_query($conn, $sql);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถดึงข้อมูลได้']);
            return;
        }
        
        $codes = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // แปลง DateTime objects เป็น string
            if (isset($row['datecreated']) && $row['datecreated'] instanceof DateTime) {
                $row['datecreated'] = $row['datecreated']->format('Y-m-d H:i:s');
            }
            if (isset($row['expiry_date']) && $row['expiry_date'] instanceof DateTime) {
                $row['expiry_date'] = $row['expiry_date']->format('Y-m-d H:i:s');
            }
            $codes[] = $row;
        }
        
        echo json_encode(['success' => true, 'codes' => $codes]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function deleteCode($conn) {
    try {
        $id = intval($_POST['id'] ?? 0);
        
        if ($id <= 0) {
            echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
            return;
        }
        
        $sql = "DELETE FROM WEB_Redeem_Code WHERE id = ? AND status = '1'";
        $stmt = sqlsrv_query($conn, $sql, [$id]);
        
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
            return;
        }
        
        echo json_encode(['success' => true, 'message' => 'ลบ Code สำเร็จ']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
}

function createTableIfNotExists($conn) {
    try {
        // ตรวจสอบว่า table มีอยู่หรือไม่
        $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_Redeem_Code'";
        $checkStmt = sqlsrv_query($conn, $checkSql);

        if (!$checkStmt) {
            error_log("Error checking table existence: " . print_r(sqlsrv_errors(), true));
            return false;
        }

        $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if (!$result) {
            error_log("Error fetching table check result: " . print_r(sqlsrv_errors(), true));
            return false;
        }

        if ($result['count'] == 0) {
            // สร้าง table ใหม่
            $createSql = "CREATE TABLE [dbo].[WEB_Redeem_Code] (
                [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
                [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
                [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
                [quantity] int NULL,
                [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
                [datecreated] datetime DEFAULT getdate() NULL,
                [expiry_date] datetime NULL
            )";

            $createStmt = sqlsrv_query($conn, $createSql);
            if (!$createStmt) {
                error_log("Error creating table: " . print_r(sqlsrv_errors(), true));
                return false;
            }
        } else {
            // ตรวจสอบว่ามี column expiry_date หรือไม่
            $checkColumnSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'WEB_Redeem_Code' AND COLUMN_NAME = 'expiry_date'";
            $checkColumnStmt = sqlsrv_query($conn, $checkColumnSql);

            if ($checkColumnStmt) {
                $columnResult = sqlsrv_fetch_array($checkColumnStmt, SQLSRV_FETCH_ASSOC);
                if ($columnResult && $columnResult['count'] == 0) {
                    // เพิ่ม column expiry_date
                    $addColumnSql = "ALTER TABLE [dbo].[WEB_Redeem_Code] ADD [expiry_date] datetime NULL";
                    $addColumnStmt = sqlsrv_query($conn, $addColumnSql);
                    if (!$addColumnStmt) {
                        error_log("Error adding expiry_date column: " . print_r(sqlsrv_errors(), true));
                    }
                }
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Exception in createTableIfNotExists: " . $e->getMessage());
        return false;
    }
}
?>
