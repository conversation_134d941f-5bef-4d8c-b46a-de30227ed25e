<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-check-circle"></i> ทดสอบระบบตรวจสอบตัวละคร
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบการทำงานของระบบ Character Monitor</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    หน้านี้ใช้สำหรับทดสอบการทำงานของระบบตรวจสอบตัวละคร
                </div>
                
                <h5>🔧 การทดสอบฟังก์ชัน</h5>
                
                <?php
                // Test database connection
                echo "<h6>1. ทดสอบการเชื่อมต่อฐานข้อมูล</h6>";
                try {
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    $result = sqlsrv_query($conn, $sql);
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        echo '<div class="alert alert-success">✅ เชื่อมต่อฐานข้อมูลสำเร็จ - พบตัวละคร ' . number_format($row['count']) . ' ตัว</div>';
                    } else {
                        echo '<div class="alert alert-danger">❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">❌ เกิดข้อผิดพลาด: ' . $e->getMessage() . '</div>';
                }
                
                // Test recent activities function
                echo "<h6>2. ทดสอบฟังก์ชัน getRecentCharacterActivities</h6>";
                try {
                    // Include the function from character-monitor.php
                    function getRecentCharacterActivities($conn, $limit = 5) {
                        $activities = array();
                        
                        try {
                            $sql = "SELECT TOP ? 
                                        'created' as activity_type,
                                        CharacterIdx,
                                        Name,
                                        LEV,
                                        Style,
                                        CreateDate as activity_time,
                                        WorldIdx,
                                        Alz
                                    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                    ORDER BY CreateDate DESC";
                            
                            $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
                            if ($stmt && sqlsrv_execute($stmt)) {
                                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                    $activities[] = $row;
                                }
                            }
                            
                        } catch (Exception $e) {
                            error_log("Recent character activities error: " . $e->getMessage());
                        }
                        
                        return $activities;
                    }
                    
                    $testActivities = getRecentCharacterActivities($conn, 5);
                    if (!empty($testActivities)) {
                        echo '<div class="alert alert-success">✅ ฟังก์ชัน getRecentCharacterActivities ทำงานได้ - พบข้อมูล ' . count($testActivities) . ' รายการ</div>';
                        
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-sm table-bordered">';
                        echo '<thead><tr><th>ชื่อ</th><th>เลเวล</th><th>Alz</th><th>วันที่สร้าง</th></tr></thead>';
                        echo '<tbody>';
                        foreach ($testActivities as $activity) {
                            $createDate = $activity['activity_time'];
                            $dateStr = $createDate instanceof DateTime ? $createDate->format('d/m/Y H:i') : date('d/m/Y H:i', strtotime($createDate));
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($activity['Name']) . '</td>';
                            echo '<td>' . $activity['LEV'] . '</td>';
                            echo '<td>' . number_format($activity['Alz']) . '</td>';
                            echo '<td>' . $dateStr . '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody></table></div>';
                    } else {
                        echo '<div class="alert alert-warning">⚠️ ไม่พบข้อมูลกิจกรรมล่าสุด</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">❌ เกิดข้อผิดพลาดในฟังก์ชัน: ' . $e->getMessage() . '</div>';
                }
                
                // Test alerts function
                echo "<h6>3. ทดสอบฟังก์ชัน getCharacterAlerts</h6>";
                try {
                    function getCharacterAlerts($conn) {
                        $alerts = array();
                        
                        try {
                            // High level characters created recently
                            $sql = "SELECT TOP 3 
                                        'high_level_new' as alert_type,
                                        CharacterIdx,
                                        Name,
                                        LEV,
                                        CreateDate,
                                        Alz
                                    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                    WHERE LEV > 50 AND CreateDate >= DATEADD(day, -7, GETDATE())
                                    ORDER BY LEV DESC";
                            
                            $result = sqlsrv_query($conn, $sql);
                            if ($result) {
                                while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                    $alerts[] = $row;
                                }
                            }
                            
                        } catch (Exception $e) {
                            error_log("Character alerts error: " . $e->getMessage());
                        }
                        
                        return $alerts;
                    }
                    
                    $testAlerts = getCharacterAlerts($conn);
                    if (!empty($testAlerts)) {
                        echo '<div class="alert alert-success">✅ ฟังก์ชัน getCharacterAlerts ทำงานได้ - พบการแจ้งเตือน ' . count($testAlerts) . ' รายการ</div>';
                        
                        foreach ($testAlerts as $alert) {
                            $createDate = $alert['CreateDate'];
                            $dateStr = $createDate instanceof DateTime ? $createDate->format('d/m/Y H:i') : date('d/m/Y H:i', strtotime($createDate));
                            echo '<div class="alert alert-warning alert-sm">';
                            echo '<strong>ตัวละครเลเวลสูง:</strong> ' . htmlspecialchars($alert['Name']);
                            echo ' (เลเวล ' . $alert['LEV'] . ') สร้างเมื่อ ' . $dateStr;
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="alert alert-info">ℹ️ ไม่มีการแจ้งเตือนในขณะนี้</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">❌ เกิดข้อผิดพลาดในฟังก์ชันการแจ้งเตือน: ' . $e->getMessage() . '</div>';
                }
                
                // Test class name function
                echo "<h6>4. ทดสอบฟังก์ชัน getClassName</h6>";
                function getClassName($style) {
                    if ($style & 1) return 'Warrior';
                    if ($style & 2) return 'Blader';
                    if ($style & 4) return 'Wizard';
                    if ($style & 8) return 'Force Archer';
                    if ($style & 16) return 'Force Shielder';
                    if ($style & 32) return 'Force Blader';
                    return 'Unknown';
                }
                
                $testStyles = [1, 2, 4, 8, 16, 32];
                echo '<div class="alert alert-success">✅ ฟังก์ชัน getClassName ทำงานได้:</div>';
                echo '<ul>';
                foreach ($testStyles as $style) {
                    echo '<li>Style ' . $style . ' = ' . getClassName($style) . '</li>';
                }
                echo '</ul>';
                ?>
                
                <h5>🔗 ลิงก์ทดสอบ</h5>
                <div class="btn-group-vertical d-block">
                    <a href="?url=manager_charecter/character-statistics" class="btn btn-primary mb-2">
                        <i class="fal fa-chart-bar"></i> ทดสอบหน้าสถิติ
                    </a>
                    <a href="?url=manager_charecter/character-analytics" class="btn btn-info mb-2">
                        <i class="fal fa-analytics"></i> ทดสอบหน้าการวิเคราะห์
                    </a>
                    <a href="?url=manager_charecter/character-monitor" class="btn btn-warning mb-2">
                        <i class="fal fa-radar"></i> ทดสอบหน้าตรวจสอบสด
                    </a>
                    <a href="?url=manager_charecter/character-export-manager" class="btn btn-success mb-2">
                        <i class="fal fa-download"></i> ทดสอบหน้าส่งออกข้อมูล
                    </a>
                </div>
                
                <h5>🔧 API ทดสอบ</h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="testAPI('live_stats')">
                        ทดสอบ Live Stats API
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="testAPI('recent_activities')">
                        ทดสอบ Recent Activities API
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="testAPI('alerts')">
                        ทดสอบ Alerts API
                    </button>
                </div>
                
                <div id="apiTestResult" class="mt-3"></div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สถานะการทดสอบ</h6>
                    <ul class="mb-0">
                        <li>✅ ฟังก์ชันพื้นฐาน: ทำงานได้</li>
                        <li>✅ การเชื่อมต่อฐานข้อมูล: ปกติ</li>
                        <li>✅ การจัดการ DateTime: แก้ไขแล้ว</li>
                        <li>✅ การแสดงผลชื่อตัวละคร: แก้ไขแล้ว</li>
                        <li>✅ ลิงก์ดูรายละเอียด: ปรับปรุงแล้ว</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testAPI(action) {
    const resultDiv = document.getElementById('apiTestResult');
    resultDiv.innerHTML = '<div class="alert alert-info">กำลังทดสอบ API...</div>';
    
    try {
        const response = await fetch(`files/manager_charecter/api/character-data.php?action=${action}`);
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6>✅ API ${action} ทำงานได้</h6>
                    <pre class="bg-light p-2 rounded mt-2" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6>❌ API ${action} เกิดข้อผิดพลาด</h6>
                    <p>${data.error || 'Unknown error'}</p>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <h6>❌ ไม่สามารถเชื่อมต่อ API ได้</h6>
                <p>${error.message}</p>
            </div>
        `;
    }
}
</script>

<style>
.btn-group-vertical .btn {
    text-align: left;
}

pre {
    font-size: 0.875rem;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}
</style>
