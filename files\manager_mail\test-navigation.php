<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-check-circle"></i> ทดสอบการนำทาง Mail System
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบลิงก์ทั้งหมดในระบบ Mail</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    หน้านี้ใช้สำหรับทดสอบการนำทางในระบบ Mail ทั้งหมด
                </div>
                
                <h5>🆕 ระบบสถิติและการตรวจสอบ (ใหม่)</h5>
                <div class="btn-group-vertical d-block mb-4">
                    <a href="?url=manager_mail/mail-statistics" class="btn btn-primary mb-2">
                        <i class="fal fa-chart-bar"></i> สถิติ Mail
                    </a>
                    <a href="?url=manager_mail/mail-analytics" class="btn btn-info mb-2">
                        <i class="fal fa-analytics"></i> การวิเคราะห์
                    </a>
                    <a href="?url=manager_mail/mail-monitor" class="btn btn-warning mb-2">
                        <i class="fal fa-radar"></i> ตรวจสอบสด
                    </a>
                    <a href="?url=manager_mail/mail-export-manager" class="btn btn-success mb-2">
                        <i class="fal fa-download"></i> ส่งออกข้อมูล
                    </a>
                    <a href="?url=manager_mail/mail-settings" class="btn btn-secondary mb-2">
                        <i class="fal fa-cogs"></i> การตั้งค่า
                    </a>
                </div>
                
                <h5>📧 ระบบ Mail เดิม</h5>
                <div class="btn-group-vertical d-block mb-4">
                    <a href="?url=manager_mail/see-mailsend" class="btn btn-outline-primary mb-2">
                        <i class="fal fa-paper-plane"></i> การส่งเมลล์
                    </a>
                    <a href="?url=manager_mail/see-mailreceiver" class="btn btn-outline-success mb-2">
                        <i class="fal fa-inbox"></i> ตรวจสอบรับเมลล์
                    </a>
                    <a href="?url=manager_mail/see-mailsend-delete" class="btn btn-outline-danger mb-2">
                        <i class="fal fa-trash"></i> ประวัติการลบการส่งเมลล์
                    </a>
                    <a href="?url=manager_mail/see-mailreceiver-delete" class="btn btn-outline-warning mb-2">
                        <i class="fal fa-trash"></i> ประวัติการลบการรับเมลล์
                    </a>
                </div>
                
                <h5>🔗 API Endpoints</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>Endpoint</th>
                                <th>Description</th>
                                <th>Test</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>files/manager_mail/api/mail-data.php?action=live_stats</code></td>
                                <td>สถิติสด</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('live_stats')">ทดสอบ</button></td>
                            </tr>
                            <tr>
                                <td><code>files/manager_mail/api/mail-data.php?action=recent_activities</code></td>
                                <td>กิจกรรมล่าสุด</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('recent_activities')">ทดสอบ</button></td>
                            </tr>
                            <tr>
                                <td><code>files/manager_mail/api/mail-data.php?action=alerts</code></td>
                                <td>การแจ้งเตือน</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('alerts')">ทดสอบ</button></td>
                            </tr>
                            <tr>
                                <td><code>files/manager_mail/api/mail-data.php?action=hourly_stats</code></td>
                                <td>สถิติรายชั่วโมง</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('hourly_stats')">ทดสอบ</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5>📁 Export Endpoints</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>Export Type</th>
                                <th>Format</th>
                                <th>Test Link</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>สถิติ 7 วันล่าสุด</td>
                                <td>CSV</td>
                                <td>
                                    <a href="files/manager_mail/export/mail-export.php?type=csv&data=statistics&date_from=<?php echo date('Y-m-d', strtotime('-7 days')); ?>&date_to=<?php echo date('Y-m-d'); ?>" 
                                       class="btn btn-sm btn-success" target="_blank">ดาวน์โหลด</a>
                                </td>
                            </tr>
                            <tr>
                                <td>เมลล์รับวันนี้</td>
                                <td>JSON</td>
                                <td>
                                    <a href="files/manager_mail/export/mail-export.php?type=json&data=received_mails&date_from=<?php echo date('Y-m-d'); ?>&date_to=<?php echo date('Y-m-d'); ?>" 
                                       class="btn btn-sm btn-warning" target="_blank">ดาวน์โหลด</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สถานะระบบ</h6>
                    <ul class="mb-0">
                        <li>✅ URL Routing: ใช้ <code>?url=</code> แทน <code>?page=</code></li>
                        <li>✅ Navigation Menu: เพิ่มเมนูใหม่ในระบบ Mail</li>
                        <li>✅ Error Handling: แก้ไข undefined variable warnings</li>
                        <li>✅ API Endpoints: พร้อมใช้งาน</li>
                        <li>✅ Export System: พร้อมใช้งาน</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Test Results Modal -->
<div class="modal fade" id="apiTestModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ผลการทดสอบ API</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="apiTestResult">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">กำลังทดสอบ...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>

<script>
async function testAPI(action) {
    $('#apiTestModal').modal('show');
    
    try {
        const response = await fetch(`files/manager_mail/api/mail-data.php?action=${action}`);
        const data = await response.json();
        
        document.getElementById('apiTestResult').innerHTML = `
            <div class="alert alert-${data.success ? 'success' : 'danger'}">
                <h6>Status: ${data.success ? 'Success' : 'Error'}</h6>
            </div>
            <pre class="bg-light p-3 rounded">${JSON.stringify(data, null, 2)}</pre>
        `;
    } catch (error) {
        document.getElementById('apiTestResult').innerHTML = `
            <div class="alert alert-danger">
                <h6>Error</h6>
                <p>${error.message}</p>
            </div>
        `;
    }
}

// Test all navigation links on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mail System Navigation Test Page Loaded');
    console.log('All links are using ?url= routing');
});
</script>

<style>
.btn-group-vertical .btn {
    text-align: left;
}

pre {
    max-height: 400px;
    overflow-y: auto;
}

.table code {
    font-size: 0.875rem;
    color: #e83e8c;
}
</style>
