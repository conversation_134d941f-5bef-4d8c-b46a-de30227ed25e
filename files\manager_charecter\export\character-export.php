<?php
require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

// Simple authentication check
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$export_type = $_GET['type'] ?? 'csv';
$data_type = $_GET['data'] ?? 'character_list';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$level_min = (int)($_GET['level_min'] ?? 1);
$level_max = (int)($_GET['level_max'] ?? 999);
$limit = (int)($_GET['limit'] ?? 1000);
$include_details = $_GET['include_details'] ?? '1';

try {
    switch ($data_type) {
        case 'character_list':
            exportCharacterList($conn, $export_type, $date_from, $date_to, $level_min, $level_max, $limit, $include_details);
            break;
            
        case 'character_stats':
            exportCharacterStats($conn, $export_type);
            break;
            
        case 'online_players':
            exportOnlinePlayers($conn, $export_type, $level_min, $level_max, $limit, $include_details);
            break;
            
        case 'new_characters':
            exportNewCharacters($conn, $export_type, $date_from, $date_to, $limit, $include_details);
            break;
            
        case 'top_players':
            exportTopPlayers($conn, $export_type, $limit, $include_details);
            break;
            
        case 'class_distribution':
            exportClassDistribution($conn, $export_type);
            break;
            
        default:
            throw new Exception('Invalid data type');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo "Error: " . $e->getMessage();
}

function exportCharacterList($conn, $format, $date_from, $date_to, $level_min, $level_max, $limit, $include_details) {
    $data = [];
    
    $limitClause = $limit > 0 ? "TOP $limit" : "";
    $detailsFields = $include_details == '1' ? ", Alz, PlayTime, WorldIdx, ChannelIdx" : "";
    
    $sql = "SELECT $limitClause 
                CharacterIdx,
                Name,
                LEV,
                Style,
                CreateDate,
                LoginTime
                $detailsFields
            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
            WHERE LEV BETWEEN ? AND ?
            AND CreateDate BETWEEN ? AND ?
            ORDER BY LEV DESC, CreateDate DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$level_min, &$level_max, &$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $exportRow = [
                'CharacterIdx' => $row['CharacterIdx'],
                'Name' => $row['Name'],
                'Level' => $row['LEV'],
                'Class' => getClassName($row['Style']),
                'Create Date' => $row['CreateDate']->format('Y-m-d H:i:s'),
                'Last Login' => $row['LoginTime'] ? $row['LoginTime']->format('Y-m-d H:i:s') : 'Never'
            ];
            
            if ($include_details == '1') {
                $exportRow['Alz'] = $row['Alz'];
                $exportRow['Playtime (Hours)'] = round($row['PlayTime'] / 3600, 2);
                $exportRow['World'] = $row['WorldIdx'];
                $exportRow['Channel'] = $row['ChannelIdx'];
                $exportRow['Status'] = $row['ChannelIdx'] > 0 ? 'Online' : 'Offline';
            }
            
            $data[] = $exportRow;
        }
    }
    
    outputData($data, $format, 'character_list_' . $date_from . '_to_' . $date_to);
}

function exportCharacterStats($conn, $format) {
    $data = [];
    
    // Total characters
    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = ['Metric' => 'Total Characters', 'Value' => $row['count'], 'Description' => 'จำนวนตัวละครทั้งหมด'];
    }
    
    // Online characters
    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE ChannelIdx > 0";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = ['Metric' => 'Online Characters', 'Value' => $row['count'], 'Description' => 'ตัวละครที่ออนไลน์'];
    }
    
    // Average level
    $sql = "SELECT AVG(CAST(LEV AS FLOAT)) as avg_level FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE LEV > 0";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = ['Metric' => 'Average Level', 'Value' => round($row['avg_level'], 2), 'Description' => 'เลเวลเฉลี่ย'];
    }
    
    // Total Alz
    $sql = "SELECT SUM(CAST(Alz AS BIGINT)) as total_alz FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = ['Metric' => 'Total Alz', 'Value' => $row['total_alz'], 'Description' => 'Alz รวมทั้งหมด'];
    }
    
    // Total playtime
    $sql = "SELECT SUM(PlayTime) as total_playtime FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = ['Metric' => 'Total Playtime (Hours)', 'Value' => round($row['total_playtime'] / 3600, 2), 'Description' => 'เวลาเล่นรวม (ชั่วโมง)'];
    }
    
    outputData($data, $format, 'character_statistics_' . date('Y-m-d'));
}

function exportOnlinePlayers($conn, $format, $level_min, $level_max, $limit, $include_details) {
    $data = [];
    
    $limitClause = $limit > 0 ? "TOP $limit" : "";
    $detailsFields = $include_details == '1' ? ", Alz, PlayTime" : "";
    
    $sql = "SELECT $limitClause 
                CharacterIdx,
                Name,
                LEV,
                Style,
                WorldIdx,
                ChannelIdx,
                LoginTime
                $detailsFields
            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
            WHERE ChannelIdx > 0
            AND LEV BETWEEN ? AND ?
            ORDER BY LEV DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$level_min, &$level_max));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $exportRow = [
                'CharacterIdx' => $row['CharacterIdx'],
                'Name' => $row['Name'],
                'Level' => $row['LEV'],
                'Class' => getClassName($row['Style']),
                'World' => $row['WorldIdx'],
                'Channel' => $row['ChannelIdx'],
                'Login Time' => $row['LoginTime'] ? $row['LoginTime']->format('Y-m-d H:i:s') : 'Unknown'
            ];
            
            if ($include_details == '1') {
                $exportRow['Alz'] = $row['Alz'];
                $exportRow['Playtime (Hours)'] = round($row['PlayTime'] / 3600, 2);
            }
            
            $data[] = $exportRow;
        }
    }
    
    outputData($data, $format, 'online_players_' . date('Y-m-d_H-i'));
}

function exportNewCharacters($conn, $format, $date_from, $date_to, $limit, $include_details) {
    $data = [];
    
    $limitClause = $limit > 0 ? "TOP $limit" : "";
    $detailsFields = $include_details == '1' ? ", Alz, WorldIdx" : "";
    
    $sql = "SELECT $limitClause 
                CharacterIdx,
                Name,
                LEV,
                Style,
                CreateDate
                $detailsFields
            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
            WHERE CreateDate BETWEEN ? AND ?
            ORDER BY CreateDate DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $exportRow = [
                'CharacterIdx' => $row['CharacterIdx'],
                'Name' => $row['Name'],
                'Level' => $row['LEV'],
                'Class' => getClassName($row['Style']),
                'Create Date' => $row['CreateDate']->format('Y-m-d H:i:s')
            ];
            
            if ($include_details == '1') {
                $exportRow['Alz'] = $row['Alz'];
                $exportRow['World'] = $row['WorldIdx'];
            }
            
            $data[] = $exportRow;
        }
    }
    
    outputData($data, $format, 'new_characters_' . $date_from . '_to_' . $date_to);
}

function exportTopPlayers($conn, $format, $limit, $include_details) {
    $data = [];
    
    $limitClause = $limit > 0 ? "TOP $limit" : "TOP 100";
    $detailsFields = $include_details == '1' ? ", CreateDate, WorldIdx, ChannelIdx" : "";
    
    $sql = "SELECT $limitClause 
                CharacterIdx,
                Name,
                LEV,
                Style,
                Alz,
                PlayTime
                $detailsFields
            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
            ORDER BY LEV DESC, Alz DESC";
    
    $result = sqlsrv_query($conn, $sql);
    if ($result) {
        $rank = 1;
        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $exportRow = [
                'Rank' => $rank++,
                'CharacterIdx' => $row['CharacterIdx'],
                'Name' => $row['Name'],
                'Level' => $row['LEV'],
                'Class' => getClassName($row['Style']),
                'Alz' => $row['Alz'],
                'Playtime (Hours)' => round($row['PlayTime'] / 3600, 2)
            ];
            
            if ($include_details == '1') {
                $exportRow['Create Date'] = $row['CreateDate'] ? $row['CreateDate']->format('Y-m-d H:i:s') : 'Unknown';
                $exportRow['World'] = $row['WorldIdx'];
                $exportRow['Status'] = $row['ChannelIdx'] > 0 ? 'Online' : 'Offline';
            }
            
            $data[] = $exportRow;
        }
    }
    
    outputData($data, $format, 'top_players_' . date('Y-m-d'));
}

function exportClassDistribution($conn, $format) {
    $data = [];

    $sql = "SELECT
                Style,
                COUNT(*) as total_count,
                COUNT(CASE WHEN ChannelIdx > 0 THEN 1 END) as online_count,
                AVG(CAST(LEV AS FLOAT)) as avg_level,
                SUM(CAST(Alz AS BIGINT)) as total_alz
            FROM [".DATABASE_SV."].[dbo].cabal_character_table
            GROUP BY Style
            ORDER BY total_count DESC";

    $result = sqlsrv_query($conn, $sql);
    $classDistribution = array();
    $total_characters = 0;

    if ($result) {
        // First pass - group by class name
        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $className = getClassName($row['Style']);

            if (isset($classDistribution[$className])) {
                $classDistribution[$className]['total_count'] += $row['total_count'];
                $classDistribution[$className]['online_count'] += $row['online_count'];
                $classDistribution[$className]['total_alz'] += $row['total_alz'];
                // Average level calculation (weighted average would be more accurate)
                $classDistribution[$className]['avg_level'] = ($classDistribution[$className]['avg_level'] + $row['avg_level']) / 2;
            } else {
                $classDistribution[$className] = [
                    'class_name' => $className,
                    'total_count' => $row['total_count'],
                    'online_count' => $row['online_count'],
                    'avg_level' => $row['avg_level'],
                    'total_alz' => $row['total_alz']
                ];
            }

            $total_characters += $row['total_count'];
        }

        // Second pass - calculate percentages and format data
        foreach ($classDistribution as $row) {
            $percentage = $total_characters > 0 ? ($row['total_count'] / $total_characters) * 100 : 0;

            $data[] = [
                'Class' => $row['class_name'],
                'Total Count' => $row['total_count'],
                'Online Count' => $row['online_count'],
                'Percentage' => round($percentage, 2) . '%',
                'Average Level' => round($row['avg_level'] ?? 0, 2),
                'Total Alz' => $row['total_alz']
            ];
        }

        // Sort by total count
        usort($data, function($a, $b) {
            return $b['Total Count'] - $a['Total Count'];
        });
    }

    outputData($data, $format, 'class_distribution_' . date('Y-m-d'));
}

function getClassName($style) {
    // คำนวณคลาสแบบเดียวกับ PHP cabalstyle function
    $battleStyle = $style & 7; // 3 บิตแรก
    $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
    $classIndex = $battleStyle | ($extendedBattleStyle << 3);

    $classNames = [
        1 => 'Warrior',
        2 => 'Blader',
        3 => 'Wizard',
        4 => 'Force Archer',
        5 => 'Force Shielder',
        6 => 'Force Blader',
        7 => 'Gladiator',
        8 => 'Force Gunner',
        9 => 'Dark Mage'
    ];

    return $classNames[$classIndex] ?? 'Unknown';
}

function outputData($data, $format, $filename) {
    if (empty($data)) {
        throw new Exception('No data found for the specified criteria');
    }
    
    switch ($format) {
        case 'csv':
            outputCSV($data, $filename);
            break;
            
        case 'json':
            outputJSON($data, $filename);
            break;
            
        case 'excel':
            outputExcel($data, $filename);
            break;
            
        default:
            throw new Exception('Invalid export format');
    }
}

function outputCSV($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    if (!empty($data)) {
        fputcsv($output, array_keys($data[0]));
        
        // Data rows
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
}

function outputJSON($data, $filename) {
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    echo json_encode([
        'export_date' => date('Y-m-d H:i:s'),
        'filename' => $filename,
        'total_records' => count($data),
        'data' => $data
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function outputExcel($data, $filename) {
    // For Excel export, we'll use HTML table format that Excel can read
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    echo '<html><head><meta charset="utf-8"></head><body>';
    echo '<table border="1">';
    
    if (!empty($data)) {
        // Headers
        echo '<tr>';
        foreach (array_keys($data[0]) as $header) {
            echo '<th>' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr>';
        
        // Data rows
        foreach ($data as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . htmlspecialchars($cell) . '</td>';
            }
            echo '</tr>';
        }
    }
    
    echo '</table>';
    echo '</body></html>';
}
?>
