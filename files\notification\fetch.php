<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

if(isset($_POST['view'])){

$params = array();
$options = array( "Scrollable" => SQLSRV_CURSOR_KEYSET );

$query = "SELECT TOP 10 * FROM [".DATABASE_WEB."].[dbo].WEB_Voucher_chack WHERE Voucher_status = 0 ORDER BY idx DESC";
$result = sqlsrv_query($conn, $query, $params, $options);

$output = '';
if(sqlsrv_num_rows($result) > 0)
{
 while($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC))
 {
   $output .= '
   <li class="unread">
   <a href="?url=game_manager/manage-voucher-item" class="d-flex align-items-center">
       <span class="status status-success d-inline-block mr-2">

               <img src="assets/img/demo/avatars/avatar-c.png" class="profile-image rounded-circle" alt="...">
       </span>
       <span class="d-flex flex-column flex-1 ml-1">
           <span class="name">'.$row["ID"].'<span
                   class="badge badge-primary fw-n position-absolute pos-top pos-right mt-1">'.$row["Voucher_price"].'</span></span>
           <span class="msg-a fs-sm">แจ้งเตือนการแลกบัตรแคส</span>
           <span class="msg-b fs-xs">สวัสดี GM กดบัตรแคสให้ผมด้วยถ้าช้าเดียวตบ...</span>
           <span class="fs-nano text-muted mt-1">เวลา '.$row["added_time"].'</span>
       </span>
   </a>
</li>
 
   ';

 }
}
else{
     $output .= '
     <li><a href="#" class="text-bold text-italic">ไม่มี แจ้งเตือนใหม่</a></li>';
    
}

$status_query = "SELECT * FROM [".DATABASE_WEB."].[dbo].WEB_Voucher_chack WHERE Voucher_status = 0";
$result = sqlsrv_query($conn, $status_query, $params, $options);
$count = sqlsrv_num_rows($result);
$data = array(
    'notification' => $output,
    'unseen_notification'  => $count
);

echo json_encode($data);

}

?>