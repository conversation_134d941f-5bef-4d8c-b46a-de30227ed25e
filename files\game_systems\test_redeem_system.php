<?php
// ไฟล์ทดสอบระบบ Redeem Code Generator
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

echo "<h2>ทดสอบระบบ Redeem Code Generator</h2>";

// ทดสอบการเชื่อมต่อฐานข้อมูล
if ($conn) {
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>";
    exit;
}

// ทดสอบการตรวจสอบ table WEB_Redeem_Code
echo "<h3>ทดสอบการตรวจสอบ Table WEB_Redeem_Code:</h3>";
$checkTableSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_Redeem_Code'";
$checkStmt = sqlsrv_query($conn, $checkTableSql);

if ($checkStmt) {
    $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
    if ($result['count'] > 0) {
        echo "<p style='color: green;'>✅ Table WEB_Redeem_Code มีอยู่แล้ว</p>";
        
        // ตรวจสอบ columns
        $columnsSql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'WEB_Redeem_Code'";
        $columnsStmt = sqlsrv_query($conn, $columnsSql);
        
        echo "<p><strong>Columns ใน table:</strong></p><ul>";
        while ($col = sqlsrv_fetch_array($columnsStmt, SQLSRV_FETCH_ASSOC)) {
            echo "<li>" . $col['COLUMN_NAME'] . "</li>";
        }
        echo "</ul>";
        
        // ตรวจสอบข้อมูลที่มีอยู่
        $dataSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code";
        $dataStmt = sqlsrv_query($conn, $dataSql);
        if ($dataStmt) {
            $dataResult = sqlsrv_fetch_array($dataStmt, SQLSRV_FETCH_ASSOC);
            echo "<p>📊 มีข้อมูล " . $dataResult['count'] . " แถว</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ Table WEB_Redeem_Code ยังไม่มี (จะสร้างอัตโนมัติเมื่อใช้งาน)</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบ table ได้</p>";
}

// ทดสอบการสร้าง table (ถ้าไม่มี)
echo "<h3>ทดสอบการสร้าง Table:</h3>";
function createTableIfNotExists($conn) {
    $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_Redeem_Code'";
    $checkStmt = sqlsrv_query($conn, $checkSql);
    
    if ($checkStmt) {
        $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($result['count'] == 0) {
            $createSql = "CREATE TABLE [dbo].[WEB_Redeem_Code] (
                [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
                [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
                [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
                [quantity] int NULL,
                [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
                [datecreated] datetime DEFAULT getdate() NULL,
                [expiry_date] datetime NULL
            )";
            
            $createStmt = sqlsrv_query($conn, $createSql);
            if ($createStmt) {
                echo "<p style='color: green;'>✅ สร้าง table WEB_Redeem_Code สำเร็จ</p>";
                return true;
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถสร้าง table ได้</p>";
                return false;
            }
        } else {
            // ตรวจสอบ column expiry_date
            $checkColumnSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'WEB_Redeem_Code' AND COLUMN_NAME = 'expiry_date'";
            $checkColumnStmt = sqlsrv_query($conn, $checkColumnSql);
            
            if ($checkColumnStmt) {
                $columnResult = sqlsrv_fetch_array($checkColumnStmt, SQLSRV_FETCH_ASSOC);
                if ($columnResult['count'] == 0) {
                    $addColumnSql = "ALTER TABLE [dbo].[WEB_Redeem_Code] ADD [expiry_date] datetime NULL";
                    $addColumnStmt = sqlsrv_query($conn, $addColumnSql);
                    if ($addColumnStmt) {
                        echo "<p style='color: green;'>✅ เพิ่ม column expiry_date สำเร็จ</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ ไม่สามารถเพิ่ม column expiry_date ได้</p>";
                    }
                } else {
                    echo "<p style='color: green;'>✅ Table พร้อมใช้งาน (มี column expiry_date แล้ว)</p>";
                }
            }
            return true;
        }
    }
    return false;
}

createTableIfNotExists($conn);

// ทดสอบการสร้าง code
echo "<h3>ทดสอบการสร้าง Code:</h3>";

function generateCodeByFormat($format) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

$formats = [
    'XXXX-XXXX-XXXX-XXXX',
    'XXXXXXXX-XXXX-XXXX',
    'XXXXXXXXXXXXXXXXXXXX'
];

foreach ($formats as $format) {
    $testCode = generateCodeByFormat($format);
    echo "<p>📝 รูปแบบ: <code>$format</code> → ผลลัพธ์: <code style='color: blue;'>$testCode</code></p>";
}

// ทดสอบการตรวจสอบรูปแบบไอเท็ม
echo "<h3>ทดสอบการตรวจสอบรูปแบบไอเท็ม:</h3>";

$testItems = [
    '1:0:31' => true,
    '10:0:31,1214:0:31' => true,
    '1:0:31,10:0:31,1214:0:31' => true,
    'invalid' => false,
    '1:0' => false,
    '1:0:31:extra' => false,
    'abc:0:31' => false
];

foreach ($testItems as $item => $expected) {
    $itemList = explode(',', $item);
    $valid = true;
    
    foreach ($itemList as $singleItem) {
        $singleItem = trim($singleItem);
        if (!preg_match('/^\d+:\d+:\d+$/', $singleItem)) {
            $valid = false;
            break;
        }
    }
    
    $result = $valid ? '✅' : '❌';
    $status = ($valid === $expected) ? 'ถูกต้อง' : 'ผิดพลาด';
    echo "<p>$result <code>$item</code> → $status</p>";
}

// ทดสอบการเพิ่มข้อมูลจริง
echo "<h3>ทดสอบการเพิ่มข้อมูลจริง:</h3>";

$testCode = generateCodeByFormat('TEST-XXXX-XXXX');
$testItems = '1:0:31,10:0:31';
$testQuantity = 1;

$insertSql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated) VALUES (?, ?, ?, '0', GETDATE())";
$insertStmt = sqlsrv_query($conn, $insertSql, [$testCode, $testItems, $testQuantity]);

if ($insertStmt) {
    echo "<p style='color: green;'>✅ เพิ่มข้อมูลทดสอบสำเร็จ: <code>$testCode</code></p>";
    
    // ทดสอบการดึงข้อมูล
    $selectSql = "SELECT * FROM WEB_Redeem_Code WHERE code = ?";
    $selectStmt = sqlsrv_query($conn, $selectSql, [$testCode]);
    
    if ($selectStmt && $row = sqlsrv_fetch_array($selectStmt, SQLSRV_FETCH_ASSOC)) {
        echo "<p>📋 ข้อมูลที่เพิ่ม:</p>";
        echo "<ul>";
        echo "<li>ID: " . $row['id'] . "</li>";
        echo "<li>Code: " . $row['code'] . "</li>";
        echo "<li>Items: " . $row['items'] . "</li>";
        echo "<li>Quantity: " . $row['quantity'] . "</li>";
        echo "<li>Status: " . $row['status'] . "</li>";
        echo "</ul>";
        
        // ลบข้อมูลทดสอบ
        $deleteSql = "DELETE FROM WEB_Redeem_Code WHERE id = ?";
        $deleteStmt = sqlsrv_query($conn, $deleteSql, [$row['id']]);
        if ($deleteStmt) {
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มข้อมูลทดสอบได้</p>";
    echo "<p>Error: " . print_r(sqlsrv_errors(), true) . "</p>";
}

echo "<h3>สรุปผลการทดสอบ:</h3>";
echo "<p>✅ ระบบพร้อมใช้งาน</p>";
echo "<p>📝 คุณสามารถเข้าใช้งานระบบได้ที่: <a href='redeem_code_generator.php'>Redeem Code Generator</a></p>";
echo "<p>🔗 หรือผ่านเมนู: จัดการระบบเกมส์ > สร้าง Redeem Code อัตโนมัติ</p>";

echo "<h3>ตัวอย่างการใช้งาน:</h3>";
echo "<ol>";
echo "<li>เลือกจำนวน Code ที่ต้องการสร้าง</li>";
echo "<li>กรอกรายการไอเท็ม เช่น: <code>1:0:31,10:0:31,1214:0:31</code></li>";
echo "<li>กำหนด Quantity (จำนวนไอเท็มต่อ Code)</li>";
echo "<li>เลือกรูปแบบ Code</li>";
echo "<li>กำหนดวันหมดอายุ (ถ้าต้องการ)</li>";
echo "<li>กดปุ่มสร้าง Code</li>";
echo "</ol>";
?>
