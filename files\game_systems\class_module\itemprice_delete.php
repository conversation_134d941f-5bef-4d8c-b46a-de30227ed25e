<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

header('Content-Type: application/json');

$data = json_decode(file_get_contents("php://input"), true);

if (!$data || !isset($data['ID'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input: ID missing']);
    exit;
}

$id = (int)$data['ID'];

// คำสั่ง DELETE
$sql = "DELETE FROM EventData.dbo.cabal_ems_event_npcitemshop_itemprice_table WHERE ID = ?";

$params = [$id];

$stmt = sqlsrv_query($conn, $sql, $params);

if ($stmt === false) {
    http_response_code(500);
    echo json_encode([
        'error' => 'SQL delete failed',
        'details' => sqlsrv_errors()
    ]);
    exit;
}

// ตรวจสอบว่ามีแถวถูกลบหรือไม่
$rowsAffected = sqlsrv_rows_affected($stmt);
if ($rowsAffected === false) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to get affected rows',
        'details' => sqlsrv_errors()
    ]);
    exit;
}

if ($rowsAffected === 0) {
    http_response_code(404);
    echo json_encode(['error' => 'No record found with the specified ID']);
    exit;
}

echo json_encode(['status' => 'deleted', 'ID' => $id]);
