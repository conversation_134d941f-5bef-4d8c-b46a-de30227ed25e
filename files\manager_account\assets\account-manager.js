/**
 * Account Manager Enhanced JavaScript
 * Provides enhanced functionality and user experience
 */

class AccountManager {
    constructor() {
        this.init();
        this.bindEvents();
        this.setupNotifications();
    }

    init() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize animations
        this.initAnimations();
        
        // Setup auto-refresh
        this.setupAutoRefresh();
        
        // Initialize search functionality
        this.initSearch();
        
        console.log('Account Manager initialized successfully');
    }

    bindEvents() {
        // Bind button click events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-interactive')) {
                this.handleButtonClick(e);
            }
        });

        // Bind form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('form-enhanced')) {
                this.handleFormSubmit(e);
            }
        });

        // Bind table row clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.table-enhanced tbody tr')) {
                this.handleTableRowClick(e);
            }
        });
    }

    initTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof $().tooltip === 'function') {
            $('[data-toggle="tooltip"]').tooltip();
        }
    }

    initAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe all cards and sections
        document.querySelectorAll('.enhanced-card, .fade-in, .fade-in-up').forEach(el => {
            observer.observe(el);
        });
    }

    setupAutoRefresh() {
        // Auto-refresh data every 30 seconds
        setInterval(() => {
            this.refreshData();
        }, 30000);
    }

    initSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });
        }
    }

    setupNotifications() {
        // Setup notification system
        this.notifications = [];
        this.createNotificationContainer();
    }

    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification-toast ${type}`;
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fal fa-${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="mb-0">${message}</p>
                </div>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fal fa-times"></i>
                </button>
            </div>
        `;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideInFromRight 0.5s ease-out reverse';
                setTimeout(() => notification.remove(), 500);
            }
        }, duration);

        return notification;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    handleButtonClick(e) {
        const button = e.target;
        
        // Add loading state
        this.setButtonLoading(button, true);
        
        // Simulate action delay
        setTimeout(() => {
            this.setButtonLoading(button, false);
        }, 1000);
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = `<span class="loading-spinner mr-2"></span>กำลังดำเนินการ...`;
        } else {
            button.disabled = false;
            // Restore original text (you might want to store this)
            button.innerHTML = button.getAttribute('data-original-text') || 'ดำเนินการ';
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        
        // Validate form
        if (!this.validateForm(form)) {
            this.showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
            return;
        }

        // Show loading
        const submitBtn = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);

        // Simulate form submission
        setTimeout(() => {
            this.setButtonLoading(submitBtn, false);
            this.showNotification('บันทึกข้อมูลเรียบร้อยแล้ว', 'success');
        }, 2000);
    }

    validateForm(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    handleTableRowClick(e) {
        const row = e.target.closest('tr');
        if (row) {
            // Add selection effect
            document.querySelectorAll('.table-enhanced tbody tr').forEach(r => {
                r.classList.remove('table-active');
            });
            row.classList.add('table-active');
        }
    }

    performSearch(query) {
        if (window.accountTable && typeof window.accountTable.search === 'function') {
            window.accountTable.search(query).draw();
        }
    }

    refreshData() {
        if (window.accountTable && typeof window.accountTable.ajax === 'object') {
            window.accountTable.ajax.reload(null, false);
        }
    }

    // Utility functions
    formatNumber(num) {
        return new Intl.NumberFormat('th-TH').format(num);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('th-TH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('คัดลอกไปยังคลิปบอร์ดแล้ว', 'success', 2000);
        }).catch(() => {
            this.showNotification('ไม่สามารถคัดลอกได้', 'error');
        });
    }

    exportTableData(format = 'csv') {
        if (window.accountTable) {
            const data = window.accountTable.data().toArray();
            
            if (format === 'csv') {
                this.exportToCSV(data);
            } else if (format === 'json') {
                this.exportToJSON(data);
            }
        }
    }

    exportToCSV(data) {
        const headers = ['UserNum', 'ID', 'Email', 'LoginTime', 'LogoutTime', 'LastIp', 'PlayTime', 'Login', 'AuthType'];
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        this.downloadFile(csvContent, 'account_data.csv', 'text/csv');
    }

    exportToJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, 'account_data.json', 'application/json');
    }

    downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        this.showNotification(`ดาวน์โหลด ${filename} เรียบร้อยแล้ว`, 'success');
    }

    // Modal helpers
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal && typeof $(modal).modal === 'function') {
            $(modal).modal('show');
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal && typeof $(modal).modal === 'function') {
            $(modal).modal('hide');
        }
    }

    // Confirmation dialogs
    confirmAction(message, callback) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'ยืนยันการดำเนินการ',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'ยืนยัน',
                cancelButtonText: 'ยกเลิก',
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed && typeof callback === 'function') {
                    callback();
                }
            });
        } else {
            if (confirm(message) && typeof callback === 'function') {
                callback();
            }
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.accountManager = new AccountManager();
});

// Global utility functions
window.showNotification = (message, type, duration) => {
    if (window.accountManager) {
        window.accountManager.showNotification(message, type, duration);
    }
};

window.confirmAction = (message, callback) => {
    if (window.accountManager) {
        window.accountManager.confirmAction(message, callback);
    }
};
