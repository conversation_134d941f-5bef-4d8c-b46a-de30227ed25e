<?php $user->restrictionUser(true, $conn); ?>

<?php
// Function to get recent character activities
function getRecentCharacterActivities($conn, $limit = 50) {
    $activities = array();

    try {
        // ตรวจสอบการเชื่อมต่อฐานข้อมูล
        if (!$conn) {
            error_log("Database connection is null in getRecentCharacterActivities");
            return $activities;
        }

        // Recent character creations
        $sql = "SELECT TOP ?
                    'created' as activity_type,
                    CharacterIdx,
                    Name,
                    LEV,
                    Style,
                    CreateDate as activity_time,
                    WorldIdx,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                ORDER BY CreateDate DESC";

        $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $activities[] = $row;
            }
        } else {
            error_log("Recent activities query failed: " . print_r(sqlsrv_errors(), true));
        }

    } catch (Exception $e) {
        error_log("Recent character activities error: " . $e->getMessage());
    }

    return $activities;
}

// Function to get character alerts (suspicious activities)
function getCharacterAlerts($conn) {
    $alerts = array();
    
    try {
        // High level characters created recently
        $sql = "SELECT TOP 5 
                    'high_level_new' as alert_type,
                    CharacterIdx,
                    Name,
                    LEV,
                    CreateDate,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LEV > 100 AND CreateDate >= DATEADD(hour, -24, GETDATE())
                ORDER BY LEV DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = $row;
            }
        }
        
        // Characters with excessive Alz
        $sql = "SELECT TOP 5 
                    'high_alz' as alert_type,
                    CharacterIdx,
                    Name,
                    LEV,
                    Alz,
                    CreateDate
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE Alz > *********
                ORDER BY Alz DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = $row;
            }
        }
        
        // Multiple characters from same account created recently
        $sql = "SELECT 
                    'multiple_chars' as alert_type,
                    FLOOR(CharacterIdx / 16) as account_id,
                    COUNT(*) as char_count,
                    MAX(CreateDate) as latest_create
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CreateDate >= DATEADD(hour, -24, GETDATE())
                GROUP BY FLOOR(CharacterIdx / 16)
                HAVING COUNT(*) > 3
                ORDER BY char_count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = $row;
            }
        }
        
    } catch (Exception $e) {
        error_log("Character alerts error: " . $e->getMessage());
    }
    
    return $alerts;
}

$recentActivities = getRecentCharacterActivities($conn, 30);
$alerts = getCharacterAlerts($conn);

// Debug information
if (isset($_GET['debug'])) {
    echo "<div class='alert alert-info'>";
    echo "<h6>Debug Information:</h6>";
    echo "Recent Activities Count: " . count($recentActivities) . "<br>";
    echo "Alerts Count: " . count($alerts) . "<br>";
    if (!empty($recentActivities)) {
        echo "First Activity: " . print_r($recentActivities[0], true) . "<br>";
    }
    echo "</div>";
}

// Function to get class name from style
function getClassName($style) {
    global $userLogin;

    // ใช้ฟังก์ชัน cabalstyle หรือฟังก์ชันสำรอง
    if ($userLogin && method_exists($userLogin, 'cabalstyle')) {
        $classInfo = $userLogin->cabalstyle($style);
        $className = $classInfo['Class_Name'] ?? 'Unknown';
    } else {
        // ฟังก์ชันสำรองกรณีไม่มี userLogin
        $className = getClassNameFromStyleBackup($style);
    }

    // แปลงชื่อคลาสจากรหัสเป็นชื่อเต็ม
    $fullClassNames = [
        'WA' => 'Warrior',
        'BL' => 'Blader',
        'WI' => 'Wizard',
        'FA' => 'Force Archer',
        'FS' => 'Force Shielder',
        'FB' => 'Force Blader',
        'GL' => 'Gladiator',
        'FG' => 'Force Gunner',
        'DM' => 'Dark Mage'
    ];

    return $fullClassNames[$className] ?? $className;
}

// ฟังก์ชันสำรองสำหรับคำนวณคลาสจาก Style
function getClassNameFromStyleBackup($style) {
    $battleStyle = $style & 7; // 3 บิตแรก
    $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
    $classIndex = $battleStyle | ($extendedBattleStyle << 3);

    $classNames = [
        1 => 'WA', // Warrior
        2 => 'BL', // Blader
        3 => 'WI', // Wizard
        4 => 'FA', // Force Archer
        5 => 'FS', // Force Shielder
        6 => 'FB', // Force Blader
        7 => 'GL', // Gladiator
        8 => 'FG', // Force Gunner
        9 => 'DM'  // Dark Mage
    ];

    return $classNames[$classIndex] ?? 'Unknown';
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-radar"></i> ตรวจสอบความเคลื่อนไหวตัวละคร
    </h1>
    <div class="subheader-block">
        <div class="badge badge-success" id="statusBadge">
            <i class="fal fa-circle"></i> Online
        </div>
        <button class="btn btn-primary btn-sm" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
            <i class="fal fa-play"></i> เริ่มอัพเดทอัตโนมัติ
        </button>
        <button class="btn btn-warning btn-sm ml-2" onclick="toggleNotificationArea()" id="notificationBtn">
            <i class="fal fa-bell"></i> <span id="notification-toggle-text">แสดงการแจ้งเตือน</span>
            <span class="badge badge-light ml-1" id="notification-count" style="display: none;">0</span>
        </button>
        <a href="?url=manager_charecter/character-statistics" class="btn btn-info btn-sm ml-2">
            <i class="fal fa-chart-bar"></i> สถิติ
        </a>
    </div>
</div>

<!-- Notification Area -->
<div id="notification-area" class="notification-area mb-3" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fal fa-bell text-warning"></i> การแจ้งเตือนล่าสุด</h6>
            <div>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearAllNotifications()">
                    <i class="fal fa-trash"></i> ล้างทั้งหมด
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleNotificationArea()">
                    <i class="fal fa-times"></i> ปิด
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div id="notifications-list" class="notifications-list">
                <div class="text-center text-muted p-4" id="no-notifications">
                    <i class="fal fa-bell-slash fa-2x mb-2"></i>
                    <p class="mb-0">ยังไม่มีการแจ้งเตือน</p>
                    <small>การแจ้งเตือนจะปรากฏที่นี่เมื่อมีกิจกรรมที่น่าสนใจ</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Section -->
<?php if (!empty($alerts)): ?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <h5><i class="fal fa-exclamation-triangle"></i> การแจ้งเตือน</h5>
            <div class="row">
                <?php foreach ($alerts as $alert): ?>
                    <div class="col-md-6 mb-2">
                        <?php if ($alert['alert_type'] == 'high_level_new'): ?>
                            <div class="alert alert-danger alert-sm">
                                <strong>ตัวละครเลเวลสูงใหม่:</strong><br>
                                ชื่อ: <?php echo $userLogin->thaitrans($alert['Name']); ?><br>
                                เลเวล: <?php echo $alert['LEV']; ?><br>
                                สร้างเมื่อ: <?php echo $alert['CreateDate'] instanceof DateTime ? $alert['CreateDate']->format('d/m/Y H:i') : date('d/m/Y H:i', strtotime($alert['CreateDate'])); ?>
                            </div>
                        <?php elseif ($alert['alert_type'] == 'high_alz'): ?>
                            <div class="alert alert-warning alert-sm">
                                <strong>ตัวละครมี Alz มาก:</strong><br>
                                ชื่อ: <?php echo $userLogin->thaitrans($alert['Name']); ?><br>
                                Alz: <?php echo number_format($alert['Alz']); ?><br>
                                เลเวล: <?php echo $alert['LEV']; ?>
                            </div>
                        <?php elseif ($alert['alert_type'] == 'multiple_chars'): ?>
                            <div class="alert alert-info alert-sm">
                                <strong>สร้างตัวละครหลายตัว:</strong><br>
                                Account ID: <?php echo $alert['account_id']; ?><br>
                                จำนวน: <?php echo $alert['char_count']; ?> ตัวใน 24 ชั่วโมง
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Recent Activities -->
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">กิจกรรมล่าสุด</h3>
                <div class="card-toolbar">
                    <span class="badge badge-info" id="lastUpdate">อัพเดทล่าสุด: <?php echo date('H:i:s'); ?></span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                    <table class="table table-sm table-hover mb-0" id="activitiesTable">
                        <thead class="thead-light sticky-top">
                            <tr>
                                <th>เวลา</th>
                                <th>กิจกรรม</th>
                                <th>ชื่อตัวละคร</th>
                                <th>เลเวล</th>
                                <th>คลาส</th>
                                <th>Alz</th>
                                <th>เวิลด์</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recentActivities)): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted p-4">
                                        <i class="fal fa-history fa-2x mb-2"></i>
                                        <p class="mb-0">ไม่มีกิจกรรมล่าสุด</p>
                                        <small>กิจกรรมจะปรากฏที่นี่เมื่อมีการสร้างตัวละครใหม่</small>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recentActivities as $activity): ?>
                                    <tr class="activity-row" data-time="<?php echo $activity['activity_time'] instanceof DateTime ? $activity['activity_time']->getTimestamp() : strtotime($activity['activity_time']); ?>">
                                    <td class="text-nowrap">
                                        <?php
                                        $activityTime = $activity['activity_time'];
                                        if ($activityTime instanceof DateTime) {
                                            echo $activityTime->format('H:i:s');
                                            echo '<br><small class="text-muted">' . $activityTime->format('d/m/Y') . '</small>';
                                        } else {
                                            echo date('H:i:s', strtotime($activityTime));
                                            echo '<br><small class="text-muted">' . date('d/m/Y', strtotime($activityTime)) . '</small>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if ($activity['activity_type'] == 'created'): ?>
                                            <span class="badge badge-success">สร้างใหม่</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['Name']); ?></td>
                                    <td><span class="badge badge-primary"><?php echo $activity['LEV']; ?></span></td>
                                    <td><?php echo getClassName($activity['Style']); ?></td>
                                    <td><?php echo number_format($activity['Alz']); ?></td>
                                    <td>World <?php echo $activity['WorldIdx']; ?></td>
                                    <td>
                                        <a href="?url=manager_charecter/manage-player&search=<?php echo urlencode($activity['Name']); ?>"
                                           class="btn btn-sm btn-info" title="ดูรายละเอียด">
                                            <i class="fal fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Live Stats -->
    <div class="col-xl-4">
        <div class="card mb-3">
            <div class="card-header">
                <h3 class="card-title">สถิติสด</h3>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h3 text-primary" id="todayCount">-</div>
                        <div class="text-muted small">ตัวละครใหม่วันนี้</div>
                    </div>
                    <div class="col-6">
                        <div class="h3 text-success" id="onlineCount">-</div>
                        <div class="text-muted small">ออนไลน์ขณะนี้</div>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-warning" id="hourCount">-</div>
                        <div class="text-muted small">ชั่วโมงนี้</div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-info" id="totalCount">-</div>
                        <div class="text-muted small">ทั้งหมด</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mb-3">
            <div class="card-header">
                <h3 class="card-title">สถิติด่วน</h3>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>เลเวลเฉลี่ย</span>
                        <span class="badge badge-primary" id="avgLevel">-</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Alz รวม</span>
                        <span class="badge badge-success" id="totalAlz">-</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>เวลาเล่นรวม</span>
                        <span class="badge badge-info" id="totalPlaytime">-</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">สถานะระบบ</h3>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>การเชื่อมต่อฐานข้อมูล</span>
                    <span class="badge badge-success">ปกติ</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>อัพเดทอัตโนมัติ</span>
                    <span class="badge badge-secondary" id="autoRefreshStatus">ปิด</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>อัพเดทล่าสุด</span>
                    <span class="text-muted" id="lastUpdateTime"><?php echo date('H:i:s'); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

function toggleAutoRefresh() {
    const btn = document.getElementById('autoRefreshBtn');
    const status = document.getElementById('autoRefreshStatus');
    
    if (isAutoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        btn.innerHTML = '<i class="fal fa-play"></i> เริ่มอัพเดทอัตโนมัติ';
        btn.className = 'btn btn-primary btn-sm';
        status.textContent = 'ปิด';
        status.className = 'badge badge-secondary';
        isAutoRefreshEnabled = false;
    } else {
        // เพิ่มระยะเวลาการ refresh เป็น 10 วินาที (เหมาะสำหรับ admin)
        autoRefreshInterval = setInterval(refreshData, 10000); // Refresh every 10 seconds
        btn.innerHTML = '<i class="fal fa-pause"></i> หยุดอัพเดทอัตโนมัติ';
        btn.className = 'btn btn-warning btn-sm';
        status.textContent = 'เปิด (ทุก 10 วินาที)';
        status.className = 'badge badge-success';
        isAutoRefreshEnabled = true;

        // แสดงการแจ้งเตือน
        addNotificationToArea(
            '🔄 เริ่มการอัพเดทอัตโนมัติทุก 10 วินาที',
            'info',
            new Date().toLocaleString('th-TH')
        );

        refreshData(); // Initial refresh
    }
}

async function refreshData() {
    try {
        // Update timestamp
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleTimeString();
        document.getElementById('lastUpdate').textContent = 'อัพเดทล่าสุด: ' + new Date().toLocaleTimeString();

        // Fetch live stats with proper authentication
        try {
            const statsResponse = await fetch('files/manager_charecter/api/character-data.php?action=live_stats', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                }
            });

            if (statsResponse.ok) {
                const responseText = await statsResponse.text();
                try {
                    const statsData = JSON.parse(responseText);
                    if (statsData.success) {
                        updateLiveStatsDisplay(statsData.data);
                    } else {
                        console.warn('Stats API error:', statsData.error || 'Unknown error');
                    }
                } catch (parseError) {
                    console.error('Stats API returned invalid JSON:', responseText.substring(0, 200));
                    addNotificationToArea(
                        '❌ Stats API ส่งคืน HTML แทน JSON - กรุณาตรวจสอบ API',
                        'danger',
                        new Date().toLocaleString('th-TH')
                    );
                }
            } else {
                console.warn('Stats API returned:', statsResponse.status, statsResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
            addNotificationToArea(
                '❌ ไม่สามารถดึงข้อมูลสถิติได้',
                'danger',
                new Date().toLocaleString('th-TH')
            );
        }

        // Fetch recent activities with proper authentication
        try {
            const activitiesResponse = await fetch('files/manager_charecter/api/character-data.php?action=recent_activities&limit=10', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                }
            });

            if (activitiesResponse.ok) {
                const responseText = await activitiesResponse.text();
                try {
                    const activitiesData = JSON.parse(responseText);
                    if (activitiesData.success) {
                        updateActivitiesTable(activitiesData.data);
                    } else {
                        console.warn('Activities API error:', activitiesData.error || 'Unknown error');
                    }
                } catch (parseError) {
                    console.error('Activities API returned invalid JSON:', responseText.substring(0, 200));
                    addNotificationToArea(
                        '❌ Activities API ส่งคืน HTML แทน JSON - กรุณาตรวจสอบ API',
                        'danger',
                        new Date().toLocaleString('th-TH')
                    );
                }
            } else {
                console.warn('Activities API returned:', activitiesResponse.status, activitiesResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching activities:', error);
            addNotificationToArea(
                '❌ ไม่สามารถดึงข้อมูลกิจกรรมได้',
                'danger',
                new Date().toLocaleString('th-TH')
            );
        }

        // Fetch alerts with proper credentials
        try {
            const alertsResponse = await fetch('files/manager_charecter/api/character-data.php?action=alerts', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (alertsResponse.ok) {
                const responseText = await alertsResponse.text();
                try {
                    const alertsData = JSON.parse(responseText);
                    if (alertsData.success && alertsData.data.length > 0) {
                        showNewAlerts(alertsData.data);
                    }
                } catch (parseError) {
                    console.error('Alerts API returned invalid JSON:', responseText.substring(0, 200));
                    addNotificationToArea(
                        '❌ Alerts API ส่งคืน HTML แทน JSON - กรุณาตรวจสอบ API',
                        'danger',
                        new Date().toLocaleString('th-TH')
                    );
                }
            } else {
                console.warn('Alerts API returned:', alertsResponse.status, alertsResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching alerts:', error);
            addNotificationToArea(
                '❌ ไม่สามารถดึงข้อมูลการแจ้งเตือนได้',
                'danger',
                new Date().toLocaleString('th-TH')
            );
        }

    } catch (error) {
        console.error('Error refreshing data:', error);
        document.getElementById('statusBadge').innerHTML = '<i class="fal fa-exclamation-triangle"></i> Error';
        document.getElementById('statusBadge').className = 'badge badge-danger';
    }
}

// Initialize live stats
function updateLiveStats() {
    // This would typically fetch data via AJAX
    // For demonstration, we'll use placeholder values
    document.getElementById('todayCount').textContent = '<?php echo count(array_filter($recentActivities, function($a) { return date('Y-m-d', strtotime($a["activity_time"])) == date('Y-m-d'); })); ?>';
    // onlineCount จะถูกอัพเดทจาก API ใน updateLiveStatsDisplay()
    document.getElementById('hourCount').textContent = '<?php echo count(array_filter($recentActivities, function($a) { return strtotime($a["activity_time"]) > strtotime("-1 hour"); })); ?>';
    document.getElementById('totalCount').textContent = '<?php echo count($recentActivities); ?>';
    
    // Additional stats
    document.getElementById('avgLevel').textContent = '<?php echo round(array_sum(array_column($recentActivities, "LEV")) / max(count($recentActivities), 1), 1); ?>';
    document.getElementById('totalAlz').textContent = '<?php echo number_format(array_sum(array_column($recentActivities, "Alz"))); ?>';
    document.getElementById('totalPlaytime').textContent = '<?php echo number_format(rand(10000, 50000)); ?> ชม.'; // Placeholder
}

// Initialize - โหลดข้อมูลเริ่มต้นจาก API
updateLiveStats();

// โหลดข้อมูลเริ่มต้นทันทีเมื่อโหลดหน้า
document.addEventListener('DOMContentLoaded', function() {
    // แสดงข้อความ loading
    document.getElementById('onlineCount').textContent = 'กำลังโหลด...';
    document.getElementById('totalCount').textContent = 'กำลังโหลด...';

    // โหลดข้อมูลจริงจาก API
    refreshData();
});

function updateLiveStatsDisplay(stats) {
    if (stats.total_characters !== undefined) {
        document.getElementById('totalCount').textContent = stats.total_characters.toLocaleString();
    }
    if (stats.online_characters !== undefined) {
        // แสดงจำนวนผู้เล่นออนไลน์จริงจาก database (ไม่ใช่ random)
        document.getElementById('onlineCount').textContent = stats.online_characters.toLocaleString();

        // Log สำหรับ admin ตรวจสอบ
        console.log(`[Admin] Online players count updated: ${stats.online_characters}`);
    }
    if (stats.created_today !== undefined) {
        document.getElementById('todayCount').textContent = stats.created_today.toLocaleString();
    }
    if (stats.created_this_hour !== undefined) {
        document.getElementById('hourCount').textContent = stats.created_this_hour.toLocaleString();
    }
    if (stats.avg_level !== undefined) {
        document.getElementById('avgLevel').textContent = stats.avg_level;
    }
    if (stats.total_alz !== undefined) {
        document.getElementById('totalAlz').textContent = stats.total_alz.toLocaleString();
    }
    if (stats.total_playtime !== undefined) {
        const hours = Math.round(stats.total_playtime / 3600);
        document.getElementById('totalPlaytime').textContent = hours.toLocaleString() + ' ชม.';
    }
}

function updateActivitiesTable(activities) {
    const tbody = document.querySelector('#activitiesTable tbody');
    if (!tbody) return;

    // ถ้าไม่มีข้อมูล แสดงข้อความ
    if (!activities || activities.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted p-4">
                    <i class="fal fa-history fa-2x mb-2"></i>
                    <p class="mb-0">ไม่มีกิจกรรมล่าสุด</p>
                    <small>กิจกรรมจะปรากฏที่นี่เมื่อมีการสร้างตัวละครใหม่</small>
                </td>
            </tr>
        `;
        return;
    }

    // Add new activities to the top
    activities.forEach(activity => {
        const existingRow = tbody.querySelector(`[data-char-id="${activity.character_idx}"]`);
        if (existingRow) return; // Skip if already exists

        const row = document.createElement('tr');
        row.className = 'activity-row new-activity';
        row.setAttribute('data-char-id', activity.character_idx);
        row.setAttribute('data-time', new Date(activity.create_date).getTime() / 1000);

        const time = new Date(activity.create_date);
        const timeStr = time.toLocaleTimeString();
        const dateStr = time.toLocaleDateString();

        row.innerHTML = `
            <td class="text-nowrap">
                ${timeStr}
                <br><small class="text-muted">${dateStr}</small>
            </td>
            <td><span class="badge badge-success">สร้างใหม่</span></td>
            <td>${activity.name}</td>
            <td><span class="badge badge-primary">${activity.level}</span></td>
            <td>${getClassName(activity.style)}</td>
            <td>${activity.alz.toLocaleString()}</td>
            <td>World ${activity.world_idx}</td>
            <td>
                <a href="?url=manager_charecter/manage-player&search=${encodeURIComponent(activity.name)}"
                   class="btn btn-sm btn-info" title="ดูรายละเอียด">
                    <i class="fal fa-eye"></i>
                </a>
            </td>
        `;

        tbody.insertBefore(row, tbody.firstChild);
    });

    // Remove old rows (keep only latest 50)
    const rows = tbody.querySelectorAll('tr');
    if (rows.length > 50) {
        for (let i = 50; i < rows.length; i++) {
            rows[i].remove();
        }
    }

    // Highlight new activities
    setTimeout(() => {
        document.querySelectorAll('.new-activity').forEach(row => {
            row.classList.remove('new-activity');
        });
    }, 3000);
}

function getClassName(style) {
    // ใช้การคำนวณแบบเดียวกับ PHP cabalstyle function
    const battleStyle = style & 7; // 3 บิตแรก
    const extendedBattleStyle = (style >> 23) & 1; // บิตที่ 23
    const classIndex = battleStyle | (extendedBattleStyle << 3);

    const classNames = {
        1: 'Warrior',
        2: 'Blader',
        3: 'Wizard',
        4: 'Force Archer',
        5: 'Force Shielder',
        6: 'Force Blader',
        7: 'Gladiator',
        8: 'Force Gunner',
        9: 'Dark Mage'
    };

    return classNames[classIndex] || 'Unknown';
}

// เก็บ alerts ที่แสดงแล้วเพื่อป้องกันการแสดงซ้ำ
let shownAlerts = new Set();

function showNewAlerts(alerts) {
    // Show alerts in notification area
    alerts.forEach(alert => {
        // สร้าง unique key สำหรับ alert
        const alertKey = alert.type + '_' + (alert.data.CharacterIdx || alert.data.Name || alert.message);

        // ตรวจสอบว่าแสดงแล้วหรือยัง (ลดเงื่อนไขให้แสดงได้บ่อยขึ้น)
        const now = Date.now();
        const lastShown = localStorage.getItem('lastAlert_' + alertKey);

        // แสดงใหม่ถ้าผ่านไป 5 นาที หรือยังไม่เคยแสดง
        if (!lastShown || (now - parseInt(lastShown)) > 3000) {
            localStorage.setItem('lastAlert_' + alertKey, now.toString());

            if (alert.severity === 'high') {
                addNotificationToArea(alert.message, 'danger', alert.time);
            } else if (alert.severity === 'medium') {
                addNotificationToArea(alert.message, 'warning', alert.time);
            } else {
                addNotificationToArea(alert.message, 'info', alert.time);
            }
        }
    });
}

function showToast(message, type = 'info') {
    // ตรวจสอบว่ามี toast เดียวกันอยู่แล้วหรือไม่
    const existingToasts = document.querySelectorAll('.toast-notification');
    for (let toast of existingToasts) {
        if (toast.textContent.includes(message.substring(0, 50))) {
            return; // ไม่แสดง toast ซ้ำ
        }
    }

    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed toast-notification`;

    // คำนวณตำแหน่ง top ตาม toast ที่มีอยู่
    const existingCount = existingToasts.length;
    const topPosition = 20 + (existingCount * 80);

    toast.style.cssText = `top: ${topPosition}px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;`;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="close ml-2" onclick="this.parentElement.parentElement.remove()">
                <span>&times;</span>
            </button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 8 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('fade-out');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                    // ปรับตำแหน่ง toast ที่เหลือ
                    repositionToasts();
                }
            }, 300);
        }
    }, 8000);
}

function repositionToasts() {
    const toasts = document.querySelectorAll('.toast-notification');
    toasts.forEach((toast, index) => {
        const topPosition = 20 + (index * 80);
        toast.style.top = topPosition + 'px';
    });
}

// ฟังก์ชันสำหรับจัดการพื้นที่แจ้งเตือน
function toggleNotificationArea() {
    const area = document.getElementById('notification-area');
    const btn = document.getElementById('notificationBtn');
    const toggleText = document.getElementById('notification-toggle-text');

    if (area.style.display === 'none') {
        area.style.display = 'block';
        toggleText.textContent = 'ซ่อนการแจ้งเตือน';
        btn.classList.remove('btn-warning');
        btn.classList.add('btn-success');
    } else {
        area.style.display = 'none';
        toggleText.textContent = 'แสดงการแจ้งเตือน';
        btn.classList.remove('btn-success');
        btn.classList.add('btn-warning');
    }
}

function addNotificationToArea(message, type, time) {
    const notificationsList = document.getElementById('notifications-list');
    const noNotifications = document.getElementById('no-notifications');
    const notificationCount = document.getElementById('notification-count');

    // ซ่อน "ยังไม่มีการแจ้งเตือน"
    if (noNotifications) {
        noNotifications.style.display = 'none';
    }

    // สร้าง notification item
    const notificationItem = document.createElement('div');
    notificationItem.className = `notification-item border-bottom`;

    const typeClass = type === 'danger' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
    const icon = type === 'danger' ? 'fa-exclamation-triangle' : type === 'warning' ? 'fa-exclamation' : 'fa-info-circle';

    notificationItem.innerHTML = `
        <div class="p-3">
            <div class="d-flex align-items-start">
                <div class="mr-3">
                    <i class="fal ${icon} ${typeClass} fa-lg"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="notification-message">${message}</div>
                    <small class="text-muted">
                        <i class="fal fa-clock"></i> ${time || new Date().toLocaleString('th-TH')}
                    </small>
                </div>
                <div class="ml-2">
                    <button class="btn btn-sm btn-outline-secondary" onclick="removeNotification(this)" title="ลบการแจ้งเตือนนี้">
                        <i class="fal fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    // เพิ่มที่ด้านบน
    notificationsList.insertBefore(notificationItem, notificationsList.firstChild);

    // แสดง notification area อัตโนมัติ
    const notificationArea = document.getElementById('notification-area');
    if (notificationArea && notificationArea.style.display === 'none') {
        notificationArea.style.display = 'block';

        // อัพเดทปุ่ม toggle
        const btn = document.getElementById('notificationBtn');
        const toggleText = document.getElementById('notification-toggle-text');
        if (btn && toggleText) {
            toggleText.textContent = 'ซ่อนการแจ้งเตือน';
            btn.classList.remove('btn-outline-warning');
            btn.classList.add('btn-warning');
        }
    }

    // จำกัดจำนวนการแจ้งเตือน (เก็บแค่ 20 รายการล่าสุด)
    const items = notificationsList.querySelectorAll('.notification-item');
    if (items.length > 20) {
        for (let i = 20; i < items.length; i++) {
            items[i].remove();
        }
    }

    // อัพเดทจำนวนการแจ้งเตือน
    updateNotificationCount();

    // แสดง animation
    notificationItem.style.opacity = '0';
    notificationItem.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        notificationItem.style.transition = 'all 0.3s ease-in-out';
        notificationItem.style.opacity = '1';
        notificationItem.style.transform = 'translateY(0)';
    }, 100);

    // แสดง toast notification ในมุมขวาบน
    showToastNotification(message, type, time);
}

function showToastNotification(message, type, time) {
    // สร้าง toast container ถ้ายังไม่มี
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // สร้าง toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    const typeClass = type === 'danger' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
    const icon = type === 'danger' ? 'fa-exclamation-triangle' : type === 'warning' ? 'fa-exclamation' : 'fa-info-circle';

    toast.innerHTML = `
        <div class="toast-header">
            <i class="fal ${icon} ${typeClass} mr-2"></i>
            <strong class="mr-auto">การแจ้งเตือน</strong>
            <small class="text-muted">${time || new Date().toLocaleTimeString('th-TH')}</small>
            <button type="button" class="close ml-2" onclick="removeToast(this)">
                <span>&times;</span>
            </button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    // เพิ่ม toast ที่ด้านบน
    toastContainer.insertBefore(toast, toastContainer.firstChild);

    // แสดง toast ด้วย animation
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // ลบ toast อัตโนมัติหลังจาก 5 วินาที
    setTimeout(() => {
        removeToast(toast.querySelector('.close'));
    }, 5000);

    // จำกัดจำนวน toast (เก็บแค่ 3 รายการ)
    const toasts = toastContainer.querySelectorAll('.toast-notification');
    if (toasts.length > 3) {
        for (let i = 3; i < toasts.length; i++) {
            toasts[i].remove();
        }
    }
}

function removeToast(closeBtn) {
    const toast = closeBtn.closest('.toast-notification');
    if (toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }
}

function removeNotification(button) {
    const notificationItem = button.closest('.notification-item');
    notificationItem.style.transition = 'all 0.3s ease-in-out';
    notificationItem.style.opacity = '0';
    notificationItem.style.transform = 'translateX(100%)';

    setTimeout(() => {
        notificationItem.remove();
        updateNotificationCount();

        // แสดง "ยังไม่มีการแจ้งเตือน" ถ้าไม่มีการแจ้งเตือนเหลือ
        const remainingItems = document.querySelectorAll('.notification-item');
        if (remainingItems.length === 0) {
            document.getElementById('no-notifications').style.display = 'block';
        }
    }, 300);
}

function clearAllNotifications() {
    const notificationsList = document.getElementById('notifications-list');
    const items = notificationsList.querySelectorAll('.notification-item');

    items.forEach((item, index) => {
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease-in-out';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';

            setTimeout(() => {
                item.remove();
                if (index === items.length - 1) {
                    document.getElementById('no-notifications').style.display = 'block';
                    updateNotificationCount();
                }
            }, 300);
        }, index * 100);
    });
}

function updateNotificationCount() {
    const count = document.querySelectorAll('.notification-item').length;
    const countBadge = document.getElementById('notification-count');

    if (count > 0) {
        countBadge.textContent = count;
        countBadge.style.display = 'inline';
    } else {
        countBadge.style.display = 'none';
    }
}

// ลบฟังก์ชัน session management ออกเพื่อป้องกันการเด้งออกจากระบบ

async function testInitialAlerts() {
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=alerts', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            const responseText = await response.text();
            try {
                const alertsData = JSON.parse(responseText);
                if (alertsData.success && alertsData.data.length > 0) {
                    addNotificationToArea(
                        `🔔 พบการแจ้งเตือน ${alertsData.data.length} รายการ`,
                        'warning',
                        new Date().toLocaleString('th-TH')
                    );

                    // แสดงการแจ้งเตือนทั้งหมด
                    alertsData.data.forEach(alert => {
                        const now = Date.now();
                        const alertKey = alert.type + '_' + (alert.data.CharacterIdx || alert.data.Name || alert.message);
                        const lastShown = localStorage.getItem('lastAlert_' + alertKey);

                        // แสดงใหม่ถ้าผ่านไป 5 นาที หรือยังไม่เคยแสดง
                        if (!lastShown || (now - parseInt(lastShown)) > 3000) {
                            localStorage.setItem('lastAlert_' + alertKey, now.toString());

                            let severity = 'info';
                            if (alert.severity === 'high') severity = 'danger';
                            else if (alert.severity === 'medium') severity = 'warning';

                            addNotificationToArea(alert.message, severity, alert.time);
                        }
                    });
                } else {
                    addNotificationToArea(
                        '✅ ไม่มีการแจ้งเตือนในขณะนี้',
                        'info',
                        new Date().toLocaleString('th-TH')
                    );
                }
            } catch (parseError) {
                console.error('Error parsing alerts:', parseError);
                addNotificationToArea(
                    '❌ เกิดข้อผิดพลาดในการดึงข้อมูลการแจ้งเตือน',
                    'danger',
                    new Date().toLocaleString('th-TH')
                );
            }
        } else {
            console.warn('Initial alerts API returned:', response.status, response.statusText);
            addNotificationToArea(
                '⚠️ ไม่สามารถดึงข้อมูลการแจ้งเตือนได้ในขณะนี้',
                'warning',
                new Date().toLocaleString('th-TH')
            );
        }
    } catch (error) {
        console.error('Error fetching initial alerts:', error);
        addNotificationToArea(
            '❌ ไม่สามารถเชื่อมต่อกับระบบการแจ้งเตือนได้',
            'danger',
            new Date().toLocaleString('th-TH')
        );
    }
}

// Highlight new activities
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.activity-row');
    const now = Math.floor(Date.now() / 1000);
    
    rows.forEach(row => {
        const time = parseInt(row.dataset.time);
        if (now - time < 300) { // Less than 5 minutes ago
            row.style.backgroundColor = '#fff3cd';
        }
    });

    // เพิ่มการแจ้งเตือนตัวอย่างเมื่อโหลดหน้า
    setTimeout(() => {
        addNotificationToArea(
            'ระบบตรวจสอบความเคลื่อนไหวตัวละครเริ่มทำงานแล้ว',
            'info',
            new Date().toLocaleString('th-TH')
        );

        // เริ่มการแจ้งเตือนอัตโนมัติ
        if (autoRefreshInterval) {
            addNotificationToArea(
                '🔄 เริ่มการตรวจสอบอัตโนมัติทุก 10 วินาที',
                'info',
                new Date().toLocaleString('th-TH')
            );
        }
    }, 2000);

    // ทดสอบการแจ้งเตือนเมื่อโหลดหน้า
    setTimeout(() => {
        testInitialAlerts();
    }, 5000);

    console.log('%c✅ Character Monitor loaded with notification system', 'color: #28a745; font-weight: bold;');
});
</script>

<style>
.new-activity {
    background-color: #fff3cd !important;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.activity-row:hover {
    background-color: #f8f9fa !important;
}

.badge {
    font-size: 0.75em;
}

/* Toast Notifications */
.toast-notification {
    transition: all 0.3s ease-in-out;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
}

.toast-notification.fade-out {
    opacity: 0;
    transform: translateX(100%);
}

.toast-notification .close {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.7;
}

.toast-notification .close:hover {
    opacity: 1;
}

/* Notification Area */
.notification-area {
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    transition: all 0.3s ease-in-out;
    background-color: #fff;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none !important;
}

.notification-message {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

/* Notification count badge */
#notification-count {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
    padding: 0 4px;
}

/* Scrollbar สำหรับ notifications list */
.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}

.thead-light th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.list-group-item {
    border-left: 0;
    border-right: 0;
}

.list-group-item:first-child {
    border-top: 0;
}

.list-group-item:last-child {
    border-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .subheader-block {
        margin-top: 10px;
    }
    
    .subheader-block .btn {
        margin-bottom: 5px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Toast Container */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

.toast-notification {
    min-width: 300px;
    max-width: 400px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease-in-out;
}

.toast-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-notification .toast-header {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.toast-notification .toast-body {
    padding: 0.75rem;
    word-wrap: break-word;
}

.toast-notification .close {
    background: none;
    border: none;
    font-size: 1.25rem;
    line-height: 1;
    color: #000;
    opacity: 0.5;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.toast-notification .close:hover {
    opacity: 1;
}

.toast-danger {
    border-left: 4px solid #dc3545;
}

.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast-success {
    border-left: 4px solid #28a745;
}
</style>
