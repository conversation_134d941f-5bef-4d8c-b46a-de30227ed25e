<?php $zpanel->checkSession(true); ?>
<header class="page-header">
    <h2>เปลียนพาสเวิส</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>หน้ารวมข้อมูล</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
<div class="row">
    <div class="col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <h2 class="panel-title">เปลียนพาสเวิส</h2>
            </header>
            <div class="panel-body">
                <?php
                                        if (isset($_POST['btn_savechange'])) {
                                            // if you want validate all inputs with filter_input you're free to make it
                                            // variables access info
                                            $editEmail = strip_tags(trim($_POST['input_email']));
                                            $editOldPass = strip_tags(trim($_POST['input_password']));
                                            $editNewPass = strip_tags(trim($_POST['input_newpass']));;
                                            $editconfirmnewpass = strip_tags(trim($_POST['input_confirmnewpass']));

                                            // convert input old pass
                                                $usernum = $userLogin->recUserAccount('UserNum', $conn);
                                                $userid = $userLogin->recUserAccount('ID', $conn);
												$email = $userLogin->recUserAccount('Email', $conn);
                                                $getDateNow = date('Y-m-d H:i:s');
                                                $selectUsersPass = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = '$usernum' AND PWDCOMPARE('$editOldPass', Password) = 1";
                                                $updateUserPassAccountParam = array();
                                                $updateUserPassAccountQuery = sqlsrv_query($conn, $selectUsersPass, $updateUserPassAccountParam);
                                                $updateUserPassAccountRows = sqlsrv_rows_affected($updateUserPassAccountQuery);


                                            // condition

                                            if (empty($editEmail)) {
                                                $returnWarning = W_EMPTY_EMAIL;
                                            } else if (!filter_var($editEmail, FILTER_VALIDATE_EMAIL)) {
                                                $returnWarning = W_WRONG_EMAIL;
											}else if ($editEmail != $email){
												$returnWarning = W_WRONG_EMAIL;
                                            } else if (!empty($editOldPass) && empty($editNewPass)) {
                                                $returnWarning = W_NEWPASS_EMPTY;
                                            } else if (!empty($editNewPass) && empty($editOldPass)) {
                                                $returnWarning = W_OLDPASS_EMPTY;
                                            } else if (!empty($updateUserPassAccountRows) == 0 ) {
                                                $returnWarning = W_WRONG_OLDPASS;
                                            } else if (!empty($editconfirmnewpass) && empty($editNewPass)) {
                                                $returnWarning = W_CONFIRMPASS_EMPTY;
                                            } else if ($editconfirmnewpass <> $editNewPass) {
                                                $returnWarning = W_EMPTY_CONFIRMPASSWORD;
                                            } else if (strlen($editNewPass)< 6 || strlen($editconfirmnewpass) >15) {
                                                $returnWarning = W_NEWPASS_LESS;
                                            } else {
                                                // update user account
                                                $updateUserAccount = "EXECUTE [".DATABASE_ACC."].[dbo].cabal_user_update_password '$userid','$editconfirmnewpass'";
                                                $updateUserAccountParam = array();
                                                $updateUserAccountQuery = sqlsrv_query($conn, $updateUserAccount, $updateUserAccountParam);
                                                $updateUserAccountRows = sqlsrv_rows_affected($updateUserAccountQuery);

                                                if ($updateUserAccountRows) {
                                                    // update user info
                                                    $updateUserInfo = "UPDATE [".DATABASE_ACC."].[dbo].cabal_auth_table SET Last_update = '$getDateNow' WHERE UserNum = '$usernum'";
                                                    $updateUserInfoParam = array();
                                                    $updateUserInfoQuery = sqlsrv_query($conn, $updateUserInfo, $updateUserInfoParam);
                                                    $updateUserInfoRows = sqlsrv_rows_affected($updateUserInfoQuery);

                                                    if ($updateUserInfoRows) {
                                                        $returnSuccess = S_ACCOUNT_UPDATED;
                                                        $zpanel->generateWebLog($conn, '2',$userLogin->recUserAccount('UserNum', $conn), 'เปลียนพาสเวิสใหม่', "ไอดี {$userLogin->recUserAccount('ID', $conn)} ได้ทำการเปลียนพาสเวิส");
                                                        $_SESSION['userLogin'] = $editEmail;
                                                    }
                                                } else {
                                                    $returnError = E_EDITACC_ERROR;
                                                }
                                            }
                                        }
                                        ?>
                <?php if (isset($returnSuccess)) { ?>
                <div class="alert alert-success j_dismiss"> <?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>

                <?php } ?>
                <div class="col-lg-6">
                    <form role="form" method="post" enctype="multipart/form-data">
                        <h2><?php echo T_ACCESS_INFO; ?></h2>
                        <div class="form-group">
                            <label for="input_email" class="control-label"><?php echo T_EMAIL; ?>:</label>
                            <input type="text" name="input_email" class="form-control" id="input_email"
                                data-toggle="tooltip" data-title="ยืนยัน Email!" title="ยืนยัน Email "
                                placeholder="ยืนยัน Email">
                        </div>
                        <div class="form-group">
                            <label for="input_password" class="control-label"><?php echo T_OLDPASS; ?>:</label>
                            <input type="password" name="input_password" class="form-control" data-toggle="tooltip"
                                data-title="ยืนยัน Password เก่า!" title="ยืนยัน Password เก่า!" id="input_password"
                                placeholder="<?php echo P_OLDPASS; ?>">
                        </div>
                        <h2><?php echo T_NEWPASS_INFO; ?></h2>
                        <div class="form-group">
                            <label for="input_newpass" class="control-label"><?php echo T_NEWPASS; ?>:</label>
                            <input type="password" name="input_newpass" class="form-control" data-toggle="tooltip"
                                data-title="ยืนยัน Password ที่ต้องการใหม่!" title="ยืนยัน Password ที่ต้องการใหม่!!"
                                id="input_newpass" placeholder="<?php echo P_NEWPASS; ?>">
                        </div>
                        <div class="form-group">
                            <label for="input_confirmnewpass"
                                class="control-label"><?php echo T_NEWPASSCONFIRM; ?>:</label>
                            <input type="password" name="input_confirmnewpass" class="form-control"
                                data-toggle="tooltip" data-title="ยืนยัน Password ที่ต้องการอีกครั้ง!"
                                title="ยืนยัน Password ที่ต้องการอีกครั้ง!!" id="input_confirmnewpass"
                                placeholder="<?php echo P_NEWPASSCONFIRM; ?>">
                        </div>

                        <div class="form-group">
                            <button type="submit" name="btn_savechange"
                                class="mb-xs mt-xs mr-xs btn btn-primary"><?php echo B_SAVECHANGES; ?></button>
                        </div>
                    </form>
                </div>

            </div>
        </section>

    </div>
</div>