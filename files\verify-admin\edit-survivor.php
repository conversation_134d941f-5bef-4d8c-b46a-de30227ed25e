<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGECHARS; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get server log by ID
                $selectCategory = "SELECT * FROM Accounts WHERE CustomerID = '$getCategoryID'";
                $$selectClanParam = array();
                $selectCategoryOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectCategoryQuery = sqlsrv_query($conn, $selectCategory, $$selectClanParam, $selectCategoryOpt);
                $selectCategoryRows = sqlsrv_num_rows($selectCategoryQuery);

                // get data info
                $selectPlayerData = "SELECT * FROM UsersData WHERE CustomerID = '$getCategoryID'";
                $selectPlayerDataParam = array();
                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                $selectPlayerDataFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);

                if ($selectCategoryRows) {
                    while ($resPlayer = sqlsrv_fetch_array($selectCategoryQuery, SQLSRV_FETCH_ASSOC)) {
                        ?>
                        <?php
                        if (isset($_POST)) {
                            
                        }
                        ?>
                        <form method="post" enctype="multipart/form-data" action="">
                            <h2 class="text-red"><?php echo T_ACCOUNTINFO; ?> <small>customerID: <?php echo $resPlayer['CustomerID']; ?></small></h2>
                            <hr>
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <h4 class="text-red"><?php echo T_EMAIL; ?></h4>
                                    <input class="form-control" type="email" name="plr-email" value="<?php echo $resPlayer['email']; ?>">
                                </div>

                                <div class="form-group">
                                    <h4 class="text-red"><?php echo T_PASSWORD; ?></h4>
                                    <input class="form-control" type="password" name="plr-pass" placeholder="New password ...">
                                </div>

                                <h4 class="text-red"><?php echo T_ISDEV; ?></h4>
                                <select name="plr-isdev" class="form-control">
                                    <option value="-1">Actual: <?php echo $isDev = ($resPlayer['IsDeveloper'] ? 'Developer' : 'Player'); ?></option>
                                    <option value="126">Developer</option>
                                    <option value="0">Player</option>
                                </select>
                            </div>
                        </form>
                        <?php
                    }
                } else {
                    $returnWarning = W_PLAYER_NOT_FOUND;
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="financialModal" tabindex="-1" role="dialog" aria-labelledby="financialModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Add GC and GD to player</h4>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <p class="text-red">Enter a value to add GC and GD to player account.<br> <span class="text-bolder">If input empty the value by default is 0.</span></p>
                    <div class="form-group">
                        <label for="plr-gc">Game Cash</label>
                        <input class="form-control" id="plr-gc" type="text" name="plr-gc" placeholder="How much GC you want add?">
                    </div>
                    <div class="form-group">
                        <label for="plr-gd">Game Dollar</label>
                        <input class="form-control" id="plr-gd" type="text" name="plr-gd" placeholder="How much GD you want add?">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <input type="submit" class="btn btn-primary j_add_gc_gd" value="Add">
                </div>
            </form>
        </div>
    </div>
</div>