<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-calendar-alt"></i> ทดสอบการวิเคราะห์ตามช่วงวัน (7/30/90)
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">การวิเคราะห์ตัวละครตามช่วงวันที่กำหนด</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> การวิเคราะห์ทั้งหมดใช้ช่วงวันที่กำหนด (7/30/90 วัน) แล้ว
                </div>
                
                <h5>📅 การวิเคราะห์ที่ใช้ช่วงวัน</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-line"></i> Level Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• แสดงเฉพาะตัวละครที่สร้างใน X วันที่ผ่านมา</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-coins"></i> Wealth Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• วิเคราะห์ Alz ของตัวละครใหม่</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fal fa-clock"></i> Activity Analysis</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• วิเคราะห์ PlayTime ของตัวละครใหม่</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-crown"></i> Honor Class</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• Honor Class ของตัวละครใหม่</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-dark">
                            <div class="card-header bg-dark text-white">
                                <h6 class="mb-0"><i class="fal fa-chart-bar"></i> Progression</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• ความก้าวหน้าของตัวละครใหม่</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fal fa-calendar"></i> Creation</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>✅ ใช้ช่วงวัน</li>
                                    <li>• WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li>• การสร้างตัวละครในช่วงที่กำหนด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔍 ทดสอบการทำงานตามช่วงวัน</h5>
                <?php
                $testDays = [7, 30, 90];
                
                foreach ($testDays as $days) {
                    echo "<h6>{$days} วันที่ผ่านมา:</h6>";
                    
                    try {
                        // ทดสอบ Level Analysis
                        $sql = "SELECT 
                                    COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                                    COUNT(CASE WHEN LEV BETWEEN 51 AND 100 THEN 1 END) as level_51_100,
                                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                                    COUNT(*) as total_characters,
                                    AVG(CAST(LEV AS FLOAT)) as avg_level
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
                        
                        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
                        
                        if ($stmt && sqlsrv_execute($stmt)) {
                            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                echo "<div class='row mb-3'>";
                                echo "<div class='col-md-6'>";
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-sm table-striped'>";
                                echo "<thead class='thead-light'><tr><th colspan='2'>Level Analysis ({$days} วัน)</th></tr></thead>";
                                echo "<tr><td>ตัวละครทั้งหมด</td><td><span class='badge badge-primary'>" . number_format($row['total_characters']) . "</span></td></tr>";
                                echo "<tr><td>Level 1-50</td><td><span class='badge badge-success'>" . number_format($row['level_1_50']) . "</span></td></tr>";
                                echo "<tr><td>Level 51-100</td><td><span class='badge badge-info'>" . number_format($row['level_51_100']) . "</span></td></tr>";
                                echo "<tr><td>Level 200+</td><td><span class='badge badge-dark'>" . number_format($row['level_200_plus']) . "</span></td></tr>";
                                echo "<tr><td>เลเวลเฉลี่ย</td><td><strong>" . number_format($row['avg_level'], 1) . "</strong></td></tr>";
                                echo "</table></div></div>";
                                
                                // ทดสอบ Honor Class
                                $sql2 = "SELECT 
                                            CASE 
                                                WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                                WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                                WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                                WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                                WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                                WHEN Reputation >= 2000000000 THEN 'Class 20'
                                                ELSE 'Other Classes'
                                            END as honor_class,
                                            COUNT(*) as count
                                        FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                                        WHERE CreateDate >= DATEADD(day, -?, GETDATE())
                                        GROUP BY 
                                            CASE 
                                                WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                                WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                                WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                                WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                                WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                                WHEN Reputation >= 2000000000 THEN 'Class 20'
                                                ELSE 'Other Classes'
                                            END
                                        ORDER BY count DESC";
                                
                                $stmt2 = sqlsrv_prepare($conn, $sql2, array(&$days));
                                
                                if ($stmt2 && sqlsrv_execute($stmt2)) {
                                    echo "<div class='col-md-6'>";
                                    echo "<div class='table-responsive'>";
                                    echo "<table class='table table-sm table-striped'>";
                                    echo "<thead class='thead-light'><tr><th colspan='2'>Honor Class ({$days} วัน)</th></tr></thead>";
                                    
                                    while ($row2 = sqlsrv_fetch_array($stmt2, SQLSRV_FETCH_ASSOC)) {
                                        $honorClass = $row2['honor_class'];
                                        $count = $row2['count'];
                                        
                                        // กำหนดสี
                                        $colorClass = 'secondary';
                                        if (strpos($honorClass, 'Class') !== false) {
                                            $classNum = (int)str_replace('Class ', '', $honorClass);
                                            if ($classNum == 0) $colorClass = 'secondary';
                                            elseif ($classNum >= 1 && $classNum <= 5) $colorClass = 'success';
                                            elseif ($classNum >= 6 && $classNum <= 10) $colorClass = 'info';
                                            elseif ($classNum >= 11 && $classNum <= 15) $colorClass = 'warning';
                                            elseif ($classNum >= 16 && $classNum <= 19) $colorClass = 'danger';
                                            elseif ($classNum >= 20) $colorClass = 'dark';
                                        }
                                        
                                        echo "<tr>";
                                        echo "<td><span class='badge badge-{$colorClass}'>" . htmlspecialchars($honorClass) . "</span></td>";
                                        echo "<td>" . number_format($count) . "</td>";
                                        echo "</tr>";
                                    }
                                    
                                    echo "</table></div></div>";
                                }
                                
                                echo "</div>";
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error for {$days} days: " . $e->getMessage() . "</div>";
                    }
                    
                    echo "<hr>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบการแสดงผล</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openAnalytics7()">
                        <i class="fal fa-analytics"></i> Analytics 7 วัน
                    </button>
                    <button class="btn btn-info" onclick="openAnalytics30()">
                        <i class="fal fa-analytics"></i> Analytics 30 วัน
                    </button>
                    <button class="btn btn-warning" onclick="openAnalytics90()">
                        <i class="fal fa-analytics"></i> Analytics 90 วัน
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการแก้ไข</h5>
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. SQL Queries ที่แก้ไข
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li><strong>Level Analysis:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li><strong>Wealth Analysis:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li><strong>Activity Analysis:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li><strong>Honor Class:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li><strong>Progression:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                    <li><strong>Creation:</strong> เพิ่ม WHERE CreateDate >= DATEADD(day, -?, GETDATE())</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. การแสดงผลที่แก้ไข
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>UI Changes:</strong></p>
                                <ul>
                                    <li>เพิ่ม "(X วันที่ผ่านมา)" ในหัวข้อทุกการ์ด</li>
                                    <li>ใช้ sqlsrv_prepare แทน sqlsrv_query</li>
                                    <li>ส่งค่า $days เป็น parameter</li>
                                    <li>แสดงข้อมูลเฉพาะตัวละครที่สร้างในช่วงที่กำหนด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. ประโยชน์ของการใช้ช่วงวัน
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ประโยชน์:</strong></p>
                                <ul>
                                    <li><strong>เทรนด์ล่าสุด:</strong> ดูการเปลี่ยนแปลงในช่วงเวลาที่กำหนด</li>
                                    <li><strong>ผู้เล่นใหม่:</strong> วิเคราะห์พฤติกรรมผู้เล่นใหม่</li>
                                    <li><strong>การเปรียบเทียบ:</strong> เปรียบเทียบ 7 vs 30 vs 90 วัน</li>
                                    <li><strong>การวางแผน:</strong> วางแผนกิจกรรมตามเทรนด์</li>
                                    <li><strong>Performance:</strong> Query เร็วขึ้นเพราะข้อมูลน้อยลง</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ทุกการวิเคราะห์ใช้ช่วงวัน</li>
                                <li>✅ SQL Queries ใช้ DATEADD</li>
                                <li>✅ sqlsrv_prepare สำหรับ parameters</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ หัวข้อแสดงช่วงวัน</li>
                                <li>✅ ข้อมูลแม่นยำตามช่วงเวลา</li>
                                <li>✅ ปุ่มเลือก 7/30/90 วัน</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openAnalytics7() {
    window.open('?url=manager_charecter/character-analytics&days=7', '_blank');
}

function openAnalytics30() {
    window.open('?url=manager_charecter/character-analytics&days=30', '_blank');
}

function openAnalytics90() {
    window.open('?url=manager_charecter/character-analytics&days=90', '_blank');
}

function refreshPage() {
    location.reload();
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
