-- Database setup for Item Sending System
-- Run this SQL to create the necessary tables

-- Table for storing item send requests
CREATE TABLE IF NOT EXISTS `item_sends` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `player_username` varchar(50) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_name` varchar(255) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `send_method` enum('inventory','mail','warehouse') NOT NULL DEFAULT 'mail',
  `upgrade_level` tinyint(4) NOT NULL DEFAULT 0,
  `craft_level` tinyint(4) NOT NULL DEFAULT 0,
  `extreme` tinyint(4) NOT NULL DEFAULT 0,
  `divine` tinyint(4) NOT NULL DEFAULT 0,
  `item_code` varchar(50) DEFAULT NULL,
  `options_code` varchar(100) DEFAULT NULL,
  `send_notification` tinyint(1) NOT NULL DEFAULT 0,
  `log_transaction` tinyint(1) NOT NULL DEFAULT 1,
  `admin_username` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `status` enum('pending','sent_to_inventory','sent_to_mail','sent_to_warehouse','failed','cancelled') NOT NULL DEFAULT 'pending',
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_player_id` (`player_id`),
  KEY `idx_player_username` (`player_username`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_admin_username` (`admin_username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for admin action logs
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_username` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `target_player` varchar(50) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_admin_username` (`admin_username`),
  KEY `idx_action` (`action`),
  KEY `idx_target_player` (`target_player`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for item database (if not exists)
CREATE TABLE IF NOT EXISTS `items` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `full_id` varchar(50) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample items (optional)
INSERT IGNORE INTO `items` (`id`, `name`, `full_id`, `category`, `type`) VALUES
(1, 'Sword of Beginner', '00000001', 'Weapon', 'Sword'),
(2, 'Magic Sword', '00000002', 'Weapon', 'Sword'),
(3, 'Health Potion', '00000003', 'Consumable', 'Potion'),
(4, 'Mana Potion', '00000004', 'Consumable', 'Potion'),
(5, 'Beginner Armor', '00000005', 'Armor', 'Body'),
(100, 'Test Item', '00000100', 'Test', 'Test');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_item_sends_player_status` ON `item_sends` (`player_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_admin_logs_admin_date` ON `admin_logs` (`admin_username`, `created_at`);

-- Create view for recent item sends
CREATE OR REPLACE VIEW `recent_item_sends` AS
SELECT 
    s.id,
    s.player_username,
    s.item_name,
    s.quantity,
    s.send_method,
    s.admin_username,
    s.created_at,
    s.status,
    i.name as item_db_name
FROM item_sends s
LEFT JOIN items i ON s.item_id = i.id
ORDER BY s.created_at DESC
LIMIT 100;

-- Create view for admin activity summary
CREATE OR REPLACE VIEW `admin_activity_summary` AS
SELECT 
    admin_username,
    COUNT(*) as total_actions,
    COUNT(CASE WHEN action = 'Send Item' THEN 1 END) as items_sent,
    MAX(created_at) as last_activity,
    DATE(created_at) as activity_date
FROM admin_logs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY admin_username, DATE(created_at)
ORDER BY last_activity DESC;
