<?php $zpanel->checkSession(true); ?>
<?php $userid = $userLogin->recUserAccount('UserNum', $conn); ?>
<?php if ($zpanel->getConfigByValue('CashGiff', 'value', $conn) == '0'){ echo '<div class="alert alert-info fade in nomargin">
										<button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
										<h4>Under Construction!</h4>
										<strong>Under Construction !</strong> หน้าเว็บนี้ ? <a href="" class="alert-link">ปิดปรับปรุงขั่วคราว</a> ขออภัยในความสดวก.
										<p>
                                            <a href="home.php" class="btn btn-info mt-xs mb-xs">Yes, ไปหน้าแรก</a>
											<button class="btn btn-default mt-xs mb-xs" type="button">Not convinced yet</button>
										</p>
                                    </div>'; }else{; ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@9"></script>

<header class="page-header">
    <h2>Voucher Cash</h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.html">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Store</span></li>
            <li><span>Voucher</span></li>
        </ol>

        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<!-- start: page -->
<section class="panel">
<div class="row">
    <div class="pricing-table">
        <div class="col-lg-3 col-sm-6">
                <div class="plan most-popular">
                    <div class="plan-ribbon-wrapper">
                        <div class="plan-ribbon">เบาๆ</div>
                    </div>
                    <?php 
                                        $percash = 1000;
                                        $percashc = $percash-($percash*7/100);
                                    ?>
                    <h3>Basic<span><?php echo $percash; ?></span></h3>
                    <button type="button" class="mb-xs mt-xs mr-xs btn btn-primary"
                        <?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo "disabled"; }else {echo "";} ?>
                        data-toggle="modal" data-target="#VModalPack1">Packet</button>
                    <ul>
                        <li><b>แคสทั้งหมด
                                <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?></b>
                            Cash</li>
                        <li><b>ค่าใช้จ่าย 7</b> %</li>
                        <li><b>ยอดที่ได้รับ</b>
                            <?php  echo number_format($percashc); ?> Voucher</li>
                        <li><b>คงเหลือ</b>
                            <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)-$percash); ?> Cash</li>
                        <li>
                            <p class="text-danger">
                                (<?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo 'แคสไม่พอ'; } else {echo 'แลกได้';} ?>)
                            </p>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="pricing-table">
            <div class="col-lg-3 col-sm-6">
                    <div class="plan most-popular">
                        <div class="plan-ribbon-wrapper">
                            <div class="plan-ribbon">ใช้จ่ายคล่อง</div>
                        </div>
                        <?php 
                                        $percash = 2000;
                                        $percashc = $percash-($percash*7/100);
                                    ?>
                        <h3>Pro<span><?php echo $percash; ?></span></h3>
                        <button type="button" class="mb-xs mt-xs mr-xs btn btn-primary"
                            <?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo "disabled"; }else {echo "";} ?>
                            data-toggle="modal" data-target="#VModalPack2">Packet</button>
                        <ul>
                            <li><b>แคสทั้งหมด
                                    <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?></b>
                                Cash</li>
                            <li><b>ค่าใช้จ่าย 7</b> %</li>
                            <li><b>ยอดที่ได้รับ</b>
                                <?php  echo number_format($percashc); ?> Voucher</li>
                            <li><b>คงเหลือ</b>
                                <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)-$percash); ?> Cash
                            </li>
                            <li>
                                <p class="text-danger">
                                    (<?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo 'แคสไม่พอ'; } else {echo 'แลกได้';} ?>)
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="pricing-table">
                <div class="col-lg-3 col-sm-6">
                        <div class="plan most-popular">
                            <div class="plan-ribbon-wrapper">
                                <div class="plan-ribbon">เป็นที่ต้องการ</div>
                            </div>
                            <?php 
                                        $percash = 5000;
                                        $percashc = $percash-($percash*7/100);
                                    ?>
                            <h3>Pro<span><?php echo $percash; ?></span></h3>
                            <button type="button" class="mb-xs mt-xs mr-xs btn btn-primary"
                                <?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo "disabled"; }else {echo "";} ?>
                                data-toggle="modal" data-target="#VModalPack3">Packet</button>
                            <ul>
                                <li><b>แคสทั้งหมด
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?></b>
                                    Cash</li>
                                <li><b>ค่าใช้จ่าย 7</b> %</li>
                                <li><b>ยอดที่ได้รับ</b>
                                    <?php  echo number_format($percashc); ?> Voucher</li>
                                <li><b>คงเหลือ</b>
                                    <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)-$percash); ?>
                                    Cash</li>
                                <li>
                                    <p class="text-danger">
                                        (<?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo 'แคสไม่พอ'; }else {echo 'แลกได้';} ?>)
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="pricing-table">
                    <div class="col-lg-3 col-sm-6">
                            <div class="plan most-popular">
                                <div class="plan-ribbon-wrapper">
                                    <div class="plan-ribbon">รวย</div>
                                </div>
                                <?php 
                                        $percash = 10000;
                                        $percashc = $percash-($percash*7/100);
                                    ?>
                                <h3>premium<span><?php echo $percash; ?></span></h3>
                                <button type="button" class="mb-xs mt-xs mr-xs btn btn-primary"
                                    <?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo "disabled"; }else {echo "";} ?>
                                    data-toggle="modal" data-target="#VModalPack4">Packet</button>
                                <ul>
                                    <li><b>แคสทั้งหมด
                                            <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?></b>
                                        Cash</li>
                                    <li><b>ค่าใช้จ่าย 7</b> %</li>
                                    <li><b>ยอดที่ได้รับ</b>
                                        <?php  echo number_format($percashc); ?> Voucher</li>
                                    <li><b>คงเหลือ</b>
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)-$percash); ?>
                                        Cash</li>
                                    <li>
                                        <p class="text-danger">
                                            (<?php if (number_format($userLogin->recUserGameCash('Cash', $conn)-$percash) < 0 ){ echo 'แคสไม่พอ'; } else {echo 'แลกได้';} ?>)
                                        </p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
          
                </section>

                <!-- reward type modal -->
                <div class="modal fade" id="VModalPack1" tabindex="-1" role="dialog" aria-labelledby="RewardModalLabel"
                    aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                                <?php $pack = 1;
                      $packc = 1000;
                ?>
                                <h4 class="modal-title" id="MyRewardModalLabel">ยืนยันข้อมูล Pack <?php echo $packc; ?>
                                </h4>
                            </div>
                            <form method="post" name="j_select_Voucher" action="">
                                <div class="modal-body">
                                    <div class="col-lg-12 j_alert"></div>

                                    <p class="text-info"> Cash ทั้งหมดที่มีอยู่
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?> Cash
                                    </p>
                                    <p class="text-danger">#ระบบแลกแคส จะหักค่าใช้จ่าย 7%</p>
                                    <p class="text-danger">#ถ้าเกิดแลกผิดกรุณาโดยไม่ได้ตั้งใจกรุณาแจ้ง GM ตรวจสอบให้</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default"
                                        data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                                    <input type="hidden" name="userid" value="<?php echo $userid; ?>">
                                    <input type="hidden" name="packs" value="<?php echo $pack; ?>">
                                    <input type="submit" class="btn btn-primary" value="ส่งข้อมูลถึง GM">
                                    <img src="assets/images/loading/loader.gif" class="load"
                                        alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
                <!-- reward type modal -->
                <div class="modal fade" id="VModalPack2" tabindex="-1" role="dialog" aria-labelledby="RewardModalLabel"
                    aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                                <?php $pack = 2;
                      $packc = 2000;
                ?>
                                <h4 class="modal-title" id="MyRewardModalLabel">ยืนยันข้อมูล Pack <?php echo $packc; ?>
                                </h4>
                            </div>
                            <form method="post" name="j_select_Voucher" action="">
                                <div class="modal-body">
                                    <div class="col-lg-12 j_alert"></div>

                                    <p class="text-info"> Cash ทั้งหมดที่มีอยู่
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?> Cash
                                    </p>
                                    <p class="text-danger">#ระบบแลกแคส จะหักค่าใช้จ่าย 7%</p>
                                    <p class="text-danger">#ถ้าเกิดแลกผิดกรุณาโดยไม่ได้ตั้งใจกรุณาแจ้ง GM ตรวจสอบให้</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default"
                                        data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                                    <input type="hidden" name="userid" value="<?php echo $userid; ?>">
                                    <input type="hidden" name="packs" value="<?php echo $pack; ?>">
                                    <input type="submit" class="btn btn-primary" value="ส่งข้อมูลถึง GM">
                                    <img src="assets/images/loading/loader.gif" class="load"
                                        alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
                <!-- reward type modal -->
                <div class="modal fade" id="VModalPack3" tabindex="-1" role="dialog" aria-labelledby="RewardModalLabel"
                    aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                                <?php $pack = 3;
                      $packc = 3000;
                ?>
                                <h4 class="modal-title" id="MyRewardModalLabel">ยืนยันข้อมูล Pack <?php echo $packc; ?>
                                </h4>
                            </div>
                            <form method="post" name="j_select_Voucher" action="">
                                <div class="modal-body">
                                    <div class="col-lg-12 j_alert"></div>

                                    <p class="text-info"> Cash ทั้งหมดที่มีอยู่
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?> Cash
                                    </p>
                                    <p class="text-danger">#ระบบแลกแคส จะหักค่าใช้จ่าย 7%</p>
                                    <p class="text-danger">#ถ้าเกิดแลกผิดกรุณาโดยไม่ได้ตั้งใจกรุณาแจ้ง GM ตรวจสอบให้</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default"
                                        data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                                    <input type="hidden" name="userid" value="<?php echo $userid; ?>">
                                    <input type="hidden" name="packs" value="<?php echo $pack; ?>">
                                    <input type="submit" class="btn btn-primary" value="ส่งข้อมูลถึง GM">
                                    <img src="assets/images/loading/loader.gif" class="load"
                                        alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
                <!-- reward type modal -->
                <div class="modal fade" id="VModalPack4" tabindex="-1" role="dialog" aria-labelledby="RewardModalLabel"
                    aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                                <?php $pack = 4;
                      $packc = 4000;
                ?>
                                <h4 class="modal-title" id="MyRewardModalLabel">ยืนยันข้อมูล Pack <?php echo $packc; ?>
                                </h4>
                            </div>
                            <form method="post" name="j_select_Voucher" action="">
                                <div class="modal-body">
                                    <div class="col-lg-12 j_alert"></div>

                                    <p class="text-info"> Cash ทั้งหมดที่มีอยู่
                                        <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?> Cash
                                    </p>
                                    <p class="text-danger">#ระบบแลกแคส จะหักค่าใช้จ่าย 7%</p>
                                    <p class="text-danger">#ถ้าเกิดแลกผิดกรุณาโดยไม่ได้ตั้งใจกรุณาแจ้ง GM ตรวจสอบให้</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default"
                                        data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                                    <input type="hidden" name="userid" value="<?php echo $userid; ?>">
                                    <input type="hidden" name="packs" value="<?php echo $pack; ?>">
                                    <input type="submit" class="btn btn-primary" value="ส่งข้อมูลถึง GM">
                                    <img src="assets/images/loading/loader.gif" class="load"
                                        alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
                <?php } ?>

              