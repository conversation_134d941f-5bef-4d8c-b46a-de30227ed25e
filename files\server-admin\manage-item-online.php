<?php $user->restrictionUser(true, $conn); ?>


</script>
<header class="page-header">
    <h2>ItemManager Online</h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span><?php echo PT_MANAGEPLAYERS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
<div class="col-lg-4">
<section class="panel">
    <header class="panel-heading">
        <div class="panel-actions">
            <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
            <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
        </div>

        <h2 class="panel-title">แอดไอเท็ม กิจกรรมผู้เล่น</h2>
    </header>
    <div class="panel-body">
        <div class="col-lg-12">
 
            <?php
                
                if (isset($_POST['btn_savechange'])) {
                    // variable
                    $inputonline = strip_tags(trim($_POST['input_online']));
                    $inputitem = strip_tags(trim($_POST['input_Item']));
                    $inputOption = strip_tags(trim($_POST['input_Option']));
                    $inputDur = strip_tags(trim($_POST['input_Dur']));

                    // condition
                    if (empty($inputitem)) {
                        $returnWarning = "โปรดกรอกข้อมูล รหัสไอเท็ม"; 
                    } else {
                    if (empty($inputOption)){
                            $inputOption = 0;
                    }

                            $adminid = $userLogin->recUserAccount('ID', $conn);
                            $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                            $dateNow = date('Y-m-d H:i:s');
                            $CashItemID = "EXECUTE WEB_Cabal_tool_giveitemtoall ?, ?, ?, ?";
                            $CashItemIDParams = array($inputitem,$inputOption,$inputDur,$inputonline);
                            $CashItemIDQuery = sqlsrv_query($conn, $CashItemID, $CashItemIDParams);
                            
                            if ($inputonline == 0){
                                 $inputonlineText = 'All account';
                            }
                            if ($inputonline == 1){
                                 $inputonlineText = 'Online Only';
                            }
                                if ($CashItemIDQuery) {
                                        //unset($UserNum, $inputPassword, $inputRePassword, $inputEmail, $inputphone);
                                        $zpanel->generateWebLog($conn, '3', $adminidx, 'แอดไอเท็มกิจกรรม', "ให้: {$inputonlineText} รหัสไอเท็ม : {$inputitem} ออฟชั่น : {$inputOption} Dur : {$inputDur}");
                                        $returnSuccess = "SUCCESS :: ระบบแอดไอเท็มเรียบร้อยแล้ว";
                                } else {
                                        $returnError = "ERROR :: ระบบผิดพลาด";
                                }
                            
                    }
                }
                ?>
            <?php if (isset($returnSuccess)) { ?>
            <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
            <?php } elseif (isset($returnWarning)) { ?>
            <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
            <?php } elseif (isset($returnError)) { ?>
            <div class="alert alert-danger"><?php echo $returnError; ?></div>
            <?php } ?>

            <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                <div class="form-group">
                <label for="input_Dur" class="control-label">กิจกรรมที่จะทำ: </label>
                <select data-plugin-selectTwo name="input_online" id="input_online" pattern="[0-9]+$"
                        class="form-control populate placeholder" 
                        data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                        <optgroup label="เลือก.">
                            <option value="1">Online Only</option>
                            <option value="0">All account</option>
                        </optgroup>
                    </select>
                </div>
                <div class="form-group">
                    <label for="input_Item" class="control-label">Code Item:</label>
                    <input type="text" name="input_Item" class="form-control" id="input_Item" pattern="[0-9]+$" placeholder="รหัสไอเท็ม"
                        value="<?php if (isset($inputitem)) echo $inputitem; ?>">
                    <span
                        class="help-block text-red"><small>รหัสไอเท็ม</small></span>
                </div>
                <div class="form-group">
                    <label for="Option" class="control-label">Code Option: </label>
                    <input type="text" name="input_Option" class="form-control" id="input_Option" pattern="[0-9]+$" placeholder="ออฟชั่น"
                        value="<?php if (isset($inputOption)) echo $inputOption; ?>">
                    <span
                        class="help-block text-red"><small>ออฟชั่นไอเท็ม</small></span>
                </div>
                <div class="form-group">
                    <label for="input_Dur" class="control-label">DurationIdx: </label>
                    <select data-plugin-selectTwo name="input_Dur" id="input_Dur" pattern="[0-9]+$"
                        class="form-control populate placeholder" 
                        data-plugin-options='{ "placeholder": "Select Dur", "allowClear": true }'>
                        <optgroup label="อายุ ชม.">
                            <option value="31">ถาวร</option>
                            <option value="1">1 ชม.</option>
                            <option value="2">2 ชม.</option>
                            <option value="3">3 ชม.</option>
                            <option value="4">4 ชม.</option>
                            <option value="5">5 ชม.</option>
                            <option value="6">6 ชม.</option>
                            <option value="7">10 ชม.</option>
                            <option value="8">12 ชม</option>
                            <option value="9">1 วัน</option>
                            <option value="10">3 วัน</option>
                            <option value="11">5 วัน</option>
                            <option value="12">7 วัน</option>
                            <option value="13">10 วัน</option>
                            <option value="14">14 วัน</option>
                            <option value="15">15 วัน</option>
                            <option value="16">20 วัน</option>
                            <option value="17">30 วัน</option>
                            <option value="18">45 วัน</option>
                            <option value="19">60 วัน</option>
                            <option value="20">90 วัน</option>
                            <option value="21">100 วัน</option>
                            <option value="22">120 วัน</option>
                            <option value="23">180 วัน</option>
                            <option value="24">270 วัน</option>
                            <option value="25">365 วัน</option>
                        </optgroup>
                    </select>
                    <span
                        class="help-block text-red"><small>เวลาหมดอายุไอเท็ม</small></span>
                </div>
                <div class="form-group">
                    <input type="submit" name="btn_savechange"  class="btn btn-block btn-success"
                        value="แอดไอเท็ม">
                </div>
            </form>
        </div>
    </div>
</section>
</div>


<div class="col-lg-8">
<section class="panel">
    <header class="panel-heading">
        <div class="panel-actions">
            <a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
            <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
        </div>

        <h2 class="panel-title">ไอเท็มแอดล่าสุด</h2>
    </header>
    <div class="panel-body">
        <div class="col-lg-12">
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>UserNum</th>
                                    <th>ID</th>
                                    <th>ItemKindIdx</th>
                                    <th>ItemOpt</th>
                                    <th>DurationIdx</th>
                                    <th>RegDate</th>
                                    <th>IsUse</th>
                                    <th>UseDate</th>
                                    <th>จัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_CCA ."].[dbo].MyCashItem ORDER BY id DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);


                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                                        
                                            $selectauth = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum  = '".$resLastUsers['UserNum']."'";
                                            $selectauthParam = array();
                                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['Id']; ?></td>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $selectauthFetch['ID']; ?></td>
                                            <td><?php echo $resLastUsers['ItemKindIdx']; ?></td>
                                            <td><?php echo $resLastUsers['ItemOpt']; ?></td>
                                            <td><?php echo $resLastUsers['DurationIdx']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['RegDate'])); ?></td>
                                            <td><?php echo $resLastUsers['IsUse']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['UseDate'])); ?></td>
                                            <td>ลบ</td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                        </div>
  
    </div>
</section>
</div>
<script>

       

