<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-line"></i> ทดสอบการแสดงข้อมูลจริงสำหรับ Admin
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไข onlineCount ให้แสดงข้อมูลจริงแทน Random</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> onlineCount แสดงข้อมูลจริงจาก database แทนที่จะเป็นเลข random
                </div>
                
                <h5>🔧 การแก้ไขที่ทำ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ ก่อนแก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>document.getElementById('onlineCount').textContent = '<?php echo rand(50, 200); ?>'; // Placeholder</code></pre>
                                <p class="mb-0"><strong>ปัญหา:</strong> แสดงเลข random ไม่ใช่ข้อมูลจริง</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ หลังแก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>// แสดงจำนวนผู้เล่นออนไลน์จริงจาก database
document.getElementById('onlineCount').textContent = stats.online_characters.toLocaleString();</code></pre>
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> แสดงข้อมูลจริงจาก API</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 ข้อมูลจริงจาก Database</h5>
                <?php
                // ดึงข้อมูลจริงจาก database
                try {
                    // ผู้เล่นออนไลน์จริง
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 1";
                    $result = sqlsrv_query($conn, $sql);
                    $onlineCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $onlineCount = $row['count'];
                    }
                    
                    // ตัวละครทั้งหมด
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    $result = sqlsrv_query($conn, $sql);
                    $totalCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $totalCount = $row['count'];
                    }
                    
                    // สร้างวันนี้
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
                    $result = sqlsrv_query($conn, $sql);
                    $todayCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $todayCount = $row['count'];
                    }
                    
                    // เลเวลเฉลี่ย
                    $sql = "SELECT AVG(CAST(LEV AS FLOAT)) as avg_level FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE LEV > 0";
                    $result = sqlsrv_query($conn, $sql);
                    $avgLevel = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $avgLevel = round($row['avg_level'], 1);
                    }
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'>";
                    echo "<div class='card text-center border-success'>";
                    echo "<div class='card-header bg-success text-white'><h6 class='mb-0'>ผู้เล่นออนไลน์</h6></div>";
                    echo "<div class='card-body'>";
                    echo "<h3 class='text-success'>" . number_format($onlineCount) . "</h3>";
                    echo "<small>คนออนไลน์จริง</small>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-3'>";
                    echo "<div class='card text-center border-primary'>";
                    echo "<div class='card-header bg-primary text-white'><h6 class='mb-0'>ตัวละครทั้งหมด</h6></div>";
                    echo "<div class='card-body'>";
                    echo "<h3 class='text-primary'>" . number_format($totalCount) . "</h3>";
                    echo "<small>ตัวละครทั้งหมด</small>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-3'>";
                    echo "<div class='card text-center border-info'>";
                    echo "<div class='card-header bg-info text-white'><h6 class='mb-0'>สร้างวันนี้</h6></div>";
                    echo "<div class='card-body'>";
                    echo "<h3 class='text-info'>" . number_format($todayCount) . "</h3>";
                    echo "<small>ตัวละครใหม่วันนี้</small>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-3'>";
                    echo "<div class='card text-center border-warning'>";
                    echo "<div class='card-header bg-warning text-dark'><h6 class='mb-0'>เลเวลเฉลี่ย</h6></div>";
                    echo "<div class='card-body'>";
                    echo "<h3 class='text-warning'>" . $avgLevel . "</h3>";
                    echo "<small>เลเวลเฉลี่ยทั้งหมด</small>";
                    echo "</div></div></div>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบ API Real-time</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testLiveStats()">
                        <i class="fal fa-chart-line"></i> ทดสอบ Live Stats API
                    </button>
                    <button class="btn btn-info" onclick="testOnlineStats()">
                        <i class="fal fa-users"></i> ทดสอบ Online Stats API
                    </button>
                    <button class="btn btn-success" onclick="openCharacterMonitor()">
                        <i class="fal fa-external-link"></i> เปิด Character Monitor
                    </button>
                    <button class="btn btn-warning" onclick="compareData()">
                        <i class="fal fa-balance-scale"></i> เปรียบเทียบข้อมูล
                    </button>
                </div>
                
                <div id="api-test-results"></div>
                
                <h5 class="mt-4">📋 วิธีการทำงานใหม่</h5>
                <div class="accordion" id="workflowAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. การโหลดข้อมูลเริ่มต้น
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#workflowAccordion">
                            <div class="card-body">
                                <p><strong>เมื่อโหลดหน้า Character Monitor:</strong></p>
                                <ol>
                                    <li>แสดง "กำลังโหลด..." ในช่อง onlineCount</li>
                                    <li>เรียก refreshData() ทันที</li>
                                    <li>ดึงข้อมูลจาก API live_stats</li>
                                    <li>อัพเดท onlineCount ด้วยข้อมูลจริง</li>
                                    <li>Log ข้อมูลใน Console สำหรับ admin</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. การอัพเดทอัตโนมัติ
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#workflowAccordion">
                            <div class="card-body">
                                <p><strong>เมื่อเปิด Auto Refresh (ทุก 10 วินาที):</strong></p>
                                <ol>
                                    <li>เรียก API live_stats ทุก 10 วินาที</li>
                                    <li>อัพเดท onlineCount ด้วยข้อมูลล่าสุด</li>
                                    <li>Log การอัพเดทใน Console</li>
                                    <li>แสดงเวลาอัพเดทล่าสุด</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. การตรวจสอบข้อมูล
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#workflowAccordion">
                            <div class="card-body">
                                <p><strong>สำหรับ Admin สามารถตรวจสอบ:</strong></p>
                                <ul>
                                    <li>เปิด Console (F12) เพื่อดู log การอัพเดท</li>
                                    <li>ตรวจสอบ API response ใน Network tab</li>
                                    <li>เปรียบเทียบข้อมูลกับ database โดยตรง</li>
                                    <li>ดูเวลาอัพเดทล่าสุดในหน้า Monitor</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ลบ random number ออก</li>
                                <li>✅ ใช้ข้อมูลจาก API จริง</li>
                                <li>✅ เพิ่ม logging สำหรับ admin</li>
                                <li>✅ โหลดข้อมูลเริ่มต้นทันที</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ อัพเดทข้อมูลทุก 10 วินาที</li>
                                <li>✅ แสดงสถานะ "กำลังโหลด..."</li>
                                <li>✅ ข้อมูลแม่นยำสำหรับ admin</li>
                                <li>✅ ไม่มีเลข random อีกต่อไป</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testLiveStats() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Live Stats API...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=live_stats');
        const data = await response.json();
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-chart-line"></i> Live Stats API Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
        
        if (data.success && data.data) {
            html += '<p><strong>Online Characters:</strong> ' + (data.data.online_characters || 0).toLocaleString() + '</p>';
            html += '<p><strong>Total Characters:</strong> ' + (data.data.total_characters || 0).toLocaleString() + '</p>';
            html += '<p><strong>Created Today:</strong> ' + (data.data.created_today || 0).toLocaleString() + '</p>';
            html += '<p><strong>Average Level:</strong> ' + (data.data.avg_level || 0) + '</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function testOnlineStats() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ Online Stats API...</div>';
    
    try {
        const response = await fetch('files/manager_charecter/api/character-data.php?action=online_stats');
        const data = await response.json();
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-users"></i> Online Stats API Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
        
        if (data.success && data.data) {
            html += '<p><strong>Total Online:</strong> ' + (data.data.total_online || 0).toLocaleString() + '</p>';
            html += '<p><strong>Last Updated:</strong> ' + (data.data.last_updated || 'N/A') + '</p>';
            
            if (data.data.by_world && data.data.by_world.length > 0) {
                html += '<p><strong>By World:</strong></p><ul>';
                data.data.by_world.forEach(world => {
                    html += '<li>World ' + world.world + ': ' + world.count + ' คน</li>';
                });
                html += '</ul>';
            }
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

async function compareData() {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังเปรียบเทียบข้อมูล...</div>';
    
    try {
        const [liveResponse, onlineResponse] = await Promise.all([
            fetch('files/manager_charecter/api/character-data.php?action=live_stats'),
            fetch('files/manager_charecter/api/character-data.php?action=online_stats')
        ]);
        
        const liveData = await liveResponse.json();
        const onlineData = await onlineResponse.json();
        
        let html = '<div class="alert alert-success">';
        html += '<h6><i class="fal fa-balance-scale"></i> เปรียบเทียบข้อมูลจาก API</h6>';
        html += '<div class="table-responsive">';
        html += '<table class="table table-sm">';
        html += '<thead><tr><th>ข้อมูล</th><th>Live Stats API</th><th>Online Stats API</th><th>สถานะ</th></tr></thead>';
        html += '<tbody>';
        
        const liveOnline = liveData.data?.online_characters || 0;
        const onlineTotal = onlineData.data?.total_online || 0;
        const match = liveOnline === onlineTotal;
        
        html += '<tr>';
        html += '<td>ผู้เล่นออนไลน์</td>';
        html += '<td>' + liveOnline.toLocaleString() + '</td>';
        html += '<td>' + onlineTotal.toLocaleString() + '</td>';
        html += '<td>' + (match ? '<span class="badge badge-success">ตรงกัน</span>' : '<span class="badge badge-warning">แตกต่าง</span>') + '</td>';
        html += '</tr>';
        
        html += '</tbody></table></div>';
        html += '<p class="mb-0"><strong>ผลการเปรียบเทียบ:</strong> ' + (match ? 'ข้อมูลตรงกันทั้งสอง API' : 'ข้อมูลแตกต่างกัน อาจมีความล่าช้าในการอัพเดท') + '</p>';
        html += '</div>';
        
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Compare Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
