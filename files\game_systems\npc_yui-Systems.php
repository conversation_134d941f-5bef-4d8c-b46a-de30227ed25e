<?php $zpanel->checkSession(true); ?>
<style>
select,
textarea[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: none;
    border-radius: 10px;
    background: #dfe6e9;
    font-size: 16px;
    color: #2d3436;
    margin-bottom: 20px;
    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
}

select:focus,
textarea[type="text"]:focus {
    outline: none;
    background: #fff;
    box-shadow: 0 0 0 2px #00cec9;
}


.special-char-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 5px;
    margin-bottom: 24px;
}

.special-char-buttons button {
    padding: 10px 10px;
    font-size: 14px;
    background: #636e72;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease;
}

.special-char-buttons button:hover {
    background: #00cec9;
    color: #2d3436;
    transform: scale(1.05);
}

button.submit {
    width: 100%;
    padding: 14px;
    font-size: 18px;
    background: linear-gradient(135deg, #00cec9, #0984e3);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

button.submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.message {
    margin-top: 20px;
    padding: 14px;
    border-radius: 10px;
    font-family: monospace;
    white-space: pre-line;
}

.message.success {
    background: #2ecc71;
    color: white;
    border-left: 6px solid #27ae60;
}

.message.error {
    background: #e74c3c;
    color: white;
    border-left: 6px solid #c0392b;
}

.slot-button {
    width: 50px;
    height: 50px;
    margin: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0;
}

.btn-success {
    color: white;
}

#slotGrid {
    display: grid;
    grid-template-columns: repeat(8, 50px);
    gap: 4px;
}

.slot-button.drag-over {
    border: 2px dashed #007bff;
    background-color: #e9f5ff;
}

</style>


<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> EventData Systems
        <small>
            แก้ไข อัพเดท เพิ่ม ลบ ข้อมูลระบบ event Data
        </small>
        <div class="message" id="resultNotice" style="display:none;"></div>
    </h1>
</div>
<div class="row">
    <div class="col-lg-6 col-xl-4 order-lg-1 order-xl-1">
        <!-- profile summary -->
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                            Event Data
                        </h2>
                    </div>
                </div>
                <div class="col-12">
                    <div class="p-3">
                        <label for="charSelect">เลือก Event:</label>
                            <div class="form-wrapper">
                                <select id="shopSelector"></select>
                                <button id="toggleBtn" class="btn btn-secondary">Toggle Active</button>
                                <button class="btn btn-success" onclick="createShop()">Create New Shop</button>
                            </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                            Shop Slot
                        </h2>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-3">
                        <div id="shopStatus" class="mb-3 text-muted"></div>
                        <div id="slotGrid"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <div class="col-lg-12 col-xl-5 order-lg-3 order-xl-2">
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                            ItemPrice Tables
                        </h2>
                    </div>
                </div>
                <div class="col-12">
                    <div class="p-2">
                        <table id="DT-basic-example-100" class="table table-bordered table-hover table-striped w-100">
                        </table>
                    </div>
                </div>
            </div>
        </div>


        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h5>Event <i>logs (AJAX Calls)</i></h5>
                    </div>
                </div>
                <div class="col-12">
                    <div class="p-2">
                        <div id="app-eventlog" class="alert alert-primary p-1 h-auto my-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 col-xl-3 order-lg-2 order-xl-3">
        <!-- rating -->
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                           แก้ไขชื่อ Event
                        </h2>
                    </div>
                </div>
                <div class="col-12">
                    <div class="p-3">
                            <?php 
                                $Event = [];
                                $sql = "SELECT * FROM ".DATABASE_EDATA.".dbo.cabal_ems_event_table";
                                $stmt = sqlsrv_query($conn, $sql);
                                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                    $Event[] = [
                                        'EventID' => $row['EventID'],
                                        'EventType' => $row['EventType'],
                                        'EndDate' => $row['EndDate'],
                                        'Name' => $row['Name'],
                                        'UseFlag' => $row['UseFlag'],
                                        'worldIndex' => $row['worldIndex'],
                                        'npcIndex' => $row['npcIndex'],
                                        'ServerIndex' => $row['ServerIndex']
                                    ];
                                }
                            
                            if (count($Event)): 
                                    ?>
                        <label for="charSelect">เลือก Event:</label>
                        <select id="charSelect" onchange="onCharSelectChange()">
                            <option value="">-- กรุณาเลือก --</option>
                            <?php foreach ($Event as $Evid): ?>
                            <option value="<?= $Evid['EventID'] ?>"
                                data-win874="<?= htmlspecialchars($Evid['Name']) ?>">
                                <?= $Evid['EventID'] ?> ->
                                <?= htmlspecialchars($userLogin->thaitrans($Evid['Name'])) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="thaiText">ชื่อใหม่ภาษาไทย:</label>
                        <textarea id="thaiText" type="text" value="" placeholder="พิมพ์ชื่อใหม่ที่นี่"></textarea>
                        <label class="special-char-label">🔣 อักษรพิเศษ:</label>
                        <div class="special-char-buttons">
                            <?php foreach (['|', '*', '๏', '~', '`', '๛', '_', '@'] as $ch): ?>
                            <button type="button" onclick="insertChar('<?= $ch ?>')"><?= $ch ?></button>
                            <?php endforeach; ?>
                        </div>
                        <button class="submit" onclick="sendNameToServer()">🚀 ยืนยันแก้ไข</button>
                        <?php else: ?>
                        <div class="message error">❌ ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้ระบุ
                            ?usernum=
                        </div>
                        <?php endif; ?>

                    </div>
                </div>

            </div>
        </div>
        <!-- skills -->
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                           แก้ไข Script Event
                        </h2>
                    </div>
                </div>
                <div class="col-12">
                    <div class="p-3">
                        <div class="message" id="resultNotice" style="display:none;"></div>
                            <?php 
                                $EventSc = [];
                                $sql = "SELECT * FROM ".DATABASE_EDATA.".dbo.cabal_ems_event_npcscript_table";
                                $stmt = sqlsrv_query($conn, $sql);
                                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                    $EventSc[] = [
                                        'EventID' => $row['EventID'],
                                        'WorldIdx' => $row['WorldIdx'],
                                        'NpcIdx' => $row['NpcIdx'],
                                        'Script1' => $row['Script1'],
                                        'ScriptDesc' => $row['ScriptDesc']
                                    ];
                                }
                            
                            if (count($EventSc)): 
                            ?>
                        <label for="charSelect2">เลือก Event:</label>
                        <select id="charSelect2" onchange="onCharSelectChange()">
                            <option value="">-- กรุณาเลือก --</option>
                            <?php foreach ($EventSc as $EvidSc): ?>
                            <option value="<?= $EvidSc['EventID'] ?>"
                                data-win874="<?= htmlspecialchars($EvidSc['Script1']) ?>">
                                <?= $EvidSc['EventID'] ?> ->
                                <?= htmlspecialchars($userLogin->thaitrans($EvidSc['Script1'])) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="thaiText2">ชื่อใหม่ภาษาไทย:</label>
                        <textarea id="thaiText2" type="text" value="" placeholder="พิมพ์ชื่อใหม่ที่นี่"></textarea>
                        <label class="special-char-label">🔣 อักษรพิเศษ:</label>
                        <div class="special-char-buttons">
                            <?php foreach (['|', '*', '๏', '~', '`', '๛', '_', '@'] as $ch): ?>
                            <button type="button" onclick="insertChar('<?= $ch ?>')"><?= $ch ?></button>
                            <?php endforeach; ?>
                        </div>
                        <button class="submit" onclick="sendNameToServerV2()">🚀 ยืนยันแก้ไข</button>
                        <?php else: ?>
                        <div class="message error">❌ ไม่พบตัวละครใน UserNum นี้ หรือยังไม่ได้ระบุ
                            ?usernum=
                        </div>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Modal -->
<div class="modal fade" id="slotModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">

        <div class="modal-content">
            <form id="slotForm">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Slot <span id="modalSlot"></span></h5>
                    <button type="button" class="close position-absolute pos-top pos-right p-2 m-1 mr-2"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>

                </div>
                <div class="modal-body">
                    <div class="mb-2">
                        <label class="form-label">Item</label>
                        <input list="itemList" id="itemInput" class="form-control" required>
                        <datalist id="itemList"></datalist>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Item Option</label>
                        <input type="number" id="optionInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Duration Index</label>
                        <input type="number" id="durationInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Alz Price</label>
                        <input type="number" id="alzInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">ItemPriceID</label>
                        <input type="number" id="itemPriceInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">point Price</label>
                        <input type="number" id="pointInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Expiration Time</label>
                        <input type="datetime-local" id="expireInput" class="form-control" value="1970-01-01T00:00">
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Daily Limit</label>
                        <input type="number" id="dailyInput" class="form-control" value="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">Weekly Limit</label>
                        <input type="number" id="weeklyInput" class="form-control" value="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger mr-auto" onclick="deleteSlot()">Delete</button>
                    <input type="hidden" id="slotNumber">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    const events = $("#app-eventlog");
    const columnSet = [
        { title: "ID", data: "ID",visible: true, editable: false},
        { title: "ItemPriceID", data: "ItemPriceID"},
        { title: "ItemKindIdx",data: "ItemKindIdx"},
        { title: "ItemOption",data: "ItemOption"},
        { title: "ItemCount",data: "ItemCount"}
    ];
    const myTable = $('#DT-basic-example-100').DataTable({
        
        dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            

        ajax: {
            url: "files/game_systems/class_module/load_item_prices.php",
            dataSrc: function(json) {
                console.log("Response:", json); // ตรวจสอบข้อมูลที่โหลดมา
                return json;
            }
        },

        columns: columnSet,
        select: 'single',
        altEditor: true,
        responsive: true,

        buttons: [{
                extend: 'selected',
                text: '<i class="fal fa-times"></i> ลบ',
                name: 'delete',
                className: 'btn-danger btn-sm mr-1'
            },
            {
                extend: 'selected',
                text: '<i class="fal fa-edit"></i> แก้ไข',
                name: 'edit',
                className: 'btn-primary btn-sm mr-1'
            },
            {
                text: '<i class="fal fa-plus"></i> เพิ่ม',
                name: 'add',
                className: 'btn-success btn-sm mr-1'
            },
            {
                text: '<i class="fal fa-sync"></i>',
                action: function() {
                    myTable.ajax.reload();
                },
                className: 'btn-warning btn-sm'
            }
        ],

        onAddRow: function(dt, rowdata, success, error) {
            $.ajax({
                url: "files/game_systems/class_module/itemprice_add.php",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify(rowdata),
                success: function(response) {
                    console.log("Add response:", response);

                    if (response && response.ID) {
                        rowdata.ID = response.ID;
                    }

                    success(rowdata);
                    events.prepend('<p class="text-success fw-500">Added: ' + JSON
                        .stringify(rowdata, null, 4) + '</p>');
                },
                error: function(xhr, status, err) {
                    console.error("Insert failed:", err);
                    error(err);
                }
            });
        },

        onEditRow: function(dt, rowdata, success, error) {
            $.ajax({
                url: "files/game_systems/class_module/itemprice_update.php",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify(rowdata),
                success: function(response) {
                    console.log("update response", response);

                    if (response && response.ID) {
                        rowdata.ID = response.ID;
                    }

                    success(rowdata);
                    events.prepend('<p class="text-info fw-500">Updated: ' + JSON
                        .stringify(rowdata) + '</p>');
                },
                error: function(xhr) {
                    error(xhr.responseText);
                }
            });
        },

        onDeleteRow: function(dt, rowdata, success, error) {
            $.ajax({
                url: "files/game_systems/class_module/itemprice_delete.php",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify(rowdata),
                success: function(response) {
                    console.log("Delete response:", response);
                    success(rowdata);
                    events.prepend('<p class="text-danger fw-500">Deleted: ' + JSON
                        .stringify(rowdata, null, 4) + '</p>');
                },
                error: function(xhr, status, err) {
                    console.error("Delete failed:", err);
                    error(err);
                }
            });
        }
    });

    // ทำให้ช่อง ID readonly ทุกครั้งที่ฟอร์ม AltEditor เปิดขึ้น
    $('#DT-basic-example-100').on('altEditor.open', function(e, mode, data, row) {
        // ตัวเลือก input ที่อาจจะเป็น id หรือ name ก็ลองปรับตามที่ฟอร์มสร้าง
        $('input[name="ID"]').prop('readonly', true);
    });
});
</script>



<script>
// แผนที่แปลง windows-874 เป็น Unicode
const win874ToUnicodeMap = {
    0xA1: 0x0E01,
    0xA2: 0x0E02,
    0xA3: 0x0E03,
    0xA4: 0x0E04,
    0xA5: 0x0E05,
    0xA6: 0x0E06,
    0xA7: 0x0E07,
    0xA8: 0x0E08,
    0xA9: 0x0E09,
    0xAA: 0x0E0A,
    0xAB: 0x0E0B,
    0xAC: 0x0E0C,
    0xAD: 0x0E0D,
    0xAE: 0x0E0E,
    0xAF: 0x0E0F,
    0xB0: 0x0E10,
    0xB1: 0x0E11,
    0xB2: 0x0E12,
    0xB3: 0x0E13,
    0xB4: 0x0E14,
    0xB5: 0x0E15,
    0xB6: 0x0E16,
    0xB7: 0x0E17,
    0xB8: 0x0E18,
    0xB9: 0x0E19,
    0xBA: 0x0E1A,
    0xBB: 0x0E1B,
    0xBC: 0x0E1C,
    0xBD: 0x0E1D,
    0xBE: 0x0E1E,
    0xBF: 0x0E1F,
    0xC0: 0x0E20,
    0xC1: 0x0E21,
    0xC2: 0x0E22,
    0xC3: 0x0E23,
    0xC4: 0x0E24,
    0xC5: 0x0E25,
    0xC6: 0x0E26,
    0xC7: 0x0E27,
    0xC8: 0x0E28,
    0xC9: 0x0E29,
    0xCA: 0x0E2A,
    0xCB: 0x0E2B,
    0xCC: 0x0E2C,
    0xCD: 0x0E2D,
    0xCE: 0x0E2E,
    0xCF: 0x0E2F,
    0xD0: 0x0E30,
    0xD1: 0x0E31,
    0xD2: 0x0E32,
    0xD3: 0x0E33,
    0xD4: 0x0E34,
    0xD5: 0x0E35,
    0xD6: 0x0E36,
    0xD7: 0x0E37,
    0xD8: 0x0E38,
    0xD9: 0x0E39,
    0xDA: 0x0E3A,
    0xDF: 0x0E3F,
    0xE0: 0x0E40,
    0xE1: 0x0E41,
    0xE2: 0x0E42,
    0xE3: 0x0E43,
    0xE4: 0x0E44,
    0xE5: 0x0E45,
    0xE6: 0x0E46,
    0xE7: 0x0E47,
    0xE8: 0x0E48,
    0xE9: 0x0E49,
    0xEA: 0x0E4A,
    0xEB: 0x0E4B,
    0xEC: 0x0E4C,
    0xED: 0x0E4D,
    0xEE: 0x0E4E,
    0xEF: 0x0E4F,
    0xF0: 0x0E50,
    0xF1: 0x0E51,
    0xF2: 0x0E52,
    0xF3: 0x0E53,
    0xF4: 0x0E54,
    0xF5: 0x0E55,
    0xF6: 0x0E56,
    0xF7: 0x0E57,
    0xF8: 0x0E58,
    0xF9: 0x0E59,
    0xFA: 0x0E5A,
    0xFB: 0x0E5B
};

function win874ToUnicode(str) {
    let result = "";
    for (let i = 0; i < str.length; i++) {
        let code = str.charCodeAt(i);
        if (code >= 0xA1 && code <= 0xFB && win874ToUnicodeMap[code]) {
            result += String.fromCharCode(win874ToUnicodeMap[code]);
        } else {
            result += str[i];
        }
    }
    return result;
}

function onCharSelectChange() {
    const selects = ['charSelect', 'charSelect2'];
    selects.forEach((id) => {
        const select = document.getElementById(id);
        if (!select) return;
        const option = select.options[select.selectedIndex];
        const win874Name = option?.getAttribute('data-win874') || "";
        const targetId = id === 'charSelect' ? 'thaiText' : 'thaiText2';
        document.getElementById(targetId).value = win874ToUnicode(win874Name);
    });
}


function insertChar(Evid) {
    const input = document.getElementById("thaiText");
    const start = input.selectionStart;
    const end = input.selectionEnd;
    const text = input.value;

    input.value = text.slice(0, start) + Evid + text.slice(end);
    input.focus();
    input.setSelectionRange(start + Evid.length, start + Evid.length);
}

function sendNameToServer() {
    const name = document.getElementById("thaiText").value.trim();
    const select = document.getElementById("charSelect");
    const Eventidx = select.value;
    const resultBox = document.getElementById("resultNotice");

    if (!Eventidx) {
        resultBox.innerText = "❌ กรุณาเลือก Event ก่อน";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    if (name === '') {
        resultBox.innerText = "❌ กรุณากรอกชื่อใหม่";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    const form = new FormData();
    form.append("form_type", "event");
    form.append("name_thai", name);
    form.append("Eventidx", Eventidx);

    fetch("files/game_systems/class_module/Yui-update-systems.php", {
            method: "POST",
            body: form
        })
        .then(res => res.text())
        .then(result => {
            resultBox.innerText = result;
            resultBox.className = result.includes("✅") ? "message success" : "message error";
            resultBox.style.display = "block";

            if (result.includes("✅")) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        })
        .catch(err => {
            resultBox.innerText = "❌ เกิดข้อผิดพลาดในการส่งข้อมูล\n" + err;
            resultBox.className = "message error";
            resultBox.style.display = "block";
        });
}

function sendNameToServerV2() {
    const name = document.getElementById("thaiText2").value.trim();
    const select = document.getElementById("charSelect2");
    const Eventidx = select.value;
    const resultBox = document.getElementById("resultNotice");

    if (!Eventidx) {
        resultBox.innerText = "❌ กรุณาเลือก Event ก่อน";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    if (name === '') {
        resultBox.innerText = "❌ กรุณากรอกชื่อใหม่";
        resultBox.className = "message error";
        resultBox.style.display = "block";
        return;
    }

    const form = new FormData();
    form.append("form_type", "npcscript");
    form.append("npc_script_name", name);
    form.append("npc_event_id", Eventidx);

    fetch("files/game_systems/class_module/Yui-update-systems.php", {
            method: "POST",
            body: form
        })
        .then(res => res.text())
        .then(result => {
            resultBox.innerText = result;
            resultBox.className = result.includes("✅") ? "message success" : "message error";
            resultBox.style.display = "block";

            if (result.includes("✅")) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        })
        .catch(err => {
            resultBox.innerText = "❌ เกิดข้อผิดพลาดในการส่งข้อมูล\n" + err;
            resultBox.className = "message error";
            resultBox.style.display = "block";
        });
}
</script>


<script>
let currentEventId = -1;
let currentUseFlag = 0;
let itemDict = {};
let currentSlotData = {};
let itemPrices = {};

function clearPriceFields() {
    // เคลียร์ input หรือค่าที่เกี่ยวกับช่อง item price
    $('#priceField1').val('');
    $('#priceField2').val('');
    // หรือใช้ .find() ในฟอร์มถ้ารวมหลายช่อง
}

async function loadItemPrices() {
    try {
        const res = await fetch('files/game_systems/class_module/load_item_prices.php');
        const data = await res.json();

        if (!Array.isArray(data) || data.length === 0) {
            console.error("No data or invalid data format");
            return;
        }

        itemPrices = data;

        // สมมติว่ามี input 'itemPriceInput' อยู่แล้ว
        const input = document.getElementById('itemPriceInput');
        const currentVal = parseInt(input.value) || 0;

        // ถ้าค่าที่มีอยู่ตรงกับข้อมูลใน itemPrices
        const price = itemPrices.find(p => p.ID === currentVal || p.ItemPriceID === currentVal);
        if (price) {
            updateFieldsFromPrice(price);
        } else {
            clearPriceFields();
        }

    } catch (err) {
        console.error("Failed to load item prices:", err);
    }
}


async function loadShops() {
    try {
        const res = await fetch('files/game_systems/class_module/load_shops.php');
        const text = await res.text();
        console.log("[loadShops] Raw Response:", text);

        const shops = JSON.parse(text);
        const selector = document.getElementById('shopSelector');
        selector.innerHTML = '<option value="">Select Shop</option>';

        shops.forEach(s => {
            const opt = document.createElement('option');
            opt.value = s.id;

            // ✅ decode ภาษาไทย (ถ้ามาจาก windows-874)
            const decodedName = win874ToUnicode(s.name);

            opt.text = `${s.id} - ${decodedName}`;
            selector.appendChild(opt);
        });
    } catch (err) {
        console.error("[loadShops] Error:", err);
    }
}

async function loadItems() {
    try {
        const res = await fetch('files/game_systems/class_module/load_items.php');
        const text = await res.text();
        console.log("[loadItems] Raw Response:", text);

        itemDict = JSON.parse(text);
        const list = document.getElementById('itemList');
        list.innerHTML = '';
        for (const [id, name] of Object.entries(itemDict)) {
            const opt = document.createElement('option');
            opt.value = `${id} - ${name}`;
            list.appendChild(opt);
        }
    } catch (err) {
        console.error("[loadItems] Error:", err);
    }
}

// ฟังก์ชันบันทึก slot (ใช้แทน POST save_slot.php)
async function saveSlot(slot, data) {
    try {
        await fetch('files/game_systems/class_module/save_slot.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                eventId: currentEventId,
                slot,
                itemId: data.itemId,
                itemOption: data.itemOption,
                duration: data.duration,
                PriceAlz: data.PriceAlz,
                ItemPriceID: data.ItemPriceID, // แก้ตรงนี้
                point: data.point,
                expire: data.expire,
                daily: data.daily,
                weekly: data.weekly
            })
        });
    } catch (err) {
        console.error("[saveSlot] Error:", err);
    }
}

// ฟังก์ชันลบ slot ตามเลขช่อง
async function deleteSlotByNumber(slot) {
    try {
        await fetch('files/game_systems/class_module/delete_slot.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                eventId: currentEventId,
                slot
            })
        });
    } catch (err) {
        console.error("[deleteSlotByNumber] Error:", err);
    }
}

// ฟังก์ชันสลับข้อมูล slot แบบปลอดภัย
async function swapSlotData(fromSlot, toSlot) {
    try {
        if (fromSlot === toSlot) return;
        const fromData = currentSlotData[fromSlot];
        const toData = currentSlotData[toSlot] || null;

        if (!fromData) {
            console.warn(`No data in fromSlot ${fromSlot}`);
            return;
        }

        // สลับข้อมูลในตัวแปรก่อน
        currentSlotData[toSlot] = fromData;
        if (toData) {
            currentSlotData[fromSlot] = toData;
        } else {
            delete currentSlotData[fromSlot];
        }

        // ส่งคำสั่งไปยัง server
        await Promise.all([
            saveSlot(toSlot, fromData),
            toData ? saveSlot(fromSlot, toData) : deleteSlotByNumber(fromSlot)
        ]);

        await loadShopItems(currentEventId); // refresh
    } catch (err) {
        console.error("[swapSlotData] Error:", err);
    }
}
async function loadShopItems(eventId) {
    try {
        const res = await fetch(`files/game_systems/class_module/load_shop_items.php?eventId=${eventId}`);
        const text = await res.text();
        console.log("[loadShopItems] Raw Response:", text);

        const data = JSON.parse(text);
        currentUseFlag = parseInt(data.useFlag);
        document.getElementById('toggleBtn').textContent = currentUseFlag ? 'Deactivate' : 'Activate';

        const grid = document.getElementById('slotGrid');
        grid.innerHTML = '';
        currentSlotData = data.items;

        document.getElementById('shopStatus').textContent =
            `Items in use: ${Object.keys(data.items).length}/64`;

        for (let i = 0; i < 128; i++) {
            const btn = document.createElement('button');
            btn.className = 'btn btn-outline-secondary slot-button';
            btn.dataset.slot = i;

            // ✅ เปลี่ยนจาก textContent เป็น innerHTML แสดงเลข slot + itemId
            const itemId = data.items[i]?.itemId ?? '';
            btn.innerHTML = `
                <div style="font-size: 14px; font-weight: bold;">${i}</div>
                <div style="font-size: 11px; color: gray;">${itemId}</div>
            `;

            btn.onclick = () => openSlotModal(i);

            // ทำให้ปุ่มลากได้
            btn.setAttribute('draggable', true);

            btn.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', i);
            });

            btn.addEventListener('dragover', (e) => {
                e.preventDefault();
                btn.classList.add('drag-over');
            });

            btn.addEventListener('dragleave', () => {
                btn.classList.remove('drag-over');
            });

            btn.addEventListener('drop', (e) => {
                e.preventDefault();
                btn.classList.remove('drag-over');

                const fromSlot = parseInt(e.dataTransfer.getData('text/plain'));

                // ✅ รองรับการ drop จาก div ภายในปุ่ม
                const toBtn = e.target.closest('button');
                if (!toBtn) return;

                const toSlot = parseInt(toBtn.dataset.slot);
                if (isNaN(toSlot)) return;

                swapSlotData(fromSlot, toSlot);
            });

            if (data.items[i]) btn.classList.add('btn-success');

            grid.appendChild(btn);
        }
    } catch (err) {
        console.error("[loadShopItems] Error:", err);
    }
}



function openSlotModal(slot) {
    if (currentEventId <= 0) return;
    document.getElementById('slotNumber').value = slot;
    document.getElementById('modalSlot').textContent = slot;

    const slotInfo = currentSlotData[slot];
    if (slotInfo) {
        document.getElementById('itemInput').value = `${slotInfo.itemId} - ${itemDict[slotInfo.itemId] || ''}`;
        document.getElementById('optionInput').value = slotInfo.itemOption;
        document.getElementById('durationInput').value = slotInfo.duration;
        document.getElementById('alzInput').value = slotInfo.PriceAlz;
        document.getElementById('itemPriceInput').value = slotInfo.itemPriceID;
        document.getElementById('pointInput').value = slotInfo.PricePoint;
        document.getElementById('expireInput').value = slotInfo.expire.replace(" ", "T");
        document.getElementById('dailyInput').value = slotInfo.daily;
        document.getElementById('weeklyInput').value = slotInfo.weekly;
    } else {
        document.getElementById('itemInput').value = '';
        document.getElementById('optionInput').value = 0;
        document.getElementById('durationInput').value = 0;
        document.getElementById('alzInput').value = 0;
        document.getElementById('itemPriceInput').value = 0;
        document.getElementById('pointInput').value = 0;
        document.getElementById('expireInput').value = "1970-01-01T00:00";
        document.getElementById('dailyInput').value = 0;
        document.getElementById('weeklyInput').value = 0;
    }

    new bootstrap.Modal(document.getElementById('slotModal')).show();
}

document.getElementById('shopSelector').addEventListener('change', function() {
    currentEventId = parseInt(this.value);
    if (currentEventId > 0) loadShopItems(currentEventId);
});

document.getElementById('slotForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const itemRaw = document.getElementById('itemInput').value;
    const itemId = parseInt(itemRaw.split('-')[0].trim());
    const slot = parseInt(document.getElementById('slotNumber').value);

    const data = {
        eventId: currentEventId,
        slot,
        itemId,
        itemOption: parseInt(document.getElementById('optionInput').value),
        duration: parseInt(document.getElementById('durationInput').value),
        PriceAlz: parseInt(document.getElementById('alzInput').value),
        itemPriceID: parseInt(document.getElementById('itemPriceInput').value),
        point: parseInt(document.getElementById('pointInput').value),
        expire: document.getElementById('expireInput').value.replace("T", " "),
        daily: parseInt(document.getElementById('dailyInput').value),
        weekly: parseInt(document.getElementById('weeklyInput').value)
    };

    await fetch('files/game_systems/class_module/save_slot.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });

    // ✅ ปิด Modal แบบ Bootstrap 4
    $('#slotModal').modal('hide');

    // โหลดข้อมูลใหม่
    loadShopItems(currentEventId);
});



async function deleteSlot() {
    const confirmDelete = confirm("คุณแน่ใจหรือไม่ว่าต้องการลบ slot นี้?");
    if (!confirmDelete) return;

    try {
        const slot = parseInt(document.getElementById('slotNumber').value);
        const res = await fetch('files/game_systems/class_module/delete_slot.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                eventId: currentEventId,
                slot
            })
        });

        const text = await res.text();
        console.log("[deleteSlot] Response:", text);

        // ✅ Bootstrap 4 ใช้ jQuery modal
        $('#slotModal').modal('hide');

        // โหลดข้อมูลใหม่
        loadShopItems(currentEventId);
    } catch (err) {
        console.error("[deleteSlot] Error:", err);
    }
}


document.getElementById('toggleBtn').addEventListener('click', async function() {
    if (currentEventId <= 0) return;
    try {
        const newFlag = currentUseFlag ? 0 : 1;
        const res = await fetch(
            `files/game_systems/class_module/toggle_shop.php?eventId=${currentEventId}&newFlag=${newFlag}`);
        const text = await res.text();
        console.log("[toggle_shop] Response:", text);

        currentUseFlag = newFlag;
        this.textContent = newFlag ? 'Deactivate' : 'Activate';
    } catch (err) {
        console.error("[toggle_shop] Error:", err);
    }
});

async function createShop() {
    const id = prompt("Enter EventID:", "1000");
    const name = prompt("Enter Shop Name:", "New Shop");
    const title = prompt("Enter Shop Title:", "Shop Title");
    const desc = prompt("Enter Description:", "Shop Description");
    if (!id || !name || !title || !desc) return;

    try {
        const res = await fetch('files/game_systems/class_module/create_shop.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id,
                name,
                title,
                desc
            })
        });
        const text = await res.text();
        console.log("[createShop] Response:", text);

        alert('Shop created!');
        loadShops();
    } catch (err) {
        console.error("[createShop] Error:", err);
        alert('Failed to create shop.');
    }
}
document.addEventListener("DOMContentLoaded", () => {
    loadItems();
    loadShops();
    loadItemPrices(); // ✅ ← ต้องเรียกสิ่งนี้ด้วย
});
</script>

