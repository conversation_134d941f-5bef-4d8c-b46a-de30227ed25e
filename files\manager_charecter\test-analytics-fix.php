<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bug-slash"></i> ทดสอบการแก้ไข Character Analytics
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ทดสอบการจัดการ DateTime ใน Analytics</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fal fa-info-circle"></i>
                    ทดสอบการแก้ไขปัญหา "Call to a member function format() on string" ใน character-analytics.php
                </div>
                
                <h5>🔧 การทดสอบการจัดการ DateTime</h5>
                
                <?php
                // Test 1: ทดสอบการดึงข้อมูล daily creation
                echo "<h6>1. ทดสอบ Daily Creation Data</h6>";
                
                try {
                    $sql = "SELECT TOP 5
                                CAST(CreateDate AS DATE) as create_date,
                                COUNT(*) as new_characters
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE CreateDate >= DATEADD(day, -7, GETDATE())
                            GROUP BY CAST(CreateDate AS DATE)
                            ORDER BY create_date DESC";
                    
                    $result = sqlsrv_query($conn, $sql);
                    if ($result) {
                        echo '<div class="alert alert-success">✅ Query ทำงานได้</div>';
                        echo '<table class="table table-sm table-bordered">';
                        echo '<thead><tr><th>Create Date</th><th>Type</th><th>Formatted Date</th><th>New Characters</th></tr></thead>';
                        echo '<tbody>';
                        
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $createDate = $row['create_date'];
                            $type = gettype($createDate);
                            
                            // ทดสอบการจัดการ DateTime
                            if ($createDate instanceof DateTime) {
                                $formattedDate = $createDate->format('Y-m-d');
                                $status = '✅ DateTime Object';
                            } elseif (is_string($createDate)) {
                                $formattedDate = date('Y-m-d', strtotime($createDate));
                                $status = '⚠️ String';
                            } else {
                                $formattedDate = date('Y-m-d');
                                $status = '❌ Other Type';
                            }
                            
                            echo "<tr>";
                            echo "<td>" . ($createDate instanceof DateTime ? $createDate->format('Y-m-d H:i:s') : $createDate) . "</td>";
                            echo "<td>$type ($status)</td>";
                            echo "<td>$formattedDate</td>";
                            echo "<td>" . $row['new_characters'] . "</td>";
                            echo "</tr>";
                        }
                        
                        echo '</tbody></table>';
                    } else {
                        echo '<div class="alert alert-danger">❌ Query ล้มเหลว</div>';
                        if (sqlsrv_errors()) {
                            echo '<pre>' . print_r(sqlsrv_errors(), true) . '</pre>';
                        }
                    }
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">❌ เกิดข้อผิดพลาด: ' . $e->getMessage() . '</div>';
                }
                
                // Test 2: ทดสอบฟังก์ชันจัดการ DateTime
                echo "<h6>2. ทดสอบฟังก์ชันจัดการ DateTime</h6>";
                
                function formatDateSafely($dateValue) {
                    if ($dateValue instanceof DateTime) {
                        return [
                            'formatted' => $dateValue->format('Y-m-d'),
                            'method' => 'DateTime->format()',
                            'status' => 'success'
                        ];
                    } elseif (is_string($dateValue)) {
                        return [
                            'formatted' => date('Y-m-d', strtotime($dateValue)),
                            'method' => 'date(strtotime())',
                            'status' => 'warning'
                        ];
                    } else {
                        return [
                            'formatted' => date('Y-m-d'),
                            'method' => 'date() fallback',
                            'status' => 'error'
                        ];
                    }
                }
                
                // ทดสอบกับข้อมูลต่างๆ
                $testDates = [
                    new DateTime('2024-01-15'),
                    '2024-01-15',
                    '2024-01-15 10:30:00',
                    null,
                    123456789
                ];
                
                echo '<table class="table table-sm table-bordered">';
                echo '<thead><tr><th>Input</th><th>Type</th><th>Formatted</th><th>Method</th><th>Status</th></tr></thead>';
                echo '<tbody>';
                
                foreach ($testDates as $testDate) {
                    $result = formatDateSafely($testDate);
                    $type = gettype($testDate);
                    $input = $testDate instanceof DateTime ? $testDate->format('Y-m-d H:i:s') : (string)$testDate;
                    
                    $statusClass = $result['status'] === 'success' ? 'table-success' : 
                                  ($result['status'] === 'warning' ? 'table-warning' : 'table-danger');
                    
                    echo "<tr class='$statusClass'>";
                    echo "<td>$input</td>";
                    echo "<td>$type</td>";
                    echo "<td>{$result['formatted']}</td>";
                    echo "<td>{$result['method']}</td>";
                    echo "<td>{$result['status']}</td>";
                    echo "</tr>";
                }
                
                echo '</tbody></table>';
                
                // Test 3: ทดสอบ JSON encoding สำหรับ JavaScript
                echo "<h6>3. ทดสอบ JSON Encoding สำหรับ JavaScript</h6>";
                
                $sampleData = [
                    ['create_date' => new DateTime('2024-01-15'), 'new_characters' => 10],
                    ['create_date' => '2024-01-14', 'new_characters' => 8],
                    ['create_date' => '2024-01-13 15:30:00', 'new_characters' => 12]
                ];
                
                // แปลงข้อมูลสำหรับ JavaScript
                $jsData = [];
                foreach ($sampleData as $data) {
                    $createDate = $data['create_date'];
                    if ($createDate instanceof DateTime) {
                        $dateStr = $createDate->format('Y-m-d');
                    } elseif (is_string($createDate)) {
                        $dateStr = date('Y-m-d', strtotime($createDate));
                    } else {
                        $dateStr = date('Y-m-d');
                    }
                    $jsData[] = [
                        'create_date' => $dateStr,
                        'new_characters' => $data['new_characters']
                    ];
                }
                
                echo '<div class="alert alert-success">✅ การแปลงข้อมูลสำหรับ JavaScript สำเร็จ</div>';
                echo '<p><strong>ข้อมูลต้นฉบับ:</strong></p>';
                echo '<pre>' . print_r($sampleData, true) . '</pre>';
                echo '<p><strong>ข้อมูลสำหรับ JavaScript:</strong></p>';
                echo '<pre>' . json_encode($jsData, JSON_PRETTY_PRINT) . '</pre>';
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบหน้าจริง</h5>
                <div class="btn-group">
                    <a href="?url=manager_charecter/character-analytics" class="btn btn-primary" target="_blank">
                        <i class="fal fa-chart-line"></i> ทดสอบ Character Analytics
                    </a>
                    <a href="?url=manager_charecter/character-statistics" class="btn btn-info" target="_blank">
                        <i class="fal fa-chart-bar"></i> ทดสอบ Character Statistics
                    </a>
                </div>
                
                <h5 class="mt-4">📋 สรุปการแก้ไข</h5>
                <div class="alert alert-info">
                    <h6>🔧 ปัญหาที่แก้ไข:</h6>
                    <ul class="mb-2">
                        <li><strong>Fatal Error:</strong> Call to a member function format() on string</li>
                        <li><strong>สาเหตุ:</strong> SQL Server ส่งคืนวันที่เป็น string แทน DateTime object</li>
                        <li><strong>ตำแหน่ง:</strong> character-analytics.php บรรทัด 265, 269</li>
                    </ul>
                    
                    <h6>✅ วิธีแก้ไข:</h6>
                    <ul class="mb-2">
                        <li><strong>เพิ่มการตรวจสอบ:</strong> <code>instanceof DateTime</code></li>
                        <li><strong>จัดการ String:</strong> ใช้ <code>date('Y-m-d', strtotime())</code></li>
                        <li><strong>Fallback:</strong> ใช้ <code>date('Y-m-d')</code> กรณีอื่นๆ</li>
                        <li><strong>แปลงข้อมูลสำหรับ JS:</strong> แยกการประมวลผลก่อนส่งไป JavaScript</li>
                    </ul>
                    
                    <h6>🎯 ผลลัพธ์:</h6>
                    <ul class="mb-0">
                        <li>✅ ไม่มี Fatal Error อีกต่อไป</li>
                        <li>✅ รองรับทั้ง DateTime object และ string</li>
                        <li>✅ JavaScript ได้รับข้อมูลที่ถูกต้อง</li>
                        <li>✅ Charts แสดงผลได้ปกติ</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

.btn-group .btn {
    margin-right: 0.5rem;
}
</style>
