<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bell-exclamation"></i> ทดสอบระบบการแจ้งเตือนความเคลื่อนไหวตัวละคร
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ระบบการแจ้งเตือนความเคลื่อนไหวตัวละครแบบครบถ้วน</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>เพิ่มแล้ว:</strong> ระบบการแจ้งเตือนความเคลื่อนไหวตัวละครที่ครบถ้วนและทันสมัย
                </div>
                
                <h5>🔔 ประเภทการแจ้งเตือนที่เพิ่ม</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">🔥 High Priority</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-level-up text-danger"></i> ตัวละครเลเวลสูงใหม่ (>100)</li>
                                    <li><i class="fal fa-clock text-danger"></i> ภายใน 24 ชั่วโมง</li>
                                    <li><i class="fal fa-exclamation-triangle text-danger"></i> ต้องตรวจสอบ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">⚠️ Medium Priority</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-coins text-warning"></i> ตัวละครเงินเยอะใหม่ (>100M Alz)</li>
                                    <li><i class="fal fa-fast-forward text-warning"></i> การสร้างตัวละครรวดเร็ว</li>
                                    <li><i class="fal fa-robot text-warning"></i> ชื่อตัวละครน่าสงสัย</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">ℹ️ Information</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-user-plus text-info"></i> ตัวละครใหม่ (30 นาทีที่ผ่านมา)</li>
                                    <li><i class="fal fa-info-circle text-info"></i> กิจกรรมปกติ</li>
                                    <li><i class="fal fa-chart-line text-info"></i> สถิติการใช้งาน</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ System Status</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> ระบบทำงานปกติ</li>
                                    <li><i class="fal fa-sync text-success"></i> อัพเดทอัตโนมัติ</li>
                                    <li><i class="fal fa-shield text-success"></i> ตรวจสอบความปลอดภัย</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแจ้งเตือน</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-danger btn-sm" onclick="testAlert('high_level_new', 'high')">
                            <i class="fal fa-level-up"></i> High Level Alert
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testAlert('high_alz', 'medium')">
                            <i class="fal fa-coins"></i> High Alz Alert
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testAlert('new_character', 'info')">
                            <i class="fal fa-user-plus"></i> New Character Alert
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testAlert('rapid_creation', 'medium')">
                            <i class="fal fa-fast-forward"></i> Rapid Creation Alert
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testAlert('suspicious_name', 'medium')">
                            <i class="fal fa-robot"></i> Suspicious Name Alert
                        </button>
                    </div>
                </div>
                
                <div class="btn-group mt-2">
                    <button class="btn btn-primary" onclick="testRealAlerts()">
                        <i class="fal fa-bell"></i> ทดสอบการแจ้งเตือนจริง
                    </button>
                    <button class="btn btn-success" onclick="openCharacterMonitor()">
                        <i class="fal fa-external-link"></i> เปิด Character Monitor
                    </button>
                    <button class="btn btn-info" onclick="testAllAlertTypes()">
                        <i class="fal fa-layer-group"></i> ทดสอบทุกประเภท
                    </button>
                </div>
                
                <div id="alert-test-results" class="mt-3"></div>
                
                <h5 class="mt-4">📊 สถิติการแจ้งเตือน</h5>
                <?php
                // ทดสอบการแจ้งเตือนแต่ละประเภท
                try {
                    // High level characters
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE LEV > 100 AND CreateDate >= DATEADD(hour, -24, GETDATE())";
                    $result = sqlsrv_query($conn, $sql);
                    $highLevelCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $highLevelCount = $row['count'];
                    }
                    
                    // High Alz characters
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE Alz > 100000000 AND CreateDate >= DATEADD(day, -1, GETDATE())";
                    $result = sqlsrv_query($conn, $sql);
                    $highAlzCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $highAlzCount = $row['count'];
                    }
                    
                    // New characters (30 minutes)
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE CreateDate >= DATEADD(minute, -30, GETDATE())";
                    $result = sqlsrv_query($conn, $sql);
                    $newCharCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $newCharCount = $row['count'];
                    }
                    
                    // Suspicious names
                    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            WHERE CreateDate >= DATEADD(hour, -2, GETDATE())
                            AND (Name LIKE '%123%' OR Name LIKE '%test%' OR Name LIKE '%bot%' OR LEN(Name) <= 3)";
                    $result = sqlsrv_query($conn, $sql);
                    $suspiciousCount = 0;
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        $suspiciousCount = $row['count'];
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">Database error: ' . $e->getMessage() . '</div>';
                }
                ?>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-danger"><?php echo $highLevelCount; ?></h4>
                                <small>High Level (24h)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning"><?php echo $highAlzCount; ?></h4>
                                <small>High Alz (24h)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info"><?php echo $newCharCount; ?></h4>
                                <small>New Characters (30m)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning"><?php echo $suspiciousCount; ?></h4>
                                <small>Suspicious Names (2h)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">⚙️ การตั้งค่าการแจ้งเตือน</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>ประเภท</th>
                                <th>เงื่อนไข</th>
                                <th>ช่วงเวลา</th>
                                <th>ความสำคัญ</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>🔥 High Level New</td>
                                <td>เลเวล > 100</td>
                                <td>24 ชั่วโมง</td>
                                <td><span class="badge badge-danger">High</span></td>
                                <td><span class="badge badge-success">เปิดใช้งาน</span></td>
                            </tr>
                            <tr>
                                <td>💰 High Alz New</td>
                                <td>Alz > 100M</td>
                                <td>24 ชั่วโมง</td>
                                <td><span class="badge badge-warning">Medium</span></td>
                                <td><span class="badge badge-success">เปิดใช้งาน</span></td>
                            </tr>
                            <tr>
                                <td>👤 New Character</td>
                                <td>ตัวละครใหม่</td>
                                <td>30 นาที</td>
                                <td><span class="badge badge-info">Info</span></td>
                                <td><span class="badge badge-success">เปิดใช้งาน</span></td>
                            </tr>
                            <tr>
                                <td>⚠️ Rapid Creation</td>
                                <td>≥3 ตัวละคร/ชั่วโมง</td>
                                <td>1 ชั่วโมง</td>
                                <td><span class="badge badge-warning">Medium</span></td>
                                <td><span class="badge badge-success">เปิดใช้งาน</span></td>
                            </tr>
                            <tr>
                                <td>🤖 Suspicious Name</td>
                                <td>ชื่อน่าสงสัย</td>
                                <td>2 ชั่วโมง</td>
                                <td><span class="badge badge-warning">Medium</span></td>
                                <td><span class="badge badge-success">เปิดใช้งาน</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">📋 วิธีใช้งาน</h5>
                <div class="alert alert-info">
                    <h6><i class="fal fa-info-circle"></i> การใช้งานระบบการแจ้งเตือน:</h6>
                    <ol class="mb-0">
                        <li>เปิดหน้า Character Monitor</li>
                        <li>คลิกปุ่ม "แสดงการแจ้งเตือน" เพื่อเปิดพื้นที่การแจ้งเตือน</li>
                        <li>เปิดการอัพเดทอัตโนมัติเพื่อรับการแจ้งเตือนแบบ real-time</li>
                        <li>การแจ้งเตือนจะปรากฏในพื้นที่เฉพาะ ไม่บดบังการทำงาน</li>
                        <li>สามารถลบการแจ้งเตือนทีละรายการหรือทั้งหมดได้</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAlert(type, severity) {
    const messages = {
        'high_level_new': '🔥 ตัวละครเลเวลสูงใหม่: TestChar (เลเวล 150)',
        'high_alz': '💰 ตัวละครเงินเยอะใหม่: RichChar (เลเวล 50, 500,000,000 Alz)',
        'new_character': '👤 ตัวละครใหม่: NewChar (เลเวล 1)',
        'rapid_creation': '⚠️ การสร้างตัวละครรวดเร็ว: 5 ตัวละครในชั่วโมงที่ผ่านมา',
        'suspicious_name': '🤖 ชื่อตัวละครน่าสงสัย: test123 (เลเวล 1)'
    };
    
    const severityMap = {
        'high': 'danger',
        'medium': 'warning',
        'info': 'info'
    };
    
    // สร้าง mock notification
    const notification = {
        type: type,
        message: messages[type] || 'Test notification',
        severity: severity,
        time: new Date().toLocaleString('th-TH'),
        data: { CharacterIdx: Math.random() * 1000 }
    };
    
    // แสดงใน results
    const resultDiv = document.getElementById('alert-test-results');
    resultDiv.innerHTML = `
        <div class="alert alert-${severityMap[severity]} alert-dismissible fade show">
            <strong>ทดสอบการแจ้งเตือน:</strong> ${notification.message}
            <br><small>เวลา: ${notification.time}</small>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    console.log('Test alert:', notification);
}

async function testRealAlerts() {
    const resultDiv = document.getElementById('alert-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบการแจ้งเตือนจริง...</div>';
    
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=alerts', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        let html = '<div class="alert alert-' + (data.success ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (data.success ? 'check' : 'times') + '"></i> Real Alerts Test Result</h6>';
        html += '<p><strong>Success:</strong> ' + (data.success ? 'Yes' : 'No') + '</p>';
        html += '<p><strong>Alert Count:</strong> ' + (data.data ? data.data.length : 0) + '</p>';
        
        if (data.success && data.data && data.data.length > 0) {
            html += '<p><strong>Alerts Found:</strong></p><ul>';
            data.data.forEach(alert => {
                html += '<li><span class="badge badge-' + 
                    (alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'info') + 
                    '">' + alert.severity + '</span> ' + alert.message + '</li>';
            });
            html += '</ul>';
        } else {
            html += '<p>ไม่มีการแจ้งเตือนในขณะนี้</p>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}

function testAllAlertTypes() {
    testAlert('high_level_new', 'high');
    setTimeout(() => testAlert('high_alz', 'medium'), 1000);
    setTimeout(() => testAlert('new_character', 'info'), 2000);
    setTimeout(() => testAlert('rapid_creation', 'medium'), 3000);
    setTimeout(() => testAlert('suspicious_name', 'medium'), 4000);
}
</script>

<style>
.card {
    margin-bottom: 1rem;
}

.badge {
    font-size: 0.75em;
}

.table th {
    font-size: 0.875rem;
}

.table td {
    font-size: 0.875rem;
}
</style>
