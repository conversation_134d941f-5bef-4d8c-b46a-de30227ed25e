# 🎮 Game Integration - ระบบส่งไอเทมเข้าเกม

ระบบส่งไอเทมที่เชื่อมต่อกับ stored procedures ของเกม Cabal Online

## 🎯 วิธีการส่งไอเทม

### 1. **Cash Inventory (inventory)**
ส่งไอเทมเข้า Cash Inventory ของผู้เล่น

**Stored Procedure:**
```sql
EXECUTE [DATABASE_CCA].[dbo].up_AddMyCashItemByItem 
    @UserNum,           -- Player UserNum
    '0',                -- Unknown parameter
    '1',                -- Unknown parameter  
    @ItemCode,          -- Item code (hex string)
    @OptionsCode,       -- Options code (hex string)
    @Duration,          -- Duration
    'Web Management System' -- Source
```

**ตัวอย่างการใช้งาน:**
```php
sendToCashInventory($connection, 12345, '0000007B00000000', '****************', 31);
```

### 2. **Mail System (mail)**
ส่งไอเทมผ่านระบบจดหมายในเกม

**Stored Procedure:**
```sql
EXECUTE cabal_sp_mail_send_GM 
    @ReceiverCharIdx,   -- Player UserNum
    @Title,             -- Mail title
    @Content,           -- Mail content
    @ItemKindIdx,       -- Item code (decimal as bigint)
    @ItemOption,        -- Options code (decimal as bigint)
    @ItemDurationIdx,   -- Duration
    @ExpirationDay      -- Mail expiration (NULL = no expiration)
```

**ตัวอย่างการใช้งาน:**
```php
sendToMail($connection, 12345, '123', '0', 31);
```

### 3. **Event Inventory (warehouse)**
ส่งไอเทมเข้า Event Inventory ของผู้เล่น

**Stored Procedure:**
```sql
EXECUTE [DATABASE_SV].[dbo].cabal_sp_event_inventory_reward 
    @UserNum,           -- Player UserNum
    @ItemCode,          -- Item code (hex string)
    @OptionsCode,       -- Options code (hex string)
    @Duration,          -- Duration
    1,                  -- Quantity (always 1)
    "0",                -- Unknown parameter
    @ExpireDay,         -- Expiration days (0 = no expiration)
    @RegisterDateTime   -- Registration timestamp
```

**ตัวอย่างการใช้งาน:**
```php
sendToEventInventory($connection, 12345, '0000007B00000000', '****************', 31);
```

## 🔄 การแปลงข้อมูล

### Item Code และ Options Code
ระบบจะแปลงข้อมูลให้เหมาะสมกับแต่ละ stored procedure:

```php
// Input: Hex strings
$item_code_hex = '0000007B00000000';
$options_code_hex = '****************';

// Convert to decimal for mail system
$item_code_decimal = hexdec($item_code_hex);     // 123
$options_code_decimal = hexdec($options_code_hex); // 0

// Usage:
// Cash Inventory: ใช้ hex strings
// Mail System: ใช้ decimal strings
// Event Inventory: ใช้ hex strings
```

### Duration Values
- `31` = Permanent item (ไอเทมถาวร)
- `1-30` = จำนวนวันที่ไอเทมจะหมดอายุ
- `0` = ไม่มีการหมดอายุ (ขึ้นอยู่กับระบบ)

## 📊 การตอบกลับ (Response)

### Success Response
```json
{
    "success": true,
    "message": "Item sent successfully",
    "data": {
        "send_id": 123,
        "player": "player_name",
        "item_id": 123,
        "item_code": "0000007B00000000",
        "item_code_decimal": 123,
        "options_code": "****************", 
        "options_code_decimal": 0,
        "quantity": 1,
        "duration": 31,
        "method": "inventory",
        "status": "sent_to_cash_inventory",
        "timestamp": "2024-01-01 12:00:00",
        "game_result": {
            "success": true,
            "method": "cash_inventory",
            "params": [12345, "0", "1", "0000007B00000000", "****************", 31, "Web Management System"]
        }
    }
}
```

### Error Response
```json
{
    "success": false,
    "error": "Player 'invalid_player' not found in database"
}
```

## 🛠️ การกำหนดค่า

### Database Configuration
ตรวจสอบว่าไฟล์ `../../_app/dbinfo.inc.php` มีการตั้งค่าถูกต้อง:

```php
$dbinfo = [
    'host' => 'your_sql_server',
    'port' => '1433',
    'database' => 'your_game_database', // DATABASE_CCA, DATABASE_SV
    'username' => 'your_username',
    'password' => 'your_password'
];
```

### Multiple Database Support
หากเกมใช้หลายฐานข้อมูล คุณอาจต้องปรับแต่ง:

```php
// ใน send_item.php
$DATABASE_CCA = 'CabalCashDB';  // Cash database
$DATABASE_SV = 'CabalGameDB';   // Game database
```

## 🔍 การ Debug

### 1. ตรวจสอบ Stored Procedures
```sql
-- ตรวจสอบว่า stored procedures มีอยู่
SELECT name FROM sys.procedures WHERE name LIKE '%cash%' OR name LIKE '%mail%' OR name LIKE '%event%';

-- ทดสอบ stored procedure
EXEC up_AddMyCashItemByItem 12345, '0', '1', '0000007B00000000', '****************', 31, 'Test';
```

### 2. ตรวจสอบ Parameters
ระบบจะ log parameters ที่ส่งไปยัง stored procedures:

```
Cash inventory params: Array([0] => 12345, [1] => 0, [2] => 1, [3] => 0000007B00000000, [4] => ****************, [5] => 31, [6] => Web Management System)
```

### 3. ตรวจสอบ Error Logs
```bash
# ดู PHP error logs
tail -f /var/log/php_errors.log

# หรือใน Windows
# ดูใน Event Viewer หรือ IIS logs
```

## ⚠️ ข้อควรระวัง

### 1. **Data Types**
- Item codes: ส่งเป็น string เสมอ
- User numbers: ส่งเป็น integer
- Duration: ส่งเป็น integer

### 2. **Database Permissions**
ตรวจสอบว่า database user มีสิทธิ์ execute stored procedures:

```sql
GRANT EXECUTE ON up_AddMyCashItemByItem TO [your_user];
GRANT EXECUTE ON cabal_sp_mail_send_GM TO [your_user];
GRANT EXECUTE ON cabal_sp_event_inventory_reward TO [your_user];
```

### 3. **Transaction Safety**
ระบบใช้ transaction เพื่อความปลอดภัย:
- หาก stored procedure ล้มเหลว จะ rollback ทั้งหมด
- บันทึก error message ในฐานข้อมูล
- แจ้งเตือน admin เมื่อเกิดข้อผิดพลาด

## 🚀 การทดสอบ

### 1. ทดสอบแต่ละวิธี
```javascript
// ทดสอบ Cash Inventory
fetch('send_item.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        playerUsername: 'test_player',
        itemCode: '0000007B00000000',
        optionsCode: '****************',
        quantity: 1,
        duration: 31,
        sendMethod: 'inventory',
        adminUsername: 'test_admin'
    })
}).then(r => r.json()).then(console.log);
```

### 2. ตรวจสอบผลลัพธ์ในเกม
- เข้าเกมด้วย account ที่ทดสอบ
- ตรวจสอบ Cash Inventory, Mail, หรือ Event Inventory
- ยืนยันว่าไอเทมถูกส่งถูกต้อง

## 📈 การปรับปรุงในอนาคต

1. **Support Multiple Items**: ส่งหลายไอเทมในครั้งเดียว
2. **Batch Processing**: ประมวลผลหลาย request พร้อมกัน
3. **Real-time Notifications**: แจ้งเตือนผู้เล่นในเกมทันที
4. **Item Validation**: ตรวจสอบว่าไอเทมมีอยู่จริงในเกม
5. **Player Status Check**: ตรวจสอบสถานะผู้เล่น (online/offline)

---

**หมายเหตุ:** ระบบนี้ออกแบบมาให้ทำงานกับ Cabal Online โดยเฉพาะ หากใช้กับเกมอื่น อาจต้องปรับแต่ง stored procedures และ parameters
