<?php $user->restrictionUser(true, $conn); ?>

<?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
<div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
<?php } ?>

<style>
/* Custom CSS for Account Manager */
.account-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    color: white;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.account-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.account-table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-header-custom {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
}

.search-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.btn-custom {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.status-badge {
    border-radius: 20px;
    padding: 5px 12px;
    font-size: 0.85rem;
    font-weight: 500;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 20px !important;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 20px;
    border: 2px solid #e9ecef;
    padding: 8px 15px;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 20px;
    border: 2px solid #e9ecef;
    padding: 5px 10px;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<?php
// อัปเดตอัตโนมัติทุกครั้งที่โหลดหน้า และเพิ่มการคำนวณสถิติ
$updateAccountJson = function ($conn) {
    $params = [];
    $options = [ "Scrollable" => SQLSRV_CURSOR_KEYSET ];
    $query = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table";
    $result = sqlsrv_query($conn, $query, $params, $options);

    if (sqlsrv_num_rows($result)) {
        $data = [];
        $stats = [
            'total' => 0,
            'online' => 0,
            'banned' => 0,
            'today_registered' => 0
        ];

        $today = date('Y-m-d');

        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total']++;

            if ($row['Login'] == 1) {
                $stats['online']++;
            }

            if ($row['AuthType'] == 2) {
                $stats['banned']++;
            }

            if ($row['createDate'] && date('Y-m-d', strtotime($row['createDate'])) == $today) {
                $stats['today_registered']++;
            }

            $data[] = [
                'UserNum'    => $row['UserNum'],
                'ID'         => $row['ID'],
                'Login'      => $row['Login'],
                'LoginTime'  => $row['LoginTime'],
                'LogoutTime' => $row['LogoutTime'],
                'AuthType'   => $row['AuthType'],
                'PlayTime'   => $row['PlayTime'],
                'LastIp'     => $row['LastIp'],
                'createDate' => $row['createDate'],
                'Email'      => $row['Email'],
                'Action'     => $row['UserNum']
            ];
        }

        $jsonContent = json_encode(['data' => $data, 'stats' => $stats], JSON_PRETTY_PRINT);

        // สร้าง hash ใหม่จากข้อมูลที่โหลด
        $newHash = md5($jsonContent);

        $hashFile = '_data/account_data.hash';
        $jsonFile = '_data/account_data.json';

        $oldHash = file_exists($hashFile) ? file_get_contents($hashFile) : '';

        // อัปเดตเฉพาะเมื่อ hash เปลี่ยน
        if ($newHash !== $oldHash) {
            file_put_contents($jsonFile, $jsonContent);
            file_put_contents($hashFile, $newHash);
        }

        return $stats;
    }
    return null;
};
$accountStats = $updateAccountJson($conn);
?>

<div class="subheader fade-in">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-users-cog"></i> Account Manager
        <small>
            ระบบจัดการบัญชีผู้เล่น - ตรวจสอบและควบคุมข้อมูลผู้เล่น
        </small>
    </h1>
</div>

<!-- Dashboard Statistics -->
<?php if ($accountStats): ?>
<div class="row mb-4 fade-in">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
        <div class="card account-stats-card h-100">
            <div class="card-body d-flex align-items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fal fa-users stats-icon"></i>
                </div>
                <div class="flex-grow-1">
                    <h3 class="mb-0 text-white"><?php echo number_format($accountStats['total']); ?></h3>
                    <p class="mb-0 text-white-50">ผู้เล่นทั้งหมด</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
        <div class="card h-100" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border: none; border-radius: 15px; color: white;">
            <div class="card-body d-flex align-items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fal fa-circle stats-icon text-success"></i>
                </div>
                <div class="flex-grow-1">
                    <h3 class="mb-0 text-white"><?php echo number_format($accountStats['online']); ?></h3>
                    <p class="mb-0 text-white-50">ออนไลน์</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
        <div class="card h-100" style="background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); border: none; border-radius: 15px; color: white;">
            <div class="card-body d-flex align-items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fal fa-ban stats-icon text-danger"></i>
                </div>
                <div class="flex-grow-1">
                    <h3 class="mb-0 text-white"><?php echo number_format($accountStats['banned']); ?></h3>
                    <p class="mb-0 text-white-50">ถูกแบน</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
        <div class="card h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; border-radius: 15px; color: white;">
            <div class="card-body d-flex align-items-center">
                <div class="flex-shrink-0 mr-3">
                    <i class="fal fa-user-plus stats-icon text-info"></i>
                </div>
                <div class="flex-grow-1">
                    <h3 class="mb-0 text-white"><?php echo number_format($accountStats['today_registered']); ?></h3>
                    <p class="mb-0 text-white-50">สมัครวันนี้</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Search and Filter Section -->
<div class="search-container fade-in">
    <div class="row align-items-center">
        <div class="col-lg-6 col-md-12 mb-3 mb-lg-0">

            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text bg-primary text-white">
                        <i class="fal fa-search"></i>
                    </span>
                </div>
                <input type="text" id="globalSearch" class="form-control" placeholder="ค้นหา ID, Email, IP Address...">
            </div>
        </div>
        <div class="col-lg-6 col-md-12">
            <div class="row">
                <div class="col-md-6 mb-2">
                    <select id="statusFilter" class="form-control">
                        <option value="">สถานะทั้งหมด</option>
                        <option value="online">ออนไลน์</option>
                        <option value="offline">ออฟไลน์</option>
                    </select>
                </div>
                <div class="col-md-6 mb-2">
                    <select id="authFilter" class="form-control">
                        <option value="">สถานะบัญชีทั้งหมด</option>
                        <option value="1">ปกติ</option>
                        <option value="2">ถูกแบน</option>
                        <option value="3">Trade Block</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Table -->
<div class="row fade-in">
    <div class="col-xl-12">
        <div class="account-table-container">
            <div class="table-header-custom">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><i class="fal fa-table mr-2"></i>ตารางข้อมูลบัญชีผู้เล่น</h3>
                        <p class="mb-0 mt-1 opacity-75">จัดการและตรวจสอบข้อมูลบัญชีผู้เล่นทั้งหมด</p>
                    </div>
                    <div>
                        <button class="btn btn-light btn-custom" onclick="refreshTable()">
                            <i class="fal fa-sync-alt mr-1"></i>รีเฟรช
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div class="table-responsive">
                    <table id="accountTable" class="table table-hover w-100" style="border: none;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th class="border-0">#</th>
                                <th class="border-0">UserNum</th>
                                <th class="border-0">ID</th>
                                <th class="border-0">Email</th>
                                <th class="border-0">Login Time</th>
                                <th class="border-0">Logout Time</th>
                                <th class="border-0">Last IP</th>
                                <th class="border-0">PlayTime</th>
                                <th class="border-0">สถานะ</th>
                                <th class="border-0">บัญชี</th>
                                <th class="border-0">จัดการ</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let accountTable;

$(document).ready(function() {
    // Initialize DataTable with enhanced features
    accountTable = $('#accountTable').DataTable({
        ajax: {
            url: '_data/account_data.json',
            dataSrc: 'data'
        },
        processing: true,
        serverSide: false,
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "ทั้งหมด"]],
        language: {
            url: 'assets/js/datatables-thai.json',
            search: "ค้นหา:",
            lengthMenu: "แสดง _MENU_ รายการ",
            info: "แสดง _START_ ถึง _END_ จาก _TOTAL_ รายการ",
            infoEmpty: "แสดง 0 ถึง 0 จาก 0 รายการ",
            infoFiltered: "(กรองจาก _MAX_ รายการทั้งหมด)",
            paginate: {
                first: "หน้าแรก",
                last: "หน้าสุดท้าย",
                next: "ถัดไป",
                previous: "ก่อนหน้า"
            }
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        columns: [
            {
                data: null,
                title: '#',
                width: '50px',
                render: (data, type, row, meta) => `<span class="badge badge-light">${meta.row + 1}</span>`
            },
            {
                data: 'UserNum',
                title: 'UserNum',
                width: '80px',
                render: function(data) {
                    return `<code class="text-primary">${data}</code>`;
                }
            },
            {
                data: 'ID',
                title: 'ID',
                render: function(data) {
                    return `<strong class="text-dark">${data}</strong>`;
                }
            },
            {
                data: 'Email',
                title: 'Email',
                render: function(data) {
                    if (data && data.trim() !== '') {
                        return `<small class="text-muted">${data}</small>`;
                    }
                    return '<span class="text-muted">-</span>';
                }
            },
            {
                data: 'LoginTime',
                title: 'Login Time',
                render: function(data) {
                    if (data && data.trim() !== '') {
                        const date = new Date(data);
                        return `<small class="text-info">${date.toLocaleString('th-TH')}</small>`;
                    }
                    return '<span class="text-muted">-</span>';
                }
            },
            {
                data: 'LogoutTime',
                title: 'Logout Time',
                render: function(data) {
                    if (data && data.trim() !== '') {
                        const date = new Date(data);
                        return `<small class="text-warning">${date.toLocaleString('th-TH')}</small>`;
                    }
                    return '<span class="text-muted">-</span>';
                }
            },
            {
                data: 'LastIp',
                title: 'Last IP',
                render: function(ip) {
                    if (ip && ip.trim() !== '') {
                        return `<a href="https://checkip.thaiware.com/?ip=${encodeURIComponent(ip)}" target="_blank" class="badge badge-info status-badge">${ip}</a>`;
                    } else {
                        return `<span class="badge badge-secondary status-badge">ไม่มีข้อมูล</span>`;
                    }
                }
            },
            {
                data: 'PlayTime',
                title: 'PlayTime',
                render: function(data) {
                    if (data && !isNaN(data)) {
                        const hours = Math.floor(data / 60);
                        return `<span class="badge badge-primary status-badge">${hours} ชม.</span>`;
                    }
                    return '<span class="badge badge-secondary status-badge">0 ชม.</span>';
                }
            },
            {
                data: 'Login',
                title: 'สถานะ',
                render: function(loginValue) {
                    return loginValue == 1
                        ? '<span class="badge badge-success status-badge"><i class="fal fa-circle mr-1"></i>Online</span>'
                        : '<span class="badge badge-secondary status-badge"><i class="fal fa-circle mr-1"></i>Offline</span>';
                }
            },
            {
                data: 'AuthType',
                title: 'บัญชี',
                render: function(authType) {
                    switch (parseInt(authType)) {
                        case 1: return '<span class="badge badge-success status-badge"><i class="fal fa-check mr-1"></i>ปกติ</span>';
                        case 2: return '<span class="badge badge-danger status-badge"><i class="fal fa-ban mr-1"></i>ถูกแบน</span>';
                        case 3: return '<span class="badge badge-warning status-badge"><i class="fal fa-exchange mr-1"></i>Trade Block</span>';
                        case 4: return '<span class="badge badge-info status-badge"><i class="fal fa-lock mr-1"></i>SubPass Block</span>';
                        case 5: return '<span class="badge badge-dark status-badge"><i class="fal fa-volume-mute mr-1"></i>Mute Block</span>';
                        case 99: return '<span class="badge badge-danger status-badge"><i class="fal fa-user-times mr-1"></i>ยกเลิกบัญชี</span>';
                        default: return '<span class="badge badge-secondary status-badge">N/A</span>';
                    }
                }
            },
            {
                data: 'UserNum',
                title: 'จัดการ',
                orderable: false,
                render: function(data) {
                    return `<a href="?url=manager_account/manage-account-edit&id=${data}" class="btn btn-sm btn-primary btn-custom">
                                <i class="fal fa-edit mr-1"></i>จัดการ
                            </a>`;
                }
            }
        ],
        order: [[1, 'desc']],
        drawCallback: function() {
            // Add animation to new rows
            $('#accountTable tbody tr').addClass('fade-in');
        }
    });

    // Global search functionality
    $('#globalSearch').on('keyup', function() {
        accountTable.search(this.value).draw();
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        const value = this.value;
        if (value === 'online') {
            accountTable.column(8).search('Online').draw();
        } else if (value === 'offline') {
            accountTable.column(8).search('Offline').draw();
        } else {
            accountTable.column(8).search('').draw();
        }
    });

    // Auth type filter
    $('#authFilter').on('change', function() {
        const value = this.value;
        if (value) {
            accountTable.column(9).search(getAuthTypeName(value)).draw();
        } else {
            accountTable.column(9).search('').draw();
        }
    });
});

// Helper function to get auth type name
function getAuthTypeName(authType) {
    switch (parseInt(authType)) {
        case 1: return 'ปกติ';
        case 2: return 'ถูกแบน';
        case 3: return 'Trade Block';
        default: return '';
    }
}

// Refresh table function
function refreshTable() {
    if (accountTable) {
        accountTable.ajax.reload(null, false);

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'รีเฟรชสำเร็จ!',
            text: 'ข้อมูลได้รับการอัปเดตแล้ว',
            timer: 1500,
            showConfirmButton: false
        });
    }
}

// Auto refresh every 30 seconds
setInterval(function() {
    if (accountTable) {
        accountTable.ajax.reload(null, false);
    }
}, 30000);
</script>