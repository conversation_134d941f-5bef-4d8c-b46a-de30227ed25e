 <link rel="stylesheet" href="files/game_systems/assets/npc-shop-editor.css" />
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> EventData Systems
        <small>
            แก้ไข อัพเดท เพิ่ม ลบ ข้อมูลระบบ event Data
        </small>
        <div class="message" id="resultNotice" style="display:none;"></div>
    </h1>
</div>
<div class="row">
    
    <div class="col-xl-6">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>✏️ แก้ไข Npc Shop Tables</h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="main-content">
                               
                                        <div class="shop-grid-container">
                                            <div id="shopGrid" class="shop-grid"></div>
                                        </div>
                                    
                               </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<div class="col-xl-6">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>✏️Import Export Npc Shop Tables</h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-xl-12 col-lg-8">
                                <div class="main-content">
                                    <div class="controls-panel">
                                        <div class="panel-header">
                                            <h2>NPC Shop Editor</h2>
                                            <div class="file-upload-section">
                                                <label for="xmlFile" class="btn btn-secondary btn-sm">Load
                                                    cabal_msg.xml</label>
                                                <input type="file" id="xmlFile" accept=".xml" style="display: none;">
                                                <span id="loadedXmlStatus" class="status-text"></span>
                                            </div>
                                        </div>

                                        <div class="current-pool-info">
                                            <span id="currentPoolId">Current Pool_ID: (none)</span>
                                            <button id="changePoolBtn" class="btn btn-secondary btn-sm" disabled>Change
                                                Pool_ID</button>
                                        </div>

                                        <div class="item-search-section">
                                            <h3>Search Items</h3>
                                            <input type="text" id="itemSearch" placeholder="Search for items..."
                                                class="search-input">
                                            <div id="itemResults" class="item-results"></div>
                                        </div>

                                        <div class="actions-section">
                                            <button id="importBtn" class="btn btn-primary btn-sm">Import Shop
                                                Table</button>
                                            <button id="exportBtn" class="btn btn-primary btn-sm" disabled>Export Shop
                                                Table</button>
                                            <input type="file" id="importFile" accept=".scp,.txt,.tsv"
                                                style="display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



</div>
<!-- Edit Slot Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>แก้ไข Slot <span id="editSlotNumber"></span></h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="editItemSelect">Item:</label>
                <select id="editItemSelect" class="form-control" style="width: 100%;">
                    <option value="">Select an item...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="editAlzPrice">Alz Price:</label>
                <input type="number" id="editAlzPrice" class="form-control" value="0" min="0">
            </div>
            <div class="form-group">
                <label for="editDpPrice">DP Price:</label>
                <input type="number" id="editDpPrice" class="form-control" value="0" min="0">
            </div>
            <div class="form-group">
                <label for="editGemPrice">Gem Price:</label>
                <input type="number" id="editGemPrice" class="form-control" value="0" min="0">
            </div>
            <div class="form-group">
                <label for="editItemOpt">Item Opt:</label>
                <input type="number" id="editItemOpt" class="form-control" value="0" min="0">
            </div>
        </div>
        <div class="modal-footer">
            <button id="saveSlotBtn" class="btn btn-primary">Save</button>
            <button id="cancelEditBtn" class="btn btn-secondary">Cancel</button>
            <button id="clearSlotBtn" class="btn btn-danger">Clear Slot</button>
        </div>
    </div>
</div>

<!-- Pool ID Selection Modal -->
<div id="poolIdModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>เลือก Pool_ID</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div id="poolIdList" class="pool-id-list"></div>
        </div>
        <div class="modal-footer">
            <button id="selectPoolBtn" class="btn btn-primary" disabled>Select</button>
            <button id="cancelPoolBtn" class="btn btn-secondary">Cancel</button>
        </div>
    </div>
</div>
    <script src="files/game_systems/assets/npc-shop-editor.js" type="text/javascript"></script>