<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
?>
<header class="page-header page-header-left-inline-breadcrumb">
    <h2 class="font-weight-bold text-6"><?php echo PT_WEB_DOWNLOADS; ?></h2>
    <div class="right-wrapper">
        <ol class="breadcrumbs">
            <li><span><?php echo PT_WEB_DOWNLOADS_DESC; ?></span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fas fa-chevron-left"></i></a>
    </div>
</header>


<form class="ecommerce-form action-buttons-fixed" action="#" method="post">

    <div class="row">
        <div class="col">
            <section class="card card-modern card-big-info">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-2-5 col-xl-1-5">
                            <i class="card-big-info-icon bx bx-download bx-fade-down"></i>
                            <h2 class="card-big-info-title">Edit Download File</h2>
                            <p class="card-big-info-desc">Add here the Download info with all details and
                                necessary information.</p>
                        </div>
                        <div class="col-lg-3-5 col-xl-4-5">
                            <div class="alert alert-warning">Links without http:// or https://</div>
                            <?php
                    $getDownloadID = filter_input(INPUT_GET, 'downid', FILTER_VALIDATE_INT);
                    $selectDownloads = "SELECT * FROM WEB_Downloads WHERE id = '$getDownloadID'";
                    $selectDownloadsQuery = sqlsrv_query($conn, $selectDownloads, array(), array("Scrollable" => SQLSRV_CURSOR_KEYSET));
                    $selectDownloadsFetch = sqlsrv_fetch_array($selectDownloadsQuery, SQLSRV_FETCH_ASSOC);
                    
                    $formDownloads = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                    if(isset($formDownloads) && isset($formDownloads['btn_savechange'])){
                        
                        // validation
                        if(in_array('', $formDownloads)){
                            $returnWarning = "Oops, inputs cant be empty!";
                        }else{
                            
                            // variables
                            $dateNow = date('Y-m-d H:i:s');
                            // update
                            $updateDownloads = "UPDATE WEB_Downloads SET name = '$formDownloads[name]', description = '$formDownloads[description]', size = '$formDownloads[size]', size_type = '$formDownloads[size_type]', url = '$formDownloads[url]', dateupdated = '$dateNow' WHERE id = '$getDownloadID'";
                            $updateDownloadsQuery = sqlsrv_query($conn, $updateDownloads, array());
                            if($updateDownloadsQuery){
                                $returnSuccess = 'Download updated!';
                            }else{
                                $returnError = 'Oops, occurs an error when try update downloads info';
                            }
                            
                                    }
                                    
                                }
                            ?>
                            <?php if (isset($returnSuccess)) { ?>
                            <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                            <?php } elseif (isset($returnWarning)) { ?>
                            <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                            <?php } elseif (isset($returnError)) { ?>
                            <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                            <?php } ?>

                            <div class="shipping-fields-wrapper collapse show">
                                <div class="form-group row align-items-center">
                                    <label for="name"
                                        class="col-lg-5 col-xl-3 control-label text-lg-right mb-0">Download
                                        name:</label>
                                    <div class="col-lg-7 col-xl-6">
                                        <input type="text" class="form-control form-control-modern" name="name"
                                            id="name" value="<?php echo $selectDownloadsFetch['name']; ?>" required />
                                    </div>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label for="description"
                                        class="col-lg-5 col-xl-3 control-label text-lg-right mb-0">Download
                                        description:</label>
                                    <div class="col-lg-7 col-xl-6">
                                        <input type="text" class="form-control form-control-modern" name="description"
                                            id="description" value="<?php echo $selectDownloadsFetch['description']; ?>"
                                            required />
                                    </div>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label for="size"
                                        class="col-lg-5 col-xl-3 control-label text-lg-right mb-0">Download
                                        Size:</label>
                                    <div class="col-lg-7 col-xl-6">
                                        <input type="text" class="form-control form-control-modern" name="size"
                                            id="size" value="<?php echo $selectDownloadsFetch['size']; ?>" />
                                    </div>
                                </div>


                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5 col-xl-3 control-label text-lg-right mb-0">Launcher Size
                                        Type:</label>
                                    <div class="col-lg-7 col-xl-6">
                                        <select class="form-control form-control-modern" name="size_type"
                                            name="customerShippingStateCountry">
                                            <option value="mb" <?php if($selectDownloadsFetch['size_type'] == 'mb'){ ?>
                                                selected="selected" <?php } ?>>MB</option>
                                            <option value="gb" <?php if($selectDownloadsFetch['size_type'] == 'gb'){ ?>
                                                selected="selected" <?php } ?>>GB</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label for="url" class="col-lg-5 col-xl-3 control-label text-lg-right mb-0">Download
                                        url:</label>
                                    <div class="col-lg-7 col-xl-6">
                                        <input type="text" name="url" id="url"
                                            value="<?php echo $selectDownloadsFetch['url']; ?>"
                                            class="form-control form-control-modern" />

                                    </div>
                                    <span>without <code>http://</code> OR
                                        <code>https://</code></span>
                                </div>
                                <div class="alert alert-info nomargin">
                                    <button aria-hidden="true" data-dismiss="alert" class="close"
                                        type="button">×</button>
                                    <h4><?php echo T_LASTUPDATE; ?>:</h4>
                                    <p> <?php echo date('d/m/Y \a\t\ H:i', strtotime($selectDownloadsFetch['dateupdated'])); ?>
                                    </p>
                                    <p>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>


    <div class="row action-buttons">
        <div class="col-12 col-md-auto">
            <button type="submit" name="btn_savechange" value="<?php echo B_SAVECHANGES; ?>" class=" submit-button btn btn-primary btn-px-4 py-3 d-flex align-items-center font-weight-semibold
                line-height-1" data-loading-text="Loading...">
                <i class="bx bx-save text-4 mr-2"></i> Save Download
            </button>
        </div>
        <div class="col-12 col-md-auto px-md-0 mt-3 mt-md-0">
            <a href="?url=web-admin/downloads"
                class="cancel-button btn btn-light btn-px-4 py-3 border font-weight-semibold text-color-dark text-3">Cancel</a>
        </div>
        <div class="col-12 col-md-auto ml-md-auto mt-3 mt-md-0">
            <a href="?url=web-admin/delete-download&downid=<?php echo $getDownloadID; ?>&delete=wait"
                class="delete-button btn btn-danger btn-px-4 py-3 d-flex align-items-center font-weight-semibold line-height-1">
                <i class="bx bx-trash text-4 mr-2"></i> Delete Download
            </a>
        </div>
    </div>
</form>
<!-- end: page -->