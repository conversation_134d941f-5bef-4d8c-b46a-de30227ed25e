<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bell"></i> ทดสอบระบบการแจ้งเตือนแบบใหม่
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ระบบการแจ้งเตือนแบบแถวลงมา (ไม่ซ้อนทับ)</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> การแจ้งเตือนจะแสดงเป็นแถวลงมาในพื้นที่เฉพาะ ไม่ซ้อนทับกัน
                </div>
                
                <h5>🎯 ฟีเจอร์ใหม่</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ ปรับปรุงแล้ว</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-check text-success"></i> พื้นที่แจ้งเตือนแยกต่างหาก</li>
                                    <li><i class="fal fa-check text-success"></i> แสดงเป็นแถวลงมา</li>
                                    <li><i class="fal fa-check text-success"></i> ไม่ซ้อนทับกัน</li>
                                    <li><i class="fal fa-check text-success"></i> มีปุ่มเปิด/ปิด</li>
                                    <li><i class="fal fa-check text-success"></i> นับจำนวนการแจ้งเตือน</li>
                                    <li><i class="fal fa-check text-success"></i> ลบทีละรายการได้</li>
                                    <li><i class="fal fa-check text-success"></i> ล้างทั้งหมดได้</li>
                                    <li><i class="fal fa-check text-success"></i> Animation สวยงาม</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">🔧 การใช้งาน</h6>
                            </div>
                            <div class="card-body">
                                <ol class="mb-0">
                                    <li>คลิกปุ่ม "แสดงการแจ้งเตือน" เพื่อเปิด/ปิด</li>
                                    <li>การแจ้งเตือนจะแสดงเป็นแถวลงมา</li>
                                    <li>การแจ้งเตือนใหม่จะอยู่ด้านบน</li>
                                    <li>คลิก X เพื่อลบทีละรายการ</li>
                                    <li>คลิก "ล้างทั้งหมด" เพื่อลบทั้งหมด</li>
                                    <li>จำกัดแค่ 20 รายการล่าสุด</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแจ้งเตือน</h5>
                <div class="alert alert-secondary">
                    <div class="btn-group">
                        <button class="btn btn-outline-danger btn-sm" onclick="testNotification('danger', 'High Priority Alert')">
                            <i class="fal fa-exclamation-triangle"></i> High Priority
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testNotification('warning', 'Medium Priority Alert')">
                            <i class="fal fa-exclamation"></i> Medium Priority
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="testNotification('info', 'Information Alert')">
                            <i class="fal fa-info"></i> Information
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="testMultipleNotifications()">
                            <i class="fal fa-layer-group"></i> Multiple Alerts
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="testRealAlerts()">
                            <i class="fal fa-bell"></i> Real API Alerts
                        </button>
                    </div>
                </div>
                
                <!-- Notification Area (Copy from character-monitor) -->
                <div class="mt-4">
                    <button class="btn btn-warning" onclick="toggleNotificationArea()" id="notificationBtn">
                        <i class="fal fa-bell"></i> <span id="notification-toggle-text">แสดงการแจ้งเตือน</span>
                        <span class="badge badge-light ml-1" id="notification-count" style="display: none;">0</span>
                    </button>
                </div>
                
                <div id="notification-area" class="notification-area mt-3" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fal fa-bell text-warning"></i> การแจ้งเตือนล่าสุด</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" onclick="clearAllNotifications()">
                                    <i class="fal fa-trash"></i> ล้างทั้งหมด
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="toggleNotificationArea()">
                                    <i class="fal fa-times"></i> ปิด
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="notifications-list" class="notifications-list">
                                <div class="text-center text-muted p-4" id="no-notifications">
                                    <i class="fal fa-bell-slash fa-2x mb-2"></i>
                                    <p class="mb-0">ยังไม่มีการแจ้งเตือน</p>
                                    <small>การแจ้งเตือนจะปรากฏที่นี่เมื่อมีกิจกรรมที่น่าสนใจ</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 สถิติการแจ้งเตือน</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-danger" id="high-count">0</h4>
                                <small>High Priority</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning" id="medium-count">0</h4>
                                <small>Medium Priority</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info" id="info-count">0</h4>
                                <small>Information</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary" id="total-count">0</h4>
                                <small>Total</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 การเปรียบเทียบ</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>ฟีเจอร์</th>
                                <th>แบบเก่า (Toast)</th>
                                <th>แบบใหม่ (Notification Area)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>การแสดงผล</td>
                                <td><span class="text-danger">ซ้อนทับกัน</span></td>
                                <td><span class="text-success">แถวลงมาเป็นระเบียบ</span></td>
                            </tr>
                            <tr>
                                <td>การจัดการ</td>
                                <td><span class="text-warning">หายไปเอง</span></td>
                                <td><span class="text-success">ควบคุมได้เต็มที่</span></td>
                            </tr>
                            <tr>
                                <td>ประวัติ</td>
                                <td><span class="text-danger">ไม่มี</span></td>
                                <td><span class="text-success">เก็บไว้ 20 รายการ</span></td>
                            </tr>
                            <tr>
                                <td>การนับ</td>
                                <td><span class="text-danger">ไม่มี</span></td>
                                <td><span class="text-success">มี Badge แสดงจำนวน</span></td>
                            </tr>
                            <tr>
                                <td>การลบ</td>
                                <td><span class="text-warning">Auto เท่านั้น</span></td>
                                <td><span class="text-success">ทีละรายการ หรือ ทั้งหมด</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy functions from character-monitor
let notificationStats = {
    high: 0,
    medium: 0,
    info: 0,
    total: 0
};

function toggleNotificationArea() {
    const area = document.getElementById('notification-area');
    const btn = document.getElementById('notificationBtn');
    const toggleText = document.getElementById('notification-toggle-text');
    
    if (area.style.display === 'none') {
        area.style.display = 'block';
        toggleText.textContent = 'ซ่อนการแจ้งเตือน';
        btn.classList.remove('btn-warning');
        btn.classList.add('btn-success');
    } else {
        area.style.display = 'none';
        toggleText.textContent = 'แสดงการแจ้งเตือน';
        btn.classList.remove('btn-success');
        btn.classList.add('btn-warning');
    }
}

function addNotificationToArea(message, type, time) {
    const notificationsList = document.getElementById('notifications-list');
    const noNotifications = document.getElementById('no-notifications');
    const notificationCount = document.getElementById('notification-count');
    
    // ซ่อน "ยังไม่มีการแจ้งเตือน"
    if (noNotifications) {
        noNotifications.style.display = 'none';
    }
    
    // สร้าง notification item
    const notificationItem = document.createElement('div');
    notificationItem.className = `notification-item border-bottom`;
    
    const typeClass = type === 'danger' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
    const icon = type === 'danger' ? 'fa-exclamation-triangle' : type === 'warning' ? 'fa-exclamation' : 'fa-info-circle';
    
    notificationItem.innerHTML = `
        <div class="p-3">
            <div class="d-flex align-items-start">
                <div class="mr-3">
                    <i class="fal ${icon} ${typeClass} fa-lg"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="notification-message">${message}</div>
                    <small class="text-muted">
                        <i class="fal fa-clock"></i> ${time || new Date().toLocaleString('th-TH')}
                    </small>
                </div>
                <div class="ml-2">
                    <button class="btn btn-sm btn-outline-secondary" onclick="removeNotification(this)" title="ลบการแจ้งเตือนนี้">
                        <i class="fal fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // เพิ่มที่ด้านบน
    notificationsList.insertBefore(notificationItem, notificationsList.firstChild);
    
    // จำกัดจำนวนการแจ้งเตือน (เก็บแค่ 20 รายการล่าสุด)
    const items = notificationsList.querySelectorAll('.notification-item');
    if (items.length > 20) {
        for (let i = 20; i < items.length; i++) {
            items[i].remove();
        }
    }
    
    // อัพเดทสถิติ
    if (type === 'danger') notificationStats.high++;
    else if (type === 'warning') notificationStats.medium++;
    else notificationStats.info++;
    notificationStats.total++;
    
    updateNotificationCount();
    updateStats();
    
    // แสดง animation
    notificationItem.style.opacity = '0';
    notificationItem.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        notificationItem.style.transition = 'all 0.3s ease-in-out';
        notificationItem.style.opacity = '1';
        notificationItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeNotification(button) {
    const notificationItem = button.closest('.notification-item');
    notificationItem.style.transition = 'all 0.3s ease-in-out';
    notificationItem.style.opacity = '0';
    notificationItem.style.transform = 'translateX(100%)';
    
    setTimeout(() => {
        notificationItem.remove();
        updateNotificationCount();
        
        // แสดง "ยังไม่มีการแจ้งเตือน" ถ้าไม่มีการแจ้งเตือนเหลือ
        const remainingItems = document.querySelectorAll('.notification-item');
        if (remainingItems.length === 0) {
            document.getElementById('no-notifications').style.display = 'block';
        }
    }, 300);
}

function clearAllNotifications() {
    const notificationsList = document.getElementById('notifications-list');
    const items = notificationsList.querySelectorAll('.notification-item');
    
    items.forEach((item, index) => {
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease-in-out';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                item.remove();
                if (index === items.length - 1) {
                    document.getElementById('no-notifications').style.display = 'block';
                    updateNotificationCount();
                }
            }, 300);
        }, index * 100);
    });
    
    // Reset stats
    notificationStats = { high: 0, medium: 0, info: 0, total: 0 };
    updateStats();
}

function updateNotificationCount() {
    const count = document.querySelectorAll('.notification-item').length;
    const countBadge = document.getElementById('notification-count');
    
    if (count > 0) {
        countBadge.textContent = count;
        countBadge.style.display = 'inline';
    } else {
        countBadge.style.display = 'none';
    }
}

function updateStats() {
    document.getElementById('high-count').textContent = notificationStats.high;
    document.getElementById('medium-count').textContent = notificationStats.medium;
    document.getElementById('info-count').textContent = notificationStats.info;
    document.getElementById('total-count').textContent = notificationStats.total;
}

function testNotification(type, message) {
    const timestamp = new Date().toLocaleString('th-TH');
    const fullMessage = `${message} - ${timestamp}`;
    addNotificationToArea(fullMessage, type, timestamp);
}

function testMultipleNotifications() {
    testNotification('danger', 'High Priority Alert 1');
    setTimeout(() => testNotification('warning', 'Medium Priority Alert 2'), 500);
    setTimeout(() => testNotification('info', 'Information Alert 3'), 1000);
    setTimeout(() => testNotification('danger', 'High Priority Alert 4'), 1500);
    setTimeout(() => testNotification('warning', 'Medium Priority Alert 5'), 2000);
}

async function testRealAlerts() {
    try {
        const response = await fetch('?url=manager_charecter/api/character-data&action=alerts', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        const data = JSON.parse(responseText);
        
        if (data.success && data.data && data.data.length > 0) {
            data.data.forEach(alert => {
                addNotificationToArea(alert.message, alert.severity, alert.time);
            });
        } else {
            addNotificationToArea('ไม่มีการแจ้งเตือนจาก API ในขณะนี้', 'info', new Date().toLocaleString('th-TH'));
        }
        
    } catch (error) {
        addNotificationToArea('เกิดข้อผิดพลาดในการดึงข้อมูลจาก API: ' + error.message, 'danger', new Date().toLocaleString('th-TH'));
    }
}

// Auto-show notification area และเพิ่มตัวอย่าง
$(document).ready(function() {
    setTimeout(() => {
        toggleNotificationArea();
        addNotificationToArea('ยินดีต้อนรับสู่ระบบการแจ้งเตือนแบบใหม่!', 'info', new Date().toLocaleString('th-TH'));
    }, 1000);
});
</script>

<style>
/* Notification Area */
.notification-area {
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    transition: all 0.3s ease-in-out;
    background-color: #fff;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none !important;
}

.notification-message {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

#notification-count {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
    padding: 0 4px;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
