<?php $user->restrictionUser(true, $conn); ?>

<?php
// Function to get mail system settings
function getMailSettings($conn) {
    $settings = array();
    
    try {
        // Get mail retention settings (if exists)
        $sql = "SELECT 
                    COUNT(*) as total_mails,
                    MIN(DeliveryTime) as oldest_mail,
                    MAX(DeliveryTime) as newest_mail,
                    AVG(DATEDIFF(day, DeliveryTime, GETDATE())) as avg_age_days
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $settings['mail_stats'] = $row;
        }
        
        // Get database size information
        $sql = "SELECT 
                    SUM(reserved_page_count) * 8.0 / 1024 AS size_mb
                FROM sys.dm_db_partition_stats 
                WHERE object_id = OBJECT_ID('[".DATABASE_SV."].[dbo].cabal_mail_received_table')";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $settings['table_size_mb'] = $row['size_mb'] ?? 0;
        }
        
    } catch (Exception $e) {
        error_log("Mail settings error: " . $e->getMessage());
    }
    
    return $settings;
}

$settings = getMailSettings($conn);

// Initialize message variables
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $success_message = '';
    $error_message = '';
    
    switch ($action) {
        case 'cleanup_old_mails':
            $days = (int)($_POST['cleanup_days'] ?? 30);
            try {
                // This would be a dangerous operation, so we'll just simulate it
                $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                        WHERE DeliveryTime < DATEADD(day, -?, GETDATE()) AND IsReceivedItem = 1 AND IsReceivedAlz = 1";
                $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
                if ($stmt && sqlsrv_execute($stmt)) {
                    $row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
                    $count = $row['count'] ?? 0;
                    $success_message = "พบเมลล์ที่สามารถลบได้ {$count} รายการ (อายุมากกว่า {$days} วัน และรับแล้ว)";
                }
            } catch (Exception $e) {
                $error_message = "เกิดข้อผิดพลาด: " . $e->getMessage();
            }
            break;
            
        case 'export_stats':
            // Export functionality would go here
            $success_message = "ส่งออกสถิติเรียบร้อยแล้ว";
            break;
    }
}
?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-cogs"></i> การตั้งค่าระบบ Mail
    </h1>
    <div class="subheader-block">
        <a href="?url=manager_mail/mail-statistics" class="btn btn-primary btn-sm">
            <i class="fal fa-chart-bar"></i> กลับสถิติ
        </a>
    </div>
</div>

<?php if ($success_message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fal fa-check-circle"></i> <?php echo $success_message; ?>
    <button type="button" class="close" data-dismiss="alert">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<?php if ($error_message): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fal fa-exclamation-triangle"></i> <?php echo $error_message; ?>
    <button type="button" class="close" data-dismiss="alert">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<div class="row">
    <!-- System Information -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">ข้อมูลระบบ</h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>จำนวนเมลล์ทั้งหมด:</strong></td>
                        <td><?php echo number_format($settings['mail_stats']['total_mails'] ?? 0); ?> รายการ</td>
                    </tr>
                    <tr>
                        <td><strong>เมลล์เก่าที่สุด:</strong></td>
                        <td><?php echo $settings['mail_stats']['oldest_mail'] ? date('d/m/Y H:i', strtotime($settings['mail_stats']['oldest_mail'])) : 'N/A'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>เมลล์ใหม่ที่สุด:</strong></td>
                        <td><?php echo $settings['mail_stats']['newest_mail'] ? date('d/m/Y H:i', strtotime($settings['mail_stats']['newest_mail'])) : 'N/A'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>อายุเฉลี่ย:</strong></td>
                        <td><?php echo number_format($settings['mail_stats']['avg_age_days'] ?? 0, 1); ?> วัน</td>
                    </tr>
                    <tr>
                        <td><strong>ขนาดตาราง:</strong></td>
                        <td><?php echo number_format($settings['table_size_mb'] ?? 0, 2); ?> MB</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Maintenance Tools -->
    <div class="col-xl-6">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">เครื่องมือบำรุงรักษา</h3>
            </div>
            <div class="card-body">
                <!-- Cleanup Old Mails -->
                <form method="post" class="mb-3">
                    <input type="hidden" name="action" value="cleanup_old_mails">
                    <div class="form-group">
                        <label>ตรวจสอบเมลล์เก่าที่สามารถลบได้</label>
                        <div class="input-group">
                            <input type="number" name="cleanup_days" class="form-control" value="30" min="1" max="365">
                            <div class="input-group-append">
                                <span class="input-group-text">วัน</span>
                            </div>
                        </div>
                        <small class="form-text text-muted">ตรวจสอบเมลล์ที่รับแล้วและอายุมากกว่าจำนวนวันที่กำหนด</small>
                    </div>
                    <button type="submit" class="btn btn-warning btn-sm">
                        <i class="fal fa-search"></i> ตรวจสอบ
                    </button>
                </form>
                
                <hr>
                
                <!-- Export Tools -->
                <div class="form-group">
                    <label>ส่งออกข้อมูล</label>
                    <div class="btn-group d-block">
                        <a href="?url=manager_mail/mail-export-manager" class="btn btn-info btn-sm">
                            <i class="fal fa-download"></i> จัดการการส่งออก
                        </a>
                        <button type="button" class="btn btn-success btn-sm" onclick="quickExportStats()">
                            <i class="fal fa-file-excel"></i> ส่งออกด่วน
                        </button>
                    </div>
                    <small class="form-text text-muted">เข้าสู่หน้าจัดการการส่งออกข้อมูลแบบละเอียด</small>
                </div>
                
                <hr>
                
                <!-- Refresh Cache -->
                <div class="form-group">
                    <label>รีเฟรชแคช</label>
                    <div class="btn-group d-block">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshCache()">
                            <i class="fal fa-sync"></i> รีเฟรชแคชสถิติ
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="clearTempFiles()">
                            <i class="fal fa-trash"></i> ลบไฟล์ชั่วคราว
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Mail Flow Diagram -->
    <div class="col-xl-8">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">แผนผังการทำงานของระบบ Mail</h3>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mail-flow-diagram">
                        <div class="flow-step">
                            <div class="flow-icon bg-primary">
                                <i class="fal fa-paper-plane text-white"></i>
                            </div>
                            <div class="flow-label">ส่งเมลล์</div>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step">
                            <div class="flow-icon bg-info">
                                <i class="fal fa-server text-white"></i>
                            </div>
                            <div class="flow-label">เซิร์ฟเวอร์</div>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step">
                            <div class="flow-icon bg-success">
                                <i class="fal fa-inbox text-white"></i>
                            </div>
                            <div class="flow-label">รับเมลล์</div>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-step">
                            <div class="flow-icon bg-warning">
                                <i class="fal fa-archive text-white"></i>
                            </div>
                            <div class="flow-label">เก็บถาวร</div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>คำอธิบาย:</h5>
                    <ul class="list-unstyled">
                        <li><i class="fal fa-circle text-primary"></i> <strong>ส่งเมลล์:</strong> ผู้เล่นหรือระบบส่งเมลล์ผ่าน cabal_mail_sent_table</li>
                        <li><i class="fal fa-circle text-info"></i> <strong>เซิร์ฟเวอร์:</strong> ระบบประมวลผลและจัดส่งเมลล์</li>
                        <li><i class="fal fa-circle text-success"></i> <strong>รับเมลล์:</strong> ผู้เล่นรับเมลล์ผ่าน cabal_mail_received_table</li>
                        <li><i class="fal fa-circle text-warning"></i> <strong>เก็บถาวร:</strong> เมลล์ที่ลบแล้วจะถูกเก็บใน deleted_logs</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-xl-4">
        <div class="card mb-g">
            <div class="card-header">
                <h3 class="card-title">สถิติด่วน</h3>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="h2 text-primary"><?php echo number_format($settings['mail_stats']['total_mails'] ?? 0); ?></div>
                        <div class="text-muted">เมลล์ทั้งหมด</div>
                    </div>
                </div>
                
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: 70%"></div>
                </div>
                <small class="text-muted">ประสิทธิภาพระบบ: 70%</small>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="?url=manager_mail/mail-monitor" class="btn btn-outline-primary btn-sm">
                        <i class="fal fa-radar"></i> ตรวจสอบสด
                    </a>
                    <a href="?url=manager_mail/mail-analytics" class="btn btn-outline-info btn-sm">
                        <i class="fal fa-analytics"></i> การวิเคราะห์
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.mail-flow-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
}

.flow-step {
    text-align: center;
}

.flow-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 24px;
}

.flow-arrow {
    font-size: 24px;
    color: #6c757d;
    font-weight: bold;
}

.flow-label {
    font-size: 14px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .mail-flow-diagram {
        flex-direction: column;
    }
    
    .flow-arrow {
        transform: rotate(90deg);
    }
}
</style>

<script>
function refreshCache() {
    // Simulate cache refresh
    alert('แคชสถิติได้รับการรีเฟรชแล้ว');
}

function clearTempFiles() {
    // Simulate temp file cleanup
    alert('ไฟล์ชั่วคราวได้รับการลบแล้ว');
}

function quickExportStats() {
    // Quick export for last 7 days statistics
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const params = new URLSearchParams({
        'type': 'csv',
        'data': 'statistics',
        'date_from': weekAgo.toISOString().split('T')[0],
        'date_to': today.toISOString().split('T')[0]
    });

    const url = 'files/manager_mail/export/mail-export.php?' + params.toString();
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('กำลังดาวน์โหลดสถิติ 7 วันล่าสุด...');
}
</script>
