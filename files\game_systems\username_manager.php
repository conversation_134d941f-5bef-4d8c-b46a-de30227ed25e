<?php $zpanel->checkSession(true); ?>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-users"></i> ระบบจัดการ Username ในกิจกรรม</h5>
    </div>
    <div class="card-body">
        <!-- เลือก Table -->
        <div class="row mb-4">
            <div class="col-md-12">
                <label for="tableSelect" class="form-label">เลือก Table:</label>
                <select id="tableSelect" class="form-control">
                    <option value="">-- เลือก Table --</option>
                    <option value="WEB_Discord_boost_userLog">WEB_Discord_boost_userLog</option>
                    <option value="WEB_Donate_Title_userLog">WEB_Donate_Title_userLog</option>
                    <option value="WEB_Event_RewardLog">WEB_Event_RewardLog</option>
                    <option value="WEB_EventOBT_DataLog">WEB_EventOBT_DataLog</option>
                    <option value="WEB_Facebook_Share">WEB_Facebook_Share</option>
                    <option value="WEB_Gamer_Steamer_userLog">WEB_Gamer_Steamer_userLog</option>
                </select>
            </div>
        </div>

        <!-- แสดงข้อมูลและเพิ่ม/ลบ Username -->
        <div id="userManagementSection" style="display: none;">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">เพิ่ม Username ใหม่</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="newUsername">Username:</label>
                                <input type="text" id="newUsername" class="form-control" placeholder="กรอก Username">
                            </div>
                            <button id="addUsernameBtn" class="btn btn-success">
                                <i class="fas fa-plus"></i> เพิ่ม Username
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">ข้อมูลปัจจุบัน</h6>
                        </div>
                        <div class="card-body">
                            <div id="currentTableInfo">
                                <p class="text-muted">เลือก Table เพื่อดูข้อมูล</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">รายการ Username ใน <span id="currentTableName"></span></h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usernameTable">
                            <thead class="thead-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Status</th>
                                    <th>เวลาที่เพิ่ม</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody id="usernameTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal สำหรับแก้ไข Username -->
<div class="modal fade" id="editUsernameModal" tabindex="-1" role="dialog" aria-labelledby="editUsernameModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUsernameModalLabel">แก้ไข Username</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editUsernameForm">
                    <input type="hidden" id="editUserId" name="id">
                    <div class="form-group">
                        <label for="editUsername">Username:</label>
                        <input type="text" id="editUsername" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="editStatus">Status:</label>
                        <select id="editStatus" name="status" class="form-control">
                            <option value="1">เปิดใช้งาน</option>
                            <option value="0">ปิดใช้งาน</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">บันทึกการแก้ไข</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Event handlers
    $('#tableSelect').change(function() {
        const tableName = $(this).val();

        if (tableName) {
            $('#userManagementSection').show();
            $('#currentTableName').text(tableName);
            loadTableInfo(tableName);
            loadUsernameData(tableName);
        } else {
            $('#userManagementSection').hide();
        }
    });
    
    $('#addUsernameBtn').click(function() {
        addUsername();
    });

    // Enter key สำหรับเพิ่ม username
    $('#newUsername').keypress(function(e) {
        if (e.which == 13) {
            addUsername();
        }
    });

    // Event handler สำหรับบันทึกการแก้ไข
    $('#saveEditBtn').click(function() {
        saveEditUsername();
    });
});

function loadTableInfo(tableName) {
    const info = `
        <strong>Table:</strong> ${tableName}<br>
        <strong>คำอธิบาย:</strong> ${getTableDescription(tableName)}<br>
        <strong>สถานะ:</strong> <span class="badge badge-success">พร้อมใช้งาน</span>
    `;
    $('#currentTableInfo').html(info);
}

function getTableDescription(tableName) {
    const descriptions = {
        'WEB_Discord_boost_userLog': 'ระบบ Discord Boost',
        'WEB_Donate_Title_userLog': 'ระบบ Donate Title',
        'WEB_Event_RewardLog': 'ระบบ Event Reward',
        'WEB_EventOBT_DataLog': 'ระบบ Event OBT',
        'WEB_Facebook_Share': 'ระบบ Facebook Share',
        'WEB_Gamer_Steamer_userLog': 'ระบบ Gamer Streamer'
    };
    return descriptions[tableName] || 'ไม่มีคำอธิบาย';
}

function loadUsernameData(tableName) {
    $.post('files/game_systems/class_module/username_manager_api.php', {
        action: 'list',
        table: tableName
    }, function(data) {
        const tbody = $('#usernameTableBody');
        tbody.empty();
        
        if (data.success && data.data.length > 0) {
            data.data.forEach(function(row) {
                const statusText = row.status == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
                const statusClass = row.status == 1 ? 'success' : 'secondary';
                
                tbody.append(`
                    <tr>
                        <td>${row.id}</td>
                        <td><strong>${row.userid}</strong></td>
                        <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                        <td>${row.added_time}</td>
                        <td>
                            <button class="btn btn-sm btn-warning mr-1" onclick="editUsername(${row.id}, '${row.userid}', ${row.status}, '${tableName}')">
                                <i class="fas fa-edit"></i> แก้ไข
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUsername(${row.id}, '${tableName}')">
                                <i class="fas fa-trash"></i> ลบ
                            </button>
                        </td>
                    </tr>
                `);
            });
        } else {
            tbody.append('<tr><td colspan="5" class="text-center">ไม่มีข้อมูล</td></tr>');
        }
    }, 'json').fail(function() {
        alert('ไม่สามารถโหลดข้อมูลได้');
    });
}

function addUsername() {
    const username = $('#newUsername').val().trim();
    const tableName = $('#tableSelect').val();

    if (!username) {
        alert('กรุณากรอก Username');
        return;
    }

    if (!tableName) {
        alert('กรุณาเลือก Table');
        return;
    }

    $.post('files/game_systems/class_module/username_manager_api.php', {
        action: 'add',
        table: tableName,
        username: username
    }, function(data) {
        if (data.success) {
            $('#newUsername').val('');
            loadUsernameData(tableName);
            alert('เพิ่ม Username สำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถเพิ่มข้อมูลได้'));
        }
    }, 'json').fail(function() {
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
    });
}

function editUsername(id, username, status, tableName) {
    $('#editUserId').val(id);
    $('#editUsername').val(username);
    $('#editStatus').val(status);

    // เก็บข้อมูล table สำหรับใช้ในการบันทึก
    $('#editUsernameModal').data('tableName', tableName);

    $('#editUsernameModal').modal('show');
}

function saveEditUsername() {
    const id = $('#editUserId').val();
    const username = $('#editUsername').val().trim();
    const status = $('#editStatus').val();
    const tableName = $('#editUsernameModal').data('tableName');

    if (!username) {
        alert('กรุณากรอก Username');
        return;
    }

    $.post('files/game_systems/class_module/username_manager_api.php', {
        action: 'edit',
        table: tableName,
        id: id,
        username: username,
        status: status
    }, function(data) {
        if (data.success) {
            $('#editUsernameModal').modal('hide');
            loadUsernameData(tableName);
            alert('แก้ไข Username สำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถแก้ไขข้อมูลได้'));
        }
    }, 'json').fail(function() {
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
    });
}

function deleteUsername(id, tableName) {
    if (!confirm('คุณต้องการลบ Username นี้หรือไม่?')) {
        return;
    }

    $.post('files/game_systems/class_module/username_manager_api.php', {
        action: 'delete',
        table: tableName,
        id: id
    }, function(data) {
        if (data.success) {
            loadUsernameData(tableName);
            alert('ลบ Username สำเร็จ');
        } else {
            alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบข้อมูลได้'));
        }
    }, 'json').fail(function() {
        alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
    });
}
</script>
