<?php $user->restrictionUser(true, $conn); ?>
					<header class="page-header">
						<h2>ระบบตรวจสอบแคส</h2>
						<div class="right-wrapper pull-right">
							<ol class="breadcrumbs">
								<li>
									<a href="home.php">
										<i class="fa fa-home"></i>
									</a>
								</li>
								<li><span>manager</span></li>
								<li><span>ระบบแคส</span></li>
							</ol>
							<a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
						</div>
					</header>

					<!-- start: page -->
						<section class="panel">
							<header class="panel-heading">
								<div class="panel-actions">
									<a href="#" class="panel-action panel-action-toggle" data-panel-toggle=""></a>
									<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss=""></a>
								</div>
								<h2 class="panel-title">Cash</h2>
							</header>
			<div class="panel-body">
                            <?php
                // variable
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 20;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM ".DATABASE_CCA.".dbo.CashAccount WHERE ID LIKE '%$getResult%' OR UserNum LIKE '$$getResult%'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Clan not found</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>UserNum</th>
                            <th>Cash</th>
                            <th>CashBonus</th>
                            <th>TotalCash</th>
                            <th>Reward</th>
                            <th>UpdateDateTime</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr>
                                        <td><?php echo $row[0]; ?></td>
                                        <td><?php echo $row[1]; ?></td>
                                        <td><?php echo $row[2]; ?></td>
                                        <td><?php echo $row[3]; ?></td>
                                        <td><?php echo $row[4]; ?></td>
                                        <td><?php echo $row[7]; ?></td>
                                        <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                                    <?php echo B_ACTION; ?> <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" role="menu">
                                                <li><a href="?url=manager/reward-item&id=<?php echo $row[1]; ?>">แลก Reward</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=helpdesk/a/tickets&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=helpdesk/a/tickets&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>