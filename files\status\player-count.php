<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
 */
ob_start();
session_start();

require('../_app/dbinfo.inc.php');
require('../_app/general_config.inc.php');
require('../_app/variables.php');

// sql inject protection
require('../_app/php/sql_inject.php');
require('../_app/php/sql_check.php');

// zpanel class
require('../_app/php/zpanel.class.php');
$zpanel = new zpanel();


//gamecabal class
require('../_app/php/game.class.php');
$game = new game();

$getCharID = filter_input(INPUT_GET, 'status', FILTER_VALIDATE_INT);
 $gamertagForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
 $countplayer = $game->countuser($conn, $getCharID) ? $game->countuser($conn, $getCharID) : '0';
?>
<!DOCTYPE html>
<html lang="<?php echo $_COOKIE['lang']; ?>">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title><?php echo $zpanel->getConfigByValue('title', 'value', $conn) . ' &middot; ' . $zpanel->getConfigByValue('description', 'value', $conn); ?></title>
        <meta name="description" content="<?php echo $zpanel->getConfigByValue('description', 'value', $conn); ?>">
        <meta name="author" content="FDEV">

         <!-- FAVICON -->
        <link rel="shortcut icon" href="../assets/images/icons/favicon.ico" type="image/x-icon" >
        <!-- core CSS -->
        <link rel="stylesheet" href="../assets/css/bootstrap/bootstrap.css" />
        <link rel="stylesheet" href="../assets/css/plugins/calendar/calendar.css" />
        <link rel="stylesheet" href="../assets/css/switch-buttons/switch-buttons.css" />
        <link rel="stylesheet" href="../assets/css/fontawesome/css/fontawesome-all.css" />
		
        <!-- Fonts  -->
       <link href="https://fonts.googleapis.com/css?family=Kanit:300" rel="stylesheet">


        <!-- Base Styling  -->

        <style type="text/css">
            body,td,th {
                font-family: Kanit, sans-serif;
            }
        </style>

        <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
          <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->

    </head>

    <body data-ng-app>
                   
					<?php if (isset($returnSuccess)) { ?>
                        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                    <?php } elseif (isset($returnWarning)) { ?>
                        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                    <?php } elseif (isset($returnError)) { ?>
                        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                    <?php } elseif (isset($returnWarningGLOBAL)) { ?>
                        <div class="alert alert-warning"><?php echo $returnWarningGLOBAL; ?></div>
                    <?php } ?>
					<center>
					  <span class="pull">
                                    
                                         <a href="#" class="label label-warning text-white btn" >
										<font size="3">
                                         <?php 
                                          echo $countplayer;
                                         ?></font>
                                         </a>
                                    
                            </span>
					</center>
        <!-- JQuery v1.9.1 -->
        <script src="../assets/js/jquery/jquery-1.9.1.min.js" type="text/javascript"></script>
        <script src="../assets/js/plugins/underscore/underscore-min.js"></script>
        <script src="../assets/js/bootstrap/bootstrap.min.js"></script>
        <script src="../assets/js/globalize/globalize.min.js"></script>
        <script src="../assets/js/plugins/nicescroll/jquery.nicescroll.min.js"></script>
        <script src="../assets/js/plugins/sparkline/jquery.sparkline.min.js"></script>
        <!--<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.3.0-beta.14/angular.min.js"></script>-->
        <script src="../assets/js/angular/todo.js"></script>
        <script src="../assets/js/plugins/calendar/calendar.js"></script>
        <script src="../assets/js/plugins/calendar/calendar-conf.js"></script>
        <script src="../assets/js/plugins/inputmask/jquery.inputmask.bundle.js"></script>
        <!-- Wysihtml5 -->
        <script src="../assets/js/plugins/bootstrap-wysihtml5/wysihtml5-0.3.0.min.js"></script>
        <script src="../assets/js/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.js"></script>
        <!-- Custom JQuery -->
        <script src="../assets/js/app/custom.js" type="text/javascript"></script>
        <script src="../assets/js/app/zpanel.js" type="text/javascript"></script>
        <script src="../assets/js/plugins/smooth-anchor/jquery.anchor.js"></script>
		<!-- Paginationy -->
		 <script src="../assets/js/app/jquery.Pagination.js" ></script>

    </body>
</html>
