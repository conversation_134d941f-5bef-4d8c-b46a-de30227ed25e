<div class="page-header"><h1><?php echo PT_REDEEM_CODE; ?> <small><?php echo PT_REDEEM_CODE_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <div class="col-lg-8">
                    <?php
                    $formRedeem = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                    if (isset($formRedeem) && $formRedeem['btn_redeem']) {

                        $getID = $userLogin->recUserAccount('ID', $conn);

                        if (empty($formRedeem['code'])) {
                            $returnWarning = W_EMPTY_INPUT;
                        } else {

                            // select codes
                            $selectCode = "SELECT * FROM WEB_Redeem_Code WHERE code = '$formRedeem[code]'";
                            $selectParam = array();
                            $selectOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectQuery = sqlsrv_query($conn, $selectCode, $selectParam, $selectOption);

                            if (sqlsrv_num_rows($selectQuery)) {

                                $selectCodeFetch = sqlsrv_fetch_array($selectQuery, SQLSRV_FETCH_ASSOC);
                                if ($selectCodeFetch['status'] == '0') {
                                    $explodeItems = explode(',', $selectCodeFetch['items']);

                                    foreach ($explodeItems as $key => $value):
                                        // executa a query
                                        $redeemCode = "EXECUTE [". DATABASE_NAME ."].[dbo].FN_AddFullItemToUser ?, ?, ?, -1, -1, 10000";
                                        $redeemCodeParam = array($getID, $value, $selectCodeFetch['quantity']);
                                        $redeemCodeQuery = sqlsrv_query($conn, $redeemCode, $redeemCodeParam);
                                    endforeach;

                                    if ($redeemCodeQuery) {
                                        $updateCode = "UPDATE WEB_Redeem_Code SET status = '1' WHERE id = '$selectCodeFetch[id]'";
                                        $updateCodeQuery = sqlsrv_query($conn, $updateCode, array());
                                        
                                        $insertCodeUsed = "INSERT INTO WEB_Redeem_CodesUsed (CodeID, UserNum, code) VALUES ('$selectCodeFetch[id]', '$getID', '$selectCodeFetch[code]')";
                                        $insertCodeUsedQuery = sqlsrv_query($conn, $insertCodeUsed, array());
                                        $returnSuccess = S_REDEEM_CODE;
                                    } else {
                                        $returnError = E_REDEEM_CODE;
                                    }
                                } else {
                                    $returnWarning = W_CODEALREADY_USED;
                                }
                            } else {
                                $returnError = E_CODE_NOTEXISTS;
                            }
                        }
                    }
                    ?>
                    <?php if (isset($returnWarning)) { ?>
                        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                    <?php } elseif (isset($returnError)) { ?>
                        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                    <?php } elseif (isset($returnSuccess)) { ?>
                        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                    <?php } ?>
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="code" class="text-red"><?php echo T_YOURCODE; ?></label>
                            <input type="text" id="code" class="form-control" name="code" placeholder="<?php echo T_YOURCOREHERE; ?>">
                        </div>
                        <div class="form-group">
                            <input type="submit" name="btn_redeem" class="btn btn-success btn-block" value="<?php echo B_REDEEM; ?>">
                        </div>
                    </form>
                </div>

                <div class="col-lg-4">
                    <h2><?php echo T_LASTCODE; ?>:</h2>
                    <p class="bg-info padd-sm text-white">
                        <?php
                            $selectUsedCodes = "SELECT * FROM WEB_Redeem_CodesUsed ORDER BY dateused DESC";
                            $selectUsedCodesQuery = sqlsrv_query($conn, $selectUsedCodes, array(), array("Scrollable" => SQLSRV_CURSOR_KEYSET));
                            if(sqlsrv_num_rows($selectUsedCodesQuery)){
                                $selectUsedCodeFetch = sqlsrv_fetch_array($selectUsedCodesQuery, SQLSRV_FETCH_ASSOC);
                                echo $selectUsedCodeFetch['code'];
                            }else{
                                echo "No code was still used";
                            }
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>