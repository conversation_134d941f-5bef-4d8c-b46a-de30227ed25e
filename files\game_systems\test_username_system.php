<?php
// ไฟล์ทดสอบระบบ Username Manager
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

$conn = db_connect();

echo "<h2>ทดสอบระบบ Username Manager</h2>";

// ทดสอบการเชื่อมต่อฐานข้อมูล
if ($conn) {
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>";
    exit;
}

// ทดสอบการดึงข้อมูลจาก WEB_Event_Data
echo "<h3>ทดสอบการดึงข้อมูลจาก WEB_Event_Data:</h3>";
$sql = "SELECT TOP 5 id, Event, status FROM WEB_Event_Data ORDER BY id DESC";
$stmt = sqlsrv_query($conn, $sql);

if ($stmt) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Event</th><th>Status</th></tr>";
    
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['Event'] . "</td>";
        echo "<td>" . ($row['status'] == 1 ? 'เปิดใช้งาน' : 'ปิดใช้งาน') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p style='color: green;'>✅ ดึงข้อมูลจาก WEB_Event_Data สำเร็จ</p>";
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถดึงข้อมูลจาก WEB_Event_Data ได้</p>";
    echo "<p>Error: " . print_r(sqlsrv_errors(), true) . "</p>";
}

// ทดสอบการตรวจสอบ table ที่มีอยู่
echo "<h3>ทดสอบการตรวจสอบ Tables ที่มีอยู่:</h3>";
$allowedTables = [
    'WEB_Discord_boost_userLog',
    'WEB_Donate_Title_userLog', 
    'WEB_Event_RewardLog',
    'WEB_EventOBT_DataLog',
    'WEB_Facebook_Share',
    'WEB_Gamer_Steamer_userLog'
];

foreach ($allowedTables as $table) {
    $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
    $checkStmt = sqlsrv_query($conn, $checkSql, [$table]);
    
    if ($checkStmt) {
        $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
        if ($result['count'] > 0) {
            echo "<p style='color: green;'>✅ Table '$table' มีอยู่แล้ว</p>";
            
            // ลองดึงข้อมูลจาก table
            $dataSql = "SELECT TOP 3 * FROM [$table]";
            $dataStmt = sqlsrv_query($conn, $dataSql);
            if ($dataStmt) {
                $rowCount = sqlsrv_num_rows($dataStmt);
                if ($rowCount !== false) {
                    echo "<p>&nbsp;&nbsp;&nbsp;📊 มีข้อมูล $rowCount แถว</p>";
                } else {
                    echo "<p>&nbsp;&nbsp;&nbsp;📊 ไม่สามารถนับจำนวนแถวได้</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Table '$table' ยังไม่มี (จะสร้างอัตโนมัติเมื่อใช้งาน)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบ table '$table' ได้</p>";
    }
}

// ทดสอบการสร้าง table ใหม่ (ถ้าจำเป็น)
echo "<h3>ทดสอบการสร้าง Table ใหม่:</h3>";
$testTable = 'WEB_Test_Username_Manager';

// ลบ table ทดสอบถ้ามีอยู่
$dropSql = "IF OBJECT_ID('$testTable', 'U') IS NOT NULL DROP TABLE [$testTable]";
sqlsrv_query($conn, $dropSql);

// สร้าง table ใหม่
$createSql = "CREATE TABLE [$testTable] (
    [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [userid] varchar(50) COLLATE Thai_CI_AS NULL,
    [status] tinyint NULL DEFAULT 1,
    [added_time] datetime NOT NULL DEFAULT GETDATE()
)";

$createStmt = sqlsrv_query($conn, $createSql);
if ($createStmt) {
    echo "<p style='color: green;'>✅ สร้าง table ทดสอบสำเร็จ</p>";
    
    // ทดสอบการเพิ่มข้อมูล
    $insertSql = "INSERT INTO [$testTable] (userid, status) VALUES (?, ?)";
    $insertStmt = sqlsrv_query($conn, $insertSql, ['test_user', 1]);

    if ($insertStmt) {
        echo "<p style='color: green;'>✅ เพิ่มข้อมูลทดสอบสำเร็จ</p>";

        // ทดสอบการดึงข้อมูล
        $selectSql = "SELECT * FROM [$testTable]";
        $selectStmt = sqlsrv_query($conn, $selectSql);

        if ($selectStmt && $row = sqlsrv_fetch_array($selectStmt, SQLSRV_FETCH_ASSOC)) {
            echo "<p style='color: green;'>✅ ดึงข้อมูลทดสอบสำเร็จ: " . $row['userid'] . "</p>";

            // ทดสอบการแก้ไขข้อมูล
            $updateSql = "UPDATE [$testTable] SET userid = ?, status = ? WHERE id = ?";
            $updateStmt = sqlsrv_query($conn, $updateSql, ['test_user_edited', 0, $row['id']]);

            if ($updateStmt) {
                echo "<p style='color: green;'>✅ แก้ไขข้อมูลทดสอบสำเร็จ</p>";

                // ตรวจสอบการแก้ไข
                $checkSql = "SELECT * FROM [$testTable] WHERE id = ?";
                $checkStmt = sqlsrv_query($conn, $checkSql, [$row['id']]);

                if ($checkStmt && $updatedRow = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC)) {
                    echo "<p style='color: green;'>✅ ตรวจสอบการแก้ไขสำเร็จ: " . $updatedRow['userid'] . " (status: " . $updatedRow['status'] . ")</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถแก้ไขข้อมูลทดสอบได้</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มข้อมูลทดสอบได้</p>";
    }
    
    // ลบ table ทดสอบ
    $dropTestSql = "DROP TABLE [$testTable]";
    sqlsrv_query($conn, $dropTestSql);
    echo "<p style='color: blue;'>🗑️ ลบ table ทดสอบแล้ว</p>";
    
} else {
    echo "<p style='color: red;'>❌ ไม่สามารถสร้าง table ทดสอบได้</p>";
    echo "<p>Error: " . print_r(sqlsrv_errors(), true) . "</p>";
}

echo "<h3>สรุปผลการทดสอบ:</h3>";
echo "<p>✅ ระบบพร้อมใช้งาน</p>";
echo "<p>📝 คุณสามารถเข้าใช้งานระบบได้ที่: <a href='username_manager.php'>Username Manager</a></p>";
echo "<p>🔗 หรือผ่านเมนู: จัดการระบบเกมส์ > จัดการ Username ในกิจกรรม</p>";
?>
