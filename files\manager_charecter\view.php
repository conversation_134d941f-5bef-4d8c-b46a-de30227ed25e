<?php
// --- Session & Permission ---
$user->restrictionUser(true, $conn);
$zpanel->checkSession(true);

// --- Init ---
$returnSuccess = $returnWarning = $returnError = null;
$charIdx = filter_input(INPUT_GET, 'char_id', FILTER_VALIDATE_INT);
if ($charIdx === false || $charIdx === null) {
    header('Location: ?url=manager_account/manage-account'); exit;
}
// --- Item Name Map for Inventory ---
require_once __DIR__ . '/itemname_helper.php';
$itemNameMap = getItemNameMap(__DIR__ . '/../game_systems/import/itemlist.xml');

// --- Fetch Character Data ---
$characterData = $soulAbilityData = $warExpData = $inventoryData = $skillData = null;
$selectCharacterSql = "SELECT * FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = ?";
$selectCharacterQuery = sqlsrv_query($conn, $selectCharacterSql, [$charIdx]);
if ($selectCharacterQuery && sqlsrv_has_rows($selectCharacterQuery)) {
    $characterData = sqlsrv_fetch_array($selectCharacterQuery, SQLSRV_FETCH_ASSOC);
    $userNum = floor($characterData['CharacterIdx']/16) ?? null;
    // Soul Ability
    $selectSoulAbilityQuery = sqlsrv_query($conn, "SELECT * FROM ".DATABASE_SV.".dbo.cabal_soul_ability_table WHERE CharacterIdx = ?", [$charIdx]);
    if ($selectSoulAbilityQuery && sqlsrv_has_rows($selectSoulAbilityQuery)) {
        $soulAbilityData = sqlsrv_fetch_array($selectSoulAbilityQuery, SQLSRV_FETCH_ASSOC);
    }
    // War Exp
    $selectWarExpQuery = sqlsrv_query($conn, "SELECT * FROM ".DATABASE_SV.".dbo.cabal_WarExp_Table WHERE CharacterIdx = ?", [$charIdx]);
    if ($selectWarExpQuery && sqlsrv_has_rows($selectWarExpQuery)) {
        $warExpData = sqlsrv_fetch_array($selectWarExpQuery, SQLSRV_FETCH_ASSOC);
    }
} else {
    $returnError = defined('E_CHARACTER_NOT_FOUND') ? E_CHARACTER_NOT_FOUND : 'Character not found.';
    $characterData = null;
}

// --- Class & Nation Name ---
$className = $nationName = 'N/A';
if ($characterData) {
    $classInfo = $userLogin->cabalstyle($characterData['Style'] ?? 0);
    $className = $classInfo['Class_Name'] ?? 'N/A';
    $nationName = $userLogin->nation($characterData['Nation']);
}
if (isset($_POST['btn_savechange_chardataname'])){
    $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
    $charIdx = $configForm['charsidx'];
    $charName = $configForm['input_Names'];

    if (empty($charIdx)) {
        echo '<script type="text/javascript">';
        echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ข้อมูลผิดพลาด!","error");';
        echo '});</script>';
    }elseif(empty($charName)){
        echo '<script type="text/javascript">';
        echo 'setTimeout(function () { Swal.fire("NOT FOUND !!!","ข้อมูลผิดพลาด!","error");';
        echo '});</script>';
    }else{

        $win874_bytes = iconv("UTF-8", "Windows-874//IGNORE", $charName);
        $bindLength = strlen($win874_bytes);


        if ($win874_bytes === false || strlen($win874_bytes) === 0) {
                    echo "
                            <script>
                                Swal.fire({
                                    icon: 'error',
                                    title: 'ข้อผิดพลาด!',
                                    text: '❌ แปลงรหัสผิดพลาด',
                                });
                            </script>
                        ";
        }elseif(strlen($win874_bytes) > 16){
                        echo "
                            <script>
                                Swal.fire({
                                    icon: 'error',
                                    title: 'ข้อผิดพลาด!',
                                    text: '❌ ข้อความยาวเกิน 16 bytes',
                                });
                            </script>
                        ";
        }else{

        $sql = "UPDATE " . DATABASE_SV . ".dbo.cabal_character_table SET Name = ? WHERE CharacterIdx = ?";
        $stmt = sqlsrv_prepare($conn, $sql, [ 
            [$win874_bytes, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_STRING('binary'), SQLSRV_SQLTYPE_VARBINARY($bindLength)],
            [(int)$charIdx, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT]
        ]);
                //var_dump(value: $win874_bytes );
                if ($stmt && sqlsrv_execute($stmt)) {
                    echo "
                            <script>
                                Swal.fire({
                                    icon: 'success',
                                    title: ' ".$charName." ',
                                    html: '<strong>ชื่อตัวละคร เปลียนแปลงแล้ว</strong>',
                                }).then(() => {
                                    window.location.href = window.location.href;
                                });
                            </script>
                        ";
                } else {
                    echo "
                            <script>
                                Swal.fire({
                                    icon: 'error',
                                    title: 'ข้อผิดพลาด!',
                                    text: 'การทำงานไม่สำเร็จ',
                                });
                            </script>
                        ";
                }
        }
    }    
}
if (isset($_POST['btn_savechange_chardata'])) {
    // รับค่าจากฟอร์ม
    $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
    $charidx = strip_tags(trim($_POST['charsidx']));

    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($charidx) || empty($configForm['input_lev'])) {
        echo "<script>setTimeout(function () { Swal.fire('NOT FOUND !!!','ข้อมูลผิดพลาด!','error'); });</script>";
        return;
    }

    // ตรวจสอบว่ามี character จริงหรือไม่
    $checkChar = sqlsrv_query($conn, "SELECT 1 FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = ?", [$charidx]);
    if (!sqlsrv_has_rows($checkChar)) {
        echo "<script>setTimeout(function () { Swal.fire('NOT FOUND !!!','ข้อมูลผิดพลาด!','error'); });</script>";
        return;
    }

    // อัปเดตข้อมูล character หลัก
    $updateAll = "UPDATE ".DATABASE_SV.".dbo.cabal_character_table SET
        LEV = ?, STR = ?, INT = ?, DEX = ?, PNT = ?, Alz = ?, WorldIdx = ?, SwdPNT = ?, MagPNT = ?, WarpBField = ?, MapsBField = ?, Reputation = ?, Nation = ?
        WHERE CharacterIdx = ?";
    $params = [
        $configForm['input_lev'], $configForm['input_str'], $configForm['input_int'], $configForm['input_dex'],
        $configForm['input_pnt'], $configForm['input_Alz'], $configForm['input_WorldIdx'], $configForm['input_SwdPNT'],
        $configForm['input_MagPNT'], $configForm['input_WarpBField'], $configForm['input_MapsBField'],
        $configForm['input_Reputation'], $configForm['input_Nation'], $charidx
    ];
    $updateAllQuery = sqlsrv_query($conn, $updateAll, $params);

    // อัปเดต Soul ability point
    $updateConfigAbilityPoint = "UPDATE ".DATABASE_SV.".dbo.cabal_soul_ability_table SET AbilityPoint = ? WHERE CharacterIdx = ?";
    $updateConfigAbilityPointQuery = sqlsrv_query($conn, $updateConfigAbilityPoint, [
        $configForm['input_AbilityPoint'], $charidx
    ]);

    // อัปเดต wexp
    $updateConfigWarExp = "UPDATE ".DATABASE_SV.".dbo.cabal_WarExp_Table SET WarExp = ? WHERE CharacterIdx = ?";
    $updateConfigWarExpQuery = sqlsrv_query($conn, $updateConfigWarExp, [
        $configForm['input_wexp'], $charidx
    ]);

    // แจ้งผลลัพธ์
    if ($updateAllQuery && $updateConfigAbilityPointQuery && $updateConfigWarExpQuery) {
        echo "<script>Swal.fire({position: 'top-end',icon: 'success',title: 'Your work has been saved',showConfirmButton: false,timer: 1500});</script>";
    } else {
        echo "<script>Swal.fire({icon: 'error',title: 'ข้อผิดพลาด!',text: 'การทำงานไม่สำเร็จ',});</script>";
    }
}
       
// --- Handle Inventory Reset ---
if (isset($_POST['btn_reset_inventory'])) {
    $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);
    $charidx = isset($configForm['charsidx']) ? trim($configForm['charsidx']) : '';

    if (empty($charidx)) {
        echo "<script>setTimeout(function () { Swal.fire('NOT FOUND !!!','ตรวจสอบไม่พบข้อมูลผู้เล่นนี้!','error'); });</script>";
        return;
    }

    // Check if character exists
    $checkChar = sqlsrv_query($conn, "SELECT 1 FROM ".DATABASE_SV.".dbo.cabal_character_table WHERE CharacterIdx = ?", [$charidx]);
    if (!$checkChar || !sqlsrv_has_rows($checkChar)) {
        echo "<script>setTimeout(function () { Swal.fire('NOT FOUND !!!','ข้อมูลผิดพลาด!','error'); });</script>";
        return;
    }

    // Reset inventory data
    $resetInventory = sqlsrv_query($conn, "UPDATE ".DATABASE_SV.".dbo.cabal_Inventory_table SET Data = 0x WHERE CharacterIdx = ?", [$charidx]);
    if ($resetInventory) {
        echo "<script>setTimeout(function () { Swal.fire('Success !!!','รีเช็ตข้อข้อมูล ช่องเก็บของ ของตัวละคร ".$charidx." เรียบร้อยแล้ว!','success'); });</script>";
    } else {
        echo "<script>setTimeout(function () { Swal.fire('NOT FOUND !!!','ข้อมูลผิดพลาด!','error'); });</script>";
    }
}

?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class='subheader-icon fal fa-gamepad'></i> Character Details
        <small>รายละเอียดตัวละคร:
            <?php echo htmlspecialchars($userLogin->thaitrans($characterData['Name'] ?? 'N/A')); ?></small>
    </h1>
</div>

<?php if (isset($returnSuccess)) { ?><div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
<?php } elseif (isset($returnWarning)) { ?><div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
<?php } elseif (isset($returnError)) { ?><div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
<?php } ?>

<div class="row">
    <div class="col-xl-12">
        <?php if ($returnSuccess): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $returnSuccess; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php endif; ?>
        <?php if ($returnWarning): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <?php echo $returnWarning; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php endif; ?>
        <?php if ($returnError): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $returnError; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php endif; ?>
    </div>

    <?php if ($characterData): ?>
    <div class="col-lg-4">
        <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
            <div class="card-body text-white text-center py-4">
                <div class="position-relative mb-3">
                    <img class="profile-image rounded-circle border border-secondary border-3"
                        src="assets/images/cabal/class/<?php echo htmlspecialchars($className); ?>.png"
                        alt="<?php echo htmlspecialchars($className); ?>"
                        style="width: 150px; height: 150px; object-fit: cover;">
                    <span class="status-indicator
                        <?php echo ($characterData['Login'] == "0") ? "bg-secondary" : "bg-success"; ?>
                        position-absolute bottom-0 end-0 rounded-circle border border-white border-2"
                        style="width: 25px; height: 25px; right: 8px; bottom: 8px;"></span>
                </div>
                <h3 class="mb-1 text-white"><?php echo $userLogin->thaitrans($characterData['Name']); ?></h3>
                <p class="text-muted small">CharacterIdx:
                    <?php echo htmlspecialchars($characterData['CharacterIdx']); ?></p>
                <p class="mb-0">
                    <span class="badge badge-pill badge-primary py-2 px-3 mr-2">
                        <i class="fas fa-hat-wizard mr-1"></i> Class: <?php echo htmlspecialchars($className); ?>
                    </span>
                    <span class="badge badge-pill badge-info py-2 px-3">
                        <i class="fas fa-flag mr-1"></i> Nation: <?php echo htmlspecialchars($nationName); ?>
                    </span>
                </p>
                <div class="mt-3">
                    <?php if ($characterData['Login'] == "0"): ?>
                    <span class="badge badge-secondary badge-pill py-2 px-3"><i class="fas fa-globe mr-1"></i>
                        Offline</span>
                    <?php else: ?>
                    <span class="badge badge-success badge-pill py-2 px-3"><i class="fas fa-globe mr-1"></i>
                        Online</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-footer bg-light border-top text-center">
                <?php if ($userNum): // Link back to the account if UserNum is available ?>
                <a href="?url=manager_account/manage-account-edit&id=<?php echo htmlspecialchars($userNum); ?>"
                    class="btn btn-sm btn-outline-secondary waves-effect waves-themed mr-2">
                    <i class="fas fa-user mr-1"></i> ดูไอดีบัญชี
                </a>
                <?php endif; ?>

            </div>
        </div>
        
<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
        <div class="card mb-g">
            <div class="row row-grid no-gutters">
                <div class="col-12">
                    <div class="p-3">
                        <h2 class="mb-0 fs-xl">
                            Character Edit
                        </h2>
                    </div>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight"
                        data-toggle="modal"
                        data-target=".setdatachar-modal<?php echo $characterData['CharacterIdx']; ?>">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_community.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">แก้ไขตัวละคร</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight"
                        data-toggle="modal" data-target=".settitle-modal<?php echo $characterData['CharacterIdx']; ?>">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_pvp.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">เพิ่มยศ</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight"
                        data-toggle="modal"
                        data-target=".setachievement-modal<?php echo $characterData['CharacterIdx']; ?>">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_quest.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">ลบยศ</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight"
                        data-toggle="modal"
                        data-target=".resetinvents-modal<?php echo $characterData['CharacterIdx']; ?>">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_share.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">รีเช็ตช่องเก็บของ</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="?url=manager_charecter/manage-charecter-skillgm&charid=<?php echo $characterData['CharacterIdx']; ?>"
                        class="text-center p-3 d-flex flex-column hover-highlight">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_dungeon.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">เพิ่มสกิล GM</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="?url=manager_charecter/manage-charecter-remove&charid=<?php echo $characterData['CharacterIdx']; ?>" class="text-center p-3 d-flex flex-column hover-highlight">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_hunt.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">ลบตัวละคร</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_item.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">(รออัพเดท DEV)</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_normal.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">(รออัพเดท DEV)</span>
                    </a>
                </div>
                <div class="col-4">
                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                        <span class="profile-image rounded-circle d-block m-auto"
                            style="background-image:url('assets/images/icons/achievement/icn_achievement_produce.png'); background-size: cover;"></span>
                        <span class="d-block text-truncate text-muted fs-xs mt-1">(รออัพเดท DEV)</span>
                    </a>
                </div>
            </div>
        </div>
<?php } ?>

<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
    <?php include "view-equipment.php"; ?>                   
<?php } ?>



    </div>
    <div class="col-lg-8">
        <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
            <div class="card-header bg-primary text-white py-3">
                <h5 class="mb-0"><i class="fas fa-chart-bar mr-2"></i> สถิติหลัก</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-info mr-3 p-2 width-120"><i class="fas fa-star mr-1"></i>
                                Level</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo htmlspecialchars($characterData['LEV'] ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-success mr-3 p-2 width-120"><i class="fas fa-flask mr-1"></i>
                                Exp</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['EXP'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-warning mr-3 p-2 width-120"><i class="fas fa-coins mr-1"></i>
                                Alz</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['Alz'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-danger mr-3 p-2 width-120"><i class="fas fa-heart mr-1"></i>
                                HP</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['HP'] ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-primary mr-3 p-2 width-120"><i class="fas fa-bolt mr-1"></i>
                                SP</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['SP'] ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-secondary mr-3 p-2 width-120"><i
                                    class="fas fa-hourglass-half mr-1"></i> PlayTime</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo round(($characterData['PlayTime'] ?? 0) / 60); ?>
                                ชม.</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-dark mr-3 p-2 width-120"><i class="fas fa-fist-raised mr-1"></i> PK
                                Count</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['PKPenalty'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-danger text-dark mr-3 p-2 width-120"><i
                                    class="fas fa-medal mr-1"></i> Honor</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['Reputation'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-purple mr-3 p-2 width-120"
                                style="background-color: #6f42c1; color: #fff;"><i class="fas fa-brain mr-1"></i>
                                Ability Pt.</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($soulAbilityData['AbilityPoint'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-brown mr-3 p-2 width-120"
                                style="background-color: #a0522d; color: #fff;"><i class="fas fa-shield-alt mr-1"></i>
                                War Exp</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($warExpData['WarExp'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-teal mr-3 p-2 width-120"
                                style="background-color: #20c997; color: #fff;"><i class="fas fa-trophy mr-1"></i>
                                Score</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo number_format($characterData['Score'] ?? 0); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded bg-light">
                            <span class="badge badge-cyan mr-3 p-2 width-120"
                                style="background-color: #17a2b8; color: #fff;"><i class="fas fa-calendar-alt mr-1"></i>
                                Creation Date</span>
                            <span
                                class="font-weight-bold text-dark fs-lg"><?php echo ($characterData['CreateDate'] instanceof DateTime) ? $characterData['CreateDate']->format('d/m/Y H:i') : 'N/A'; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
        <?php include "view-inventory.php"; ?>                   
    <?php } ?>

        <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
            <div class="card-header bg-success text-white py-3">
                <h5 class="mb-0"><i class="fas fa-book-reader mr-2"></i> ทักษะ (Skills)</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">ข้อมูล
           
                           <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
        <?php include "view-warehouse.php"; ?>                   
    <?php } ?>
            </div>
        </div>

    </div>

    <?php else: ?>
    <div class="col-xl-12">
        <div class="alert alert-info text-center mt-4" role="alert">
            <h4 class="alert-heading">ตัวละครไม่พบข้อมูล</h4>
            <p>ไม่พบตัวละครด้วย CharacterIdx ที่ระบุ โปรดตรวจสอบอีกครั้ง</p>
            <hr>
            <a href="?url=manager_account/manage-account" class="btn btn-primary"><i class="fas fa-arrow-left mr-2"></i>
                กลับหน้ารายชื่อบัญชี</a>
        </div>
    </div>
    <?php endif; ?>
</div>


<!-- Modal Default Transparent -->
<div class="modal fade settitle-modal<?php echo $characterData['CharacterIdx']; ?>" tabindex="-1" role="dialog"
    aria-hidden="true">
    <div class="modal-dialog modal-transparent" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title text-white"> เพิ่ม Title
                    <small class="m-0 text-white opacity-70">
                        ถ้าตัวละครเข้าเกมส์ไม่ได้ให้ทำการรียศใหม่
                    </small>
                </h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" name="j_add_title" class="form-horizontal">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
                        <div class="">
                            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                                <?php echo $userLogin->thaitrans($characterData['Name']); ?>#<?php echo $characterData['CharacterIdx']; ?>
                            </h3>
                        </div>
                        <i class="fal fa-user position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n1"
                            style="font-size:6rem"></i>
                    </div>
                    <div class="form-group">
                        <label for="Title-input" class="form-label">Code
                            Title</label>
                        <input id="Title-input" name="title" class="form-control"></input>
                    </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <input type="hidden" name="charsidx" value="<?php echo $characterData["CharacterIdx"]; ?>">
                <button type="submit" class="btn btn-primary">Save
                    changes</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Default Transparent -->
<div class="modal fade setachievement-modal<?php echo $characterData['CharacterIdx']; ?>" tabindex="-1" role="dialog"
    aria-hidden="true">
    <div class="modal-dialog modal-transparent" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title text-white"> ลบ Title
                    <small class="m-0 text-white opacity-70">
                        ถ้าตัวละครเข้าเกมส์ไม่ได้ให้ทำการรียศใหม่
                    </small>
                </h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" name="j_del_title" class="form-horizontal">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
                        <div class="">
                            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                                <?php echo $userLogin->thaitrans($characterData['Name']); ?>#<?php echo $characterData['CharacterIdx']; ?>
                            </h3>
                        </div>
                        <i class="fal fa-user position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n1"
                            style="font-size:6rem"></i>
                    </div>
                    <div class="form-group">
                        <label for="Title-input" class="form-label">Code
                            Title</label>
                        <input id="Title-input" name="title" class="form-control"></input>
                    </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <input type="hidden" name="charsidx" value="<?php echo $characterData["CharacterIdx"]; ?>">
                <button type="submit" class="btn btn-primary">Save
                    changes</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Default resetinventory -->
<div class="modal fade resetinvents-modal<?php echo $characterData['CharacterIdx']; ?>" tabindex="-1" role="dialog"
    aria-hidden="true">
    <div class="modal-dialog modal-transparent" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title text-white">
                    Reset ช่องเก็บของ
                    <small class="m-0 text-white opacity-70">
                        ถ้าทำการรีเช็ตช่องเก็บของแล้วของในช่องเก็บของจะหายหมดแล้วไม่สามารถกู้คืนข้อมูลเดิมได้
                    </small>
                </h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" class="form-horizontal">
                    <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
                        <div class="">
                            <h3 class="display-4 d-block l-h-n m-0 fw-500">
                                <?php echo $userLogin->thaitrans($characterData['Name']); ?>#<?php echo $characterData['CharacterIdx']; ?>
                            </h3>
                        </div>
                        <i class="fal fa-user position-absolute pos-right pos-bottom opacity-15 mb-n1 mr-n1"
                            style="font-size:6rem"></i>
                    </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <input type="hidden" name="charsidx" value="<?php echo $characterData["CharacterIdx"]; ?>">
                <button type="submit" name="btn_reset_inventory" class="btn btn-primary">Reset inventory</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Right -->
<div class="modal fade setdatachar-modal<?php echo $characterData['CharacterIdx']; ?>" tabindex="-1" role="dialog"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-right">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h4">ระบบแก้ไขข้อมูลตัวละคร</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <form method="post" class="form-horizontal">
                <div class="modal-body">
                    <div class="card mb-g">
                        <div class="card-body p-3">
                            <h5 class="text-success">
                                แก้ไขชื่อตัวละครผู้เล่น
                                <small class="mt-0 mb-3 text-muted">
                                    ภาษาอังกฤษหรือตัวเลขเท่านั้น
                                </small>
                                <span
                                    class="badge badge-success fw-n position-absolute pos-top pos-right mt-3 mr-3">D</span>
                            </h5>
                            <div class="row fs-b fw-300 text-secondar">
                                <div class="col text-left">
                                    <div class="form-row">
                                        <div class="col-md-12 mb-4">
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span
                                                            class="btn btn-danger waves-effect waves-themed">ชื่อ</span>
                                                    </div>
                                                    <input type="text" name="input_Names"
                                                        value="<?php echo htmlspecialchars($userLogin->thaitrans($characterData['Name']), ENT_QUOTES, 'UTF-8'); ?>"
                                                        class="form-control" title="Please enter a Names."
                                                        placeholder="eg.: 0" required />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col text-right">
                                    <input type="hidden" name="charsidx"
                                        value="<?php echo $characterData["CharacterIdx"]; ?>">
                                    <button type="button" class="btn btn-secondary btn-sm"
                                        data-dismiss="modal">ปิด</button>
                                    <button type="submit" name="btn_savechange_chardataname"
                                        class="btn btn-default btn-sm">ยืนยันการเปลียนแปลง</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mb-g">
                        <div class="card-body p-3">
                            <h5 class="text-danger">
                                ตัวละครชื่อ
                                <span class="badge badge-danger fw-n position-absolute pos-top pos-right mt-3 mr-3">
                                    <?php echo $userLogin->thaitrans($characterData['Name']); ?>#<?php echo $characterData['CharacterIdx']; ?></span>
                                <small class="mt-0 mb-3 text-muted">
                                    ตัวละครต้องออกเกมส์
                                    หรือรออยู่หน้าเลือกตัวละคร
                                </small>
                            </h5>
                            <div class="form-row">
                                <div class="col-md-8 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="btn btn-danger waves-effect waves-themed">ชื่อ</span>
                                            </div>
                                            <input type="text" name="input_Name"
                                                value="<?php echo $userLogin->thaitrans($characterData["Name"]); ?>"
                                                class="form-control" title="Please enter a Name." placeholder="eg.: 0"
                                                required disabled />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">Lev</span>
                                            </div>
                                            <input type="text" name="input_lev"
                                                value="<?php echo $characterData["LEV"]; ?>" class="form-control"
                                                title="Please enter a LEVEL." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">STR</span>
                                            </div>
                                            <input type="text" name="input_str"
                                                value="<?php echo $characterData["STR"]; ?>" class="form-control"
                                                title="Please enter a STR." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">INT</span>
                                            </div>
                                            <input type="text" name="input_int"
                                                value="<?php echo $characterData["INT"]; ?>" class="form-control"
                                                title="Please enter a INT." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">DEX</span>
                                            </div>
                                            <input type="text" name="input_dex"
                                                value="<?php echo $characterData["DEX"]; ?>" class="form-control"
                                                title="Please enter a DEX." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">PNT</span>
                                            </div>
                                            <input type="text" name="input_pnt"
                                                value="<?php echo $characterData["PNT"]; ?>" class="form-control"
                                                title="Please enter a PNT." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">ALZ</span>
                                            </div>
                                            <input type="text" name="input_Alz"
                                                value="<?php echo $characterData["Alz"]; ?>" class="form-control"
                                                title="Please enter a Alz." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="btn btn-danger waves-effect waves-themed">แมพ</span>
                                            </div>
                                            <input type="text" name="input_WorldIdx"
                                                value="<?php echo $characterData["WorldIdx"]; ?>" class="form-control"
                                                title="Please enter a WorldIdx." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">SwPNT</span>
                                            </div>
                                            <input type="text" name="input_SwdPNT"
                                                value="<?php echo $characterData["SwdPNT"]; ?>" class="form-control"
                                                title="Please enter a SwdPNT." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">MgPNT</span>
                                            </div>
                                            <input type="text" name="input_MagPNT"
                                                value="<?php echo $characterData["MagPNT"]; ?>" class="form-control"
                                                title="Please enter a MagPNT." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">WarpB</span>
                                            </div>
                                            <input type="text" name="input_WarpBField"
                                                value="<?php echo $characterData["WarpBField"]; ?>" class="form-control"
                                                title="Please enter a WarpBField." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">MapsB</span>
                                            </div>
                                            <input type="text" name="input_MapsBField"
                                                value="<?php echo $characterData["MapsBField"]; ?>" class="form-control"
                                                title="Please enter a MapsBField." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="form-row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="btn btn-danger waves-effect waves-themed">Reputation</span>
                                            </div>
                                            <input type="text" name="input_Reputation"
                                                value="<?php echo $characterData["Reputation"]; ?>" class="form-control"
                                                title="Please enter a Reputation." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">Nation</span>
                                            </div>
                                            <select name="input_Nation" class="form-control"
                                                title="Please enter a Nation." placeholder="eg.: 0" required>
                                                <option value="0"
                                                    <?php if($characterData["Nation"] == '0'){ echo ' selected="selected"';} ?>>
                                                    ไม่มีประเทศ</option>
                                                <option value="1"
                                                    <?php if($characterData["Nation"] == '1'){ echo ' selected="selected"';} ?>>
                                                    Capella</option>
                                                <option value="2"
                                                    <?php if($characterData["Nation"] == '2'){ echo ' selected="selected"';} ?>>
                                                    Procyon</option>
                                                <option value="3"
                                                    <?php if($characterData["Nation"] == '3'){ echo ' selected="selected"';} ?>>
                                                    GM</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="btn btn-danger waves-effect waves-themed">AP</span>
                                            </div>
                                            <input type="text" name="input_AbilityPoint"
                                                value="<?php echo $soulAbilityData["AbilityPoint"]; ?>"
                                                class="form-control" title="Please enter a AbilityPoint."
                                                placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="btn btn-danger waves-effect waves-themed">Wexp</span>
                                            </div>
                                            <input type="text" name="input_wexp"
                                                value="<?php echo $warExpData["WarExp"]; ?>" class="form-control"
                                                title="Please enter a wexp." placeholder="eg.: 0" required />
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <input type="hidden" name="charsidx" value="<?php echo $characterData["CharacterIdx"]; ?>">
                    <button type="submit" name="btn_savechange_chardata" class="btn btn-primary">Save changes</button>
                </div>

        </div>
    </div>
    </form>
</div>



