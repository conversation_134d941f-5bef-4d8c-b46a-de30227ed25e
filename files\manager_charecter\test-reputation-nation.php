<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-medal"></i> ทดสอบสถิติ Honor Class และ Nation
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">เพิ่มสถิติ Honor Class และ Nation ในระบบ</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>อัพเดทแล้ว:</strong> สถิติ Honor Class (ตาม Cabal Wiki) และ Nation ได้รับการเพิ่มเข้าในระบบแล้ว
                </div>
                
                <h5>📊 สถิติที่เพิ่มใหม่</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-medal"></i> Honor Class</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>ระดับเกียรติยศ (ตาม <a href="https://cabal.fandom.com/wiki/Honor#Honor_Class" target="_blank">Cabal Wiki</a>):</strong></p>
                                <div class="row">
                                    <div class="col-6">
                                        <ul class="list-unstyled mb-0 small">
                                            <li><span class="badge badge-secondary">0</span> No Class</li>
                                            <li><span class="badge badge-success">1</span> Class 1</li>
                                            <li><span class="badge badge-success">2</span> Class 2</li>
                                            <li><span class="badge badge-success">3</span> Class 3</li>
                                            <li><span class="badge badge-success">4</span> Class 4</li>
                                            <li><span class="badge badge-success">5</span> Class 5</li>
                                            <li><span class="badge badge-info">6</span> Class 6</li>
                                            <li><span class="badge badge-info">7</span> Class 7</li>
                                            <li><span class="badge badge-info">8</span> Class 8</li>
                                            <li><span class="badge badge-info">9</span> Class 9</li>
                                            <li><span class="badge badge-info">10</span> Class 10</li>
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <ul class="list-unstyled mb-0 small">
                                            <li><span class="badge badge-warning">11</span> Class 11</li>
                                            <li><span class="badge badge-warning">12</span> Class 12</li>
                                            <li><span class="badge badge-warning">13</span> Class 13</li>
                                            <li><span class="badge badge-warning">14</span> Class 14</li>
                                            <li><span class="badge badge-warning">15</span> Class 15</li>
                                            <li><span class="badge badge-danger">16</span> Class 16</li>
                                            <li><span class="badge badge-danger">17</span> Class 17</li>
                                            <li><span class="badge badge-danger">18</span> Class 18</li>
                                            <li><span class="badge badge-danger">19</span> Class 19</li>
                                            <li><span class="badge badge-dark">20</span> Class 20</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fal fa-flag"></i> Nation</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>ฝ่ายที่สังกัด:</strong></p>
                                <ul class="list-unstyled mb-0">
                                    <li><span class="badge badge-secondary">0</span> Neutral</li>
                                    <li><span class="badge badge-danger">1</span> Capella</li>
                                    <li><span class="badge badge-primary">2</span> Procyon</li>
                                </ul>
                                
                                <p class="mt-3"><strong>การวิเคราะห์:</strong></p>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fal fa-balance-scale text-success"></i> สมดุลของ Nation</li>
                                    <li><i class="fal fa-chart-pie text-info"></i> สัดส่วนแต่ละฝ่าย</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔍 ข้อมูลจริงจาก Database</h5>
                <?php
                // ดึงข้อมูลจริงจาก database
                try {
                    echo "<div class='row'>";
                    
                    // Honor Class distribution - แก้ไข NULL และค่าผิดปกติ
                    $sql = "SELECT
                                CASE
                                    WHEN Reputation IS NULL THEN 'No Class'
                                    WHEN Reputation = 0 THEN 'No Class'
                                    WHEN Reputation >= 1 AND Reputation <= 20 THEN 'Class ' + CAST(Reputation AS VARCHAR)
                                    WHEN Reputation < 0 THEN 'No Class'
                                    WHEN Reputation > 20 THEN 'Class 20+'
                                    ELSE 'No Class'
                                END as honor_class,
                                ISNULL(Reputation, 0) as honor_value,
                                COUNT(*) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table
                            GROUP BY Reputation
                            ORDER BY Reputation";
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='col-md-6'>";
                    echo "<h6>Honor Class Distribution:</h6>";
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead><tr><th>Honor Class</th><th>จำนวน</th><th>%</th></tr></thead>";
                    echo "<tbody>";

                    $totalChars = 0;
                    $honorData = [];
                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $honorData[] = $row;
                            $totalChars += $row['count'];
                        }
                    }

                    foreach ($honorData as $row) {
                        $percentage = $totalChars > 0 ? ($row['count'] / $totalChars) * 100 : 0;

                        // กำหนดสีตาม Honor Class
                        $colorClass = '';
                        $honorValue = $row['honor_value'];
                        if ($honorValue == 0) $colorClass = 'text-secondary';
                        elseif ($honorValue >= 1 && $honorValue <= 5) $colorClass = 'text-success';
                        elseif ($honorValue >= 6 && $honorValue <= 10) $colorClass = 'text-info';
                        elseif ($honorValue >= 11 && $honorValue <= 15) $colorClass = 'text-warning';
                        elseif ($honorValue >= 16 && $honorValue <= 19) $colorClass = 'text-danger';
                        elseif ($honorValue == 20) $colorClass = 'text-dark';

                        echo "<tr>";
                        echo "<td><strong class='{$colorClass}'>" . $row['honor_class'] . "</strong></td>";
                        echo "<td>" . number_format($row['count']) . "</td>";
                        echo "<td>" . number_format($percentage, 1) . "%</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table></div></div>";
                    
                    // Nation distribution
                    $sql = "SELECT 
                                CASE 
                                    WHEN Nation = 0 THEN 'Neutral'
                                    WHEN Nation = 1 THEN 'Capella'
                                    WHEN Nation = 2 THEN 'Procyon'
                                    ELSE 'Unknown'
                                END as nation_name,
                                COUNT(*) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            GROUP BY Nation
                            ORDER BY COUNT(*) DESC";
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='col-md-6'>";
                    echo "<h6>Nation Distribution:</h6>";
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead><tr><th>Nation</th><th>จำนวน</th><th>%</th></tr></thead>";
                    echo "<tbody>";
                    
                    $nationData = [];
                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $nationData[] = $row;
                        }
                    }
                    
                    foreach ($nationData as $row) {
                        $percentage = $totalChars > 0 ? ($row['count'] / $totalChars) * 100 : 0;
                        $colorClass = '';
                        switch($row['nation_name']) {
                            case 'Capella': $colorClass = 'text-danger'; break;
                            case 'Procyon': $colorClass = 'text-primary'; break;
                            default: $colorClass = 'text-secondary';
                        }
                        
                        echo "<tr>";
                        echo "<td><strong class='{$colorClass}'>" . $row['nation_name'] . "</strong></td>";
                        echo "<td>" . number_format($row['count']) . "</td>";
                        echo "<td>" . number_format($percentage, 1) . "%</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table></div></div>";
                    echo "</div>";
                    
                    // Nation Balance Analysis
                    $capellaCount = 0;
                    $procyonCount = 0;
                    foreach ($nationData as $nation) {
                        if ($nation['nation_name'] == 'Capella') $capellaCount = $nation['count'];
                        if ($nation['nation_name'] == 'Procyon') $procyonCount = $nation['count'];
                    }
                    
                    if ($capellaCount > 0 && $procyonCount > 0) {
                        $ratio = $capellaCount / $procyonCount;
                        echo "<div class='alert alert-info mt-3'>";
                        echo "<h6><i class='fal fa-balance-scale'></i> การวิเคราะห์สมดุล Nation:</h6>";
                        echo "<p><strong>Capella:</strong> " . number_format($capellaCount) . " คน</p>";
                        echo "<p><strong>Procyon:</strong> " . number_format($procyonCount) . " คน</p>";
                        echo "<p><strong>อัตราส่วน:</strong> ";
                        
                        if ($ratio >= 0.8 && $ratio <= 1.2) {
                            echo "<span class='badge badge-success'>สมดุลดี</span> (อัตราส่วน " . number_format($ratio, 2) . ":1)";
                        } elseif ($ratio > 1.2) {
                            echo "<span class='badge badge-warning'>Capella เหนือกว่า</span> (อัตราส่วน " . number_format($ratio, 2) . ":1)";
                        } else {
                            echo "<span class='badge badge-warning'>Procyon เหนือกว่า</span> (อัตราส่วน " . number_format($ratio, 2) . ":1)";
                        }
                        echo "</p></div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบการแสดงผล</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="testTopPlayers()">
                        <i class="fal fa-trophy"></i> ทดสอบ Top Players
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรชหน้านี้
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการเพิ่มฟีเจอร์</h5>
                <div class="accordion" id="featuresAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. เพิ่มสถิติ Reputation Class
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>แสดงการกระจายตาม Reputation Class (0-9)</li>
                                    <li>แสดงสัดส่วนเปอร์เซ็นต์</li>
                                    <li>ใช้สีและไอคอนที่เหมาะสม</li>
                                    <li>แสดงชื่อเต็มของแต่ละระดับ</li>
                                </ul>
                                
                                <p><strong>SQL Query:</strong></p>
                                <pre><code>SELECT 
    CASE 
        WHEN ReputationClass = 0 THEN 'Neutral'
        WHEN ReputationClass = 1 THEN 'Apprentice'
        ...
    END as rep_class,
    COUNT(*) as count
FROM cabal_character_table 
GROUP BY ReputationClass</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. เพิ่มสถิติ Nation
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>ฟีเจอร์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>แสดงการกระจายตาม Nation (0-2)</li>
                                    <li>วิเคราะห์สมดุลระหว่าง Capella และ Procyon</li>
                                    <li>ใช้สีตามฝ่าย (แดง/น้ำเงิน)</li>
                                    <li>แสดงอัตราส่วนและคำแนะนำ</li>
                                </ul>
                                
                                <p><strong>การวิเคราะห์สมดุล:</strong></p>
                                <ul>
                                    <li>อัตราส่วน 0.8-1.2 = สมดุลดี</li>
                                    <li>อัตราส่วน > 1.2 = Capella เหนือกว่า</li>
                                    <li>อัตราส่วน < 0.8 = Procyon เหนือกว่า</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. อัพเดท Top Players
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <p><strong>คอลัมน์ที่เพิ่ม:</strong></p>
                                <ul>
                                    <li>Reputation Class - แสดงระดับชื่อเสียง</li>
                                    <li>Nation - แสดงฝ่ายที่สังกัด</li>
                                </ul>
                                
                                <p><strong>การแสดงผล:</strong></p>
                                <ul>
                                    <li>ใช้ Badge สีตามประเภท</li>
                                    <li>แสดงชื่อเต็มแทนรหัส</li>
                                    <li>เรียงตาม Level และ Alz</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการเพิ่มฟีเจอร์:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ เพิ่มสถิติ Reputation Class (10 ระดับ)</li>
                                <li>✅ เพิ่มสถิติ Nation (3 ฝ่าย)</li>
                                <li>✅ วิเคราะห์สมดุล Nation</li>
                                <li>✅ อัพเดท Top Players table</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ใช้สีและไอคอนที่เหมาะสม</li>
                                <li>✅ แสดงสัดส่วนเปอร์เซ็นต์</li>
                                <li>✅ การวิเคราะห์แบบ real-time</li>
                                <li>✅ ระบบสถิติครบถ้วนสำหรับ admin</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function testTopPlayers() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> เปิดหน้าสถิติเพื่อดู Top Players ที่มี Reputation Class และ Nation</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
