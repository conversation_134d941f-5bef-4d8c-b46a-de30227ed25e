﻿<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> ตรวจการส่งเมลล์

    </h1>
</div>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>Mail Send <span class="fw-300"><i>ตารางการส่งเมลล์</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                <table id="datatables-default" class="table table-sm table-bordered w-100">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>LOGID</th>
                            <th>UserNum</th>
                            <th>Action</th>
                            <th>Message</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php

                            // generic function to get page
                            function getPage($stmt, $pageNum, $rowsPerPage) {
                                $offset = ($pageNum - 1) * $rowsPerPage;
                                $rows = array();
                                $i = 0;
                                while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                    array_push($rows, $row);
                                    $i++;
                                }
                                return $rows;
                            }

                            // Set the number of rows to be returned on a page.
                            $rowsPerPage = 1000;

                            // Define and execute the query.  
                            // Note that the query is executed with a "scrollable" cursor.
                            $sql = "SELECT * FROM WEB_Log ORDER BY id DESC";

                            $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                            if (!$stmt)
                                die(print_r(sqlsrv_errors(), true));

                            // Get the total number of rows returned by the query.
                            $rowsReturned = sqlsrv_num_rows($stmt);
                            if ($rowsReturned === false)
                                die(print_r(sqlsrv_errors(), true));
                            elseif ($rowsReturned == 0) {
                                echo W_NOTHING_RETURNED;
                                //exit();
                            } else {
                                /* Calculate number of pages. */
                                $numOfPages = ceil($rowsReturned / $rowsPerPage);
                            }

                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
                                <td><?php echo $row[2]; ?></td>
                                <td><?php echo $row[3]; ?></td>
                                <td><?php echo $row[4]; ?></td>
								<td><?php echo $row[5]; ?></td>
                                 </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    </div>
</div>
