/*
Name: 			Tables / Ajax - Examples
Written by: 	Okler Themes - (http://www.okler.net)
Theme Version: 	1.7.0
*/

(function($) {

    'use strict';

    var datatableItem = function() {

        var $table = $('#datatable-item');
        $table.dataTable({
            bProcessing: true,
            sAjaxSource: $table.data('url')
        });

    };
    var datatablequest = function() {

        var $table = $('#datatable-quest');
        $table.dataTable({
            bProcessing: true,
            sAjaxSource: $table.data('url')
        });

    };
    var datatableInit = function() {

        var $table = $('#datatable-ajax');
        $table.dataTable({
            bProcessing: true,
            sAjaxSource: $table.data('url')
        });

    };
    var datatabledun_world = function() {

        var $table = $('#datatable-dun_world');
        $table.dataTable({
            bProcessing: true,
            sAjaxSource: $table.data('url')
        });

    };
    var datatablemonster = function() {

        var $table = $('#datatable-monster');
        $table.dataTable({
            bProcessing: true,
            sAjaxSource: $table.data('url')
        });

    };
    $(function() {
        datatableItem();
        datatablequest();
        datatableInit();
        datatabledun_world();
        datatablemonster();

    });

}).apply(this, [jQuery]);