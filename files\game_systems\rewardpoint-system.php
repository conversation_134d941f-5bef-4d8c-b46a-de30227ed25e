<?php
header('Content-Type: text/html; charset=UTF-8');
$user->restrictionUser(true, $conn); // คงการเรียก restrictionUser

// ข้อความแจ้งเตือน
$alert = isset($_SESSION['alert']) ? $_SESSION['alert'] : '';
unset($_SESSION['alert']);

// รับพารามิเตอร์จาก URL
$package_id = isset($_GET['package_id']) ? intval($_GET['package_id']) : 0;
$item_code = isset($_GET['item_code']) ? intval($_GET['item_code']) : 0;
$edit_data = null;

if ($package_id > 0 && $item_code > 0) {
    $query = "SELECT pi.*, p.package_name, ri.item_name 
              FROM WEB_Package_items pi 
              JOIN WEB_Reward_packages p ON pi.package_id = p.package_id 
              JOIN WEB_Reward_items ri ON pi.item_code = ri.item_code 
              WHERE pi.package_id = ? AND pi.item_code = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$package_id, $item_code]);
    if ($stmt === false) {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการเตรียม query: ' . print_r(sqlsrv_errors(), true) . '</div>';
    } elseif (sqlsrv_execute($stmt)) {
        $edit_data = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
        if (!$edit_data) {
            $alert = '<div class="alert alert-danger">ไม่พบข้อมูลสำหรับ package_id และ item_code ที่ระบุ</div>';
        }
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการดึงข้อมูล: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);
}

// จัดการแพ็กเกจ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_package'])) {
    $package_name = $_POST['package_name'];
    $required_points = intval($_POST['required_points']);
    if ($required_points <= 0) {
        $alert = '<div class="alert alert-danger">คะแนนต้องมากกว่า 0</div>';
    } else {
        $query = "INSERT INTO WEB_Reward_packages (package_name, required_points) VALUES (?, ?)";
        $stmt = sqlsrv_prepare($conn, $query, [$package_name, $required_points]);
        if ($stmt && sqlsrv_execute($stmt)) {
            $alert = '<div class="alert alert-success">เพิ่มแพ็กเกจสำเร็จ</div>';
        } else {
            $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการเพิ่มแพ็กเกจ: ' . print_r(sqlsrv_errors(), true) . '</div>';
        }
        sqlsrv_free_stmt($stmt);
    }
}

// ลบแพ็กเกจ
if (isset($_GET['delete_package'])) {
    $delete_id = intval($_GET['delete_package']);
    $query = "DELETE FROM WEB_Reward_packages WHERE package_id = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$delete_id]);
    if ($stmt && sqlsrv_execute($stmt)) {
        $alert = '<div class="alert alert-success">ลบแพ็กเกจสำเร็จ</div>';
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการลบแพ็กเกจ: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);
}

// จัดการไอเทม
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_item'])) {
    $item_code = intval($_POST['item_code']);
    $item_name = $_POST['item_name'];
    $item_description = $_POST['item_description'];
    $query = "INSERT INTO WEB_Reward_items (item_code, item_name, item_description) VALUES (?, ?, ?)";
    $stmt = sqlsrv_prepare($conn, $query, [$item_code, $item_name, $item_description]);
    if ($stmt && sqlsrv_execute($stmt)) {
        $alert = '<div class="alert alert-success">เพิ่มไอเทมสำเร็จ</div>';
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการเพิ่มไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);
}

// ลบไอเทม
if (isset($_GET['delete_item'])) {
    $delete_id = intval($_GET['delete_item']);
    $query = "DELETE FROM WEB_Reward_items WHERE item_id = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$delete_id]);
    if ($stmt && sqlsrv_execute($stmt)) {
        $alert = '<div class="alert alert-success">ลบไอเทมสำเร็จ</div>';
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการลบไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);
}

// จัดการเพิ่มไอเทมในแพ็กเกจ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_package_item'])) {
    $package_id = intval($_POST['package_id']);
    $item_code = intval($_POST['item_code']);
    $item_option = intval($_POST['item_option']);
    $quantity = intval($_POST['quantity']);
    $duration = !empty($_POST['duration']) ? intval($_POST['duration']) : null;

    // ดึง item_name จาก WEB_Reward_items
    $query = "SELECT item_name FROM WEB_Reward_items WHERE item_code = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$item_code]);
    $item_name = '';
    if ($stmt && sqlsrv_execute($stmt)) {
        if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $item_name = $row['item_name'];
        }
    } else {
        $_SESSION['alert'] = '<div class="alert alert-danger">ข้อผิดพลาดในการดึง item_name: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);

    if ($quantity <= 0) {
        $_SESSION['alert'] = '<div class="alert alert-danger">จำนวนต้องมากกว่า 0</div>';
    } elseif (empty($item_name)) {
        $_SESSION['alert'] = '<div class="alert alert-danger">ไม่พบไอเทมสำหรับ item_code ที่ระบุ</div>';
    } else {
        $check_query = "SELECT COUNT(*) AS cnt FROM WEB_Package_items WHERE package_id = ? AND item_code = ?";
        $check_stmt = sqlsrv_prepare($conn, $check_query, [$package_id, $item_code]);
        if ($check_stmt && sqlsrv_execute($check_stmt) && $row = sqlsrv_fetch_array($check_stmt, SQLSRV_FETCH_ASSOC)) {
            if ($row['cnt'] > 0) {
                $_SESSION['alert'] = '<div class="alert alert-danger">ไอเทมนี้มีอยู่ในแพ็กเกจแล้ว</div>';
            } else {
                $insert_query = "INSERT INTO WEB_Package_items (package_id, item_code, item_name, item_option, quantity, duration) VALUES (?, ?, ?, ?, ?, ?)";
                $params = [$package_id, $item_code, $item_name, $item_option, $quantity, $duration];
                $insert_stmt = sqlsrv_prepare($conn, $insert_query, $params);
                if ($insert_stmt && sqlsrv_execute($insert_stmt)) {
                    $_SESSION['alert'] = '<div class="alert alert-success">เพิ่มไอเทมในแพ็กเกจสำเร็จ</div>';
                } else {
                    $_SESSION['alert'] = '<div class="alert alert-danger">ข้อผิดพลาดในการเพิ่มไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
                }
                sqlsrv_free_stmt($insert_stmt);
            }
        } else {
            $_SESSION['alert'] = '<div class="alert alert-danger">ข้อผิดพลาดในการตรวจสอบไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
        }
        sqlsrv_free_stmt($check_stmt);
    }
}

// อัปเดตไอเทมในแพ็กเกจ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_package_item'])) {
    $package_id = intval($_POST['package_id']);
    $item_code = intval($_POST['item_code']);
    $item_option = intval($_POST['item_option']);
    $quantity = intval($_POST['quantity']);
    $duration = !empty($_POST['duration']) ? intval($_POST['duration']) : null;

    // ดึง item_name จาก WEB_Reward_items
    $query = "SELECT item_name FROM WEB_Reward_items WHERE item_code = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$item_code]);
    $item_name = '';
    if ($stmt && sqlsrv_execute($stmt)) {
        if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $item_name = $row['item_name'];
        }
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการดึง item_name: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);

    if ($quantity <= 0) {
        $alert = '<div class="alert alert-danger">จำนวนต้องมากกว่า 0</div>';
    } elseif (empty($item_name)) {
        $alert = '<div class="alert alert-danger">ไม่พบไอเทมสำหรับ item_code ที่ระบุ</div>';
    } else {
        $update_query = "UPDATE WEB_Package_items SET item_name = ?, item_option = ?, quantity = ?, duration = ? WHERE package_id = ? AND item_code = ?";
        $params = [$item_name, $item_option, $quantity, $duration, $package_id, $item_code];
        $update_stmt = sqlsrv_prepare($conn, $update_query, $params);
        if ($update_stmt && sqlsrv_execute($update_stmt)) {
            $alert = '<div class="alert alert-success">อัปเดตไอเทมในแพ็กเกจสำเร็จ</div>';
        } else {
            $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการอัปเดตไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
        }
        sqlsrv_free_stmt($update_stmt);
    }
}

// ลบไอเทมในแพ็กเกจ
if (isset($_GET['delete_package_item_package_id']) && isset($_GET['delete_package_item_item_code'])) {
    $delete_package_id = intval($_GET['delete_package_item_package_id']);
    $delete_item_code = intval($_GET['delete_package_item_item_code']);
    $query = "DELETE FROM WEB_Package_items WHERE package_id = ? AND item_code = ?";
    $stmt = sqlsrv_prepare($conn, $query, [$delete_package_id, $delete_item_code]);
    if ($stmt && sqlsrv_execute($stmt)) {
        $alert = '<div class="alert alert-success">ลบไอเทมในแพ็กเกจสำเร็จ</div>';
    } else {
        $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการลบไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
    }
    sqlsrv_free_stmt($stmt);
}

// ดึงข้อมูลแพ็กเกจ
$packages_query = "SELECT * FROM WEB_Reward_packages ORDER BY package_id";
$packages_result = sqlsrv_query($conn, $packages_query);
$packages = [];
if ($packages_result) {
    while ($row = sqlsrv_fetch_array($packages_result, SQLSRV_FETCH_ASSOC)) {
        $packages[] = $row;
    }
    sqlsrv_free_stmt($packages_result);
} else {
    $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการดึงข้อมูลแพ็กเกจ: ' . print_r(sqlsrv_errors(), true) . '</div>';
}

// ดึงข้อมูลไอเทม
$items_query = "SELECT * FROM WEB_Reward_items ORDER BY item_code";
$items_result = sqlsrv_query($conn, $items_query);
$items = [];
if ($items_result) {
    while ($row = sqlsrv_fetch_array($items_result, SQLSRV_FETCH_ASSOC)) {
        $items[] = $row;
    }
    sqlsrv_free_stmt($items_result);
} else {
    $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการดึงข้อมูลไอเทม: ' . print_r(sqlsrv_errors(), true) . '</div>';
}

// ดึงข้อมูลไอเทมในแพ็กเกจ
$package_items_query = "SELECT pi.*, p.package_name, ri.item_name AS reward_item_name 
                       FROM WEB_Package_items pi 
                       JOIN WEB_Reward_packages p ON pi.package_id = p.package_id 
                       JOIN WEB_Reward_items ri ON pi.item_code = ri.item_code 
                       ORDER BY pi.package_id, pi.item_code";
$package_items_result = sqlsrv_query($conn, $package_items_query);
$package_items = [];
if ($package_items_result) {
    while ($row = sqlsrv_fetch_array($package_items_result, SQLSRV_FETCH_ASSOC)) {
        $package_items[] = $row;
    }
    sqlsrv_free_stmt($package_items_result);
} else {
    $alert = '<div class="alert alert-danger">ข้อผิดพลาดในการดึงข้อมูลไอเทมในแพ็กเกจ: ' . print_r(sqlsrv_errors(), true) . '</div>';
}

// อาร์เรย์สีสำหรับแต่ละ package_id
$package_colors = [];
$colors = ['#e6f3ff', '#e6ffe6', '#fffde6', '#ffe6e6', '#f3e6ff', '#e6f3f3', '#fff0e6', '#e6e6ff', '#f0ffe6', '#ffe6f0', '#e6fff0'];
foreach ($packages as $index => $pkg) {
    $package_colors[$pkg['package_id']] = $colors[$index % count($colors)];
}
?>

<style>
<?php foreach ($package_colors as $pkg_id=> $color): ?>.package-<?php echo $pkg_id; ?> {
    background-color: <?php echo $color;
    ?> !important;
}

<?php endforeach;

?>.legend-box {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    vertical-align: middle;
}
</style>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="fal fa-th-list text-primary"></i> จัดการ Reward Point System
        <small>
          ระบบจัดการการ โปรโมชั่น แต้มสะสม 
        </small>
    </h1>
</div>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    เพิ่มไอเทมในแพ็กเกจ <span class="fw-300"><i>&& รายการไอเทมในแพ็กเกจ</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <?php echo $alert; ?>
                    <!-- ฟอร์มแก้ไขไอเทมในแพ็กเกจเมื่อมี package_id และ item_code -->
                    <?php if ($edit_data): ?>
                    <div class="panel-tag">
                        <h4>แก้ไขไอเทมในแพ็กเกจ: <?php echo htmlspecialchars($edit_data['package_name']); ?> -
                            <?php echo htmlspecialchars($edit_data['item_name']); ?></h4>
                    </div>
                    <form method="POST" class="mb-4">
                        <input type="hidden" name="package_id"
                            value="<?php echo htmlspecialchars($edit_data['package_id']); ?>">
                        <input type="hidden" name="item_code"
                            value="<?php echo htmlspecialchars($edit_data['item_code']); ?>">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="item_option" class="form-label">ออฟชั่นไอเท็ม</label>
                                <input type="number" class="form-control" id="item_option" name="item_option"
                                    value="<?php echo htmlspecialchars($edit_data['item_option']); ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">จำนวน</label>
                                <input type="number" class="form-control" id="quantity" name="quantity"
                                    value="<?php echo htmlspecialchars($edit_data['quantity']); ?>" min="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="duration" class="form-label">ระยะเวลา (วัน)</label>
                                <input type="number" class="form-control" id="duration" name="duration"
                                    value="<?php echo htmlspecialchars($edit_data['duration']); ?>" min="0">
                            </div>
                        </div>
                        <button type="submit" name="update_package_item" class="btn btn-primary">บันทึกการแก้ไข</button>
                        <a href="?url=game_systems/rewardpoint-system" class="btn btn-secondary">ยกเลิก</a>
                    </form>
                    <?php endif; ?>
                    <!-- ฟอร์มเพิ่มไอเทมในแพ็กเกจ -->
                    <h4>เพิ่มไอเทมในแพ็กเกจ</h4>
                    <form method="POST" class="mb-4">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="package_id" class="form-label">แพ็กเกจ</label>
                                <select class="form-control" id="package_id" name="package_id" required>
                                    <option value="">เลือกแพ็กเกจ</option>
                                    <?php foreach ($packages as $package): ?>
                                    <option value="<?php echo $package['package_id']; ?>">
                                        <?php echo htmlspecialchars($package['package_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="item_code" class="form-label">ไอเทม</label>
                                <select class="form-control" id="item_code" name="item_code" onchange="updateItemName()"
                                    required>
                                    <option value="">เลือกไอเทม</option>
                                    <?php foreach ($items as $item): ?>
                                    <option value="<?php echo $item['item_code']; ?>"
                                        data-name="<?php echo htmlspecialchars($item['item_name']); ?>">
                                        <?php echo htmlspecialchars($item['item_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <input type="hidden" id="item_name" name="item_name">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="item_option" class="form-label">ออฟชั่นไอเท็ม</label>
                                <input type="number" class="form-control" id="item_option" name="item_option" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">จำนวน</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="1"
                                    required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="duration" class="form-label">ระยะเวลา (วัน)</label>
                                <input type="number" class="form-control" id="duration" name="duration" min="0">
                            </div>
                            <div class="col-md-4 mb-3 d-flex align-items-end">
                                <button type="submit" name="add_package_item"
                                    class="btn btn-primary">เพิ่มไอเทมในแพ็กเกจ</button>
                            </div>
                        </div>
                    </form>
                    <!-- Legend สีแพ็กเกจ -->
                    <h4>รายการไอเทมในแพ็กเกจ</h4>
                    <div class="mb-3">
                        <strong>สีแพ็กเกจ:</strong>
                        <?php foreach ($packages as $pkg): ?>
                        <span style="margin-right: 15px;">
                            <span class="legend-box package-<?php echo $pkg['package_id']; ?>"></span>
                            <?php echo htmlspecialchars($pkg['package_name']); ?>
                        </span>
                        <?php endforeach; ?>
                    </div>

                    <!-- ตารางไอเทมในแพ็กเกจ -->
                    <table id="packageItemsTable" class="table table-sm table-bordered w-100">
                        <thead>
                            <tr>
                                <th>แพ็กเกจ</th>
                                <th>รหัสไอเทม</th>
                                <th>ชื่อไอเทม</th>
                                <th>ตัวเลือก</th>
                                <th>จำนวน</th>
                                <th>ระยะเวลา</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($package_items as $pi): ?>
                            <tr class="package-<?php echo $pi['package_id']; ?>">
                                <td class="package-<?php echo $pi['package_id']; ?>">
                                    <?php echo htmlspecialchars($pi['package_name']); ?>
                                </td>
                                <td><?php echo htmlspecialchars($pi['item_code']); ?></td>
                                <td><?php echo htmlspecialchars($pi['reward_item_name']); ?></td>
                                <td><?php echo htmlspecialchars($pi['item_option']); ?></td>
                                <td><?php echo htmlspecialchars($pi['quantity']); ?></td>
                                <td><?php echo htmlspecialchars($pi['duration'] ?: '-'); ?></td>
                                <td>
                                    <a href="?url=game_systems/rewardpoint-system&package_id=<?php echo $pi['package_id']; ?>&item_code=<?php echo $pi['item_code']; ?>"
                                        class="btn btn-warning btn-sm">แก้ไข</a>
                                    <a href="?url=game_systems/rewardpoint-system&delete_package_item_package_id=<?php echo $pi['package_id']; ?>&delete_package_item_item_code=<?php echo $pi['item_code']; ?>"
                                        class="btn btn-danger btn-sm" onclick="return confirm('ยืนยันการลบ?')">ลบ</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div id="panel-1" class="panel">
                    <div class="panel-hdr">
                        <h2>
                            รายการแพ็กเกจ <span class="fw-300"><i>&& รายการแพ็กเกจ</i></span>
                        </h2>
                        <div class="panel-toolbar">
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                        </div>
                    </div>
                    <div class="panel-container show">
                        <div class="panel-content">
                            <!-- ฟอร์มเพิ่มแพ็กเกจ -->
                            <h4>เพิ่มแพ็กเกจใหม่</h4>
                            <form method="POST" class="mb-4">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="package_name" class="form-label">ชื่อแพ็กเกจ</label>
                                        <input type="text" class="form-control" id="package_name" name="package_name"
                                            required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="required_points" class="form-label">คะแนนที่ต้องการ</label>
                                        <input type="number" class="form-control" id="required_points"
                                            name="required_points" min="1" required>
                                    </div>
                                </div>
                                <button type="submit" name="add_package" class="btn btn-primary">เพิ่มแพ็กเกจ</button>
                            </form>

                            <!-- ตารางแพ็กเกจ -->
                            <h4>รายการแพ็กเกจ</h4>
                            <table id="datatables-default" class="table table-sm table-bordered w-100">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>ชื่อแพ็กเกจ</th>
                                        <th>คะแนนที่ต้องการ</th>
                                        <th>วันที่สร้าง</th>
                                        <th>การดำเนินการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($packages as $package): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($package['package_id']); ?></td>
                                        <td><?php echo htmlspecialchars($package['package_name']); ?></td>
                                        <td><?php echo htmlspecialchars($package['required_points']); ?></td>
                                        <td><?php echo htmlspecialchars($package['created_at'] ?? ''); ?></td>
                                        <td>
                                            <a href="?url=game_systems/rewardpoint-system&delete_package=<?php echo $package['package_id']; ?>"
                                                class="btn btn-danger btn-sm"
                                                onclick="return confirm('ยืนยันการลบ?')">ลบ</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--start รายการไอเทม -->
        <div class="row">
            <div class="col-xl-12">
                <div id="panel-1" class="panel">
                    <div class="panel-hdr">
                        <h2>
                            รายการไอเทม <span class="fw-300"><i>&& เพิ่มไอเทมใหม่</i></span>
                        </h2>
                        <div class="panel-toolbar">
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                            <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                                data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                        </div>
                    </div>
                    <div class="panel-container show">
                        <div class="panel-content">
                            <!-- ฟอร์มเพิ่มไอเทม -->
                            <h4>เพิ่มไอเทมใหม่</h4>
                            <form method="POST" class="mb-4">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="item_code" class="form-label">รหัสไอเทม</label>
                                        <input type="number" class="form-control" id="item_code" name="item_code"
                                            required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="item_name" class="form-label">ชื่อไอเทม</label>
                                        <input type="text" class="form-control" id="item_name" name="item_name"
                                            required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="item_description" class="form-label">คำอธิบายไอเทม</label>
                                        <textarea class="form-control" id="item_description"
                                            name="item_description"></textarea>
                                    </div>
                                </div>
                                <button type="submit" name="add_item" class="btn btn-primary">เพิ่มไอเทม</button>
                            </form>

                            <!-- ตารางไอเทม -->
                            <h4>รายการไอเทม</h4>
                            <table id="datatables-1" class="table table-sm table-bordered w-100">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>รหัสไอเทม</th>
                                        <th>ชื่อไอเทม</th>
                                        <th>คำอธิบาย</th>
                                        <th>วันที่สร้าง</th>
                                        <th>การดำเนินการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['item_id']); ?></td>
                                        <td><?php echo htmlspecialchars($item['item_code']); ?></td>
                                        <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['item_description']); ?></td>
                                        <td><?php echo htmlspecialchars($item['created_at'] ?? ''); ?></td>
                                        <td>
                                            <a href="?url=game_systems/rewardpoint-system&delete_item=<?php echo $item['item_id']; ?>"
                                                class="btn btn-danger btn-sm"
                                                onclick="return confirm('ยืนยันการลบ?')">ลบ</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end รายการไอเทม -->

        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script>
        function updateItemName() {
            const select = document.getElementById('item_code');
            const itemNameInput = document.getElementById('item_name');
            const selectedOption = select.options[select.selectedIndex];
            itemNameInput.value = selectedOption.getAttribute('data-name');
        }

        $(document).ready(function() {
            $('#packageItemsTable').DataTable();
            $('#datatables-1').DataTable();
            $('#datatables-2').DataTable();
        });
        </script>