import tkinter as tk
from tkinter import ttk, messagebox
import pyodbc
import random

class SearchableCombobox(ttk.Combobox):
    def __init__(self, master=None, callback=None, **kwargs):
        super().__init__(master, **kwargs)
        self._original_values = list(self['values'])
        self.bind('<KeyRelease>', self.on_keyrelease)
        self.bind('<Return>', self.on_enter)
        self.bind('<<ComboboxSelected>>', self.on_select)
        self.callback = callback

    def on_keyrelease(self, event):
        if event.keysym in ['Return', 'Up', 'Down', 'Escape']:
            return
        typed = self.get()
        filtered = [item for item in self._original_values if typed.lower() in item.lower()]
        self['values'] = filtered

    def on_enter(self, event):
        self.event_generate('<Down>')

    def on_select(self, event):
        nome = self.get()
        item_id = item_dict.get(nome)
        if self.callback:
            self.callback(item_id)

class ItemGeneratorApp:
    def __init__(self, config):
        self.config = config

    item_map_name = {
        "Helm": [
            {"1": "MP"},
            {"2": "DEF"},
            {"3": "DEF RATE"},
            {"4": "CRIT DAMAGE"},
            {"5": "CRIT RATE"},
            {"6": "2SLOT DROP"},
            {"7": "SKILL EXP"},
            {"8": "SWORD SKILL AMP"},
            {"9": "MAGIC SKILL AMP"},
            {"A": "ALL ATTACK UP"},
            {"B": "MAX HP STEAL"},
            {"C": "MAX MP STEAL"},
            {"D": "ALZ DROP AMMOUNT"},
            {"E": "1 SLOT ITEM DROP"},
            {"F": "ALL SKILL AMP"}
        ],
        "Suit": [
            {"1": "HP"},
            {"2": "DEF"},
            {"3": "DEF RATE"},
            {"4": "MP"},
            {"5": "HP AUTO HEAL"},
            {"6": "2 SLOTS ITEM DROP"},
            {"7": "SKILL EXP"},
            {"8": "SWORD SKILL AMP"},
            {"9": "MAGIC SKILL AMP"},
            {"A": "ALL ATTACK UP"},
            {"B": "MP AUTO HEAL"},
            {"C": "MAX CRIT RATE"},
            {"D": "ALZ DROP AMOUNT"},
            {"E": "FLEE RATE"},
            {"F": "ALL SKILL AMP"}
        ],
        "Gloves": [
            {"1": "DEF"},
            {"2": "ATTACK RATE"},
            {"3": "DEF RATE"},
            {"4": "ATTACK"},
            {"5": "HP AUTO HEAL"},
            {"6": "2 SLOTS ITEM DROP"},
            {"7": "SKILL EXP"},
            {"8": "SWORD SKILL AMP"},
            {"9": "MAGIC SKILL AMP"},
            {"A": "ALL ATTACK UP"},
            {"B": "MP AUTO HEAL"},
            {"C": "MAX CRIT RATE"},
            {"D": "HP STEAL"},
            {"E": "MP STEAL"},
            {"F": "ALL SKILL AMP"}
        ],
        "Boots": [
            {"1": "DEF"},
            {"2": "DEF RATE"},
            {"3": "HP"},
            {"4": "MP"},
            {"5": "HP AUTO HEAL"},
            {"6": "2 SLOTS ITEM DROP"},
            {"7": "SKILL EXP"},
            {"8": "SWORD SKILL AMP"},
            {"9": "MAGIC SKILL AMP"},
            {"A": "ALL ATTACK UP"},
            {"B": "MP AUTO HEAL"},
            {"C": "MAX HP STEAL"},
            {"D": "ALZ DROP AMMOUNT"},
            {"E": "FLEE RATE"},
            {"F": "ALL SKILL AMP"}
        ],
        "Weapon": [
            {"1": "ATTACK"},
            {"2": "MAGIC ATTACK"},
            {"3": "ATTACK RATE"},
            {"4": "CRIT DAMAGE"},
            {"5": "CRIT RATE"},
            {"6": "2 SLOTS ITEM DROP"},
            {"7": "SKILL EXP"},
            {"8": "SWORD SKILL AMP"},
            {"9": "MAGIC SKILL AMP"},
            {"A": "ALL ATTACK UP"},
            {"B": "MIN DAMAGE"},
            {"C": "ADD DAMAGE"},
            {"D": "ALZ DROP"},
            {"E": "1 SLOT DROP"},
            {"F": "ALL SKILL AMP"}
        ],
        "Bike": [
            {"1": "HP"},
            {"2": "ATTACK"},
            {"3": "MAGIC ATTACK"},
            {"4": "CRIT DAMAGE"},
            {"5": "CRIT RATE"},
            {"6": "MAX CRIT RATE"},
            {"7": "SWORD SKILL AMP"},
            {"8": "MAGIC SKILL AMP"},
            {"9": "RESIST CRIT RATE"},
            {"A": "RESIST CRIT DAMAGE"},
            {"B": "RESIST SKILL AMP"},
            {"C": "ALL ATTACK UP"},
            {"D": "ALL SKILL AMP"},
            {"E": "NON VALID"},
            {"F": "NON VALID"}
        ]
    }


    advance_setting = 0
    upgrade = 0
    extreme = 0
    divine = 0
    craft = "00"
    options1 = ""
    options2 = ""
    options3 = ""

    def on_upgrade_change(self, event):
        selected_value = self.combo_upgrade.get()
        self.upgrade = int(selected_value) * 8192

    def on_divine_change(self, event):
        selected_value = self.combo_divine.get()
        self.divine = int(selected_value) * ***********

    def on_extreme_change(self, event):
        selected_value = self.combo_extreme.get()
        self.extreme = int(selected_value) * **********

    def on_setting_change(self, setting):
        if setting == "Non Bound":
            self.advance_setting = 0
        elif setting == "Bound On Account (Extended)":
            self.advance_setting = 4096
        elif setting == "Bound On Character":
            self.advance_setting = 524288
        elif setting == "Bound On Character (On equipped)":
            self.advance_setting = 1572864

    def on_item_type_change(self, event):
        selected_value = self.combo_item_type.get()
        self.cb1.option_clear()
        self.cb2.option_clear()
        self.cb3.option_clear()
        self.combo_craft_option.option_clear()
        atributos = [list(attr.values())[0] for attr in self.item_map_name[selected_value]]
        atributos.append("EMPTY")
        atributos.append("NOT")
        self.combo_craft_option['values'] = atributos
        self.cb1['values'] = atributos
        self.cb2['values'] = atributos
        self.cb3['values'] = atributos

    def on_options_change_slot1(self, *args):
        if (self.cb1.get() == "NOT"):
            self.options1 = ""
            return
        self.options1 = self.getSlotType(self.combo_item_type.get(), self.cb1.current())

    def on_options_change_slot2(self, *args):
        if (self.cb2.get() == "NOT"):
            self.options2 = ""
            return
        self.options2 = self.getSlotType(self.combo_item_type.get(), self.cb2.current())

    def on_options_change_slot3(self, *args):
        if (self.cb3.get() == "NOT"):
            self.options3 = ""
            return
        self.options3 = self.getSlotType(self.combo_item_type.get(), self.cb3.current())
    
    def getSlotType(self, itemType, slot_index):
        re = "0"
        if (slot_index != -1):
            if (slot_index != 15):
                re = list(self.item_map_name.get(itemType)[slot_index].keys())[0]
            else:
                re = "0"
            return re; 
        else:
            return ""

    def main_window(self):
        root = tk.Tk()
        root.title("Item Generator")
        root.geometry("700x600")

        # Basic Settings
        group_basic = ttk.LabelFrame(root, text="Basic settings")
        group_basic.place(x=10, y=10, width=320, height=150)

        ttk.Label(group_basic, text="Item ID :").place(x=10, y=10)
        self.entry_item_id = ttk.Entry(group_basic)
        self.entry_item_id.place(x=80, y=10, width=200)

        ttk.Label(group_basic, text="Item Type :").place(x=10, y=40)
        self.combo_item_type = ttk.Combobox(group_basic, values=["Helm", "Suit", "Gloves", "Boots", "Weapon", "Bike"], )
        self.combo_item_type.bind("<<ComboboxSelected>>", self.on_item_type_change)
        self.combo_item_type.place(x=80, y=40, width=200)

        ttk.Label(group_basic, text="Upgrade :").place(x=10, y=70)
        self.combo_upgrade = ttk.Combobox(group_basic, values=[f"{i}" for i in range(21)])
        self.combo_upgrade.bind("<<ComboboxSelected>>", self.on_upgrade_change)
        self.combo_upgrade.set("0")
        self.combo_upgrade.place(x=80, y=70, width=50)

        ttk.Label(group_basic, text="Extreme :").place(x=10, y=100)
        self.combo_extreme = ttk.Combobox(group_basic, values=[f"{i}" for i in range(8)])
        self.combo_extreme.set("0")
        self.combo_extreme.bind("<<ComboboxSelected>>", self.on_extreme_change)
        self.combo_extreme.place(x=80, y=100, width=50)

        ttk.Label(group_basic, text="Divine :").place(x=140, y=100)
        self.combo_divine = ttk.Combobox(group_basic, values=[f"{i}" for i in range(16)])
        self.combo_divine.bind("<<ComboboxSelected>>", self.on_divine_change)
        self.combo_divine.set("0")
        self.combo_divine.place(x=200, y=100, width=50)

        # Item Slots
        group_slots = ttk.LabelFrame(root, text="Item Slots")
        group_slots.place(x=10, y=170, width=320, height=130)

        self.slots = []
        ttk.Label(group_slots, text=f"1 Slot :").place(x=10, y=10 + 30 * 0)
        self.cb1 = ttk.Combobox(group_slots)
        self.cb1.bind("<<ComboboxSelected>>", self.on_options_change_slot1)
        self.cb1.place(x=80, y=10 + 30 * 0, width=220)
        self.slots.append(self.cb1)

        ttk.Label(group_slots, text=f"2 Slot :").place(x=10, y=10 + 30 * 1)
        self.cb2 = ttk.Combobox(group_slots)
        self.cb2.bind("<<ComboboxSelected>>", self.on_options_change_slot2)
        self.cb2.place(x=80, y=10 + 30 * 1, width=220)
        self.slots.append(self.cb2)

        ttk.Label(group_slots, text=f"3 Slot :").place(x=10, y=10 + 30 * 2)
        self.cb3 = ttk.Combobox(group_slots)
        self.cb3.bind("<<ComboboxSelected>>", self.on_options_change_slot3)
        self.cb3.place(x=80, y=10 + 30 * 2, width=220)
        self.slots.append(self.cb3)

        # Generate
        group_generate = ttk.LabelFrame(root, text="Generate")
        group_generate.place(x=10, y=300, width=320, height=150)

        ttk.Button(group_generate, text="Generate", command= self.generate_code).place(x=10, y=20, width=70, height=60)
        
        ttk.Label(group_generate, text="Character:").place(x=10, y=100)
        self.entry_account_id = ttk.Entry(group_generate)
        self.entry_account_id.place(x=80, y=100, width=100)
        ttk.Button(group_generate, text="Send", command= self.send_to_account).place(x=190, y=95, width=50, height=30)

        ttk.Label(group_generate, text="Item Code :").place(x=90, y=20)
        self.entry_item_code = ttk.Entry(group_generate)
        self.entry_item_code.place(x=180, y=20, width=100)

        ttk.Label(group_generate, text="Options Code:").place(x=90, y=45)
        self.entry_options_code = ttk.Entry(group_generate)
        self.entry_options_code.place(x=180, y=45, width=100)

        # Advanced settings
        group_adv = ttk.LabelFrame(root, text="Advanced settings")
        group_adv.place(x=340, y=10, width=320, height=140)

        settings = ["Non Bound", "Bound On Account (Extended)", "Bound On Character (On equipped)", "Bound On Character"]
        for i, setting in enumerate(settings):
            ttk.Radiobutton(group_adv, text=setting, value=i, command=lambda s=setting: self.on_setting_change(s)).place(x=10, y=10 + i * 25)

        # Craft Option
        group_craft = ttk.LabelFrame(root, text="Craft Option")
        group_craft.place(x=340, y=150, width=320, height=125)

        ttk.Label(group_craft, text="Craft Height :").place(x=10, y=10)
        self.combo_craft_height = ttk.Combobox(group_craft, values=[f"{i}" for i in range(8)])
        self.combo_craft_height.set(0)
        self.combo_craft_height.place(x=100, y=10, width=150)

        ttk.Label(group_craft, text="Craft Option :").place(x=10, y=45)
        self.combo_craft_option = ttk.Combobox(group_craft)
        self.combo_craft_option.place(x=100, y=45, width=150)

        # Search Options
        group_search = ttk.LabelFrame(root, text="Search")
        group_search.place(x=340, y=300, width=320, height=125)

        ttk.Label(group_search, text="Item Search:").place(x=10, y=10)
        self.combo_search = SearchableCombobox(group_search, values=list(item_dict.keys()), callback=self.set_id)
        self.combo_search.pack(padx=20, pady=30, fill='x')

        root.mainloop()

    def set_id(self, itemid):
        self.entry_item_id.delete(0, tk.END)
        self.entry_item_id.insert(0, itemid)
    
    def getUserNumAccount(self, characterName):
        try:
            conn_str = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.config['ip']},{self.config['port']};"
                f"DATABASE={self.config['database']};"
                f"UID={self.config['user']};"
                f"PWD={self.config['password']};"
            )

            # Conecta ao banco
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            # Executa a query
            cursor.execute("select CharacterIdx / 16 as usernum from cabal_character_table where name = ?", characterName)
            result = cursor.fetchone()

            # Fecha conexão
            cursor.close()
            conn.close()

            # Se tiver resultado, converte e insere
            if result and result[0] is not None:
                return result.usernum
            else:
                messagebox.showerror("Error", f"Character Not Found")
        except Exception as e:
            print(f"Error: {e}")

    def send_to_account(self):
        accountid = self.getUserNumAccount(self.entry_account_id.get())
        if accountid == 0:
             messagebox.showinfo("Error", "Invalid Character")
             return
        
        self.generate_code()
        try:
            conn_str = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.config['ip']},{self.config['port']};"
                f"DATABASE={self.config['database_cash']};"
                f"UID={self.config['user']};"
                f"PWD={self.config['password']};"
            )

            # Conecta ao banco
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            # Executa a query
            cursor.execute("DECLARE @return_value INT; EXEC @return_value = dbo.up_AddMyCashItemByItem @UserNum = ?, @TranNo = ?, @ServerIdx = ?, " \
            " @ItemIdx = ?, @ItemOpt = ?, @DurationIdx = 0, @Memo = '1';", accountid, random.randint(10000, 99999), 
            1, self.entry_item_code.get(), self.entry_options_code.get())
            conn.commit()

            # Fecha conexão
            cursor.close()
            conn.close()
            messagebox.showinfo("Sucess", f"Item Send")
        except Exception as e:
            print(f"Error: {e}")

    def generate_code(self):
        if not self.entry_item_id.get().isdigit():
            messagebox.showinfo("Error", "Invalid ID")
            return

        item_id = int(self.entry_item_id.get())
        item_code = item_id + self.advance_setting + self.upgrade + self.extreme + self.divine

        self.craft = "00"
        craftheight = "00"

        if self.combo_craft_option.current() > -1 and self.combo_craft_option.get() != "EMPTY" and self.combo_craft_option.get() != "NOT":
            if self.combo_craft_height.current() == 1:
                craftheight = "9"
            elif self.combo_craft_height.current() == 2:
                craftheight = "A"
            elif self.combo_craft_height.current() == 3:
                craftheight = "B"
            elif self.combo_craft_height.current() == 4:
                craftheight = "C"
            elif self.combo_craft_height.current() == 5:
                craftheight = "D"
            elif self.combo_craft_height.current() == 6:
                craftheight = "E"
            elif self.combo_craft_height.current() == 7:
                craftheight = "F"

        if self.combo_craft_option.current() > -1 and craftheight != "00" and self.combo_craft_option.get() != "EMPTY" and self.combo_craft_option.get() != "NOT":
            self.craft = craftheight + list(self.item_map_name.get(self.combo_item_type.get())[self.combo_craft_option.current()].keys())[0]

        slots_number = 0
        optionsCode = ""
        alloptions = ""
        opton_count = ""

        if self.cb1.current() != -1 and self.cb1.get() != 'NOT':
            slots_number += 1
            alloptions += self.options1
        if self.cb2.current() != -1 and self.cb2.get() != 'NOT':
            slots_number += 1
            alloptions += self.options2
        if self.cb3.current() != -1 and self.cb3.get() != 'NOT':
            slots_number += 1
            alloptions += self.options3

        print(alloptions)

        alloptions = list(alloptions)
        i = 0
        while i < len(alloptions):
            if alloptions[i] == "0":             
                opton_count = "00" + opton_count
            else:
                count = 0
                a = alloptions[i]

                for n in range(len(alloptions)):
                    if a == alloptions[n]:
                        count += 1

                alloptions = [c if c != a else "0" for c in alloptions]
                opton_count += str(count) + a
            i += 1         

        if len(opton_count) == 2:
            opton_count = "0000" + opton_count
        elif len(opton_count) == 4:
            opton_count = "00" + opton_count
        elif len(opton_count) == 8:
            opton_count = opton_count[2:]
            
        optionsCode = opton_count

        if slots_number == 0:
            if self.craft != "00":
                optionsCode = "000000" + self.craft
            else:
                optionsCode = "00000000"
            
        if slots_number == 1:
            optionsCode = opton_count
            if self.craft != "00":
                optionsCode = "1" + optionsCode[1:] + self.craft
            else:
                optionsCode = "10" + optionsCode

        if slots_number == 2:
            if self.craft != "00":
                optionsCode = "2" + optionsCode[1:] + self.craft
            else:
                optionsCode = "20" + optionsCode
        
        if slots_number == 3:
            if self.craft != "00":
                optionsCode = "3" + optionsCode[1:] + self.craft
            else:
                optionsCode = "30" + optionsCode

        self.entry_options_code.delete(0, tk.END)
        self.entry_item_code.delete(0, tk.END)
        self.entry_item_code.insert(0, item_code)
        self.entry_options_code.insert(0, int(optionsCode, 16))

item_dict = {
  "Upgrade Core (Medium)": "10",
  "Forcium Great Sword": "100",
  "Sample Battlehelm (Lv. 50)": "1000",
  "Sample Martialhelm (Lv. 50)": "1001",
  "Epaulet of Vampire R": "1002",
  "Epaulet of Halloween R": "1003",
  "Little Jack": "1004",
  "Funny Little Jack - Halloween Pack.": "1005",
  "Funny Little Jack - Type A": "1006",
  "Funny Little Jack - Type B": "1007",
  "Funny Little Jack - Type C": "1008",
  "Halloween Life Candy (Lv. 3)": "1009",
  "Master's Bluestin Great Sword": "101",
  "Halloween Mana Candy (Lv. 3)": "1010",
  "Halloween Gift Ticket": "1011",
  "Halloween Trick Ticket": "1012",
  "Costume] Halloween - Pumpkin Helmet": "1013",
  "[Costume] Candy - Orb/Crystal": "1014",
  "[Costume] Candy - Katana/Daikatana": "1015",
  "[Costume] Candy - Blade/Greatsword": "1016",
  "[Costume] Sweet Candy - Orb/Crystal": "1017",
  "[Costume] Sweet Candy - Katana/Daikatana": "1018",
  "[Costume] Sweet Candy - Blade/Greatsword": "1019",
  "Master's Titanium Great Sword": "102",
  "Avatar - Pumpkin Helmet": "1020",
  "Pet - Garlie": "1021",
  "Pet - Red Garlie": "1022",
  "Pet - Giant Beetle": "1023",
  "Pet - Nipperlug": "1024",
  "Pet - Rabbithorn": "1025",
  "Pet - Troglo": "1026",
  "Pet - Troglo Warrior": "1027",
  "Pet - Elder Troglo": "1028",
  "Pet - Mantis": "1029",
  "Master's Shadow Titanium Great Sword": "103",
  "Pet - Dire Boar": "1030",
  "Pet - Mummy": "1031",
  "Pet - Crag Turtle": "1032",
  "Pet - Armaku": "1033",
  "Epaulet of Night Walkers (Armor)": "1034",
  "Epaulet of Night Walkers (Battle)": "1035",
  "Epaulet of Night Walkers (Martial)": "1036",
  "[Costume] Epaulet of Vampire": "1445",
  "[Costume] Epaulet of Halloween": "1038",
  "[Costume] Epaulet of Night Walkers (Armor)": "1039",
  "Master's Osmium Great Sword": "104",
  "[Costume] Epaulet of Night Walkers (Battle)": "1040",
  "[Costume] Epaulet of Night Walkers (Martial)": "1041",
  "Gold Coin (10,000 Alz)": "1042",
  "Silver Coin (5,000 Alz)": "1043",
  "Copper Coin (1,000 Alz)": "1044",
  "Pet - Little Pery": "1045",
  "Pet - Golem Jr.": "1046",
  "Pet - Little Gry": "1047",
  "Pet - Cutie Lai": "1048",
  "Pet - Cutie Tai": "1049",
  "Training Armorsuit": "105",
  "Pet - Charisma Pan": "1050",
  "Pet - Fancy Zard": "1051",
  "Pet - Gentle Parrot": "1052",
  "Pet - Peng": "1053",
  "Pet - Pinky": "1054",
  "Pet - Rudolph": "1055",
  "[Costume] Epaulet of Freed": "1056",
  "[Costume] Epaulet of Rin": "1057",
  "[Costume] Epaulet of Skaild": "1058",
  "[Costume] Epaulet of Mystic Blade": "1059",
  "Armor Suits": "106",
  "[Costume] Epaulet of Yuan": "1060",
  "[Costume] Epaulet of Santa": "1061",
  "[Costume] Epaulet of Cutie Santa": "1062",
  "[Costume] Snow Ice - Orb/Crystal": "1063",
  "[Costume] Snow Ice - Katana/Daikatana": "1064",
  "[Costume] Snow Ice - Blade/Greatsword": "1065",
  "[Costume] Snow Star - Orb/Crystal": "1066",
  "[Costume] Snow Star - Katana/Daikatana": "1067",
  "[Costume] Snow Star - Blade/Greatsword": "1068",
  "Antler & Red Nose": "1069",
  "Reinforced Armorsuit": "107",
  "Antler": "1070",
  "Red Nose": "1071",
  "Winter Cap": "1072",
  "Earmuff": "1073",
  "X Mask": "1074",
  "Skeleton Mask": "1075",
  "Santa Hood": "1076",
  "Cutie Santa Hood": "1077",
  "[Costume] Antler & Red Nose": "1078",
  "[Costume] Antler": "1079",
  "Iron Armorsuit": "108",
  "[Costume] Red Nose": "1080",
  "[Costume] Winter Cap": "1081",
  "[Costume] Earmuff": "1082",
  "[Costume] X Mask": "1083",
  "[Costume] Skeleton Mask": "1084",
  "[Costume] Santa Hood": "1085",
  "[Costume] Cutie Santa Hood": "1086",
  "[Costume] Santa Coat": "1087",
  "[Costume] Cutie Santa Coat": "1088",
  "[Costume] Suit of Wealthy's Party": "1089",
  "Shadowsteel Armorsuit": "109",
  "[Costume] Moon Walkers (Armor)": "1090",
  "[Costume] Moon Walkers (Battle)": "1091",
  "[Costume] Moon Walkers (Martial)": "1092",
  "[Costume] Suit of Vampire": "1093",
  "[Costume] Night Walkers (Armor)": "1094",
  "[Costume] Night Walkers (Battle)": "1095",
  "[Costume] Night Walkers (Martial)": "1096",
  "[Costume] Suit of Freed": "1097",
  "[Costume] Suit of Rin": "1098",
  "[Costume] Suit of Skaild": "1099",
  "SP Potion": "11",
  "Bluestin Armorsuit": "110",
  "[Costume] Suit of Mystic Blade": "1100",
  "[Costume] Suit of Yuan": "1101",
  "[Costume] Halloween Dress": "1102",
  "Love Letter": "1103",
  "Single Diary": "1106",
  "Couple elimination trophy": "1105",
  "Bad Couple": "1107",
  "Sad Single": "1108",
  "Bad Couple (Collect)": "1109",
  "Titanium Armorsuit": "111",
  "Sad Single (Collect)": "1110",
  "Bad Couple (Divide)": "1111",
  "Sad Single (Divide)": "1112",
  "Present box Formula Card (Couple)": "1113",
  "Present box Formula Card (Single)": "1114",
  "Snow Star HP Potion": "1115",
  "Snow Ice MP Potion": "1116",
  "Christmas present box": "1118",
  "Freeze Santa": "1119",
  "Osmium Armorsuit": "112",
  "Stomp Santa": "1120",
  "Strike Santa": "1121",
  "Walking Santa": "1122",
  "Chase Santa": "1123",
  "Ouch! Santa": "1124",
  "Christmas Tree": "575",
  "Little House": "1126",
  "Single Snowman": "33555580",
  "Couple Snowman": "33555581",
  "Red-nosed reindeer Rudolph": "1129",
  "Shineguard Plate (WA)": "113",
  "Christmas Tree (Fancy)": "1130",
  "Freeze Santa (Fancy)": "1131",
  "Stomp Santa (Fancy)": "1132",
  "Strike Santa (Fancy)": "1133",
  "Walking Santa (Fancy)": "1134",
  "Chase Santa (Fancy)": "1135",
  "Ouch! Santa (Fancy)": "1136",
  "Little House (Fancy)": "1137",
  "Merry Christmas Dialogue": "1138",
  "ATI Graphic Card": "784",
  "Shineguard Plate (FS)": "114",
  "Gem": "1140",
  "Helmet": "1141",
  "Fire": "1142",
  "[Costume] New Year's Orb/Crystal": "1143",
  "[Costume] New Year's Katana/Daikatana": "1144",
  "[Costume] New Year's Blade/Great Sword": "1145",
  "[Costume] Japanese Gala Dress": "1146",
  "[Costume] Epaulet of Japanese Gala Dress": "1742",
  "not used": "933",
  "Forcium Plate (WA)": "115",
  "Japanese traditional gala headdress": "1157",
  "[Costume] Japanese traditional Gala Headdress": "1158",
  "Forcium Cube (Orb)": "1159",
  "Forcium Plate (FS)": "116",
  "Forcium Cube (Crystal)": "1160",
  "Forcium Cube (Katana)": "1161",
  "Forcium Cube (Blade)": "1162",
  "Forcium Cube (Daikatana)": "1163",
  "Forcium Cube (Greatsword)": "1164",
  "Forcium Cube (Plate (WA))": "1165",
  "Forcium Cube (Plate (FS))": "1166",
  "Forcium Cube (Coat (FB))": "1167",
  "Forcium Cube (Coat (FA))": "1168",
  "Forcium Cube (Suit (BL))": "1169",
  "Master's Topaz Orb": "117",
  "Forcium Cube (Suit (WI))": "1170",
  "Forcium Cube (Gauntlet (WA))": "1171",
  "Forcium Cube (Gauntlet (FS))": "1172",
  "Forcium Cube (Gloves (FB))": "1173",
  "Forcium Cube (Gloves (FA))": "1174",
  "Forcium Cube (Hands (BL))": "1175",
  "Forcium Cube (Hands (WI))": "1176",
  "Formula Card(No Slot, x2)": "1177",
  "Formula Card(No Slot)": "1178",
  "Formula Card(Slot, x2)": "1179",
  "Master's SIGMetal Orb": "118",
  "Formula Card(Slot)": "1180",
  "Key (R)N": "1181",
  "Key (G)N": "1182",
  "Key (B)N": "1183",
  "Gold KeyN": "1184",
  "MusterN": "1185",
  "G_Sword2N": "1186",
  "Purify Epaulet": "1187",
  "Forcium Cube (Greaves (WA))": "1188",
  "Forcium Cube (Greaves (FS))": "1189",
  "Master's Forcium Orb": "119",
  "Forcium Cube (Boots (FB))": "1190",
  "Forcium Cube (Boots (FA))": "1191",
  "Forcium Cube (Shoes (BL))": "1192",
  "Forcium Cube (Shoes (WI))": "1193",
  "Forcium Cube (Visor (WA))": "1194",
  "Forcium Cube (Visor (FS))": "1195",
  "Forcium Cube (Headgear (FB))": "1196",
  "Forcium Cube (Headgear (FA))": "1197",
  "Forcium Cube (Headpiece (BL))": "1198",
  "Forcium Cube (Headpiece (WI))": "1199",
  "Return Stone": "12",
  "Training Battlesuit": "120",
  "Astral Core (Forcium)": "1200",
  "Astral Core (Blue Forcium)": "1201",
  "C.A. Unit - Regard for H.Age": "1202",
  "Illusion Coral": "1203",
  "Machinery Head": "1204",
  "Astral Skull": "1205",
  "Infernal Ruby": "1206",
  "Formula Card (Cartridge Change)": "1207",
  "Forcium Epaulet of Guardian": "1208",
  "Forcium Epaulet of Fighter": "1209",
  "Battle Suits": "121",
  "Forcium Epaulet of Sage": "1210",
  "NationSelect1N": "1211",
  "NationSelect2N": "1212",
  "Copy Ring2": "1213",
  "Upgrade Core (Highest)": "1214",
  "Force Core (Highest)": "1215",
  "Slot Extender (Highest)": "1216",
  "Cat Stone": "1217",
  "C.A. Unit - Desire H.Age": "1218",
  "Earring of Bless": "1219",
  "Reinforced Battlesuit": "122",
  "Earring of Bless +1": "1220",
  "Adept Bracelet": "1221",
  "Adept Bracelet +1": "1222",
  "[Costume] Epaulet of Chinese Gala Dress": "1743",
  "[Costume] Epaulet of Korean Gala Dress": "1224",
  "[Costume] Epaulet of Goguryeo": "1225",
  "Spicy Rice-cake soup": "1227",
  "Full Rice-cake soup": "1228",
  "Nutritious Rice-cake soup": "1229",
  "Silk Battlesuit": "123",
  "Korean Gala Headdress": "1230",
  "Goguryeo Headband": "1231",
  "[Costume] Korean Gala Headdress": "1232",
  "[Costume] Goguryeo Headband": "1233",
  "[Costume] Chinese Gala Dress": "1234",
  "[Costume] Korean Gala Dress": "1235",
  "[Costume] Goguryeo Dress": "1236",
  "Sandy Dust Mask": "1237",
  "[Costume] Sandy Dust Mask": "1238",
  "X-Gen Card": "1239",
  "Aramid Battlesuit": "124",
  "Bamboo Leaf": "1240",
  "Bamboo shoot": "1241",
  "(S)Concentration Potion (Lv. 1)": "1242",
  "(S)Reflex Potion (Lv. 1)": "1243",
  "(S)Vital Potion (Lv. 1)": "1244",
  "(S)Sharpness Potion (Lv. 1)": "1245",
  "(S)Mana Condense Potion (Lv. 1)": "1246",
  "(S)Vital Regen Potion (Lv. 1)": "1247",
  "(S)Hardness Potion (Lv. 1)": "1248",
  "(S)Strike Potion (Lv. 1)": "1249",
  "Bluestin Battlesuit": "125",
  "(S)Concentration Potion (Lv. 2)": "1250",
  "(S)Reflex Potion (Lv. 2)": "1251",
  "(S)Vital Potion (Lv. 2)": "1252",
  "(S)Sharpness Potion (Lv. 2)": "1253",
  "(S)Mana Condense Potion (Lv. 2)": "1254",
  "(S)Goodluck Potion (Lv. 1)": "1255",
  "(S)Vital Regen Potion (Lv. 2)": "1256",
  "(S)Hardness Potion (Lv. 2)": "1257",
  "(S)Strike Potion (Lv. 2)": "1258",
  "(S)Goodluck Potion (Lv. 2)": "1259",
  "Titanium Battlesuit": "126",
  "(S)Concentration Potion (Lv. 3)": "1260",
  "(S)Reflex Potion (Lv. 3)": "1261",
  "(S)Vital Potion (Lv. 3)": "1262",
  "(S)Sharpness Potion (Lv. 3)": "1263",
  "(S)Mana Condense Potion (Lv. 3)": "1264",
  "(S)Vital Regen Potion (Lv. 3)": "1265",
  "(S)Hardness Potion (Lv. 3)": "1266",
  "(S)Strike Potion (Lv. 3)": "1267",
  "Core Enhancer (Highest)": "1268",
  "Life Capsule (Lv. 2) TypeA": "1269",
  "Osmium Battlesuit": "127",
  "Life Capsule (Lv. 2) TypeB": "1270",
  "Life Capsule (Lv. 3) TypeA": "1271",
  "Life Capsule (Lv. 3) TypeB": "1272",
  "Pet - Fortune Pig": "1273",
  "Pet - Golden Fortune Pig": "1274",
  "Periodical Remote shop card": "1275",
  "Capella (WA)": "1276",
  "Capella (BL)": "1277",
  "Capella (WI)": "1278",
  "Capella (FA)": "1279",
  "Teragrace Coat (FB)": "128",
  "Capella (FS)": "1280",
  "Capella (FB)": "1281",
  "Procyon (WA)": "1282",
  "Procyon (BL)": "1283",
  "Procyon (WI)": "1284",
  "Procyon (FA)": "1285",
  "Procyon (FS)": "1286",
  "Procyon (FB)": "1287",
  "[Costume] Uniform of Capella (WA)": "1288",
  "[Costume] Uniform of Capella (BL)": "1289",
  "Teragrace Coat (FA)": "129",
  "[Costume] Uniform of Capella (WI)": "1290",
  "[Costume] Uniform of Capella (FA)": "1291",
  "[Costume] Uniform of Capella (FS)": "1292",
  "[Costume] Uniform of Capella (FB)": "1293",
  "[Costume] Uniform of Procyon (WA)": "1294",
  "[Costume] Uniform of Procyon (BL)": "1295",
  "[Costume] Uniform of Procyon (WI)": "1296",
  "[Costume] Uniform of Procyon (FA)": "1297",
  "[Costume] Uniform of Procyon (FS)": "1298",
  "[Costume] Uniform of Procyon (FB)": "1299",
  "Money": "13",
  "Forcium Coat (FB)": "130",
  "Portable FT Panel": "1300",
  "Core Cube": "1301",
  "Duplicate Ring 3": "1302",
  "Chaos Box - Ticket": "1303",
  "Chaos Box - Epaulet": "1304",
  "Chaos Box - Avatar": "1305",
  "Chaos Box - Core": "1306",
  "Chaos Box - Extender": "1307",
  "Chaos Box - Board": "1308",
  "Fan": "1309",
  "Forcium Coat (FA)": "131",
  "Chaos Lamp (GLB Exclusive)": "1310",
  "Chaos Box - Free": "1311",
  "Legacy Weapon": "1312",
  "Honda Logo": "1313",
  "Shadow Titanium Armor Suit(WA)": "1314",
  "Shadow Titanium Armor Suit(FS)": "1315",
  "Shadow Titanium Battle Suit(FB)": "1316",
  "Shadow Titanium Battle Suit(FA)": "1317",
  "Shadow Titanium Martial Suit(BL)": "1318",
  "Shadow Titanium Martial Suit(WI)": "1319",
  "Master's Topaz Crystal": "132",
  "Shadow Titanium Armor Gloves(WA)": "1320",
  "Shadow Titanium Armor Gloves(FS)": "1321",
  "Shadow Titanium Battle Gloves(FB)": "1322",
  "Shadow Titanium Battle Gloves(FA)": "1323",
  "Shadowtitanium Martial Gloves (BL)": "1324",
  "Shadow Titanium Martial Gloves(WI)": "1325",
  "Shadow Titanium Armor Boots(WA)": "1326",
  "Shadow Titanium Armor Boots(FS)": "1327",
  "Shadow Titanium Battle Boots(FB)": "1328",
  "Shadow Titanium Battle Boots(FA)": "1329",
  "Master's SIGMetal Crystal": "133",
  "Shadow Titanium Martial Boots(BL)": "1330",
  "Shadow Titanium Martial Boots(WI)": "1331",
  "Shadow Titanium Armor Helm (WA)": "1332",
  "Shadow Titanium Armor Helm (FS)": "1333",
  "Shadow Titanium Battle Helm(FB)": "1334",
  "Shadow Titanium Battle Helm(FA)": "1335",
  "Shadow Titanium Martial Helm(BL)": "1336",
  "Shadow Titanium Martial Helm(WI)": "1337",
  "Shadow Epaulet of Guardian": "1338",
  "Shadow Epaulet of Fighter": "1339",
  "Master's Forcium Crystal": "134",
  "Shadow Epaulet of Sage": "1340",
  "[Costume] Love Dad Yellow Shirt Epaulet": "1341",
  "[Costume] Love Dad Yellow Shirt": "1342",
  "Anniversary Medal-Armor (Lv. 1)": "1343",
  "Anniversary Medal-Armor (Lv. 2)": "1344",
  "Anniversary Medal-Armor (Lv. 3)": "1345",
  "Anniversary Medal-Battle (Lv. 1)": "1346",
  "Anniversary Medal-Battle (Lv. 2)": "1347",
  "Anniversary Medal-Battle (Lv. 3)": "1348",
  "Anniversary Medal-Martial (Lv. 1)": "1349",
  "Training Martialsuit": "135",
  "Anniversary Medal-Martial (Lv. 2)": "1350",
  "Anniversary Medal-Martial (Lv. 3)": "1351",
  "Anniversary item (Bronze)": "1564",
  "Anniversary item (Silver)": "1565",
  "Anniversary item (Platium)": "1354",
  "Anniversary item": "1567",
  "Wedding Ring": "1356",
  "[Costume] Happy Birthday Hat": "1357",
  "Avatar- Happy Birthday Hat": "1358",
  "Happy Birthday Dialogue": "1359",
  "Martial Suits": "136",
  "Happy Birthday Ring": "33555611",
  "Event Rudolph": "1361",
  "Royal Fixer Lv 1": "1362",
  "Royal Fixer Lv 2": "1363",
  "Royal Fixer Lv 3": "1364",
  "Royal Fixer Lv 4": "1365",
  "Royal Fixer Lv 5": "1366",
  "Miracle Key Lv 1": "1367",
  "Miracle Key Lv 2": "1368",
  "Miracle Key Lv 3": "1369",
  "Reinforced Martialsuit": "137",
  "Miracle Key Lv 4": "1370",
  "Miracle Key Lv 5": "1371",
  "Raw Platinum": "1372",
  "Santa's Sleigh": "1373",
  "White Socks": "1374",
  "Green Socks": "1375",
  "Shiny Blue Socks": "1376",
  "Santa's Red Socks": "1377",
  "[Costume] Santa Fury Epaulet": "1378",
  "Gold Coin": "33555353",
  "Silk Martialsuit": "138",
  "Silver Coin": "1380",
  "Copper Coin": "1381",
  "Blessing Bead - Plus": "1382",
  "Chaos Box - Arena": "1383",
  "Spring Bamboo shoot": "1384",
  "Soft Bamboo shoot": "1385",
  "Tasty Bamboo shoot": "1386",
  "Spring Bamboo Leaf (Small)": "1387",
  "Spring Bamboo Leaf (Big)": "1388",
  "Blessing Bead - WEXP (25%)": "1389",
  "Aramid Martialsuit": "139",
  "Giftbox - [Costume] Santa Coat": "1390",
  "Giftbox - [Costume] Cutie Santa Coat": "1391",
  "Giftbox - [Costume] Suit of Wealthy's Party": "1392",
  "Giftbox - [Costume] Suit of Vampire": "1393",
  "Giftbox - [Costume] Night Walkers (Armor)": "1394",
  "Giftbox - [Costume] Night Walkers (Battle)": "1395",
  "Giftbox - [Costume] Night Walkers (Martial)": "1396",
  "Giftbox - [Costume] Suit of Freed": "1397",
  "Giftbox - [Costume] Suit of Rin": "1398",
  "Giftbox - [Costume] Suit of Skaild": "1399",
  "Item Box": "14",
  "Bluestin Martialsuit": "140",
  "Giftbox - [Costume] Suit of Mystic Blade": "1400",
  "Giftbox - [Costume] Suit of Yuan": "1401",
  "Giftbox - [Costume] Halloween Dress": "1402",
  "Giftbox - [Costume] Japanese Gala Dress": "1403",
  "Giftbox - [Costume] Chinese Gala Dress": "1404",
  "Giftbox - [Costume] Korean Gala Dress": "1405",
  "Giftbox - [Costume] Goguryeo Dress": "1406",
  "Giftbox - [Costume] Uniform of Capella (WA)": "1407",
  "Giftbox - [Costume] Uniform of Capella (BL)": "1408",
  "Giftbox - [Costume] Uniform of Capella (WI)": "1409",
  "Titanium Martialsuit": "141",
  "Giftbox - [Costume] Uniform of Capella (FA)": "1410",
  "Giftbox - [Costume] Uniform of Capella (FS)": "1411",
  "Giftbox - [Costume] Uniform of Capella (FB)": "1412",
  "Giftbox - [Costume] Uniform of Procyon (WA)": "1413",
  "Giftbox - [Costume] Uniform of Procyon (BL)": "1414",
  "Giftbox - [Costume] Uniform of Procyon (WI)": "1415",
  "Giftbox - [Costume] Uniform of Procyon (FA)": "1416",
  "Giftbox - [Costume] Uniform of Procyon (FS)": "1417",
  "Giftbox - [Costume] Uniform of Procyon (FB)": "1418",
  "Giftbox - Astral Board Card - X Steelblue": "1419",
  "Osmium Martialsuit": "142",
  "Giftbox - Astral Board Card - X Green": "1420",
  "Giftbox - Astral Board Card - X2 Red": "1421",
  "Giftbox - Astral Board Card - X2 Blue": "1422",
  "Giftbox - Astral Board Card - X3 White": "1423",
  "Perfect Core (Low)": "1424",
  "Perfect Core (Medium)": "1425",
  "Perfect Core (High)": "1426",
  "Vital Gear (HP)": "1427",
  "Forest Water": "1428",
  "Clean Water": "1429",
  "Mystic Suit (BL)": "143",
  "Water Elemental": "1430",
  "[Costume] Water Elemental Epaulet": "1431",
  "[Costume] Water Elemental Dress": "1432",
  "[Costume] Water Elemental - Orb/Crystal": "1433",
  "[Costume] Water Elemental - Katana/Daikatana": "1434",
  "[Costume] Water Elemental - Blade/Great Sword": "1435",
  "Fireworks": "1436",
  "Chaos Lamp for Global service": "1437",
  "Water Dialogue": "1438",
  "Fireworks FX": "1439",
  "Mystic Suit (WI)": "144",
  "Blessing Bead - Skill EXP (50%)": "1440",
  "NationSelect3N": "1441",
  "Astral Bike Card - Crystal Blue": "3128",
  "Astral Bike Card - Crystal RW3": "3129",
  "X-Fire Event Item": "1444",
  "Earring of Guard +5": "1446",
  "Earring of Guard +6": "1447",
  "Earring of Guard +7": "1448",
  "Force Regeneration Earring +5": "1449",
  "Forcium Suit (BL)": "145",
  "Force Regeneration Earring +6": "1450",
  "Force Regeneration Earring +7": "1451",
  "Vampiric Earring +5": "1452",
  "Vampiric Earring +6": "1453",
  "Vampiric Earring +7": "1454",
  "Bracelet Of Fighter +5": "1455",
  "Bracelet Of Fighter +6": "1456",
  "Bracelet Of Fighter +7": "1457",
  "Bracelet Of Sage +5": "1458",
  "Bracelet Of Sage +6": "1459",
  "Forcium Suit (WI)": "146",
  "Bracelet Of Sage +7": "1460",
  "Extortion Bracelet +5": "1461",
  "Extortion Bracelet +6": "1462",
  "Extortion Bracelet +7": "1463",
  "Pet Name Card": "1464",
  "Pet Sleeping Kit (Lv. 1)": "1465",
  "Pet Untrain Kit": "1466",
  "Muster2N": "1467",
  "TorchN": "1468",
  "Piece of Rock1S": "1469",
  "Master's Red Osmium Katana": "147",
  "Piece of Rock2S": "1470",
  "Oil BottleN": "1471",
  "Piece of Rock3N": "1472",
  "Piece of Rock4N": "1473",
  "AriyakN": "1474",
  "ArtoorbarN": "1475",
  "Crown": "1476",
  "Trugater": "1477",
  "PuzzleN": "1478",
  "Reflecting StoneN": "1479",
  "Master's SIGMetal Katana": "148",
  "Pet Sleeping Kit (Lv. 2)": "1480",
  "Pet Sleeping Kit (Lv. 3)": "1481",
  "Pet Sleeping Kit (Lv. 4)": "1482",
  "Pet Sleeping Kit (Lv. 5)": "1483",
  "Pet Sleeping Kit (Lv. 6)": "1484",
  "Pet Sleeping Kit (Lv. 7)": "1485",
  "Pet Sleeping Kit (Lv. 8)": "1486",
  "Pet Sleeping Kit (Lv. 9)": "1487",
  "Pet Sleeping Kit (Lv. 10)": "1488",
  "Pet Sleeping Kit (Special)": "1489",
  "Master's Forcium Katana": "149",
  "Pet - ChaCha": "1490",
  "Pet - Worry Bear": "1491",
  "Pet - Meow Ninja": "1492",
  "Pet - Robo Robo": "1493",
  "Pet - Woolly": "1494",
  "Pet - Nevareth Husky": "1495",
  "Giftbox - [Costume] Epaulet of Yuan-30days": "1496",
  "Giftbox - [Costume] Epaulet of Freed-30days": "1497",
  "Giftbox - [Costume] Epaulet of Yuan": "1498",
  "Giftbox - [Costume] Epaulet of Freed": "1499",
  "Training Orb": "15",
  "Training Armorgloves": "150",
  "Cloverfield DVD Item": "1500",
  "Cloverfield Statue Item": "1501",
  "Japanese Ticket - Gold": "1502",
  "Japanese Ticket - Silver": "1503",
  "Japanese Ticket - Bronze": "1504",
  "Pet Rename Card": "1505",
  "Festival Coin Piece - Gold": "1506",
  "Festival Coin - Silver": "1507",
  "Festival Coin - Gold": "1508",
  "Festival Coin Piece - Silver": "1509",
  "Armor Gloves": "151",
  "Blessing Bead - Pet EXP (100%)": "1510",
  "[Costume] Legend of Yellow Suit": "1511",
  "[Costume] Secret Agent Suit": "1512",
  "[Costume] The Sage Tower Apprentice Suit": "1513",
  "[Costume] Legend of Yellow Epaulet": "1514",
  "[Costume] Secret Agent Suit Epaulet": "1515",
  "[Costume] The Sage Tower Apprentice Epaulet": "1516",
  "Giftbox - [Costume] Sonkran Dress": "1517",
  "Giftbox - [Costume] Legend of Yellow Suit": "1518",
  "Giftbox - [Costume] Secret Agent Suit": "1519",
  "Reinforced Armorgloves": "152",
  "Giftbox - [Costume] The Sage Tower Apprentice Suit": "1520",
  "Force Amplifier": "1521",
  "Concentration Potion (Lv. 4) of War": "1522",
  "Reflex Potion (Lv. 4) of War - Mission War": "1523",
  "Vital Potion (Lv. 4) of War": "1524",
  "Sharpness Potion (Lv. 4) of War": "1525",
  "Mana Condense Potion (Lv. 4) of War": "1526",
  "Vital Regen Potion (Lv. 4) of War": "1527",
  "Hardness Potion (Lv. 4) of War": "1528",
  "Strike Potion (Lv. 4) of War": "1529",
  "Iron Armorgloves": "153",
  "Sword Master Potion (Lv. 4) of War": "1530",
  "Magic Master Potion (Lv. 4) of War": "1531",
  "Evasion Potion (Lv. 4) of War": "1532",
  "Pet Change Kit": "1533",
  "Platinum Thanksgiving Giftbox": "1534",
  "Thanksgiving Giftbox": "1535",
  "[Costume] Charisma Pan": "1536",
  "[Costume] Charisma Pan Epaulet": "1537",
  "Compass N": "1538",
  "Giftbox - [Costume] Charisma Pan": "1539",
  "Shadowsteel Armorgloves": "154",
  "Spirit Gear (Lv. 1)": "1540",
  "Pet - Outer Invader": "1541",
  "Remote Agent Shop Card": "1542",
  "[Costume] Spirit of Hip Hop": "1543",
  "[Costume] Gorgeous Servant": "1544",
  "[Costume] Spirit of Hip Hop Epaulet": "1545",
  "[Costume] Gorgeous Servant Epaulet": "1546",
  "Giftbox - [Costume] Spirit of Hip Hop": "1547",
  "Giftbox - [Costume] Gorgeous Servant": "1548",
  "Core Cube (B)": "1549",
  "Bluestin Armorgloves": "155",
  "[Costume] CABAL Birthday Hat": "1550",
  "Avatar - CABAL Birthday Hood": "1551",
  "CABAL Birthday Ring": "1552",
  "Red Necklace S": "1553",
  "Red Necklace N": "1554",
  "Red Stone N": "1555",
  "Fire Charm N": "1556",
  "Spear 2N": "1557",
  "[Costume] Cabal High School": "1558",
  "[Costume] Cabal High School Epaulet": "1559",
  "Titanium Armorgloves": "156",
  "Giftbox - [Costume] Cabal High School": "1560",
  "Event 1S": "1561",
  "Event2N": "1562",
  "Event Lotto Box": "1563",
  "Anniversary item (Platinum)": "1566",
  "Yekaterina VIP Membership": "1568",
  "Giftbox - Black Transmuter": "1569",
  "Osmium Armorgloves": "157",
  "Event Font A": "1570",
  "Event Font B": "1571",
  "Event Font C": "1572",
  "Event Font D": "1573",
  "Event Font E": "1574",
  "Event Font F": "1575",
  "Event Font G": "1576",
  "Event Font H": "1577",
  "Event Font I": "1578",
  "Event Font J": "1579",
  "Shineguard Gauntlet (WA)": "158",
  "Event Font K": "1580",
  "Event Font L": "1581",
  "Event Font M": "1582",
  "Event Font N": "1583",
  "Event Font O": "1584",
  "Event Font P": "1585",
  "Event Font Q": "1586",
  "Event Font R": "1587",
  "Event Font S": "1588",
  "Event Font T": "1589",
  "Shineguard Gauntlet (FS)": "159",
  "Event Font U": "1590",
  "Event Font V": "1591",
  "Event Font W": "1592",
  "Event Font X": "1593",
  "Event Font Y": "1594",
  "Event Font Z": "1595",
  "Event Font 0": "1596",
  "Event Font 1": "1597",
  "Event Font 2": "1598",
  "Event Font 3": "1599",
  "Crude Orb": "16",
  "Forcium Gauntlet (WA)": "160",
  "Event Font 4": "1600",
  "Event Font 5": "1601",
  "Event Font 6": "1602",
  "Event Font 7": "1603",
  "Event Font 8": "1604",
  "Event Font 9": "1605",
  "[Costume] Fists of Rage": "1606",
  "[Costume] Fists of Rage Epaulet": "1607",
  "[Costume] Winter Fever": "1608",
  "[Costume] Winter Fever Epaulet": "1609",
  "Forcium Gauntlet (FS)": "161",
  "Giftbox - [Costume] Fists of Rage": "1610",
  "Giftbox - [Costume] Winter Fever": "1611",
  "Giftbox - [Costume] Love Dad Yellow Shirt": "1612",
  "SIGMetal Orb": "1613",
  "SIGMetal Crystal": "1614",
  "SIGMetal Katana": "1615",
  "SIGMetal Blade": "1616",
  "SIGMetal Daikatana": "1617",
  "SIGMetal Great Sword": "1618",
  "Worm Red Socks": "1619",
  "Master's Red Osmium Blade": "162",
  "Pet - Moley": "1620",
  "Absolute Badge": "1621",
  "Vital Gear (HP) Type AC1": "1622",
  "Vital Gear (HP) Type AC2": "1623",
  "Vital Gear (HP) Type AC3": "1624",
  "Vital Gear (HP) Type AC4": "1625",
  "Vital Gear (HP) Type AC5": "1626",
  "Vital Gear (HP) Type AC6": "1627",
  "Vital Gear (HP) Type AC7": "1628",
  "[Costume] Lovely Sweet": "1629",
  "Master's SIGMetal Blade": "163",
  "[Costume] Lovely Sweet Epaulet": "1630",
  "Pet - Cookie": "1631",
  "Giftbox - [Costume] Lovely Sweet": "1632",
  "SIGMetal Plate (WA)": "1633",
  "SIGMetal Plate (FS)": "1634",
  "SIGMetal Coat (FB)": "1635",
  "SIGMetal Coat (FA)": "1636",
  "SIGMetal Suit (BL)": "1637",
  "SIGMetal Suit (WI)": "1638",
  "SIGMetal Gauntlet (WA)": "1639",
  "Master's Forcium Blade": "164",
  "SIGMetal Gauntlet (FS)": "1640",
  "SIGMetal Glove (FB)": "1641",
  "SIGMetal Glove (FA)": "1642",
  "SIGMetal Hand (BL)": "1643",
  "SIGMetal Hand (WI)": "1644",
  "SIGMetal Greave (WA)": "1645",
  "SIGMetal Greave (FS)": "1646",
  "SIGMetal Boots (FB)": "1647",
  "SIGMetal Boots (FA)": "1648",
  "SIGMetal Shoes (BL)": "1649",
  "Training Battlegloves": "165",
  "SIGMetal Shoes (WI)": "1650",
  "SIGMetal Visor (WA)": "1651",
  "SIGMetal Visor (FS)": "1652",
  "SIGMetal Headgear (FB)": "1653",
  "SIGMetal Headgear (FA)": "1654",
  "SIGMetal Headpiece (BL)": "1655",
  "SIGMetal Headpiece (WI)": "1656",
  "SIGMetal Epaulet of Guardian": "1657",
  "SIGMetal Epaulet of Fighter": "1658",
  "SIGMetal Epaulet of Sage": "1659",
  "Battle Gloves": "166",
  "Disguise SeedN": "1660",
  "Gem1N": "1661",
  "Gem2N": "1662",
  "Gem3N": "1663",
  "Gem4N": "1664",
  "Dry BranchN": "1665",
  "Burning BranchN": "1666",
  "FragmentsN": "1667",
  "Clean Water BottleN": "1668",
  "War Reward Cube (Lv. 1)": "1669",
  "Reinforced Battlegloves": "167",
  "War Reward Cube (Lv. 2)": "1670",
  "War Reward Cube (Lv. 3)": "1671",
  "Hardness Capsule (Lv. 1)": "1672",
  "Hardness Capsule (Lv. 2)": "1673",
  "Hardness Capsule (Lv. 3)": "1674",
  "Hardness Capsule (Lv. 4)": "1675",
  "Hardness Capsule (Lv. 5)": "1676",
  "Hardness Capsule (Lv. 6)": "1677",
  "Hardness Capsule (Lv. 7)": "1678",
  "Hardness Capsule (Lv. 8)": "1679",
  "Silk Battlegloves": "168",
  "EXP": "1680",
  "Skill EXP": "1681",
  "Party EXP": "1682",
  "Pet EXP": "1683",
  "Alz Drop Amount": "1684",
  "Alz Drop Rate": "1685",
  "Alz Bomb Chance": "1686",
  "Attack Rate": "1687",
  "Defense Rate": "1688",
  "Down Resist.": "1689",
  "Aramid Battlegloves": "169",
  "Resist Knock Back": "1690",
  "Stun Resist.": "1691",
  "STR": "1692",
  "DEX": "1693",
  "INT": "1694",
  "HP": "33557989",
  "MP": "1696",
  "Attack": "33557388",
  "Magic Attack": "33557389",
  "Defense": "1699",
  "Red Orb": "17",
  "Bluestin Battlegloves": "170",
  "Max HP Absorb Up": "1700",
  "Max MP Absorb Up": "1701",
  "HP Absorb Up": "1702",
  "MP Absorb Up": "1703",
  "Sword Skill Amp.": "1704",
  "Magic Skill Amp.": "1705",
  "Blessing Bead - AXP (25%)": "1706",
  "EventS (1x2)": "1707",
  "EventN (1x2)": "1708",
  "Event Lotto (1x2)": "1709",
  "Titanium Battlegloves": "171",
  "EventS (2x1)": "1710",
  "EventN (2x1)": "1711",
  "Event Lotto (2x1)": "1712",
  "EventS (2x2)": "1713",
  "EventN (2x2)": "1714",
  "Event Lotto (2x2)": "1715",
  "Event Formula Card": "1716",
  "Blessing Bead - EXP (30%)": "1717",
  "Blessing Bead - Skill EXP (100%)": "1718",
  "Blessing Bead - WEXP (50%)": "1719",
  "Osmium Battlegloves": "172",
  "War Reward Cube (UCC)": "1720",
  "Astral Board Card - K Silver": "1721",
  "Astral Board Card - K Black": "1722",
  "Astral Board Card - K Gold": "1723",
  "Astral Board Card - K Red": "1724",
  "Astral Board Card - K Pink": "1725",
  "Astral Board Card - K Violet": "1726",
  "Astral Board Card - K Blue": "1727",
  "Astral Board Card - K Sky": "1728",
  "Astral Board Card - K Green": "1729",
  "Teragrace gloves (FB)": "173",
  "Astral Board Card - K White": "1730",
  "Blessing Bead - EXP (40%)": "2151",
  "Blessing Bead - Skill EXP (40%)": "2152",
  "Panic Cave Entry Item": "1733",
  "Pet Safety Kit": "1734",
  "Anima Gem": "1735",
  "Vital Potion": "1736",
  "Sharpness Potion": "1737",
  "Manacondense Potion": "1738",
  "Hardness Potion": "1739",
  "Teragrace gloves (FA)": "174",
  "Sword Master Potion": "672",
  "Magic Master Potion": "673",
  "[Costume] Epaulet of Wealthy Party": "1744",
  "Giftbox - [Costume] Water Elemental Epaulet (30 Days)": "1745",
  "Giftbox - [Costume] Epaulet of Goguryeo (30 Days)": "1746",
  "Giftbox - [Costume] Epaulet of Japanese Gala Dress (30 Days)": "1747",
  "Giftbox - [Costume] Epaulet of Chinese gala dress (30 Days)": "1748",
  "Giftbox - [Costume] Epaulet of Gorgeous Servant (30 Days)": "1749",
  "Forcium gloves (FB)": "175",
  "Giftbox - [Costume] Fists of Rage Epaulet (30 Days)": "1750",
  "Giftbox - [Costume] Epaulet of Wealthy Party (30 Days)": "1751",
  "Giftbox - [Costume] Legend of Yellow Epaulet (30 Days)": "1752",
  "Giftbox - [Costume] Epaulet of Lovely Sweety (30 Days)": "1753",
  "Giftbox - [Costume] Epaulet of Charisma Pan (30 Days)": "1754",
  "Giftbox - [Costume] Water Elemental Epaulet (90 Days)": "1755",
  "Giftbox - [Costume] Epaulet of Goguryeo (90 Days)": "1756",
  "Giftbox - [Costume] Epaulet of Japanese gala dress (90 Days)": "1757",
  "Giftbox - [Costume] Epaulet of Chinese gala dress (90 Days)": "1758",
  "Giftbox - [Costume] Epaulet of Gorgeous Servant (90 Days)": "1759",
  "Forcium gloves (FA)": "176",
  "Giftbox - [Costume] Fists of Rage Epaulet (90 Days)": "1760",
  "Giftbox - [Costume] Epaulet of Wealthy Party (90 Days)": "1761",
  "Giftbox - [Costume] Legend of Yellow Epaulet (90 Days)": "1762",
  "Giftbox - [Costume] Epaulet of Lovely Sweety (90 Days)": "1763",
  "Giftbox - [Costume] Epaulet of Charisma Pan (90 Days)": "1764",
  "Gift Box - Rare Epaulet of Guardian (10 Days)": "1765",
  "Gift Box - Rare Epaulet of Guardian (30 Days)": "1766",
  "Gift Box - Rare Epaulet of Guardian (90 Days)": "1767",
  "Gift Box - Forcium Epaulet of Guardian (10 Days)": "1768",
  "Gift Box - Forcium Epaulet of Guardian (30 Days)": "1769",
  "Master's Red Osmium Daikatana": "177",
  "Gift Box - Forcium Epaulet of Guardian (90 Days)": "1770",
  "Gift Box - Rare Epaulet of Fighter (10 Days)": "1771",
  "Gift Box - Rare Epaulet of Fighter (30 Days)": "1772",
  "Gift Box - Rare Epaulet of Fighter (90 Days)": "1773",
  "Gift Box - Forcium Epaulet of Fighter (10 Days)": "1774",
  "Gift Box - Forcium Epaulet of Fighter (30 Days)": "1775",
  "Gift Box - Forcium Epaulet of Fighter(90 Days)": "1776",
  "Gift Box - Rare Epaulet of Sage (10 Days)": "1777",
  "Gift Box - Rare Epaulet of Sage (30 Days)": "1778",
  "Gift Box - Rare Epaulet of Sage (90 Days)": "1779",
  "Master's SIGMetal Daikatana": "178",
  "Gift Box - Forcium Epaulet of Sage (10 Days)": "1780",
  "Gift Box - Forcium Epaulet of Sage (30 Days)": "1781",
  "Gift Box - Forcium Epaulet of Sage (90 Days)": "1782",
  "Chaos Box - Epaulet (Guardian)": "1783",
  "Chaos Box - Epaulet (Fighter)": "1784",
  "Chaos Box - Epaulet (Sage)": "1785",
  "Festival Coin - Bronze": "1786",
  "Disguise Seed2N": "1787",
  "Strike Pray I": "1788",
  "Strike Pray II": "1789",
  "Master's Forcium Daikatana": "179",
  "Strike Pray III": "1790",
  "Crushing Fist I": "1791",
  "Crushing Fist II": "1792",
  "Crushing Fist III": "1793",
  "Sensibility I": "1794",
  "Sensibility II": "1795",
  "Sensibility III": "1796",
  "Counter Force I": "1797",
  "Counter Force II": "1798",
  "Counter Force III": "1799",
  "Coraleye Orb": "18",
  "Training Martialgloves": "180",
  "Sword Dance I": "1800",
  "Sword Dance II": "1801",
  "Sword Dance III": "1802",
  "Sword Pressure I": "1803",
  "Sword Pressure II": "1804",
  "Sword Pressure III": "1805",
  "Force Dance I": "1806",
  "Force Dance II": "1807",
  "Force Dance III": "1808",
  "Force Pressure I": "1809",
  "Martial Gloves": "181",
  "Force Pressure II": "1810",
  "Force Pressure III": "1811",
  "Trauma I": "1812",
  "Trauma II": "1813",
  "Trauma III": "1814",
  "Shock I": "1815",
  "Shock II": "1816",
  "Shock III": "1817",
  "Hornet Pierce I": "1818",
  "Hornet Pierce II": "1819",
  "Reinforced Martialgloves": "182",
  "Hornet Pierce III": "1820",
  "Velocity I": "1821",
  "Velocity II": "1822",
  "Velocity III": "1823",
  "Cure I": "1824",
  "Cure II": "1825",
  "Cure III": "1826",
  "Defenseless I": "1827",
  "Defenseless II": "1828",
  "Defenseless III": "1829",
  "Silk Martialgloves": "183",
  "Shield Smash I": "1830",
  "Shield Smash II": "1831",
  "Shield Smash III": "1832",
  "Warp Stone": "1833",
  "[Costume] Dancing Temptation": "1834",
  "[Costume] Epaulet of Dancing Temptation": "1835",
  "[Costume] Wild Western": "1836",
  "[Costume] Epaulet of Wild Western": "1837",
  "Giftbox - [Costume] Dancing Temptation": "1838",
  "Giftbox - [Costume] Wild Western": "1839",
  "Aramid Martialgloves": "184",
  "[Costume] Dancing Temptation Mask": "1840",
  "[Costume] Mask of Dancing Temptation": "1841",
  "[Costume] Wild Western Hat": "1842",
  "[Costume] Hat of Wild Western": "1843",
  "Blessing Bead - Dummy": "2253",
  "Blessing Bead - T Point (100%)": "1845",
  "Blessing Bead - WEXP (10%)": "1846",
  "Skill Book (Flash Draw)": "1847",
  "Skill Book (Impact Stab)": "1848",
  "Skill Book (Power Stab)": "1849",
  "Bluestin Martialgloves": "185",
  "Skill Book (Stab Slash)": "1850",
  "Skill Book (Force Stab)": "1851",
  "Skill Book (Raging Thrust)": "1852",
  "Skill Book (Death Cross)": "1853",
  "Skill Book (Wild Strike)": "1854",
  "Skill Book (Illusion Stab)": "1855",
  "Skill Book (Heavy Slash)": "1856",
  "Skill Book (Power Slash)": "1857",
  "Skill Book (Press Impact)": "1858",
  "Skill Book (Slash Break)": "1859",
  "Titanium Martialgloves": "186",
  "Skill Book (Rolling Crash)": "1860",
  "Skill Book (Guillotine)": "1861",
  "Skill Book (Double Slash)": "1862",
  "Skill Book (Force Slash)": "1863",
  "Skill Book (Triple Slash)": "1864",
  "Skill Book (Force Break)": "1865",
  "Skill Book (Hasing Dance)": "1866",
  "Skill Book (Force Drive)": "1867",
  "Skill Book (Rising Shot)": "1868",
  "Skill Book (Rising Blade)": "1869",
  "Osmium Martialgloves": "187",
  "Skill Book (Double Rising)": "1870",
  "Skill Book (Soaring Shot)": "1871",
  "Skill Book (Cascade Break)": "1872",
  "Skill Book (Storm Grind)": "1873",
  "Skill Book (Earth Divide)": "1874",
  "Skill Book (Dance of Ruin)": "1875",
  "Skill Book (Charge)": "1876",
  "Skill Book (Assault)": "1877",
  "Skill Book (Shield Charge)": "1878",
  "Skill Book (Force Assault)": "1879",
  "Mystic Hands (BL)": "188",
  "Skill Book (Whirlwind)": "1880",
  "Skill Book (Blade Scud)": "1881",
  "Skill Book (Shield Break)": "1882",
  "Skill Book (Infernal Impact)": "1883",
  "Skill Book (Round Cut)": "1884",
  "Skill Book (Spiral Doom)": "1885",
  "Skill Book (Mirage Grind)": "1886",
  "Skill Book (Storm Crush)": "1887",
  "Skill Book (Infernal Stigma)": "1888",
  "Skill Book (Sword Cannon)": "1889",
  "Mystic Hands (WI)": "189",
  "Skill Book (Twin-moon Slash)": "1890",
  "Skill Book (Shield Ray)": "1891",
  "Skill Book (Abyssal Crystal)": "1892",
  "Skill Book (Terra Break)": "1893",
  "Skill Book (Blade Cry)": "1894",
  "Skill Book (Shield Explosion)": "1895",
  "Skill Book (Force Impact)": "1896",
  "Skill Book (Seismic Wave)": "1897",
  "Skill Book (Lightning Slash)": "1898",
  "Skill Book (Shield Splinter)": "1899",
  "Citrine Orb": "19",
  "Forcium Hands (BL)": "190",
  "Skill Book (Assassinate)": "1900",
  "Skill Book (Force Kick)": "1901",
  "Skill Book (Vital Interfere)": "1902",
  "Skill Book (Provocation)": "1903",
  "Skill Book (Magic Arrow)": "1904",
  "Skill Book (Terra Arrow)": "1905",
  "Skill Book (Aqua Arrow)": "1906",
  "Skill Book (w)ind Arrow)": "1907",
  "Skill Book (Fire Arrow)": "1908",
  "Skill Book (Freezing Arrow)": "1909",
  "Forcium Hands (WI)": "191",
  "Skill Book (Lightning Arrow)": "1910",
  "Skill Book (Magic Blast)": "1911",
  "Skill Book (St)one Blast)": "1912",
  "Skill Book (Aqua Blast)": "1913",
  "Skill Book (Wind Blast)": "1914",
  "Skill Book (Fire Blast)": "1915",
  "Skill Book (Ice Blast)": "1916",
  "Skill Book (Lightning Blast)": "1917",
  "Skill Book (Magic Lance)": "1918",
  "Skill Book (Terra Lance)": "1919",
  "Master's Red Osmium Great Sword": "192",
  "Skill Book (Aqua Lance)": "1920",
  "Skill Book (Wind Lance)": "1921",
  "Skill Book (Fire Lance)": "1922",
  "Skill Book (Freezing Lance)": "1923",
  "Skill Book (Lightning Lance)": "1924",
  "Skill Book (Magic Cannon)": "1925",
  "Skill Book (Stone Cannon)": "1926",
  "Skill Book (Aqua Cannon)": "1927",
  "Skill Book (Wind Cannon)": "1928",
  "Skill Book (Fire Cannon)": "1929",
  "Master's SIGMetal Great Sword": "193",
  "Skill Book (Crystal Cannon)": "1930",
  "Skill Book (Lightning Cannon)": "1931",
  "Skill Book (Burning Hand)": "1932",
  "Skill Book (Freeze)": "1933",
  "Skill Book (Dig Bomb)": "1934",
  "Skill Book (Icicle Shower)": "1935",
  "Skill Book (Wind Cutter)": "1936",
  "Skill Book (Hydro Disk)": "1937",
  "Skill Book (Lightning Strike)": "1938",
  "Skill Book (Acid Trap)": "1939",
  "Master's Forcium Great Sword": "194",
  "Skill Book (Vacuum)": "1940",
  "Skill Book (Chain Explosion)": "1941",
  "Skill Book (Hail Storm)": "1942",
  "Skill Book (Extreme Dual Cannon)": "1943",
  "Skill Book (Arctic Field)": "1944",
  "Skill Book (Space Collapse)": "1945",
  "Skill Book (Power Shot)": "1946",
  "Skill Book (Critical Shot)": "1947",
  "Skill Book (Explosion Shot)": "1948",
  "Skill Book (Poison Arrow)": "1949",
  "Training Armorboots": "195",
  "Skill Book (Shadow Shot)": "1950",
  "Skill Book (Drilled Shot)": "1951",
  "Skill Book (Prismatic Arrow)": "1952",
  "Skill Book (Gravity Distortion)": "1953",
  "Skill Book (Shooting Star)": "1954",
  "Skill Book (Shield Shock)": "1955",
  "Skill Book (Flying Shield)": "1956",
  "Skill Book (Blade of Judgement)": "1957",
  "Skill Book (Blade Force)": "1958",
  "Skill Book (Blade Aura)": "1959",
  "Armor Boots": "196",
  "Skill Book (Soul Blade)": "1960",
  "Skill Book (Concentration)": "1961",
  "Skill Book (Iron skin)": "1962",
  "Skill Book (Aura Barrier)": "1963",
  "Skill Book (Panic Cry)": "1964",
  "Skill Book (Field of Fear)": "1965",
  "Skill Book (Dash)": "1966",
  "Skill Book (Fade Step)": "1967",
  "Skill Book (Instant Immunity)": "1968",
  "Skill Book (Mirage Step)": "1969",
  "Reinforced Armorboots": "197",
  "Skill Book (Natural Hiding)": "1970",
  "Skill Book (Intuition)": "1971",
  "Skill Book (Art of Fierceness)": "1972",
  "Skill Book (Intense Blade)": "1973",
  "Skill Book (Down Breaker)": "1974",
  "Skill Book (Morale Shout)": "1975",
  "Skill Book (Fury Shout)": "1976",
  "Skill Book (Cat's Recovery)": "1977",
  "Skill Book (Bear's Vitality)": "1978",
  "Skill Book (Bloody Spirit)": "1979",
  "Iron Armorboots": "198",
  "Skill Book (Art of Shout)": "33554614",
  "Skill Book (Resist intension)": "1981",
  "Skill Book (Shadow Shield)": "1982",
  "Skill Book (Art of Defense)": "1983",
  "Skill Book (Art of Force Control)": "1984",
  "Skill Book (Raise Spirit)": "1985",
  "Skill Book (Mass Restore)": "1986",
  "Skill Book (High Regeneration)": "1987",
  "Skill Book (Regeneration)": "1988",
  "Skill Book (Weaken)": "1989",
  "Shadowsteel Armorboots": "199",
  "Skill Book (Hardness)": "1990",
  "Skill Book (Energy Field)": "1991",
  "Skill Book (Blink)": "1992",
  "Skill Book (Spirit Intension)": "1993",
  "Skill Book (Force Hardness)": "1994",
  "Skill Book (Precision)": "1995",
  "Skill Book (Repulsive Armor)": "1996",
  "Skill Book (Blind)": "1997",
  "Skill Book (Eagle Eye)": "1998",
  "Skill Book (Curse of Wither)": "1999",
  "Force Core (High)": "2",
  "Bluestin Orb": "20",
  "Bluestin Armorboots": "200",
  "Skill Book (Sharpness)": "2000",
  "Skill Book (Mana Condense)": "2001",
  "Skill Book (Vital Force)": "2002",
  "Skill Book (Greater Heal)": "2003",
  "Skill Book (Vital Bless)": "2004",
  "Skill Book (Mass Heal)": "2005",
  "Skill Book (Crushing Blade)": "2006",
  "Skill Book (Heal)": "2007",
  "Skill Book (Shield Harden)": "2008",
  "Skill Book (Mortal Bane)": "2009",
  "Titanium Armorboots": "201",
  "Skill Book (Enchant)": "2010",
  "Skill Book (Earth Guard)": "2011",
  "Skill Book (Aqua Vitality)": "2012",
  "Skill Book (Wind Movement)": "2013",
  "Skill Book (Fire Blade)": "2014",
  "Skill Book (Guard Break)": "2015",
  "Skill Book (Lightning Blade)": "2016",
  "Skill Book (Ice Blade)": "2017",
  "Skill Book (Enervation)": "2018",
  "Skill Book (Execration": "2019",
  "Osmium Armorboots": "202",
  "Skill Book (Hard Luck)": "2020",
  "Skill Book (Darkness)": "2021",
  "Skill Book (Curse of Freeze)": "2022",
  "Skill Book (Field of Enervation)": "2023",
  "Skill Book (Field of Execration)": "2024",
  "Skill Book (Thrusting Arrow)": "2025",
  "Skill Book (Art of Healing)": "2026",
  "Skill Book (Art of Sniping)": "2027",
  "Skill Book (Vitality Mastery Lv.1)": "2028",
  "Skill Book (Vitality Mastery Lv.2)": "2029",
  "Shineguard Greaves (WA)": "203",
  "Skill Book (Vitality Mastery Lv.3)": "2030",
  "Skill Book (Vitality Mastery Lv.4)": "2031",
  "Skill Book (Vitality Mastery Lv.5)": "2032",
  "Skill Book (Vitality Mastery Lv.6)": "2033",
  "Skill Book (Vitality Mastery Lv.7)": "2034",
  "Skill Book (Vitality Mastery Lv.8)": "2035",
  "Skill Book (Vitality Mastery Lv.9)": "2036",
  "Skill Book (Vitality Mastery Lv.10)": "2037",
  "Skill Book (Mana Mastery Lv.1)": "2038",
  "Skill Book (Mana Mastery Lv.2)": "2039",
  "Shineguard Greaves (FS)": "204",
  "Skill Book (Mana Mastery Lv.3)": "2040",
  "Skill Book (Mana Mastery Lv.4)": "2041",
  "Skill Book (Mana Mastery Lv.5)": "2042",
  "Skill Book (Mana Mastery Lv.6)": "2043",
  "Skill Book (Mana Mastery Lv.7)": "2044",
  "Skill Book (Mana Mastery Lv.8)": "2045",
  "Skill Book (Mana Mastery Lv.9)": "2046",
  "Skill Book (Mana Mastery Lv.10)": "2047",
  "Skill Book (Offensive Sense Lv.1)": "2048",
  "Skill Book (Offensive Sense Lv.2)": "2049",
  "Forcium Greaves (WA)": "205",
  "Skill Book (Offensive Sense Lv.3)": "2050",
  "Skill Book (Offensive Sense Lv.4)": "2051",
  "Skill Book (Offensive Sense Lv.5)": "2052",
  "Skill Book (Offensive Sense Lv.6)": "2053",
  "Skill Book (Offensive Sense Lv.7)": "2054",
  "Skill Book (Offensive Sense Lv.8)": "2055",
  "Skill Book (Defensive Sense Lv.1)": "2056",
  "Skill Book (Defensive Sense Lv.2)": "2057",
  "Skill Book (Defensive Sense Lv.3)": "2058",
  "Skill Book (Defensive Sense Lv.4)": "2059",
  "Forcium Greaves (FS)": "206",
  "Skill Book (Defensive Sense Lv.5)": "2060",
  "Skill Book (Defensive Sense Lv.6)": "2061",
  "Skill Book (Defensive Sense Lv.7)": "2062",
  "Skill Book (Defensive Sense Lv.8)": "2063",
  "Skill Book (Force Control Lv.1)": "2064",
  "Skill Book (Force Control Lv.2)": "2065",
  "Skill Book (Force Control Lv.3)": "2066",
  "Skill Book (Force Control Lv.4)": "2067",
  "Skill Book (Force Control Lv.5)": "2068",
  "Skill Book (Force Control Lv.6)": "2069",
  "Parasite Mushrooms": "207",
  "Skill Book (Force Control Lv.7)": "2070",
  "Skill Book (Force Control Lv.8)": "2071",
  "Skill Book (Reflex Lv.1)": "2072",
  "Skill Book (Reflex Lv.2)": "2073",
  "Skill Book (Reflex Lv.3)": "2074",
  "Skill Book (Reflex Lv.4)": "2075",
  "Skill Book (Reflex Lv.5)": "2076",
  "Skill Book (Reflex Lv.6)": "2077",
  "Skill Book (Reflex Lv.7)": "2078",
  "Skill Book (Reflex Lv.8)": "2079",
  "Not in use": "4094",
  "Skill Book (Reflex Lv.9)": "2080",
  "Skill Book (Sharp Eyes Lv.1)": "2081",
  "Skill Book (Sharp Eyes Lv.2)": "2082",
  "Skill Book (Sharp Eyes Lv.3)": "2083",
  "Skill Book (Sharp Eyes Lv.4)": "2084",
  "Skill Book (Sharp Eyes Lv.5)": "2085",
  "Skill Book (Sharp Eyes Lv.6)": "2086",
  "Skill Book (Sharp Eyes Lv.7)": "2087",
  "Skill Book (Sharp Eyes Lv.8)": "2088",
  "Skill Book (Sharp Eyes Lv.9)": "2089",
  "Absolute Armorboots": "209",
  "Skill Book (Eyes of Mind Lv.1)": "2090",
  "Skill Book (Eyes of Mind Lv.2)": "2091",
  "Skill Book (Eyes of Mind Lv.3)": "2092",
  "Skill Book (Sixth Sense Lv.1)": "2093",
  "Skill Book (Sixth Sense Lv.2)": "2094",
  "Skill Book (Sixth Sense Lv.3)": "2095",
  "Skill Book (Ruling Force Lv.1)": "2096",
  "Skill Book (Ruling Force Lv.2)": "2097",
  "Skill Book (Ruling Force Lv.3)": "2098",
  "Skill Book (Impact Control Lv.1)": "2099",
  "Pherystin Orb": "21",
  "Training Battleboots": "210",
  "Skill Book (Impact Control Lv.2)": "2100",
  "Skill Book (Impact Control Lv.3)": "2101",
  "Skill Book (Damage Absorb Lv.1)": "2102",
  "Skill Book (Damage Absorb Lv.2)": "2103",
  "Skill Book (Damage Absorb Lv.3)": "2104",
  "Skill Book (Combo Start)": "2105",
  "Skill Book (Astral Bike)": "2106",
  "Skill Book (Magic Arrow (FA))": "2107",
  "Skill Book (Terra Arrow (FA))": "2108",
  "Skill Book (Aqua Arrow (FA))": "2109",
  "Battle Boots": "211",
  "Skill Book (Wind Arrow (FA))": "2110",
  "Skill Book (Fire Arrow (FA))": "2111",
  "Skill Book (Freezing Arrow (FA))": "2112",
  "Skill Book (Lightning Arrow (FA))": "2113",
  "Skill Book (Magic Blast (FA))": "2114",
  "Skill Book (Stone Blast (FA))": "2115",
  "Skill Book (Aqua Blast (FA))": "2116",
  "Skill Book (Wind Blast (FA))": "2117",
  "Skill Book (Fire Blast (FA))": "2118",
  "Skill Book (Ice Blast (FA))": "2119",
  "Reinforced Battleboots": "212",
  "Skill Book (Lightning Blast (FA))": "2120",
  "Skill Book (Magic Lance (FA))": "2121",
  "Skill Book (Terra Lance (FA))": "2122",
  "Skill Book (Aqua Lance (FA))": "2123",
  "Skill Book (Wind Lance (FA))": "2124",
  "Skill Book (Fire Lance (FA))": "2125",
  "Skill Book (Freezing Lance (FA))": "2126",
  "Skill Book (Lightning Lance (FA))": "2127",
  "Skill Book (Magic Cannon (FA))": "2128",
  "Skill Book (Stone Cannon (FA))": "2129",
  "Silk Battleboots": "213",
  "Skill Book (Aqua Cannon (FA))": "2130",
  "Skill Book (Wind Cannon (FA))": "2131",
  "Skill Book (Fire Cannon (FA))": "2132",
  "Skill Book (Crystal Cannon (FA))": "2133",
  "Skill Book (Lightning Cannon (FA))": "2134",
  "Skill Book (Astral Board)": "2135",
  "Skill Book (Mighty Wish)": "2136",
  "Skill Book (Field of Provocation)": "2137",
  "[Costume] German Traditional Dress": "2138",
  "[Costume] Epaulet of German Traditional Dress": "2139",
  "Aramid Battleboots": "214",
  "Giftbox - [Costume] German Traditional Dress": "2140",
  "[Costume] Water Elemental - Chakram": "2141",
  "Giftbox - [Costume] Water Elemental - Chakram": "2142",
  "Plate of Honor (Lv. 1)": "2143",
  "Plate of Honor (Lv. 2)": "2144",
  "Plate of Honor (Lv. 3)": "2145",
  "Plate of Honor (Lv. 4)": "2146",
  "Plate of Soul": "2147",
  "Entrance Item of Lake in Dusk (DT)": "2148",
  "Entrance Item of Ruina Station (DT)": "2149",
  "Bluestin Battleboots": "215",
  "Entrance Item of Frozen Tower of Undead B1F (DT)": "2150",
  "Buff Potion Lv.1 (HP Up)": "2153",
  "Buff Potion Lv.1 (Attack Up)": "2154",
  "Buff Potion Lv.1 (Magic Attack Up)": "2155",
  "Buff Potion Lv.1 (Defense Up)": "2156",
  "Buff Potion Lv.2 (HP Up)": "2157",
  "Buff Potion Lv.2 (Attack Up)": "2158",
  "Buff Potion Lv.2 (Magic Attack Up)": "2159",
  "Titanium Battleboots": "216",
  "Buff Potion Lv.2 (Defense Up)": "2160",
  "Buff Potion Lv.3 (HP Up)": "2161",
  "Buff Potion Lv.3 (Attack Up)": "2162",
  "Buff Potion Lv.3 (Magic Attack Up)": "2163",
  "Buff Potion Lv.3 (Defense Up)": "2164",
  "Giftbox - [Costume] Sweet Candy - Orb/Crystal": "2165",
  "Giftbox - [Costume] Sweet Candy - Katana/Daikatana": "2166",
  "Giftbox - [Costume] Sweet Candy - Blade/Greatsword": "2167",
  "Giftbox - [Costume] Snow Ice - Orb/Crystal": "2168",
  "Giftbox - [Costume] Snow Ice - Katana/Daikatana": "2169",
  "Osmium Battleboots": "217",
  "Giftbox - [Costume] Snow Ice - Blade/Greatsword": "2170",
  "Giftbox - [Costume] Snow Star - Orb/Crystal": "2171",
  "Giftbox - [Costume] Snow Star - Katana/Daikatana": "2172",
  "Giftbox - [Costume] Snow Star - Blade/Greatsword": "2173",
  "Giftbox - [Costume] Water Elemental - Orb/Crystal": "2174",
  "Giftbox - [Costume] Water Elemental - Katana/Daikatana": "2175",
  "Giftbox - [Costume] Water Elemental - Blade/Great Sword": "2176",
  "STR Potion (beginner)": "2177",
  "Magic Potion (beginner)": "2178",
  "Celebrate CABAL Birthday": "2179",
  "Teragrace boots (FB)": "218",
  "Legendary Ring": "2180",
  "Steamer Crazy Entry Item": "2181",
  "Giftbox - [Costume] Korean Gala Dress Epaulet (30 days)": "2182",
  "Giftbox - [Costume] Korean Gala Dress Epaulet (90 days)": "2183",
  "Giftbox - Astral Board Card - K Silver": "2184",
  "Giftbox - Astral Board Card - K Black": "2185",
  "Giftbox - Astral Board Card - K Gold": "2186",
  "Giftbox - Astral Board Card - K Red": "2187",
  "Giftbox - Astral Board Card - K Pink": "2188",
  "Giftbox - Astral Board Card - K Violet": "2189",
  "Teragrace boots (FA)": "219",
  "Giftbox - Astral Board Card - K Blue": "2190",
  "Giftbox - Astral Board Card - K Sky": "2191",
  "Giftbox - Astral Board Card - K Green": "2192",
  "Giftbox - Astral Board Card - K White": "2193",
  "Blessing Bead - EXP (50%)": "2194",
  "Blessing Bead - EXP (100%)": "2195",
  "Blessing Bead - Increase Craft Request Slot (8)": "2196",
  "Blessing Bead - Alz Bomb Chance (100%)": "2197",
  "Giftbox - Santa's Sleigh": "2198",
  "Blessing Bead - EXP (80%)": "2199",
  "Aqua Orb": "22",
  "Forcium boots (FB)": "220",
  "Blessing Bead - AXP (40%)": "2200",
  "Blessing Bead - AXP (80%)": "2201",
  "Entrance Item of Illusion Castle - Underworld": "2202",
  "The Mergaheph's ring": "2203",
  "Belt of Rapid": "2204",
  "Belt of Rapid +1": "2205",
  "Belt of Rapid +2": "2206",
  "Belt of Rapid +3": "2207",
  "Belt of Rapid +4": "2208",
  "Belt of Vital": "2209",
  "Forcium boots (FA)": "221",
  "Belt of Vital +1": "2210",
  "Belt of Vital +2": "2211",
  "Belt of Vital +3": "2212",
  "Belt of Vital +4": "2213",
  "Belt of Guard": "2214",
  "Belt of Guard +1": "2215",
  "Belt of Guard +2": "2216",
  "Belt of Guard +3": "2217",
  "Belt of Guard +4": "2218",
  "Belt of Prevent": "2219",
  "Belt of Prevent +1": "2220",
  "Belt of Prevent +2": "2221",
  "Belt of Prevent +3": "2222",
  "Belt of Prevent +4": "2223",
  "Belt of Damp": "2224",
  "Belt of Damp +1": "2225",
  "Belt of Damp +2": "2226",
  "Belt of Damp +3": "2227",
  "Belt of Damp +4": "2228",
  "Ring of Evil Spirit": "2229",
  "Lycanus's Orb": "2230",
  "Lycanus' Crystal": "2231",
  "Lycanus's Katana": "2232",
  "Lycanus's Blade": "2233",
  "Lycanus's Daikatana": "2234",
  "Lycanus's Great Sword": "2235",
  "[Costume] Midreth Armor Set": "2236",
  "[Costume] Epaulet of Midereth Armor": "2237",
  "[Costume] Pastur Battle Set": "2238",
  "[Costume] Epaulet of Pastur Battle": "2239",
  "Absolute Battleboots": "224",
  "[Costume] Huan Martial Set": "2240",
  "[Costume] Epaulet of Hual Martial": "2241",
  "Giftbox - [Costume] Midreth Armor Set": "2242",
  "Giftbox - [Costume] Pastur Battle Set": "2243",
  "Giftbox - [Costume] Huan Martial Set": "2244",
  "Pet - Cute Rudolph": "2245",
  "Blessing Bead - Platinum Wing": "2246",
  "Blessing Bead - Silver Wing (Academy Kit)": "2247",
  "Blessing Bead - Silver Wing (Craftsman Kit)": "2248",
  "Blessing Bead - Silver Wing (Adventurer Kit)": "2249",
  "Training Martialboots": "225",
  "Blessing Bead - CABAL Leader's Choice": "2250",
  "Catacomb Frost Entry Item": "2254",
  "Illusion Castle - Radiant Hall Entry Item": "2255",
  "Drosnin's Earrings": "2256",
  "[Costume] Brazil Football Uniform": "2257",
  "[Costume] Epaulet of Brazil Football Uniform": "2258",
  "[Costume] Kendo Suit": "2259",
  "Martial Boots": "226",
  "[Costume] Epaulet of Kendo Suit": "2260",
  "Giftbox - [Costume] Brazil Football Uniform": "2261",
  "Giftbox - [Costume] Kendo Suit": "2262",
  "[Costume] Shineguard Set": "2263",
  "[Costume] Epaulet of Shineguard": "2264",
  "[Costume] Teragrace Set": "2265",
  "[Costume] Epaulet of Teragrace": "2266",
  "[Costume] Mystic Set": "2267",
  "[Costume] Epaulet of Mystic": "2268",
  "[Costume] SIGMetal Armor Set": "2269",
  "Reinforced Martialboots": "227",
  "[Costume] Epaulet of SIGMetal Armor": "2270",
  "[Costume] SIGMetal Battle Set": "2271",
  "[Costume] Epaulet of SIGMetal Battle": "2272",
  "[Costume] SIGMetal Martial Set": "2273",
  "[Costume] Epaulet of SIGMetal Martial": "2274",
  "[Costume] Mithril Armor Set": "2275",
  "[Costume] Epaulet of Mithril Armor": "2276",
  "[Costume] Mithril Battle Set": "2277",
  "[Costume] Epaulet of Mithril Battle": "2278",
  "[Costume] Mithril Martial Set": "2279",
  "Silk Martialboots": "228",
  "[Costume] Epaulet of Mithril Martial": "2280",
  "Giftbox - [Costume] Shineguard Set": "2281",
  "Giftbox - [Costume] Teragrace Set": "2282",
  "Giftbox - [Costume] Mystic Set": "2283",
  "Giftbox - [Costume] SIGMetal Armor Set": "2284",
  "Giftbox - [Costume] SIGMetal Battle Set": "2285",
  "Giftbox - [Costume] SIGMetal Martial Set": "2286",
  "Giftbox - [Costume] Mithril Armor Set": "2287",
  "Giftbox - [Costume] Mithril Battle Set": "2288",
  "Giftbox - [Costume] Mithril Martial Set": "2289",
  "Aramid Martialboots": "229",
  "[Costume] Midreth Armor Helmet": "2290",
  "[Costume] Pastur Battle Helmet": "2291",
  "[Costume] Huan Martial Helmet": "2292",
  "[Costume] Shineguard Visor": "2293",
  "[Costume] Teragrace Headgear": "2294",
  "[Costume] Mystic Headpiece": "2295",
  "[Costume] SIGMetal Visor": "2296",
  "[Costume] SIGMetal Headgear": "2297",
  "[Costume] SIGMetal Headpiece": "2298",
  "[Costume] Mithril Visor": "2299",
  "Lapis Orb": "23",
  "Bluestin Martialboots": "230",
  "[Costume] Mithril Headgear": "2300",
  "[Costume] Mithril Headpiece": "2301",
  "Change Kit (Face) - Extreme Type A": "2302",
  "Change Kit (Face) - Extreme Type B": "2303",
  "Change Kit (Face) - Festival Type A": "2304",
  "Change Kit (Face) - Festival Type B": "2305",
  "Change Kit (Hair Style) - Oriental Type A": "2306",
  "Change Kit (Hair Style) - Fever Type A": "2307",
  "Change Kit (Hair Style) - Oriental Type B": "2308",
  "Change Kit (Hair Style) - Fever Type B": "2309",
  "Titanium Martialboots": "231",
  "Blessing Bead - Skill EXP (200%)": "2310",
  "Skill Book (Sword Quake)": "2311",
  "Skill Book (Death Tempest)": "2312",
  "Skill Book (Shield Grenade)": "2313",
  "Skill Book (Seal of Damnation)": "2314",
  "Skill Book (Meteorite)": "2315",
  "Skill Book (Sonic Shooter)": "2316",
  "Agent Shop Coupon": "2317",
  "Megaphone": "2318",
  "Blessing Bead - Plus (JP Exclusive)": "2319",
  "Osmium Martialboots": "232",
  "Grandma Yerte's Jade Earrings": "2320",
  "Grandma Yerte's Enamel Bracelet": "2321",
  "Grandma Yerte's Jewelry Box": "2322",
  "Key of Chaos Lv. 1": "2323",
  "Key of Chaos Lv. 2": "2324",
  "Key of Chaos Lv. 3": "2325",
  "Key of Chaos Lv. 4": "2326",
  "Key of Chaos Lv. 5": "2327",
  "Key of Chaos Lv. 6": "2328",
  "Wonder Box - SIGMetal Orb": "2329",
  "Mystic Shoes (BL)": "233",
  "Wonder Box - SIGMetal Crystal": "2330",
  "Wonder Box - SIGMetal Katana": "2331",
  "Wonder Box - SIGMetal Blade": "2332",
  "Wonder Box - SIGMetal Daikatana": "2333",
  "Wonder Box - SIGMetal Greatsword": "2334",
  "Remote Warehouse Card": "2335",
  "Plate of Glory": "2336",
  "Frontier Stone": "2337",
  "Holy Water of Vitality": "2338",
  "Holy Water of Speed": "2339",
  "Mystic Shoes (WI)": "234",
  "Holy Water of Traveler": "2340",
  "Holy Water of Critical Strike": "2341",
  "Holy Water of Fighter": "2342",
  "Holy Water of Sage": "2343",
  "Holy Water of Resistance": "2344",
  "Holy Water of Flawless Defense": "2345",
  "Pet Slot Extender": "2346",
  "Costume Hanger": "2347",
  "Skill Book (Offensive Bless )": "2348",
  "Skill Book (Curse Remove)": "2349",
  "Forcium Shoes (BL)": "235",
  "Skill Book (Curse Dodge)": "2350",
  "Skill Book (Fatality Increase)": "2351",
  "Chaos Box - Extender (Highest)": "2352",
  "Summon of Heroes": "2353",
  "Honorable Circle": "2354",
  "Oath of Sacrifice": "2355",
  "Oath of Protection": "2356",
  "Yekaterina Gold Membership": "2357",
  "[Costume] Halloween Horror - Orb/Crystal": "2358",
  "[Costume] Halloween Horror - Katana/Daikatana": "2359",
  "Forcium Shoes (WI)": "236",
  "[Costume] Halloween Horror - Blade/Great Sword": "2360",
  "Giftbox - [Costume] Halloween Horror - Orb/Crystal": "2361",
  "Giftbox - [Costume] Halloween Horror - Katana/Daikatana": "2362",
  "Giftbox - [Costume] Halloween Horror - Blade/Great Sword": "2363",
  "CABAL Gift Voucher (Bronze)": "2464",
  "CABAL Gift Voucher (Silver)": "2465",
  "CABAL Gift Voucher (Gold)": "2466",
  "Panic Cave Premium Entry Item": "2367",
  "Steamer Crazy Premium Entry Item": "2368",
  "Catacomb Frost Premium Entry Item": "2369",
  "Blessing Bead - W.Tower of Undead B1F": "2370",
  "Astral Board Card - K Red Crystal Edition": "2371",
  "Giftbox - Astral Board Card - K Red Crystal Edition": "2372",
  "Max Critical Rate Up": "2373",
  "Critical DMG": "2374",
  "Special Badge": "2375",
  "SIGMetal Weapon Coupon": "2376",
  "Premium Shop Sign": "2377",
  "Enchant Safeguard (Low)": "2378",
  "Enchant Safeguard (Medium)": "2379",
  "Enchant Safeguard (High)": "2380",
  "Enchant Safeguard (Highest)": "2381",
  "Aura Transmuter": "2382",
  "Japanese Chaos Lamp": "2383",
  "Chaos Infinity Entry Item": "2384",
  "Shineguard Cube (WA)": "2385",
  "SIGMetal Cube (WA)": "2386",
  "Forcium Cube (WA)": "2387",
  "Mystic Cube (BL)": "2388",
  "SIGMetal Cube (BL)": "2389",
  "Absolute Martialboots": "239",
  "Forcium Cube (BL)": "2390",
  "Mystic Cube (WI)": "2391",
  "SIGMetal Cube (WI)": "2392",
  "Forcium Cube (WI)": "2393",
  "Teragrace Cube (FA)": "2394",
  "SIGMetal Cube (FA)": "2395",
  "Forcium Cube (FA)": "2396",
  "Shineguard Cube (FS)": "2397",
  "SIGMetal Cube (FS)": "2398",
  "Forcium Cube (FS)": "2399",
  "Topaz Orb": "24",
  "Training Armorhelm": "240",
  "Teragrace Cube (FB)": "2400",
  "SIGMetal Cube (FB)": "2401",
  "Forcium Cube (FB)": "2402",
  "Officer's support Cube (WExp)": "2403",
  "Protection of Veradrix": "2404",
  "Not in Use": "33557815",
  "Blessing Bead - EXP (150%)": "2407",
  "Blessing Bead - EXP (200%)": "2408",
  "Blessing Bead - Skill EXP (150%)": "2409",
  "Armor Helmets": "241",
  "Blessing Bead - WEXP (75%)": "2410",
  "Blessing Bead - WEXP (100%)": "2411",
  "Blessing Bead - AXP (100%)": "2412",
  "Blessing Bead - AXP (200%)": "2413",
  "Blessing Bead - Pet EXP (200%)": "2414",
  "Blessing Bead - Pet EXP (300%)": "2415",
  "Concentration Potion (Lv. 1)": "2416",
  "Concentration Potion (Lv. 2)": "2417",
  "Concentration Potion (Lv. 3)": "2418",
  "Concentration Potion (Lv. 4)": "2419",
  "Reinforced Armorhelm": "242",
  "Reflex Potion (Lv. 1)": "658",
  "Reflex Potion (Lv. 2)": "849",
  "Reflex Potion (Lv. 3)": "861",
  "Reflex Potion (Lv. 4)": "2423",
  "Vital Potion (Lv. 1)": "659",
  "Vital Potion (Lv. 2)": "850",
  "Vital Potion (Lv. 3)": "862",
  "Vital Potion (Lv. 4)": "2427",
  "Sharpness Potion (Lv. 1)": "660",
  "Sharpness Potion (Lv. 2)": "851",
  "Iron Armorhelm": "243",
  "Sharpness Potion (Lv. 3)": "863",
  "Sharpness Potion (Lv. 4)": "2431",
  "Mana Condense Potion (Lv. 1)": "661",
  "Mana Condense Potion (Lv. 2)": "852",
  "Mana Condense Potion (Lv. 3)": "864",
  "Mana Condense Potion (Lv. 4)": "2435",
  "Vital Regen Potion (Lv. 1)": "2436",
  "Vital Regen Potion (Lv. 2)": "2437",
  "Vital Regen Potion (Lv. 3)": "2438",
  "Vital Regen Potion (Lv. 4)": "2439",
  "Shadowsteel Armorhelm": "244",
  "Hardness Potion (Lv. 1)": "664",
  "Hardness Potion (Lv. 2)": "855",
  "Hardness Potion (Lv. 3)": "867",
  "Hardness Potion (Lv. 4)": "2443",
  "Strike Potion (Lv. 1)": "665",
  "Strike Potion (Lv. 2)": "856",
  "Strike Potion (Lv. 3)": "868",
  "Strike Potion (Lv. 4)": "2447",
  "Sword Master Potion (Lv. 1)": "2448",
  "Sword Master Potion (Lv. 2)": "2449",
  "Bluestin Armorhelm": "245",
  "Sword Master Potion (Lv. 3)": "2450",
  "Sword Master Potion (Lv. 4)": "2451",
  "Magic Master Potion (Lv. 1)": "2452",
  "Magic Master Potion (Lv. 2)": "2453",
  "Magic Master Potion (Lv. 3)": "2454",
  "Magic Master Potion (Lv. 4)": "2455",
  "Evasion Potion (Lv. 1)": "2456",
  "Evasion Potion (Lv. 2)": "2457",
  "Evasion Potion (Lv. 3)": "2458",
  "Evasion Potion (Lv. 4)": "2459",
  "Titanium Armorhelm": "246",
  "Goodluck Potion (Lv. 1)": "2460",
  "Goodluck Potion (Lv. 2)": "2461",
  "Goodluck Potion (Lv. 3)": "2462",
  "Goodluck Potion (Lv. 4)": "2463",
  "Gem of Covenant (Unique)": "2467",
  "Gem of Summons (Normal)": "2468",
  "Gem of Summons (Rare)": "2469",
  "Osmium Armorhelm": "247",
  "Gem of Summons (Epic)": "2470",
  "Gem of Summons (Unique)": "2471",
  "Gem of Dazzlement (Normal)": "2472",
  "Gem of Dazzlement (Rare)": "2473",
  "Gem of Dazzlement (Epic)": "2474",
  "Gem of Dazzlement (Unique)": "2475",
  "Corrupted Warrior (Normal)": "2476",
  "Corrupted Warrior (Rare)": "2477",
  "Corrupted Warrior (Epic)": "2478",
  "Corrupted Warrior (Unique)": "2479",
  "Shineguard Visor (WA)": "248",
  "Corrupted Blader (Normal)": "2480",
  "Corrupted Blader (Rare)": "2481",
  "Corrupted Blader (Epic)": "2482",
  "Corrupted Blader (Unique)": "2483",
  "Corrupted Wizard (Normal)": "2484",
  "Corrupted Wizard (Rare)": "2485",
  "Corrupted Wizard (Epic)": "2486",
  "Corrupted Wizard (Unique)": "2487",
  "Corrupted Force Archer (Normal)": "2488",
  "Corrupted Force Archer (Rare)": "2489",
  "Shineguard Visor (FS)": "249",
  "Corrupted Force Archer (Epic)": "2490",
  "Corrupted Force Archer (Unique)": "2491",
  "Corrupted Force Shielder (Normal)": "2492",
  "Corrupted Force Shielder (Rare)": "2493",
  "Corrupted Force Shielder (Epic)": "2494",
  "Corrupted Force Shielder (Unique)": "2495",
  "Corrupted Force Blader (Normal)": "2496",
  "Corrupted Force Blader (Rare)": "2497",
  "Corrupted Force Blader (Epic)": "2498",
  "Corrupted Force Blader (Unique)": "2499",
  "Forcium Orb": "25",
  "Forcium Visor (WA)": "250",
  "Yuan (Normal)": "2500",
  "Yuan (Rare)": "2501",
  "Yuan (Epic)": "2502",
  "Yuan (Unique)": "2503",
  "Arionell (Normal)": "2504",
  "Arionell (Rare)": "2505",
  "Arionell (Epic)": "2506",
  "Arionell (Unique)": "2507",
  "Rin (Normal)": "2508",
  "Rin (Rare)": "2509",
  "Forcium Visor (FS)": "251",
  "Rin (Epic)": "2510",
  "Rin (Unique)": "2511",
  "Freed (Normal)": "2512",
  "Freed (Rare)": "2513",
  "Freed (Epic)": "2514",
  "Freed (Unique)": "2515",
  "Black Bard (Normal)": "2516",
  "Black Bard (Rare)": "2517",
  "Black Bard (Epic)": "2518",
  "Black Bard (Unique)": "2519",
  "Pertz von Zatellerean (Normal)": "2520",
  "Pertz von Zatellerean (Rare)": "2521",
  "Pertz von Zatellerean (Epic)": "2522",
  "Pertz von Zatellerean (Unique)": "2523",
  "Perzen Bhha (Normal)": "2524",
  "Perzen Bhha (Rare)": "2525",
  "Perzen Bhha (Epic)": "2526",
  "Perzen Bhha (Unique)": "2527",
  "Bricry (Normal)": "2528",
  "Bricry (Rare)": "2529",
  "Bricry (Epic)": "2530",
  "Bricry (Unique)": "2531",
  "Event - Yuan (Normal)": "2532",
  "Event - Yuan (Rare)": "2533",
  "Event - Yuan (Epic)": "2534",
  "Event - Yuan (Unique)": "2535",
  "Event - Arionell (Normal)": "2536",
  "Event - Arionell(Rare)": "2537",
  "Event - Arionell (Epic)": "2538",
  "Event - Arionell (Unique)": "2539",
  "Absolute Armorhelm": "254",
  "Event - Rin (Normal)": "2540",
  "Event - Rin (Rare)": "2541",
  "Event - Rin (Epic)": "2542",
  "Event - Rin (Unique)": "2543",
  "Event - Freed (Normal)": "2544",
  "Event - Freed (Rare)": "2545",
  "Event - Freed (Epic)": "2546",
  "Event - Freed (Unique)": "2547",
  "Event - Black Bard (Normal)": "2548",
  "Event - Black Bard (Rare)": "2549",
  "Training Battlehelm": "255",
  "Event - Black Bard (Epic)": "2550",
  "Event - Black Bard (Unique)": "2551",
  "Maga Eyagre (Normal)": "2552",
  "Maga Eyagre (Rare)": "2553",
  "Maga Eyagre (Epic)": "2554",
  "Maga Eyagre (Unique)": "2555",
  "Ratzel of Reason and Wind (Normal)": "2556",
  "Ratzel of Reason and Wind (Rare)": "2557",
  "Ratzel of Reason and Wind (Epic)": "2558",
  "Ratzel of Reason and Wind (Unique)": "2559",
  "Battle Helmets": "256",
  "Giftbox - [Costume] Epaulet of Charisma Pan": "2560",
  "Giftbox - [Costume] Epaulet of Lovely Sweety": "2561",
  "Giftbox - [Costume] Epaulet of Mystic Blade (30 days)": "2562",
  "Giftbox - [Costume] Epaulet of Mystic Blade": "2563",
  "Chaos Box - Avatar (GLB Exclusive)": "2564",
  "Lava Hellfire Entry Item": "2565",
  "Lava Hellfire Premium Entry Item": "2566",
  "Hazardous Valley Entry Item": "2567",
  "Leth Tyrant's Ring": "2568",
  "Ring of Evil Spirit 2": "2569",
  "Reinforced Battlehelm": "257",
  "[Costume] Nevareth Pirate": "2570",
  "[Costume] Epaulet of Nevareth Pirate": "2571",
  "Giftbox - [Costume] Nevareth Pirate": "2572",
  "[Costume] Nevareth Pirate Tricorn Hat": "2573",
  "[Costume] Tricorn Hat of Nevareth Pirate": "2574",
  "[Costume] Navy Officer": "2575",
  "[Costume] Epaulet of Naval Officer": "2576",
  "Giftbox - [Costume] Naval Officer": "2577",
  "[Costume] Navy Cap": "2578",
  "[Costume] Cap of Naval Officer": "2579",
  "Silk Battlehelm": "258",
  "Bead Box (Bronze)": "2580",
  "Potion Box (Bronze)": "2581",
  "Skill Book Box (Bronze)": "2582",
  "Capsule Box (Silver)": "2583",
  "Entry Box (Silver)": "2584",
  "Potion Box (Silver)": "2585",
  "Skill Book Box (Silver)": "2586",
  "Capsule Box (Gold)": "2587",
  "Entry Box (Gold)": "2588",
  "Essence Rune Box (Gold)": "2589",
  "Aramid Battlehelm": "259",
  "Unknown Box (Gold)": "2590",
  "Blessing Bead - Plus (GLB Exclusive)": "2591",
  "Single Snowman (Normal)": "2592",
  "Single Snowman (Rare)": "2593",
  "Single Snowman (Epic)": "2594",
  "Single Snowman (Unique)": "2595",
  "Couple Snowman (Normal)": "2596",
  "Couple Snowman (Rare)": "2597",
  "Couple Snowman (Epic)": "2598",
  "Couple Snowman (Unique)": "2599",
  "Master's Bluestin Orb": "26",
  "Bluestin Battlehelm": "260",
  "[Costume] Panda's Warm Epaulet": "2600",
  "Blessing Bead - AXP UP (10%)": "2601",
  "Blessing Bead - AXP UP (50%)": "2602",
  "Blessing Bead - AXP UP (75%)": "2603",
  "Giftbox - [Costume] Pumpkin Helmet": "2604",
  "Giftbox - [Costume] Antler + Red Nose": "2605",
  "Giftbox - [Costume] Antler": "2606",
  "Giftbox - [Costume] Red Nose": "2607",
  "Giftbox - [Costume] Winter Cap": "2608",
  "Giftbox - [Costume] Earmuff": "2609",
  "Titanium Battlehelm": "261",
  "Giftbox - [Costume] X Mask": "2610",
  "Giftbox - [Costume] Skeleton Mask": "2611",
  "Giftbox - [Costume] Santa Hood": "2612",
  "Giftbox - [Costume] Cutie Santa Hood": "2613",
  "Giftbox - [Costume] Japanese Gala Headdress": "2614",
  "Giftbox - [Costume] Korean Gala Headdress": "2615",
  "Giftbox - [Costume] Goguryeo Headdress": "2616",
  "Giftbox - [Costume] Sandy Mask": "2617",
  "Giftbox - [Costume] Happy Birthday Hat": "2618",
  "Giftbox - [Costume] CABAL Birthday Hood": "2619",
  "Osmium Battlehelm": "262",
  "Giftbox - [Costume] Dancing Temptation Mask": "2620",
  "Giftbox - [Costume] Wild Western Hat": "2621",
  "Giftbox - [Costume] Midreth Armor Helmet": "2622",
  "Giftbox - [Costume] Pastur Battle Helmet": "2623",
  "Giftbox - [Costume] Huan Martial Helmet": "2624",
  "Giftbox - [Costume] Shineguard Visor": "2625",
  "Giftbox - [Costume] Teragrace Headgear": "2626",
  "Giftbox - [Costume] Mystic Headpiece": "2627",
  "Giftbox - [Costume] SIGMetal Visor": "2628",
  "Giftbox - [Costume] SIGMetal Headgear": "2629",
  "Teragrace Headgear (FB)": "263",
  "Giftbox - [Costume] SIGMetal Headpiece": "2630",
  "Giftbox - [Costume] Mithril Visor": "2631",
  "Giftbox - [Costume] Mithril Headgear": "2632",
  "Giftbox - [Costume] Mithril Headpiece": "2633",
  "Giftbox - [Costume] Nevareth Pirate Tricorn Hat": "2634",
  "Giftbox - [Costume] Navy Cap": "2635",
  "Maximum Level Rabbit (Normal)": "2636",
  "Maximum Level Rabbit (Rare)": "2637",
  "Maximum Level Rabbit (Epic)": "2638",
  "Maximum Level Rabbit (Unique)": "2639",
  "Teragrace Headgear (FA)": "264",
  "Mixture (Lv. 3)": "2640",
  "Mixture (Lv. 4)": "2641",
  "Mixture (Lv. 5)": "2642",
  "Mixture (Lv. 6)": "2643",
  "Sword Damage Amplifier (Lv. 3)": "2644",
  "Sword Damage Amplifier (Lv. 4)": "2645",
  "Sword Damage Amplifier (Lv. 5)": "2646",
  "Sword Damage Amplifier (Lv. 6)": "2647",
  "Magic Damage Amplifier (Lv. 3)": "2648",
  "Magic Damage Amplifier (Lv. 4)": "2649",
  "Forcium Headgear (FB)": "265",
  "Magic Damage Amplifier (Lv. 5)": "2650",
  "Magic Damage Amplifier (Lv. 6)": "2651",
  "Bike Upgrade Kit (Low)": "2652",
  "Bike Upgrade Kit (Medium)": "2653",
  "Bike Upgrade Kit (High)": "2654",
  "Bike Upgrade Kit (Highest)": "2655",
  "Bike Perfect Upgrade Kit (Low)": "2656",
  "Bike Perfect Upgrade Kit (Medium)": "2657",
  "Bike Perfect Upgrade Kit (High)": "2658",
  "Bike Perfect Upgrade Kit (Highest)": "2659",
  "Forcium Headgear (FA)": "266",
  "Bike Force Kit (Low)": "2660",
  "Bike Force Kit (Medium)": "2661",
  "Bike Force Kit (High)": "2662",
  "Bike Force Kit (Highest)": "2663",
  "Bike Slot Extender (Low)": "2664",
  "Bike Slot Converter (Lv. 2)": "2665",
  "Bike Slot Converter (Lv. 3)": "2666",
  "Bike Slot Extender (Highest)": "2667",
  "Bike Kit Enhancer (Low)": "2668",
  "Bike Kit Enhancer (Medium)": "2669",
  "Bike Kit Enhancer (High)": "2670",
  "Bike Kit Enhancer (Highest)": "2671",
  "Bike Coating Kit (Dark Red)": "2672",
  "Bike Coating Kit (Dark Blue)": "2673",
  "Bike Coating Kit (Burnt Yellow)": "2674",
  "Bike Coating Kit (Dark Viridian)": "2675",
  "Bike Coating Kit (Iron Yellow)": "2676",
  "Bike Coating Kit (Perm. Dark Red)": "2677",
  "Bike Coating Kit (Dark Ultra Marine)": "2678",
  "Giant SnakeS": "2679",
  "CorpseN": "2680",
  "CoffinN": "2681",
  "FungusN": "2682",
  "Red FungusN": "2683",
  "KistS": "2684",
  "Cannibal PlantN": "2685",
  "PumpkinS": "2686",
  "SignN": "2687",
  "TelescopeS": "2688",
  "HeartS": "2689",
  "Absolute Battlehelm": "269",
  "KeyS": "2690",
  "Circuit PartS": "2691",
  "BoomerangS": "2692",
  "ClawS": "2693",
  "MirrorN": "2694",
  "BreadN": "2695",
  "NetN": "2696",
  "ArmbandN": "2697",
  "A piece of ArmbandN": "2698",
  "Blue LiquidN": "2699",
  "Master's Pherystin Orb": "27",
  "Training Martialhelm": "270",
  "HoneyN": "2700",
  "Red BottleS": "2701",
  "Weapon Option Scroll (Low)": "2702",
  "Weapon Option Scroll (Medium)": "2703",
  "Weapon Option Scroll (High)": "2704",
  "Weapon Option Scroll (Highest)": "2705",
  "Armor Option Scroll (Low)": "2706",
  "Armor Option Scroll (Medium)": "2707",
  "Armor Option Scroll (High)": "2708",
  "Armor Option Scroll (Highest)": "2709",
  "Martial Helmets": "271",
  "Drei Frame Plate (WA)": "2710",
  "Drei Frame Plate (FS)": "2711",
  "Drei Frame Coat (FB)": "2712",
  "Drei Frame Coat (FA)": "2713",
  "Drei Frame Suit (BL)": "2714",
  "Drei Frame Suit (WI)": "2715",
  "Drei Frame Gauntlet (WA)": "2716",
  "Drei Frame Gauntlet (FS)": "2717",
  "Drei Frame Gloves (FB)": "2718",
  "Drei Frame Gloves (FA)": "2719",
  "Reinforced Martialhelm": "272",
  "Drei Frame Hands (BL)": "2720",
  "Drei Frame Hands (WI)": "2721",
  "Drei Frame Greaves (WA)": "2722",
  "Drei Frame Greaves (FS)": "2723",
  "Drei Frame Boots (FB)": "2724",
  "Drei Frame Boots (FA)": "2725",
  "Drei Frame Shoes (BL)": "2726",
  "Drei Frame Shoes (WI)": "2727",
  "Drei Frame Visor (WA)": "2728",
  "Drei Frame Visor (FS)": "2729",
  "Silk Martialhelm": "273",
  "Drei Frame Headgear (FB)": "2730",
  "Drei Frame Headgear (FA)": "2731",
  "Drei Frame Headpiece (BL)": "2732",
  "Drei Frame Headpiece (WI)": "2733",
  "Drei Frame Epaulet of Laws": "2734",
  "Drei Frame Epaulet of Fighter": "2735",
  "Drei Frame Epaulet of Sage": "2736",
  "Upgrade Core Cube": "2737",
  "Bluestin Plate (WA)": "2738",
  "Bluestin Plate (FS)": "2739",
  "Aramid Martialhelm": "274",
  "Bluestin Coat (FB)": "2740",
  "Bluestin Coat (FA)": "2741",
  "Bluestin Suit (BL)": "2742",
  "Bluestin Suit (WI)": "2743",
  "Bluestin Gauntlet (WA)": "2744",
  "Bluestin Gauntlet (FS)": "2745",
  "Bluestin Gloves (FB)": "2746",
  "Bluestin Gloves (FA)": "2747",
  "Bluestin Hands (BL)": "2748",
  "Bluestin Hands (WI)": "2749",
  "Bluestin Martialhelm": "275",
  "Bluestin Greaves (WA)": "2750",
  "Bluestin Greaves (FS)": "2751",
  "Bluestin Boots (FB)": "2752",
  "Bluestin Boots (FA)": "2753",
  "Bluestin Shoes (BL)": "2754",
  "Bluestin Shoes (WI)": "2755",
  "Bluestin Visor (WA)": "2756",
  "Bluestin Visor (FS)": "2757",
  "Bluestin Headgear (FB)": "2758",
  "Bluestin Headgear (FA)": "2759",
  "Titanium Martialhelm": "276",
  "Bluestin Headpiece (BL)": "2760",
  "Bluestin Headpiece (WI)": "2761",
  "Titanium Plate (WA)": "2762",
  "Titanium Plate (FS)": "2763",
  "Titanium Coat (FB)": "2764",
  "Titanium Coat (FA)": "2765",
  "Titanium Suit (BL)": "2766",
  "Titanium Suit (WI)": "2767",
  "Titanium Gauntlet (WA)": "2768",
  "Titanium Gauntlet (FS)": "2769",
  "Osmium Martialhelm": "277",
  "Titanium Gloves (FB)": "2770",
  "Titanium Gloves (FA)": "2771",
  "Titanium Hands (BL)": "2772",
  "Titanium Hands (WI)": "2773",
  "Titanium Greaves (WA)": "2774",
  "Titanium Greaves (FS)": "2775",
  "Titanium Boots (FB)": "2776",
  "Titanium Boots (FA)": "2777",
  "Titanium Shoes (BL)": "2778",
  "Titanium Shoes (WI)": "2779",
  "Mystic Headpiece (BL)": "278",
  "Titanium Visor (WA)": "2780",
  "Titanium Visor (FS)": "2781",
  "Titanium Headgear (FB)": "2782",
  "Titanium Headgear (FA)": "2783",
  "Titanium Headpiece (BL)": "2784",
  "Titanium Headpiece (WI)": "2785",
  "Osmium Plate (WA)": "2786",
  "Osmium Plate (FS)": "2787",
  "Osmium Coat (FB)": "2788",
  "Osmium Coat (FA)": "2789",
  "Mystic Headpiece (WI)": "279",
  "Osmium Suit (BL)": "2790",
  "Osmium Suit (WI)": "2791",
  "Osmium Gauntlet (WA)": "2792",
  "Osmium Gauntlet (FS)": "2793",
  "Osmium Gloves (FB)": "2794",
  "Osmium Gloves (FA)": "2795",
  "Osmium Hands (BL)": "2796",
  "Osmium Hands (WI)": "2797",
  "Osmium Greaves (WA)": "2798",
  "Osmium Greaves (FS)": "2799",
  "Master's Aqua Orb": "28",
  "Forcium Headpiece (BL)": "280",
  "Osmium Boots (FB)": "2800",
  "Osmium Boots (FA)": "2801",
  "Osmium Shoes (BL)": "2802",
  "Osmium Shoes (WI)": "2803",
  "Osmium Visor (WA)": "2804",
  "Osmium Visor (FS)": "2805",
  "Osmium Headgear (FB)": "2806",
  "Osmium Headgear (FA)": "2807",
  "Osmium Headpiece (BL)": "2808",
  "Osmium Headpiece (WI)": "2809",
  "Forcium Headpiece (WI)": "281",
  "Quartz Core (SIGMetal)": "2810",
  "Material Core (SIGMetal)": "2811",
  "Quartz Core (Epaulet)": "2812",
  "Material Core (Bike)": "2813",
  "Hunting KitN": "2814",
  "StakeN": "2815",
  "FedoraS": "2816",
  "HairS": "2817",
  "SirenN": "2818",
  "Invisible FabricS": "2819",
  "SackN": "2820",
  "FurS": "2821",
  "Highzard's Red HandS": "2822",
  "Bugzard's Red HandS": "2823",
  "Troglo's ClubS": "2824",
  "Mummy BladeS": "2825",
  "Phantom Walker's DaggerS": "2826",
  "Shade's SwordS": "2827",
  "Zombie's HammerS": "2828",
  "Hound TailS": "2829",
  "Golem HeadS": "2830",
  "Frog LegS": "2831",
  "SeafoodN": "2832",
  "Red ShellS": "2833",
  "Black PowderS": "2834",
  "Green BottleN": "2835",
  "FanN": "2836",
  "GatewayN": "2837",
  "CompassN": "2838",
  "Destruction Upgrade Potion": "2839",
  "Absolute Martialhelm": "284",
  "DP Cube of Enchant": "2840",
  "DP Cube of Growth": "2841",
  "Potion of Luck": "2842",
  "Potion of Experience": "2843",
  "Potion of Skill": "2844",
  "Potion of Training": "2845",
  "Potion of Solidarity": "2846",
  "Potion of Honor": "2847",
  "Potion of Soul": "2848",
  "Potion of Courage": "2849",
  "Ring of Fighter": "285",
  "Outpost Entry ItemN": "2850",
  "Outpost Capella TabletN": "2851",
  "Outpost Procyon TabletN": "2852",
  "Outpost General TabletN": "2853",
  "Cube of Experience (Low)": "2854",
  "Cube of Experience (Medium)": "2855",
  "Cube of Experience (High)": "2856",
  "Cube of Experience (Highest)": "2857",
  "Cube of Soul (Low)": "2858",
  "Cube of Soul (Medium)": "2859",
  "Ring of Fighter +1": "286",
  "Cube of Soul (High)": "2860",
  "Cube of Soul (Highest)": "2861",
  "Cube of Enchant (Low)": "2862",
  "Cube of Enchant (Medium)": "2863",
  "Cube of Enchant (High)": "2864",
  "Cube of Enchant (Highest)": "2865",
  "Mander FigureS": "2866",
  "Giant SnakeN": "2867",
  "Green BraceletS": "2868",
  "Jewel1S": "2869",
  "Ring of Fighter +2": "287",
  "StingS": "2870",
  "Japanese Ticket Circuit SilverS": "2871",
  "Japanese Ticket Circuit BronzeS": "2872",
  "spear1S": "2873",
  "eyeballS": "2874",
  "B.I. CertificateS": "2875",
  "D.S. CertificateS": "2876",
  "G.D. CertificateS": "2877",
  "Bluestin Class Attribute Card (WA/FB/BL)": "2878",
  "Titanium Class Attribute Card (WA/FB/BL)": "2879",
  "Ring of Fighter +3": "288",
  "Osmium Class Attribute Card (WA/FB/BL)": "2880",
  "Bluestin Class Attribute Card (FS/FA/WI)": "2881",
  "Titanium Class Attribute Card (FS/FA/WI)": "2882",
  "Osmium Class Attribute Card (FS/FA/WI)": "2883",
  "Event_Graduation Cap (Black)": "2884",
  "Event_Graduation Cap (Gold)": "2885",
  "Event_Graduation Cap (Silver)": "2886",
  "Event_Number 0": "2887",
  "Event_Number 1": "2888",
  "Event_Number 2": "2889",
  "Ring of Fighter +4": "289",
  "Event_Number 3": "2890",
  "Event_Number 4": "2891",
  "Event_Number 5": "2892",
  "Event_Number 6": "2893",
  "Event_Number 7": "2894",
  "Event_Number 8": "2895",
  "Event_Number 9": "2896",
  "Event_Back pack of Charisma Pan": "2897",
  "Event_Ice": "2898",
  "Event_Rose (Blue)": "2899",
  "Master's Lapis Orb": "29",
  "Ring of Fighter +5": "290",
  "Event_Rose (Green)": "2900",
  "Event_Rose (Pink)": "2901",
  "Event_Rose (Red)": "2902",
  "Event_Rose (Purple)": "2903",
  "Event_Rose (White)": "2904",
  "Event_Rose (Yellow)": "2905",
  "Event_Rose bundle (Blue)": "2906",
  "Event_Rose bundle (Green)": "2907",
  "Event_Rose bundle (Pink)": "2908",
  "Event_Rose bundle (Red)": "2909",
  "Ring of Fighter +6": "291",
  "Event_Rose bundle (Purple)": "2910",
  "Event_Rose bundle (White)": "2911",
  "Event_Rose bundle (Yellow)": "2912",
  "Event_Platinum Badge": "2913",
  "Event_Beach Ball": "2914",
  "Event_Tube": "2915",
  "Event_Christmas Wreath": "2916",
  "Event_Christmas Ball (Green)": "2917",
  "Event_Christmas Ball (Yellow)": "2918",
  "Event_Christmas Ball (Red)": "2919",
  "Ring of Fighter +7": "292",
  "Event_Christmas Ball (White)": "2920",
  "Orb of Alphabisco": "2921",
  "Orb of Licoreking": "2922",
  "Orb of Treiple": "2923",
  "Orb of Black Snake": "2924",
  "Orb of Bugreil": "2925",
  "Orb of Etoku": "2926",
  "Crystal of Alphabisco": "2927",
  "Crystal of Licoreking": "2928",
  "Crystal of Treiple": "2929",
  "Ring of Sage": "293",
  "Crystal of Black Snake": "2930",
  "Crystal of Bugreil": "2931",
  "Crystal of Etoku": "2932",
  "Katana of Alphabisco": "2933",
  "Katana of Licoreking": "2934",
  "Katana of Treiple": "2935",
  "Katana of Black Snake": "2936",
  "Katana of Bugreil": "2937",
  "Katana of Etoku": "2938",
  "Blade of Alphabisco": "2939",
  "Ring of Sage +1": "294",
  "Blade of Licoreking": "2940",
  "Blade of Treiple": "2941",
  "Blade of Black Snake": "2942",
  "Blade of Bugreil": "2943",
  "Blade of Etoku": "2944",
  "Daikatana of Alphabisco": "2945",
  "Daikatana of Licoreking": "2946",
  "Daikatana of Treiple": "2947",
  "Daikatana of Black Snake": "2948",
  "Daikatana of Bugreil": "2949",
  "Ring of Sage +2": "295",
  "Daikatana of Etoku": "2950",
  "Greatsword of Alphabisco": "2951",
  "Greatsword of Licoreking": "2952",
  "Greatsword of Treiple": "2953",
  "Greatsword of Black Snake": "2954",
  "Greatsword of Bugreil": "2955",
  "Greatsword of Etoku": "2956",
  "Plate of Devil Lynxhorn (FS)": "2957",
  "Plate of Devil Lynxhorn (WA)": "2958",
  "Plate of Mega Hound (FS)": "2959",
  "Ring of Sage +3": "296",
  "Plate of Mega Hound (WA)": "2960",
  "Plate of Pelcost (FS)": "2961",
  "Plate of Pelcost (WA)": "2962",
  "Coat of Troglo King (FA)": "2963",
  "Coat of Troglo King (FB)": "2964",
  "Coat of Spamaton (FA)": "2965",
  "Coat of Spamaton (FB)": "2966",
  "Coat of Crag King Crab (FA)": "2967",
  "Coat of Crag King Crab (FB)": "2968",
  "Suit of Fox Major (BL)": "2969",
  "Ring of Sage +4": "297",
  "Suit of Fox Major (WI)": "2970",
  "Suit of Comoarchite (BL)": "2971",
  "Suit of Comoarchite (WI)": "2972",
  "Suit of Entros (BL)": "2973",
  "Suit of Entros (WI)": "2974",
  "Gauntlet of Cold Triple (FS)": "2975",
  "Gauntlet of Cold Triple (WA)": "2976",
  "Gauntlet of Mega Hound (FS)": "2977",
  "Gauntlet of Mega Hound (WA)": "2978",
  "Gauntlet of Pelcost (FS)": "2979",
  "Ring of Sage +5": "298",
  "Gauntlet of Pelcost (WA)": "2980",
  "Gloves of Poki Triple (FA)": "2981",
  "Gloves of Poki Triple (FB)": "2982",
  "Gloves of Spamaton (FA)": "2983",
  "Gloves of Spamaton (FB)": "2984",
  "Gloves of Crag King Crab (FA)": "2985",
  "Gloves of Crag King Crab (FB)": "2986",
  "Hands of Teer Triple (BL)": "2987",
  "Hands of Teer Triple (WI)": "2988",
  "Hands of Comoarchite (BL)": "2989",
  "Ring of Sage +6": "299",
  "Hands of Comoarchite (WI)": "2990",
  "Hands of Entros (BL)": "2991",
  "Hands of Entros (WI)": "2992",
  "Greaves of Cold Triple (FS)": "2993",
  "Greaves of Cold Triple (WA)": "2994",
  "Greaves of Mega Hound (FS)": "2995",
  "Greaves of Mega Hound (WA)": "2996",
  "Greaves of Pelcost (FS)": "2997",
  "Greaves of Pelcost (WA)": "2998",
  "Boots of Poki Triple (FA)": "2999",
  "HP Potion (Lv. 1)": "3",
  "Training Crystal": "30",
  "Ring of Sage +7": "300",
  "Boots of Poki Triple (FB)": "3000",
  "Boots of Spamaton (FA)": "3001",
  "Boots of Spamaton (FB)": "3002",
  "Boots of Crag King Crab (FA)": "3003",
  "Boots of Crag King Crab (FB)": "3004",
  "Shoes of Teer Triple (BL)": "3005",
  "Shoes of Teer Triple (WI)": "3006",
  "Shoes of Comoarchite (BL)": "3007",
  "Shoes of Comoarchite (WI)": "3008",
  "Shoes of Entros (BL)": "3009",
  "Ring of Champion": "301",
  "Shoes of Entros (WI)": "3010",
  "Visor of Devil Lynxhorn (FS)": "3011",
  "Visor of Devil Lynxhorn (WA)": "3012",
  "Visor of Mega Hound (FS)": "3013",
  "Visor of Mega Hound (WA)": "3014",
  "Visor of Pelcost (FS)": "3015",
  "Visor of Pelcost (WA)": "3016",
  "Headgear of Troglo King (FA)": "3017",
  "Headgear of Troglo King (FB)": "3018",
  "Headgear of Spamaton (FA)": "3019",
  "Ring of Champion +1": "302",
  "Headgear of Spamaton (FB)": "3020",
  "Headgear of Crag King Crab (FA)": "3021",
  "Headgear of Crag King Crab (FB)": "3022",
  "Headpiece of Fox Major (BL)": "3023",
  "Headpiece of Fox Major (WI)": "3024",
  "Headpiece of Comoarchite (BL)": "3025",
  "Headpiece of Comoarchite (WI)": "3026",
  "Headpiece of Entros (BL)": "3027",
  "Headpiece of Entros (WI)": "3028",
  "Ring of Garlelia": "3029",
  "Ring of Champion +2": "303",
  "Ring of Nippertrica": "3030",
  "Ring of Momento": "3031",
  "Ring of Golden Toad": "3032",
  "Ring of Kailth": "3033",
  "Ring of Babimadon": "3034",
  "Amulet of Galelia": "3035",
  "Amulet of Nippertrica": "3036",
  "Amulet of Momento": "3037",
  "Epaulet of Phantom Fera": "3038",
  "Epaulet of Black Hound": "3039",
  "Ring of Champion +3": "304",
  "Board Card of Phantom Fera": "3040",
  "Board card of Black Hound": "3041",
  "Earrings of Kailth": "3042",
  "Earrings of Babimadon": "3043",
  "Braclets of Kailth": "3044",
  "Braclets of Babimadon": "3045",
  "Belt of Golden Toad": "3046",
  "Pet - Angela": "3047",
  "Pet - Devil O'": "3048",
  "Pet - Draca": "3049",
  "Ring of Champion +4": "305",
  "Event_Base Ball": "3050",
  "Event_Basket Ball": "3051",
  "Event_Bowling Ball": "3052",
  "Event_Volley Ball": "3053",
  "Event_Ping-Pong Ball": "3054",
  "Event_Rugby Ball": "3055",
  "Event_Soccer Ball": "3056",
  "Event_Beer Mug": "3057",
  "Event_Cocktail Glass": "3058",
  "Event_Mug": "3059",
  "Ring of Champion +5": "306",
  "Event_Piece of Cake": "3060",
  "Event_Cup Cake": "3061",
  "Event_Large Cake": "3062",
  "Event_Ice Cream": "3063",
  "Event_Turkey": "3064",
  "Event_Egg": "3065",
  "Event_Fried Egg": "3066",
  "Event_Basket of Eggs": "3067",
  "Event_Dragon Egg": "3068",
  "Event_Broken Dragon Egg": "3069",
  "Ring of Champion +6": "307",
  "Event_Dragon Claw": "3070",
  "Event_Magic Wand": "3071",
  "Event_Broom": "3072",
  "Event_Cross": "3073",
  "Event_Chicken Leg": "3074",
  "Event_Bottled Letter": "3075",
  "Event_Ribbon": "3076",
  "Event_Giftbox": "3077",
  "Event_Candle": "3078",
  "Event_Candlestick": "3079",
  "Ring of Champion +7": "308",
  "Event_Gold medal": "3080",
  "Event_Silver medal": "3081",
  "Event_Bronze medal": "3082",
  "Event_Gold Trophy": "3083",
  "Event_Silver Trophy": "3084",
  "Event_Bronze Trophy": "3085",
  "Event_Earth": "3086",
  "Event_Fire": "3087",
  "Event_Wind": "3088",
  "Event_Water": "3089",
  "Critical Ring": "309",
  "Event_Lightning": "3090",
  "Event_Snow": "3091",
  "[GM] Holy Water of Speed": "3092",
  "Holy Water of Traveler (30 min)": "3093",
  "Holy Water of Critical Strike (30 min)": "3094",
  "Holy Water of Fighter (30 min)": "3095",
  "Holy Water of Sage (30 min)": "3096",
  "Holy Water of Traveler (15 min)": "3097",
  "Holy Water of Critical Strike (15 min)": "3098",
  "Holy Water of Fighter (15 min)": "3099",
  "Crude Crystal": "31",
  "Critical Ring +1": "310",
  "Holy Water of Sage (15 min)": "3100",
  "Saint's Forcecalibur": "3101",
  "Combination for Sealed Core (No.1)": "3102",
  "HP Potion (Lv. 4)": "3103",
  "MP Potion (Lv. 4)": "3104",
  "Fury Potion (Lv. 4)": "3105",
  "Life Capsule (Lv. 4) TypeA": "3106",
  "Life Capsule (Lv. 4) TypeB": "3107",
  "Bike Option Scroll (Low)": "3108",
  "Bike Option Scroll (Medium)": "3109",
  "Critical Ring +2": "311",
  "Bike Option Scroll (High)": "3110",
  "Bike Option Scroll (Highest)": "3111",
  "Chaos Box - Slot Extender": "3112",
  "Chaos Box - Epaulet of Fighter": "3113",
  "Chaos Box - Epaulet of Sage": "3114",
  "Chaos Box - Epaulet of Guardian": "3115",
  "Chaos Box - Upgrade Item": "3116",
  "Chaos Box - Classic Avatar": "3117",
  "Chaos Box - Premium Avatar": "3118",
  "Gift Box - SIGMetal Epaulet of Fighter (10 Days)": "3119",
  "Ring of Luck": "312",
  "Gift Box - SIGMetal Epaulet of Fighter (30 Days)": "3120",
  "Gift Box - SIGMetal Epaulet of Fighter (90 Days)": "3121",
  "Gift Box - SIGMetal Epaulet of Sage (10 Days)": "3122",
  "Gift Box - SIGMetal Epaulet of Sage (30 Days)": "3123",
  "Gift Box - SIGMetal Epaulet of Sage (90 Days)": "3124",
  "Gift Box - SIGMetal Epaulet of Guardian (10 Days)": "3125",
  "Gift Box - SIGMetal Epaulet of Guardian (30 Days)": "3126",
  "Gift Box - SIGMetal Epaulet of Guardian (90 Days)": "3127",
  "Ring of Luck +1": "313",
  "Astral Bike Card - Red Flare": "3130",
  "Astral Bike Card - Blue Flare": "3131",
  "Mixture (Lv. 1)": "3132",
  "Mixture (Lv. 2)": "3133",
  "Sword Damage Amplifier (Lv. 1)": "3134",
  "Sword Damage Amplifier (Lv. 2)": "3135",
  "Magic Damage Amplifier (Lv. 1)": "3136",
  "Magic Damage Amplifier (Lv. 2)": "3137",
  "[Costume] Nevareth Biker": "3138",
  "[Costume] Epaulet of Nevareth Biker": "3139",
  "Ring of Luck +2": "314",
  "Giftbox - [Costume] Nevareth Biker": "3140",
  "[Costume] Biker Bandana": "3141",
  "[Costume] Cap of Nevareth Biker": "3142",
  "Giftbox - [Costume] Biker Bandana": "3143",
  "Cube of Challenge": "3144",
  "[Costume] Lightsaber - Orb": "3145",
  "[Costume] Lightsaber - Katana": "3146",
  "[Costume] Lightsaber - Blade": "3147",
  "[Costume] Lightsaber - Crystal": "3148",
  "[Costume] Lightsaber - Daikatana": "3149",
  "Force Absorb Ring": "315",
  "[Costume] Lightsaber - Greatsword": "3150",
  "Giftbox - [Costume] Lightsaber - Orb": "3151",
  "Giftbox - [Costume] Lightsaber - Katana": "3152",
  "Giftbox - [Costume] Lightsaber - Blade": "3153",
  "Giftbox - [Costume] Lightsaber - Crystal": "3154",
  "Giftbox - [Costume] Lightsaber - Daikatana": "3155",
  "Giftbox - [Costume] Lightsaber - Greatsword": "3156",
  "Giftbox - [Costume] Candy - Orb/Crystal": "3157",
  "Giftbox - [Costume] Candy - Katana/Daikatana": "3158",
  "Giftbox - [Costume] Candy - Blade/Greatsword": "3159",
  "Force Absorb Ring +1": "316",
  "Giftbox - [Costume] Festival - Orb/Crystal": "3160",
  "Giftbox - [Costume] Festival - Katana/Daikatana": "3161",
  "Giftbox - [Costume] Festival - Blade/Greatsword": "3162",
  "Giftbox - Astral Board Card - X Yellow": "3163",
  "Giftbox - Astral Board Card - Zero Black": "3164",
  "Giftbox - Astral Board Card - Zero Silver": "3165",
  "Hat Rack": "3167",
  "Weapon Storage Box": "3168",
  "Board Card Case": "3169",
  "Force Absorb Ring +2": "317",
  "Gem of Infinite Summons (Normal)": "3170",
  "Gem of Infinite Summons (Rare)": "3171",
  "Gem of Infinite Summons (Epic)": "3172",
  "Gem of Infinite Summons (Unique)": "3173",
  "Bike Deco (Crystal RW)": "3174",
  "Leap of Hero": "3175",
  "Inexhaustible HP Potion(Lv. 1)": "3176",
  "Inexhaustible HP Potion(Lv. 2)": "3177",
  "Inexhaustible HP Potion(Lv. 3)": "3178",
  "Inexhaustible MP Potion(Lv. 1)": "3179",
  "Life Absorb Ring": "318",
  "Inexhaustible MP Potion(Lv. 2)": "3180",
  "Inexhaustible MP Potion(Lv. 3)": "3181",
  "Inexhaustible HP Potion(Lv. 4)": "3182",
  "Inexhaustible MP Potion(Lv. 4)": "3183",
  "Killian's Ring": "3184",
  "Black Cat's Tear": "3185",
  "Limbic Tonsil Cell": "3186",
  "Killian's Ring Craft Recipe": "3187",
  "Quest Capsule No. 1": "3188",
  "Quest Capsule No. 2": "3189",
  "Life Absorb Ring +1": "319",
  "Quest Capsule No. 3": "3190",
  "DP Saver": "3191",
  "AP Saver": "3192",
  "Critical Ring +3": "3193",
  "Ring of Luck +3": "3194",
  "Quest Capsule No. 4": "3195",
  "Quest Capsule No. 5": "3196",
  "Quest Capsule No. 6": "3197",
  "Quest Capsule No. 7": "3198",
  "Quest Capsule No. 8": "3199",
  "Red Crystal": "32",
  "Life Absorb Ring +2": "320",
  "Quest Capsule No. 9": "3200",
  "Quest Capsule No. 10": "3201",
  "Quest Capsule No. 11": "3202",
  "Quest Capsule No. 12": "3203",
  "Quest Capsule No. 13": "3204",
  "Quest Capsule No. 14": "3205",
  "Quest Capsule No. 15": "3206",
  "Quest Capsule No. 16": "3207",
  "Quest Capsule No. 17": "3208",
  "Quest Capsule No.18": "3209",
  "Life Absorb Ring +3": "321",
  "Quest Capsule No.19": "3210",
  "Quest Capsule No.20": "3211",
  "Quest Capsule No.21": "3212",
  "Quest Capsule No.22": "3213",
  "Quest Capsule No.23": "3214",
  "Quest Capsule No.24": "3215",
  "Quest Capsule No.25": "3216",
  "Quest Capsule No.26": "3217",
  "Quest Capsule No.27": "3218",
  "Quest Capsule No.28": "3219",
  "Mana Absorb Ring": "322",
  "Quest Capsule No.29": "3220",
  "Quest Capsule No.30": "3221",
  "Quest Capsule No.31": "3222",
  "Quest Capsule No.32": "3223",
  "Quest Capsule No.33": "3224",
  "Quest Capsule No.34": "3225",
  "Quest Capsule No.35": "3226",
  "Cube of Honor (Low)": "3227",
  "Cube of Honor (Medium)": "3228",
  "Cube of Honor (High)": "3229",
  "Mana Absorb Ring +1": "323",
  "Epaulet Option Spell Scroll (High)": "3230",
  "Epaulet Option Spell Scroll (Highest)": "3231",
  "Druga (Normal)": "3232",
  "Druga (Rare)": "3233",
  "Druga (Epic)": "3234",
  "Druga (Unique)": "3235",
  "Syarsor (Normal)": "3236",
  "Syarsor (Rare)": "3237",
  "Syarsor (Epic)": "3238",
  "Syarsor (Unique)": "3239",
  "Mana Absorb Ring +2": "324",
  "Angelo (Normal)": "3240",
  "Angelo (Rare)": "3241",
  "Angelo (Epic)": "3242",
  "Angelo (Unique)": "3243",
  "Lamp of Dazzlement (Lv. 1)": "3244",
  "Lamp of Dazzlement (Lv. 2)": "3245",
  "Lamp of Dazzlement (Lv. 3)": "3246",
  "Lamp of Dazzlement (Lv. 4)": "3247",
  "Magic Water of Purification": "3248",
  "Police Epaulet": "3249",
  "Mana Absorb Ring +3": "325",
  "Police Cap": "3250",
  "[Costume] CABAL Cops": "3251",
  "[Costume] CABAL Cops Cap": "3252",
  "Giftbox - [Costume] CABAL Cops": "3253",
  "Giftbox - [Costume] CABAL Cops Cap": "3254",
  "Battle Mode 3 Armor": "3255",
  "Battle Mode 3 Martial": "3256",
  "Battle Mode 3 Battle": "3257",
  "Battle Mode 3 Visor": "3258",
  "Battle Mode 3 Headpiece": "3259",
  "Ring Of Fighter +8": "326",
  "Battle Mode 3 Headgear": "3260",
  "Skill Book (Axe Destroyer)": "3261",
  "Skill Book (Axe Specialty Stage 2)": "3262",
  "Skill Book (Axe Specialty Stage 3)": "3263",
  "Skill Book (Knucklet Striker)": "3264",
  "Skill Book (Knucklet Specialty Stage 2)": "3265",
  "Skill Book (Knucklet Specialty Stage 3)": "3266",
  "Skill Book (Elemental Dominator)": "3267",
  "Skill Book (Elemental Specialty Stage 2)": "3268",
  "Skill Book (Elemental Specialty Stage 3)": "3269",
  "Ring Of Fighter +9": "327",
  "Skill Book (Launcher Punisher)": "3270",
  "Skill Book (Launcher Specialty Stage 2)": "3271",
  "Skill Book (Launcher Specialty Stage 3)": "3272",
  "Skill Book (Hammer Crusher)": "3273",
  "Skill Book (Hammer Specialty Stage 2)": "3274",
  "Skill Book (Hammer Specialty Stage 3)": "3275",
  "Skill Book (Sword Splitter)": "3276",
  "Skill Book (Split Specialty Stage 2)": "3277",
  "Skill Book (Split Specialty Stage 3)": "3278",
  "Minesta Training Book Chapter 1": "3279",
  "Ring Of Fighter +10": "328",
  "Minesta Training Book Chapter 2": "3280",
  "Minesta Training Book Chapter 3": "3281",
  "Minesta Training Book Chapter 4": "3282",
  "Minesta Training Book Chapter 5": "3283",
  "Minesta Training Book Chapter 6": "3284",
  "Minesta Training Book Chapter 7": "3285",
  "Minesta Training Book Chapter 8": "3286",
  "Minesta Training Book Chapter 9": "3287",
  "Minesta Training Book Chapter 10": "3288",
  "Minesta Training Book Chapter 11": "3289",
  "Ring Of Sage +8": "329",
  "Minesta Training Book Chapter 12": "3290",
  "Minesta Training Book Chapter 13": "3291",
  "Minesta Training Book Chapter 14": "3292",
  "Minesta Training Book Chapter 15": "3293",
  "Minesta Training Book Chapter 16": "3294",
  "Minesta Training Book Chapter 17": "3295",
  "Minesta Training Book Chapter 18": "3296",
  "Minesta Training Book Chapter 19": "3297",
  "Minesta Training Book Chapter 20": "3298",
  "Minesta Training Book Chapter 21": "3299",
  "Coraleye Crystal": "33",
  "Ring Of Sage +9": "330",
  "Minesta Training Book Chapter 22": "3300",
  "Minesta Training Book Chapter 23": "3301",
  "Minesta Training Book Chapter 24": "3302",
  "Minesta Training Book Chapter 25": "3303",
  "Minesta Training Book Chapter 26": "3304",
  "Minesta Training Book Chapter 27": "3305",
  "Minesta Training Book Chapter 28": "3306",
  "Minesta Training Book Chapter 29": "3307",
  "Minesta Training Book Chapter 30": "3308",
  "Minesta Training Book Chapter 31": "3309",
  "Ring Of Sage +10": "331",
  "Minesta Craft Stone - Lv.1 (WA)": "3310",
  "Minesta Craft Stone - Lv.2 (WA)": "3311",
  "Minesta Craft Stone - Lv.3 (WA)": "3312",
  "Minesta Craft Stone - Lv.1 (BL)": "3313",
  "Minesta Craft Stone - Lv.2 (BL)": "3314",
  "Minesta Craft Stone - Lv.3 (BL)": "3315",
  "Minesta Craft Stone - Lv.1 (WI)": "3316",
  "Minesta Craft Stone - Lv.2 (WI)": "3317",
  "Minesta Craft Stone - Lv.3 (WI)": "3318",
  "Minesta Craft Stone - Lv.1 (FA)": "3319",
  "Ring Of Champion +8": "332",
  "Minesta Craft Stone - Lv.2 (FA)": "3320",
  "Minesta Craft Stone - Lv.3 (FA)": "3321",
  "Minesta Craft Stone - Lv.1 (FS)": "3322",
  "Minesta Craft Stone - Lv.2 (FS)": "3323",
  "Minesta Craft Stone - Lv.3 (FS)": "3324",
  "Minesta Craft Stone - Lv.1 (FB)": "3325",
  "Minesta Craft Stone - Lv.2 (FB)": "3326",
  "Minesta Craft Stone - Lv.3 (FB)": "3327",
  "Potion of Honor (Lv. 1)": "3328",
  "Potion of Honor (Lv. 2)": "3329",
  "Ring Of Champion +9": "333",
  "Potion of Honor (Lv. 3)": "3330",
  "Blessing Bead - GPS Warp": "3331",
  "Yekaterina Membership": "3332",
  "Blessing Bead - Warehouse (No. 5)": "3333",
  "Blessing Bead - Warehouse (No. 6)": "3334",
  "Blessing Bead - Warehouse (No. 7)": "3335",
  "Blessing Bead - Warehouse (No. 8)": "3336",
  "Pet Untrain Kit - Option": "3337",
  "Guild Name Card": "3338",
  "Core Superior (Low)": "3339",
  "Ring Of Champion +10": "334",
  "Core Superior (Medium)": "3340",
  "Core Superior (High)": "3341",
  "Core Superior (Highest)": "3342",
  "Prideus' Bracelet": "3343",
  "Extract Potion (STR) (Lv. 2) (Not in use)": "3344",
  "Extract Potion (DEX) (Lv. 2) (Not in use)": "3345",
  "Extract Potion (INT) (Lv. 2) (Not in use)": "3346",
  "[Costume] Lightsaber - Chakram": "3347",
  "[Costume] Hellforged Weapon - Chakram": "3348",
  "Giftbox - [Costume] Lightsaber - Chakram": "3349",
  "Force Absorb Ring +3": "335",
  "Giftbox - [Costume] Hellforged Weapon - Chakram": "3350",
  "Vital Gear (Lv. 1)": "3352",
  "Vital Gear (Lv. 2)": "3353",
  "Vital Gear (Lv. 3)": "3354",
  "Megaphone (CN Exclusive)": "3355",
  "Archridium Orb": "33554433",
  "Archridium Crystal": "33554434",
  "Archridium Katana": "33554435",
  "Archridium Blade": "33554436",
  "Archridium Daikatana": "33554437",
  "Archridium Greatsword": "33554438",
  "Archridium Plate (WA)": "33554439",
  "Archridium Plate (FS)": "33554440",
  "Archridium Coat (FB)": "33554441",
  "Archridium Coat (FA)": "33554442",
  "Archridium Suit (BL)": "33554443",
  "Archridium Suit (WI)": "33554444",
  "Archridium Gauntlet (WA)": "33554445",
  "Archridium Gauntlet (FS)": "33554446",
  "Archridium Gloves (FB)": "33554447",
  "Archridium Gloves (FA)": "33554448",
  "Archridium Hands (BL)": "33554449",
  "Archridium Hands (WI)": "33554450",
  "Archridium Greaves (WA)": "33554451",
  "Archridium Greaves (FS)": "33554452",
  "Archridium Boots (FB)": "33554453",
  "Archridium Boots (FA)": "33554454",
  "Archridium Shoes (BL)": "33554455",
  "Archridium Shoes (WI)": "33554456",
  "Archridium Visor (WA)": "33554457",
  "Archridium Visor (FS)": "33554458",
  "Archridium Headgear (FB)": "33554459",
  "Archridium Headgear (FA)": "33554460",
  "Archridium Headpiece (BL)": "33554461",
  "Archridium Headpiece (WI)": "33554462",
  "Craftsman's Archridium Orb": "33554463",
  "Craftsman's Archridium Crystal": "33554464",
  "Craftsman's Archridium Katana": "33554465",
  "Craftsman's Archridium Blade": "33554466",
  "Craftsman's Archridium Daikatana": "33554467",
  "Craftsman's Archridium Greatsword": "33554468",
  "Craftsman's Archridium Plate (WA)": "33554469",
  "Craftsman's Archridium Plate (FS)": "33554470",
  "Craftsman's Archridium Coat (FB)": "33554471",
  "Craftsman's Archridium Coat (FA)": "33554472",
  "Craftsman's Archridium Suit (BL)": "33554473",
  "Craftsman's Archridium Suit (WI)": "33554474",
  "Craftsman's Archridium Gauntlet (WA)": "33554475",
  "Craftsman's Archridium Gauntlet (FS)": "33554476",
  "Craftsman's Archridium Gloves (FB)": "33554477",
  "Craftsman's Archridium Gloves (FA)": "33554478",
  "Craftsman's Archridium Hands (BL)": "33554479",
  "Craftsman's Archridium Hands (WI)": "33554480",
  "Craftsman's Archridium Greaves (WA)": "33554481",
  "Craftsman's Archridium Greaves (FS)": "33554482",
  "Craftsman's Archridium Boots (FB)": "33554483",
  "Craftsman's Archridium Boots (FA)": "33554484",
  "Craftsman's Archridium Shoes (BL)": "33554485",
  "Craftsman's Archridium Shoes (WI)": "33554486",
  "Craftsman's Archridium Visor (WA)": "33554487",
  "Craftsman's Archridium Visor (FS)": "33554488",
  "Craftsman's Archridium Headgear (FB)": "33554489",
  "Craftsman's Archridium Headgear (FA)": "33554490",
  "Craftsman's Archridium Headpiece (BL)": "33554491",
  "Craftsman's Archridium Headpiece (WI)": "33554492",
  "Master's Archridium Orb": "33554493",
  "Master's Archridium Crystal": "33554494",
  "Master's Archridium Katana": "33554495",
  "Master's Archridium Blade": "33554496",
  "Master's Archridium Daikatana": "33554497",
  "Master's Archridium Greatsword": "33554498",
  "Master's Archridium Plate (WA)": "33554499",
  "Master's Archridium Plate (FS)": "33554500",
  "Master's Archridium Coat (FB)": "33554501",
  "Master's Archridium Coat (FA)": "33554502",
  "Master's Archridium Suit (BL)": "33554503",
  "Master's Archridium Suit (WI)": "33554504",
  "Master's Archridium Gauntlet (WA)": "33554505",
  "Master's Archridium Gauntlet (FS)": "33554506",
  "Master's Archridium Gloves (FB)": "33554507",
  "Master's Archridium Gloves (FA)": "33554508",
  "Master's Archridium Hands (BL)": "33554509",
  "Master's Archridium Hands (WI)": "33554510",
  "Master's Archridium Greaves (WA)": "33554511",
  "Master's Archridium Greaves (FS)": "33554512",
  "Master's Archridium Boots (FB)": "33554513",
  "Master's Archridium Boots (FA)": "33554514",
  "Master's Archridium Shoes (BL)": "33554515",
  "Master's Archridium Shoes (WI)": "33554516",
  "Master's Archridium Visor (WA)": "33554517",
  "Master's Archridium Visor (FS)": "33554518",
  "Master's Archridium Headgear (FB)": "33554519",
  "Master's Archridium Headgear (FA)": "33554520",
  "Master's Archridium Headpiece (BL)": "33554521",
  "Master's Archridium Headpiece (WI)": "33554522",
  "Anniversary Damage Reduction Potion": "33554523",
  "Anniversary Accuracy Potion": "33554524",
  "Anniversary Penetration Potion": "33554525",
  "Damage Reduction Potion": "33554526",
  "Accuracy Potion": "33554570",
  "Penetration Potion": "33554571",
  "Anniversary (VIP)": "33554530",
  "Anniversary Cube": "33554532",
  "Damage Reduction": "33554533",
  "Extract Potion (STR) (Lv. 3) (Not in use)": "33554534",
  "Extract Potion (DEX) (Lv. 3) (Not in use)": "33554535",
  "Extract Potion (INT) (Lv. 3) (Not in use)": "33554536",
  "Extract Potion (STR) (Lv. 4) (Not in use)": "33554537",
  "Extract Potion (DEX) (Lv. 4) (Not in use)": "33554538",
  "Extract Potion (INT) (Lv. 4) (Not in use)": "33554539",
  "[Costume] Hellforged Weapon - Orb": "33554540",
  "[Costume] Hellforged Weapon - Katana": "33554541",
  "[Costume] Hellforged Weapon - Blade": "33554542",
  "[Costume] Hellforged Weapon - Crystal": "33554543",
  "[Costume] Hellforged Weapon - Daikatana": "33554544",
  "[Costume] Hellforged Weapon - Greatsword": "33554545",
  "Giftbox - [Costume] Hellforged Weapon - Orb": "33554546",
  "Giftbox - [Costume] Hellforged Weapon - Katana": "33554547",
  "Giftbox - [Costume] Hellforged Weapon - Blade": "33554548",
  "Giftbox - [Costume] Hellforged Weapon - Crystal": "33554549",
  "Giftbox - [Costume] Hellforged Weapon - Daikatana": "33554550",
  "Giftbox - [Costume] Hellforged Weapon - Greatsword": "33554551",
  "Epaulet of the Dead 3N": "33554552",
  "Tempus' Ring": "33554553",
  "Minesta's Red Sapphire Charm": "33554554",
  "Minesta's Green Sapphire Charm": "33554555",
  "Minesta's Yellow Sapphire Charm": "33554556",
  "Minesta's Blue Ruby Charm": "33554557",
  "Minesta's Green Ruby Charm": "33554558",
  "Minesta's Yellow Ruby Charm": "33554559",
  "Minesta's Blue Emerald Charm": "33554560",
  "Minesta's Red Emerald Charm": "33554561",
  "Minesta's Yellow Emerald Charm": "33554562",
  "Minesta's Blue Amber Charm": "33554563",
  "Minesta's Red Amber Charm": "33554564",
  "Minesta's Green Amber Charm": "33554565",
  "Cabal Lucky Bag (Korean Exclusive)": "33554566",
  "[GM]Epaulet of Romy": "33554567",
  "[GM]Epaulet of Hany": "33554568",
  "DMG Reduction Potion": "33554569",
  "Bike Parts (Highest)": "33554572",
  "Bike Epic Converter Box (Highest)": "33554573",
  "Bike Epic Converter (Highest) - HP": "33554574",
  "Bike Epic Converter (Highest) - All Skill Amp.": "33554575",
  "Bike Epic Converter (Highest) - Critical Rate": "33554576",
  "Bike Epic Converter (Highest) - Critical Damage": "33554577",
  "Bike Epic Converter (Highest) - All Attack": "33554578",
  "Bike Epic Converter (Highest) - Accuracy": "33554579",
  "Bike Epic Converter (Lv. 4) - Penetration": "33554580",
  "Bike Epic Converter (Highest) - Skill Amp. Resistance": "33554581",
  "Bike Slot Converter (Highest)": "33554582",
  "Archridium Epaulet of Laws": "33554583",
  "Archridium Epaulet of Fighter": "33554584",
  "Archridium Epaulet of Sage": "33554585",
  "Accuracy": "33554586",
  "Powerless Tempus' Ring": "33554587",
  "Option Scroll Cube (Highest)": "33554588",
  "Chaos Box - Premium Avatar II": "33554589",
  "Chaos Box - Avatar Plus": "33554590",
  "Cursed Muster2N": "33554591",
  "Awakened Tyrant's Ring": "33554592",
  "Cursed Tyrant's Ring": "33554593",
  "Chaos Box - Premium Pet Items": "33554648",
  "Chaos Box - Pet Change Kit": "33554595",
  "Chaos Box - Blessing Core": "33554596",
  "Prideus' Unbinding Stone": "33554902",
  "Drosnin Unbinding Stone": "33554903",
  "Yerte's Weapon Box (Orb)": "33554599",
  "Yerte's Weapon Box (Crystal)": "33554600",
  "Eite's Weapon Box (Katana)": "33554601",
  "Eite's Weapon Box (Blade)": "33554602",
  "Eite's Weapon Box (Daikatana)": "33554603",
  "Eite's Weapon Box (Greatsword)": "33554604",
  "Agris's Armor Box (WA)": "33554605",
  "Agris's Armor Box (FS)": "33554606",
  "Agris's Armor Box (FB)": "33554607",
  "Agris's Armor Box (FA)": "33554608",
  "Agris's Armor Box (BL)": "33554609",
  "Agris's Armor Box (WI)": "33554610",
  "Peticia's Golden Key": "33554611",
  "Lycanus Core": "33554612",
  "Drei Frame Core": "33554613",
  "Skill Book (Piercing Spell)": "33554615",
  "Skill Book (Elemental Enchant)": "33554616",
  "Cube of Enchant (Premium)": "33554617",
  "Skill Book (Apocalypse Decision)": "33554618",
  "Skill Book (Infinity Blaze)": "33554619",
  "Skill Book (Hell Crasher)": "33554620",
  "Skill Book (Quake Cataclysm)": "33554621",
  "Skill Book (Glacial Trap)": "33554622",
  "Skill Book (Arrow Barrage)": "33554623",
  "Extreme Core (Lv. 1)": "33554624",
  "Extreme Core (Lv. 2)": "33554625",
  "Extreme Core (Lv. 3)": "33554626",
  "Extreme Core (Lv. 4)": "33554627",
  "Extreme Core (Lv. 5)": "33554628",
  "Extreme Core (Lv. 6)": "33554629",
  "Extreme Core (Lv. 7)": "33554630",
  "Extreme Core (Lv. 8)": "33554631",
  "Extreme Core (Lv. 9)": "33554632",
  "Extreme Core (Lv. 10)": "33554633",
  "Bronze Repair Kit": "33554634",
  "Silver Repair Kit": "33554635",
  "Gold Repair Kit": "33554636",
  "Extreme Core Pocket (Normal)": "33554637",
  "Extreme Core Pocket (Rare)": "33554638",
  "Extreme Core Pocket (Epic)": "33554639",
  "Extreme Core Pocket (Unique)": "33554640",
  "Extreme Core Pocket (Premium)": "33554641",
  "Repair Kit Box": "33554642",
  "Premium Repair Kit Box": "33554643",
  "Extreme Core (Piece)": "33554644",
  "Repair Kit Certificate": "33554645",
  "Chaos Box - Upgrade III": "33554646",
  "Honey Dew Water": "33554647",
  "Chaos Box - Enchant Safeguard": "33554649",
  "Chaos Box - Weapon Skins": "********",
  "Blessing Bead - Supreme": "********",
  "Blessing Bead - Ultimate": "********",
  "Cube of Protection (Premium)": "********",
  "Gift of the Bread Boy": "********",
  "Pet - Bonies": "********",
  "Chaos Box - Blessing Core II": "********",
  "Awakened CompassN": "********",
  "Mystery Force Piece": "********",
  "Mystery Force Stone": "********",
  "Mystery Force Crystal": "********",
  "Mystery Box": "********",
  "Questionable treasure box": "********",
  "Weapon Account bind Seal Stone": "********",
  "Armor Account bind Seal Stone": "********",
  "Bike Account bind Seal Stone": "********",
  "Weapon Account bind Unseal Stone": "********",
  "Armor Account bind Unseal Stone": "********",
  "Bike Account bind Unseal Stone": "********",
  "Mirror of Observation (Bronze)": "********",
  "Mirror of Observation (Silver)": "********",
  "Mirror of Observation (Gold)": "********",
  "Bloody Spirit Effector": "********",
  "Panic Cry Effector": "********",
  "Art of Fierceness Effector": "********",
  "Intuition Effector": "********",
  "Art of Force Control Effector": "********",
  "Spirit Shield Effector": "********",
  "Mortal Bain Effector": "********",
  "Art of Defense Effector": "********",
  "Art of Sniping Effector": "********",
  "Quick Move Effector": "********",
  "Elemental Enchant Effector": "********",
  "Art of Curse Effector": "********",
  "Clear Slate": "********",
  "Change Slate": "********",
  "Effector Core (Piece)": "********",
  "Oath of Soul": "********",
  "Cute Bunny Dialogue": "********",
  "Yummy Pumpkin Dialogue": "********",
  "CABAL Lucky Bag (GLB Exclusive)": "********",
  "Chaos Box - Hardness Capsule": "********",
  "Legendary Cube - Tower of Undead B3F": "33554692",
  "Legendary Cube - Forgotten Temple B2F (Awakened)": "33554693",
  "Legendary Cube - Maquinas Outpost": "33554694",
  "Legendary Cube - Forbidden Island (Awakened)": "33554695",
  "Legendary Cube - Altar of Siena B2F": "33554696",
  "Legendary Cube - Illusion Castle Radiant Hall": "33554697",
  "Legendary Cube - Forbidden Island": "33554698",
  "Legendary Cube - Altar of Siena B1F": "33554699",
  "Legendary Cube - Illusion Castle Underworld": "33554700",
  "Legendary Cube - Forgotten Temple B2F": "33554701",
  "Legendary Cube - Hazardous Valley": "33554702",
  "Legendary Cube - Steamer Crazy": "33554703",
  "Legendary Cube - Catacomb Frost": "33554704",
  "Legendary Cube - Panic Cave": "33554705",
  "Legendary Cube - Lava Hellfire": "33554706",
  "Legendary Cube - Steamer Crazy (Premium)": "33554707",
  "Legendary Cube - Catacomb Frost (Premium)": "33554708",
  "Legendary Cube - Panic Cave (Premium)": "33554709",
  "Legendary Cube - Lava Hellfire (Premium)": "33554710",
  "Option Scroll Cube (High)": "33554711",
  "Potion of Enhancement": "33554712",
  "Minesta's Amethyst Charm": "33554713",
  "Minesta's Amethyst Charm +1": "33554714",
  "Minesta's Amethyst Charm +2": "33554715",
  "Minesta's Amethyst Charm +3": "33554716",
  "Minesta's Amethyst Charm +4": "33554717",
  "Minesta's Amethyst Charm +5": "33554718",
  "Minesta's Amethyst Charm +6": "33554719",
  "Minesta's Amethyst Charm +7": "33554720",
  "Extreme Core (Lv. 11)": "33554721",
  "Extreme Core (Lv. 12)": "33554722",
  "Sealed Siena's Bracelet": "33554723",
  "Siena's Bracelet": "33554724",
  "Piece of Prideus' Bracelet": "33554725",
  "Piece of Siena's Bracelet": "33554726",
  "Infinite Arena entry ticket - Normal": "33554727",
  "Infinite Arena entry ticket - Premium": "33554728",
  "Minesta's Chaos Guardian Belt": "33554729",
  "Minesta's Chaos Fighter Belt": "33554730",
  "Minesta's Chaos Sage Belt": "33554731",
  "Fragment of Chaos": "33554732",
  "Chaos Core": "33554733",
  "Extreme Core Pocket(Supplementary)": "33554734",
  "Chaos Epic Ticket (Normal)": "33554735",
  "Chaos Epic Ticket (Premium)": "33554736",
  "Bike Epic Reset Scroll": "33554737",
  "Chaos Belt Reset Scroll": "33554738",
  "Chaos Belt Epic Converter - Temp1": "33554739",
  "Chaos Belt Epic Converter - Temp2": "33554740",
  "Chaos Belt Epic Converter - Temp3": "33554741",
  "Chaos Belt Epic Converter - Temp4": "33554742",
  "Chaos Belt Epic Converter - Temp5": "33554743",
  "Chaos Belt Epic Converter - Temp6": "33554744",
  "Chaos Belt Epic Converter - Temp7": "33554745",
  "Chaos Belt Epic Converter - Temp8": "33554746",
  "Siena's Unbinding Stone": "33554904",
  "Skill Book (Crescent Slash)": "33554748",
  "Skill Book (Heavy Crush)": "33554749",
  "Skill Book (Waving Slash)": "33554750",
  "Skill Book (Reverse Step)": "33554751",
  "Skill Book (Moonlight Smash)": "33554752",
  "Skill Book (Moon Rising)": "33554753",
  "Skill Book (Cross on Earth)": "33554754",
  "Skill Book (Storm Slash)": "33554755",
  "Skill Book (Shadow Step)": "33554756",
  "Skill Book (Rapid Dash)": "33554757",
  "Skill Book (Moon Strike)": "33554758",
  "Skill Book (Fadeaway)": "33554759",
  "Skill Book (Mystic Dash)": "33554760",
  "Skill Book (Eternal Slash)": "33554761",
  "Skill Book (Trinity Slash)": "33554762",
  "Skill Book (Rising Storm)": "33554763",
  "Skill Book (Chakram Break)": "33554764",
  "Skill Book (Blade of Fury)": "33554765",
  "Skill Book (Chakram Tornado)": "33554766",
  "Skill Book (Freezing Strike)": "33554767",
  "Skill Book (Momentary Illumination)": "33554768",
  "Skill Book (Crimson Blade)": "33554769",
  "Skill Book (Illusion Blade)": "33554770",
  "Skill Book (Genocider)": "33554771",
  "Skill Book (Genocider Specialty Stage 2)": "33554772",
  "Skill Book (Genocider Specialty Stage 3)": "33554773",
  "Training Chakram": "33554774",
  "Chakram": "33554775",
  "Iron Chakram": "33554776",
  "Damascus Chakram": "33554777",
  "Shadowsteel Chakram": "33554778",
  "Bluestin Chakram": "33554779",
  "Titanium Chakram": "33554780",
  "Shadow Titanium Chakram": "33554781",
  "Osmium Chakram": "33554782",
  "Red Osmium Chakram": "33554783",
  "SIGMetal Chakram": "33554784",
  "Lycanus Chakram": "33554785",
  "Forcium Chakram": "33554786",
  "Archridium Chakram": "33554787",
  "Bluestin Visor(GL)": "33554788",
  "Bluestin Plate(GL)": "33554789",
  "Bluestin Gauntlets(GL)": "33554790",
  "Bluestin Greaves(GL)": "33554791",
  "Titanium Visor(GL)": "33554792",
  "Titanium Plate(GL)": "33554793",
  "Titanium Gauntlets(GL)": "33554794",
  "Titanium Greaves(GL)": "33554795",
  "Shadow Titanium Visor(GL)": "33554796",
  "Shadow Titanium Plate(GL)": "33554797",
  "Shadow Titanium Gauntlets(GL)": "33554798",
  "Shadow Titanium Greaves(GL)": "33554799",
  "Osmium Visor(GL)": "33554800",
  "Osmium Plate(GL)": "33554801",
  "Osmium Gauntlets(GL)": "33554802",
  "Osmium Greaves(GL)": "33554803",
  "Shineguard Visor(GL)": "33554804",
  "Shineguard Plate(GL)": "33554805",
  "Shineguard Gauntlets(GL)": "33554806",
  "Shineguard Greaves(GL)": "33554807",
  "SIGMetal Visor(GL)": "33554808",
  "SIGMetal Plate(GL)": "33554809",
  "SIGMetal Gauntlets(GL)": "33554810",
  "SIGMetal Greaves(GL)": "33554811",
  "Drei Frame Visor(GL)": "33554812",
  "Drei Frame Plate(GL)": "33554813",
  "Drei Frame Gauntlets(GL)": "33554814",
  "Drei Frame Greaves(GL)": "33554815",
  "Forcium Visor(GL)": "33554816",
  "Forcium Plate(GL)": "33554817",
  "Forcium Gauntlets(GL)": "33554818",
  "Forcium Greaves(GL)": "33554819",
  "Archridium Visor(GL)": "33554820",
  "Archridium Plate(GL)": "33554821",
  "Archridium Gauntlets(GL)": "33554822",
  "Archridium Greaves(GL)": "33554823",
  "Character Slot Opener (No. 7)": "33554824",
  "Battle Mode 3 Gladiator": "33554825",
  "Battle Mode 3 Gladiator Capella": "33554826",
  "Battle Mode 3 Gladiator Procyon": "33554827",
  "Battle Mode 3 Gladiator Visor": "33554828",
  "Battle Mode 3 Gladiator Visor Capella": "33554829",
  "Battle Mode 3 Gladiator Visor Procyon": "33554830",
  "Capella(GL)": "33554831",
  "Procyon(GL)": "33554832",
  "Afro": "33554833",
  "Bamboo Hat": "33554834",
  "Silk Hat": "33554835",
  "Rococo Hairstyle": "33554836",
  "Sunglasses": "33554837",
  "Teddy Hat": "33554838",
  "Eyeglass": "33554839",
  "[Costume] Afro Hair": "33554840",
  "[Costume] Bamboo Hat": "33554841",
  "[Costume] Top Hat": "33554842",
  "[Costume] Rococo Hairstyle": "33554843",
  "[Costume] Shutter Shade": "33554844",
  "[Costume] Teddy Hat": "33554845",
  "[Costume] Monocle": "33554846",
  "Giftbox - [Costume] Afro Hair": "33554847",
  "Giftbox - [Costume] Bamboo Hat": "33554848",
  "Giftbox - [Costume] Top Hat": "33554849",
  "Giftbox - [Costume] Rococo Hairstyle": "33554850",
  "Giftbox - [Costume] Shutter Shade": "33554851",
  "Giftbox - [Costume] Teddy Hat": "33554852",
  "Giftbox - [Costume] Monocle": "33554853",
  "Chakram of Alphabisco": "33554854",
  "Chakram of Licoreking": "33554855",
  "Chakram of Triple": "33554856",
  "Chakram of Black Snake": "33554857",
  "Chakram of Bugreil": "33554858",
  "Chakram of Etoku": "33554859",
  "Plate of Devil Lynxhorn(GL)": "33554860",
  "Plate of Mega Hound(GL)": "33554861",
  "Plate of Pelcost(GL)": "33554862",
  "Gauntlet of Cold Triple(GL)": "33554863",
  "Gauntlet of Mega Hound(GL)": "33554864",
  "Gauntlet of Pelcost(GL)": "33554865",
  "Greaves of Cold Triple(GL)": "33554866",
  "Greaves of Mega Hound(GL)": "33554867",
  "Greaves of Pelcost(GL)": "33554868",
  "Visor of Devil Lynxhorn(GL)": "33554869",
  "Visor of Mega Hound(GL)": "33554870",
  "Visor of Pelcost(GL)": "33554871",
  "Eite's Weapon Box (Chakram)": "33554872",
  "Agris's Armor Box (GL)": "33554873",
  "Shineguard Cube(GL)": "33554874",
  "SIGMetal Cube(GL)": "33554875",
  "Forcium Cube(GL)": "33554876",
  "[Costume] Uniform of Capella (GL)": "33554877",
  "[Costume] Uniform of Procyon (GL)": "33554878",
  "Giftbox - [Costume] Uniform of Capella (GL)": "33554879",
  "Giftbox - [Costume] Uniform of Procyon (GL)": "33554880",
  "Not In Use": "33554881",
  "Skill Book (Bloodyrusk)": "33554882",
  "Skill Book (Vital Charge)": "33554883",
  "Skill Book (Art of Rage)": "33554884",
  "Skill Book (Sight Increase)": "33554885",
  "Skill Book (Rage Resist)": "33554886",
  "Skill Book (Battle Fury)": "33554887",
  "Skill Book (Aegis)": "33554888",
  "Skill Book (Mortal Combat)": "33554889",
  "Sirius' Unbinding Stone (High) - Chakram": "33554890",
  "Sirius' Unbinding Stone (Highest) - Chakram": "33554891",
  "Minesta's Unbinding Stone (Medium) - Chakram": "33554892",
  "Minesta's Unbinding Stone (High) - Chakram": "33554893",
  "Minesta's Unbinding Stone (Highest) - Chakram": "33554894",
  "Bloodyrusk Effector": "33554895",
  "Rage Shield Effector": "33554896",
  "Skill Book(Rage Shield)": "33554897",
  "Golden Medal box": "33554898",
  "Silver Medal box": "33554899",
  "Shiny Golden Medal": "33554900",
  "Glittering Silver Medal": "33554901",
  "Fortune Box": "33554905",
  "Fortune Box - Silver Token": "33554906",
  "Fortune Box - Gold Token": "33554907",
  "EXP Saver": "33554908",
  "PvP Battle Entry Ticket": "33554909",
  "PvP Battle Orb": "33554910",
  "PvP Battle Crystal": "33554911",
  "PvP Battle Katana": "33554912",
  "PvP Battle Blade": "33554913",
  "PvP Battle Daikatana": "33554914",
  "PvP Battle Great Sword": "33554915",
  "PvP Battle Chakram": "33554916",
  "PvP Weapon": "33554917",
  "Blessing Bead - Platinum Wing (TW exclusive)": "33554918",
  "[Costume] Liberty Lady & Gentleman": "33554919",
  "[Costume] Accessory of Liberty Lady & Gentleman": "33554920",
  "GifBox- [Costume] Liberty Lady & Gentleman": "33554921",
  "Merit Medal of Luminosity": "33554922",
  "Merit Medal of Storm": "33554923",
  "Gladiator's Merit Medal of Luminosity": "33554924",
  "Officer's Merit Medal of Luminosity": "33554925",
  "Captain's Merit Medal of Luminosity": "33554926",
  "General's Merit Medal of Luminosity": "33554927",
  "Commander's Merit Medal of Luminosity": "33554928",
  "Hero's Merit Medal of Luminosity": "33554929",
  "Legend's Merit Medal of Luminosity": "33554930",
  "God's Merit Medal of Luminosity": "33554931",
  "Gladiator's Merit Medal of Storm": "33554932",
  "Officer's Merit Medal of Storm": "33554933",
  "Captain's Merit Medal of Storm": "33554934",
  "General's Merit Medal of Storm": "33554935",
  "Commander's Merit Medal of Storm": "33554936",
  "Hero's Merit Medal of Storm": "33554937",
  "Legend's Merit Medal of Storm": "33554938",
  "God's Merit Medal of Storm": "33554939",
  "Olivia's Glittering Merit Medal": "33554940",
  "Kyle's Glittering Merit Medal": "33554941",
  "Olivia's Merit Medal of General": "33554942",
  "Olivia's Merit Medal of Commander": "33554943",
  "Olivia's Merit Medal of Hero": "33554944",
  "Olivia's Merit Medal of Legend": "33554945",
  "Olivia's Merit Medal of God": "33554946",
  "Kyle's Merit Medal of General": "33554947",
  "Kyle's Merit Medal of Commander": "33554948",
  "Kyle's Merit Medal of Hero": "33554949",
  "Kyle's Merit Medal of Legend": "33554950",
  "Kyle's Merit Medal of God": "33554951",
  "Honorable Merit Medal": "33554952",
  "Honorable Gladiator's Merit Medal": "33554953",
  "Honorable Officer's Merit Medal": "33554954",
  "Honorable Captain's Merit Medal": "33554955",
  "Honorable General's Merit Medal": "33554956",
  "Honorable Commander's Merit Medal": "33554957",
  "Honorable Hero's Merit Medal": "33554958",
  "Honorable Legend's Merit Medal": "33554959",
  "Honorable God's Merit Medal": "33554960",
  "Merit Medal Exchange Ticket": "33554961",
  "Merit Medal evaluation order": "33554962",
  "Merit Medal evaluation cancel order": "33554963",
  "Craftman's Bluestin Chakram": "33554964",
  "Craftman's Titanium Chakram": "33554965",
  "Craftman's Shadow Titanium Chakram": "33554966",
  "Craftman's Osmium Chakram": "33554967",
  "Craftman's Red Osmium Chakram": "33554968",
  "Craftman's SIGMetal Chakram": "33554969",
  "Craftman's Forcium Chakram": "33554970",
  "Craftman's Archridium Chakram": "33554971",
  "Craftman's Bluestin Visor (GL)": "33554972",
  "Craftman's Bluestin Plate (GL)": "33554973",
  "Craftman's Bluestin Gauntlets (GL)": "33554974",
  "Craftman's Bluestin Greaves (GL)": "33554975",
  "Craftman's Titanium Visor (GL)": "33554976",
  "Craftman's Titanium Plate (GL)": "33554977",
  "Craftman's Titanium Gauntlets (GL)": "33554978",
  "Craftman's Titanium Greaves (GL)": "33554979",
  "Craftman's Shadow Titanium Visor (GL)": "33554980",
  "Craftman's Shadow Titanium Plate (GL)": "33554981",
  "Craftman's Shadow Titanium Gauntlets (GL)": "33554982",
  "Craftman's Shadow Titanium Greaves (GL)": "33554983",
  "Craftman's Osmium Visor (GL)": "33554984",
  "Craftman's Osmium Plate (GL)": "33554985",
  "Craftman's Osmium Gauntlets (GL)": "33554986",
  "Craftman's Osmium Greaves (GL)": "33554987",
  "Craftman's Shineguard Visor (GL)": "33554988",
  "Craftman's Shineguard Plate (GL)": "33554989",
  "Craftman's Shineguard Gauntlets (GL)": "33554990",
  "Craftman's Shineguard Greaves (GL)": "33554991",
  "Craftman's SIGMetal Visor (GL)": "33554992",
  "Craftman's SIGMetal Plate (GL)": "33554993",
  "Craftman's SIGMetal Gauntlets (GL)": "33554994",
  "Craftman's SIGMetal Greaves (GL)": "33554995",
  "Craftman's Forcium Visor (GL)": "33554996",
  "Craftman's Forcium Plate (GL)": "33554997",
  "Craftman's Forcium Gauntlets (GL)": "33554998",
  "Craftman's Forcium Greaves (GL)": "33554999",
  "Craftman's Archridium Visor (GL)": "33555000",
  "Craftman's Archridium Plate (GL)": "33555001",
  "Craftman's Archridium Gauntlets (GL)": "33555002",
  "Craftman's Archridium Greaves (GL)": "33555003",
  "Master's Bluestin Chakram": "33555004",
  "Master's Titanium Chakram": "33555005",
  "Master's Shadow Titanium Chakram": "33555006",
  "Master's Osmium Chakram": "33555007",
  "Master's Red Osmium Chakram": "33555008",
  "Master's SIGMetal Chakram": "33555009",
  "Master's Forcium Chakram": "33555010",
  "Master's Archridium Chakram": "33555011",
  "Master's Bluestin Visor (GL)": "33555012",
  "Master's Bluestin Plate (GL)": "33555013",
  "Master's Bluestin Gauntlets (GL)": "33555014",
  "Master's Bluestin Greaves (GL)": "33555015",
  "Master's Titanium Visor (GL)": "33555016",
  "Master's Titanium Plate (GL)": "33555017",
  "Master's Titanium Gauntlets (GL)": "33555018",
  "Master's Titanium Greaves (GL)": "33555019",
  "Master's Shadow Titanium Visor (GL)": "33555020",
  "Master's Shadow Titanium Plate (GL)": "33555021",
  "Master's Shadow Titanium Gauntlets (GL)": "33555022",
  "Master's Shadow Titanium Greaves (GL)": "33555023",
  "Master's Osmium Visor (GL)": "33555024",
  "Master's Osmium Plate (GL)": "33555025",
  "Master's Osmium Gauntlets (GL)": "33555026",
  "Master's Osmium Greaves (GL)": "33555027",
  "Master's Shineguard Visor (GL)": "33555028",
  "Master's Shineguard Plate (GL)": "33555029",
  "Master's Shineguard Gauntlets (GL)": "33555030",
  "Master's Shineguard Greaves (GL)": "33555031",
  "Master's SIGMetal Visor (GL)": "33555032",
  "Master's SIGMetal Plate (GL)": "33555033",
  "Master's SIGMetal Gauntlets (GL)": "33555034",
  "Master's SIGMetal Greaves (GL)": "33555035",
  "Master's Forcium Visor (GL)": "33555036",
  "Master's Forcium Plate (GL)": "33555037",
  "Master's Forcium Gauntlets (GL)": "33555038",
  "Master's Forcium Greaves (GL)": "33555039",
  "Master's Archridium Visor (GL)": "33555040",
  "Master's Archridium Plate (GL)": "33555041",
  "Master's Archridium Gauntlets (GL)": "33555042",
  "Master's Archridium Greaves (GL)": "33555043",
  "Astral Core (Archridium)": "33555044",
  "Sword Damage Amplifier (Lv. 7)": "33555045",
  "Magic Damage Amplifier (Lv. 7)": "33555046",
  "Mixture (Lv. 7)": "33555047",
  "Hyper Odd Circle": "33555048",
  "Cube of Honor (Highest)": "33555049",
  "Potion of Honor (Lv. 4)": "33555050",
  "Agent Shop Coupon (Bronze)": "33555051",
  "Agent Shop Coupon (Silver)": "33555052",
  "Blessing Bead - WEXP (200%)": "33555053",
  "Devil Horn": "33555054",
  "Korean Flag Headband": "33555055",
  "Soccer Ball Cap": "33555056",
  "[Costume] Devil Horn": "33555057",
  "[Costume] Korean Flag Headband": "33555058",
  "[Costume] Soccer Ball Cap": "33555059",
  "Giftbox - [Costume] Devil Horn": "33555060",
  "Giftbox - [Costume] Korean National Flag Headband": "33555061",
  "Giftbox - [Costume] Soccer Ball Cap": "33555062",
  "National Soccer Player Cheering Suit": "33555063",
  "[Costume] National Soccer Player Cheering Suit": "33555064",
  "Giftbox - [Costume] National Soccer Player Cheering Suit": "33555065",
  "Aodai": "33555066",
  "[Costume] Aodai": "33555067",
  "Giftbox - [Costume] Aodai": "33555068",
  "Giftbox - [Unique Costume] Secret Agent Suit": "33555069",
  "Giftbox - [Unique Costume] Navy Officer": "33555070",
  "Giftbox - [Unique Costume] Nevareth Pirate": "33555071",
  "Giftbox - [Unique Costume] Summer Vacation": "33555072",
  "Giftbox - [Unique Costume] Wild Western": "33555073",
  "Giftbox - [Unique Costume] Hip Hop Spirit": "33555074",
  "Giftbox - [Unique Costume] CABAL High School": "33555075",
  "Giftbox - [Unique Costume] Suit of Vampire": "33555076",
  "Giftbox - [Unique Costume] Dancing Temptation": "33555077",
  "Giftbox - [Unique Costume] Love Dad Yellow Shirt": "33555078",
  "[Unique Costume] Secret Agent Suit": "33555082",
  "[Unique Costume] Navy Officer": "33555086",
  "[Unique Costume] Nevareth Pirate": "33555090",
  "[Unique Costume] Summer Vacation": "33555091",
  "[Unique Costume] Wild Western": "33555092",
  "[Unique Costume] Hip Hop Spirit": "33555093",
  "[Unique Costume] CABAL High School": "33555094",
  "[Unique Costume] Suit of Vampire": "33555095",
  "[Unique Costume] Dancing Temptation": "33555096",
  "[Unique Costume] Love Dad Yellow Shirt": "33555097",
  "Chaos Box - Merit Medal Evaluation Order": "33555098",
  "Mysterious Golden Bottle - Mutant Forest": "33555099",
  "Mysterious Golden Bottle - Pontus Ferrum": "33555100",
  "Mysterious Golden Bottle - Porta Inferno": "33555101",
  "Mysterious Golden Bottle - Arcane Trace": "33555102",
  "Mysterious Golden Bottle - Low": "33555103",
  "Mysterious Golden Bottle - Medium": "33555104",
  "Mysterious Golden Bottle - High": "33555105",
  "Epic Booster": "33555106",
  "Epic Booster (Epic Item Exclusive)": "33555107",
  "Epic Booster (Craft Item Exclusive)": "33555108",
  "Wormhole Generator": "33555109",
  "Fire Stone": "33555110",
  "Ice Stone": "33555111",
  "Wind Stone": "33555112",
  "Earth Stone": "33555113",
  "Elemental Crystal": "33555114",
  "Steamer Crazy (Awakened) Entry Item": "33555115",
  "Lava Hellfire (Awakened) Entry Item": "33555116",
  "Panic Cave (Awakened) Entry Item": "33555117",
  "Catacomb Frost (Awakened) Entry Item": "33555118",
  "Skill Book (Focus)": "33555119",
  "Skill Book (Dread)": "33555120",
  "Rage Potion (Lv.3)": "33555121",
  "Legendary Cube - Steamer Crazy (Awakened)": "33555122",
  "Legendary Cube - Catacomb Frost (Awakened)": "33555123",
  "Legendary Cube - Panic Cave (Awakened)": "33555124",
  "Legendary Cube - Lava Hellfire (Awakened)": "33555125",
  "Welcome to Nightmare": "33555126",
  "[Costume] Welcome to Nightmare": "33555127",
  "Giftbox - [Costume] Welcome to Nightmare": "33555128",
  "[Costume] Fresh from the Sea - Orb": "33555129",
  "[Costume] Fresh from the Sea - Katana": "33555130",
  "[Costume] Fresh from the Sea - Blade": "33555131",
  "[Costume] Fresh from the Sea - Crystal": "33555132",
  "[Costume] Fresh from the Sea - Daikatana": "33555133",
  "[Costume] Fresh from the Sea - Great Sword": "33555134",
  "[Costume] Fresh from the Sea - Chakram": "33555135",
  "Giftbox - [Costume] Fresh from the Sea - Orb": "33555136",
  "Giftbox - [Costume] Fresh from the Sea - Katana": "33555137",
  "Giftbox - [Costume] Fresh from the Sea - Blade": "33555138",
  "Giftbox - [Costume] Fresh from the Sea - Crystal": "33555139",
  "Giftbox - [Costume] Fresh from the Sea - Daikatana": "33555140",
  "Giftbox - [Costume] Fresh from the Sea - Great Sword": "33555141",
  "Giftbox - [Costume] Fresh from the Sea - Chakram": "33555142",
  "Hazardous Valley (Awakened) Entry Item": "33555143",
  "Guild Level Up (Lv. 6)": "33555144",
  "Guild Level Up (Lv. 7)": "33555145",
  "Guild Level Up (Lv. 8)": "33555146",
  "Guild Level Up (Lv. 9)": "33555147",
  "Guild Level Up (Lv. 10)": "33555148",
  "Guild Level Up (Lv. 11)": "33555149",
  "Guild Level Up (Lv. 12)": "33555150",
  "Use HP Boost Potion of War": "33555151",
  "Defense Potion of War": "33557405",
  "Fighter Potion of War": "33555153",
  "Sage Potion of War": "33555154",
  "Awakened Fighter Potion of War": "33555155",
  "Awakened Sage Potion of War": "33555156",
  "Pan's Bag": "33555157",
  "Pan's Luxury Bag": "33555158",
  "Sweet Piece of Cake": "33555159",
  "Fried Egg with Ketchup": "33555160",
  "Sunny Side up Fried Egg": "33555161",
  "Orphidia's Amulet": "33555164",
  "Potion Cube": "33555165",
  "Essence Rune Cube (DP)": "33555166",
  "Option Scroll Cube(High)": "33555167",
  "Option Scroll Cube(Highest)": "33555168",
  "Legendary Cube - Hazardous Valley(Awakened)": "33555169",
  "Orphidia's Unbinding Stone": "33555170",
  "[Costume] Wedding Ceremony": "33555171",
  "[Costume] Epaulet of Wedding Ceremony": "33555172",
  "Giftbox - [Costume] Wedding Ceremony": "33555173",
  "[Costume] Wedding Hair Accessory": "33555174",
  "[Costume] Accessory of Wedding Hair": "33555175",
  "Giftbox - [Costume] Wedding Hair Accessory": "33555176",
  "Pet - Pirate Sheep": "33555177",
  "[Costume] Bloody Skeleton - Orb": "33555178",
  "[Costume] Bloody Skeleton - Katana": "33555179",
  "[Costume] Bloody Skeleton - Blade": "33555180",
  "[Costume] Bloody Skeleton - Crystal": "33555181",
  "[Costume] Bloody Skeleton - Daikatana": "33555182",
  "[Costume] Bloody Skeleton - Great Sword": "33555183",
  "[Costume] Bloody Skeleton - Chakram": "33555184",
  "Giftbox - [Costume] Blood Skeleton - Orb": "33555185",
  "Giftbox - [Costume] Blood Skeleton - Katana": "33555186",
  "Giftbox - [Costume] Blood Skeleton - Blade": "33555187",
  "Giftbox - [Costume] Blood Skeleton - Crystal": "33555188",
  "Giftbox - [Costume] Blood Skeleton - Daikatana": "33555189",
  "Giftbox - [Costume] Blood Skeleton - Great Sword": "33555190",
  "Giftbox - [Costume] Blood Skeleton - Chakram": "33555191",
  "[Costume] Horror Halloween - Chakram": "33555192",
  "Giftbox - [Costume] Horror Halloween - Chakram": "33555193",
  "[Costume] Snow Ice - Chakram": "33555194",
  "Giftbox - [Costume] Snow Ice - Chakram": "33555195",
  "Pan's Bag (GLB Exclusive)": "33555196",
  "Pan's Luxury Bag (GLB Exclusive)": "33555197",
  "Santa's Present (GLB Exclusive)": "33555198",
  "Santa's Special Present (GLB Exclusive)": "33555199",
  "Santa's Extra Special Present (GLB Exclusive)": "33555200",
  "Christmas Ball (Gold)": "33555257",
  "Christmas Ball (Silver)": "33555258",
  "Christmas Flower": "33555259",
  "Gemstone of Transcendence": "33555204",
  "[Costume] Good and Evil": "33555205",
  "[Costume] Angel and Devil Costume": "33555206",
  "Giftbox - [Costume] Good and Evil": "33555207",
  "[Costume] Good and Evil Headgear": "33555208",
  "[Costume] Angel and Devil Helmet Costume": "33555209",
  "Giftbox - [Costume] Good and Evil Headgear": "33555210",
  "Pet - Pirate Parrot": "33555211",
  "Pet - Pirate Monkey": "33555212",
  "Abandoned City Entry Item": "33555213",
  "[Costume] Liberty Lady & Gentleman Hair": "33555214",
  "Force Gem Package": "33555215",
  "Chaos Orb": "33555216",
  "Chaos Crystal": "33555217",
  "Chaos Katana": "33555218",
  "Chaos Blade": "33555219",
  "Chaos Daikatana": "33555220",
  "Chaos Greatsword": "33555221",
  "Chaos Chakram": "33555222",
  "Chaos Armor Suit": "33555225",
  "Chaos Battle Suit": "33555224",
  "Chaos Armor Gloves": "33555228",
  "Chaos Armor Boots": "33555231",
  "Chaos Armor Helmet": "33555234",
  "Chaos Epaulet of Guardian": "33555237",
  "Chaos Talisman": "33555238",
  "Potion of Experience (Lv180+)": "33556164",
  "Potion of Experience (Lv181+)": "33556557",
  "Potion of Experience (Lv182+)": "33555241",
  "Potion of Experience (Lv183+)": "33555242",
  "Potion of Experience (Lv184+)": "33555243",
  "Potion of Experience (Lv185+)": "33555244",
  "Potion of Experience (Lv186+)": "33555245",
  "Potion of Experience (Lv187+)": "33555246",
  "Potion of Experience (Lv188+)": "33555247",
  "Potion of Experience (Lv189+)": "33555248",
  "Potion of Experience (Lv10+)": "33555249",
  "[Costume] Spy of the Kingdom": "33555250",
  "[Costume] Accessory Orb Spy of the Kingdom": "33555251",
  "Gift Box - [Costume] Spy of the Kingdom": "33555252",
  "[Costume] Accessory of Liberty Lady & Gentleman Hair": "33555253",
  "Santa Claus Gift Pack (GLB Exclusive)": "33555254",
  "Santa Claus Special Gift Pack (GLB Exclusive)": "33555255",
  "Santa Claus Very Special Gift Pack (GLB Exclusive)": "33555256",
  "Legendary Cube - Abandoned City": "33555260",
  "Blessing Bead - Box Drop Rate (100%)": "33555261",
  "Blessing Bead - Box Drop Rate (200%)": "33555262",
  "Blessing Bead - Box Drop Rate (300%)": "33555263",
  "[Costume] Sweet Candy - Chakram": "33555264",
  "Giftbox - [Costume] Sweet Candy - Chakram": "33555265",
  "Crest of Life Capsule": "33555266",
  "Crest of Life Slot Converter": "33555267",
  "Crest of Life Option Scroll": "33555268",
  "Crest of Life Safety Kit": "33555269",
  "Crest of Life Capsule Seal Stone": "33555270",
  "Costume Cloth Fragment": "33555271",
  "Old Magic Book (Low)": "33555272",
  "Old Magic Book (Medium)": "33555273",
  "Old Magic Book (High)": "33555274",
  "[Costume] JP Exclusive": "33555290",
  "[Costume] JP Exclusive.": "33555296",
  "Giftbox - [Costume] JP Exclusive": "33555302",
  "Giftbox - [Costume] JP Exclusive.": "33555308",
  "Upgrade Token(High)": "33555309",
  "Upgrade Token(Highest)": "33555310",
  "Upgrade Token(Chaos)": "33555311",
  "Palladium Orb": "33555312",
  "Palladium Crystal": "33555313",
  "Palladium Katana": "33555314",
  "Palladium Blade": "33555315",
  "Palladium Daikatana": "33555316",
  "Palladium Great Sword": "33555317",
  "Palladium Chakram": "33555318",
  "Palladium Plate(WA)": "33555319",
  "Palladium Plate(FS)": "33555320",
  "Palladium Plate(GL)": "33555321",
  "Palladium Coat(FB)": "33555322",
  "Palladium Coat(FA)": "33555323",
  "Palladium Suit(BL)": "33555324",
  "Palladium Suit(WI)": "33555325",
  "Palladium Gauntlet(WA)": "33555326",
  "Palladium Gauntlet(FS)": "33555327",
  "Palladium Gauntlet(GL)": "33555328",
  "Palladium Gloves(FB)": "33555329",
  "Palladium Gloves(FA)": "33555330",
  "Palladium Hands(BL)": "33555331",
  "Palladium Hands(WI)": "33555332",
  "Palladium Greaves(WA)": "33555333",
  "Palladium Greaves(FS)": "33555334",
  "Palladium Greaves(GL)": "33555335",
  "Palladium Boots(FB)": "33555336",
  "Palladium Boots(FA)": "33555337",
  "Palladium Shoes(BL)": "33555338",
  "Palladium Shoes(WI)": "33555339",
  "Palladium Visor(WA)": "33555340",
  "Palladium Visor(FS)": "33555341",
  "Palladium Visor(GL)": "33555342",
  "Palladium Headgear(FB)": "33555343",
  "Palladium Headgear(FA)": "33555344",
  "Palladium Headpiece(BL)": "33555345",
  "Palladium Headpiece(WI)": "33555346",
  "Palladium Epaulet of Laws": "33555347",
  "Palladium Epaulet of Fighter": "33555348",
  "Palladium Epaulet of Sage": "33555349",
  "Material Core(Palladium)": "33555350",
  "Quartz Core(Palladium)": "33555351",
  "Astral Core(Palladium)": "33555352",
  "Rune Slot Extender - Essence": "33555354",
  "Chaos Talisman of Destruction": "33555355",
  "Orb of Ruin": "33555356",
  "Orb of Destruction": "33555357",
  "Crystal of Ruin": "33555358",
  "Crystal of Destruction": "33555359",
  "Orb of Fire": "33555360",
  "Orb of Wind": "33555361",
  "Orb of Earth": "33555362",
  "Orb of Ice": "33555363",
  "Penetration": "33555364",
  "Ignore Penetration": "33555365",
  "Crest of Life Epic Converter - HP": "33555366",
  "Crest of Life Epic Converter - All Attack": "33555367",
  "Crest of Life Epic Converter - Defense": "33555368",
  "Crest of Life Epic Converter - Evasion": "33555369",
  "Crest of Life Epic Converter - Critical DMG": "33555370",
  "Crest of Life Epic Converter - Resist Critical Rate": "33555371",
  "Crest of Life Epic Converter - Penetration": "33555372",
  "Crest of Life Epic Converter - All Skill Amp.": "33555373",
  "Crest of Life Epic Reset Scroll": "33555374",
  "Crest of Life Epic Converter Box (Med)": "33555375",
  "Crest of Life Epic Converter Box": "33555376",
  "Carnelian": "33555377",
  "Insurance Service (PH Exclusive)": "33555378",
  "Blessing Bead - Superior": "33555379",
  "SIGmetal Armor Box(WA)": "33555380",
  "SIGmetal Weapon Box(WA)": "33555381",
  "SIGmetal Armor Box(BL)": "33555382",
  "SIGmetal Weapon Box(BL)": "33555383",
  "SIGmetal Armor Box(WI)": "33555384",
  "SIGmetal Weapon Box(WI)": "33555385",
  "SIGmetal Armor Box(FA)": "33555386",
  "SIGmetal Weapon Box(FA)": "33555387",
  "SIGmetal Armor Box(FS)": "33555388",
  "SIGmetal Weapon Box(FS)": "33555389",
  "SIGmetal Armor Box(FB)": "33555390",
  "SIGmetal Weapon Box(FB)": "33555391",
  "SIGmetal Armor Box(GL)": "33555392",
  "SIGmetal Weapon Box(GL)": "33555393",
  "[Costume] Electronica - Orb": "33555394",
  "[Costume] Electronica - Katana": "33555395",
  "[Costume] Electronica - Blade": "33555396",
  "[Costume] Electronica - Crystal": "33555397",
  "[Costume] Electronica - Daikatana": "33555398",
  "[Costume] Electronica - Great Sword": "33555399",
  "[Costume] Electronica - Chakram": "33555400",
  "Giftbox - [Costume] Electronica - Orb": "33555401",
  "Giftbox - [Costume] Electronica - Katana": "33555402",
  "Giftbox - [Costume] Electronica - Blade": "33555403",
  "Giftbox - [Costume] Electronica - Crystal": "33555404",
  "Giftbox - [Costume] Electronica - Daikatana": "33555405",
  "Giftbox - [Costume] Electronica - Great Sword": "33555406",
  "Giftbox - [Costume] Electronica - Chakram": "33555407",
  "[Costume] Electronica Suit": "33555408",
  "[Costume] Electronica Suit Epaulet": "33555409",
  "Giftbox - [Costume] Electronica Suit": "33555410",
  "[Costume] Electronica Helmet": "33555411",
  "[Costume] Electronica Helmet Accessory": "33555412",
  "Giftbox - [Costume] Electronica Helmet": "33555413",
  "Pet - Franky": "33555414",
  "Board Tuning Kit - Violet": "33555415",
  "Battle Style Change Kit": "33555416",
  "Heroic Holy Water (2H)": "33555417",
  "Heroic Holy Water (15 min.)": "33555418",
  "Heroic Holy Water (30 min.)": "33555419",
  "Ring of Luck +4": "33555420",
  "Critical Ring +4": "33555421",
  "Earring of Guard +9": "33555422",
  "Vampiric Earring +9": "33555423",
  "Extreme Holy Water (2H)": "33555424",
  "Extreme Holy Water (15 min)": "33555425",
  "Extreme Holy Water (30 min)": "33555426",
  "Epaulet of the Dead 3N (Awakened)": "33555427",
  "Amulet of Resist +1": "33555428",
  "Amulet of Resist +2": "33555429",
  "Amulet of Resist +3": "33555430",
  "WA Skill Books": "33555431",
  "BL Skill Books": "33555432",
  "WI Skill Books": "33555433",
  "FA Skill Books": "33555434",
  "FS Skill Books": "33555435",
  "FB Skill Books": "33555436",
  "GL Skill Books": "33555437",
  "WA Universal/Specialized Skill Book Package I": "33555438",
  "BL Universal/Specialized Skill Books": "33555439",
  "WI Universal/Specialized Skill Books": "33555440",
  "FA Universal/Specialized Skill Books": "33555441",
  "FS Universal/Specialized Skill Books": "33555442",
  "FB Universal/Specialized Skill Books": "33555443",
  "GL Universal/Specialized Skill Books": "33555444",
  "Universal Upgrade Skill Books": "33555445",
  "WA Support Skill Book Package I": "33555446",
  "BL Support Skill Books": "33555447",
  "WI Support Skill Book Package I": "33555448",
  "FA Support Skill Book Package I": "33555449",
  "FS Support Skill Book Package I": "33555450",
  "FB Support Skill Books I": "33555451",
  "FB Support Skill Books II": "33555452",
  "GL Support Skill Books": "33555453",
  "WA ATK Skill Books": "33555454",
  "BL ATK Skill Books": "33555455",
  "WI ATK Skill Books": "33555456",
  "FA ATK Skill Books": "33555457",
  "FS ATK Skill Books": "33555458",
  "FB ATK Skill Books": "33555459",
  "GL ATK Skill Books I": "33555460",
  "GL ATK Skill Books II": "33555461",
  "Amulet of Resist +4": "33555462",
  "Amulet of Resist +5": "33555463",
  "[Costume] Natural Skaild (Male Character Only)": "33555464",
  "[Costume] Frowning Skaild (Male Character Only)": "33555465",
  "[Costume] Angry Skaild (Male Character Only)": "33555466",
  "[Costume] Natural Elena (Female Character Only)": "33555467",
  "[Costume] Frowning Elena (Female Character Only)": "33555468",
  "[Costume] Angry Elena (Female Character Only)": "33555469",
  "[Costume] Natural Yuan (Female Character Only)": "33555470",
  "[Costume] Frowning Yuan (Female Character Only)": "33555471",
  "[Costume] Angry Yuan (Female Character Only)": "33555472",
  "[Costume] Natural Arionell (Female Character Only)": "33555473",
  "[Costume] Frowning Arionell (Female Character Only)": "33555474",
  "[Costume] Angry Arionell (Female Character Only)": "33555475",
  "[Costume] Natural Aizhan (Female Character Only)": "33555476",
  "[Costume] Frowning Aizhan (Female Character Only)": "33555477",
  "[Costume] Angry Aizhan (Female Character Only)": "33555478",
  "[Costume] Gas Mask": "33555479",
  "[Costume] Gatekeeper of Hell": "33555480",
  "[Costume] Stag of Bloody Ice": "33555481",
  "[Costume] Elk Helmet": "33555482",
  "[Costume] Golden Buddha": "33555483",
  "[Costume] Stripe Suit": "33555484",
  "[Costume] Accessory of Natural Skaild  (Male Character Only)": "33555485",
  "[Costume] Accessory of Frowning Skaild  (Male Character Only)": "33555486",
  "[Costume] Accessory of Angry Skaild  (Male Character Only)": "33555487",
  "[Costume] Accessory of Natural Elena (Female Character Only)": "33555488",
  "[Costume] Accessory of Frowning Elena (Female Character Only)": "33555489",
  "[Costume] Accessory of Angry Elena (Female Character Only)": "33555490",
  "[Costume] Accessory of Natural Yuan (Female Character Only)": "33555491",
  "[Costume] Accessory of Frowning Yuan (Female Character Only)": "33555492",
  "[Costume] Accessory of Angry Yuan (Female Character Only)": "33555493",
  "[Costume] Accessory of Natural Arionell (Female Character Only)": "33555494",
  "[Costume] Accessory of Frowning Arionell (Female Character Only)": "33555495",
  "[Costume] Accessory of Angry Arionell (Female Character Only)": "33555496",
  "[Costume] Accessory of Natura Aizhan (Female Character Only)": "33555497",
  "[Costume] Accessory of Frowning Aizhan (Female Character Only)": "33555498",
  "[Costume] Accessory of Angry Aizhan (Female Character Only)": "33555499",
  "[Costume] Accessory of Gas Mask": "33555500",
  "[Costume] Accessory of Gatekeeper of Hell": "33555501",
  "[Costume] Accessory of Stag of Bloody Ice": "33555502",
  "[Costume] Accessory of Elk Helmet": "33555503",
  "[Costume] Accessory of Golden Buddha": "33555504",
  "[Costume] Accessory of Stripe Suit": "33555505",
  "Giftbox - [Costume] Natural Skaild (Male Character Only)": "33555506",
  "Giftbox - [Costume] Frowning Skaild (Male Character Only)": "33555507",
  "Giftbox - [Costume] Angry Skaild (Male Character Only)": "33555508",
  "Giftbox - [Costume] Natural Elena (Female Character Only)": "33555509",
  "Giftbox - [Costume] Frowning Elena (Female Character Only)": "33555510",
  "Giftbox - [Costume] Angry Elena (Female Character Only)": "33555511",
  "Giftbox - [Costume] Natural Yuan (Female Character Only)": "33555512",
  "Giftbox - [Costume] Frowning Yuan (Female Character Only)": "33555513",
  "Giftbox - [Costume] Angry Yuan (Female Character Only)": "33555514",
  "Giftbox - [Costume] Natural Arionell (Female Character Only)": "33555515",
  "Giftbox - [Costume] Frowning Arionell (Female Character Only)": "33555516",
  "Giftbox - [Costume] Angry Arionell (Female Character Only)": "33555517",
  "Giftbox - [Costume] Natural Aizhan (Female Character Only)": "33555518",
  "Giftbox - [Costume] Frowning Aizhan (Female Character Only)": "33555519",
  "Giftbox - [Costume] Angry Aizhan (Female Character Only)": "33555520",
  "Giftbox - [Costume] Gas Mask": "33555521",
  "Giftbox - [Costume] Gatekeeper of Hell": "33555522",
  "Giftbox - [Costume] Stag of Bloody Ice": "33555523",
  "Giftbox - [Costume] Elk Helmet": "33555524",
  "Giftbox - [Costume] Golden Buddha": "33555525",
  "Giftbox - [Costume] Stripe Suit": "33555526",
  "[Costume] Drunken Pirate": "33555527",
  "[Costume] Mergaheph": "33555528",
  "[Costume] Clown": "33555529",
  "[Costume] CB-222": "33555530",
  "[Costume] Mechbuffalo": "33555531",
  "[Costume] Twisted Zombie": "33555532",
  "[Costume] Minesta Tabby": "33555533",
  "[Costume] Minesta Shorthair": "33555534",
  "[Costume] Ebony Garlie": "33555535",
  "[Costume] Ruby Garlie": "33555536",
  "[Costume] Crystal Garlie": "33555537",
  "[Costume] Accessory of Drunken Pirate": "33555538",
  "[Costume] Accessory of Mergaheph": "33555539",
  "[Costume] Accessory of Clown": "33555540",
  "[Costume] Accessory of CB-222": "33555541",
  "[Costume] Accessory of Mechbuffalo": "33555542",
  "[Costume] Accessory of Twisted Zombie": "33555543",
  "[Costume] Accessory of Minesta Tabby": "33555544",
  "[Costume] Accessory of Minesta Shorthair": "33555545",
  "[Costume] Accessory of Ebony Garlie": "33555546",
  "[Costume] Accessory of Ruby Garlie": "33555547",
  "[Costume] Accessory of Crystal Garlie": "33555548",
  "Giftbox - [Costume] Drunken Pirate": "33555549",
  "Giftbox - [Costume] Mergaheph": "33555550",
  "Giftbox - [Costume] Clown": "33555551",
  "Giftbox - [Costume] CB-222": "33555552",
  "Giftbox - [Costume] Mechbuffalo": "33555553",
  "Giftbox - [Costume] Twisted Zombie": "33555554",
  "Giftbox - [Costume] Minesta Tabby": "33555555",
  "Giftbox - [Costume] Minesta Shorthair": "33555556",
  "Giftbox - [Costume] Ebony Garlie": "33555557",
  "Giftbox - [Costume] Ruby Garlie": "33555558",
  "Giftbox - [Costume] Crystal Garlie": "33555559",
  "Training Stone": "33555560",
  "Leedy": "33555561",
  "Skaild": "33555562",
  "Aizhan": "33555563",
  "Elena": "33555564",
  "Mercenary Card Piece - Leedy": "33555565",
  "Mercenary Card Piece - Skaild": "33555566",
  "Mercenary Card Piece - Aizhan": "33555567",
  "Mercenary Card Piece - Elena": "33555568",
  "Corrupted Warrior": "33555569",
  "Corrupted Blader": "33555570",
  "Corrupted Wizard": "33555571",
  "Corrupted Force Archer": "33555572",
  "Corrupted Force Shielder": "33555573",
  "Corrupted Force Blader": "33555574",
  "Yuan": "33555575",
  "Arionell": "33555576",
  "Rin": "33555577",
  "Freed": "33555578",
  "Black Bard": "33555579",
  "Maximum Level Rabbit": "33555582",
  "Event - Yuan": "33555583",
  "Event - Arionell": "33555584",
  "Event - Rin": "33555585",
  "Event - Freed": "33555586",
  "Event - Black Bard": "33555587",
  "Pertz von Zatellerean": "33555588",
  "Perzen Bhha": "33555589",
  "Bricry": "33555590",
  "Druga": "33555591",
  "Syarsor": "33555592",
  "Angelo": "33555593",
  "Orb of Summoning": "33555594",
  "Legendary Cube - Tower of the Dead B3F (Part2)": "33555595",
  "GM's Blessing (Lv. 1) Holy Water": "33555596",
  "GM's Blessing (Lv. 2) Holy Water": "33555597",
  "GM's Blessing (Lv. 3) Holy Water": "33555598",
  "Minesta's Red Amethyst Charm": "33555599",
  "Minesta's Green Amethyst Charm": "33555600",
  "Minesta's Yellow Amethyst Charm": "33555601",
  "Minesta's Blue Amethyst Charm": "33555602",
  "Minesta's Purple Sapphire Charm": "33555603",
  "Minesta's Purple Ruby Charm": "33555604",
  "Minesta's Purple Emerald Charm": "33555605",
  "Minesta's Purple Amber Charm": "33555606",
  "Chaos Box - Mercenary Card Piece (Leedy)": "33555607",
  "Chaos Box - Mercenary Card Piece (Skaild)": "33555608",
  "Chaos Box - Mercenary Card Piece (Aizhan)": "33555609",
  "Chaos Box - Mercenary Card Piece (Elena)": "33555610",
  "[Costume] Italy National Team": "33555612",
  "[Costume] Turkey National Team": "33555613",
  "[Costume] German Soccer Uniform": "33555614",
  "[Costume] England National Team": "33555615",
  "[Costume] Accessory of Italian Soccer Uniform": "33555616",
  "[Costume] Accessory of Turkish Soccer Uniform": "33555617",
  "[Costume] Accessory of German Soccer Uniform": "33555618",
  "[Costume] Accessory of English Soccer Uniform": "33555619",
  "Giftbox - [Costume] Italian Soccer Uniform": "33555620",
  "Giftbox - [Costume] Turkish Soccer Uniform": "33555621",
  "Giftbox - [Costume] German Soccer Uniform": "33555622",
  "Giftbox - [Costume] English Soccer Uniform": "33555623",
  "[Costume] Cherry Blossom - Orb": "33555624",
  "[Costume] Cherry Blossom - Katana": "33555625",
  "[Costume] Cherry Blossom - Blade": "33555626",
  "[Costume] Cherry Blossom - Crystal": "33555627",
  "[Costume] Cherry Blossom - Daikatana": "33555628",
  "[Costume] Cherry Blossom - Greatsword": "33555629",
  "[Costume] Cherry Blossom - Chakram": "33555630",
  "Giftbox - [Costume] Cherry Blossom - Orb": "33555631",
  "Giftbox - [Costume] Cherry Blossom - Katana": "33555632",
  "Giftbox - [Costume] Cherry Blossom - Blade": "33555633",
  "Giftbox - [Costume] Cherry Blossom - Crystal": "33555634",
  "Giftbox - [Costume] Cherry Blossom - Daikatana": "33555635",
  "Giftbox - [Costume] Cherry Blossom - Greatsword": "33555636",
  "Gift Box - [Costume] Cherry Blossom - Chakram": "33555637",
  "Sirius' Unbinding Stone (High) - Weapon": "33555638",
  "Sirius' Unbinding Stone (Highest) - Weapon": "33555639",
  "Sirius' Unbinding Stone (High) - Armor": "33555640",
  "Sirius' Unbinding Stone (Highest) - Armor": "33555641",
  "Minesta's Unbinding Stone (Medium) - Weapon": "33555642",
  "Minesta's Unbinding Stone (High) - Weapon": "33555643",
  "Minesta's Unbinding Stone (Highest) - Weapon": "33555644",
  "Minesta's Unbinding Stone (Medium) - Armor": "33555645",
  "Minesta's Unbinding Stone (High) - Armor": "33555646",
  "Minesta's Unbinding Stone (Highest) - Armor": "33555647",
  "Rune Slot Extender - Blended": "33555648",
  "Honorable Warrior's Key": "33555649",
  "Baldus Token": "33555650",
  "Legend Arena Potion Box Lv.1": "33555651",
  "Legend Arena Potion Box Lv.2": "33555652",
  "Legend Arena Potion Box Lv.3": "33555653",
  "[Costume] Panda Doll Mask": "33555654",
  "[Costume] Bear Doll Mask": "33555655",
  "[Costume] Shark Doll Mask": "33555656",
  "[Costume] Rabbit Doll Mask": "33555657",
  "[Costume] Lion Doll Mask": "33555658",
  "[Costume] Tiger Doll Mask": "33555659",
  "[Costume] Dinosaur Doll Mask": "33555660",
  "[Costume] Lion Face": "33555661",
  "[Costume] Accessory of Panda Doll Mask": "33555662",
  "[Costume] Accessory of Bear Doll Mask": "33555663",
  "[Costume] Accessory of Shark Doll Mask": "33555664",
  "[Costume] Accessory of Rabbit Doll Mask": "33555665",
  "[Costume] Accessory of Lion Doll Mask": "33555666",
  "[Costume] Accessory of Tiger Doll Mask": "33555667",
  "[Costume] Accessory of Dinosaur Doll Mask": "33555668",
  "[Costume] Accessory of Lion Face": "33555669",
  "Giftbox - [Costume] Panda Doll Mask": "33555670",
  "Giftbox - [Costume] Bear Doll Mask": "33555671",
  "Giftbox - [Costume] Shark Doll Mask": "33555672",
  "Giftbox - [Costume] Rabbit Doll Mask": "33555673",
  "Giftbox - [Costume] Lion Doll Mask": "33555674",
  "Giftbox - [Costume] Tiger Doll Mask": "33555675",
  "Giftbox - [Costume] Dinosaur Doll Mask": "33555676",
  "Giftbox - [Costume] Lion Face": "33555677",
  "[Costume] Aizhan Dress (Female Character Only)": "33555678",
  "[Costume] Accessory of Aizhan Dress (Female Character Only)": "33555679",
  "Giftbox - [Costume] Aizhan Dress (Female Character Only)": "33555680",
  "[Costume] CABAL School Uniform": "33555681",
  "[Costume] Accessory of CABAL School Uniform": "33555682",
  "Giftbox - [Costume] CABAL School Uniform": "33555683",
  "[Costume] Imperial Dress": "33555684",
  "[Costume] Accessory of Imperial Dress": "33555685",
  "Giftbox - [Costume] Imperial Dress": "33555686",
  "[Costume] Gold Dragon - Orbs": "33555687",
  "[Costume] Gold Dragon - Katana": "33555688",
  "[Costume] Gold Dragon - Blade": "33555689",
  "[Costume] Gold Dragon - Crystal": "33555690",
  "[Costume] Gold Dragon - Daikatana": "33555691",
  "[Costume] Gold Dragon - Great Sword": "33555692",
  "[Costume] Gold Dragon - Chakram": "33555693",
  "Giftbox [Costume] Gold Dragon - Orbs": "33555694",
  "Giftbox [Costume] Gold Dragon - Katana": "33555695",
  "Giftbox [Costume] Gold Dragon - Blade": "33555696",
  "Giftbox [Costume] Gold Dragon - Crystal": "33555697",
  "Giftbox [Costume] Gold Dragon - Daikatana": "33555698",
  "Giftbox [Costume] Gold Dragon - Great Sword": "33555699",
  "Giftbox [Costume] Gold Dragon - Chakram": "33555700",
  "[Costume] Demon's Bone - Orb": "33555701",
  "[Costume] Demon's Bone - Katana": "33555702",
  "[Costume] Demon's Bone - Blade": "33555703",
  "[Costume] Demon's Bone - Crystal": "33555704",
  "[Costume] Demon's Bone - Daikatana": "33555705",
  "[Costume] Demon's Bone - Greatsword": "33555706",
  "[Costume] Demon's Bone - Chakram": "33555707",
  "Giftbox - [Costume] Demon's Bone - Orb": "33555708",
  "Giftbox - [Costume] Demon's Bone - Katana": "33555709",
  "Giftbox - [Costume] Demon's Bone - Blade": "33555710",
  "Giftbox - [Costume] Demon's Bone - Crystal": "33555711",
  "Giftbox - [Costume] Demon's Bone - Daikatana": "33555712",
  "Giftbox - [Costume] Demon's Bone - Greatsword": "33555713",
  "Giftbox - [Costume] Demon's Bone - Chakram": "33555714",
  "Arcana of Chaos": "33555715",
  "Arcana of Laws": "33555716",
  "Defensive Earring +1": "33555717",
  "Defensive Earring +2": "33555718",
  "Defensive Earring +3": "33555719",
  "Defensive Earring +4": "33555720",
  "Defensive Earring +5": "33555721",
  "Defensive Earring +6": "33555722",
  "Defensive Earring +7": "33555723",
  "Skill Book(Lightning Revenge)": "33555724",
  "Skill Book(Overwhelming)": "33555725",
  "Skill Book(Hawk Hunter)": "33555726",
  "Skill Book(Shield Taunt)": "33555727",
  "Skill Book(Chakram Slam)": "33555728",
  "Skill Book(Multiple Canon)": "33555729",
  "Skill Book(Arrow Forest)": "33555730",
  "Skill Book(Piercing Shield)": "33555731",
  "[Costume] Aizhan Face (Female Character Only)": "33555732",
  "[Costume] Accessory of Aizhan Face (Female Character Only)": "33555733",
  "Giftbox - [Costume] Aizhan Face (Female Character Only)": "33555734",
  "Blended Rune Box - Character Activate": "33555735",
  "Blended Rune Box - Monster Activate": "33555736",
  "[Costume] Ruler of Night": "33555737",
  "[Costume] Accessory of Ruler of Night": "33555738",
  "Giftbox - [Costume] Ruler of Night": "33555739",
  "[Costume] Ruler of Night - Helmet (Male Character Only)": "33555740",
  "Giftbox - [Costume] Accessory of Ruler of Night - Helmet (Male Character Only)": "33555741",
  "Giftbox - [Costume] Ruler of Night - Helmet (Male Character Only)": "33555742",
  "[Costume] Ruler of Night - Helmet (Female Character Only)": "33555743",
  "Giftbox - [Costume] Accessory of Ruler of Night - Helmet (Female Character Only)": "33555744",
  "Giftbox - [Costume] Ruler of Night - Helmet (Female Character Only)": "33555745",
  "Resist Critical Rate": "33557027",
  "Crest of Life Epic Converter Box (CN Exclusive)": "33555747",
  "Bike Epic Converter (Lv. 4) - Resist Critical Rate": "33555748",
  "Bike Epic Converter (Lv. 4) - Resist Critical DMG": "33555749",
  "[Costume] Archfiend Armor": "33555750",
  "[Costume] Accessory of Archfiend Armor": "33555751",
  "Giftbox - [Costume] Archfiend Armor": "33555752",
  "[Costume] Archfiend Armor Helmet": "33555753",
  "[Costume] Accessory of Archfiend Armor Helmet": "33555754",
  "Giftbox - [Costume] Archfiend Armor Helmet": "33555755",
  "Chaos Safeguard": "33555756",
  "Crystal of Frozen Flame": "33555757",
  "Skill Book (Adrenaline of Rage)": "33555758",
  "Bluestin Coat (FG)": "33555759",
  "Bluestin Gloves (FG)": "33555760",
  "Bluestin Boots (FG)": "33555761",
  "Bluestin Headgear (FG)": "33555762",
  "Titanium Coat (FG)": "33555763",
  "Titanium Gloves (FG)": "33555764",
  "Titanium Boots (FG)": "33555765",
  "Titanium Headgear (FG)": "33555766",
  "Shadow Titanium Coat (FG)": "33555767",
  "Shadow Titanium Gloves (FG)": "33555768",
  "Shadow Titanium Boots (FG)": "33555769",
  "Shadow Titanium Headgear (FG)": "33555770",
  "Osmium Coat (FG)": "33555771",
  "Osmium Gloves (FG)": "33555772",
  "Osmium Boots (FG)": "33555773",
  "Osmium Headgear (FG)": "33555774",
  "Teragrace Coat (FG)": "33555775",
  "Teragrace Gloves (FG)": "33555776",
  "Teragrace Boots (FG)": "33555777",
  "Teragrace Headgear (FG)": "33555778",
  "SIGMetal Coat (FG)": "33555779",
  "SIGMetal Gloves (FG)": "33555780",
  "SIGMetal Boots (FG)": "33555781",
  "SIGMetal Headgear (FG)": "33555782",
  "Drei Frame's Coat (FG)": "33555783",
  "Drei Frame's Gloves (FG)": "33555784",
  "Drei Frame's Boots (FG)": "33555785",
  "Drei Frame's Headgear (FG)": "33555786",
  "Forcium Coat (FG)": "33555787",
  "Forcium Gloves (FG)": "33555788",
  "Forcium Boots (FG)": "33555789",
  "Forcium Headgear (FG)": "33555790",
  "Archridium Coat (FG)": "33555791",
  "Archridium Gloves (FG)": "33555792",
  "Archridium Boots (FG)": "33555793",
  "Archridium Headgear (FG)": "33555794",
  "Palladium Coat (FG)": "33555795",
  "Palladium Gloves (FG)": "33555796",
  "Palladium Boots (FG)": "33555797",
  "Palladium Headgear (FG)": "33555798",
  "Battle Mode 3 Force Gunner": "33555799",
  "Battle Mode 3 Force Gunner Capella": "33555800",
  "Battle Mode 3 Force Gunner Procyon": "33555801",
  "Battle Mode 3 Force Gunner Headgear": "33555802",
  "Battle Mode 3 Force Gunner Headgear Capella": "33555803",
  "Battle Mode 3 Force Gunner Headgear Procyon": "33555804",
  "Capella (FG)": "33555805",
  "Procyon (FG)": "33555806",
  "Craftsman's Bluestin Coat (FG)": "33555807",
  "Craftsman's Bluestin Gloves (FG)": "33555808",
  "Craftsman's Bluestin Boots (FG)": "33555809",
  "Craftsman's Bluestin Headgear (FG)": "33555810",
  "Craftsman's Titanium Coat (FG)": "33555811",
  "Craftsman's Titanium Gloves (FG)": "33555812",
  "Craftsman's Titanium Boots (FG)": "33555813",
  "Craftsman's Titanium Headgear (FG)": "33555814",
  "Craftsman's Shadow Titanium Coat (FG)": "33555815",
  "Craftsman's Shadow Titanium Gloves (FG)": "33555816",
  "Craftsman's Shadow Titanium Boots (FG)": "33555817",
  "Craftsman's Shadow Titanium Headgear (FG)": "33555818",
  "Craftsman's Osmium Coat (FG)": "33555819",
  "Craftsman's Osmium Gloves (FG)": "33555820",
  "Craftsman's Osmium Boots (FG)": "33555821",
  "Craftsman's Osmium Headgear (FG)": "33555822",
  "Craftsman's Teragrace Coat (FG)": "33555823",
  "Craftsman's Teragrace Gloves (FG)": "33555824",
  "Craftsman's Teragrace Boots (FG)": "33555825",
  "Craftsman's Teragrace Headgear (FG)": "33555826",
  "Craftsman's SIGMetal Coat (FG)": "33555827",
  "Craftsman's SIGMetal Gloves (FG)": "33555828",
  "Craftsman's SIGMetal Boots (FG)": "33555829",
  "Craftsman's SIGMetal Headgear (FG)": "33555830",
  "Craftsman's Forcium Coat (FG)": "33555831",
  "Craftsman's Forcium Gloves (FG)": "33555832",
  "Craftsman's Forcium Boots (FG)": "33555833",
  "Craftsman's Forcium Headgear (FG)": "33555834",
  "Craftsman's Archridium Coat (FG)": "33555835",
  "Craftsman's Archridium Gloves (FG)": "33555836",
  "Craftsman's Archridium Boots (FG)": "33555837",
  "Craftsman's Archridium Headgear (FG)": "33555838",
  "Master's Bluestin Coat (FG)": "33555839",
  "Master's Bluestin Gloves (FG)": "33555840",
  "Master's Bluestin Boots (FG)": "33555841",
  "Master's Bluestin Headgear (FG)": "33555842",
  "Master's Titanium Coat (FG)": "33555843",
  "Master's Titanium Gloves (FG)": "33555844",
  "Master's Titanium Boots (FG)": "33555845",
  "Master's Titanium Headgear (FG)": "33555846",
  "Master's Shadow Titanium Coat (FG)": "33555847",
  "Master's Shadow Titanium Gloves (FG)": "33555848",
  "Master's Shadow Titanium Boots (FG)": "33555849",
  "Master's Shadow Titanium Headgear (FG)": "33555850",
  "Master's Osmium Coat (FG)": "33555851",
  "Master's Osmium Gloves (FG)": "33555852",
  "Master's Osmium Boots (FG)": "33555853",
  "Master's Osmium Headgear (FG)": "33555854",
  "Master's Teragrace Coat (FG)": "33555855",
  "Master's Teragrace Gloves (FG)": "33555856",
  "Master's Teragrace Boots (FG)": "33555857",
  "Master's Teragrace Headgear (FG)": "33555858",
  "Master's SIGMetal Coat (FG)": "33555859",
  "Master's SIGMetal Gloves (FG)": "33555860",
  "Master's SIGMetal Boots (FG)": "33555861",
  "Master's SIGMetal Headgear (FG)": "33555862",
  "Master's Forcium Coat (FG)": "33555863",
  "Master's Forcium Gloves (FG)": "33555864",
  "Master's Forcium Boots (FG)": "33555865",
  "Master's Forcium Headgear (FG)": "33555866",
  "Master's Archridium Coat (FG)": "33555867",
  "Master's Archridium Gloves (FG)": "33555868",
  "Master's Archridium Boots (FG)": "33555869",
  "Master's Archridium Headgear (FG)": "33555870",
  "Teragrace Cube (FG)": "33555871",
  "SIGMetal Cube (FG)": "33555872",
  "Forcium Cube (FG)": "33555873",
  "SIGMetal Armor Box (FG)": "33555874",
  "SIGMetal Weapons Box (FG)": "33555875",
  "Skill Book (Hit Man Speciality Stage 2)": "33555876",
  "Skill Book (Hit Man Speciality Stage 3)": "33555877",
  "Skill Book (Magic Arrow (FG))": "33555878",
  "Skill Book (Terra Arrow (FG))": "33555879",
  "Skill Book (Aqua Arrow (FG))": "33555880",
  "Skill Book (Wind Arrow (FG))": "33555881",
  "Skill Book (Fire Arrow (FG))": "33555882",
  "Skill Book (Freezing Arrow (FG))": "33555883",
  "Skill Book (Lightning Arrow (FG))": "33555884",
  "Skill Book (Magic Blast (FG))": "33555885",
  "Skill Book (Stone Blast (FG))": "33555886",
  "Skill Book (Aqua Blast (FG))": "33555887",
  "Skill Book (Wind Blast (FG))": "33555888",
  "Skill Book (Fire Blast (FG))": "33555889",
  "Skill Book (Ice Blast (FG))": "33555890",
  "Skill Book (Lightning Blast (FG))": "33555891",
  "Skill Book (Magic Lance (FG))": "33555892",
  "Skill Book (Terra Lance (FG))": "33555893",
  "Skill Book (Aqua Lance (FG))": "33555894",
  "Skill Book (Wind Lance (FG))": "33555895",
  "Skill Book (Fire Lance (FG))": "33555896",
  "Skill Book (Freezing Lance (FG))": "33555897",
  "Skill Book (Lightning Lance (FG))": "33555898",
  "Skill Book (Magic Cannon (FG))": "33555899",
  "Skill Book (Stone Cannon (FG))": "33555900",
  "Skill Book (Aqua Cannon (FG))": "33555901",
  "Skill Book (Wind Cannon (FG))": "33555902",
  "Skill Book (Fire Cannon (FG))": "33555903",
  "Skill Book (Crystal Cannon (FG))": "33555904",
  "Skill Book (Lightning Cannon (FG))": "33555905",
  "Skill Book (Aim Shot)": "33555906",
  "Skill Book (Targeting Shot)": "33555907",
  "Skill Book (Slug Shot)": "33555908",
  "Skill Book (Oppression Shot)": "33555909",
  "Skill Book (Rolling Launcher)": "33555910",
  "Skill Book (Force Slug)": "33555911",
  "Skill Book (Shot Impact)": "33555912",
  "Skill Book (Living Fog)": "33555913",
  "Skill Book (Broken Arrow)": "33555914",
  "Skill Book (Ion Bullet)": "33555915",
  "Skill Book (Lightning Satellite)": "33555916",
  "Skill Book (Sunset Missiles)": "33555917",
  "Skill Book (Grenade Down)": "33555918",
  "Skill Book (Buster Bombard)": "33555919",
  "Skill Book (Wide Cover Shot)": "33555920",
  "Skill Book (Mouse Mk-2)": "33555921",
  "Skill Book (Call Valkyrie)": "33555922",
  "Skill Book (Revenger)": "33555923",
  "Skill Book (Mechanic Crop)": "33555924",
  "Skill Book (Storm Blitz)": "33555925",
  "[Costume] Uniform of Capella (FG)": "33555926",
  "[Costume] Uniform of Procyon (FG)": "33555927",
  "Gift Box - [Costume] Uniform of Capella (FG)": "33555928",
  "Gift Box - [Costume] Uniform of Procyon (FG)": "33555929",
  "Skill Book (Hit Man)": "33555930",
  "Skill Book (Art of Desperado)": "33555931",
  "Skill Book (Reactive Technique)": "33555932",
  "Skill Book (Inspire)": "33555933",
  "Skill Book (Lock On)": "33555934",
  "Skill Book (Target Focus)": "33555935",
  "Skill Book (Sniper Plan)": "33555936",
  "Skill Book (Castle Rock)": "33555937",
  "Skill Book (Warfare Matrix)": "33555938",
  "Character Slot Opener (No. 8)": "33555939",
  "Reservation Benefit - SIGMetal Armor Box (FG)": "33555940",
  "Reservation Benefit - SIGMetal Weapon Box (FG)": "33555941",
  "Chaos Box - Upgrade Supplement": "33555942",
  "Troglo King's Coat (FG)": "33555943",
  "Spamaton's Coat (FG)": "33555944",
  "Crag King Crab's Coat (FG)": "33555945",
  "Poki Triple's Gloves (FG)": "33555946",
  "Spamaton's Gloves (FG)": "33555947",
  "Crag King Crab's Gloves (FG)": "33555948",
  "Poki Triple's Boots (FG)": "33555949",
  "Spamaton's Boots (FG)": "33555950",
  "Crag King Crab's Boots (FG)": "33555951",
  "Troglo King's Headgear (FG)": "33555952",
  "Spamaton's Headgear (FG)": "33555953",
  "Crag King Crab's Headgear (FG)": "33555954",
  "Agris' Armor Box (FG)": "33555955",
  "Art of Desperado Effector": "33555956",
  "Lock On Effector": "33555957",
  "Divine Converter - Weapon": "33555958",
  "Divine Converter - Armor": "33555959",
  "Divine Converter - Bike": "33555960",
  "Divine Core": "33555961",
  "Fragment of Divine Core": "33555962",
  "Arcana Box of Laws": "33555963",
  "Arcana Box of Chaos": "33555964",
  "Carnelian Box": "33557803",
  "GM's Blessing (Lv.4) Holy Water": "33555966",
  "[Costume] Bounty Hunter": "33555967",
  "[Costume] Accessory of Bounty Hunter": "33555968",
  "Gift Box - [Costume] Bounty Hunter": "33555969",
  "[Costume] Bounty Hunter Helmet": "33555970",
  "[Costume] Accessory of Bounty Hunter Helmet": "33555971",
  "Gift Box - [Costume] Bounty Hunter Helmet": "33555972",
  "[Costume] Jiangshi": "33555973",
  "[Costume] Accessory of Jiangshi": "33555974",
  "Gift Box - [Costume] Jiangshi": "33555975",
  "[Costume] Jiangshi Hat": "33555976",
  "[Costume] Accessory of Jiangshi Hat": "33555977",
  "Gift Box - [Costume] Jiangshi Hat": "33555978",
  "Wexp Saver": "33555979",
  "Blessing Bead - Pet EXP (1000%)": "33555980",
  "Yekaterina Red Membership": "33555981",
  "Event_ChocoChoco": "33555982",
  "Event_Charm": "33555983",
  "Event_Fortune Cookie": "33555984",
  "Event_Moon": "33555985",
  "Event_Chinese Yuebing": "33555986",
  "Event_Snow Crystal": "33555987",
  "Event_Star Decoration": "33555988",
  "Event_Sun": "33555989",
  "Event_Sushi": "33555990",
  "Event_Teru Teru Bozu": "33555991",
  "Divine Seal Stone": "33555992",
  "[Costume] KA-17 - Orb": "33555993",
  "[Costume] KA-17 - Katana": "33555994",
  "[Costume] KA-17 - Blade": "33555995",
  "[Costume] KA-17 - Crystal": "33555996",
  "[Costume] KA-17 - Daikatana": "33555997",
  "[Costume] KA-17 - Great Sword": "33555998",
  "[Costume] KA-17 - Chakram": "33555999",
  "Gift Box - [Costume] KA-17 - Orb": "33556000",
  "Gift Box - [Costume] KA-17 - Katana": "33556001",
  "Gift Box - [Costume] KA-17 - Blade": "33556002",
  "Gift Box - [Costume] KA-17 - Crystal": "33556003",
  "Gift Box - [Costume] KA-17 - Daikatana": "33556004",
  "Gift Box - [Costume] KA-17 - Great Sword": "33556005",
  "Gift Box - [Costume] KA-17 - Chakram": "33556006",
  "[Costume] Space Suit": "33556007",
  "[Costume] Accessory of Space Suit": "33556008",
  "Giftbox - [Costume] Space Suit": "33556009",
  "[Costume] Space Helmet": "33556010",
  "[Costume] Accessory of Space Helmet": "33556011",
  "Giftbox - [Costume] Space Helmet": "33556012",
  "Craftsman's Palladium Orb": "33556013",
  "Craftsman's Palladium Crystal": "33556014",
  "Craftsman's Palladium Katana": "33556015",
  "Craftsman's Palladium Blade": "33556016",
  "Craftsman's Palladium Daikatana": "33556017",
  "Craftsman's Palladium Great Sword": "33556018",
  "Craftsman's Palladium Chakram": "33556019",
  "Craftsman's Palladium Plate(WA)": "33556020",
  "Craftsman's Palladium Plate(FS)": "33556021",
  "Craftsman's Palladium Plate(GL)": "33556022",
  "Craftsman's Palladium Coat(FB)": "33556023",
  "Craftsman's Palladium Coat(FA)": "33556024",
  "Craftsman's Palladium Suit(BL)": "33556025",
  "Craftsman's Palladium Suit(WI)": "33556026",
  "Craftsman's Palladium Gauntlet(WA)": "33556027",
  "Craftsman's Palladium Gauntlet(FS)": "33556028",
  "Craftsman's Palladium Gauntlet(GL)": "33556029",
  "Craftsman's Palladium Gloves(FB)": "33556030",
  "Craftsman's Palladium Gloves(FA)": "33556031",
  "Craftsman's Palladium Hands(BL)": "33556032",
  "Craftsman's Palladium Hands(WI)": "33556033",
  "Craftsman's Palladium Greaves(WA)": "33556034",
  "Craftsman's Palladium Greaves(FS)": "33556035",
  "Craftsman's Palladium Greaves(GL)": "33556036",
  "Craftsman's Palladium Boots(FB)": "33556037",
  "Craftsman's Palladium Boots(FA)": "33556038",
  "Craftsman's Palladium Shoes(BL)": "33556039",
  "Craftsman's Palladium Shoes(WI)": "33556040",
  "Craftsman's Palladium Visor(WA)": "33556041",
  "Craftsman's Palladium Visor(FS)": "33556042",
  "Craftsman's Palladium Visor(GL)": "33556043",
  "Craftsman's Palladium Headgear(FB)": "33556044",
  "Craftsman's Palladium Headgear(FA)": "33556045",
  "Craftsman's Palladium Headpiece(BL)": "33556046",
  "Craftsman's Palladium Headpiece(WI)": "33556047",
  "Craftsman's Palladium Coat(FG)": "33556048",
  "Craftsman's Palladium Gloves(FG)": "33556049",
  "Craftsman's Palladium Boots(FG)": "33556050",
  "Craftsman's Palladium Headgear(FG)": "33556051",
  "GM's Mysterious Golden Bottle - Mutant Forest": "33556052",
  "GM's Mysterious Golden Bottle - Pontus Ferrum": "33556053",
  "GM's Mysterious Golden Bottle - Porta Inferno": "33556054",
  "GM's Mysterious Golden Bottle - Arcane Trace": "33556055",
  "GM's Mysterious Golden Bottle(Low)": "33556056",
  "GM's Mysterious Golden Bottle(Medium)": "33556057",
  "GM's Mysterious Golden Bottle(High)": "33556058",
  "Extreme Upgrade Seal Stone (Master)": "33556059",
  "Pet Sleeping Kit (Covenant)": "33556060",
  "Pet Sleeping Kit (Trust)": "33556061",
  "Pet Sleeping Kit (Transcendence)": "33556062",
  "Extreme Upgrade Seal Stone (Normal)": "33556063",
  "Illusion Castle Underworld(Apocrypha) Entry Item": "33556064",
  "Illusion Castle Radiant Hall(Apocrypha) Entry Item": "33556065",
  "Potion of Honor Medal": "33556066",
  "Saver Force": "33556067",
  "Honor Medal Chest": "33556068",
  "Chaos Safeguard - Chaos Belt": "33556069",
  "Chaos Safeguard - Chaos Talisman": "33556070",
  "Chaos Safeguard - Carnelian": "33556071",
  "Chaos Safeguard - Arcana of Chaos": "33556072",
  "Chaos Safeguard - Arcana of Laws": "33556073",
  "Enchant Safeguard (Divine)": "33556074",
  "Unrefined Divine Essence": "33556075",
  "Divine Purification Scroll": "33556076",
  "Essence of Chaos Guardian (Chaos Belt)": "33556077",
  "Essence of Chaos Guardian (Chaos Talisman)": "33556078",
  "Essence of Chaos Guardian (Carnelian)": "33556079",
  "Essence of Chaos Guardian (Arcana of Chaos)": "33556080",
  "Essence of Chaos Guardian (Arcana of Laws)": "33556081",
  "Legendary Cube - Illusion Castle Underworld(Apocrypha)": "33556082",
  "Legendary Cube - Illusion Castle Radiant Hall(Apocrypha)": "33556083",
  "[Costume] Snow Star - Chakram": "33556084",
  "Giftbox - [Costume] Snow Star - Chakram": "33556085",
  "Force Gunner Skill Book Package": "33556086",
  "FG Universal/Specialized Skill Books": "33556087",
  "FG Support Skill Books": "33556088",
  "FG ATK Skill Books I": "33556089",
  "FG ATK Skill Books II": "33556090",
  "Muster Card : Forgotten Temple B3F": "33556091",
  "Broken Epaulet of the Dead": "33556092",
  "Ring of Dr. Mazel": "33556093",
  "Cursed Ring of Dr. Mazel": "33556094",
  "For Character Creation Only Gauntlet(WA)": "33556095",
  "For Character Creation Only Gloves(FS)": "33556096",
  "For Character Creation Only Gloves(FB)": "33556097",
  "For Character Creation Only Gloves(FA)": "33556098",
  "For Character Creation Only Hand(BL)": "33556099",
  "For Character Creation Only Hand(WI)": "33556100",
  "For Character Creation Only Gauntlet(GL)": "33556101",
  "For Character Creation Only Gloves(FG)": "33556102",
  "Arcana of Guardian": "33556103",
  "EXP II": "33556104",
  "HP II": "33556105",
  "Attack II": "33556106",
  "Magic Attack II": "33556107",
  "Defense II": "33556108",
  "Sword Skill Amp II": "33556109",
  "Magic Skill Amp II": "33556110",
  "Critical DMG II": "33556111",
  "Ignore Resist Critical Rate": "33556112",
  "Ignore Damage Reduction": "33556113",
  "Resist Skill Amp.": "33556114",
  "Resist Critical DMG": "33556115",
  "Ignore Accuracy": "33556116",
  "Crest of Life Epic Seal Stone": "33556117",
  "Life Capsule(Special) Type A": "33556118",
  "Life Capsule(Special) Type B": "33556119",
  "Chaos Safeguard - Arcana of Guardian": "33556120",
  "Essence of Chaos Guardian (Arcana of Guardian)": "33556121",
  "Legendary Cube - Edge of Phantom": "33556122",
  "Legendary Cube - Forgotten Temple B3F": "33556123",
  "Costume Bag": "33556124",
  "Blessing Bead - Costume Warehouse (No.2)": "33556125",
  "Blessing Bead - Costume Warehouse (No.3)": "33556126",
  "Blessing Bead - Costume Warehouse (No.4)": "33556127",
  "Honor Medal Reset Scroll (Option Selective)": "33556128",
  "Blessing Bead - Costume Warehouse": "33556129",
  "[Costume] Devil Hunter": "33556130",
  "[Costume] Accessory Of Devil Hunter": "33556131",
  "Giftbox - [Costume] Devil Hunter": "33556132",
  "[Costume] Devil Hunter Helmet": "33556133",
  "[Costume] Accessory Of Devil Hunter Helmet": "33556134",
  "Giftbox - [Costume] Devil Hunter Helmet": "33556135",
  "[Costume] Charisma Panda Costume": "33556136",
  "[Costume] Accessory of Charisma Panda Costume": "33556137",
  "Giftbox - [Costume] Charisma Panda Costume": "33556138",
  "[Costume] Charisma Panda Helmet": "33556139",
  "[Costume] Accessory of Charisma Panda Helmet": "33556140",
  "Giftbox - [Costume] Charisma Panda Helmet": "33556141",
  "[Costume] Cute Devil Face": "33556142",
  "[Costume] Accessory Of Cute Devil Face": "33556143",
  "Giftbox - [Costume] Cute Devil Face": "33556144",
  "Chaos Box - Upgrade Item (Low)": "33556145",
  "Chaos Box - Upgrade Item (High)": "33556146",
  "[Costume] Guardian of Light - Orb": "33556147",
  "[Costume] Guardian of Light - Katana": "33556148",
  "[Costume] Guardian of Light - Blade": "33556149",
  "[Costume] Guardian of Light - Crystal": "33556150",
  "[Costume] Guardian of Light - Daikatana": "33556151",
  "[Costume] Guardian of Light - Greatsword": "33556152",
  "[Costume] Guardian of Light - Chakram": "33556153",
  "Giftbox - [Costume] Guardian of Light - Orb": "33556154",
  "Giftbox - [Costume] Guardian of Light - Katana": "33556155",
  "Giftbox - [Costume] Guardian of Light - Blade": "33556156",
  "Giftbox - [Costume] Guardian of Light - Crystal": "33556157",
  "Giftbox - [Costume] Guardian of Light - Daikatana": "33556158",
  "Giftbox - [Costume] Guardian of Light - Greatsword": "33556159",
  "Giftbox - [Costume] Guardian of Light - Chakram": "33556160",
  "Yekaterina Special Membership": "33556161",
  "Legend Arena Potion Box Lv3": "33556162",
  "Legend Arena Potion Box Lv4": "33556163",
  "Holy Water of Resistance (15min)": "33556165",
  "Holy Water of Resistance (30min)": "33556166",
  "Potion of OXP": "33556167",
  "Potion of OXP (Lv10+)": "33556168",
  "Potion of OXP (Lv20+)": "33556169",
  "Potion of OXP (Lv30+)": "33556170",
  "Potion of OXP (Lv40+)": "33556171",
  "Potion of OXP (Lv50+)": "33556172",
  "Potion of OXP (Lv60+)": "33556173",
  "Potion of OXP (Lv70+)": "33556174",
  "Potion of OXP (Lv80+)": "33556175",
  "Potion of OXP (Lv90+)": "33556176",
  "Mercenary Seal Kit": "33556177",
  "Key of Grudged Warrior": "33556178",
  "Legendary Cube - Acheron Arena": "33556179",
  "Inner Orb": "33556180",
  "Skill Book (Aqua Aura)": "33556181",
  "Skill Book (Flame Aura)": "33556182",
  "Skill Book (Ice Aura)": "33556183",
  "Skill Book (Lightening Aura)": "33556184",
  "Normal Damage UP": "33556185",
  "Attack Potion of War (Highest)": "33556186",
  "Master Potion of War (Highest)": "33556187",
  "[Costume] Warrior's Soul Costume": "33556188",
  "[Costume] Accessory of Warrior's Soul Costume": "33556189",
  "Giftbox - [Costume] Warrior's Soul Costume": "33556190",
  "[Costume] Warrior's Soul Helmet": "33556191",
  "[Costume] Accessory of Warrior's Soul Helmet": "33556192",
  "Giftbox - [Costume] Warrior's Soul Helmet": "33556193",
  "[Costume] Dark Party Costume": "33556194",
  "[Costume] Accessory of Dark Party Costume": "33556195",
  "Giftbox - [Costume] Dark Party Costume": "33556196",
  "[Costume] Dark Party Hair": "33556197",
  "[Costume] Accessory of Dark Party Hair": "33556198",
  "Giftbox - [Costume] Dark Party Hair": "33556199",
  "The Soul Key": "33556200",
  "Master's Palladium Orb": "33556201",
  "Master's Palladium Crystal": "33556202",
  "Master's Palladium Katana": "33556203",
  "Master's Palladium Blade": "33556204",
  "Master's Palladium Daikatana": "33556205",
  "Master's Palladium Great Sword": "33556206",
  "Master's Palladium Chakram": "33556207",
  "Master's Palladium Plate (WA)": "33556208",
  "Master's Palladium Plate (FS)": "33556209",
  "Master's Palladium Plate (GL)": "33556210",
  "Master's Palladium Coat (FB)": "33556211",
  "Master's Palladium Coat (FA)": "33556212",
  "Master's Palladium Suit (BL)": "33556213",
  "Master's Palladium Suit (WI)": "33556214",
  "Master's Palladium Gauntlets (WA)": "33556215",
  "Master's Palladium Gauntlets (FS)": "33556216",
  "Master's Palladium Gauntlets (GL)": "33556217",
  "Master's Palladium Gloves (FB)": "33556218",
  "Master's Palladium Gloves (FA)": "33556219",
  "Master's Palladium Hand (BL)": "33556220",
  "Master's Palladium Hand (WI)": "33556221",
  "Master's Palladium Greaves (WA)": "33556222",
  "Master's Palladium Greaves (FS)": "33556223",
  "Master's Palladium Greaves (GL)": "33556224",
  "Master's Palladium Boots (FB)": "33556225",
  "Master's Palladium Boots (FA)": "33556226",
  "Master's Palladium Shoes (BL)": "33556227",
  "Master's Palladium Shoes (WI)": "33556228",
  "Master's Palladium Visor (WA)": "33556229",
  "Master's Palladium Visor (FS)": "33556230",
  "Master's Palladium Visor (GL)": "33556231",
  "Master's Palladium Headgear (FB)": "33556232",
  "Master's Palladium Headgear (FA)": "33556233",
  "Master's Palladium Headpiece (BL)": "33556234",
  "Master's Palladium Headpiece (WI)": "33556235",
  "Master's Palladium Coat (FG)": "33556236",
  "Master's Palladium Gloves (FG)": "33556237",
  "Master's Palladium Boots (FG)": "33556238",
  "Master's Palladium Headgear (FG)": "33556239",
  "Devil's Key": "33556240",
  "Fury Potion (Lv. 5)": "33556241",
  "Essence of Ancient Soul": "33556242",
  "Essence of Ancient Soul (Lv11+)": "33556243",
  "Essence of Ancient Soul (Lv21+)": "33556244",
  "Essence of Ancient Soul (Lv31+)": "33556245",
  "Essence of Ancient Soul (Lv41+)": "33556246",
  "Essence of Ancient Soul (Lv51+)": "33556247",
  "Essence of Ancient Soul (Lv61+)": "33556248",
  "Essence of Ancient Soul (Lv71+)": "33556249",
  "Essence of Ancient Soul (Lv81+)": "33556250",
  "Essence of Ancient Soul (Lv91+)": "33556251",
  "Hursurf's Letter I": "33556252",
  "Hursurf's Letter II": "33556253",
  "Hursurf's Letter III": "33556254",
  "Hursurf's Letter IV": "33556255",
  "Hursurf's Letter V": "33556256",
  "Hursurf's Letter VI": "33556257",
  "Hursurf's Letter VII": "33556258",
  "Hursurf's Letter VIII": "33556259",
  "Hursurf's Letter IX": "33556260",
  "Hursurf's Letter X": "33556261",
  "Drei Frame Epaulet of Guardian": "33556262",
  "Archridium Epaulet of Guardian": "33556263",
  "Palladium Epaulet of Guardian": "33556264",
  "Devil's Token (Medium)": "33556265",
  "Devil's Token (High)": "33556266",
  "Devil's Token (Highest)": "33556267",
  "[Vehicle Costume] Santa's Christmas Sleigh": "33556268",
  "[Vehicle Costume] Flaming Bike (Red Flare)": "33556269",
  "[Vehicle Costume] Flaming Bike (Blue Flare)": "33556270",
  "[Vehicle Costume] Astral Transport Machine": "33556271",
  "Giftbox - [Vehicle Costume] Santa's Christmas Sleigh": "33556272",
  "Giftbox - [Vehicle Costume] Flaming Bike (Red Flare)": "33556273",
  "Giftbox - [Vehicle Costume] Flaming Bike (Blue Flare)": "33556274",
  "Giftbox - [Vehicle Costume] Astral Transport Machine": "33556275",
  "Legendary Cube - Devil's Tower": "33556276",
  "[Costume] Christmas Weapon - Orb": "33556277",
  "[Costume] Christmas Weapon - Katana": "33556278",
  "[Costume] Christmas Weapon - Blade": "33556279",
  "[Costume] Christmas Weapon - Crystal": "33556280",
  "[Costume] Christmas Weapon - Daikatana": "33556281",
  "[Costume] Christmas Weapon - Greatsword": "33556282",
  "[Costume] Christmas Weapon - Chakram": "33556283",
  "Giftbox - [Costume] Christmas Weapon - Orb": "33556284",
  "Giftbox - [Costume] Christmas Weapon - Katana": "33556285",
  "Giftbox - [Costume] Christmas Weapon - Blade": "33556286",
  "Giftbox - [Costume] Christmas Weapon - Crystal": "33556287",
  "Giftbox - [Costume] Christmas Weapon - Daikatana": "33556288",
  "Giftbox - [Costume] Christmas Weapon - Greatsword": "33556289",
  "Giftbox - [Costume] Christmas Weapon - Chakram": "33556290",
  "Revived Devil's Key": "33556291",
  "[Vehicle Costume] Mythical Lion (White)": "33556292",
  "Giftbox - [Vehicle Costume] Mythical Lion (White)": "33556293",
  "[Vehicle Costume] Mythical Lion (Black)": "33556294",
  "Giftbox - [Vehicle Costume] Mythical Lion (Black)": "33556295",
  "[Vehicle Costume] Mythical Lion (Red)": "33556296",
  "Giftbox - [Vehicle Costume] Mythical Lion (Red)": "33556297",
  "[Vehicle Costume] Mythical Lion (Blue)": "33556298",
  "Giftbox - [Vehicle Costume] Mythical Lion (Blue)": "33556299",
  "Pet - Puncharoo": "33556300",
  "Mysterious Golden Bottle - Senillinea": "33556301",
  "GM's Mysterious Golden Bottle(Highest)": "33556302",
  "GM's Mysterious Golden Bottle(Rare)": "33556303",
  "GM's Mysterious Golden Bottle(Epic)": "33556304",
  "Pet - Elephie": "33556305",
  "[Costume] Spy of the Kingdom Hair": "33556306",
  "[Costume] Accessory Orb Spy of the Kingdom Hair": "33556307",
  "Gift Box - [Costume] Spy of the Kingdom Hair": "33556308",
  "GiftBox- [Costume] Liberty Lady & Gentleman Hair": "33556309",
  "[Costume] Panda Doll Clothes": "33556310",
  "[Costume] Accessory of Panda Doll Clothes": "33556311",
  "Giftbox - [Costume] Panda Doll Clothes": "33556312",
  "[Costume] Bear Doll Clothes": "33556313",
  "[Costume] Accessory of Bear Doll Clothes": "33556314",
  "Giftbox - [Costume] Bear Doll Clothes": "33556315",
  "[Costume] Shark Doll Clothes": "33556316",
  "[Costume] Accessory of Shark Doll Clothes": "33556317",
  "Giftbox - [Costume] Shark Doll Clothes": "33556318",
  "[Costume] Rabbit Doll Clothes": "33556319",
  "[Costume] Accessory of Rabbit Doll Clothes": "33556320",
  "Giftbox - [Costume] Rabbit Doll Clothes": "33556321",
  "[Costume] Lion Doll Clothes": "33556322",
  "[Costume] Accessory of Lion Doll Clothes": "33556323",
  "Giftbox - [Costume] Lion Doll Clothes": "33556324",
  "[Costume] Tiger Doll Clothes": "33556325",
  "[Costume] Accessory of Tiger Doll Clothes": "33556326",
  "Giftbox - [Costume] Tiger Doll Clothes": "33556327",
  "[Costume] Dinosaur Doll Clothes": "33556328",
  "[Costume] Accessory of Dinosaur Doll Clothes": "33556329",
  "Giftbox - [Costume] Dinosaur Doll Clothes": "33556330",
  "Enchant Item Box (Classic)": "33556331",
  "PC Cafe Ticket": "33556332",
  "Lucky Box": "33556333",
  "PC Cafe exclusive cube": "33556334",
  "Devil's Invitation": "33556335",
  "Broken Tiara of Nix": "33556336",
  "Shiny Jewel of Nix": "33556337",
  "Chaos Converter": "33556338",
  "Chaos Seal Stone": "33556339",
  "Legendary Cube - Pandemonium": "33556340",
  "[Vehicle Costume] Chirpy the Explorer": "33556341",
  "Giftbox - [Vehicle Costume] Chirpy the Explorer": "33556342",
  "Crest of Earth Slot Converter": "33556343",
  "Crest of Earth Option Scroll": "33556344",
  "Crest of Earth Epic Converter - HP": "33556345",
  "Crest of Earth Epic Converter - Defense": "33556346",
  "Crest of Earth Epic Converter - Ignore Penetration": "33556347",
  "Crest of Earth Epic Converter - All Attack UP": "33556348",
  "Crest of Earth Epic Converter - Resist Skill Amp.": "33556349",
  "Crest of Earth Epic Converter - Ignore Resist Skill Amp.": "33556350",
  "Crest of Earth Epic Converter - Resist Critical DMG": "33556351",
  "Crest of Earth Epic Converter - Critical DMG": "33556352",
  "Broken Bike Card": "33556353",
  "Mystery Cube (Normal)": "33556354",
  "Mystery Cube (Rare)": "33556355",
  "Crest of Earth Epic Converter Box": "33556356",
  "Crest of Earth Capsule": "33556357",
  "Crest of Earth Safety Kit": "33556358",
  "Crest of Earth Capsule Seal Stone": "33556359",
  "Crest of Earth Epic Reset Scroll": "33556360",
  "Crest of Earth Epic Seal Stone": "33556361",
  "Piece of Luminous Illusion Stone": "33556362",
  "Piece of Beautiful Illusion Stone": "33556363",
  "Piece of Glorious Illusion Stone": "33556364",
  "Merit Medal Trade Certificate (Premium)": "33556365",
  "Happy Birthday Box": "33556366",
  "[Costume] Dignity of Lady/Gent": "33556367",
  "[Costume] Accessory Orb of Dignity of Lady/Gent": "33556672",
  "Giftbox - [Costume] Dignity of Lady/Gent": "33556369",
  "Pet - Cute Mahjong": "33556370",
  "Holy Water": "33556371",
  "Blessing Bead - Force Wing EXP (200%)": "33556372",
  "Blessing Bead - Force Wing EXP (1000%)": "33556373",
  "[Wing Costume] Black": "33556374",
  "Giftbox - [Force Wing Costume] Black Wing": "33556375",
  "Demonite Orb": "33556376",
  "Demonite Crystal": "33556377",
  "Demonite Katana": "33556378",
  "Demonite Blade": "33556379",
  "Demonite Daikatana": "33556380",
  "Demonite Great Sword": "33556381",
  "Demonite Chakram": "33556382",
  "Demonite Plate (WA)": "33556383",
  "Demonite Plate (FS)": "33556384",
  "Demonite Plate (GL)": "33556385",
  "Demonite Coat (FB)": "33556386",
  "Demonite Coat (FA)": "33556387",
  "Demonite Coat (FG)": "33556388",
  "Demonite Suit (BL)": "33556389",
  "Demonite Suit (WI)": "33556390",
  "Demonite Gauntlet (WA)": "33556391",
  "Demonite Gauntlet (FS)": "33556392",
  "Demonite Gauntlet (GL)": "33556393",
  "Demonite Gloves (FB)": "33556394",
  "Demonite Gloves (FA)": "33556395",
  "Demonite Gloves (FG)": "33556396",
  "Demonite Hands (BL)": "33556397",
  "Demonite Hands (WI)": "33556398",
  "Demonite Greaves (WA)": "33556399",
  "Demonite Greaves (FS)": "33556400",
  "Demonite Greaves (GL)": "33556401",
  "Demonite Boots (FB)": "33556402",
  "Demonite Boots (FA)": "33556403",
  "Demonite Boots (FG)": "33556404",
  "Demonite Shoes (BL)": "33556405",
  "Demonite Shoes (WI)": "33556406",
  "Demonite Visor (WA)": "33556407",
  "Demonite Visor (FS)": "33556408",
  "Demonite Visor (GL)": "33556409",
  "Demonite Headgear (FB)": "33556410",
  "Demonite Headgear (FA)": "33556411",
  "Demonite Headgear (FG)": "33556412",
  "Demonite Headpiece (BL)": "33556413",
  "Demonite Headpiece (WI)": "33556414",
  "Demonite Epaulet of Laws": "33556415",
  "Demonite Epaulet of Fighter": "33556416",
  "Demonite Epaulet of Sage": "33556417",
  "Material Core (Demonite)": "33556418",
  "Quartz Core (Demonite)": "33556419",
  "Astral Core (Demonite)": "33556420",
  "Force Core (Ultimate)": "33556421",
  "Upgrade Core (Ultimate)": "33556422",
  "Slot Extender (Ultimate)": "33556423",
  "Weapon Option Scroll (Ultimate)": "33556424",
  "Armor Option Scroll (Ultimate)": "33556425",
  "Skill Scroll (Wing Fly)": "33556426",
  "Skill Scroll (Wing Assault)": "33556427",
  "Skill Scroll (Wing Protect)": "33556428",
  "Core Enhancer (Ultimate)": "33556429",
  "Core Superior (Ultimate)": "33556430",
  "Perfect Core (Ultimate)": "33556431",
  "Enchant Safeguard (Ultimate)": "33556432",
  "Demonite Epaulet of Guardian": "33556433",
  "Luxury Jewelry Box": "33556434",
  "Force Essence": "33556435",
  "[Costume] Demonite Plate": "33556436",
  "[Costume] Accessory of Demonite Plate": "33556437",
  "Giftbox - [Costume] Demonite Plate": "33556438",
  "[Costume] Demonite Coat": "33556439",
  "[Costume] Accessory of Demonite Coat": "33556440",
  "Giftbox - [Costume] Demonite Coat": "33556441",
  "[Costume] Demonite Suit": "33556442",
  "[Costume] Accessory of Demonite Suit": "33556443",
  "Giftbox - [Costume] Demonite Suit": "33556444",
  "[Costume] Demonite Visor": "33556445",
  "[Costume] Accessory of Demonite Visor": "33556446",
  "Giftbox - [Costume] Demonite Visor": "33556447",
  "[Costume] Demonite Headgear": "33556448",
  "[Costume] Accessory of Demonite Headgear": "33556449",
  "Giftbox - [Costume] Demonite Headgear": "33556450",
  "[Costume] Demonite Headpiece": "33556451",
  "[Costume] Accessory of Demonite Headpiece": "33556452",
  "Giftbox - [Costume] - Demonite Headpiece": "33556453",
  "Skill Book (Force Field)": "33556454",
  "Force Wing - Normal Grade": "33556455",
  "[Costume] Demonite Special Edition - Orb": "33556456",
  "[Costume] Demonite Special Edition - Katana": "33556457",
  "[Costume] Demonite Special Edition - Blade": "33556458",
  "[Costume] Demonite Special Edition - Crystal": "33556459",
  "[Costume] Demonite Special Edition - Daikatana": "33556460",
  "[Costume] Demonite Special Edition - Great Sword": "33556461",
  "[Costume] Demonite Special Edition - Chakram": "33556462",
  "Giftbox - [Costume] Demonite Special Edition - Orb": "33556463",
  "Giftbox - [Costume] Demonite Special Edition - Katana": "33556464",
  "Giftbox - [Costume] Demonite Special Edition - Blade": "33556465",
  "Giftbox - [Costume] Demonite Special Edition - Crystal": "33556466",
  "Giftbox - [Costume] Demonite Special Edition - Daikatana": "33556467",
  "Giftbox - [Costume] Demonite Special Edition - Great Sword": "33556468",
  "Giftbox - [Costume] Demonite Special Edition - Chakram": "33556469",
  "Drifting Bottle": "33556470",
  "Legendary Cube - Mirage Island": "33556471",
  "[Wing Costume] Fairy": "33556472",
  "Giftbox - [Wing Costume] Fairy": "33556473",
  "Black Transmuter (Only Procyon)": "33556474",
  "Black Transmuter (Only Capella)": "33556475",
  "[Wing Costume] Mech": "33556476",
  "Giftbox - [Wing Costume] Mech": "33556477",
  "Crest of Sky Slot Converter": "33556478",
  "Crest of Sky Option Scroll": "33556479",
  "Light Feather": "33556480",
  "Mysterious Magic Book (Normal)": "33556481",
  "Mysterious Magic Book (Rare)": "33556482",
  "Crest of Sky Epic Converter - HP": "33556483",
  "Crest of Sky Epic Converter - Defense": "33556484",
  "Crest of Sky Epic Converter - All Attack UP": "33556485",
  "Crest of Sky Epic Converter - All Skill Amp.": "33556486",
  "Crest of Sky Epic Converter - Critical DMG": "33556487",
  "Crest of Sky Epic Converter - Normal DMG UP": "33556488",
  "Crest of Sky Epic Converter - Additional DMG": "33556489",
  "Crest of Sky Epic Converter -Ignore DMG Reduction": "33556490",
  "Crest of Sky Epic Converter Box": "33556491",
  "Crest of Sky Epic Reset Scroll": "33556492",
  "Crest of Sky Epic Seal Stone": "33556493",
  "Crest of Sky Capsule": "33556494",
  "Crest of Sky Capsule Seal Stone": "33556495",
  "Crest of Sky Safety Kit": "33556496",
  "[Costume] Bunny Girl Costume": "33556497",
  "[Costume] Accessory of Bunny Girl Costume": "33556498",
  "Giftbox - [Costume] Bunny Girl Costume": "33556499",
  "[Costume] Bunny Girl Hair": "33556500",
  "[Costume] Accessory of Bunny Girl Hair": "33556501",
  "Giftbox - [Costume] Bunny Girl Hair": "33556502",
  "[Costume] Blazing Summer Suit": "33556503",
  "[Costume] Accessory of Blazing Summer Suit": "33556504",
  "Giftbox - [Costume] Blazing Summer Suit": "33556505",
  "[Costume] Blazing Summer Cap": "33556506",
  "[Costume] Accessory of Blazing Summer Cap": "33556507",
  "Giftbox - [Costume] Blazing Summer Cap": "33556508",
  "[Costume] Sheep Doll Mask": "33556509",
  "[Costume] Accessory of Sheep Doll Mask": "33556510",
  "Giftbox - [Costume] Sheep Doll Mask": "33556511",
  "Upgrade Core Set (Low)": "33556512",
  "Upgrade Core Set (Medium)": "33556513",
  "Upgrade Core Set (High)": "33556514",
  "Upgrade Core Set (Highest)": "33556515",
  "Upgrade Core Set (Ultimate)": "33556516",
  "Chaos Core Set": "33556517",
  "Divine Stone Set": "33556518",
  "Yul's Exchange Ticket (Dungeon Entry Ticket)": "33556519",
  "Yul's Exchange Ticket (Bead)": "33556520",
  "Yul's Exchange Ticket (Potion)": "33556521",
  "Yul's Exchange Ticket_1": "33556522",
  "Yul's Exchange Ticket_2": "33556523",
  "Key of Chaos Lv. 7": "33556524",
  "Coupon": "33556525",
  "Chaos Safeguard - Earring": "33556526",
  "Essence of Chaos Guardian (Earring)": "33556527",
  "Wing Potion": "33556528",
  "Cube of Wing": "33556529",
  "Potion of Honor(Rank 1+)": "33556530",
  "Potion of Honor(Rank 5+)": "33556531",
  "Potion of Honor(Rank 6+)": "33556532",
  "Potion of Honor(Rank 7+)": "33556533",
  "Potion of Honor(Rank 8+)": "33556534",
  "Potion of Honor(Rank 9+)": "33556535",
  "Potion of Honor(Rank 10+)": "33556536",
  "Potion of Honor(Rank 11+)": "33556537",
  "Potion of Honor(Rank 12+)": "33556538",
  "Potion of Honor(Rank 13+)": "33556539",
  "Potion of Honor(Rank 14+)": "33556540",
  "Potion of Honor(Rank 15+)": "33556541",
  "Potion of Honor(Rank 16+)": "33556542",
  "Potion of Honor(Rank 17+)": "33556543",
  "Potion of Honor(Rank 18+)": "33556544",
  "Potion of Honor(Rank 19+)": "33556545",
  "Potion of Experience (Lv1+)": "33556546",
  "Potion of Experience (Lv11+)": "33556547",
  "Potion of Experience (Lv31+)": "33556548",
  "Potion of Experience (Lv51+)": "33556549",
  "Potion of Experience (Lv71+)": "33556550",
  "Potion of Experience (Lv91+)": "33556551",
  "Potion of Experience (Lv111+)": "33556552",
  "Potion of Experience (Lv131+)": "33556553",
  "Potion of Experience (Lv151+)": "33556554",
  "Potion of Experience (Lv161+)": "33556555",
  "Potion of Experience (Lv171+)": "33556556",
  "Potion of Experience (Lv191+)": "33556558",
  "Potion of Experience (Lv192+)": "33556559",
  "Potion of Experience (Lv193+)": "33556560",
  "Potion of Experience (Lv194+)": "33556561",
  "Potion of Experience (Lv195+)": "33556562",
  "Potion of Experience (Lv196+)": "33556563",
  "Potion of Experience (Lv197+)": "33556564",
  "Potion of Experience (Lv198+)": "33556565",
  "Potion of Experience (Lv199+)": "33556566",
  "Charisma Pan's Special Box": "33556567",
  "Pet Change Kit Coupon": "33556568",
  "[Wing Costume] Golden Rose": "33556569",
  "Giftbox - [Wing Costume] Golden Rose": "33556570",
  "[Wing Costume] Evil Succubus": "33556571",
  "Giftbox - [Costume] Evil Succubus Wing - Force Wing": "33556572",
  "[Wing Costume] Frozen Gear": "33556573",
  "Giftbox - [Wing Costume] Frozen Gear": "33556574",
  "[Wing Costume] Mutant Lord": "33556575",
  "Giftbox - [Wing Costume] Mutant Lord": "33556576",
  "[Wing Costume] Porta Lord": "33556577",
  "Giftbox - [Wing Costume] Porta Lord": "33556578",
  "Astral Bikecard - QW7": "33556579",
  "Bike Epic Converter Box (Lv. 5)": "33556580",
  "Bike Epic Converter (Lv. 5) - HP": "33556581",
  "Bike Epic Converter (Lv. 5) - Critical Rate": "33556582",
  "Bike Epic Converter (Lv. 5) - Skill Amp. Resistance": "33556583",
  "Bike Epic Converter (Lv. 5) - Critical DMG Resistance": "33556584",
  "Bike Epic Converter (Lv. 5) - Accuracy": "33556585",
  "Bike Epic Converter (Lv. 5) - Penetration": "33556586",
  "Bike Epic Converter (Lv. 5) - Ignore Penetration": "33556587",
  "Bike Epic Converter (Lv. 5) - Ignore Resist Critical DMG": "33556588",
  "Bike Epic Converter (Lv. 5) - Ignore Resist Skill Amp.": "33556589",
  "Bike Epic Converter (Lv. 5) - All Attack UP": "33556590",
  "Bike Option Scroll (Ultimate)": "33556591",
  "Bike Slot Converter (Lv. 5)": "33556592",
  "Bike Parts (Lv. 5)": "33556593",
  "Honor of Nevareth (Rank.1)": "33556594",
  "Honor of Nevareth (Rank.2)": "33556595",
  "Honor of Nevareth (Rank.3)": "33556596",
  "Honor of Nevareth (Rank.4)": "33556597",
  "Honor of Nevareth (Rank.5)": "33556598",
  "Honor of Nevareth (Rank.6)": "33556599",
  "Legend Arena Potion Box Lv5": "33556600",
  "Blazing Crystal": "33556601",
  "Token of Unity": "33556602",
  "Cube of Blaze (Easy)": "33556603",
  "Cube of Blaze (Normal)": "33556604",
  "Cube of Blaze (Hard)": "33556605",
  "Cube of Blaze (Heroic)": "33556606",
  "Cube of Blaze (Awakened)": "33556607",
  "Essence of Unity (OXP)": "33556608",
  "Essence of Unity (EXP)": "33556609",
  "Essence of Unity (Wing)": "33556610",
  "Essence of Unity (Honor)": "33556611",
  "Cube of Unity (OXP)": "33556612",
  "Cube of Unity (EXP)": "33556613",
  "Cube of Unity (Wing)": "33556614",
  "Cube of Unity (Honor)": "33556615",
  "Chaos Safeguard - Amulet": "33556616",
  "Essence of Chaos Guardian (Amulet)": "33556617",
  "[Costume] Call of Valkyrie": "33556618",
  "[Costume] Accessory of Call of Valkyrie": "33556619",
  "Giftbox - [Costume] Call of Valkyrie": "33556620",
  "[Costume] Call of Valkyrie Helmet": "33556621",
  "[Costume] Accessory of Call of Valkyrie Helmet": "33556622",
  "Giftbox - [Costume] Call of Valkyrie Helmet": "33556623",
  "[Costume] Steampunk Look": "33556624",
  "[Costume] Accessory Steampunk Look": "33556625",
  "Giftbox - [Costume] Steampunk Look": "33556626",
  "[Costume] Steampunk Mask": "33556627",
  "[Costume] Accessory Steampunk Mask": "33556628",
  "Giftbox - [Costume] Steampunk Mask": "33556629",
  "[Costume] Merry Christmas": "33556630",
  "[Costume] Accessory of Merry Christmas": "33556631",
  "Giftbox - [Costume] Merry Christmas": "33556632",
  "[Costume] Merry Christmas Hair": "33556633",
  "[Costume] Accessory of Merry Christmas Hair": "33556634",
  "Giftbox - [Costume] Merry Christmas Hair": "33556635",
  "Badge of Unity": "33556636",
  "Dummy": "33556806",
  "Honor Medal Seal Chest": "33556647",
  "[Vehicle Costume] Mech Plasma - Vehicle": "33556648",
  "Giftbox - [Vehicle Costume] Mech Plasma - Vehicle": "33556649",
  "Arionell's Treasure Box": "33556650",
  "[Costume] Steampunk - Orb": "33556651",
  "[Costume] Steampunk - Katana": "33556652",
  "[Costume] Steampunk - Blade": "33556653",
  "[Costume] Steampunk - Crystal": "33556654",
  "[Costume] Steampunk - Daikatana": "33556655",
  "[Costume] Steampunk - Great Sword": "33556656",
  "[Costume] Steampunk - Chakram": "33556657",
  "Giftbox - [Costume] Steampunk - Orb": "33556658",
  "Giftbox - [Costume] Steampunk - Katana": "33556659",
  "Giftbox - [Costume] Steampunk - Blade": "33556660",
  "Giftbox - [Costume] Steampunk - Crystal": "33556661",
  "Giftbox - [Costume] Steampunk - Daikatana": "33556662",
  "Giftbox - [Costume] Steampunk - Great Sword": "33556663",
  "Giftbox - [Costume] Steampunk - Chakram": "33556664",
  "[Wing Costume] Gold Champion": "33556665",
  "Giftbox - [Force Wing Costume] Gold Champion Wing": "33556666",
  "[Wing Costume] Aqua": "33556667",
  "Giftbox - [Force Wing Costume] Aqua Wing": "33556668",
  "[Wing Costume] Mischievous Ghost": "33556669",
  "Giftbox - [Force Wing Costume] Mischievous Ghost Wing": "33556670",
  "[Costume] Dignity of Lady/Gent Hair": "33556671",
  "Giftbox - [Costume] Dignity of Lady/Gent Hair": "33556673",
  "Epic Booster (Medium)": "33556675",
  "Epic Booster (High)": "33556676",
  "Epic Booster (Highest)": "33556677",
  "Broken Devil's Horn": "33556678",
  "Legendary Cube - Flame Nest": "33556679",
  "Elixir of Unity Lv.1 (EXP)": "33556680",
  "Elixir of Unity Lv.1 (Honor)": "33556681",
  "Elixir of Unity Lv.1 (Honor Medal)": "33556682",
  "Elixir of Unity Lv.1 (OXP)": "33556683",
  "Elixir of Unity Lv.1 (Wing)": "33556684",
  "Jewel Cube Piece (Emerald)": "33556685",
  "Guild Golden Treasure1": "33556686",
  "Guild Golden Treasure2": "33556687",
  "Agent Yul Ring": "33556688",
  "Agent Yul Necklace": "33556689",
  "Agent Yul Earring": "33556690",
  "Agent Yul Bracelet": "33556691",
  "Luxury Suitcase": "33556692",
  "Soul Core (Bloody Ice)": "33556693",
  "Soul Core (Desert Scream)": "33556694",
  "Soul Core (Green Despair)": "33556695",
  "Soul Core (Port Lux)": "33556696",
  "Soul Core (Fort. Ruina)": "33556697",
  "Soul Core (Undead Ground)": "33556698",
  "Soul Core (Forgotten Ruin)": "33556699",
  "Soul Core (Lakeside)": "33556700",
  "Soul Core (Mutant Forest)": "33556701",
  "Soul Core (Pontus Ferrum)": "33556702",
  "Soul Core (Porta Inferno)": "33556703",
  "Soul Core (Arcane Trace)": "33556704",
  "Soul Core (Senillinea)": "33556705",
  "Transformation Card - Rabithorn": "33556706",
  "Transformation Card - BabaYaga": "33556707",
  "Transformation Card - Red Garlie": "33556708",
  "Transformation Card - Mummy": "33556709",
  "Transformation Card - Troglo": "33556710",
  "Transformation Card - Wriggleleaf": "33556711",
  "Transformation Card - Crag Crab": "33556712",
  "Transformation Card - Phantom Crew": "33556713",
  "Transformation Card - Mecha Buffalo": "33556714",
  "Transformation Card - Electric Bird": "33556715",
  "Transformation Card - Wraith": "33556716",
  "Transformation Card - Ghost Blader+": "33556717",
  "Transformation Card - Griffin": "33556718",
  "Transformation Card - Hexscyther": "33556719",
  "Transformation Card - Dark Minotaur": "33556720",
  "Transformation Card - Dark Troll": "33556721",
  "Transformation Card - Entrita": "33556722",
  "Transformation Card - Viant": "33556723",
  "Transformation Card - UMD-02 Cornus": "33556724",
  "Transformation Card - UMD-01 Brachium": "33556725",
  "Transformation Card - Succubus": "33556726",
  "Transformation Card - Incubus": "33556727",
  "Transformation Card - Rebel Leadership Captain": "33556728",
  "Transformation Card - Rebel Leader": "33556729",
  "Transformation Card - Outrageous Butterfly": "33556730",
  "Transformation Card - Giant Wolf": "33556731",
  "[Wing Costume] Gold Guardian": "33556732",
  "Giftbox - [Force Wing Costume] Gold Guardian Wing": "33556733",
  "[Wing Costume] Poison Edge": "33556734",
  "Giftbox - [Force Wing Costume] Poison Edge Wing": "33556735",
  "[Wing Costume] Tentacle Machine": "33556736",
  "Giftbox - [Force Wing Costume] Tentacle Machine Wing": "33556737",
  "[Vehicle Costume] Steampunk Replica": "33556738",
  "Giftbox - [Vehicle Costume] Steampunk Replica": "33556739",
  "Nomal Jewel Cube (Ruby)": "33556740",
  "Nomal Jewel Cube (Sapphire)": "33556741",
  "Nomal Jewel Cube (Emerald)": "33556742",
  "Shiny Jewel Cube (Ruby)": "33556743",
  "Shiny Jewel Cube (Sapphire)": "33556744",
  "Shiny Jewel Cube (Emerald)": "33556745",
  "Luminous Jewel Cube (Ruby)": "33556746",
  "Luminous Jewel Cube (Sapphire)": "33556747",
  "Luminous Jewel Cube (Emerald)": "33556748",
  "Guild Golden Treasure3": "33556749",
  "Guild Golden Treasure4": "33556750",
  "Guild Golden Treasure5": "33556751",
  "Guild Golden Treasure6": "33556752",
  "Guild Golden Treasure7": "33556753",
  "Guild Golden Treasure8": "33556754",
  "Guild Golden Treasure9": "33556755",
  "Guild Golden Treasure10": "33556756",
  "Guild Golden Treasure11": "33556757",
  "Guild Golden Treasure12": "33556758",
  "Guild Golden Treasure13": "33556759",
  "Guild Golden Treasure14": "33556760",
  "Guild Golden Treasure15": "33556761",
  "Guild Golden Treasure16": "33556762",
  "Guild Golden Treasure17": "33556763",
  "Guild Golden Treasure18": "33556764",
  "Guild Golden Treasure19": "33556765",
  "Guild Golden Treasure20": "33556766",
  "Guild Golden Treasure21": "33556767",
  "Guild Golden Treasure22": "33556768",
  "Guild Golden Treasure23": "33556769",
  "Guild Golden Treasure24": "33556770",
  "Guild Golden Treasure25": "33556771",
  "Guild Golden Treasure26": "33556772",
  "Guild Golden Treasure27": "33556773",
  "Guild Golden Treasure28": "33556774",
  "Guild Golden Treasure29": "33556775",
  "Guild Golden Treasure30": "33556776",
  "Jewel Cube of Union (Emerald)": "33556777",
  "Elixir of Unity Lv.2 (EXP)": "33556778",
  "Elixir of Unity Lv.2 (Honor)": "33556779",
  "Elixir of Unity Lv.2 (Honor Medal)": "33556780",
  "Elixir of Unity Lv.2 (OXP)": "33556781",
  "Elixir of Unity Lv.2 (Wing)": "33556782",
  "Elixir of Unity Lv.3 (EXP)": "33556783",
  "Elixir of Unity Lv.3 (Honor)": "33556784",
  "Elixir of Unity Lv.3 (Honor Medal)": "33556785",
  "Elixir of Unity Lv.3 (OXP)": "33556786",
  "Elixir of Unity Lv.3 (Wing)": "33556787",
  "Elixir of Unity Lv.4 (EXP)": "33556788",
  "Elixir of Unity Lv.4 (Honor)": "33556789",
  "Elixir of Unity Lv.4 (Honor Medal)": "33556790",
  "Elixir of Unity Lv.4 (OXP)": "33556791",
  "Elixir of Unity Lv.4 (Wing)": "33556792",
  "Explorer's Cube (Medium)": "33556793",
  "Explorer's Cube (High)": "33556794",
  "Explorer's Cube (Highest)": "33556795",
  "Skaild's Secret Box": "33556796",
  "[Costume] Obsidian Dragon": "33556797",
  "[Costume] Accessory of Obsidian Dragon": "33556798",
  "Giftbox - Obsidian Dragon": "33556799",
  "[Costume] Black Dragon Helmet": "33556800",
  "[Costume] Accessory of Obsidian Dragon Helmet": "33556801",
  "Giftbox - Obsidian Dragon Helmet": "33556802",
  "[Costume] Low Twin Tail": "33556803",
  "[Costume] Accessory of Low Twin Tail": "33556804",
  "Giftbox - Low Twin Tail": "33556805",
  "Command Potion (Lv. 1)": "33556807",
  "Command Potion (Lv. 2)": "33556808",
  "Command Potion (Lv. 3)": "33556809",
  "Command Potion (Lv. 4)": "33556810",
  "Command Potion (Lv. 5)": "33556811",
  "Force Wing (Rare)": "33556812",
  "Force Wing (Unique)": "33556813",
  "Force Wing (Epic)": "33556814",
  "Force Wing (Master)": "33556815",
  "Force Wing (Legend)": "33556816",
  "A piece of mysterious statue.": "33556817",
  "Essence of Wing (Rare)": "33556818",
  "Giftbox of Hicks": "33556819",
  "Force Wing Elixir": "33556820",
  "Time Reducer (5min) - Platinum Insignia": "33556821",
  "Time Reducer (10min) - Platinum Insignia": "33556822",
  "Time Reducer (30min) - Platinum Insignia": "33556823",
  "Time Reducer (1H) - Platinum Insignia": "33556824",
  "Time Reducer (3H) - Platinum Insignia": "33556825",
  "Time Reducer (8H) - Platinum Insignia": "33556826",
  "Time Reducer (12H) - Platinum Insignia": "33556827",
  "Time Reducer (1D) - Platinum Insignia": "33556828",
  "Time Reducer (3D) - Platinum Insignia": "33556829",
  "Time Reducer (7D) - Platinum Insignia": "33556830",
  "[Vehicle Costume] King Leo the Sky Guardian": "33556831",
  "Giftbox - [Costume] King Leo the Guardian of Sky - Vehicle": "33556832",
  "[Costume] Space Predator Mask": "33556833",
  "[Costume] Space Predator Accessory": "33556834",
  "Giftbox - [Costume] Space Predator": "33556835",
  "[Costume] Xautja Mask": "33556836",
  "[Costume] Xautja Accessory": "33556837",
  "Giftbox - [Costume] Xautja Mask": "33556838",
  "[Wing Costume] Star Fly (Blue)": "33556839",
  "Giftbox - [Force Wing Costume] Star Fly Wing(Blue)": "33556840",
  "[Wing Costume] Star Fly (Yellow)": "33556841",
  "[Force Wing Costume] Giftbox - Star Fly Wing(Yellow)": "33556842",
  "[Wing Costume] Star Fly (Emerald)": "33556843",
  "Giftbox - [Force Wing Costume] Star Fly Wing(Emerald)": "33556844",
  "Box of Luck": "33557697",
  "Yuan's Upgrade Box - Low": "33556846",
  "Yuan's Upgrade Box - Medium": "33556847",
  "Yuan's Upgrade Box - High": "33556848",
  "Yuan's Upgrade Box - Highest": "33556849",
  "Gold Insignia Elixir": "33556850",
  "[Vehicle Costume] Knight Dragon": "33556851",
  "Giftbox - [Costume] Knight Dragon - Vehicle": "33556852",
  "[Vehicle Costume] Red Knight Dragon": "33556853",
  "Giftbox - [Costume] Red Knight Dragon - Vehicle": "33556854",
  "[Vehicle Costume] Black Knight Dragon": "33556855",
  "Giftbox - [Costume] Black Knight Dragon - Vehicle": "********",
  "[Vehicle Costume] Cute Little Phoenix": "********",
  "Giftbox - [Costume] Cute Little Pheonix - Vehicle": "********",
  "[Costume] Red Dragon Doll Mask": "********",
  "Giftbox - [Costume] Red Dragon Doll Mask": "********",
  "[Costume] Blue Dragon Doll Mask": "********",
  "[Costume] Blue Dragon Doll Mask Accessory": "********",
  "Giftbox - [Costume] Blue Dragon Doll Mask": "********",
  "Yuan's Upgrade Box - Ultimate": "********",
  "Pet account bind change stone": "********",
  "Lucky Present Box - Low": "********",
  "Lucky Present Box - Med": "********",
  "Lucky Present Box - High": "********",
  "[Costume] Ribbon Ponytail": "********",
  "[Costume] Accessory of Ribbon Ponytail": "********",
  "Giftbox - Ribbon Ponytail": "********",
  "[Vehicle Costume] Liberty Bike": "********",
  "GiftBox- [Vehicle Costume] Liberty Bike": "********",
  "Special Mastery Chest": "********",
  "Memorize Extender - Platinum Insignia": "********",
  "Attack Rate II": "********",
  "Defense Rate II": "********",
  "Troglo's Golden Fruit": "********",
  "Hirogley's Rune Box - Attack Rate II": "********",
  "Hirogley's Rune Box - Defense Rate II": "********",
  "Cube of Challenge (Ultimate)": "********",
  "Cube of Challenge (Wing)": "********",
  "Keller's Secret Box": "********",
  "[Costume] Crown Ponytail": "********",
  "[Costume] Accessory of Crown Ponytail": "********",
  "Giftbox - Crown Ponytail": "********",
  "[Vehicle Costume] Liberty Chirpy": "33556888",
  "GiftBox- [Vehicle Costume] Liberty Chirpy": "33556889",
  "Collection Chest": "33556890",
  "Dragon Slate": "33556891",
  "Dragon Jewel": "33556892",
  "Orb of the Dragon": "33556893",
  "Essence of Dragon of Light": "33556894",
  "Essence of Dragon of Darkness": "33556895",
  "STR II": "33556896",
  "Event Coupon (Type A)": "33556897",
  "Event Coupon (Type B)": "33556898",
  "Event Coupon (Type C)": "33556899",
  "Hidden Tomb Key": "33556900",
  "Legendary Cube - Ancient Tomb": "33556901",
  "Ignore Evasion": "33556902",
  "Saint's Forcecalibur (WA)": "33556903",
  "Saint's Forcecalibur (BL)": "33556904",
  "Saint's Forcecalibur (WI)": "33556905",
  "Saint's Forcecalibur (FA)": "33556906",
  "Saint's Forcecalibur (FS)": "33556907",
  "Saint's Forcecalibur (FB)": "33556908",
  "Saint's Forcecalibur (GL)": "33556909",
  "Saint's Forcecalibur (FG)": "33556910",
  "[Vehicle Costume] Red Beetle": "33556911",
  "Giftbox - [Vehicle Costume] Red Beetle": "33556912",
  "[Vehicle Costume] Yellow Beetle": "33556913",
  "Giftbox - [Vehicle Costume] Yellow Beetle": "33556914",
  "[Vehicle Costume] Green Beetle": "33556915",
  "Giftbox - [Vehicle Costume] Green Beetle": "33556916",
  "Sirius' Unbinding Stone (Ultimate) - Weapon": "33556917",
  "Sirius' Unbinding Stone (Ultimate) - Armor": "33556918",
  "Minesta's Unbinding Stone (Ultimate) - Weapon": "33556919",
  "Minesta's Unbinding Stone (Ultimate) - Armor": "33556920",
  "[Vehicle Costume] Skull Bike": "33556921",
  "Giftbox - [Vehicle Costume] Skull Bike": "33556922",
  "[Vehicle Costume] Ghost Knight": "33556923",
  "Gift Box - [Vehicle Costume] Ghost Knight": "33556924",
  "[Costume] Fountain Hair": "33556925",
  "[Costume] Accessory of Long Ponytail Hair": "33556926",
  "Giftbox - Long Ponytail Hair": "33556927",
  "[Costume] Rabbit Twin Tail": "33556928",
  "[Costume] Accessory of Rabbit Twin Tail": "33556929",
  "Giftbox - [Costume] Rabbit Twin Tail": "33556930",
  "[Wing Costume] Liberty Guardian": "33556931",
  "GiftBox- [Wing Costume] Liberty Guardian": "33556932",
  "[Vehicle Costume] Three Star Bike": "33556933",
  "Giftbox - [Costume] Three Star Bike - Vehicle": "33556934",
  "[Vehicle Costume] Carnaval Chirpy": "33556935",
  "Giftbox - [Costume] Carnaval Chirpy - Vehicle": "33556936",
  "Epic Booster (Ultimate)": "33556937",
  "Elixir of Honor (Rank 1+)": "33556938",
  "Elixir of Honor (Rank 5+)": "33556939",
  "Elixir of Honor (Rank 6+)": "33556940",
  "Elixir of Honor (Rank 7+)": "33556941",
  "Elixir of Honor (Rank 8+)": "33556942",
  "Elixir of Honor (Rank 9+)": "33556943",
  "Elixir of Honor (Rank 10+)": "33556944",
  "Elixir of Honor (Rank 11+)": "33556945",
  "Elixir of Honor (Rank 12+)": "33556946",
  "Elixir of Honor (Rank 13+)": "33556947",
  "Elixir of Honor (Rank 14+)": "33556948",
  "Elixir of Honor (Rank 15+)": "33556949",
  "Elixir of Honor (Rank 16+)": "33556950",
  "Elixir of Honor (Rank 17+)": "33556951",
  "Elixir of Honor (Rank 18+)": "33556952",
  "Elixir of Honor (Rank 19+)": "33556953",
  "OXP Elixir (OLV1+)": "33556954",
  "OXP Elixir (OLV11+)": "33556955",
  "OXP Elixir (OLV21+)": "33556956",
  "OXP Elixir (OLV31+)": "33556957",
  "OXP Elixir (OLV41+)": "33556958",
  "OXP Elixir (OLV51+)": "33556959",
  "OXP Elixir (OLV61+)": "33556960",
  "OXP Elixir (OLV71+)": "33556961",
  "OXP Elixir (OLV81+)": "33556962",
  "OXP Elixir (OLV91+)": "33556963",
  "EXP Elixir (Lv1+)": "33556964",
  "EXP Elixir (Lv11+)": "33556965",
  "EXP Elixir (Lv31+)": "33556966",
  "EXP Elixir (Lv51+)": "33556967",
  "EXP Elixir (Lv71+)": "33556968",
  "EXP Elixir (Lv91+)": "33556969",
  "EXP Elixir (Lv111+)": "33556970",
  "EXP Elixir (Lv131+)": "33556971",
  "EXP Elixir (Lv151+)": "33556972",
  "EXP Elixir (Lv161+)": "33556973",
  "EXP Elixir (Lv171+)": "33556974",
  "EXP Elixir (Lv181+)": "33556975",
  "EXP Elixir (Lv191+)": "33556976",
  "EXP Elixir (Lv192+)": "33556977",
  "EXP Elixir (Lv193+)": "33556978",
  "EXP Elixir (Lv194+)": "33556979",
  "EXP Elixir (Lv195+)": "33556980",
  "EXP Elixir (Lv196+)": "33556981",
  "EXP Elixir (Lv197+)": "33556982",
  "EXP Elixir (Lv198+)": "33556983",
  "EXP Elixir (Lv199+)": "33556984",
  "Essence of Gale (5min)": "33556985",
  "Crystal of Fire": "33556986",
  "Crystal of Ice": "33556987",
  "Crystal of Wind": "33556988",
  "Crystal of Earth": "33556989",
  "Troglo's White Gold Fruit": "33556990",
  "Lamp that illuminates the Soul": "33556991",
  "Ignore Resist Skill Amp.": "33556992",
  "Legendary Cube - Frozen Canyon": "33556993",
  "Perfect Core (Ultimate) +1": "33556994",
  "Perfect Core (Ultimate) +2": "33556995",
  "Perfect Core (Ultimate) +3": "33556996",
  "Perfect Core (Ultimate) +4": "33556997",
  "Perfect Core (Ultimate) +5": "33556998",
  "Perfect Core (Ultimate) +6": "33556999",
  "Perfect Core (Ultimate) +7": "33557000",
  "Perfect Core (Ultimate) +8": "33557001",
  "Perfect Core (Ultimate) +9": "33557002",
  "Perfect Core (Ultimate) +10": "33557003",
  "Perfect Core (Ultimate) +11": "33557004",
  "Perfect Core (Ultimate) +12": "33557005",
  "Perfect Core (Ultimate) +13": "33557006",
  "Perfect Core (Ultimate) +14": "33557007",
  "Perfect Core (Ultimate) +15": "33557008",
  "Sirius' Secret Box": "33557009",
  "Troglo's Obsidian Fruit": "33557010",
  "Hirogley's Rune Box - Resist Critical DMG": "33557011",
  "Mysterious Branch": "33557012",
  "Essence of Wing (Unique)": "33557013",
  "Essence of Wing (Epic)": "33557014",
  "Essence of Wing (Master)": "33557015",
  "Essence of Wing (Legend)": "33557016",
  "Essence of Chaos Guardian (Bracelet)": "33557017",
  "Chaos Safeguard - Bracelet": "33557018",
  "Token of Retaliator": "33557019",
  "Essence of Low Level Ruler": "33557020",
  "Essence of Med Level Ruler": "33557021",
  "Essence of High Level Ruler": "33557022",
  "Essence of Highest Level Ruler": "33557023",
  "Decision of Ruler": "33557024",
  "Critical Ring +5": "33557025",
  "Ring of Luck +5": "33557026",
  "Skill Book  (Frenzy - Target Focus)": "33557028",
  "Skillbook (Frenzy)": "33557029",
  "Skillbook (Dooms blade)": "33557030",
  "Skillbook (Force Unleashing(Target Focus))": "33557031",
  "Skillbook (Force Unleashing)": "33557032",
  "Skillbook (Sharp Instinct)": "33557033",
  "Skillbook (Preemptive Defense)": "33557034",
  "Skillbook (Elemental Discharge)": "33557035",
  "Skillbook (Furious)": "33557036",
  "Skillbook (Over Fire)": "33557037",
  "Skill Book (Rhinos Toughness)": "33557038",
  "Skillbook (Vampiric Movement)": "33557039",
  "Skillbook (Force Shell)": "33557040",
  "Skillbook (Sprit Walk)": "33557041",
  "Skillbook (Shield Castle)": "33557042",
  "Skillbook (Elemental Barrier)": "33557043",
  "Skillbook (Unbreakable)": "33557044",
  "Skillbook (Tactical Armor)": "33557045",
  "Skillbook (Charging Spirit)": "33557046",
  "Skillbook (Blind Anger)": "33557047",
  "Skillbook (Warrior Mastery)": "33557048",
  "Skillbook (Blader Mastery)": "33557049",
  "Skillbook (Wizard Mastery)": "33557050",
  "Skillbook (Force Archer Mastery)": "33557051",
  "Skillbook (Force Shielder Mastery)": "33557052",
  "Skillbook (Force Blader Mastery)": "33557053",
  "Skillbook (Gladiator Mastery)": "33557054",
  "Skillbook (Force Gunner Mastery)": "33557055",
  "Frenzy Effector": "33557056",
  "Rhinos Toughness Effector": "33557057",
  "Dooms Blade Effector": "33557058",
  "Vampiric Movement Effector": "33557059",
  "Force Unleashing Effector": "33557060",
  "Force Shell Effector": "33557061",
  "Sharp Instinct Effector": "33557062",
  "Spirit Walk Effector": "33557063",
  "Preemtive Defense Effector": "33557064",
  "Shield Castle Effector": "33557065",
  "Elemental Discharge Effector": "33557066",
  "Elemental Barrier Effector": "33557067",
  "Furious Effector": "33557068",
  "Unbreakable Effector": "33557069",
  "Overfire Effector": "33557070",
  "Tactical Armor Effector": "33557071",
  "Faded Epaulet of Undead (B1F)": "33557072",
  "Faded Epaulet of Undead (B2F) Part 1": "33557073",
  "Bluestin Suit (DM)": "33557074",
  "Bluestin Hand (DM)": "33557075",
  "Bluestin Shoes (DM)": "33557076",
  "Bluestin Headpiece (DM)": "33557077",
  "Titanium Suit (DM)": "33557078",
  "Titanium Hand (DM)": "33557079",
  "Titanium Shoes (DM)": "33557080",
  "Titanium Headpiece (DM)": "33557081",
  "Shadow Titanium Suit (DM)": "33557082",
  "Shadow Titanium Hand (DM)": "33557083",
  "Shadow Titanium Shoes (DM)": "33557084",
  "Shadow Titanium Headpiece (DM)": "33557085",
  "Osmium Suit (DM)": "33557086",
  "Osmium Hand (DM)": "33557087",
  "Osmium Shoes (DM)": "33557088",
  "Osmium Headpiece (DM)": "33557089",
  "Mystic Suit (DM)": "33557090",
  "Mystic Hand (DM)": "33557091",
  "Mystic Shoes (DM)": "33557092",
  "Mystic Headpiece (DM)": "33557093",
  "SIGmetal Suit (DM)": "33557094",
  "SIGmetal Hand (DM)": "33557095",
  "SIGmetal Shoes (DM)": "33557096",
  "SIGmetal Headpiece (DM)": "33557097",
  "Forcium Suit (DM)": "33557098",
  "Forcium Hand (DM)": "33557099",
  "Forcium Shoes (DM)": "33557100",
  "Forcium Headpiece (DM)": "33557101",
  "Archridium Suit (DM)": "33557102",
  "Archridium Hand (DM)": "33557103",
  "Archridium Shoes (DM)": "33557104",
  "Archridium Headpiece (DM)": "33557105",
  "Palladium Suit (DM)": "33557106",
  "Palladium Hand (DM)": "33557107",
  "Palladium Shoes (DM)": "33557108",
  "Palladium Headpiece (DM)": "33557109",
  "Demonite Suit (DM)": "33557110",
  "Demonite Hand (DM)": "33557111",
  "Demonite Shoes (DM)": "33557112",
  "Demonite Headpiece (DM)": "33557113",
  "Drei Frame Suit (DM)": "33557114",
  "Drei Frame Hand (DM)": "33557115",
  "Drei Frame Shoes (DM)": "33557116",
  "Drei Frame Headpiece (DM)": "33557117",
  "Craftman's Bluestin Suit (DM)": "33557118",
  "Craftman's Bluestin Hand (DM)": "33557119",
  "Craftman's Bluestin Shoes (DM)": "33557120",
  "Craftman's Bluestin Headpiece (DM)": "33557121",
  "Craftman's Titanium Suit (DM)": "33557122",
  "Craftman's Titanium Hand (DM)": "33557123",
  "Craftman's Titanium Shoes (DM)": "33557124",
  "Craftman's Titanium Headpiece (DM)": "33557125",
  "Craftman's Shadow Titanium Suit (DM)": "33557126",
  "Craftman's Shadow Titanium Hand (DM)": "33557127",
  "Craftman's Shadow Titanium Shoes (DM)": "33557128",
  "Craftman's Shadow Titanium Headpiece (DM)": "33557129",
  "Craftman's Osmium Suit (DM)": "33557130",
  "Craftman's Osmium Hand (DM)": "33557131",
  "Craftman's Osmium Shoes (DM)": "33557132",
  "Craftman's Osmium Headpiece (DM)": "33557133",
  "Craftman's Mystic Suit (DM)": "33557134",
  "Craftman's Mystic Hand (DM)": "33557135",
  "Craftman's Mystic Shoes (DM)": "33557136",
  "Craftman's Mystic Headpiece (DM)": "33557137",
  "Craftman's SIGmetal Suit (DM)": "33557138",
  "Craftman's SIGmetal Hand (DM)": "33557139",
  "Craftman's SIGmetal Shoes (DM)": "33557140",
  "Craftman's SIGmetal Headpiece (DM)": "33557141",
  "Craftman's Forcium Suit (DM)": "33557142",
  "Craftman's Forcium Hand (DM)": "33557143",
  "Craftman's Forcium Shoes (DM)": "33557144",
  "Craftman's Forcium Headpiece (DM)": "33557145",
  "Craftman's Archridium Suit (DM)": "33557146",
  "Craftman's Archridium Hand (DM)": "33557147",
  "Craftman's Archridium Shoes (DM)": "33557148",
  "Craftman's Archridium Headpiece (DM)": "33557149",
  "Craftman's Palladium Suit (DM)": "33557150",
  "Craftman's Palladium Hand (DM)": "33557151",
  "Craftman's Palladium Shoes (DM)": "33557152",
  "Craftman's Palladium Headpiece (DM)": "33557153",
  "Master's Bluestin Suit (DM)": "33557154",
  "Master's Bluestin Hand (DM)": "33557155",
  "Master's Bluestin Shoes (DM)": "33557156",
  "Master's Bluestin Headpiece (DM)": "33557157",
  "Master's Titanium Suit (DM)": "33557158",
  "Master's Titanium Hand (DM)": "33557159",
  "Master's Titanium Shoes (DM)": "33557160",
  "Master's Titanium Headpiece (DM)": "33557161",
  "Master's Shadow Titanium Suit (DM)": "33557162",
  "Master's Shadow Titanium Hand (DM)": "33557163",
  "Master's Shadow Titanium Shoes (DM)": "33557164",
  "Master's Shadow Titanium Headpiece (DM)": "33557165",
  "Master's Osmium Suit (DM)": "33557166",
  "Master's Osmium Hand (DM)": "33557167",
  "Master's Osmium Shoes (DM)": "33557168",
  "Master's Osmium Headpiece (DM)": "33557169",
  "Master's Mystic Suit (DM)": "33557170",
  "Master's Mystic Hand (DM)": "33557171",
  "Master's Mystic Shoes (DM)": "33557172",
  "Master's Mystic Headpiece (DM)": "33557173",
  "Master's SIGmetal Suit (DM)": "33557174",
  "Master's SIGmetal Hand (DM)": "33557175",
  "Master's SIGmetal Shoes (DM)": "33557176",
  "Master's SIGmetal Headpiece (DM)": "33557177",
  "Master's Forcium Suit (DM)": "33557178",
  "Master's Forcium Hand (DM)": "33557179",
  "Master's Forcium Shoes (DM)": "33557180",
  "Master's Forcium Headpiece (DM)": "33557181",
  "Master's Archridium Suit (DM)": "33557182",
  "Master's Archridium Hand (DM)": "33557183",
  "Master's Archridium Shoes (DM)": "33557184",
  "Master's Archridium Headpiece (DM)": "33557185",
  "Master's Palladium Suit (DM)": "33557186",
  "Master's Palladium Hand (DM)": "33557187",
  "Master's Palladium Shoes (DM)": "33557188",
  "Master's Palladium Headpiece (DM)": "33557189",
  "Skillbook (Skull Shooter)": "33557190",
  "Skillbook (Bone Javelin)": "33557191",
  "Skillbook (Dark Ray)": "33557192",
  "Skillbook (Dark Explosion)": "33557193",
  "Skillbook (Death Crasher)": "33557194",
  "Skillbook (Bone Ballista)": "33557195",
  "Skillbook (Dark Ray Diffusion)": "33557196",
  "Skillbook (Night Fall)": "33557197",
  "Skillbook (Shadow Fall)": "33557198",
  "Skillbook (Dark Scream)": "33557199",
  "Skillbook (Bone Gluttony)": "33557200",
  "Skillbook (Deadly Smasher)": "33557201",
  "Skillbook (Assassinate Bomber)": "33557202",
  "Skillbook (Venom Affair)": "33557203",
  "Skillbook (Demons Waltz)": "33557204",
  "Skillbook (Creeping Pain)": "33557205",
  "Skillbook (Revenge of Nature)": "33557206",
  "Skillbook (Spirit Swarm)": "33557207",
  "Skillbook (Soul Intake)": "33557208",
  "Skillbook (Totem of Venom)": "33557209",
  "Skillbook (Butcher's Hell)": "33557210",
  "Skillbook (Totem of Dystrophy)": "33557211",
  "Skillbook (Offering of Hell)": "33557212",
  "Skillbook (Darkmatter Fallen)": "33557213",
  "Skillbook (Totem of Absorption)": "33557214",
  "Skillbook (Reaper of Harvest)": "33557215",
  "Skillbook (Spirit Redemption)": "33557216",
  "Skillbook (Vanquish of Evil)": "33557217",
  "Skillbook (Corps Explosion)": "33557218",
  "Skillbook (Hell's Requiem)": "33557219",
  "Skillbook (Defensive Posture)": "33557220",
  "Skillbook (Crumble body)": "33557221",
  "Skillbook (Curse of Blooding)": "33557222",
  "Skillbook (Leg fracture)": "33557223",
  "Skillbook (Bloodless)": "33557224",
  "Skillbook (Skin Weakness)": "33557225",
  "Skillbook (Silence)": "33557226",
  "Skillbook (Bone Shield)": "33557227",
  "Skillbook (Petrified One)": "33557228",
  "Skillbook (Art of Blood Thrust)": "33557229",
  "Skillbook (Demon Trigger Speciality Stage 2)": "33557230",
  "Skillbook (Demon Trigger Speciality Stage 3)": "33557231",
  "Battle Mode 3 Dark Mage": "33557232",
  "Battle Mode 3 Dark Mage Capella": "33557233",
  "Battle Mode 3 Dark Mage Procyon": "33557234",
  "Battle Mode 3 Dark Mage Headgear": "33557235",
  "Battle Mode 3 Dark Mage Headgear Capella": "33557236",
  "Battle Mode 3 Dark Mage Headgear Procyon": "33557237",
  "Capella (DM)": "33557238",
  "Procyon (DM)": "33557239",
  "Skillbook (Demon Trigger)": "33557240",
  "Hands of Teer Triple (DM)": "33557241",
  "Shoes of Teer Triple (DM)": "33557242",
  "Suit of Fox Major (DM)": "33557243",
  "Headpiece of Fox Major (DM)": "33557244",
  "Comoarchyte Suit (DM)": "33557245",
  "Comoarchyte Hand (DM)": "33557246",
  "Comoarchyte Shoes (DM)": "33557247",
  "Comoarchyte Headpiece (DM)": "33557248",
  "Suit of Entros (DM)": "33557249",
  "Hands of Entros (DM)": "33557250",
  "Shoes of Entros (DM)": "33557251",
  "Headpiece of Entros (DM)": "33557252",
  "Character Slot Extender": "33557253",
  "Skillbook (Vital Recovery)": "33557254",
  "Skillbook (Adaptive barrier)": "33557255",
  "Skillbook (Dark Mage Mastery)": "33557256",
  "Drifting Bottle (Awakened)": "33557257",
  "Legendary Cube - Mirage Island (Awakened)": "33557258",
  "Piece of Chief Laxar's Crystal": "33557259",
  "Sealed Chief Laxar's Brooch": "33557260",
  "Chief Laxar's Brooch (Low)": "33557261",
  "Chief Laxar's Brooch (Med)": "33557262",
  "Chief Laxar's Brooch (High)": "33557263",
  "Chief Laxar's Unbinding Stone": "33557264",
  "Cancel Ignore Penetration": "33557265",
  "Skill Book (Calamity Soul)": "33557266",
  "Skillbook (Bloody Breath)": "33557267",
  "Skillbook (Circle Crafting)": "33557268",
  "Skillbook (Amplifier)": "33557269",
  "Skillbook (Force Overload)": "33557270",
  "Saint's Forcecalibur (DM)": "33557271",
  "[Costume] Uniform of Capella (DM)": "33557272",
  "[Costume] Uniform of Procyon (DM)": "33557273",
  "Art of Blood Thrust Effector": "33557274",
  "Bone Shield Effector": "33557275",
  "Calamity Soul Effector": "33557276",
  "Bloody Breath Effector": "33557277",
  "[Costume] Demonite Suit (DM)": "33557278",
  "[Costume] Accessory of Demonite Suit (DM)": "33557279",
  "Giftbox - [Costume] Demonite Suit (DM)": "33557280",
  "[Costume] Demonite Headpiece (DM)": "33557281",
  "[Costume] Accessory of Demonite Headpiece (DM)": "33557282",
  "Giftbox - [Costume] Demonite Headpiece (DM)": "33557283",
  "[Costume] Palladium Suit (DM)": "33557284",
  "[Costume] Accessory Palladium Suit (DM)": "33557285",
  "Giftbox - [Costume] Palladium Suit (DM)": "33557286",
  "[Costume] Palladium Headpiece (DM)": "33557287",
  "[Costume] Accessory of Palladium Headpiece (DM)": "33557288",
  "Giftbox - [Costume] Palladium Headpiece (DM)": "33557289",
  "WA Support Skill Book Package II": "33557290",
  "WI Support Skill Book Package II": "33557291",
  "FA Support Skill Book Package II": "33557292",
  "FS Support Skill Book Package II": "33557293",
  "WA Universal/Specialized Skill Book Package II": "33557294",
  "DEX II": "33557295",
  "[Costume] Archridium Suit (DM)": "33557296",
  "[Costume] Accessory of Archridium Suit (DM)": "33557297",
  "Giftbox - [Costume] Archridium Suit (DM)": "33557298",
  "[Costume] Archridium Headpiece (DM)": "33557299",
  "[Costume] Accessory of Archridium Headpiece (DM)": "33557300",
  "Giftbox - [Costume] Archridium Headpiece (DM)": "33557301",
  "[Costume] Forcium Suit (DM)": "33557302",
  "[Costume] Accessory of Forcium Suit (DM)": "33557303",
  "Giftbox - [Costume] Forcium Suit (DM)": "33557304",
  "[Costume] Forcium Headpiece (DM)": "33557305",
  "[Costume] Accessory of Forcium Headpiece (DM)": "33557306",
  "Giftbox - [Costume] Forcium Headpiece (DM)": "33557307",
  "[Costume] Mystic Suit (DM)": "33557308",
  "[Costume] Accessory of Mystic Suit (DM)": "33557309",
  "Giftbox - [Costume] Mystic Suit (DM)": "33557310",
  "[Costume] Mystic Headpiece (DM)": "33557311",
  "[Costume] Accessory of Mystic Headpiece (DM)": "33557312",
  "Giftbox - [Costume] Mystic Headpiece (DM)": "33557313",
  "Giftbox - [Costume] Uniform of Capella (DM)": "33557314",
  "Giftbox - [Costume] Uniform of Procyon (DM)": "33557315",
  "Agris' Armor Box (DM)": "33557316",
  "Luminous Substance": "33557317",
  "Modification Stone": "33557318",
  "Black Substance": "33557319",
  "Old Machine Scrap": "33557320",
  "Damaged Electronic Circuit": "33557321",
  "Permanent Power Device": "33557322",
  "Broken Circuit Chip": "33557323",
  "Ignore Resist Critical DMG": "33557324",
  "Legendary Cube - Terminus Machina": "33557325",
  "Contaminated Cactus": "33557326",
  "Legendary Cube - Garden of Dust": "33557327",
  "DMG Reduction II": "33557328",
  "Battle Style Change Kit (Ultimate)": "33557329",
  "Old Map Part": "33557330",
  "Luminous : Sole": "33557331",
  "Luminous : Selena": "33557332",
  "Luminous : Eos": "33557333",
  "Grinding Drill": "33557334",
  "Luminous Fragment": "33557335",
  "Faded Red Jewel": "33557336",
  "Faded Orange Jewel": "33557337",
  "Faded Yellow Jewel": "33557338",
  "Faded Green Jewel": "33557339",
  "Faded Blue Jewel": "33557340",
  "Faded Violet Jewel": "33557341",
  "Faded White Jewel": "33557342",
  "Faded Black Jewel": "33557343",
  "Defensive Earring +8": "33557344",
  "Amulet of Resist +6": "33557345",
  "A Seal of Phantasmal - Zigdris Faello": "33557346",
  "A Seal of Phantasmal - Barlog": "33557347",
  "A Seal of Phantasmal - Kanaph Lycanus": "33557348",
  "A Seal of Phantasmal - K'nith of Chaos and Darkness": "33557349",
  "A Seal of Plant - Centurion Prideus": "33557350",
  "A Seal of Plant - Siena": "33557351",
  "A Seal of Plant - Zaishu of wind and land": "33557352",
  "A Seal of Advance - Invader Mechspider": "33557353",
  "A Seal of Advance - MF-03 Drei Frame": "33557354",
  "A Seal of Advance - Shane's Evil - Destruction": "33557355",
  "A Seal of Oblivion - Orca": "33557356",
  "A Seal of Oblivion - Tyrant": "33557357",
  "A Seal of Oblivion - Dr. Mazel": "33557358",
  "A Seal of the Undead - Mergaheph": "33557359",
  "A Seal of the Undead - Killian Grausten": "33557360",
  "A Seal of the Undead - Immortal Etherno Calligo Tempus": "33557361",
  "A Seal of the Undead - Etherno Calligo Thanatos": "33557362",
  "A Seal of Island - Captain Trugust & Queen Calamity": "33557363",
  "A Seal of Island - I'sobha Jurife": "33557364",
  "A Seal of Island - Island Ruler Bariald": "33557365",
  "A Seal of Island - Destroyer Ageluth": "33557366",
  "Holyroid(Under Lv.11)": "33557367",
  "Holyroid(Under Lv.21)": "33557368",
  "Holyroid(Under Lv.31)": "33557369",
  "Holyroid(Under Lv.41)": "33557370",
  "Holyroid(Under Lv.51)": "33557371",
  "Holyroid(Under Lv.61)": "33557372",
  "Holyroid(Under Lv.71)": "33557373",
  "Holyroid(Under Lv.81)": "33557374",
  "Holyroid(Under Lv.91)": "33557375",
  "Holyroid(Under Lv.100)": "33557376",
  "Holyroid(Highest)": "33557377",
  "Test Bracelet12": "33557378",
  "Test Bracelet13": "33557379",
  "Test Bracelet14": "33557380",
  "Test Bracelet15": "33557381",
  "Test Bracelet16": "33557382",
  "Test Bracelet17": "33557383",
  "Test Bracelet18": "33557384",
  "Test Bracelet19": "33557385",
  "Test Bracelet20": "33557386",
  "Test Bracelet21": "33557387",
  "Holyroid": "33557390",
  "GM's Blessing (Lv.5) Holy Water": "33557391",
  "Elixir Material": "33557392",
  "A Holographic Map": "33557393",
  "Legendary Cube - Automata Lab": "33557394",
  "Crystal of Siena's Bracelet": "33557395",
  "Turmacan's Energy Battery": "33557396",
  "Sealed Turmacan's Bracelet": "33557397",
  "Turmacan's Bracelet(Low)": "33557398",
  "Turmacan's Bracelet(Med)": "33557399",
  "Turmacan's Bracelet(High)": "33557400",
  "Supplementary Elixir of Unity (EXP)": "33557401",
  "Supplementary Elixir of Unity (Honor)": "33557402",
  "Supplementary Elixir of Unity (OXP)": "33557403",
  "Penetration II": "33557404",
  "Offense Potion of War": "33557406",
  "Elixir of Myth(Low)": "33557407",
  "Elixir of Myth(Med)": "33557408",
  "Elixir of Myth(High)": "33557409",
  "Hit of Binding Potion": "33557410",
  "Supplementary Elixir of Unity (MXP)": "33557411",
  "[Costume] Metro": "33557412",
  "[Costume] Accessory of Metro": "33557413",
  "Giftbox - [Costume] Metro": "33557414",
  "[Costume] Metro Look": "33557415",
  "[Costume] Accessory of Metro Look": "33557416",
  "Giftbox - [Costume] Metro Look": "33557417",
  "[Costume] Charming Rose": "33557418",
  "[Costume] Accessory of Fatal Passion": "33557419",
  "Giftbox - [Costume] Charming Rose": "33557420",
  "[Costume] Fatal Passion Look": "33557421",
  "[Costume] Accessory of Fatal Passion Look": "33557422",
  "Giftbox - [Costume] Fatal Passion Look": "33557423",
  "[Wing Costume] Sakura": "33557424",
  "Giftbox- [Wing Costume] Sakura": "33557425",
  "[Costume] Admiral": "33557426",
  "[Costume] Accessory of Admiral": "33557427",
  "GiftBox- [Costume] Admiral": "33557428",
  "[Costume] Admiral Hat": "33557429",
  "[Costume] Accessory of Admiral Hat": "33557430",
  "Giftbox- [Costume] Admiral Hat": "33557431",
  "Scroll of Ordeal": "33557432",
  "Token of Ordeal": "33557433",
  "Silver Wood's Treasure Box": "33557434",
  "[Costume] Chicken Suit": "33557435",
  "[Costume] Accessory of Chicken Suit": "33557436",
  "Giftbox - [Costume] Chicken Suit": "33557437",
  "[Costume] Chicken Hat": "33557438",
  "[Costume] Accessory of Chicken Hat": "33557439",
  "Giftbox - [Costume] Chicken Hat": "33557440",
  "[Costume] Royal Dog Suit": "33557441",
  "[Costume] Accessory of Royal Dog Suit": "33557445",
  "Giftbox - [Costume] Royal Dog Suit": "33557443",
  "[Costume] Royal Dog Hat": "33557444",
  "Giftbox - [Costume] Royal Dog Hat": "33557446",
  "Reward of Observer - Low": "33557447",
  "Reward of Observer - Med": "33557448",
  "Reward of Observer - High": "33557449",
  "Crystal of Temple Box - Low": "33557450",
  "Crystal of Temple Box - Med": "33557451",
  "Crystal of Temple Box - High": "33557452",
  "Fighter's Soulstone Box - Low": "33557453",
  "Fighter's Soulstone Box - Med": "33557454",
  "Fighter's Soulstone Box - High": "33557455",
  "Delight of Observer": "33557456",
  "Grace of Observer": "33557457",
  "Holy of Observer": "33557458",
  "Crystal of Temple(Low)": "33557459",
  "Crystal of Temple(Med)": "33557460",
  "Crystal of Temple(High)": "33557461",
  "Fighter's Soul Stone(Low)": "33557462",
  "Fighter's Soul Stone(High)": "33557463",
  "Defense Potion of Fighter": "33557464",
  "Attack Potion of Fighter": "33557465",
  "Infinite Ordeal - Luminous Box": "33557466",
  "[Costume] Traditional Filipino Dress": "33557467",
  "[Costume] Accessory of Traditional Filipino Dress": "33557468",
  "Giftbox - [Costume] Traditional Filipino Dress": "33557469",
  "[Costume] Traditional Filipino Hair": "33557470",
  "[Costume] Accessory of Traditional Filipino Hair": "33557471",
  "Giftbox - [Costume] Traditional Filipino Hair": "33557472",
  "Dragonium Orb": "33557473",
  "Dragonium Crystal": "33557474",
  "Dragonium Katana": "33557475",
  "Dragonium Blade": "33557476",
  "Dragonium Daikatana": "33557477",
  "Dragonium Greatsword": "33557478",
  "Dragonium Chakram": "33557479",
  "Dragonium Plate (WA)": "33557480",
  "Dragonium Plate (FS)": "33557481",
  "Dragonium Plate (GL)": "33557482",
  "Dragonium Coast (FB)": "33557483",
  "Dragonium Coat (FA)": "33557484",
  "Dragonium Coat (FG)": "33557485",
  "Dragonium Suit (BL)": "33557486",
  "Dragonium Suit (WI)": "33557487",
  "Dragonium Suit (DM)": "33557488",
  "Dragonium Gauntlet (WA)": "33557489",
  "Dragonium Gauntlet (FS)": "33557490",
  "Dragonium Gauntlet (GL)": "33557491",
  "Dragonium Glove (FB)": "33557492",
  "Dragonium Glove (FA)": "33557493",
  "Dragonium Glove (FG)": "33557494",
  "Dragonium Hand (BL)": "33557495",
  "Dragonium Hand (WI)": "33557496",
  "Dragonium Hand (DM)": "33557497",
  "Dragonium Greave (WA)": "33557498",
  "Dragonium Greave (FS)": "33557499",
  "Dragonium Greave (GL)": "33557500",
  "Dragonium Boots (FB)": "33557501",
  "Dragonium Boots (FA)": "33557502",
  "Dragonium Boots (FG)": "33557503",
  "Dragonium Shoes (BL)": "33557504",
  "Dragonium Shoes (WI)": "33557505",
  "Dragonium Shoes (DM)": "33557506",
  "Dragonium Visor (WA)": "33557507",
  "Dragonium Visor (FS)": "33557508",
  "Dragonium Visor (GL)": "33557509",
  "Dragonium Headgear (FB)": "33557510",
  "Dragonium Headgear (FA)": "33557511",
  "Dragonium Headgear (FG)": "33557512",
  "Dragonium Headpiece (BL)": "33557513",
  "Dragonium Headpiece (WI)": "33557514",
  "Dragonium Headpiece (DM)": "33557515",
  "Dragonium Epaulet of Guardian": "33557516",
  "Dragonium Epaulet of Laws": "33557517",
  "Dragonium Epaulet of Fighter": "33557518",
  "Dragonium Epaulet of Sage": "33557519",
  "Material Core (Dragonium)": "33557520",
  "Quartz Core (Dragonium)": "33557521",
  "Astral Core (Dragonium)": "33557522",
  "INT II": "33557523",
  "Dark Mage ATK Skill Book Package I": "33557524",
  "Dark Mage ATK Skill Book Package II": "33557525",
  "Dark Mage Support Skill Book Package I": "33557526",
  "Dark Mage Support Skill Book Package II": "33557527",
  "Dark Mage Universal/Specialized Skill Book Package": "33557528",
  "Dark Mage Skill Book Package": "33557529",
  "Option Scroll Cube (Ultimate)": "33557530",
  "Mystical Feather": "33557531",
  "Key of the Mighty Fighter": "33557532",
  "Shiny Piece of Ruby": "33557533",
  "Shiny Piece of Amber": "33557534",
  "Shiny Piece of Topaz": "33557535",
  "Shiny Piece of Emerald": "33557536",
  "Shiny Piece of Sapphire": "33557537",
  "Shiny Piece of Aquamarine": "33557538",
  "Shiny Piece of Amethyst": "33557539",
  "Shiny Piece of Jade": "33557540",
  "Shiny Piece of Black Quartz": "33557541",
  "Shiny Piece of Diamond": "33557542",
  "Shiny Ruby": "33557543",
  "Shiny Amber": "33557544",
  "Shiny Topaz": "33557545",
  "Shiny Emerald": "33557546",
  "Shiny Sapphire": "33557547",
  "Shiny Aquamarine": "33557548",
  "Shiny Amethyst": "33557549",
  "Shiny Jade": "33557550",
  "Shiny Black Quartz": "33557551",
  "Shiny Diamond": "33557552",
  "[Costume] Dragonium Plate": "33557553",
  "[Costume] Accessory of Dragonium Plate": "33557554",
  "Giftbox - [Costume] Dragonium Plate": "33557555",
  "[Costume] Dragonium Visor": "33557556",
  "[Costume] Dragonium Visor Accessory": "33557557",
  "Giftbox - [Costume] Dragonium Visor": "33557558",
  "[Costume] Dragonium Coat": "33557559",
  "[Costume] Accessory of Dragonium Coat": "33557560",
  "Giftbox - [Costume] Dragonium Coat": "33557561",
  "[Costume] Dragonium Headgear": "33557562",
  "[Costume] Accessory of Dragonium Headgear": "33557563",
  "Giftbox - [Costume] Dragonium Headgear": "33557564",
  "[Costume] Dragonium Suit": "33557565",
  "[Costume] Accessory of Dragonium Suit": "33557566",
  "Giftbox - [Costume] Dragonium Suit": "33557567",
  "[Costume] Dragonium Headpiece": "33557568",
  "[Costume] Accessory of Dragonium Headpiece": "33557569",
  "Giftbox - [Costume] Dragonium Headpiece": "33557570",
  "[Costume] Accessory of Dragonium Suit (DM)": "33557571",
  "Giftbox - [Costume] Dragonium Suit (DM)": "33557572",
  "[Costume] Dragonium Headpiece (DM)": "33557573",
  "[Costume] Accessory of Dragonium Headpiece (DM)": "33557574",
  "Giftbox - [Costume] Dragonium Headpiece (DM)": "33557575",
  "Remnants of Battle": "33557576",
  "[Costume] Dragonium Suit (DM)": "33557577",
  "[Costume] Dragonium Orb": "33557578",
  "[Costume] Dragonium Katana": "33557579",
  "[Costume] Dragonium Blade": "33557580",
  "[Costume] Dragonium Crystal": "33557581",
  "[Costume] Dragonium Daikatana": "33557582",
  "[Costume] Dragonium Greatsword": "33557583",
  "[Costume] Dragonium Chakram": "33557584",
  "Giftbox - [Costume] Dragonium Orb": "33557585",
  "Giftbox - [Costume] Dragonium Katana": "33557586",
  "Giftbox - [Costume] Dragonium Blade": "33557587",
  "Giftbox - [Costume] Dragonium Crystal": "33557588",
  "Giftbox - [Costume] Dragonium Daikatana": "33557589",
  "Giftbox - [Costume] Dragonium Greatsword": "33557590",
  "Holyroid(Low)": "33557591",
  "Holyroid(Med)": "33557592",
  "Holyroid(High)": "33557593",
  "Chaos Seal Stone - Ring": "33557594",
  "Chaos Seal Stone - Amulet": "33557595",
  "Chaos Seal Stone - Epaulet": "33557596",
  "Chaos Seal Stone - Earring": "33557597",
  "Chaos Seal Stone - Bracelet": "33557598",
  "Chaos Safeguard - Ring": "33557599",
  "Chaos Safeguard - Epaulet": "33557600",
  "Cashshop Event Item (MPH)": "33557601",
  "Chaos Converter - Ring": "33557602",
  "Chaos Converter - Amulet": "33557603",
  "Chaos Converter - Epaulet": "33557604",
  "Chaos Converter - Earring": "33557605",
  "Chaos Converter - Bracelet": "33557606",
  "Gacha Ticket": "33557636",
  "Weapon Gacha Ticket": "33557637",
  "Armor Gacha Ticket": "33557638",
  "Accessory Gacha Ticket": "33557639",
  "Core Gacha Ticket": "33557640",
  "Pet Gacha Ticket": "33557641",
  "Rune Gacha Ticket": "33557642",
  "Special Pandora Box(Event)": "33557643",
  "Normal Pandora Box": "33557644",
  "Rare Pandora Box": "33557645",
  "Special Pandora Box": "33557646",
  "Ultimate Upgrade Box": "33557647",
  "Ultimate Shiny Upgrade Box": "33557648",
  "Ultimate Upgrade Box of Faith": "33557649",
  "Proof of Nevareth Traveler": "33557650",
  "Proof of Nevareth Fighter": "33557805",
  "Armor Box(WA) (Lv. 1)": "33557652",
  "Weapon Box(WA) (Lv. 1)": "33557653",
  "Armor Box(BL) (Lv. 1)": "33557654",
  "Weapon Box(BL) (Lv. 1)": "33557655",
  "Armor Box(WI) (Lv. 1)": "33557656",
  "Weapon Box(WI) (Lv. 1)": "33557657",
  "Armor Box(FA) (Lv. 1)": "33557658",
  "Weapon Box(FA) (Lv. 1)": "33557659",
  "Armor Box(FS) (Lv. 1)": "33557660",
  "Weapon Box(FS) (Lv. 1)": "33557661",
  "Armor Box(FB) (Lv. 1)": "33557662",
  "Weapon Box(FB) (Lv. 1)": "33557663",
  "Armor Box(GL) (Lv. 1)": "33557664",
  "Weapon Box(GL) (Lv. 1)": "33557665",
  "Armor Box(FG) (Lv. 1)": "33557666",
  "Weapon Box(FG) (Lv. 1)": "33557667",
  "Armor Box(WA) (Lv. 2)": "33557668",
  "Weapon Box(WA) (Lv. 2)": "33557669",
  "Armor Box(BL) (Lv. 2)": "33557670",
  "Weapon Box(BL) (Lv. 2)": "33557671",
  "Armor Box(WI) (Lv. 2)": "33557672",
  "Weapon Box(WI) (Lv. 2)": "33557673",
  "Armor Box(FA) (Lv. 2)": "33557674",
  "Weapon Box(FA) (Lv. 2)": "33557675",
  "Armor Box(FS) (Lv. 2)": "33557676",
  "Weapon Box(FS) (Lv. 2)": "33557677",
  "Armor Box(FB) (Lv. 2)": "33557678",
  "Weapon Box(FB) (Lv. 2)": "33557679",
  "Armor Box(GL) (Lv. 2)": "33557680",
  "Weapon Box(GL) (Lv. 2)": "33557681",
  "Armor Box(FG) (Lv. 2)": "33557682",
  "Weapon Box(FG) (Lv. 2)": "33557683",
  "Low Upgrade Box": "33557698",
  "Medium Upgrade Box": "33557699",
  "High Upgrade Box": "33557700",
  "Highest Upgrade Box": "33557701",
  "Low Shiny Upgrade Box": "33557702",
  "High Shiny Upgrade Box": "33557703",
  "Highest Shiny Upgrade Box": "33557704",
  "Blessing Bead - EXP UP (1000%)": "33557692",
  "Blessing Bead - Skill EXP UP (1000%)": "33557693",
  "Blessing Bead - WExp UP(1000%)": "33557694",
  "Blessing Bead - AXP UP(1000%)": "33557695",
  "Blessing Bead - Box Drop Rate Up(1000%)": "33557696",
  "Medium Faith Upgrade Box": "33557705",
  "High Faith Upgrade Box": "33557706",
  "Highest Faith Upgrade Box": "33557707",
  "Minesta's Charm Box": "33557708",
  "Talisman's Box": "33557709",
  "Minesta's Belt Box": "33557710",
  "Deightons' Supply Box": "33557711",
  "Mysterious Core Box": "33557712",
  "Daily Bead Box": "33557713",
  "Daily Saver Box": "33557714",
  "Explorer Orb": "33557715",
  "Light Orb": "33557716",
  "Explorer Crystal": "33557717",
  "Light Crystal": "33557718",
  "Explorer Katana": "33557719",
  "Light Katana": "33557720",
  "Explorer Blade": "33557721",
  "Light Blade": "33557722",
  "Explorer Great Sword": "33557723",
  "Light Great Sword": "33557724",
  "Explorer Armorsuit": "33557725",
  "Explorer Battlesuit": "33557726",
  "Explorer Martialsuit": "33557727",
  "Explorer Armorgloves": "33557728",
  "Explorer Battlegloves": "33557729",
  "Explorer Martialgloves": "33557730",
  "Explorer Armorboots": "33557731",
  "Explorer Battleboots": "33557732",
  "Explorer Martialboots": "33557733",
  "Explorer Armorhelm": "33557734",
  "Explorer Battlehelm": "33557735",
  "Explorer Martialhelm": "33557736",
  "Light Plate (WA)": "33557737",
  "Light Plate (FS)": "33557738",
  "Light Coat (FB)": "33557739",
  "Light Coat (FA)": "33557740",
  "Light Suit (BL)": "33557741",
  "Light Suit (WI)": "33557742",
  "Light Gauntlet (WA)": "33557743",
  "Light Gauntlet (FS)": "33557744",
  "Light Glove (FB)": "33557745",
  "Light Glove (FA)": "33557746",
  "Light Hand (BL)": "33557747",
  "Light Hand (WI)": "33557748",
  "Light Greaves (WA)": "33557749",
  "Light Greaves (FS)": "33557750",
  "Light Boots (FB)": "33557751",
  "Light Boots (FA)": "33557752",
  "Light Shoes (BL)": "33557753",
  "Light Shoes (WI)": "33557754",
  "Light Visor (WA)": "33557755",
  "Light Visor (FS)": "33557756",
  "Light Headgear (FB)": "33557757",
  "Light Headgear (FA)": "33557758",
  "Light Headpiece (BL)": "33557759",
  "Light Headpiece (WI)": "33557760",
  "Explorer Chakram": "33557761",
  "Light Chakram": "33557762",
  "Light Visor (GL)": "33557763",
  "Light Plate (GL)": "33557764",
  "Light Guntlet (GL)": "33557765",
  "Light Greaves (GL)": "33557766",
  "Light Coat (GL)": "33557767",
  "Light Glove (FG)": "33557768",
  "Light Boots (FG)": "33557769",
  "Light Headgear (FG)": "33557770",
  "Explorer Armor Box (WA)": "33557771",
  "Explorer Weapon Box (WA)": "33557772",
  "Explorer Armor Box (BL)": "33557773",
  "Explorer Weapon Box (BL)": "33557774",
  "Explorer Armor Box (WI)": "33557775",
  "Explorer Weapon Box (WI)": "33557776",
  "Explorer Armor Box (FA)": "33557777",
  "Explorer Weapon Box (FA)": "33557778",
  "Explorer Armor Box (FS)": "33557779",
  "Explorer Weapon Box (FS)": "33557780",
  "Explorer Armor Box (FB)": "33557781",
  "Explorer Weapon Box (FB)": "33557782",
  "Explorer Armor Box (GL)": "33557783",
  "Explorer Weapon Box (GL)": "33557784",
  "Explorer Armor Box (FG)": "33557785",
  "Explorer Weapon Box (FG)": "33557786",
  "Novice Armor Box (WA)": "33557787",
  "Novice Weapon Box (WA)": "33557788",
  "Novice Armor Box (BL)": "33557789",
  "Novice Weapon Box (BL)": "33557790",
  "Novice Armor Box (WI)": "33557791",
  "Novice Weapon Box (WI)": "33557792",
  "Novice Armor Box (FA)": "33557793",
  "Novice Weapon Box (FA)": "33557794",
  "Novice Armor Box (FS)": "33557795",
  "Novice Weapon Box (FS)": "33557796",
  "Novice Armor Box (FB)": "33557797",
  "Novice Weapon Box (FB)": "33557798",
  "Novice Armor Box (GL)": "33557799",
  "Novice Weapon Box (GL)": "33557800",
  "Novice Armor Box (FG)": "33557801",
  "Novice Weapon Box (FG)": "33557802",
  "Arcana Box": "33557804",
  "Deightons' High Grade Supply Box": "33557806",
  "Blessing Bead - Silver Wing": "33557807",
  "Blessing Bead - Platinum Wing (TH Dummy 2)": "33557808",
  "Blessing Bead - Platinum Wing (TH Dummy 3)": "33557809",
  "Sullivan's rare box": "33557810",
  "Blessing Bead - Platinum Wing (TH Dummy 4)": "33557811",
  "Blessing Bead - Platinum Wing (TH Dummy 5)": "33557812",
  "Blessing Bead - Platinum Wing (TH Dummy 6)": "33557813",
  "Box of Chaos Seal Stone (Earring)": "33557816",
  "Eillens Supply Box": "33557817",
  "Sally's Treasure Box": "33557818",
  "Middle Orb": "33557819",
  "Middle Crystal": "33557820",
  "Middle Katana": "33557821",
  "Middle Blade": "33557822",
  "Middle Greatsword": "33557823",
  "Middle Plate (WA)": "33557824",
  "Middle Plate (FS)": "33557825",
  "Middle Coat (FB)": "33557826",
  "Middle Coat (FA)": "33557827",
  "Middle Suit (BL)": "33557828",
  "Middle Suit (WI)": "33557829",
  "Middle Gauntlets (WA)": "33557830",
  "Middle Gauntlets (FS)": "33557831",
  "Middle Gloves (FB)": "33557832",
  "Middle Gloves (FA)": "33557833",
  "Middle Hands (BL)": "33557834",
  "Middle Hands (WI)": "33557835",
  "Middle Greaves (WA)": "33557836",
  "Middle Greaves (FS)": "33557837",
  "Middle Boots (FB)": "33557838",
  "Middle Boots (FA)": "33557839",
  "Middle Shoes (BL)": "33557840",
  "Middle Shoes (WI)": "33557841",
  "Middle Visors (WA)": "33557842",
  "Middle Visors (FS)": "33557843",
  "Middle Headgear (FB)": "33557844",
  "Middle Headgear (FA)": "33557845",
  "Middle Headpiece (BL)": "33557846",
  "Middle Headpiece (WI)": "33557847",
  "Middle Chakram": "33557848",
  "Middle Visors (GL)": "33557849",
  "Middle Plate (GL)": "33557850",
  "Middle Gauntlets (GL)": "33557851",
  "Middle Greaves (GL)": "33557852",
  "Middle Coat (FG)": "33557853",
  "Middle Gloves (FG)": "33557854",
  "Middle Boots (FG)": "33557855",
  "Middle Headgear (FG)": "33557856",
  "Trained Armor Box(WA)": "33557857",
  "Trained Weapon Box(WA)": "33557858",
  "Trained Armor Box(BL)": "33557859",
  "Trained Weapon Box(BL)": "33557860",
  "Trained Armor Box(WI)": "33557861",
  "Trained Weapon Box(WI)": "33557862",
  "Trained Armor Box(FA)": "33557863",
  "Trained Weapon Box (FA)": "33557864",
  "Trained Armor Box(FS)": "33557865",
  "Trained Weapon Box (FS)": "33557866",
  "Trained Armor Box(FB)": "33557867",
  "Trained Weapon Box (FB)": "33557868",
  "Trained Armor Box(GL)": "33557869",
  "Trained Weapon Box (GL)": "33557870",
  "Trained Armor Box(FG)": "33557871",
  "Trained Weapon Box(FG)": "33557872",
  "SIGMetal Orb Box": "33557873",
  "SIGMetal Crystal Box": "33557874",
  "SIGMetal Katana Box": "33557875",
  "SIGMetal Blade Box": "33557876",
  "SIGMetal Greatsword Box": "33557877",
  "SIGMetal Plate Box(WA)": "33557878",
  "SIGMetal Plate Box(FS)": "33557879",
  "SIGMetal Coat Box(FB)": "33557880",
  "SIGMetal Coat Box(FA)": "33557881",
  "SIGMetal Suit Box (BL)": "33557882",
  "SIGMetal Suit Box (WI)": "33557883",
  "SIGMetal Gauntlets Box(WA)": "33557884",
  "SIGMetal Gauntlets Box(FS)": "33557885",
  "SIGMetal Gloves Box(FB)": "33557886",
  "SIGMetal Gloves Box (FA)": "33557887",
  "SIGMetal Hands Box (BL)": "33557888",
  "SIGMetal Hands Box (WI)": "33557889",
  "SIGMetal Greaves Box (WA)": "33557890",
  "SIGMetal Greaves Box (FS)": "33557891",
  "SIGMetal Boots Box (FB)": "33557892",
  "SIGMetal Boots Box (FA)": "33557893",
  "SIGMetal Shoes Box (BL)": "33557894",
  "SIGMetal Shoes Box (WI)": "33557895",
  "SIGMetal Visor Box (WA)": "33557896",
  "SIGMetal Visor Box (FS)": "33557897",
  "SIGMetal Headgear Box (FB)": "33557898",
  "SIGMetal Headgear Box (FA)": "33557899",
  "SIGMetal Headpiece Box (BL)": "33557900",
  "SIGMetal Headpiece Box (WI)": "33557901",
  "SIGMetal Chakram Box": "33557902",
  "SIGMetal Visor Box (GL)": "33557903",
  "SIGMetal Plate Box (GL)": "33557904",
  "SIGMetal Gauntlets Box (GL)": "33557905",
  "SIGMetal Greaves Box (GL)": "33557906",
  "SIGMetal Coat Box (FG)": "33557907",
  "SIGMetal Gloves Box (FG)": "33557908",
  "SIGMetal Boots Box (FG)": "33557909",
  "SIGMetal Headgear Box (FG)": "33557910",
  "Drosnin's Earrings Box": "33557911",
  "Prideus' Bracelet Box": "33557912",
  "Siena's Bracelet Box": "33557913",
  "Light Suit (DM)": "33557914",
  "Light Hands (DM)": "33557915",
  "Light Shoes (DM)": "33557916",
  "Light Headpiece (DM)": "33557917",
  "Novice Armor Box (DM)": "33557918",
  "Novice Weapon Box (DM)": "33557919",
  "SIGMetal Armor Box (DM)": "33557920",
  "SIGMetal Weapon Box (DM)": "33557921",
  "Crystal Bike of Fighter": "33557922",
  "Crystal Bike of Sage": "33557923",
  "Crystal Bike of Extreme": "33557924",
  "Jewel Cube - Sapphire (Bronze)": "33557925",
  "Jewel Cube - Ruby (Bronze)": "33557926",
  "Jewel Cube - Sapphire (Silver)": "33557927",
  "Jewel Cube - Ruby (Silver)": "33557928",
  "Jewel Cube - Sapphire (Gold)": "33557929",
  "Jewel Cube - Ruby (Gold)": "33557930",
  "Fierce Orb": "33557931",
  "Fierce Crystal": "33557932",
  "Fierce Katana": "33557933",
  "Fierce Blade": "33557934",
  "Fierce Greatsword": "33557935",
  "Fierce Plate (WA)": "33557936",
  "Fierce Plate (FS)": "33557937",
  "Fierce Coat (FB)": "33557938",
  "Fierce Coat (FA)": "33557939",
  "Fierce Shoes (BL)": "33557952",
  "Fierce Shoes (WI)": "33557953",
  "Fierce Gauntlets (WA)": "33557942",
  "Fierce Gauntlets (FS)": "33557943",
  "Fierce Gloves (FB)": "33557944",
  "Fierce Gloves (FA)": "33557945",
  "Fierce Hands (BL)": "33557946",
  "Fierce Hands (WI)": "33557947",
  "Fierce Greaves (WA)": "33557948",
  "Fierce Greaves (FS)": "33557949",
  "Fierce Boots (FB)": "33557950",
  "Fierce Boots (FA)": "33557951",
  "Fierce Visors (WA)": "33557954",
  "Fierce Visors (FS)": "33557955",
  "Fierce Headgear (FB)": "33557956",
  "Fierce Headgear (FA)": "33557957",
  "Fierce Headpiece (BL)": "33557958",
  "Fierce Headpiece (WI)": "33557959",
  "Fierce Chakram": "33557960",
  "Fierce Visors (GL)": "33557961",
  "Fierce Plate (GL)": "33557962",
  "Fierce Gauntlets (GL)": "33557963",
  "Fierce Greaves (GL)": "33557964",
  "Fierce Coat (FG)": "33557965",
  "Fierce Gloves (FG)": "33557966",
  "Fierce Boots (FG)": "33557967",
  "Fierce Headgear (FG)": "33557968",
  "Fierce Armor Box (WA)": "33557969",
  "Fierce Weapon Box (WA)": "33557970",
  "Fierce Armor Box (BL)": "33557971",
  "Fierce Weapon Box (BL)": "33557972",
  "Fierce Armor Box (WI)": "33557973",
  "Fierce Weapon Box (WI)": "33557974",
  "Fierce Armor Box (FA)": "33557975",
  "Fierce Weapon Box (FA)": "33557976",
  "Fierce Armor Box (FS)": "33557977",
  "Fierce Weapon Box (FS)": "33557978",
  "Fierce Armor Box (FB)": "33557979",
  "Fierce Weapon Box (FB)": "33557980",
  "Fierce Armor Box (GL)": "33557981",
  "Fierce Weapon Box (GL)": "33557982",
  "Fierce Armor Box (FG)": "33557983",
  "Fierce Weapon Box (FG)": "33557984",
  "Karma Rune Exchange Ticket": "33557985",
  "Karma Rune Exchange Ticket Box (Green)": "33557986",
  "Karma Rune Exchange Ticket Box (Purple)": "33557987",
  "Rune Slot Extender - Karma": "33557988",
  "PVP Resist All Skill Amp.": "33557990",
  "PVP All Attack": "33557991",
  "PVE All Attack": "33557992",
  "A fragment of memory": "33557993",
  "Huge Forcegem Package": "33557994",
  "Giftbox - [Costume] Dragonium Chakram": "33558021",
  "Upgrade Token(Ultimate)": "33558022",
  "Upgrade Token(Divine)": "33558023",
  "Sealed Agent Yul's Earrings": "33558024",
  "Drosnin's Magic Core": "33558025",
  "Perius' Empty Heart": "33558026",
  "Sealed Perius' Earrings": "33558027",
  "Ascending Perius' Earrings (Lv. 1)": "33558028",
  "Ascending Perius' Earrings (Lv. 2)": "33558029",
  "Ascending Perius' Earrings (Lv. 3)": "33558030",
  "Eroding Perius' Earrings (Lv. 1)": "33558031",
  "Eroding Perius' Earrings (Lv. 2)": "33558032",
  "Eroding Perius' Earrings (Lv. 3)": "33558033",
  "Dimensional Ship's Key": "33558034",
  "Legendary Cube - Celestia": "33558035",
  "Battle Token": "33558036",
  "[Vehicle Costume] Royal Chirpy": "33558037",
  "Giftbox - [Vehicle Costume] Royal Chirpy": "33558038",
  "[Wing Costume] Courageous": "33558039",
  "Giftbox - [Wing Costume] Courageous": "33558040",
  "[Costume] Forest Guardian": "33558041",
  "[Costume] Accessory of Forest Guardian": "33558042",
  "Giftbox - [Costume] Forest Guardian": "33558043",
  "[Costume] Forest Guardian Face": "33558044",
  "[Costume] Accessory of Forest Guardian Face": "33558045",
  "Giftbox - [Costume] Forest Guardian Face": "33558046",
  "[Wing Costume] Forest Guardian": "33558047",
  "Giftbox - [Wing Costume] Forest Guardian": "33558048",
  "Ignore Penetration II": "33558049",
  "Crest of Life": "33558050",
  "Crest of Sky": "33558051",
  "Crest of Earth": "33558052",
  "Shining Crest of Life": "33558057",
  "Mountain Radar": "33558058",
  "Gaia Force Amulet (Lv. 1)": "33558059",
  "Gaia Force Amulet (Lv. 2)": "33558060",
  "Gaia Force Amulet (Lv. 3)": "33558061",
  "Genuine Gaia Force Amulet": "33558062",
  "Sealed Gaia Force Amulet": "33558063",
  "Serum of Earth": "33558064",
  "Orphidia's Soul": "33558065",
  "Gaia's Mercy": "33558066",
  "Sealed Terra Force Amulet": "33558067",
  "Terra Force Amulet": "33558068",
  "HP III": "33558069",
  "Legendary Cube - Purifier in the Forest": "33558070",
  "New Amulet for Force Code testing": "33558071",
  "[Costume] Siam Dress": "33558072",
  "[Costume] Accessory of Siam Dress": "33558073",
  "Giftbox - [Costume] Siam Dress": "33558074",
  "[Costume] Siam Dress Face": "33558075",
  "[Costume] Accessory of Siam Dress Face": "33558076",
  "Giftbox - [Costume] Siam Dress Face": "33558077",
  "PVE Attack Rate": "33558078",
  "PVP Attack Rate": "33558079",
  "PVE Defense Rate": "33558080",
  "PVP Defense Rate": "33558081",
  "PVE All Skill Amp. UP": "33558082",
  "PVP All Skill Amp. UP": "33558083",
  "PVE Defense": "33558084",
  "PVP Defense": "33558085",
  "PVE DMG Reduction": "33558086",
  "PVP DMG Reduction": "33558087",
  "PVE Ignore DMG Reduction": "33558088",
  "PVP Ignore DMG Reduction": "33558089",
  "PVE Cancel Ignore DMG Reduction": "33558090",
  "PVP Cancel Ignore DMG Reduction": "33558091",
  "PVE Penetration": "33558092",
  "PVP Penetration": "33558093",
  "PVE Ignore Penetration": "33558094",
  "PVP Ignore Penetration": "33558095",
  "PVE Cancel Ignore Penetration": "33558096",
  "PVP Cancel Ignore Penetration": "33558097",
  "PVE Critical DMG": "33558098",
  "PVP Critical DMG": "33558099",
  "PVE Critical Rate": "33558100",
  "PVE Accuracy": "33558101",
  "PVP Accuracy": "33558102",
  "PVE Ignore Accuracy": "33558103",
  "PVP Ignore Accuracy": "33558104",
  "PVE Evasion": "33558105",
  "PVP Evasion": "33558106",
  "PVE Ignore Evasion": "33558107",
  "PVP Ignore Evasion": "33558108",
  "PVE Final DMG Reduction": "33558109",
  "PVP Final DMG Reduction": "33558110",
  "PVE Final DMG UP": "33558111",
  "PVP Final DMG UP": "33558112",
  "[Costume] Toy - Orb": "33558113",
  "[Costume] Toy - Katana": "33558114",
  "[Costume] Toy - Blade": "33558115",
  "[Costume] Toy - Crystal": "33558116",
  "[Costume] Toy - Daikatana": "33558117",
  "[Costume] Toy - Great Sword": "33558118",
  "[Costume] Toy - Chakram": "33558119",
  "Giftbox - [Costume] Toy - Orb": "33558120",
  "Giftbox - [Costume] Toy - Katana": "33558121",
  "Giftbox - [Costume] Toy - Blade": "33558122",
  "Giftbox - [Costume] Toy - Crystal": "33558123",
  "Giftbox - [Costume] Toy - Daikatana": "33558124",
  "Giftbox - [Costume] Toy - Great Sword": "33558125",
  "Giftbox - [Costume] Toy - Chakram": "33558126",
  "[Wing Costume] Dark Butterfly": "33558127",
  "Giftbox - [Wing Costume] Dark Butterfly": "33558128",
  "[Wing Costume] Colorful Butterfly": "33558129",
  "Giftbox - [Wing Costume] Colorful Butterfly": "33558130",
  "[Wing Costume] Forest Butterfly": "33558131",
  "Giftbox - [Wing Costume] Forest Butterfly": "33558132",
  "[Wing Costume] Twilight Butterfly": "33558133",
  "Giftbox - [Wing Costume] Twilight Butterfly": "33558134",
  "[Wing Costume] Stained Glass Butterfly": "33558135",
  "Giftbox - [Wing Costume] Stained Glass Butterfly": "33558136",
  "[Wing Costume] White Butterfly": "33558137",
  "Giftbox - [Wing Costume] White Butterfly": "33558138",
  "[Wing Costume] Red Flame": "33558139",
  "Giftbox - [Wing Costume] Red Flame": "33558140",
  "[Wing Costume] Blue Flame": "33558141",
  "Giftbox - [Wing Costume] Blue Flame": "33558142",
  "[Wing Costume] Tropical": "33558143",
  "Giftbox - [Wing Costume] Tropical": "33558144",
  "[Costume] Pixel Weapon - Orb": "33558145",
  "[Costume] Pixel Weapon - Katana": "33558146",
  "[Costume] Pixel Weapon - Blade": "33558147",
  "[Costume] Pixel Weapon - Crystal": "33558148",
  "[Costume] Pixel Weapon - Daikatana": "33558149",
  "[Costume] Pixel Weapon - Great Sword": "33558150",
  "[Costume] Pixel Weapon - Chakram": "33558151",
  "Giftbox - [Costume] Pixel Weapon - Orb": "33558152",
  "Giftbox - [Costume] Pixel Weapon - Katana": "33558153",
  "Giftbox - [Costume] Pixel Weapon - Blade": "33558154",
  "Giftbox - [Costume] Pixel Weapon - Crystal": "33558155",
  "Giftbox - [Costume] Pixel Weapon - Daikatana": "33558156",
  "Giftbox - [Costume] Pixel Weapon - Great Sword": "33558157",
  "Giftbox - [Costume] Pixel Weapon - Chakram": "33558158",
  "[Wing Costume] Halloween Surprise": "33558159",
  "Giftbox - [Wing Costume] Halloween Surprise": "33558160",
  "[Wing Costume] Casino Royale": "33558161",
  "Giftbox - [Wing Costume] Casino Royale": "33558162",
  "Vehicle Case": "33558163",
  "Wing Storage Box": "33558164",
  "Transformation Card - Corrupted Swordsman's Specter": "33558165",
  "Transformation Card - Scorpion Guardian": "33558166",
  "Transformation Card - Mossite": "33558167",
  "Transformation Card - Bapomat": "33558168",
  "Transformation Card - Steam Floater": "33558169",
  "Transformation Card - Berserk Ghoul": "33558170",
  "Transformation Card - Mayozin": "33558171",
  "Transformation Card - Hupier(Orc 2nd Corps)": "33558172",
  "Transformation Card - Maku Sha": "33558173",
  "Transformation Card - ARM-03 Inspector": "33558174",
  "Transformation Card - Diable Beholder": "33558175",
  "Transformation Card - Evil's Shadow Curse": "33558176",
  "Transformation Card - Lesser Panda": "33558177",
  "Artifact of Dawn": "33558178",
  "Artifact of Twilight": "33558179",
  "Artifact of Midnight": "33558180",
  "Time piece of Dawn": "33558181",
  "Time piece of Twilight": "33558182",
  "Time piece of Midnight": "33558183",
  "Golden Relic Lv.1": "33558184",
  "Golden Relic Lv.2": "33558185",
  "Golden Relic Lv.3": "33558186",
  "Golden Relic Lv.4": "33558187",
  "Golden Relic Lv.5": "33558188",
  "Golden Relic Lv.6": "33558189",
  "Golden Relic Lv.1 (Inactivate)": "33558190",
  "Golden Relic Lv.2 (Inactivate)": "33558191",
  "Golden Relic Lv.3 (Inactivate)": "33558192",
  "Golden Relic Lv.4 (Inactivate)": "33558193",
  "Golden Relic Lv.5 (Inactivate)": "33558194",
  "Golden Relic Lv.6 (Inactivate)": "33558195",
  "Event_Snowball": "33558196",
  "Event_Hat": "33558197",
  "Event_Button": "33558198",
  "Event_Red Wool": "33558199",
  "Event_Red Scarf": "33558200",
  "Event_Blue Wool": "33558201",
  "Event_Blue Scarf": "33558202",
  "Event_Color Block": "33558203",
  "Event_Train": "33558204",
  "Event_Teddy Bear": "33558205",
  "Event_Assorted Pancakes": "33558206",
  "Event_Herbs": "33558207",
  "Event_Braised Beef": "33558208",
  "Event_Yakgwa": "33558209",
  "Event_Soju": "33558210",
  "Event_Soju glass": "33558211",
  "Event_Korean Traditional Food": "33558212",
  "Event_Sandwich": "33558213",
  "Event_Picnic Box": "33558214",
  "Event_Bud": "33558215",
  "Event_Yellow Tulip": "33558216",
  "Event_Red Tulip": "33558217",
  "Event_Cherry Blossom Petal": "33558218",
  "Event_Cherry Blossom": "33558219",
  "Event_Banana": "33558220",
  "Event_Strawberry": "33558221",
  "Event_Mango": "33558222",
  "Event_Melon": "33558223",
  "Event_Fruit Basket": "33558224",
  "Event_Red Bean Ice Flake": "33558225",
  "Event_Mango Ice Flake": "33558226",
  "Event_Maple Leaf": "33558227",
  "Event_Ginkgo Leaf": "33558228",
  "Event_Acorn": "33558229",
  "Yul Event Exclusive Ticket - Gold": "33558230",
  "Yul Event Exclusive Ticket - Silver": "33558231",
  "Yul Event Exclusive Ticket - Bronze": "33558232",
  "Yul Event Exclusive Ticket - Red": "33558233",
  "Yul Event Exclusive Ticket - Green": "33558234",
  "Yul Event Exclusive Ticket - Purple": "33558235",
  "Yul Event Exclusive Ticket - Blue": "33558236",
  "Yul Event Exclusive Ticket - Black": "33558237",
  "Event_Apple": "33558238",
  "Event_Pear": "33558239",
  "Event_Grape": "33558240",
  "Event_Persimmon": "33558241",
  "Event_Inividual Yul": "33558242",
  "Event_Yut Set": "33558243",
  "Event_Red Soup Pot": "33558244",
  "Event_White Soup Pot": "33558245",
  "[Vehicle Costume] Azure Vortex": "33558246",
  "Giftbox - [Vehicle Costume] Azure Vortex": "33558247",
  "Ring of Galelia +1": "33558248",
  "Ring of Galelia +2": "33558249",
  "Ring of Tolden Toad +1": "33558250",
  "Ring of Tolden Toad +2": "33558251",
  "Belt of Tommy": "33558252",
  "Belt of Tommy +1": "33558253",
  "Belt of Tommy +2": "33558254",
  "Belt of Tommy +3": "33558255",
  "Belt of Tommy +4": "33558256",
  "Belt of Tommy +5": "33558257",
  "Amulet of Matilda": "33558258",
  "Amulet of Matilda +1": "33558259",
  "Amulet of Matilda +2": "33558260",
  "Astral Board Card - The Giant Step": "33558261",
  "Obsidian GreatSword": "33558262",
  "Emerald GreatSword": "33558263",
  "Ruby GreatSword": "33558264",
  "Obsidian Blade": "33558265",
  "Emerald Blade": "33558266",
  "Ruby Blade": "33558267",
  "Obsidian Katana": "33558268",
  "Emerald Katana": "33558269",
  "Ruby Katana": "33558270",
  "Obsidian Chakram": "33558271",
  "Emerald Chakram": "33558272",
  "Ruby Chakram": "33558273",
  "Obsidian Crystal": "33558274",
  "Emerald Crystal": "33558275",
  "Ruby Crystal": "33558276",
  "Obsidian Orb": "33558277",
  "Emerald Orb": "33558278",
  "Ruby Orb": "33558279",
  "Novice Pioneer Chain Mail": "33558280",
  "Mid-level Adventurer Chain Mail": "33558281",
  "Veteran Adventure Chain Mail": "33558282",
  "Novice Pioneer Band": "33558283",
  "Mid-level Adventurer Band": "33558284",
  "Veteran Adventurer Band": "33558285",
  "Novice Pioneer Shoes": "33558286",
  "Mid-level Adventurer Shoes": "33558287",
  "Veteran Adventurer Shoes": "33558288",
  "Novice Pioneer Helmet": "33558289",
  "Mid-level Adventurer Helmet": "33558290",
  "Veteran Adventurer Helmet": "33558291",
  "Platinum Insignia Elixir": "33558292",
  "Time Reducer (5m) - Diamond Insignia": "33558293",
  "Time Reducer (10m) - Diamond Insignia": "33558294",
  "Time Reducer (30m) - Diamond Insignia": "33558295",
  "Time Reducer (1h) - Diamond Insignia": "33558296",
  "Time Reducer (3h) - Diamond Insignia": "33558297",
  "Time Reducer (8h) - Diamond Insignia": "33558298",
  "Time Reducer (12h) - Diamond Insignia": "33558299",
  "Time Reducer (1d) - Diamond Insignia": "33558300",
  "Time Reducer (3d) - Diamond Insignia": "33558301",
  "Time Reducer (7d) - Diamond Insignia": "33558302",
  "Memorize Extender - Diamond Insignia": "33558303",
  "[Costume] Dark Dragon - Orbs": "33558304",
  "[Costume] Dark Dragon - Katana": "33558305",
  "[Costume] Dark Dragon - Blade": "33558306",
  "[Costume] Dark Dragon - Crystal": "33558307",
  "[Costume] Dark Dragon - Daikatana": "33558308",
  "[Costume] Dark Dragon - Great Sword": "33558309",
  "[Costume] Dark Dragon - Chakram": "33558310",
  "Giftbox- [Costume] Dark Dragon - Orbs": "33558311",
  "Giftbox- [Costume] Dark Dragon - Katana": "33558312",
  "Giftbox- [Costume] Dark Dragon - Blade": "33558313",
  "Giftbox- [Costume] Dark Dragon - Crystal": "33558314",
  "Giftbox- [Costume] Dark Dragon - Daikatana": "33558315",
  "Giftbox- [Costume] Dark Dragon - Great Sword": "33558316",
  "Giftbox- [Costume] Dark Dragon - Chakram": "33558317",
  "[Costume] Dark Imperial Dress": "33558318",
  "[Costume] Accessory of Dark Imperial Dress": "33558319",
  "Giftbox - [Costume] Dark Imperial Dress": "33558320",
  "Agent Shop Coupon(Blue)": "33558321",
  "Agent Shop Coupon(Red)": "33558322",
  "Agent Shop Coupon(Green)": "33558323",
  "Agent Shop Coupon(Purple)": "33558324",
  "Agent Shop Coupon(Black)": "33558325",
  "empty": "33558526",
  "[Costume] Ruler of the stars": "33558417",
  "[Costume] Accessory of Ruler of the stars": "33558418",
  "Giftbox - [Costume] Ruler of the stars": "33558419",
  "[Costume] Ruler of the stars Face": "33558420",
  "[Costume] Accessory of Ruler of the stars Face": "33558421",
  "Giftbox - [Costume] Ruler of the stars Face": "33558422",
  "[Costume] Pop-Star": "33558423",
  "[Costume] Accessory of Pop-Star": "33558424",
  "Giftbox - [Costume] Pop-Star": "33558425",
  "[Costume] Pop-Star Face": "33558426",
  "[Costume] Accessory of Pop-Star Face": "33558427",
  "Giftbox - [Costume] Pop-Star Face": "33558428",
  "[Costume] Winter Admiral": "33558433",
  "[Costume] Accessory of Winter Admiral": "33558434",
  "Giftbox - [Costume] Winter Admiral": "33558435",
  "[Costume] Admiral Face": "33558436",
  "[Costume] Accessory of Winter Admiral Face": "33558437",
  "Giftbox - [Costume] Winter Admiral Face": "33558438",
  "Perfect Core +1 (Low)": "3356",
  "Perfect Core +2 (Low)": "3357",
  "Perfect Core +3 (Low)": "3358",
  "Perfect Core +4 (Low)": "3359",
  "Force Absorb Ring +4": "336",
  "Perfect Core +5 (Low)": "3360",
  "Perfect Core +6 (Low)": "3361",
  "Perfect Core +7 (Low)": "3362",
  "Perfect Core +8 (Low)": "3363",
  "Perfect Core +9 (Low)": "3364",
  "Perfect Core +10 (Low)": "3365",
  "Perfect Core +11 (Low)": "3366",
  "Perfect Core +12 (Low)": "3367",
  "Perfect Core +13 (Low)": "3368",
  "Perfect Core +14 (Low)": "3369",
  "Force Absorb Ring +5": "337",
  "Perfect Core +15 (Low)": "3370",
  "Perfect Core +1 (Medium)": "3371",
  "Perfect Core +2 (Medium)": "3372",
  "Perfect Core +3 (Medium)": "3373",
  "Perfect Core +4 (Medium)": "3374",
  "Perfect Core +5 (Medium)": "3375",
  "Perfect Core +6 (Medium)": "3376",
  "Perfect Core +7 (Medium)": "3377",
  "Perfect Core +8 (Medium)": "3378",
  "Perfect Core +9 (Medium)": "3379",
  "Ring Of Seven Coins": "338",
  "Perfect Core +10 (Medium)": "3380",
  "Perfect Core +11 (Medium)": "3381",
  "Perfect Core +12 (Medium)": "3382",
  "Perfect Core +13 (Medium)": "3383",
  "Perfect Core +14 (Medium)": "3384",
  "Perfect Core +15 (Medium)": "3385",
  "Perfect Core +1 (High)": "3386",
  "Perfect Core +2 (High)": "3387",
  "Perfect Core +3 (High)": "3388",
  "Perfect Core +4 (High)": "3389",
  "Ring Of Seven Coins +1": "339",
  "Perfect Core +5 (High)": "3390",
  "Perfect Core +6 (High)": "3391",
  "Perfect Core +7 (High)": "3392",
  "Perfect Core +8 (High)": "3393",
  "Perfect Core +9 (High)": "3394",
  "Perfect Core +10 (High)": "3395",
  "Perfect Core +11 (High)": "3396",
  "Perfect Core +12 (High)": "3397",
  "Perfect Core +13 (High)": "3398",
  "Perfect Core +14 (High)": "3399",
  "Citrine Crystal": "34",
  "Ring Of Seven Coins +2": "340",
  "Perfect Core +15 (High)": "3400",
  "Perfect Core +1 (Highest)": "3401",
  "Perfect Core +2 (Highest)": "3402",
  "Perfect Core +3 (Highest)": "3403",
  "Perfect Core +4 (Highest)": "3404",
  "Perfect Core +5 (Highest)": "3405",
  "Perfect Core +6 (Highest)": "3406",
  "Perfect Core +7 (Highest)": "3407",
  "Perfect Core +8 (Highest)": "3408",
  "Perfect Core +9 (Highest)": "3409",
  "Ring Of Seven Coins +3": "341",
  "Perfect Core +10 (Highest)": "3410",
  "Perfect Core +11 (Highest)": "3411",
  "Perfect Core +12 (Highest)": "3412",
  "Perfect Core +13 (Highest)": "3413",
  "Perfect Core +14 (Highest)": "3414",
  "Perfect Core +15 (Highest)": "3415",
  "Guild Creator": "3416",
  "Guild Level Up (Lv. 2)": "3417",
  "Guild Level Up (Lv. 3)": "3418",
  "Guild Level Up (Lv. 4)": "3419",
  "Ring Of Seven Coins +4": "342",
  "Guild Level Up (Lv. 5)": "3420",
  "Weapon Option Scroll Protector (Low)": "3421",
  "Weapon Option Scroll Protector (Medium)": "3422",
  "Weapon Option Scroll Protector (High)": "3423",
  "Weapon Option Scroll Protector (Highest)": "3424",
  "Armor Option Scroll Protector (Low)": "3425",
  "Armor Option Scroll Protector (Medium)": "3426",
  "Armor Option Scroll Protector (High)": "3427",
  "Armor Option Scroll Protector (Highest)": "3428",
  "Bike Option Scroll Protector (Low)": "3429",
  "Ring Of Seven Coins +5": "343",
  "Bike Option Scroll Protector (Medium)": "3430",
  "Bike Option Scroll Protector (High)": "3431",
  "Bike Option Scroll Protector (Highest)": "3432",
  "Item Option Remover (Low)": "3433",
  "Item Option Remover (Medium)": "3434",
  "Item Option Remover (High)": "3435",
  "Item Option Remover (Highest)": "3436",
  "Stat Reset Potion": "3437",
  "Pet - Alpa": "3438",
  "Pet - Alca": "3439",
  "Ring Of Seven Coins +6": "344",
  "Pet - Paca": "3440",
  "Pet - Alpaca": "3441",
  "Blessing Bead - Special Inventory": "3442",
  "Minisha's Relic I": "3443",
  "Minisha's Relic II": "3444",
  "Minisha's Relic III": "3445",
  "Minisha's Relic IV": "3446",
  "Minisha's Relic V": "3447",
  "Minisha's Relic VI": "3448",
  "Cube of Lost Skills_1": "3449",
  "Ring Of Seven Coins +7": "345",
  "Cube of Lost Skills_2": "3450",
  "Cube of Lost Skills_3": "3451",
  "Cube of Lost Skills_4": "3452",
  "Cube of Lost Skills_5": "3453",
  "Cube of Lost Skills_6": "3454",
  "Cube of Lost Skills_7": "3455",
  "Dungeon Entry Cube_1": "3456",
  "Dungeon Entry Cube_2": "3457",
  "Cube of Equipment Upgrade_1": "3458",
  "Cube of Equipment Upgrade_2": "3459",
  "Adept Ring": "346",
  "Cube of Equipment Upgrade_3": "3460",
  "Cube of Equipment Upgrade_4": "3461",
  "Cube of Equipment Upgrade_5": "3462",
  "Cube of Equipment Upgrade_6": "3463",
  "Cube of Equipment Upgrade_7": "3464",
  "Cube of Equipment Upgrade_8": "3465",
  "Cube of Equipment Upgrade_9": "3466",
  "Cube of Experience_1": "3467",
  "Cube of Experience_2": "3468",
  "Bike Deco (Red Flare)": "3469",
  "Adept Ring +1": "347",
  "Bike Deco (Blue Flare)": "3470",
  "Powerful Elixir (Lv. 1)": "3471",
  "Powerful Elixir (Lv. 2)": "3472",
  "Powerful Elixir (Lv. 3)": "3473",
  "Powerful Elixir (Lv. 4)": "3474",
  "Critical Amp. Elixir (Lv. 1)": "3475",
  "Critical Amp. Elixir (Lv. 2)": "3476",
  "Critical Amp. Elixir (Lv. 3)": "3477",
  "Critical Amp. Elixir (Lv. 4)": "3478",
  "Charge Elixir (Lv. 1)": "3479",
  "Adept Ring +2": "348",
  "Charge Elixir (Lv. 2)": "3480",
  "Charge Elixir (Lv. 3)": "3481",
  "Charge Elixir (Lv. 4)": "3482",
  "Evade Amp. Elixir (Lv. 1)": "3483",
  "Evade Amp. Elixir (Lv. 2)": "3484",
  "Evade Amp. Elixir (Lv. 3)": "3485",
  "Evade Amp. Elixir (Lv. 4)": "3486",
  "Gale Elixir (Lv. 1)": "3487",
  "Gale Elixir (Lv. 2)": "3488",
  "Gale Elixir (Lv. 3)": "3489",
  "Adept Ring +3": "349",
  "Gale Elixir (Lv. 4)": "3490",
  "Typhoon Elixir (Lv. 1)": "3491",
  "Typhoon Elixir (Lv. 2)": "3492",
  "Typhoon Elixir (Lv. 3)": "3493",
  "Typhoon Elixir (Lv. 4)": "3494",
  "Immortal Elixir (Lv. 1)": "3495",
  "Immortal Elixir (Lv. 2)": "3496",
  "Immortal Elixir (Lv. 3)": "3497",
  "Immortal Elixir (Lv. 4)": "3498",
  "Magic Elixir (Lv. 1)": "3499",
  "Bluestin Crystal": "35",
  "Amulet of Strength": "350",
  "Magic Elixir (Lv. 2)": "3500",
  "Magic Elixir (Lv. 3)": "3501",
  "Magic Attack Elixir (Lv. 1)": "3502",
  "Magic Attack Elixir (Lv. 2)": "3503",
  "Magic Attack Elixir (Lv. 3)": "3504",
  "Physical Elixir (Lv. 1)": "3505",
  "Physical Elixir (Lv. 2)": "3506",
  "Physical Elixir (Lv. 3)": "3507",
  "Physical Attack Elixir (Lv. 1)": "3508",
  "Physical Attack Elixir (Lv. 2)": "3509",
  "Power Amulet": "351",
  "Physical Attack Elixir (Lv. 3)": "3510",
  "Magic Attack Capsule (Lv. 1)": "3511",
  "Magic Attack Capsule (Lv. 2)": "3512",
  "Magic Attack Capsule (Lv. 3)": "3513",
  "Physical Attack Capsule (Lv. 1)": "3514",
  "Physical Attack Capsule (Lv. 2)": "3515",
  "Physical Attack Capsule (Lv. 3)": "3516",
  "Critical Elixir (Lv. 1)": "3517",
  "Critical Elixir (Lv. 2)": "3518",
  "Critical Elixir (Lv. 3)": "3519",
  "Ogre Power Amulet": "352",
  "Rage Elixir (Lv. 1)": "3520",
  "Rage Elixir (Lv. 2)": "3521",
  "Rage Elixir (Lv. 3)": "3522",
  "Wind Elixir (Lv. 1)": "3523",
  "Wind Elixir (Lv. 2)": "3524",
  "Wind Elixir (Lv. 3)": "3525",
  "Defense Elixir (Lv. 1)": "3526",
  "Defense Elixir (Lv. 2)": "3527",
  "Defense Elixir (Lv. 3)": "3528",
  "Psi Elixir (Lv. 1)": "3529",
  "Atlas Amulet": "353",
  "Psi Elixir (Lv. 2)": "3530",
  "Psi Elixir (Lv. 3)": "3531",
  "[Costume] Male - None, Female - Nurse": "3532",
  "[Costume] Male - None, Female - Nurse Cap": "3533",
  "[Costume] Nevareth Angel in White": "3534",
  "[Costume] Nevareth Angel in White Cap": "3535",
  "Giftbox - [Costume] Nevareth Angel in White": "3536",
  "Giftbox - [Costume] Nevareth Angel in White Cap": "3537",
  "Ninja Epaulet": "3538",
  "Ninja Hood": "3539",
  "Amulet of Intelligence": "354",
  "[Costume] CABAL Assassin": "3540",
  "[Costume] CABAL Assassin Hood": "3541",
  "Giftbox - [Costume] CABAL Assassin": "3542",
  "GiftBox - [Costume] CABAL Assassin Hood": "3543",
  "Battle Mode3 Armor Capella": "3544",
  "Battle Mode3 Marshal Capella": "3545",
  "Battle Mode3 Battle Capella": "3546",
  "Battle Mode3 Armor Procyon": "3547",
  "Battle Mode3 Marshal Procyon": "3548",
  "Battle Mode3 Battle Procyon": "3549",
  "Smart Amulet": "355",
  "Battle Mode3 Armor Visor Capella": "3550",
  "Battle Mode3 Marshal Headpiece Capella": "3551",
  "Battle Mode3 Battle Headgear Capella": "3552",
  "Battle Mode3 Armor Visor Procyon": "3553",
  "Battle Mode3 Marshal Headpiece Procyon": "3554",
  "Battle Mode3 Battle Headgear Procyon": "3555",
  "Peticia's Reward Cube": "3556",
  "Bracelet Of Fighter +8": "3557",
  "Bracelet Of Sage +8": "3558",
  "Earring of Guard +8": "3559",
  "Genius Amulet": "356",
  "Vampiric Earring +8": "3560",
  "Craftsman's Ring (Lv. 1)": "3561",
  "Craftsman's Ring (Lv. 2)": "3562",
  "Craftsman's Ring (Lv. 3)": "3563",
  "Craftsman's Ring (Lv. 4)": "3564",
  "Craftsman's Amulet (Lv. 1)": "3565",
  "Craftsman's Amulet (Lv. 2)": "3566",
  "Craftsman's Amulet (Lv. 3)": "3567",
  "Craftsman's Amulet (Lv. 4)": "3568",
  "Bike Binding Converter (Lv. 2)": "3569",
  "Enlightenment Amulet": "357",
  "Bike Binding Converter (Lv. 3)": "3570",
  "Bike Parts (Lv. 2)": "3571",
  "Bike Parts (Lv. 3)": "3572",
  "Bike Epic Converter Box (Lv. 2)": "3573",
  "Bike Epic Converter Box (Lv. 3)": "3574",
  "[Costume] Summer Vacation": "3575",
  "[Costume] Epaulet of Summer Vacation": "3576",
  "Giftbox - [Costume] Summer Vacation": "3577",
  "[Costume] Vacation Sun Hat": "3578",
  "[Costume] Sun hat of Vacation": "3579",
  "Amulet of Dexterity": "358",
  "Giftbox - [Costume] Vacation Sun Hat": "3580",
  "Gem of Liberation": "3581",
  "Unbinding Stone Cube (Medium)": "3582",
  "Unbinding Stone Cube (High)": "3583",
  "Unbinding Stone Cube (Highest)": "3584",
  "Minesta's Unbinding Stone (Highest) - Boots": "3585",
  "Minesta's Unbinding Stone (Highest) - Helmet": "3586",
  "Sirius' Unbinding Stone (Highest) - Helmet": "3587",
  "Superior Odd Circle": "3588",
  "Nation Megaphone": "3589",
  "Fast Amulet": "359",
  "Training Cube": "3590",
  "Craftsman's Bluestin Orb": "3591",
  "Craftsman's Pherystin Orb": "3592",
  "Craftsman's Aqua Orb": "3593",
  "Craftsman's Lapis Orb": "3594",
  "Craftsman's Topaz Orb": "3595",
  "Craftsman's SIGMetal Orb": "3596",
  "Craftsman's Forcium Orb": "3597",
  "Craftsman's Bluestin Crystal": "3598",
  "Craftsman's Pherystin Crystal": "3599",
  "Pherystin Crystal": "36",
  "Rapid Amulet": "360",
  "Craftsman's Aqua Crystal": "3600",
  "Craftsman's Lapis Crystal": "3601",
  "Craftsman's Topaz Crystal": "3602",
  "Craftsman's SIGMetal Crystal": "3603",
  "Craftsman's Forcium Crystal": "3604",
  "Craftsman's Bluestin Katana": "3605",
  "Craftsman's Titanium Katana": "3606",
  "Craftsman's Shadow Titanium Katana": "3607",
  "Craftsman's Osmium Katana": "3608",
  "Craftsman's Red Osmium Katana": "3609",
  "Quickness Amulet": "361",
  "Craftsman's SIGMetal Katana": "3610",
  "Craftsman's Forcium Katana": "3611",
  "Craftsman's Bluestin Blade": "3612",
  "Craftsman's Titanium Blade": "3613",
  "Craftsman's Shadow Titanium Blade": "3614",
  "Craftsman's Osmium Blade": "3615",
  "Craftsman's Red Osmium Blade": "3616",
  "Craftsman's SIGMetal Blade": "3617",
  "Craftsman's Forcium Blade": "3618",
  "Craftsman's Bluestin Daikatana": "3619",
  "Amulet Of Bless": "362",
  "Craftsman's Titanium Daikatana": "3620",
  "Craftsman's Shadow Titanium Daikatana": "3621",
  "Craftsman's Osmium Daikatana": "3622",
  "Craftsman's Red Osmium Daikatana": "3623",
  "Craftsman's SIGMetal Daikatana": "3624",
  "Craftsman's Forcium Daikatana": "3625",
  "Craftsman's Bluestin Great Sword": "3626",
  "Craftsman's Titanium Great Sword": "3627",
  "Craftsman's Shadow Titanium Great Sword": "3628",
  "Craftsman's Osmium Great Sword": "3629",
  "Amulet Of Bless +1": "363",
  "Craftsman's Red Osmium Great Sword": "3630",
  "Craftsman's SIGMetal Great Sword": "3631",
  "Craftsman's Forcium Great Sword": "3632",
  "Craftsman's Bluestin Plate (WA)": "3633",
  "Craftsman's Bluestin Plate (FS)": "3634",
  "Craftsman's Bluestin Coat (FB)": "3635",
  "Craftsman's Bluestin Coat (FA)": "3636",
  "Craftsman's Bluestin Suit (BL)": "3637",
  "Craftsman's Bluestin Suit (WI)": "3638",
  "Craftsman's Bluestin Gauntlets (WA)": "3639",
  "Amulet Of Bless +2": "364",
  "Craftsman's Bluestin Gauntlets (FS)": "3640",
  "Craftsman's Bluestin Gloves (FB)": "3641",
  "Craftsman's Bluestin Gloves (FA)": "3642",
  "Craftsman's Bluestin Hand (BL)": "3643",
  "Craftsman's Bluestin Hand (WI)": "3644",
  "Craftsman's Bluestin Greave (WA)": "3645",
  "Craftsman's Bluestin Greave (FS)": "3646",
  "Craftsman's Bluestin Boots (FB)": "3647",
  "Craftsman's Bluestin Boots (FA)": "3648",
  "Craftsman's Bluestin Shoes (BL)": "3649",
  "Amulet Of Bless +3": "365",
  "Craftsman's Bluestin Shoes (WI)": "3650",
  "Craftsman's Bluestin Visor (WA)": "3651",
  "Craftsman's Bluestin Visor (FS)": "3652",
  "Craftsman's Bluestin Headgear (FB)": "3653",
  "Craftsman's Bluestin Headgear (FA)": "3654",
  "Craftsman's Bluestin Headpiece (BL)": "3655",
  "Craftsman's Bluestin Headpiece (WI)": "3656",
  "Craftsman's Titanium Plate (WA)": "3657",
  "Craftsman's Titanium Plate (FS)": "3658",
  "Craftsman's Titanium Coat (FB)": "3659",
  "Force Regeneration Amulet": "366",
  "Craftsman's Titanium Coat (FA)": "3660",
  "Craftsman's Titanium Suit (BL)": "3661",
  "Craftsman's Titanium Suit (WI)": "3662",
  "Craftsman's Titanium Gauntlets (WA)": "3663",
  "Craftsman's Titanium Gauntlets (FS)": "3664",
  "Craftsman's Titanium Gloves (FB)": "3665",
  "Craftsman's Titanium Gloves (FA)": "3666",
  "Craftsman's Titanium Hand (BL)": "3667",
  "Craftsman's Titanium Hand (WI)": "3668",
  "Craftsman's Titanium Greave (WA)": "3669",
  "Force Regeneration Amulet +1": "367",
  "Craftsman's Titanium Greave (FS)": "3670",
  "Craftsman's Titanium Boots (FB)": "3671",
  "Craftsman's Titanium Boots (FA)": "3672",
  "Craftsman's Titanium Shoes (BL)": "3673",
  "Craftsman's Titanium Shoes (WI)": "3674",
  "Craftsman's Titanium Visor (WA)": "3675",
  "Craftsman's Titanium Visor (FS)": "3676",
  "Craftsman's Titanium Headgear (FB)": "3677",
  "Craftsman's Titanium Headgear (FA)": "3678",
  "Craftsman's Titanium Headpiece (BL)": "3679",
  "Force Regeneration Amulet +2": "368",
  "Craftsman's Titanium Headpiece (WI)": "3680",
  "Craftsman's Shadow Titanium Plate (WA)": "3681",
  "Craftsman's Shadow Titanium Plate (FS)": "3682",
  "Craftsman's Shadow Titanium Coat (FB)": "3683",
  "Craftsman's Shadow Titanium Coat (FA)": "3684",
  "Craftsman's Shadow Titanium Suit (BL)": "3685",
  "Craftsman's Shadow Titanium Suit (WI)": "3686",
  "Craftsman's Shadow Titanium Gauntlets (WA)": "3687",
  "Craftsman's Shadow Titanium Gauntlets (FS)": "3688",
  "Craftsman's Shadow Titanium Gloves (FB)": "3689",
  "Life Regeneration Amulet": "369",
  "Craftsman's Shadow Titanium Gloves (FA)": "3690",
  "Craftsman's Shadow Titanium Hand (BL)": "3691",
  "Craftsman's Shadow Titanium Hand (WI)": "3692",
  "Craftsman's Shadow Titanium Greave (WA)": "3693",
  "Craftsman's Shadow Titanium Greave (FS)": "3694",
  "Craftsman's Shadow Titanium Boots (FB)": "3695",
  "Craftsman's Shadow Titanium Boots (FA)": "3696",
  "Craftsman's Shadow Titanium Shoes (BL)": "3697",
  "Craftsman's Shadow Titanium Shoes (WI)": "3698",
  "Craftsman's Shadow Titanium Visor (WA)": "3699",
  "Aqua Crystal": "37",
  "Life Regeneration Amulet +1": "370",
  "Craftsman's Shadow Titanium Visor (FS)": "3700",
  "Craftsman's Shadow Titanium Headgear (FB)": "3701",
  "Craftsman's Shadow Titanium Headgear (FA)": "3702",
  "Craftsman's Shadow Titanium Headpiece (BL)": "3703",
  "Craftsman's Shadow Titanium Headpiece (WI)": "3704",
  "Craftsman's Osmium Plate (WA)": "3705",
  "Craftsman's Osmium Plate (FS)": "3706",
  "Craftsman's Osmium Coat (FB)": "3707",
  "Craftsman's Osmium Coat (FA)": "3708",
  "Craftsman's Osmium Suit (BL)": "3709",
  "Life Regeneration Amulet +2": "371",
  "Craftsman's Osmium Suit (WI)": "3710",
  "Craftsman's Osmium Gauntlets (WA)": "3711",
  "Craftsman's Osmium Gauntlets (FS)": "3712",
  "Craftsman's Osmium Gloves (FB)": "3713",
  "Craftsman's Osmium Gloves (FA)": "3714",
  "Craftsman's Osmium Hand (BL)": "3715",
  "Craftsman's Osmium Hand (WI)": "3716",
  "Craftsman's Osmium Greave (WA)": "3717",
  "Craftsman's Osmium Greave (FS)": "3718",
  "Craftsman's Osmium Boots (FB)": "3719",
  "Mana Regeneration Amulet": "372",
  "Craftsman's Osmium Boots (FA)": "3720",
  "Craftsman's Osmium Shoes (BL)": "3721",
  "Craftsman's Osmium Shoes (WI)": "3722",
  "Craftsman's Osmium Visor (WA)": "3723",
  "Craftsman's Osmium Visor (FS)": "3724",
  "Craftsman's Osmium Headgear (FB)": "3725",
  "Craftsman's Osmium Headgear (FA)": "3726",
  "Craftsman's Osmium Headpiece (BL)": "3727",
  "Craftsman's Osmium Headpiece (WI)": "3728",
  "Craftsman's Shineguard Plate (WA)": "3729",
  "Mana Regeneration Amulet +1": "373",
  "Craftsman's Shineguard Plate (FS)": "3730",
  "Craftsman's Teragrace Coat (FB)": "3731",
  "Craftsman's Teragrace Coat (FA)": "3732",
  "Craftsman's Mystic Suit (BL)": "3733",
  "Craftsman's Mystic Suit (WI)": "3734",
  "Craftsman's Shineguard Gauntlets (WA)": "3735",
  "Craftsman's Shineguard Gauntlets (FS)": "3736",
  "Craftsman's Teragrace Gloves (FB)": "3737",
  "Craftsman's Teragrace Gloves (FA)": "3738",
  "Craftsman's Mystic Hand (BL)": "3739",
  "Mana Regeneration Amulet +2": "374",
  "Craftsman's Mystic Hand (WI)": "3740",
  "Craftsman's Shineguard Greave (WA)": "3741",
  "Craftsman's Shineguard Greave (FS)": "3742",
  "Craftsman's Teragrace Boots (FB)": "3743",
  "Craftsman's Teragrace Boots (FA)": "3744",
  "Craftsman's Mystic Shoes (BL)": "3745",
  "Craftsman's Mystic Shoes (WI)": "3746",
  "Craftsman's Shineguard Visor (WA)": "3747",
  "Craftsman's Shineguard Visor (FS)": "3748",
  "Craftsman's Teragrace Headgear (FB)": "3749",
  "Revive Amulet": "375",
  "Craftsman's Teragrace Headgear (FA)": "3750",
  "Craftsman's Mystic Headpiece (BL)": "3751",
  "Craftsman's Mystic Headpiece (WI)": "3752",
  "Craftsman's SIGMetal Plate (WA)": "3753",
  "Craftsman's SIGMetal Plate (FS)": "3754",
  "Craftsman's SIGMetal Coat (FB)": "3755",
  "Craftsman's SIGMetal Coat (FA)": "3756",
  "Craftsman's SIGMetal Suit (BL)": "3757",
  "Craftsman's SIGMetal Suit (WI)": "3758",
  "Craftsman's SIGMetal Gauntlets (WA)": "3759",
  "Revive Amulet +1": "376",
  "Craftsman's SIGMetal Gauntlets (FS)": "3760",
  "Craftsman's SIGMetal Gloves (FB)": "3761",
  "Craftsman's SIGMetal Gloves (FA)": "3762",
  "Craftsman's SIGMetal Hand (BL)": "3763",
  "Craftsman's SIGMetal Hand (WI)": "3764",
  "Craftsman's SIGMetal Greave (WA)": "3765",
  "Craftsman's SIGMetal Greave (FS)": "3766",
  "Craftsman's SIGMetal Boots (FB)": "3767",
  "Craftsman's SIGMetal Boots (FA)": "3768",
  "Craftsman's SIGMetal Shoes (BL)": "3769",
  "Amulet Of Guard": "377",
  "Craftsman's SIGMetal Shoes (WI)": "3770",
  "Craftsman's SIGMetal Visor (WA)": "3771",
  "Craftsman's SIGMetal Visor (FS)": "3772",
  "Craftsman's SIGMetal Headgear (FB)": "3773",
  "Craftsman's SIGMetal Headgear (FA)": "3774",
  "Craftsman's SIGMetal Headpiece (BL)": "3775",
  "Craftsman's SIGMetal Headpiece (WI)": "3776",
  "Craftsman's Forcium Plate (WA)": "3777",
  "Craftsman's Forcium Plate (FS)": "3778",
  "Craftsman's Forcium Coat (FB)": "3779",
  "Amulet Of Guard +1": "378",
  "Craftsman's Forcium Coat (FA)": "3780",
  "Craftsman's Forcium Suit (BL)": "3781",
  "Craftsman's Forcium Suit (WI)": "3782",
  "Craftsman's Forcium Gauntlets (WA)": "3783",
  "Craftsman's Forcium Gauntlets (FS)": "3784",
  "Craftsman's Forcium Gloves (FB)": "3785",
  "Craftsman's Forcium Gloves (FA)": "3786",
  "Craftsman's Forcium Hand (BL)": "3787",
  "Craftsman's Forcium Hand (WI)": "3788",
  "Craftsman's Forcium Greave (WA)": "3789",
  "Amulet Of Guard +2": "379",
  "Craftsman's Forcium Greave (FS)": "3790",
  "Craftsman's Forcium Boots (FB)": "3791",
  "Craftsman's Forcium Boots (FA)": "3792",
  "Craftsman's Forcium Shoes (BL)": "3793",
  "Craftsman's Forcium Shoes (WI)": "3794",
  "Craftsman's Forcium Visor (WA)": "3795",
  "Craftsman's Forcium Visor (FS)": "3796",
  "Craftsman's Forcium Headgear (FB)": "3797",
  "Craftsman's Forcium Headgear (FA)": "3798",
  "Craftsman's Forcium Headpiece (BL)": "3799",
  "Lapis Crystal": "38",
  "Amulet Of Guard +3": "380",
  "Craftsman's Forcium Headpiece (WI)": "3800",
  "Diamond Cube": "3801",
  "Minesta's Diamond Charm": "3802",
  "Minesta's Sapphire Charm": "3803",
  "Minesta's Sapphire Charm +1": "3804",
  "Minesta's Sapphire Charm +2": "3805",
  "Minesta's Sapphire Charm +3": "3806",
  "Minesta's Sapphire Charm +4": "3807",
  "Minesta's Sapphire Charm +5": "3808",
  "Minesta's Sapphire Charm +6": "3809",
  "Amulet Of Guard +4": "381",
  "Minesta's Sapphire Charm +7": "3810",
  "Minesta's Ruby Charm": "3811",
  "Minesta's Ruby Charm +1": "3812",
  "Minesta's Ruby Charm +2": "3813",
  "Minesta's Ruby Charm +3": "3814",
  "Minesta's Ruby Charm +4": "3815",
  "Minesta's Ruby Charm +5": "3816",
  "Minesta's Ruby Charm +6": "3817",
  "Minesta's Ruby Charm +7": "3818",
  "Minesta's Emerald Charm": "3819",
  "Amulet Of Guard +5": "382",
  "Minesta's Emerald Charm +1": "3820",
  "Minesta's Emerald Charm +2": "3821",
  "Minesta's Emerald Charm +3": "3822",
  "Minesta's Emerald Charm +4": "3823",
  "Minesta's Emerald Charm +5": "3824",
  "Minesta's Emerald Charm +6": "3825",
  "Minesta's Emerald Charm +7": "3826",
  "Minesta's Amber Charm": "3827",
  "Minesta's Amber Charm +1": "3828",
  "Minesta's Amber Charm +2": "3829",
  "Amulet Of Guard +6": "383",
  "Minesta's Amber Charm +3": "3830",
  "Minesta's Amber Charm +4": "3831",
  "Minesta's Amber Charm +5": "3832",
  "Minesta's Amber Charm +6": "3833",
  "Minesta's Amber Charm +7": "3834",
  "Craftsman's Token": "3835",
  "Chloe's Token": "3836",
  "Potion of Craftsman": "3837",
  "Potion of Amity": "3838",
  "Remains of the DeadN": "3839",
  "Amulet Of Guard +7": "384",
  "Bike Epic Converter (Lv. 2) - HP": "3840",
  "Bike Epic Converter (Lv. 2) - All Skill Amp. UP": "3841",
  "Bike Epic Converter (Lv. 2) - Critical Rate": "3842",
  "Bike Epic Converter (Lv. 2) - Critical DMG": "3843",
  "Bike Epic Converter (Lv. 2) - All Attack": "3844",
  "Bike Epic Converter (Lv. 2) - Resist Critical Rate": "3845",
  "Bike Epic Converter (Lv. 2) - Critical DMG Resistance": "3846",
  "Bike Epic Converter (Lv. 2) - Skill Amp. Resistance": "3847",
  "Bike Epic Converter (Lv. 3) - HP": "3848",
  "To be applied": "3849",
  "Amulet Of Guard +8": "385",
  "Bike Epic Converter (Lv. 3) - All Skill Amp. UP": "3850",
  "Bike Epic Converter (Lv. 3) - Critical Rate": "3851",
  "Bike Epic Converter (Lv. 3) - Critical DMG": "3852",
  "Bike Epic Converter (Lv. 3) - All Attack": "3853",
  "Bike Epic Converter (Lv. 3) - Resist Critical Rate": "3854",
  "Bike Epic Converter (Lv. 3) - Critical DMG Resistance": "3855",
  "Bike Epic Converter (Lv. 3) - Skill Amp. Resistance": "3856",
  "Stain Clone Set": "3857",
  "Flame Dice (Lv. 2)": "3858",
  "Flame Dice (Lv. 3)": "3859",
  "Amulet Of Guard +9": "386",
  "Flame Dice (Lv. 4)": "3860",
  "Dark Dice (Lv. 2)": "3861",
  "Dark Dice (Lv. 3)": "3862",
  "Dark Dice (Lv. 4)": "3863",
  "Holy Dice (Lv. 2)": "3864",
  "Holy Dice (Lv. 3)": "3865",
  "Holy Dice (Lv. 4)": "3866",
  "Air Dice (Lv. 2)": "3867",
  "Air Dice (Lv. 3)": "3868",
  "Air Dice (Lv. 4)": "3869",
  "Amulet Of Guard +10": "387",
  "Thunder Dice (Lv. 2)": "3870",
  "Thunder Dice (Lv. 3)": "3871",
  "Thunder Dice (Lv. 4)": "3872",
  "Bloody Dice(Lv. 2)": "3873",
  "Bloody Dice(Lv. 3)": "3874",
  "Bloody Dice(Lv. 4)": "3875",
  "Earth Dice (Lv. 2)": "3876",
  "Earth Dice (Lv. 3)": "3877",
  "Earth Dice (Lv. 4)": "3878",
  "Aqua Dice (Lv. 2)": "3879",
  "Force Regeneration Amulet +3": "388",
  "Aqua Dice (Lv. 3)": "3880",
  "Aqua Dice (Lv. 4)": "3881",
  "Soul Dice (Lv. 2)": "3882",
  "Soul Dice (Lv. 3)": "3883",
  "Soul Dice (Lv. 4)": "3884",
  "Circuit Jewel (Lv. 7) Sample": "3885",
  "Cartridge Roulette (Lv. 2)": "3886",
  "Cartridge Roulette (Lv. 3)": "3887",
  "Cartridge Roulette (Lv. 4)": "3888",
  "Field Lotto CoinN": "3889",
  "Force Regeneration Amulet +4": "389",
  "Coin Lotto (Forgotten Ruin)": "3890",
  "Coin Lotto (Lakeside)": "3891",
  "Coin Lotto (Mutant Forest)": "3892",
  "Coin Lotto (Pontus Ferrum)": "3893",
  "Coin Lotto (Porta Inferno)": "3894",
  "Coin Lotto (Arcane Trace)": "3895",
  "Reflection StoneS": "3896",
  "Astral Core (Bluestin)": "3897",
  "Astral Core (Titanium)": "3898",
  "Astral Core (Shadow Titanium)": "3899",
  "Topaz Crystal": "39",
  "Force Regeneration Amulet +5": "390",
  "Astral Core (Osmium)": "3900",
  "Astral Core (Red Osmium)": "3901",
  "Astral Core (SIGMetal)": "3902",
  "Master's Bluestin Plate (WA)": "3903",
  "Master's Bluestin Plate (FS)": "3904",
  "Master's Bluestin Coat (FB)": "3905",
  "Master's Bluestin Coat (FA)": "3906",
  "Master's Bluestin Suit (BL)": "3907",
  "Master's Bluestin Suit (WI)": "3908",
  "Master's Bluestin Gauntlets (WA)": "3909",
  "Force Regeneration Amulet +6": "391",
  "Master's Bluestin Gauntlets (FS)": "3910",
  "Master's Bluestin Gloves (FB)": "3911",
  "Master's Bluestin Gloves (FA)": "3912",
  "Master's Bluestin Hand (BL)": "3913",
  "Master's Bluestin Hand (WI)": "3914",
  "Master's Bluestin Greave (WA)": "3915",
  "Master's Bluestin Greave (FS)": "3916",
  "Master's Bluestin Boots (FB)": "3917",
  "Master's Bluestin Boots (FA)": "3918",
  "Master's Bluestin Shoes (BL)": "3919",
  "Force Regeneration Amulet +7": "392",
  "Master's Bluestin Shoes (WI)": "3920",
  "Master's Bluestin Visor (WA)": "3921",
  "Master's Bluestin Visor (FS)": "3922",
  "Master's Bluestin Headgear (FB)": "3923",
  "Master's Bluestin Headgear (FA)": "3924",
  "Master's Bluestin Headpiece (BL)": "3925",
  "Master's Bluestin Headpiece (WI)": "3926",
  "Master's Titanium Plate (WA)": "3927",
  "Master's Titanium Plate (FS)": "3928",
  "Master's Titanium Coat (FB)": "3929",
  "Amulet Of Seven Coins": "393",
  "Master's Titanium Coat (FA)": "3930",
  "Master's Titanium Suit (BL)": "3931",
  "Master's Titanium Suit (WI)": "3932",
  "Master's Titanium Gauntlets (WA)": "3933",
  "Master's Titanium Gauntlets (FS)": "3934",
  "Master's Titanium Gloves (FB)": "3935",
  "Master's Titanium Gloves (FA)": "3936",
  "Master's Titanium Hand (BL)": "3937",
  "Master's Titanium Hand (WI)": "3938",
  "Master's Titanium Greave (WA)": "3939",
  "Amulet Of Seven Coins +1": "394",
  "Master's Titanium Greave (FS)": "3940",
  "Master's Titanium Boots (FB)": "3941",
  "Master's Titanium Boots (FA)": "3942",
  "Master's Titanium Shoes (BL)": "3943",
  "Master's Titanium Shoes (WI)": "3944",
  "Master's Titanium Visor (WA)": "3945",
  "Master's Titanium Visor (FS)": "3946",
  "Master's Titanium Headgear (FB)": "3947",
  "Master's Titanium Headgear (FA)": "3948",
  "Master's Titanium Headpiece (BL)": "3949",
  "Amulet Of Seven Coins +2": "395",
  "Master's Titanium Headpiece (WI)": "3950",
  "Master's Shadow Titanium Plate (WA)": "3951",
  "Master's Shadow Titanium Plate (FS)": "3952",
  "Master's Shadow Titanium Coat (FB)": "3953",
  "Master's Shadow Titanium Coat (FA)": "3954",
  "Master's Shadow Titanium Suit (BL)": "3955",
  "Master's Shadow Titanium Suit (WI)": "3956",
  "Master's Shadow Titanium Gauntlets (WA)": "3957",
  "Master's Shadow Titanium Gauntlets (FS)": "3958",
  "Master's Shadow Titanium Gloves (FB)": "3959",
  "Amulet Of Seven Coins +3": "396",
  "Master's Shadow Titanium Gloves (FA)": "3960",
  "Master's Shadow Titanium Hand (BL)": "3961",
  "Master's Shadow Titanium Hand (WI)": "3962",
  "Master's Shadow Titanium Greave (WA)": "3963",
  "Master's Shadow Titanium Greave (FS)": "3964",
  "Master's Shadow Titanium Boots (FB)": "3965",
  "Master's Shadow Titanium Boots (FA)": "3966",
  "Master's Shadow Titanium Shoes (BL)": "3967",
  "Master's Shadow Titanium Shoes (WI)": "3968",
  "Master's Shadow Titanium Visor (WA)": "3969",
  "Amulet Of Seven Coins +4": "397",
  "Master's Shadow Titanium Visor (FS)": "3970",
  "Master's Shadow Titanium Headgear (FB)": "3971",
  "Master's Shadow Titanium Headgear (FA)": "3972",
  "Master's Shadow Titanium Headpiece (BL)": "3973",
  "Master's Shadow Titanium Headpiece (WI)": "3974",
  "Master's Osmium Plate (WA)": "3975",
  "Master's Osmium Plate (FS)": "3976",
  "Master's Osmium Coat (FB)": "3977",
  "Master's Osmium Coat (FA)": "3978",
  "Master's Osmium Suit (BL)": "3979",
  "Amulet Of Seven Coins +5": "398",
  "Master's Osmium Suit (WI)": "3980",
  "Master's Osmium Gauntlets (WA)": "3981",
  "Master's Osmium Gauntlets (FS)": "3982",
  "Master's Osmium Gloves (FB)": "3983",
  "Master's Osmium Gloves (FA)": "3984",
  "Master's Osmium Hand (BL)": "3985",
  "Master's Osmium Hand (WI)": "3986",
  "Master's Osmium Greave (WA)": "3987",
  "Master's Osmium Greave (FS)": "3988",
  "Master's Osmium Boots (FB)": "3989",
  "Amulet Of Seven Coins +6": "399",
  "Master's Osmium Boots (FA)": "3990",
  "Master's Osmium Shoes (BL)": "3991",
  "Master's Osmium Shoes (WI)": "3992",
  "Master's Osmium Visor (WA)": "3993",
  "Master's Osmium Visor (FS)": "3994",
  "Master's Osmium Headgear (FB)": "3995",
  "Master's Osmium Headgear (FA)": "3996",
  "Master's Osmium Headpiece (BL)": "3997",
  "Master's Osmium Headpiece (WI)": "3998",
  "Master's Shineguard Plate (WA)": "3999",
  "HP Potion (Lv. 2)": "4",
  "Forcium Crystal": "40",
  "Amulet Of Seven Coins +7": "400",
  "Master's Shineguard Plate (FS)": "4000",
  "Master's Teragrace Coat (FB)": "4001",
  "Master's Teragrace Coat (FA)": "4002",
  "Master's Mystic Suit (BL)": "4003",
  "Master's Mystic Suit (WI)": "4004",
  "Master's Shineguard Gauntlets (WA)": "4005",
  "Master's Shineguard Gauntlets (FS)": "4006",
  "Master's Teragrace Gloves (FB)": "4007",
  "Master's Teragrace Gloves (FA)": "4008",
  "Master's Mystic Hand (BL)": "4009",
  "Amulet Of Pain": "401",
  "Master's Mystic Hand (WI)": "4010",
  "Master's Shineguard Greave (WA)": "4011",
  "Master's Shineguard Greave (FS)": "4012",
  "Master's Teragrace Boots (FB)": "4013",
  "Master's Teragrace Boots (FA)": "4014",
  "Master's Mystic Shoes (BL)": "4015",
  "Master's Mystic Shoes (WI)": "4016",
  "Master's Shineguard Visor (WA)": "4017",
  "Master's Shineguard Visor (FS)": "4018",
  "Master's Teragrace Headgear (FB)": "4019",
  "Amulet Of Pain +1": "402",
  "Master's Teragrace Headgear (FA)": "4020",
  "Master's Mystic Headpiece (BL)": "4021",
  "Master's Mystic Headpiece (WI)": "4022",
  "Master's SIGMetal Plate (WA)": "4023",
  "Master's SIGMetal Plate (FS)": "4024",
  "Master's SIGMetal Coat (FB)": "4025",
  "Master's SIGMetal Coat (FA)": "4026",
  "Master's SIGMetal Suit (BL)": "4027",
  "Master's SIGMetal Suit (WI)": "4028",
  "Master's SIGMetal Gauntlets (WA)": "4029",
  "Amulet Of Pain +2": "403",
  "Master's SIGMetal Gauntlets (FS)": "4030",
  "Master's SIGMetal Gloves (FB)": "4031",
  "Master's SIGMetal Gloves (FA)": "4032",
  "Master's SIGMetal Hand (BL)": "4033",
  "Master's SIGMetal Hand (WI)": "4034",
  "Master's SIGMetal Greave (WA)": "4035",
  "Master's SIGMetal Greave (FS)": "4036",
  "Master's SIGMetal Boots (FB)": "4037",
  "Master's SIGMetal Boots (FA)": "4038",
  "Master's SIGMetal Shoes (BL)": "4039",
  "Amulet Of Pain +3": "404",
  "Master's SIGMetal Shoes (WI)": "4040",
  "Master's SIGMetal Visor (WA)": "4041",
  "Master's SIGMetal Visor (FS)": "4042",
  "Master's SIGMetal Headgear (FB)": "4043",
  "Master's SIGMetal Headgear (FA)": "4044",
  "Master's SIGMetal Headpiece (BL)": "4045",
  "Master's SIGMetal Headpiece (WI)": "4046",
  "Master's Forcium Plate (WA)": "4047",
  "Master's Forcium Plate (FS)": "4048",
  "Master's Forcium Coat (FB)": "4049",
  "Amulet Of Pain +4": "405",
  "Master's Forcium Coat (FA)": "4050",
  "Master's Forcium Suit (BL)": "4051",
  "Master's Forcium Suit (WI)": "4052",
  "Master's Forcium Gauntlets (WA)": "4053",
  "Master's Forcium Gauntlets (FS)": "4054",
  "Master's Forcium Gloves (FB)": "4055",
  "Master's Forcium Gloves (FA)": "4056",
  "Master's Forcium Hand (BL)": "4057",
  "Master's Forcium Hand (WI)": "4058",
  "Master's Forcium Greave (WA)": "4059",
  "Amulet Of Pain +5": "406",
  "Master's Forcium Greave (FS)": "4060",
  "Master's Forcium Boots (FB)": "4061",
  "Master's Forcium Boots (FA)": "4062",
  "Master's Forcium Shoes (BL)": "4063",
  "Master's Forcium Shoes (WI)": "4064",
  "Master's Forcium Visor (WA)": "4065",
  "Master's Forcium Visor (FS)": "4066",
  "Master's Forcium Headgear (FB)": "4067",
  "Master's Forcium Headgear (FA)": "4068",
  "Master's Forcium Headpiece (BL)": "4069",
  "Amulet Of Pain +6": "407",
  "Master's Forcium Headpiece (WI)": "4070",
  "Circuit Jewel (Lv. 7)": "4071",
  "Astral Jewel (Lv. 1)": "4072",
  "Astral Jewel (Lv. 2)": "4073",
  "Astral Jewel (Lv. 3)": "4074",
  "Astral Jewel (Lv. 4)": "4075",
  "Astral Jewel (Lv. 5)": "4076",
  "Astral Jewel (Lv. 6)": "4077",
  "Astral Jewel (Lv. 7)": "4078",
  "Master's Token": "4079",
  "Amulet Of Pain +7": "408",
  "Remote Request Card": "4080",
  "Pet - May": "4081",
  "Pet - Claris": "4082",
  "Chaos Box - Upgrade II": "4083",
  "Astral Bike Card - PW5": "4084",
  "Compass of Guidance": "4085",
  "Test Item": "4088",
  "Skill Book(Magic Control)": "4089",
  "Epaulet of Guardian": "410",
  "Skill Book(Spirit Shield)": "4090",
  "Skill Book(Quick Move)": "4091",
  "Skill Book(Reflex Shield)": "4092",
  "Skill Book(Art of Curse)": "4093",
  "Master's Bluestin Crystal": "41",
  "Upgraded Epaulet of Guardian": "411",
  "Shadow Aramid Epaulet of Guardian": "412",
  "Bluestin Epaulet of Guardian": "413",
  "Titanium Epaulet of Guardian": "414",
  "Osmium Epaulet of Guardian": "415",
  "Rare Epaulet of Guardian": "416",
  "Life Regeneration Epaulet": "417",
  "Life Regeneration Epaulet +1": "418",
  "Life Regeneration Epaulet +2": "419",
  "Master's Pherystin Crystal": "42",
  "Mana Regeneration Epaulet": "420",
  "Mana Regeneration Epaulet +1": "421",
  "Mana Regeneration Epaulet +2": "422",
  "Fortune Epaulet": "423",
  "Fortune Epaulet +1": "424",
  "Hiding Eapulet": "425",
  "Rare Epaulet of Fighter": "426",
  "Rare Epaulet of Sage": "427",
  "Titanium Epaulet of Fighter": "428",
  "Osmium Epaulet of Fighter": "429",
  "Master's Aqua Crystal": "43",
  "Titanium Epaulet of Sage": "430",
  "Osmium Epaulet of Sage": "431",
  "NightWalkers (Armorset)": "432",
  "NightWalkers (Battleset)": "433",
  "NightWalkers (Martialset)": "434",
  "Epaulets": "435",
  "Broken CoreS": "436",
  "Astral Bikecard - Type: Blue": "437",
  "LatterN": "438",
  "Astral Bikecard - Type: RW3": "439",
  "Master's Lapis Crystal": "44",
  "ScrollN": "440",
  "ParchmentN": "441",
  "LeatherS": "442",
  "Sword PieceS": "443",
  "Sword PieceN": "444",
  "HornS": "445",
  "KeyN": "446",
  "NecklaceS": "447",
  "BoxS": "448",
  "ScaleS": "449",
  "Training Katana": "45",
  "BottleN": "450",
  "EggS": "451",
  "ShoeS": "452",
  "LeafS": "453",
  "Present BoxN": "454",
  "MineralS": "455",
  "FruitS": "456",
  "ThornS": "457",
  "AxS": "458",
  "ShellS": "459",
  "Katana": "46",
  "Red QuartzS": "460",
  "Crude QuartzS": "461",
  "Aquq QuartzS": "462",
  "Broken QuartzS": "463",
  "Astral Board Card - X Steelblue": "464",
  "BookN": "465",
  "Quest Armor GloveS": "466",
  "Quest Battle GloveS": "467",
  "Quest Martial GloveS": "468",
  "Quest Upgrade CoreS": "469",
  "Iron Katana": "47",
  "Quest Force CoreS": "470",
  "EmblemS": "471",
  "CobwebS": "472",
  "Warp CardN": "473",
  "Quest Armor GloveN": "474",
  "Quest Battle GloveN": "475",
  "Quest Martial GloveN": "476",
  "Astral Board Card - X Yellow": "477",
  "BottleS": "478",
  "ScrollS": "479",
  "Damascus Katana": "48",
  "Astral Board Card - X3 White": "480",
  "LeatherN": "481",
  "Astral Board Card - X2 Blue": "482",
  "Astral Board Card - X Green": "483",
  "Aquq QuartzN": "484",
  "B.I. CertificateN": "485",
  "D.S. CertificateN": "486",
  "G.D. CertificateN": "487",
  "MineralN": "488",
  "NecklaceN": "489",
  "Shadowsteel Katana": "49",
  "ShoeN": "490",
  "Blue CoreN": "491",
  "Red CoreN": "492",
  "Green CoreN": "493",
  "Yellow CoreN": "494",
  "Precious CardN": "495",
  "CircuitN": "496",
  "Card PieceN": "497",
  "Magic RelicN": "498",
  "CaneN": "499",
  "HP Potion (Lv. 3)": "5",
  "Bluestin Katana": "50",
  "ShackleN": "500",
  "FruitN": "501",
  "BoneN": "502",
  "EyeballN": "503",
  "EggN": "504",
  "BoxN": "505",
  "FeatherS": "506",
  "Astral Board Card - X2 Red": "507",
  "CardS": "508",
  "SoilS": "509",
  "Titanium Katana": "51",
  "LeafN": "510",
  "MeatS": "511",
  "ForcepsS": "512",
  "Feather (B)S": "513",
  "ShellfishS": "514",
  "Metallic PieceS": "515",
  "NucleusS": "516",
  "Mech-LegS": "517",
  "Mech-HeadS": "518",
  "Mech-Big HandS": "519",
  "Shadow Titanium Katana": "52",
  "Mech-HandS": "520",
  "Mech-HumpS": "521",
  "Mach-BackS": "522",
  "Mech-MouthS": "523",
  "Mech-WingS": "524",
  "Golem CookieN": "525",
  "Ugly Troll": "526",
  "Pretty House": "527",
  "Mecha-BowS": "528",
  "Mechzard-HandS": "529",
  "Osmium Katana": "53",
  "Mech-TailS": "530",
  "Mechhealer-HeadS": "531",
  "OPDollS": "532",
  "EXDollS": "533",
  "Mander DollS": "534",
  "Zard DollS": "535",
  "Ape DollS": "536",
  "Ape Archer DollS": "537",
  "Buffalo DollS": "538",
  "Bird DollS": "539",
  "Red Osmium Katana": "54",
  "Dog DollS": "540",
  "Scale (B)S": "541",
  "ToothS": "542",
  "D.T. (D Grade/Single)": "543",
  "D.T. (C Grade/Single)": "544",
  "D.T. (B Grade/Single)": "545",
  "D.T. (A Grade/Single)": "546",
  "D.T. (AA Grade/Single)": "547",
  "D.T. (AAA Grade/Single)": "548",
  "D.T. (X Grade/Single)": "549",
  "Forcium Katana": "55",
  "D.T. (D Grade/Party)": "550",
  "D.T. (C Grade/Party)": "551",
  "D.T. (B Grade/Party)": "552",
  "D.T. (A Grade/Party)": "553",
  "D.T. (AA Grade/Party)": "554",
  "D.T. (AAA Grade/Party)": "555",
  "D.T. (X Grade/Party)": "556",
  "Dungeon Doll": "557",
  "Gate Wqy": "558",
  "Auto Trap": "559",
  "Master's Bluestin Katana": "56",
  "Formula Card (Event)": "560",
  "I am GM": "561",
  "TailS": "562",
  "LegS": "563",
  "BoneS": "564",
  "Mana ReadS": "565",
  "BookS": "566",
  "Black RingN": "567",
  "Quest ItemN": "568",
  "ParchmentS": "569",
  "Master's Titanium Katana": "57",
  "Red QuartzN": "570",
  "Crude QuartzN": "571",
  "FeatherN": "572",
  "Necklace 2": "573",
  "Duplicate Ring": "574",
  "Bike CardS": "576",
  "StoneS": "577",
  "Map PartN": "578",
  "Extract Potion (STR)": "579",
  "Master's Shadow Titanium Katana": "58",
  "Extract Potion (DEX)": "580",
  "Extract Potion (INT)": "581",
  "Force Core (Low)": "582",
  "Force Core (Medium)": "583",
  "Bike Coating Kit(Basic)": "584",
  "SoilN": "585",
  "ShellN": "586",
  "ArrowS": "587",
  "ArrowN": "588",
  "Broken QuartzN": "589",
  "Master's Osmium Katana": "59",
  "Machine PartS": "590",
  "Machine PartN": "591",
  "StoneN": "592",
  "Metallic PieceN": "593",
  "CobwebN": "594",
  "ScaleN": "595",
  "NucleusN": "596",
  "Scale (B)N": "597",
  "ToothN": "598",
  "GaugeS": "599",
  "MP Potion (Lv. 1)": "6",
  "Training Blade": "60",
  "GaugeN": "600",
  "Epaulet of the DeadN": "601",
  "Buffalo Doll N": "602",
  "Feather (B)N": "603",
  "Sweet Candy": "604",
  "Quartz Core (Citrine)": "605",
  "Quartz Core (Bluestin)": "606",
  "Quartz Core (Pherystin)": "607",
  "Quartz Core (Aqua)": "608",
  "Quartz Core (Lapis)": "609",
  "Blade": "61",
  "Quartz Core (Topaz)": "610",
  "Quartz Core (Forcium)": "611",
  "Quartz Core (Blue Forcium)": "612",
  "Quartz Core(Archridium)": "613",
  "Quartz Core (True Orihalcon)": "614",
  "Material Core (Aramid)": "615",
  "Material Core (Shadowsteel)": "616",
  "Material Core (Bluestin)": "617",
  "Material Core (Titanium)": "618",
  "Material Core(Shadow Titanium)": "619",
  "Iron Blade": "62",
  "Material Core (Osmium)": "620",
  "Material Core (Red Osmium)": "621",
  "Material Core (Forcium)": "622",
  "Material Core (Blue Forcium)": "623",
  "Material Core(Archridium)": "624",
  "Material Core (True Orihalcon)": "625",
  "Formula Cards": "626",
  "Formula Card (Instant)": "627",
  "Upgrade Core (Piece)": "628",
  "Force Core (Piece)": "629",
  "Damascus Blade": "63",
  "Upgrade Core (Crystal)": "630",
  "Force Core (Crystal)": "631",
  "Perfect Core (Highest)": "632",
  "Plasma Circuit": "633",
  "Plasma Plug": "634",
  "Extender Circuit": "635",
  "Perfect Coating Kit": "636",
  "Slot Extender (Low)": "637",
  "Slot Extender (Medium)": "638",
  "Slot Extender (High)": "639",
  "Shadowsteel Blade": "64",
  "Core Alchemic Transmuter (Sword)": "640",
  "Core Alchemic Transmuter (Armorset)": "641",
  "Core Alchemic Transmuter (Battleset)": "642",
  "Core Alchemic Transmuter (Martialset)": "643",
  "Core Alchemic Transmuter (Artifact)": "644",
  "Red BottleN": "645",
  "Goodluck Potion[ALZ] (Lv. 3)": "646",
  "Goodluck Potion[ALZ] (Lv. 1)": "647",
  "Fury Potion (Lv. 1)": "648",
  "Fury Potion (Lv. 2)": "649",
  "Bluestin Blade": "65",
  "Fury Potion (Lv. 3)": "650",
  "Party Epaulet": "651",
  "Greatsword1N": "652",
  "Halberd1N": "653",
  "Glory Potion (Lv. 1)": "654",
  "Glory Potion (Lv. 2)": "655",
  "Glory Potion (Lv. 3)": "656",
  "Concentraion Potion (Lv. 1)": "657",
  "Titanium Blade": "66",
  "Vital Regen. Potion (Lv. 1)": "662",
  "Force Regen. Potion (Lv. 1)": "663",
  "Critical Potion (Lv. 1)": "666",
  "Crushing Potion (Lv. 1)": "667",
  "Skill Potion (Lv. 1)": "668",
  "Goodluck Potion[ALZ] (Lv. 2)": "669",
  "Shadow Titanium Blade": "67",
  "Party Epaulet +1": "670",
  "Party Epaulet +2": "671",
  "Cabal NFT Coupon": "67108865",
  "Evasion Potion": "674",
  "C.A. License": "675",
  "Circuit Jewel (Lv. 1)": "676",
  "Circuit Jewel (Lv. 2)": "677",
  "Circuit Jewel (Lv. 3)": "678",
  "Circuit Jewel (Lv. 4)": "679",
  "Osmium Blade": "68",
  "Circuit Jewel (Lv. 5)": "680",
  "Circuit Jewel (Lv. 6)": "681",
  "Plate of Honor": "682",
  "Flame Disc": "683",
  "Dark Disc": "684",
  "Holy Disc": "685",
  "Air Disc": "686",
  "Thunder Disc": "687",
  "Bloody Disc": "688",
  "Earth Disc": "689",
  "Red Osmium Blade": "69",
  "Aqua Disc": "690",
  "Soul Disc": "691",
  "Shape Cartridges": "692",
  "Ring Of Brute": "693",
  "Ring Of Brute +1": "694",
  "Ring Of Brute +2": "695",
  "Ring Of Brute +3": "696",
  "Ring Of Serenity": "697",
  "Ring Of Serenity +1": "698",
  "Ring Of Serenity +2": "699",
  "MP Potion (Lv. 2)": "7",
  "Forcium Blade": "70",
  "Ring Of Serenity +3": "700",
  "Extortion Ring": "701",
  "Extortion Ring +1": "702",
  "Extortion Ring +2": "703",
  "Extortion Ring +3": "704",
  "Extortion Ring +4": "705",
  "Extortion Ring +5": "706",
  "Adept Amulet": "707",
  "Adept Amulet +1": "708",
  "Adept Amulet +2": "709",
  "Master's Bluestin Blade": "71",
  "Adept Amulet +3": "710",
  "Vampiric Amulet": "711",
  "Vampiric Amulet +1": "712",
  "Vampiric Amulet +2": "713",
  "Vampiric Amulet +3": "714",
  "Vampiric Amulet +4": "715",
  "Vampiric Amulet +5": "716",
  "Vampiric Amulet +6": "717",
  "Amulet Of Battler": "718",
  "Amulet Of Battler +1": "719",
  "Master's Titanium Blade": "72",
  "Amulet Of Battler +2": "720",
  "Amulet Of Battler +3": "721",
  "Amulet Of Battler +4": "722",
  "Amulet Of Battler +5": "723",
  "Amulet Of Battler +6": "724",
  "Amulet Of Battler +7": "725",
  "Amulet Of Battler +8": "726",
  "Amulet Of Battler +9": "727",
  "Ring of Proof": "728",
  "Ring of Proof +1": "729",
  "Master's Shadow Titanium Blade": "73",
  "Ring of Proof +2": "730",
  "Ring of Proof +3": "731",
  "Ring of Proof +4": "732",
  "Ring of Proof +5": "733",
  "Ring of Proof +6": "734",
  "Ring of Proof +7": "735",
  "Amulet of Proof": "736",
  "Amulet of Proof +1": "737",
  "Amulet of Proof +2": "738",
  "Amulet of Proof +3": "739",
  "Master's Osmium Blade": "74",
  "Amulet of Proof +4": "740",
  "Amulet of Proof +5": "741",
  "Amulet of Proof +6": "742",
  "Amulet of Proof +7": "743",
  "Epaulet of Proof": "744",
  "Epaulet of Proof +1": "745",
  "Epaulet of Proof +2": "746",
  "Epaulet of Proof +3": "747",
  "Epaulet of Proof +4": "748",
  "Epaulet of Proof +5": "749",
  "Training Daikatana": "75",
  "Epaulet of Proof +6": "750",
  "Epaulet of Proof +7": "751",
  "Glowing JewelS": "752",
  "Yellow PowderS": "753",
  "Red PowderS": "754",
  "Stain Clone": "755",
  "Yellow JewelS": "756",
  "Silver JewelS": "757",
  "CircuitS": "758",
  "Red LiquidS": "759",
  "Daikatana": "76",
  "Blue LiquidS": "760",
  "Shadow Aramid Epaulet of Fighter": "761",
  "Shadow Aramid Epaulet of Sage": "762",
  "Bluestin Epaulet of Fighter": "763",
  "Bluestin Epaulet of Sage": "764",
  "Sirius' Unbinding Stone (High) - Magic Weapon": "765",
  "Sirius' Unbinding Stone (High) - 1H Weapon": "766",
  "Sirius' Unbinding Stone (High) - 2H Weapon": "767",
  "Sirius' Unbinding Stone (High) - Suit": "768",
  "Sirius' Unbinding Stone (High) - Glove": "769",
  "Iron Daikatana": "77",
  "Sirius' Unbinding Stone (High) - Boots": "770",
  "Sirius' Unbinding Stone (High) - Helmet": "771",
  "Sirius' Unbinding Stone (Highest) - Magic Weapon": "772",
  "Sirius' Unbinding Stone (Highest) - 1H Weapon": "773",
  "Sirius' Unbinding Stone (Highest) - 2H Weapon": "774",
  "Sirius' Unbinding Stone (Highest) - Suit": "775",
  "Sirius' Unbinding Stone (Highest) - Glove": "776",
  "Sirius' Unbinding Stone (Highest) - Boots": "777",
  "First Core": "778",
  "Second Core": "779",
  "Damascus Daikatana": "78",
  "VGA Card": "780",
  "Main Board": "781",
  "Intel Dual Core Machine": "782",
  "Chaos Lamp": "783",
  "Bracelet Of Fighter": "785",
  "Bracelet Of Fighter +1": "786",
  "Bracelet Of Fighter +2": "787",
  "Bracelet Of Fighter +3": "788",
  "Bracelet Of Fighter +4": "789",
  "Shadowsteel Daikatana": "79",
  "Bracelet Of Sage": "790",
  "Bracelet Of Sage +1": "791",
  "Bracelet Of Sage +2": "792",
  "Bracelet Of Sage +3": "793",
  "Bracelet Of Sage +4": "794",
  "Extortion Bracelet": "795",
  "Extortion Bracelet +1": "796",
  "Extortion Bracelet +2": "797",
  "Extortion Bracelet +3": "798",
  "Extortion Bracelet +4": "799",
  "MP Potion (Lv. 3)": "8",
  "Bluestin Daikatana": "80",
  "Bracelet Of Seven Coins": "800",
  "Bracelet Of Seven Coins +1": "801",
  "Bracelet Of Seven Coins +2": "802",
  "Bracelet Of Seven Coins +3": "803",
  "Bracelet Of Seven Coins +4": "804",
  "Earring of Guard": "805",
  "Earring of Guard +1": "806",
  "Earring of Guard +2": "807",
  "Earring of Guard +3": "808",
  "Earring of Guard +4": "809",
  "Titanium Daikatana": "81",
  "Force Regeneration Earring": "810",
  "Force Regeneration Earring +1": "811",
  "Force Regeneration Earring +2": "812",
  "Force Regeneration Earring +3": "813",
  "Force Regeneration Earring +4": "814",
  "Vampiric Earring": "815",
  "Vampiric Earring +1": "816",
  "Vampiric Earring +2": "817",
  "Vampiric Earring +3": "818",
  "Vampiric Earring +4": "819",
  "Shadow Titanium Daikatana": "82",
  "Earring of Seven Coins": "820",
  "Earring of Seven Coins +1": "821",
  "Earring of Seven Coins +2": "822",
  "Earring of Seven Coins +3": "823",
  "Earring of Seven Coins +4": "824",
  "Bracelet Of Brute": "825",
  "Bracelet Of Brute +1": "826",
  "Bracelet Of Brute +2": "827",
  "Bracelet Of Serenity": "828",
  "Bracelet Of Serenity +1": "829",
  "Osmium Daikatana": "83",
  "Bracelet Of Serenity +2": "830",
  "Life Absorb Bracelet": "831",
  "Life Absorb Bracelet +1": "832",
  "Life Absorb Bracelet +2": "833",
  "Mana Absorb Bracelet": "834",
  "Mana Absorb Bracelet +1": "835",
  "Mana Absorb Bracelet +2": "836",
  "Protection Earring": "837",
  "Protection Earring +1": "838",
  "Protection Earring +2": "839",
  "Red Osmium Daikatana": "84",
  "Vital Earring": "840",
  "Vital Earring +1": "841",
  "Vital Earring +2": "842",
  "Evasion Earring": "843",
  "Evasion Earring +1": "844",
  "Evasion Earring +2": "845",
  "I am a reporter": "846",
  "Broken Sword (B)N": "847",
  "Concentraion Potion (Lv. 2)": "848",
  "Forcium Daikatana": "85",
  "Vital Regen. Potion (Lv. 2)": "853",
  "Honorable Earring of Guard": "854",
  "Honorable Extortion Bracelet": "857",
  "Crushing Potion (Lv. 2)": "858",
  "Skill Potion (Lv. 2)": "859",
  "Master's Bluestin Daikatana": "86",
  "Concentraion Potion (Lv. 3)": "860",
  "Vital Regen. Potion (Lv. 3)": "865",
  "Force Regen. Potion (Lv. 3)": "866",
  "Critical Potion (Lv. 3)": "869",
  "Master's Titanium Daikatana": "87",
  "Crushing Potion (Lv. 3)": "870",
  "Skill Potion (Lv. 3)": "871",
  "Minesta's Unbinding Stone (Medium) - Magic Weapon": "872",
  "Minesta's Unbinding Stone (Medium) - One Hand Weapon": "873",
  "Minesta's Unbinding Stone (Medium) - Two Hand Weapon": "874",
  "Minesta's Unbinding Stone (Medium) - Suit": "875",
  "Minesta's Unbinding Stone (Medium) - Glove": "876",
  "Minesta's Unbinding Stone (Medium) - Boots": "877",
  "Minesta's Unbinding Stone (Medium) - Helmet": "878",
  "Minesta's Unbinding Stone (High) - Magic Weapon": "879",
  "Master's Shadow Titanium Daikatana": "88",
  "Minesta's Unbinding Stone (High) - One Hand Weapon": "880",
  "Minesta's Unbinding Stone (High) - Two Hand Weapon": "881",
  "Minesta's Unbinding Stone (High) - Suit": "882",
  "Minesta's Unbinding Stone (High) - Glove": "883",
  "Minesta's Unbinding Stone (High) - Boots": "884",
  "Minesta's Unbinding Stone (High) - Helmet": "885",
  "Minesta's Unbinding Stone (Highest) - Magic Weapon": "886",
  "Minesta's Unbinding Stone (Highest) - One Hand Weapon": "887",
  "Minesta's Unbinding Stone (Highest) - Two Hand Weapon": "888",
  "Minesta's Unbinding Stone (Highest) - Suit": "889",
  "Master's Osmium Daikatana": "89",
  "Minesta's Unbinding Stone (Highest) - Glove": "890",
  "[Costume] Wealthy Party Epaulet": "891",
  "NightWalkers (Armor) R": "892",
  "NightWalkers (Battle) R": "893",
  "NightWalkers (Martial) R": "894",
  "Social Effect Kit-Firecracker of Heil": "895",
  "Social Effect Kit-Sample Emblem": "896",
  "Social Effect Kit-Sample Logo": "897",
  "Social Effect Kit4": "898",
  "Lost Amulet": "899",
  "Upgrade Core (Low)": "9",
  "Training Great Sword": "90",
  "Advisor": "900",
  "Change Kit (Hair Style) - Novice": "901",
  "Change Kit (Hair Style) - Foetree": "902",
  "Change Kit (Hair Style) - Season": "903",
  "Change Kit (Color) - Normal": "904",
  "Change Kit (Color) - Premium": "905",
  "Change Kit (Face) - Novice": "906",
  "Change Kit (Face) - Premium": "907",
  "Minisha's Relic Key": "908",
  "Change Kit(Hair Style) - Charming": "909",
  "Greatsword": "91",
  "Change Kit - RESERVED3": "910",
  "Rename Card": "911",
  "Core Enhancer (Low)": "912",
  "Core Enhancer (Medium)": "913",
  "Core Enhancer (High)": "914",
  "Blessing Bead - Warehouse (No. 4)": "915",
  "Blessing Bead - EXP (10%)": "916",
  "Blessing Bead - EXP (25%)": "917",
  "Blessing Bead - Skill EXP (10%)": "918",
  "Blessing Bead - Skill EXP (25%)": "919",
  "Iron Great Sword": "92",
  "Blessing Bead - Drop Rate (50%)": "920",
  "Blessing Bead - Drop Rate (100%)": "921",
  "GolemCookieS": "922",
  "Damascus Great Sword": "93",
  "[Costume] Epaulet of Moon Walker (Armor)": "934",
  "[Costume] Epaulet of Moon Walker (Battle)": "935",
  "[Costume] Epaulet of Moon Walker (Martial)": "936",
  "Astral Board Card - Zero Black": "937",
  "Astral Board Card - Zero Silver": "938",
  "Odd Circle": "939",
  "Shadowsteel Great Sword": "94",
  "Resurrection Potion": "940",
  "Cure Potion": "942",
  "Remote Shop Card (One time use)": "943",
  "Return Core": "944",
  "Epaulet of the Dead2N": "945",
  "Pet - Little Jack": "946",
  "Epaulet of Discipline": "947",
  "Sample Orb (Lv. 10)": "948",
  "Sample Crystal (Lv. 10)": "949",
  "Bluestin Great Sword": "95",
  "Sample Katana (Lv. 10)": "950",
  "Sample Blade (Lv. 10)": "951",
  "Sample Daikatana (Lv. 10)": "952",
  "Sample Great Sword (Lv. 10)": "953",
  "Sample Armorsuit (Lv. 10)": "954",
  "Sample Battlesuit (Lv. 10)": "955",
  "Sample Martialsuit (Lv. 10)": "956",
  "Sample Armorgloves (Lv. 10)": "957",
  "Sample Battlegloves (Lv. 10)": "958",
  "Sample Martialgloves (Lv. 10)": "959",
  "Titanium Great Sword": "96",
  "Sample Armorboots (Lv. 10)": "960",
  "Sample Battleboots (Lv. 10)": "961",
  "Sample Martialboots (Lv. 10)": "962",
  "Sample Armorhelm (Lv. 10)": "963",
  "Sample Battlehelm (Lv. 10)": "964",
  "Sample Martialhelm (Lv. 10)": "965",
  "Sample Orb (Lv. 30)": "966",
  "Sample Crystal (Lv. 30)": "967",
  "Sample Katana (Lv. 30)": "968",
  "Sample Blade (Lv. 30)": "969",
  "Shadow Titanium Great Sword": "97",
  "Sample Daikatana (Lv. 30)": "970",
  "Sample Great Sword (Lv. 30)": "971",
  "Sample Armorsuit (Lv. 30)": "972",
  "Sample Battlesuit (Lv. 30)": "973",
  "Sample Martialsuit (Lv. 30)": "974",
  "Sample Armorgloves (Lv. 30)": "975",
  "Sample Battlegloves (Lv. 30)": "976",
  "Sample Martialgloves (Lv. 30)": "977",
  "Sample Armorboots (Lv. 30)": "978",
  "Sample Battleboots (Lv. 30)": "979",
  "Osmium Great Sword": "98",
  "Sample Martialboots (Lv. 30)": "980",
  "Sample Armorhelm (Lv. 30)": "981",
  "Sample Battlehelm (Lv. 30)": "982",
  "Sample Martialhelm (Lv. 30)": "983",
  "Sample Orb (Lv. 50)": "984",
  "Sample Crystal (Lv. 50)": "985",
  "Sample Katana (Lv. 50)": "986",
  "Sample Blade (Lv. 50)": "987",
  "Sample Daikatana (Lv. 50)": "988",
  "Sample Great Sword (Lv. 50)": "989",
  "Red Osmium Great Sword": "99",
  "Sample Armorsuit (Lv. 50)": "990",
  "Sample Battlesuit (Lv. 50)": "991",
  "Sample Martialsuit (Lv. 50)": "992",
  "Sample Armorgloves (Lv. 50)": "993",
  "Sample Battlegloves (Lv. 50)": "994",
  "Sample Martialgloves (Lv. 50)": "995",
  "Sample Armorboots (Lv. 50)": "996",
  "Sample Battleboots (Lv. 50)": "997",
  "Sample Martialboots (Lv. 50)": "998",
  "Sample Armorhelm (Lv. 50)": "999"
}