
    <style>
        .grid { 
            display: grid;
            grid-template-columns: repeat(8, 0.01fr);
            gap: 10px; 
            margin-top: 10px; }
        .slot {
            background-color: #1f1e1e8c;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            width: 90px;
            height: 90px;
            text-align: center;
            line-height: 1.1;
            padding: 1px;
            margin: 2px;
            border: 1px solid #555;
            border-radius: 8px;
            cursor: pointer;
        }
        .slot.empty {
            background:rgb(177, 177, 177);
            color: #888;
            opacity: 0.5;
        }
        .slot 
        .label { 
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 12px; }
        #hexAll {
            width: 100%;
            height: 100px;
            margin-top: 30px;
            font-family: monospace;
        }
        .tab-header { margin-top: 20px; }
    </style>

    <h2 class="mb-4">🎮 Inventory  Viewer</h2>
     <div class="panel-content mb-4">
        <ul class="nav nav-tabs" id="tabNav" role="tablist">
        </ul>
        <div class="tab-content border border-top-0 p-3" id="tabs"></div>

        <h3 class="mt-4">🔢 HexData รวมทั้งหมด</h3>
        <textarea id="hexAll" class="form-control mb-2" readonly></textarea>
        <button id="updateInventoryBtn" class="btn btn-primary mb-4">Update Inventory</button>
    </div>


    <!-- Modal for editing slot -->
    <div class="modal fade" id="editSlotModal" tabindex="-1" role="dialog" aria-labelledby="editSlotModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-light">
          <form id="editSlotForm">
            <div class="modal-header">
              <h5 class="modal-title" id="editSlotModalLabel">แก้ไข Slot <span id="editSlotLabel"></span></h5>
              <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="closeModalBtn">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <input type="hidden" id="editSlotNumber" name="slot">
              <div class="form-group">
                <label for="editItemIndex">ItemIndex:</label>
                <input type="number" class="form-control" id="editItemIndex" name="itemIndex">
              </div>
              <div class="form-group">
                <label for="editSerial">Serial:</label>
                <input type="number" class="form-control" id="editSerial" name="serial">
              </div>
              <div class="form-group">
                <label for="editOption">Option:</label>
                <input type="number" class="form-control" id="editOption" name="option">
              </div>
              <div class="form-group">
                <label for="editPeriod">Period:</label>
                <input type="number" class="form-control" id="editPeriod" name="period">
              </div>
              <div class="form-group">
                <label for="editKindIdx">KindIdx:</label>
                <input type="number" class="form-control" id="editKindIdx" name="kindIdx">
              </div>
              <div style="margin:10px 0 0 0;color:#2ecc71;font-family:monospace;font-size:13px;">HexData: <span id="modalHexPreview"></span></div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" id="cancelEditSlot" data-dismiss="modal">ยกเลิก</button>
              <button type="submit" class="btn btn-success">บันทึก</button>
            </div>
          </form>
        </div>
      </div>
    </div>

<script>
function updateHex(slot, data) {
    const slotHex = slot.toString(16).padStart(4, '0');
    const slotLE = slotHex.match(/.{2}/g).reverse().join('');
    return data.HexData.slice(0, 48) + slotLE + data.HexData.slice(52);
}

function bigIntToLittleEndianHexJS(value, lengthBytes) {
    let hex = value.toString(16);
    hex = hex.padStart(lengthBytes * 2, '0');
    let bytes = hex.match(/.{2}/g);
    return bytes.reverse().join('');
}

function rebuildHexAll(gridData) {
    const hexAll = [];
    Object.keys(gridData).sort((a,b)=>parseInt(a)-parseInt(b)).forEach(slot => {
        if (gridData[slot]) hexAll.push(gridData[slot].HexData);
    });
    $('#hexAll').val('0x' + hexAll.join('').toUpperCase());
}

$.getJSON('files/game_systems/class_module/inventory_data.php?ajax=1', function(data) {
    const gridData = {};
    Object.keys(data).forEach(s => gridData[s] = data[s]);

    const tabNav = $('#tabNav');
    const tabs = $('#tabs');
    const slotsPerTab = 64;
    const maxSlot = Math.max(...Object.keys(gridData).map(n => parseInt(n)));
    const tabCount = Math.ceil((maxSlot + 1) / slotsPerTab);

    for (let t = 0; t < tabCount; t++) {
        const start = t * slotsPerTab;
        const end = start + slotsPerTab - 1;
        const tabId = 'tab_' + t;
        // Bootstrap 4 nav-tabs
        tabNav.append(`
            <li class="nav-item">
                <a class="nav-link${t === 0 ? ' active' : ''}" id="${tabId}-tab" data-toggle="tab" href="#${tabId}" role="tab" aria-controls="${tabId}" aria-selected="${t === 0 ? 'true' : 'false'}">เก็บของ ${start}-${end}</a>
            </li>
        `);

        const grid = $('<div class="grid"></div>');
        for (let s = start; s <= end; s++) {
            const slotDiv = $('<div class="slot" data-slot="'+s+'" id="slot_'+s+'"></div>');
            slotDiv.append(`<div class="label">${s}</div>`);
            if (gridData[s]) {
                slotDiv.append(`<div>${gridData[s].itemIndex}</div>`);
                slotDiv.append(`<div>${gridData[s].Option}</div>`);
                // slotDiv.append(`<div>Period: ${gridData[s].Period}</div>`);
                //slotDiv.append(`<div class="hex">Hex: ${gridData[s].HexData}</div>`);
                // ปุ่มลบไอเท็ม (แสดงใน grid ด้วย)
                slotDiv.append(`<button class="remove-item-btn" data-slot="${s}" style="margin-top:8px;background:#e74c3c;color:#fff;padding:3px 12px;border:none;border-radius:4px;cursor:pointer;">ลบไอเท็ม</button>`);
            } else {
                slotDiv.addClass('empty');
                slotDiv.append(`<div>Emtry</div>`);
            }
            grid.append(slotDiv);
        }
        // Bootstrap 4 tab-pane
        tabs.append(`<div class="tab-pane fade${t === 0 ? ' show active' : ''}" id="${tabId}" role="tabpanel" aria-labelledby="${tabId}-tab">${grid.prop('outerHTML')}</div>`);
    }

    // Bootstrap 4 tabs: handled by Bootstrap JS, no need for manual activation
    rebuildHexAll(gridData);

    $('.slot').draggable({
        helper: "clone",
        revert: "invalid"
    });

    $('.slot').droppable({
        accept: ".slot",
        drop: function(event, ui) {
            const fromSlot = $(ui.draggable).data('slot');
            const toSlot = $(this).data('slot');

            const temp = gridData[fromSlot];
            gridData[fromSlot] = gridData[toSlot];
            gridData[toSlot] = temp;

            if (gridData[fromSlot]) gridData[fromSlot].HexData = updateHex(fromSlot, gridData[fromSlot]);
            if (gridData[toSlot]) gridData[toSlot].HexData = updateHex(toSlot, gridData[toSlot]);


            // รีโหลดข้อความในช่อง (แก้ไข bug: ต้องใช้ gridData[slot] ทุกจุด)
            const reload = (slot) => {
                const div = $('#slot_' + slot);
                div.empty().removeClass('empty');
                div.append(`<div class="label">${slot}</div>`);
                if (gridData[slot]) {
                    div.append(`<div>${gridData[slot].itemIndex}</div>`);
                    div.append(`<div>${gridData[slot].Option}</div>`);
                    //div.append(`<div>Period: ${gridData[slot].Period}</div>`);
                    //div.append(`<div class="hex">Hex: ${gridData[slot].HexData}</div>`);
                    div.append(`<button class="remove-item-btn" data-slot="${slot}" style="margin-top:8px;background:#e74c3c;color:#fff;padding:3px 12px;border:none;border-radius:4px;cursor:pointer;">ลบไอเท็ม</button>`);

                } else {
                    div.addClass('empty');
                    div.append(`<div>Emtry</div>`);
                }
            };

            reload(fromSlot);
            reload(toSlot);
            rebuildHexAll(gridData);
        }
    });


    // คลิกขวา (contextmenu) ที่ slot เพื่อแก้ไข
    $(document).on('contextmenu', '.slot', function(e) {
        e.preventDefault();
        const slot = $(this).data('slot');
        const data = gridData[slot] || { itemIndex: '', Serial: '', Option: '', Period: '', KindIdx: '' };
        $('#editSlotNumber').val(slot);
        $('#editSlotLabel').text(slot);
        $('#editItemIndex').val(data.itemIndex || '0');
        $('#editSerial').val(data.Serial || '0');
        $('#editOption').val(data.Option || '0');
        $('#editPeriod').val(data.Period || '0');
        $('#editKindIdx').val(data.KindIdx || '0');
        $('#editSlotModal').modal('show');

        // ฟังก์ชัน encode itemIndex ลง KindIdx (เหมือน PHP)
        function setItemIndexInKindIdx(kindIdx, itemIndex) {
            // ลบ bit itemIndex เดิมออก แล้วใส่ใหม่
            return (kindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        }

        // ฟังก์ชันอัพเดท HexData แบบเรียลไทม์
        function updateModalHex() {
            let KindIdx = parseInt($('#editKindIdx').val()) || 0;
            const itemIndex = parseInt($('#editItemIndex').val()) || 0;
            const Serial = parseInt($('#editSerial').val()) || 0;
            const Option = parseInt($('#editOption').val()) || 0;
            const slotNum = parseInt($('#editSlotNumber').val()) || 0;
            const Period = parseInt($('#editPeriod').val()) || 0;
            // encode itemIndex ลง KindIdx
            KindIdx = setItemIndexInKindIdx(KindIdx, itemIndex);
            const hexData =
                bigIntToLittleEndianHexJS(KindIdx, 8) +
                bigIntToLittleEndianHexJS(Serial, 8) +
                bigIntToLittleEndianHexJS(Option, 8) +
                bigIntToLittleEndianHexJS(slotNum, 2) +
                bigIntToLittleEndianHexJS(Period, 4);
            $('#modalHexPreview').text(hexData.toUpperCase());
        }
        updateModalHex();
        $('#editSlotForm input').off('input.modalhex').on('input.modalhex', updateModalHex);
    });


    // ปิด modal เมื่อกดปุ่มยกเลิก หรือปุ่ม X (Bootstrap 4)
    $('#cancelEditSlot, #closeModalBtn').on('click', function() {
        $('#editSlotModal').modal('hide');
    });


    $('#editSlotForm').on('submit', function(e) {
        e.preventDefault();

        const slot = parseInt($('#editSlotNumber').val());
        const itemIndex = parseInt($('#editItemIndex').val()) || 0;
        const Serial = parseInt($('#editSerial').val()) || 0;
        const Option = parseInt($('#editOption').val()) || 0;
        const Period = parseInt($('#editPeriod').val()) || 0;
        let KindIdx = parseInt($('#editKindIdx').val()) || 0;

        // encode itemIndex ลง KindIdx ก่อนสร้าง HexData
        KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        const hexData = $('#modalHexPreview').text();

        gridData[slot] = {
            KindIdx: KindIdx,
            itemIndex: itemIndex,
            Serial: Serial,
            Option: Option,
            Period: Period,
            HexData: hexData
        };

        // อัพเดท UI
        const div = $('#slot_' + slot);
        div.empty().removeClass('empty');
        div.append(`<div class="label">${slot}</div>`);
        div.append(`<div>${itemIndex}</div>`);
        div.append(`<div>${Option}</div>`);
        //div.append(`<div>Period: ${Period}</div>`);
        //div.append(`<div class="hex">Hex: ${hexData}</div>`);
        // ปุ่มลบไอเท็ม (แสดงใน modal ด้วย)
        div.append(`<button class="remove-item-btn" data-slot="${slot}" style="margin-top:8px;background:#e74c3c;color:#fff;padding:3px 12px;border:none;border-radius:4px;cursor:pointer;">ลบไอเท็ม</button>`);

        $('#editSlotModal').modal('hide');
        rebuildHexAll(gridData);
    });

    // ปุ่มลบไอเท็มออกจาก slot (ต้องอยู่นอก on submit)
    $(document).on('click', '.remove-item-btn', function(e) {
        e.stopPropagation();
        const slot = $(this).data('slot');
        // ลบข้อมูล slot และอัปเดท UI
        delete gridData[slot];
        const div = $('#slot_' + slot);
        div.empty().addClass('empty');
        div.append(`<div class="label">${slot}</div>`);
        div.append(`<div>Emtry</div>`);
        rebuildHexAll(gridData);
    });

    // ฟังก์ชันแปลง hex string เป็น binary (base64)
    function hexToBase64(hex) {
        return btoa(hex.match(/.{1,2}/g).map(byte => String.fromCharCode(parseInt(byte, 16))).join(''));
    }

    $('#updateInventoryBtn').on('click', function() {
        const hex = $('#hexAll').val().replace(/^0x/i, '');
        // ส่งไปอัปเดตฝั่ง PHP (base64)
        $.ajax({
            url: 'files/game_systems/class_module/inventory_data.php',
            method: 'POST',
            data: {
                update_inventory: 1,
                hex: hex
            },
            success: function(res) {
                alert(res);
            },
            error: function() {
                alert('Update failed');
            }
        });
    });
});
</script>