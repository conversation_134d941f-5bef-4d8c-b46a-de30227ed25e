<?php 
// Debug file สำหรับตรวจสอบปัญหา character-analytics
error_reporting(E_ALL);
ini_set('display_errors', 1);

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($conn)) {
    echo "❌ Database connection not found<br>";
    exit;
}

echo "<h2>🔍 Character Analytics Debug</h2>";

// Test 1: ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>1. Database Connection Test</h3>";
try {
    $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
    $result = sqlsrv_query($conn, $sql);
    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        echo "✅ Database connected - Total characters: " . number_format($row['count']) . "<br>";
    } else {
        echo "❌ Database query failed<br>";
        if (sqlsrv_errors()) {
            echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: ตรวจสอบ daily creation data
echo "<h3>2. Daily Creation Data Test</h3>";
try {
    $sql = "SELECT TOP 5
                CAST(CreateDate AS DATE) as create_date,
                COUNT(*) as new_characters
            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
            WHERE CreateDate >= DATEADD(day, -7, GETDATE())
            GROUP BY CAST(CreateDate AS DATE)
            ORDER BY create_date DESC";
    
    $result = sqlsrv_query($conn, $sql);
    if ($result) {
        echo "✅ Daily creation query successful<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Date</th><th>New Characters</th><th>Date Type</th></tr>";
        
        $hasData = false;
        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $hasData = true;
            $createDate = $row['create_date'];
            $dateType = gettype($createDate);
            $dateStr = '';
            
            if ($createDate instanceof DateTime) {
                $dateStr = $createDate->format('Y-m-d');
            } elseif (is_string($createDate)) {
                $dateStr = $createDate;
            } else {
                $dateStr = 'Invalid date';
            }
            
            echo "<tr>";
            echo "<td>" . $dateStr . "</td>";
            echo "<td>" . $row['new_characters'] . "</td>";
            echo "<td>" . $dateType . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (!$hasData) {
            echo "⚠️ No data found for the last 7 days<br>";
        }
    } else {
        echo "❌ Daily creation query failed<br>";
        if (sqlsrv_errors()) {
            echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
        }
    }
} catch (Exception $e) {
    echo "❌ Daily creation error: " . $e->getMessage() . "<br>";
}

// Test 3: ตรวจสอบ JavaScript dependencies
echo "<h3>3. JavaScript Dependencies Test</h3>";
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 JavaScript Dependencies Check:');
    
    // ตรวจสอบ jQuery
    if (typeof jQuery !== 'undefined') {
        console.log('✅ jQuery loaded:', jQuery.fn.jquery);
        document.write('✅ jQuery loaded: ' + jQuery.fn.jquery + '<br>');
    } else {
        console.log('❌ jQuery not loaded');
        document.write('❌ jQuery not loaded<br>');
    }
    
    // ตรวจสอบ Chart.js
    if (typeof Chart !== 'undefined') {
        console.log('✅ Chart.js loaded:', Chart.version);
        document.write('✅ Chart.js loaded: ' + Chart.version + '<br>');
    } else {
        console.log('❌ Chart.js not loaded');
        document.write('❌ Chart.js not loaded<br>');
    }
    
    // ตรวจสอบ Canvas support
    var canvas = document.createElement('canvas');
    if (canvas.getContext && canvas.getContext('2d')) {
        console.log('✅ Canvas 2D supported');
        document.write('✅ Canvas 2D supported<br>');
    } else {
        console.log('❌ Canvas 2D not supported');
        document.write('❌ Canvas 2D not supported<br>');
    }
});
</script>

<?php
// Test 4: ตรวจสอบ PHP errors
echo "<h3>4. PHP Error Check</h3>";

// ตรวจสอบ memory limit
$memoryLimit = ini_get('memory_limit');
echo "Memory Limit: " . $memoryLimit . "<br>";

// ตรวจสอบ execution time
$maxExecutionTime = ini_get('max_execution_time');
echo "Max Execution Time: " . $maxExecutionTime . " seconds<br>";

// ตรวจสอบ error reporting
$errorReporting = error_reporting();
echo "Error Reporting Level: " . $errorReporting . "<br>";

// Test 5: ทดสอบ getCharacterAnalytics function
echo "<h3>5. getCharacterAnalytics Function Test</h3>";

function getCharacterAnalyticsDebug($conn, $days = 7) {
    $analytics = array();
    
    try {
        echo "🔍 Testing daily creation query...<br>";
        
        // Daily character creation
        $sql = "SELECT 
                    CAST(CreateDate AS DATE) as create_date,
                    COUNT(*) as new_characters
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CreateDate >= DATEADD(day, -?, GETDATE())
                GROUP BY CAST(CreateDate AS DATE)
                ORDER BY create_date DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
        if ($stmt && sqlsrv_execute($stmt)) {
            echo "✅ Daily creation query prepared and executed<br>";
            $count = 0;
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $analytics['daily_creation'][] = $row;
                $count++;
            }
            echo "📊 Found " . $count . " daily creation records<br>";
        } else {
            echo "❌ Daily creation query failed<br>";
            if (sqlsrv_errors()) {
                echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
            }
        }
        
        // Test playtime distribution
        echo "🔍 Testing playtime distribution query...<br>";
        
        $sql = "SELECT 
                    CASE 
                        WHEN PlayTime < 3600 THEN '< 1 hour'
                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                        WHEN PlayTime BETWEEN 36001 AND 180000 THEN '10-50 hours'
                        WHEN PlayTime BETWEEN 180001 AND 720000 THEN '50-200 hours'
                        WHEN PlayTime > 720000 THEN '200+ hours'
                        ELSE 'Unknown'
                    END as playtime_range,
                    COUNT(*) as count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                GROUP BY 
                    CASE 
                        WHEN PlayTime < 3600 THEN '< 1 hour'
                        WHEN PlayTime BETWEEN 3600 AND 36000 THEN '1-10 hours'
                        WHEN PlayTime BETWEEN 36001 AND 180000 THEN '10-50 hours'
                        WHEN PlayTime BETWEEN 180001 AND 720000 THEN '50-200 hours'
                        WHEN PlayTime > 720000 THEN '200+ hours'
                        ELSE 'Unknown'
                    END
                ORDER BY count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            echo "✅ Playtime distribution query successful<br>";
            $count = 0;
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $analytics['playtime_distribution'][] = $row;
                $count++;
            }
            echo "📊 Found " . $count . " playtime distribution records<br>";
        } else {
            echo "❌ Playtime distribution query failed<br>";
            if (sqlsrv_errors()) {
                echo "<pre>" . print_r(sqlsrv_errors(), true) . "</pre>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Function error: " . $e->getMessage() . "<br>";
        error_log("Character analytics debug error: " . $e->getMessage());
    }
    
    return $analytics;
}

$testAnalytics = getCharacterAnalyticsDebug($conn, 7);

echo "<h3>6. Analytics Data Summary</h3>";
echo "Daily Creation Records: " . (isset($testAnalytics['daily_creation']) ? count($testAnalytics['daily_creation']) : 0) . "<br>";
echo "Playtime Distribution Records: " . (isset($testAnalytics['playtime_distribution']) ? count($testAnalytics['playtime_distribution']) : 0) . "<br>";

if (!empty($testAnalytics)) {
    echo "<h4>Sample Data:</h4>";
    echo "<pre>" . print_r($testAnalytics, true) . "</pre>";
} else {
    echo "⚠️ No analytics data returned<br>";
}

// Test 6: ตรวจสอบ file permissions และ paths
echo "<h3>7. File System Check</h3>";

$filesToCheck = [
    'character-analytics.php',
    '../assets/js/chart.min.js',
    '../assets/css/app.bundle.css'
];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        echo "✅ File exists: " . $file . "<br>";
        if (is_readable($file)) {
            echo "✅ File readable: " . $file . "<br>";
        } else {
            echo "❌ File not readable: " . $file . "<br>";
        }
    } else {
        echo "❌ File not found: " . $file . "<br>";
    }
}

echo "<h3>8. Server Environment</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";

echo "<h3>9. Recommendations</h3>";
echo "<div style='background: #f0f8ff; padding: 10px; border-left: 4px solid #007bff;'>";
echo "<strong>หากพบปัญหา:</strong><br>";
echo "1. ตรวจสอบ Console (F12) หา JavaScript errors<br>";
echo "2. ตรวจสอบ Network tab ว่า CSS/JS files โหลดได้หรือไม่<br>";
echo "3. ตรวจสอบ PHP error logs<br>";
echo "4. ตรวจสอบ database connection และ permissions<br>";
echo "5. ตรวจสอบ Chart.js version compatibility<br>";
echo "</div>";
?>
