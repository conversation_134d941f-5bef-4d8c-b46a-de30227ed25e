<?php
$zpanel->checkSession(true);
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $packageName = trim($_POST['package_name']);
        $packageDescription = trim($_POST['package_description']);
        $packageValue = (int)$_POST['package_value'];
        $itemNum = (int)$_POST['item_num'];
        $packageIcon = trim($_POST['package_icon']);
        $packageColor = trim($_POST['package_color']);
        $sortOrder = (int)$_POST['sort_order'];
        
        $query = "EXEC sp_ManageVoucherPackage 
                  @Action = 'INSERT',
                  @PackageName = ?,
                  @PackageDescription = ?,
                  @PackageValue = ?,
                  @ItemNum = ?,
                  @PackageIcon = ?,
                  @PackageColor = ?,
                  @SortOrder = ?,
                  @UpdatedBy = ?";
        
        $params = [$packageName, $packageDescription, $packageValue, $itemNum, 
                  $packageIcon, $packageColor, $sortOrder, 'ADMIN'];
        
        $result = sqlsrv_query($conn, $query, $params);
        
        if ($result) {
            $message = "เพิ่ม Package สำเร็จ!";
            $messageType = "success";
        } else {
            $message = "เกิดข้อผิดพลาดในการเพิ่ม Package";
            $messageType = "error";
        }
    }
    
    elseif ($action === 'update') {
        $packageId = (int)$_POST['package_id'];
        $packageName = trim($_POST['package_name']);
        $packageDescription = trim($_POST['package_description']);
        $packageValue = (int)$_POST['package_value'];
        $itemNum = (int)$_POST['item_num'];
        $packageIcon = trim($_POST['package_icon']);
        $packageColor = trim($_POST['package_color']);
        $sortOrder = (int)$_POST['sort_order'];
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        $query = "EXEC sp_ManageVoucherPackage 
                  @Action = 'UPDATE',
                  @PackageID = ?,
                  @PackageName = ?,
                  @PackageDescription = ?,
                  @PackageValue = ?,
                  @ItemNum = ?,
                  @PackageIcon = ?,
                  @PackageColor = ?,
                  @IsActive = ?,
                  @SortOrder = ?,
                  @UpdatedBy = ?";
        
        $params = [$packageId, $packageName, $packageDescription, $packageValue, $itemNum, 
                  $packageIcon, $packageColor, $isActive, $sortOrder, 'ADMIN'];
        
        $result = sqlsrv_query($conn, $query, $params);
        
        if ($result) {
            $message = "อัปเดต Package สำเร็จ!";
            $messageType = "success";
        } else {
            $message = "เกิดข้อผิดพลาดในการอัปเดต Package";
            $messageType = "error";
        }
    }
    
    elseif ($action === 'delete') {
        $packageId = (int)$_POST['package_id'];
        
        $query = "EXEC sp_ManageVoucherPackage 
                  @Action = 'DELETE',
                  @PackageID = ?,
                  @UpdatedBy = ?";
        
        $params = [$packageId, 'ADMIN'];
        
        $result = sqlsrv_query($conn, $query, $params);
        
        if ($result) {
            $message = "ลบ Package สำเร็จ!";
            $messageType = "success";
        } else {
            $message = "เกิดข้อผิดพลาดในการลบ Package";
            $messageType = "error";
        }
    }
}

// ดึงข้อมูล Packages ทั้งหมด
$packages = [];
$query = "EXEC sp_ManageVoucherPackage @Action = 'SELECT'";
$result = sqlsrv_query($conn, $query);

if ($result) {
    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $packages[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการ Voucher Packages - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .package-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .package-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .package-card.inactive {
            opacity: 0.6;
            background-color: #f8f9fa;
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            border: 2px solid #ddd;
        }
        .form-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <h1><i class="fas fa-cogs"></i> จัดการ Voucher Packages</h1>
            <p class="mb-0">ระบบจัดการแพ็คเกจ Voucher สำหรับร้านค้า</p>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Add New Package Form -->
        <div class="form-section">
            <h3><i class="fas fa-plus-circle"></i> เพิ่ม Package ใหม่</h3>
            <form method="POST" action="">
                <input type="hidden" name="action" value="add">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">ชื่อ Package</label>
                            <input type="text" class="form-control" name="package_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">ราคา (Cash)</label>
                            <input type="number" class="form-control" name="package_value" min="1" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">รายละเอียด</label>
                    <textarea class="form-control" name="package_description" rows="2" required></textarea>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Item Number</label>
                            <input type="number" class="form-control" name="item_num" min="1" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">Icon Class</label>
                            <input type="text" class="form-control" name="package_icon" value="fas fa-gift" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">สี</label>
                            <input type="color" class="form-control" name="package_color" value="#ddf247" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">ลำดับการแสดง</label>
                            <input type="number" class="form-control" name="sort_order" value="0" min="0" required>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> เพิ่ม Package
                </button>
            </form>
        </div>

        <!-- Packages List -->
        <div class="row">
            <div class="col-12">
                <h3><i class="fas fa-list"></i> รายการ Packages ทั้งหมด</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>ชื่อ Package</th>
                                <th>รายละเอียด</th>
                                <th>ราคา</th>
                                <th>Item#</th>
                                <th>Icon</th>
                                <th>สี</th>
                                <th>ลำดับ</th>
                                <th>สถานะ</th>
                                <th>จัดการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($packages as $package): ?>
                            <tr class="<?php echo $package['IsActive'] ? '' : 'table-secondary'; ?>">
                                <td><?php echo $package['PackageID']; ?></td>
                                <td><?php echo htmlspecialchars($package['PackageName']); ?></td>
                                <td><?php echo htmlspecialchars($package['PackageDescription']); ?></td>
                                <td><?php echo number_format($package['PackageValue']); ?> Cash</td>
                                <td><?php echo $package['ItemNum']; ?></td>
                                <td><i class="<?php echo htmlspecialchars($package['PackageIcon']); ?>"></i></td>
                                <td>
                                    <span class="color-preview" style="background-color: <?php echo htmlspecialchars($package['PackageColor']); ?>"></span>
                                    <?php echo htmlspecialchars($package['PackageColor']); ?>
                                </td>
                                <td><?php echo $package['SortOrder']; ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $package['IsActive'] ? 'success' : 'secondary'; ?>">
                                        <?php echo $package['IsActive'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-warning" onclick="editPackage(<?php echo htmlspecialchars(json_encode($package)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('ยืนยันการลบ?')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="package_id" value="<?php echo $package['PackageID']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">แก้ไข Package</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="package_id" id="edit_package_id">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ชื่อ Package</label>
                                    <input type="text" class="form-control" name="package_name" id="edit_package_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ราคา (Cash)</label>
                                    <input type="number" class="form-control" name="package_value" id="edit_package_value" min="1" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">รายละเอียด</label>
                            <textarea class="form-control" name="package_description" id="edit_package_description" rows="2" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Item Number</label>
                                    <input type="number" class="form-control" name="item_num" id="edit_item_num" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">Icon Class</label>
                                    <input type="text" class="form-control" name="package_icon" id="edit_package_icon" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">สี</label>
                                    <input type="color" class="form-control" name="package_color" id="edit_package_color" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">ลำดับการแสดง</label>
                                    <input type="number" class="form-control" name="sort_order" id="edit_sort_order" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    เปิดใช้งาน
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">บันทึกการแก้ไข</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editPackage(packageData) {
            document.getElementById('edit_package_id').value = packageData.PackageID;
            document.getElementById('edit_package_name').value = packageData.PackageName;
            document.getElementById('edit_package_description').value = packageData.PackageDescription;
            document.getElementById('edit_package_value').value = packageData.PackageValue;
            document.getElementById('edit_item_num').value = packageData.ItemNum;
            document.getElementById('edit_package_icon').value = packageData.PackageIcon;
            document.getElementById('edit_package_color').value = packageData.PackageColor;
            document.getElementById('edit_sort_order').value = packageData.SortOrder;
            document.getElementById('edit_is_active').checked = packageData.IsActive == 1;
            
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }
    </script>
</body>
</html>
