<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Send Item</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Send Item System</h1>
        <p>This page helps debug the send item functionality.</p>
        
        <form id="debugForm">
            <div class="form-group">
                <label for="playerUsername">Player Username:</label>
                <input type="text" id="playerUsername" value="test_player" required>
            </div>
            
            <div class="form-group">
                <label for="itemCode">Item Code:</label>
                <input type="text" id="itemCode" value="0000007B00000000" required>
                <small>Example: 0000007B00000000 (Item ID 123)</small>
            </div>
            
            <div class="form-group">
                <label for="optionsCode">Options Code:</label>
                <input type="text" id="optionsCode" value="0000000000000000" required>
                <small>Example: 0000000000000000 (No options)</small>
            </div>
            
            <div class="form-group">
                <label for="quantity">Quantity:</label>
                <input type="number" id="quantity" value="1" min="1" max="999" required>
            </div>
            
            <div class="form-group">
                <label for="duration">Duration:</label>
                <input type="number" id="duration" value="31" min="0" max="999" required>
                <small>31 = Permanent, other values = days</small>
            </div>
            
            <div class="form-group">
                <label for="sendMethod">Send Method:</label>
                <select id="sendMethod" required>
                    <option value="inventory">To Inventory</option>
                    <option value="mail" selected>Via Mail System</option>
                    <option value="warehouse">To Event Inventory</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="adminUsername">Admin Username:</label>
                <input type="text" id="adminUsername" value="debug_admin" required>
            </div>
            
            <button type="submit">🚀 Send Item</button>
            <button type="button" onclick="testConnection()">🔗 Test Connection</button>
            <button type="button" onclick="clearResults()">🗑️ Clear Results</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        document.getElementById('debugForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                playerUsername: document.getElementById('playerUsername').value,
                itemCode: document.getElementById('itemCode').value,
                optionsCode: document.getElementById('optionsCode').value,
                quantity: parseInt(document.getElementById('quantity').value),
                duration: parseInt(document.getElementById('duration').value),
                sendMethod: document.getElementById('sendMethod').value,
                adminUsername: document.getElementById('adminUsername').value
            };
            
            showResult('info', 'Sending request...', JSON.stringify(formData, null, 2));
            
            try {
                const response = await fetch('send_item.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const responseText = await response.text();
                
                showResult('info', `Response Status: ${response.status}`, `Raw Response:\n${responseText}`);
                
                try {
                    const result = JSON.parse(responseText);

                    if (result.success) {
                        let successMessage = `Item sent successfully! (${result.data.quantity} items)`;

                        // Add final results summary
                        if (result.final_results) {
                            successMessage += '\n\nFINAL RESULTS:';
                            successMessage += `\n✅ Item Delivered: ${result.final_results.item_delivered ? 'YES' : 'NO'}`;
                            successMessage += `\n🔔 Notification Sent: ${result.final_results.notification_sent ? 'YES' : 'NO'}`;
                            successMessage += `\n📝 Database Logged: ${result.final_results.database_logged ? 'YES' : 'NO'}`;
                            successMessage += `\n✅ Transaction Completed: ${result.final_results.transaction_completed ? 'YES' : 'NO'}`;
                            successMessage += `\n📦 Quantity Sent: ${result.data.quantity} items`;
                        }

                        showResult('success', successMessage, JSON.stringify(result, null, 2));
                    } else {
                        showResult('error', 'Send failed', JSON.stringify(result, null, 2));
                    }
                } catch (parseError) {
                    showResult('error', 'JSON Parse Error', `Could not parse response as JSON:\n${responseText}`);
                }
                
            } catch (error) {
                showResult('error', 'Network Error', error.message);
            }
        });
        
        async function testConnection() {
            showResult('info', 'Testing connection...', 'Checking if test_send_item.php is accessible');
            
            try {
                const response = await fetch('test_send_item.php');
                const text = await response.text();
                
                if (response.ok) {
                    showResult('success', 'Connection test completed', 'Check the test results in the response');
                    
                    // Open test results in new window
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(text);
                    newWindow.document.close();
                } else {
                    showResult('error', 'Connection test failed', `Status: ${response.status}\n${text}`);
                }
            } catch (error) {
                showResult('error', 'Connection test error', error.message);
            }
        }
        
        function showResult(type, title, content) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
                <small>Time: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-fill with sample data
        window.addEventListener('load', function() {
            showResult('info', 'Debug page loaded', 'Ready to test send item functionality');
        });
    </script>
</body>
</html>
