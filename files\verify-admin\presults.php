<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGEPLAYERS; ?> <small><?php echo PT_MANAGEPLAYERS_DESC; ?></small></h1></div>
<div class="row">

    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-12">
                <a href="?url=manager/players" class="btn btn-block btn-info">กลับไปยังหน้า บัญชีผู้เล่น</a>
            </div>
        </div>
        <hr class="clean">
        <div class="panel panel-default">
            <div class="panel-body no-padd">
                <?php
                // variable
                $getResult = $_POST['search'];

                if ($getResult == '0') {
                    echo '<div class="alert alert-warning flat"><strong>Was not passed anything in the Search field.</strong></div>';
                } else {

                    // generic function to get page
                    function getPage($stmt, $pageNum, $rowsPerPage) {
                        $offset = ($pageNum - 1) * $rowsPerPage;
                        $rows = array();
                        $i = 0;
                        while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                            array_push($rows, $row);
                            $i++;
                        }
                        return $rows;
                    }

                    // Set the number of rows to be returned on a page.
                    $rowsPerPage = 1000;

                    // Define and execute the query.  
                    // Note that the query is executed with a "scrollable" cursor.
                    $sql = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum LIKE '$getResult%' OR ID LIKE '$getResult%' OR LastIp LIKE '$getResult%' OR  Email LIKE '$getResult%' OR Phone_number LIKE '$getResult%' OR IP LIKE '$getResult%'";

                    $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                    if (!$stmt)
                        die(print_r(sqlsrv_errors(), true));

                    // Get the total number of rows returned by the query.
                    $rowsReturned = sqlsrv_num_rows($stmt);
                    if ($rowsReturned === false){
                        die(print_r(sqlsrv_errors(), true));
                    }elseif ($rowsReturned == 0) {
                        echo '<div class="alert alert-warning flat"><strong>Player not found</strong></div>';
                        //exit();
                    } else {
                        /* Calculate number of pages. */
                        $numOfPages = ceil($rowsReturned / $rowsPerPage);
                    }
                    ?>
                    <table class="table table-hover">
                        <thead>
                            <tr>
							<th>[UserNum]</th>
                            <th>[ID]</th>
                            <th>[LoginTime]</th>
                            <th>[LogoutTime]</th>
							<th>[AuthType]</th>
							<th>[PlayTime]</th>
							<th>[LastIp]</th>
							<th>[createDate]</th>
							<th>[Email]</th>
							<th>[IP]</th>
							<th>[Phone]</th>
							<th><?php echo T_ACTION; ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Display the selected page of data.
                            $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                            $page = getPage($stmt, $pageNum, $rowsPerPage);

                            foreach ($page as $row) {
                                ?>
                                <tr
                                    <?php
                                    $selectUsersData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$row[0]'";
                                    $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, array());
                                    $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                                    if($selectUsersDataFetch['AuthType'] == '2' ||
                                            $selectUsersDataFetch['AuthType'] == '3'){ echo ' class="bg-red text-white"'; }
                                ?>>
                                 <td><?php echo $row[0]; ?></td>
                                <td><?php echo $row[1]; ?></td>
								<td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[4])); ?></td>
								<td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[5])); ?></td>
								<td><?php echo $row[6]; ?></td>
								<td><?php echo $row[7]; ?></td>
								<td><?php echo $row[10]; ?></td>
								<td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[13])); ?></td>
								<td><?php echo $row[14]; ?></td>
								<td><?php echo $row[15]; ?></td>
								<td><?php echo $row[19]; ?></td>
                                <td>
                                            <a class="text-info" href="?url=manager/see-player&id=<?php echo $row[0]; ?>" data-toggle="tooltip" data-placement="left" title="" data-original-title="รายระเอียด"><i class="fa fa-info-circle"></i></a>
										    <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                            <?php if ($selectUsersDataFetch['AuthType'] == '2' || $selectUsersDataFetch['AuthType'] == '3' || $selectUsersDataFetch['AuthType'] == '4') { ?>
                                                <a class="text-danger" href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=unban-wait" data-toggle="tooltip" data-placement="left" title="" data-original-title="โดนแบน"><i class="fa fa-ban"></i></a>
                                            <?php } else { ?>
                                                <a  class="text-success" href="?url=manager/see-player&id=<?php echo $row[0]; ?>&ban=wait" data-toggle="tooltip" data-placement="left" title="" data-original-title="ต้องการแบน"><i class="fa fa-check"></i></a>
                                            <?php } ?>
                                            <?php } ?>
                                     
                                </td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                <?php } ?>
                <hr>
                </div>
            </div>
        </div>
    </div>

</div>
