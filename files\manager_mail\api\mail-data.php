<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

// Simple authentication check (you might want to implement proper API authentication)
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'live_stats':
            echo json_encode(getLiveStats($conn));
            break;
            
        case 'recent_activities':
            $limit = (int)($_GET['limit'] ?? 20);
            echo json_encode(getRecentActivities($conn, $limit));
            break;
            
        case 'alerts':
            echo json_encode(getMailAlerts($conn));
            break;
            
        case 'hourly_stats':
            echo json_encode(getHourlyStats($conn));
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

function getLiveStats($conn) {
    $stats = [];
    
    try {
        // Today's mail count
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE CAST(DeliveryTime AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['today_count'] = $row['count'];
        }
        
        // This hour's mail count
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(hour, DATEDIFF(hour, 0, GETDATE()), 0)
                AND DeliveryTime < DATEADD(hour, DATEDIFF(hour, 0, GETDATE()) + 1, 0)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['hour_count'] = $row['count'];
        }
        
        // Unread mails
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE IsReceivedItem = 0 AND IsReceivedAlz = 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['unread_count'] = $row['count'];
        }
        
        // Today's Alz total
        $sql = "SELECT SUM(Alz) as total FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE CAST(DeliveryTime AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['alz_total'] = $row['total'] ?? 0;
        }
        
        // Last 5 minutes activity
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(minute, -5, GETDATE())";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['recent_activity'] = $row['count'];
        }
        
    } catch (Exception $e) {
        error_log("Live stats error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

function getRecentActivities($conn, $limit = 20) {
    $activities = [];
    
    try {
        $sql = "SELECT TOP ? 
                    ReceivedMailID,
                    ReceiverCharIdx,
                    DeliveryTime,
                    Alz,
                    ItemKindIdx,
                    ItemOption,
                    IsReceivedItem,
                    IsReceivedAlz,
                    SenderCharName,
                    Title
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                ORDER BY DeliveryTime DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, array(&$limit));
        if ($stmt && sqlsrv_execute($stmt)) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                $activities[] = [
                    'mail_id' => $row['ReceivedMailID'],
                    'receiver' => $row['ReceiverCharIdx'],
                    'time' => $row['DeliveryTime']->format('Y-m-d H:i:s'),
                    'alz' => $row['Alz'],
                    'item_code' => $row['ItemKindIdx'],
                    'item_option' => $row['ItemOption'],
                    'is_received_item' => $row['IsReceivedItem'],
                    'is_received_alz' => $row['IsReceivedAlz'],
                    'sender' => $row['SenderCharName'],
                    'title' => $row['Title']
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Recent activities error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $activities,
        'count' => count($activities)
    ];
}

function getMailAlerts($conn) {
    $alerts = [];
    
    try {
        // Large Alz transfers in last hour
        $sql = "SELECT TOP 5 
                    'large_alz' as type,
                    ReceivedMailID,
                    ReceiverCharIdx,
                    SenderCharName,
                    Alz,
                    DeliveryTime
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE Alz > 1000000 AND DeliveryTime >= DATEADD(hour, -1, GETDATE())
                ORDER BY Alz DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "Large Alz transfer: " . number_format($row['Alz']) . " from " . $row['SenderCharName'],
                    'severity' => 'high',
                    'time' => $row['DeliveryTime']->format('Y-m-d H:i:s'),
                    'data' => $row
                ];
            }
        }
        
        // High frequency senders
        $sql = "SELECT TOP 3 
                    SenderCharName,
                    COUNT(*) as mail_count
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(minute, -30, GETDATE())
                GROUP BY SenderCharName
                HAVING COUNT(*) > 5
                ORDER BY mail_count DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = [
                    'type' => 'high_frequency',
                    'message' => "High frequency sender: " . $row['SenderCharName'] . " (" . $row['mail_count'] . " mails in 30 min)",
                    'severity' => 'medium',
                    'time' => date('Y-m-d H:i:s'),
                    'data' => $row
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Mail alerts error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $alerts,
        'count' => count($alerts)
    ];
}

function getHourlyStats($conn) {
    $stats = [];
    
    try {
        $sql = "SELECT 
                    DATEPART(hour, DeliveryTime) as hour,
                    COUNT(*) as mail_count,
                    SUM(Alz) as total_alz,
                    COUNT(CASE WHEN ItemKindIdx > 0 THEN 1 END) as items_count
                FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
                WHERE DeliveryTime >= DATEADD(day, -1, GETDATE())
                GROUP BY DATEPART(hour, DeliveryTime)
                ORDER BY hour";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $stats[] = [
                    'hour' => $row['hour'],
                    'mail_count' => $row['mail_count'],
                    'total_alz' => $row['total_alz'],
                    'items_count' => $row['items_count']
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Hourly stats error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats
    ];
}
?>
