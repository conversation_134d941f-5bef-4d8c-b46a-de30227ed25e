<?php
// ตรวจสอบ path ที่ถูกต้อง
require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');
require('../../../_app/php/userLogin.class.php');
header('Content-Type: application/json; charset=utf-8');

// สร้าง instance ของ userLogin class สำหรับใช้ฟังก์ชัน thaitrans
$userLogin = new userLogged();

if (!isset($_SESSION)) {
    session_start();
}

// ตรวจสอบ session
if (!isset($_SESSION['userLogin'])) {
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    $conn = db_connect();
    if (!$conn) {
        throw new Exception('ไม่สามารถเชื่อมต่อฐานข้อมูลได้');
    }

    switch ($action) {
        case 'generate':
            generateCharacterCodes($conn);
            break;
        case 'list':
            listCharacterCodes($conn);
            break;
        case 'download':
            downloadCharacterCodes($conn);
            break;
        case 'clear_all':
            clearAllCharacterCodes($conn);
            break;
        case 'edit':
            editCharacterCode($conn);
            break;
        case 'delete':
            deleteCharacterCode($conn);
            break;
        case 'list_usage_logs':
            listCharacterUsageLogs($conn);
            break;
        case 'delete_usage_log':
            deleteCharacterUsageLog($conn);
            break;
        case 'delete_all_usage_logs':
            deleteAllCharacterUsageLogs($conn);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Action ไม่ถูกต้อง']);
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}

function createCharacterRedeemTable($conn) {
    $createSql = "
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_Redeem_Code_Character')
    BEGIN
        CREATE TABLE [dbo].[WEB_Redeem_Code_Character] (
            [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
            [user_num] int NULL,
            [character_idx] int NULL,
            [character_name] nvarchar(50) COLLATE Thai_CI_AS NULL,
            [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
            [quantity] int NULL,
            [channel_idx] int NULL,
            [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
            [used] bit DEFAULT 0,
            [datecreated] datetime DEFAULT getdate() NULL,
            [expiry_date] datetime NULL
        )
    END";

    return sqlsrv_query($conn, $createSql);
}

function generateCharacterCodes($conn) {
    $tableCreated = createCharacterRedeemTable($conn);
    if (!$tableCreated) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถสร้างตารางได้: ' . print_r(sqlsrv_errors(), true)]);
        return;
    }

    $items = trim($_POST['items'] ?? '');
    $quantity = intval($_POST['quantity'] ?? 1);
    $character_status = $_POST['character_status'] ?? '';
    $channel_idx = $_POST['channel_idx'] ?? null;
    $expiry_date = $_POST['expiry_date'] ?? '';
    $code_format = $_POST['code_format'] ?? 'XXX-XXX-XXXX-XXX';
    
    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายการไอเท็ม']);
        return;
    }
    
    if ($character_status === '' || $character_status === null) {
        echo json_encode(['success' => false, 'message' => 'กรุณาเลือกสถานะตัวละคร']);
        return;
    }
    
    // ประมวลผลข้อมูล items - ตัด newline ออกและต่อข้อมูลกัน
    $original_items = $items;
    $items = preg_replace('/\r?\n/', '', $items);   // ลบ newline ออกทั้งหมด (ข้อมูลจะต่อกัน)
    $items = preg_replace('/\s*,\s*/', ',', $items); // ลบ space รอบ comma
    $items = preg_replace('/,+/', ',', $items);     // ลบ comma ซ้ำ
    $items = trim($items, ',');  // ลบ comma ที่ต้นและท้าย

    // Debug log
    error_log("Original items: " . $original_items);
    error_log("Processed items: " . $items);

    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'รายการไอเท็มว่างเปล่าหลังประมวลผล']);
        return;
    }

    // ตรวจสอบรูปแบบไอเท็ม
    $itemList = explode(',', $items);
    foreach ($itemList as $item) {
        $item = trim($item);
        if (empty($item)) continue; // ข้าม item ที่ว่าง
        if (!preg_match('/^\d+:\d+:\d+$/', $item)) {
            echo json_encode(['success' => false, 'message' => "รูปแบบไอเท็มไม่ถูกต้อง: '$item' (ต้องเป็น itemid:option:duration)"]);
            return;
        }
    }

    // ค้นหาตัวละครที่ตรงตามเงื่อนไข (แสดงทั้งหมด)
    $characters = findEligibleCharacters($conn, $character_status, $channel_idx, 0);

    if (empty($characters)) {
        // Debug: ลองตรวจสอบว่ามีตัวละครในฐานข้อมูลหรือไม่
        $debugSql = "SELECT COUNT(*) as total FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $debugStmt = sqlsrv_query($conn, $debugSql);
        $debugCount = 0;
        if ($debugStmt) {
            $debugRow = sqlsrv_fetch_array($debugStmt, SQLSRV_FETCH_ASSOC);
            $debugCount = $debugRow['total'];
        }

        // ตรวจสอบตามสถานะ
        $statusDebugSql = "";
        if ($character_status === '0') {
            $statusDebugSql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 0";
        } elseif ($character_status === '1') {
            $statusDebugSql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 1";
        } else {
            $statusDebugSql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        }

        $statusDebugStmt = sqlsrv_query($conn, $statusDebugSql);
        $statusCount = 0;
        if ($statusDebugStmt) {
            $statusRow = sqlsrv_fetch_array($statusDebugStmt, SQLSRV_FETCH_ASSOC);
            $statusCount = $statusRow['count'];
        }

        echo json_encode([
            'success' => false,
            'message' => "ไม่พบตัวละครที่ตรงตามเงื่อนไข",
            'debug_info' => [
                'character_status' => $character_status,
                'channel_idx' => $channel_idx,
                'total_characters_in_db' => $debugCount,
                'characters_with_status' => $statusCount,
                'status_query' => $statusDebugSql
            ]
        ]);
        return;
    }

    $actualCharacterCount = count($characters);

    $generatedCodes = [];
    $successCount = 0;
    
    // สร้าง code สำหรับแต่ละตัวละครที่พบ
    foreach ($characters as $character) {
        $code = generateUniqueCharacterCode($conn, $code_format);

        if ($code) {
            // ใช้ UserNum ที่คำนวณมาจาก SQL แล้ว
            $userNum = intval($character['UserNum']);

            // แปลงชื่อตัวละครเป็นภาษาไทย
            global $userLogin;
            $thaiCharacterName = $userLogin->thaitrans($character['Name']);

            $params = [$code, $userNum, $character['CharacterIdx'], $character['Name'], $items, $quantity, $character['ChannelIdx']];

            if (!empty($expiry_date)) {
                $formatted_date = formatExpiryDate($expiry_date);
                if ($formatted_date) {
                    $sql = "INSERT INTO WEB_Redeem_Code_Character (code, user_num, character_idx, character_name, items, quantity, channel_idx, status, used, datecreated, expiry_date) VALUES (?, ?, ?, ?, ?, ?, ?, '0', 0, GETDATE(), ?)";
                    $params[] = $formatted_date;
                } else {
                    $sql = "INSERT INTO WEB_Redeem_Code_Character (code, user_num, character_idx, character_name, items, quantity, channel_idx, status, used, datecreated) VALUES (?, ?, ?, ?, ?, ?, ?, '0', 0, GETDATE())";
                }
            } else {
                $sql = "INSERT INTO WEB_Redeem_Code_Character (code, user_num, character_idx, character_name, items, quantity, channel_idx, status, used, datecreated) VALUES (?, ?, ?, ?, ?, ?, ?, '0', 0, GETDATE())";
            }

            $stmt = sqlsrv_query($conn, $sql, $params);

            if ($stmt) {
                $generatedCodes[] = [
                    'code' => $code,
                    'user_num' => $userNum,
                    'character_idx' => $character['CharacterIdx'],
                    'character_name' => $thaiCharacterName,
                    'items' => $items,
                    'quantity' => $quantity,
                    'channel_idx' => $character['ChannelIdx']
                ];
                $successCount++;
            }
        }
    }
    
    if ($actualCharacterCount > 0) {
        echo json_encode([
            'success' => true,
            'message' => "พบตัวละคร $actualCharacterCount ตัว สร้าง $successCount codes สำเร็จ",
            'characters_found' => $actualCharacterCount,
            'codes_generated' => $successCount,
            'codes' => $generatedCodes
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => "ไม่พบตัวละครที่ตรงเงื่อนไข",
            'characters_found' => 0,
            'codes_generated' => 0,
            'codes' => []
        ]);
    }
}

function findEligibleCharacters($conn, $character_status, $channel_idx, $limit) {
    // สร้าง WHERE clause ตามเงื่อนไข
    $whereConditions = [];
    $params = [];

    // ตรวจสอบสถานะตัวละคร
    if ($character_status === '0') {
        $whereConditions[] = "Login = 0"; // Offline
    } elseif ($character_status === '1') {
        $whereConditions[] = "Login = 1"; // Online
    }
    // ถ้าเป็น 'all' ไม่ต้องเพิ่มเงื่อนไข

    // ตรวจสอบ Channel
    if (!empty($channel_idx)) {
        $whereConditions[] = "ChannelIdx = ?";
        $params[] = intval($channel_idx);
    }

    // สร้าง SQL query ที่เลือกตัวละครที่มี LoginTime ล่าสุดต่อ UserNum
    // ใช้ ROW_NUMBER() เพื่อจัดอันดับตาม LoginTime ล่าสุดในแต่ละ UserNum
    $limitValue = intval($limit);

    $sql = "
    WITH RankedCharacters AS (
        SELECT
            CharacterIdx,
            Name,
            ChannelIdx,
            Login,
            Nation,
            LoginTime,
            (CharacterIdx / 16) as UserNum,
            ROW_NUMBER() OVER (PARTITION BY (CharacterIdx / 16) ORDER BY LoginTime DESC) as rn
        FROM [".DATABASE_SV."].[dbo].cabal_character_table";

    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(" AND ", $whereConditions);
    }

    $sql .= "
    )
    SELECT CharacterIdx, Name, ChannelIdx, Login, Nation, UserNum
    FROM RankedCharacters
    WHERE rn = 1";

    if ($limitValue > 0) {
        $sql .= " ORDER BY LoginTime DESC OFFSET 0 ROWS FETCH NEXT $limitValue ROWS ONLY";
    } else {
        $sql .= " ORDER BY LoginTime DESC";
    }

    $stmt = sqlsrv_query($conn, $sql, $params);

    $characters = [];
    if ($stmt) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $characters[] = $row;
        }
    } else {
        // Debug: แสดง error ถ้ามี
        error_log("SQL Error in findEligibleCharacters: " . print_r(sqlsrv_errors(), true));
    }

    return $characters;
}

function generateUniqueCharacterCode($conn, $format) {
    $maxAttempts = 100;
    
    for ($i = 0; $i < $maxAttempts; $i++) {
        $code = generateCodeByFormat($format);
        
        $checkSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code_Character WHERE code = ?";
        $checkStmt = sqlsrv_query($conn, $checkSql, [$code]);
        
        if ($checkStmt) {
            $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
            if ($result && $result['count'] == 0) {
                return $code;
            }
        }
    }
    
    return false;
}

function generateCodeByFormat($format) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

function formatExpiryDate($dateString) {
    try {
        $date = new DateTime($dateString);
        return $date->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        return null;
    }
}

function listCharacterCodes($conn) {
    $tableCreated = createCharacterRedeemTable($conn);
    if (!$tableCreated) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถสร้างตารางได้: ' . print_r(sqlsrv_errors(), true)]);
        return;
    }
    
    $sql = "SELECT TOP 100 * FROM WEB_Redeem_Code_Character ORDER BY datecreated DESC";
    $stmt = sqlsrv_query($conn, $sql);
    
    $codes = [];
    if ($stmt) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // แปลง datetime ให้เป็น string
            if ($row['datecreated']) {
                if (is_object($row['datecreated']) && method_exists($row['datecreated'], 'format')) {
                    $row['datecreated'] = $row['datecreated']->format('Y-m-d H:i:s');
                } elseif (is_string($row['datecreated'])) {
                    // ถ้าเป็น string อยู่แล้วก็ใช้ตรง ๆ
                    $row['datecreated'] = $row['datecreated'];
                }
            }
            if ($row['expiry_date']) {
                if (is_object($row['expiry_date']) && method_exists($row['expiry_date'], 'format')) {
                    $row['expiry_date'] = $row['expiry_date']->format('Y-m-d H:i:s');
                } elseif (is_string($row['expiry_date'])) {
                    // ถ้าเป็น string อยู่แล้วก็ใช้ตรง ๆ
                    $row['expiry_date'] = $row['expiry_date'];
                }
            }

            // แปลงชื่อตัวละครเป็นภาษาไทย (ถ้ายังไม่ได้แปลง)
            if ($row['character_name']) {
                global $userLogin;
                $row['character_name'] = $userLogin->thaitrans($row['character_name']);
            }

            $codes[] = $row;
        }
    }
    
    echo json_encode(['success' => true, 'codes' => $codes]);
}

function editCharacterCode($conn) {
    createCharacterRedeemTable($conn);

    $id = intval($_POST['id'] ?? 0);
    $items = trim($_POST['items'] ?? '');
    $quantity = intval($_POST['quantity'] ?? 1);
    $used = intval($_POST['used'] ?? 0);

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายการไอเท็ม']);
        return;
    }

    // ประมวลผลข้อมูล items - ตัด newline ออกและต่อข้อมูลกัน
    $original_items = $items;
    $items = preg_replace('/\r?\n/', '', $items);   // ลบ newline ออกทั้งหมด (ข้อมูลจะต่อกัน)
    $items = preg_replace('/\s*,\s*/', ',', $items); // ลบ space รอบ comma
    $items = preg_replace('/,+/', ',', $items);     // ลบ comma ซ้ำ
    $items = trim($items, ',');  // ลบ comma ที่ต้นและท้าย

    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'รายการไอเท็มว่างเปล่าหลังประมวลผล']);
        return;
    }

    // ตรวจสอบรูปแบบไอเท็ม
    $itemList = explode(',', $items);
    foreach ($itemList as $item) {
        $item = trim($item);
        if (empty($item)) continue; // ข้าม item ที่ว่าง
        if (!preg_match('/^\d+:\d+:\d+$/', $item)) {
            echo json_encode(['success' => false, 'message' => "รูปแบบไอเท็มไม่ถูกต้อง: '$item' (ต้องเป็น itemid:option:duration)"]);
            return;
        }
    }

    $sql = "UPDATE WEB_Redeem_Code_Character SET items = ?, quantity = ?, status = ? WHERE id = ?";
    $params = [$items, $quantity, $used, $id];
    $stmt = sqlsrv_query($conn, $sql, $params);

    if ($stmt) {
        $rowsAffected = sqlsrv_rows_affected($stmt);
        if ($rowsAffected > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'แก้ไขข้อมูลเรียบร้อยแล้ว'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'ไม่พบข้อมูลที่ต้องการแก้ไข'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถแก้ไขข้อมูลได้: ' . print_r(sqlsrv_errors(), true)
        ]);
    }
}

function deleteCharacterCode($conn) {
    createCharacterRedeemTable($conn);

    $id = intval($_POST['id'] ?? 0);

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    $sql = "DELETE FROM WEB_Redeem_Code_Character WHERE id = ?";
    $params = [$id];
    $stmt = sqlsrv_query($conn, $sql, $params);

    if ($stmt) {
        $rowsAffected = sqlsrv_rows_affected($stmt);
        if ($rowsAffected > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'ลบข้อมูลเรียบร้อยแล้ว'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'ไม่พบข้อมูลที่ต้องการลบ'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถลบข้อมูลได้: ' . print_r(sqlsrv_errors(), true)
        ]);
    }
}

function clearAllCharacterCodes($conn) {
    createCharacterRedeemTable($conn);

    $sql = "DELETE FROM WEB_Redeem_Code_Character";
    $stmt = sqlsrv_query($conn, $sql);

    if ($stmt) {
        $rowsAffected = sqlsrv_rows_affected($stmt);
        echo json_encode([
            'success' => true,
            'message' => "ลบข้อมูลทั้งหมดเรียบร้อยแล้ว ($rowsAffected รายการ)"
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่สามารถลบข้อมูลได้: ' . print_r(sqlsrv_errors(), true)
        ]);
    }
}

function downloadCharacterCodes($conn) {
    $tableCreated = createCharacterRedeemTable($conn);
    if (!$tableCreated) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถสร้างตารางได้']);
        return;
    }
    
    $sql = "SELECT * FROM WEB_Redeem_Code_Character ORDER BY datecreated DESC";
    $stmt = sqlsrv_query($conn, $sql);
    
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: attachment; filename="character_redeem_codes_' . date('Y-m-d_H-i-s') . '.txt"');
    
    echo "Character Redeem Codes - Generated on " . date('Y-m-d H:i:s') . "\n";
    echo "=================================================\n\n";
    
    if ($stmt) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $channelText = $row['channel_idx'] ? "Channel {$row['channel_idx']}" : 'All Channels';

            echo "Code: {$row['code']}\n";
            echo "User Num: {$row['user_num']}\n";
            echo "Character ID: {$row['character_idx']}\n";
            echo "Character Name: {$row['character_name']}\n";
            echo "Items: {$row['items']}\n";
            echo "Quantity: {$row['quantity']}\n";
            echo "Channel: {$channelText}\n";
            echo "Status: " . ($row['status'] == '1' ? 'Used (1)' : 'Not Used (0)') . "\n";

            // แปลง datetime สำหรับการแสดงผล
            $createdDate = is_object($row['datecreated']) && method_exists($row['datecreated'], 'format')
                         ? $row['datecreated']->format('Y-m-d H:i:s')
                         : $row['datecreated'];
            echo "Created: " . $createdDate . "\n";

            if ($row['expiry_date']) {
                $expiryDate = is_object($row['expiry_date']) && method_exists($row['expiry_date'], 'format')
                            ? $row['expiry_date']->format('Y-m-d H:i:s')
                            : $row['expiry_date'];
                echo "Expires: " . $expiryDate . "\n";
            }
            echo "---\n";
        }
    }
}

function listCharacterUsageLogs($conn) {
    try {
        // สร้าง table ถ้าไม่มี (ใช้โครงสร้างที่ถูกต้อง)
        $createSql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_CodesUsed_Character' AND xtype='U')
        CREATE TABLE [dbo].[WEB_Redeem_CodesUsed_Character] (
            [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            [CodeID] int NOT NULL,
            [CustomerID] int NULL,
            [code] nvarchar(20) COLLATE Thai_CI_AS NULL,
            [character_id] int NULL,
            [channel_idx] int NULL,
            [used_date] datetime DEFAULT getdate() NULL
        )";
        $createStmt = sqlsrv_query($conn, $createSql);

        if (!$createStmt) {
            $errors = sqlsrv_errors();
            error_log("Error creating WEB_Redeem_CodesUsed_Character table: " . print_r($errors, true));
        }

        // ลองดึงข้อมูลจากตารางที่มีอยู่
        $sql = "SELECT TOP 100 * FROM WEB_Redeem_CodesUsed_Character ORDER BY used_date DESC";
        $stmt = sqlsrv_query($conn, $sql);

        $logs = [];
        if ($stmt) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                // แปลง DateTime objects เป็น string
                if (isset($row['used_date']) && $row['used_date'] instanceof DateTime) {
                    $row['used_date'] = $row['used_date']->format('Y-m-d H:i:s');
                }
                $logs[] = $row;
            }

            echo json_encode([
                'success' => true,
                'logs' => $logs,
                'count' => count($logs),
                'message' => 'ดึงข้อมูล character usage logs สำเร็จ'
            ]);
        } else {
            // หากไม่สามารถดึงข้อมูลได้ ให้ log error
            $errors = sqlsrv_errors();
            error_log("Error fetching character usage logs: " . print_r($errors, true));

            echo json_encode([
                'success' => false,
                'logs' => [],
                'count' => 0,
                'message' => 'ไม่สามารถดึงข้อมูล character usage logs ได้',
                'error_details' => $errors
            ]);
        }

    } catch (Exception $e) {
        error_log("Exception in listCharacterUsageLogs: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'logs' => [],
            'count' => 0,
            'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
        ]);
    }
}

function deleteCharacterUsageLog($conn) {
    $id = intval($_POST['id'] ?? 0);

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    $sql = "DELETE FROM WEB_Redeem_CodesUsed_Character WHERE id = ?";
    $stmt = sqlsrv_query($conn, $sql, [$id]);

    if ($stmt) {
        echo json_encode(['success' => true, 'message' => 'ลบ Character Log การใช้งานสำเร็จ']);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}

function deleteAllCharacterUsageLogs($conn) {
    // ตรวจสอบจำนวน logs ที่จะลบ
    $countSql = "SELECT COUNT(*) as count FROM WEB_Redeem_CodesUsed_Character";
    $countStmt = sqlsrv_query($conn, $countSql);

    if (!$countStmt) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
        return;
    }

    $countResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
    $logCount = $countResult['count'];

    if ($logCount == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่มี Character Log การใช้งานให้ลบ']);
        return;
    }

    // ลบ logs ทั้งหมด
    $deleteSql = "DELETE FROM WEB_Redeem_CodesUsed_Character";
    $deleteStmt = sqlsrv_query($conn, $deleteSql);

    if ($deleteStmt) {
        echo json_encode([
            'success' => true,
            'message' => "ลบ Character Log การใช้งานทั้งหมดสำเร็จ",
            'deleted_count' => $logCount
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}
?>