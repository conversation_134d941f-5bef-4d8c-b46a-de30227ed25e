<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
?>

<header class="page-header">
    <h2>รวมข้อมูลผู้เล่น</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="home.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>server-admin</span></li>
            <li><span>general-statistics</span></li>
        </ol>
        <a class="sidebar-right-toggle"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
					<div class="row">
						<div class="col-md-6 col-lg-12 col-xl-6">
							<section class="panel">
								<div class="panel-body">
									<div class="row">
										<div class="col-lg-8">
											<div class="chart-data-selector" id="salesSelectorWrapper">
												<h2>
													สถิติ:
													<strong>
														<select class="form-control" id="salesSelector">
															<option value="Porto user" selected>user</option>
															<option value="Porto char" >char</option>
															<option value="Porto Wordpress" >Porto Wordpress</option>
														</select>
													</strong>
												</h2>
					
												<div id="salesSelectorItems" class="chart-data-selector-items mt-sm">
													<!-- Flot: Sales Porto Admin -->
													<div class="chart chart-sm" data-sales-rel="Porto user" id="flotDashSales1" class="chart-active"></div>
													<script>
					
														var flotDashSales1Data = [{
														    data: [
														        ["Jan", 140],
														        ["Feb", 240],
														        ["Mar", 190],
														        ["Apr", 140],
														        ["May", 180],
														        ["Jun", 320],
														        ["Jul", 270],
														        ["Aug", 180]
														    ],
														    color: "#0088cc"
														}];
					
														// See: assets/javascripts/dashboard/examples.dashboard.js for more settings.
					
													</script>
					
													<!-- Flot: Sales Porto Drupal -->
													<div class="chart chart-sm" data-sales-rel="Porto char" id="flotDashSales2" class="chart-hidden"></div>
													<script>
					
														var flotDashSales2Data = [{
														    data: [
														        ["Jan", 240],
														        ["Feb", 240],
														        ["Mar", 290],
														        ["Apr", 540],
														        ["May", 480],
														        ["Jun", 220],
														        ["Jul", 170],
														        ["Aug", 190]
														    ],
														    color: "#2baab1"
														}];
					
														// See: assets/javascripts/dashboard/examples.dashboard.js for more settings.
					
													</script>
					
													<!-- Flot: Sales Porto Wordpress -->
													<div class="chart chart-sm" data-sales-rel="Porto Wordpress" id="flotDashSales3" class="chart-hidden"></div>
													<script>
					
														var flotDashSales3Data = [{
														    data: [
														        ["Jan", 840],
														        ["Feb", 740],
														        ["Mar", 690],
														        ["Apr", 940],
														        ["May", 1180],
														        ["Jun", 820],
														        ["Jul", 570],
														        ["Aug", 780]
														    ],
														    color: "#734ba9"
														}];
					
														// See: assets/javascripts/dashboard/examples.dashboard.js for more settings.
					
													</script>
												</div>
					
											</div>
										</div>
										<div class="col-lg-4 text-center">
											<h2 class="panel-title mt-md">Sales Goal</h2>
											<div class="liquid-meter-wrapper liquid-meter-sm mt-lg">
												<div class="liquid-meter">
													<meter min="0" max="100" value="35" id="meterSales"></meter>
												</div>
												<div class="liquid-meter-selector" id="meterSalesSel">
													<a href="javascript:void(0)" data-val="35" class="active">Monthly Goal</a>
													<a href="javascript:void(0)" data-val="28">Annual Goal</a>
												</div>
											</div>
										</div>
									</div>
								</div>
							</section>
						</div>
						<div class="col-md-6 col-lg-12 col-xl-6">
							<div class="row">
							<div class="col-md-12 col-lg-6 col-xl-6">
									<section class="panel panel-featured-left panel-featured-quaternary">
										<div class="panel-body">
											<div class="widget-summary">
												<div class="widget-summary-col widget-summary-col-icon">
													<div class="summary-icon bg-quaternary">
														<i class="fa fa-user"></i>
													</div>
												</div>
												<div class="widget-summary-col">
													<div class="summary">
														<h4 class="title">บัญชีผู้เล่นทั้งหมด </h4>
														<div class="info">
															<strong class="amount"><?php echo $countplayer = $userLogin->countuser($conn) ? $userLogin->countuser($conn) : '0'; ?> บัญชี</strong>
														</div>
													</div>
													<div class="summary-footer">
														<a class="text-muted text-uppercase">(ทั้งหมด)</a>
													</div>
												</div>
											</div>
										</div>
									</section>
								</div>					
								<div class="col-md-12 col-lg-6 col-xl-6">
									<section class="panel panel-featured-left panel-featured-primary">
										<div class="panel-body">
											<div class="widget-summary">
												<div class="widget-summary-col widget-summary-col-icon">
													<div class="summary-icon bg-primary">
														<i class="fa fa-life-ring"></i>
													</div>
												</div>
												<div class="widget-summary-col">
													<div class="summary">
														<h4 class="title">ตัวละครผู้เล่นทั้งหมด</h4>
														<div class="info">
															<strong class="amount"><?php echo $countplayer = $userLogin->countcharecter($conn) ? $userLogin->countcharecter($conn) : '0'; ?> ตัว</strong>
															<span class="text-primary">(14 unread)</span>
														</div>
													</div>
													<div class="summary-footer">
														<a class="text-muted text-uppercase">(ทั้งหมด)</a>
													</div>
												</div>
											</div>
										</div>
									</section>
								</div>
								
							</div>
							<div class="row">
								<div class="col-md-12 col-lg-6 col-xl-6">
									<section class="panel panel-featured-left panel-featured-tertiary">
										<div class="panel-body">
											<div class="widget-summary">
												<div class="widget-summary-col widget-summary-col-icon">
													<div class="summary-icon bg-tertiary">
														<i class="fa fa-shopping-cart"></i>
													</div>
												</div>
												<div class="widget-summary-col">
													<div class="summary">
														<h4 class="title">Total Online</h4>
														<div class="info">
															<strong class="amount">20</strong>
														</div>
													</div>
													<div class="summary-footer">
														<a class="text-muted text-uppercase">(statement)</a>
													</div>
												</div>
											</div>
										</div>
									</section>
								</div>
								<div class="col-md-12 col-lg-6 col-xl-6">
									<section class="panel panel-featured-left panel-featured-secondary">
										<div class="panel-body">
											<div class="widget-summary">
												<div class="widget-summary-col widget-summary-col-icon">
													<div class="summary-icon bg-secondary">
														<i class="fa fa-usd"></i>
													</div>
												</div>
												<div class="widget-summary-col">
													<div class="summary">
														<h4 class="title">ตัวละครผู้เล่นทั้งหมด</h4>
														<div class="info">
															<strong class="amount">fgdfgdfg</strong>
														</div>
													</div>
													<div class="summary-footer">
														<a class="text-muted text-uppercase">(withdraw)</a>
													</div>
												</div>
											</div>
										</div>
									</section>
								</div>
							</div>
						</div>
					</div>
					
					<div class="row">
						<div class="col-xl-3 col-lg-6">
							<section class="panel panel-transparent">
								<header class="panel-heading">
									<div class="panel-actions">
										<a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
										<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
									</div>
					
									<h2 class="panel-title">My Stats</h2>
								</header>
								<div class="panel-body">
									<section class="panel">
										<div class="panel-body">
											<div class="small-chart pull-right" id="sparklineBarDash"></div>
											<script type="text/javascript">
												var sparklineBarDashData = [5, 6, 7, 2, 0, 4 , 2, 4, 2, 0, 4 , 2, 4, 2, 0, 4];
											</script>
											<div class="h4 text-weight-bold mb-none">488</div>
											<p class="text-xs text-muted mb-none">Average Profile Visits</p>
										</div>
									</section>
									<section class="panel">
										<div class="panel-body">
											<div class="circular-bar circular-bar-xs m-none mt-xs mr-md pull-right">
												<div class="circular-bar-chart" data-percent="45" data-plugin-options='{ "barColor": "#2baab1", "delay": 300, "size": 50, "lineWidth": 4 }'>
													<strong>Average</strong>
													<label><span class="percent">45</span>%</label>
												</div>
											</div>
											<div class="h4 text-weight-bold mb-none">12</div>
											<p class="text-xs text-muted mb-none">Working Projects</p>
										</div>
									</section>
									<section class="panel">
										<div class="panel-body">
											<div class="small-chart pull-right" id="sparklineLineDash"></div>
											<script type="text/javascript">
												var sparklineLineDashData = [15, 16, 17, 19, 10, 15, 13, 12, 12, 14, 16, 17];
											</script>
											<div class="h4 text-weight-bold mb-none">89</div>
											<p class="text-xs text-muted mb-none">Pending Tasks</p>
										</div>
									</section>
								</div>
							</section>
							<section class="panel">
								<header class="panel-heading">
									<div class="panel-actions">
										<a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
										<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
									</div>
					
									<h2 class="panel-title">
										<span class="label label-primary label-sm text-weight-normal va-middle mr-sm">198</span>
										<span class="va-middle">Friends</span>
									</h2>
								</header>
								<div class="panel-body">
									<div class="content">
										<ul class="simple-user-list">
											<li>
												<figure class="image rounded">
													<img src="assets/images/!sample-user.jpg" alt="Joseph Doe Junior" class="img-circle">
												</figure>
												<span class="title">Joseph Doe Junior</span>
												<span class="message truncate">Lorem ipsum dolor sit.</span>
											</li>
											<li>
												<figure class="image rounded">
													<img src="assets/images/!sample-user.jpg" alt="Joseph Junior" class="img-circle">
												</figure>
												<span class="title">Joseph Junior</span>
												<span class="message truncate">Lorem ipsum dolor sit.</span>
											</li>
											<li>
												<figure class="image rounded">
													<img src="assets/images/!sample-user.jpg" alt="Joe Junior" class="img-circle">
												</figure>
												<span class="title">Joe Junior</span>
												<span class="message truncate">Lorem ipsum dolor sit.</span>
											</li>
										</ul>
										<hr class="dotted short">
										<div class="text-right">
											<a class="text-uppercase text-muted" href="#">(View All)</a>
										</div>
									</div>
								</div>
								<div class="panel-footer">
									<div class="input-group input-search">
										<input type="text" class="form-control" name="q" id="q" placeholder="Search...">
										<span class="input-group-btn">
											<button class="btn btn-default" type="submit"><i class="fa fa-search"></i>
											</button>
										</span>
									</div>
								</div>
							</section>
						</div>
						<div class="col-xl-6 col-lg-12">
							<section class="panel">
								<header class="panel-heading panel-heading-transparent">
									<div class="panel-actions">
										<a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
										<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
									</div>
					
									<h2 class="panel-title">Company Activity</h2>
								</header>
								<div class="panel-body">
									<div class="timeline timeline-simple mt-xlg mb-md">
										<div class="tm-body">
											<div class="tm-title">
												<h3 class="h5 text-uppercase">November 2016</h3>
											</div>
											<ol class="tm-items">
												<li>
													<div class="tm-box">
														<p class="text-muted mb-none">7 months ago.</p>
														<p>
															It's awesome when we find a good solution for our projects, Porto Admin is <span class="text-primary">#awesome</span>
														</p>
													</div>
												</li>
												<li>
													<div class="tm-box">
														<p class="text-muted mb-none">7 months ago.</p>
														<p>
															Checkout! How cool is that!
														</p>
														<div class="thumbnail-gallery">
															<a class="img-thumbnail lightbox" href="assets/images/projects/project-4.jpg" data-plugin-options='{ "type":"image" }'>
																<img class="img-responsive" width="215" src="assets/images/projects/project-4.jpg">
																<span class="zoom">
																	<i class="fa fa-search"></i>
																</span>
															</a>
														</div>
													</div>
												</li>
											</ol>
										</div>
									</div>
								</div>
							</section>
						</div>
						<div class="col-lg-3 col-md-6">
							<section class="panel">
								<header class="panel-heading panel-heading-transparent">
									<div class="panel-actions">
										<a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
										<a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
									</div>
					
									<h2 class="panel-title">Projects Stats</h2>
								</header>
								<div class="panel-body">
									<div class="table-responsive">
										<table class="table table-striped mb-none">
											<thead>
												<tr>
													<th>#</th>
													<th>Project</th>
													<th>Status</th>
													<th>Progress</th>
												</tr>
											</thead>
											<tbody>
												<tr>
													<td>1</td>
													<td>Porto - Responsive HTML5 Template</td>
													<td><span class="label label-success">Success</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 100%;">
																100%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>2</td>
													<td>Porto - Responsive Drupal 7 Theme</td>
													<td><span class="label label-success">Success</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 100%;">
																100%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>3</td>
													<td>Tucson - Responsive HTML5 Template</td>
													<td><span class="label label-warning">Warning</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%;">
																60%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>4</td>
													<td>Tucson - Responsive Business WordPress Theme</td>
													<td><span class="label label-success">Success</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 90%;">
																90%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>5</td>
													<td>Porto - Responsive Admin HTML5 Template</td>
													<td><span class="label label-warning">Warning</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 45%;">
																45%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>6</td>
													<td>Porto - Responsive HTML5 Template</td>
													<td><span class="label label-danger">Danger</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded m-none mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 40%;">
																40%
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<td>7</td>
													<td>Porto - Responsive Drupal 7 Theme</td>
													<td><span class="label label-success">Success</span></td>
													<td>
														<div class="progress progress-sm progress-half-rounded mt-xs light">
															<div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 95%;">
																95%
															</div>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</section>
						</div>
					</div>
					<div class="row">
				
					</div>
					<!-- end: page -->
				</section>
			</div>

			<aside id="sidebar-right" class="sidebar-right">
				<div class="nano">
					<div class="nano-content">
						<a href="#" class="mobile-close visible-xs">
							Collapse <i class="fa fa-chevron-right"></i>
						</a>
			
						<div class="sidebar-right-wrapper">
			
							<div class="sidebar-widget widget-calendar">
								<h6>Upcoming Tasks</h6>
								<div data-plugin-datepicker data-plugin-skin="dark" ></div>
			
								<ul>
									<li>
										<time datetime="2016-04-19T00:00+00:00">04/19/2016</time>
										<span>Company Meeting</span>
									</li>
								</ul>
							</div>
			
							<div class="sidebar-widget widget-friends">
								<h6>Friends</h6>
								<ul>
									<li class="status-online">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-online">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-offline">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
									<li class="status-offline">
										<figure class="profile-picture">
											<img src="assets/images/!sample-user.jpg" alt="Joseph Doe" class="img-circle">
										</figure>
										<div class="profile-info">
											<span class="name">Joseph Doe Junior</span>
											<span class="title">Hey, how are you?</span>
										</div>
									</li>
								</ul>
							</div>
			
						</div>
					</div>
				</div>
			</aside>

    <div class="col-lg-12">

        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                <div class="col-lg-6">
                        <h2>สมัครไอดีล่าสุด<small> 10 ไอดีล่าสุด</small></h2>
                        <table class="table table-striped no-margn id=">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th>สถานะไอดี</th>
                                    <th><?php echo T_EMAIL; ?></th>
                                    <th>IP</th>
                                    <th><?php echo T_REGISTEREDIN; ?></th>
                                    <th><?php echo T_LAST_LOGIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table ORDER BY UserNum DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (@sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><span
                                                class="label
								                <?php echo $label = ($resLastUsers['AuthType'] == '2' ? ' label-danger' : $label = $resLastUsers['AuthType'] == '1' ? ' label-success' : ($resLastUsers['AuthType'] == '0' ? ' label-success' : 'label-default'));?>">
                                                <?php echo $status = ($resLastUsers['AuthType'] == '2' ? 'โดนแบน' : $status = $resLastUsers['AuthType'] == '1' ? 'ปรกติ' : ($resLastUsers['AuthType'] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
                                            </td>
                                            <td><?php echo $resLastUsers['Email']; ?></td>
                                            <td><?php echo $resLastUsers['LastIp']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['createDate'])); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['LoginTime'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                        </div>
                    <div class="col-lg-6">
                        <h2>ไอดีออนไลน์<small> ล่าสุด 10 ไอดี</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th>สถานะไอดี</th>
                                    <th><?php echo T_EMAIL; ?></th>
                                    <th><?php echo T_REGISTEREDIN; ?></th>
                                    <th><?php echo T_LAST_LOGIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table ORDER BY LoginTime,Login DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><span
                                                class="label
								                <?php echo $label = ($resLastUsers['AuthType'] == '2' ? ' label-danger' : $label = $resLastUsers['AuthType'] == '1' ? ' label-success' : ($resLastUsers['AuthType'] == '0' ? ' label-success' : 'label-default'));?>">
                                                <?php echo $status = ($resLastUsers['AuthType'] == '2' ? 'โดนแบน' : $status = $resLastUsers['AuthType'] == '1' ? 'ปรกติ' : ($resLastUsers['AuthType'] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
                                            </td>
                                            <td><?php echo $resLastUsers['Email']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['createDate'])); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['LoginTime'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>

                    <div class="col-lg-6">
                        <h2>ไอดีที่เล่นออนไลน์นานสุด<small> 10 ไอดีล่าสุด</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th>สถานะไอดี</th>
                                    <th><?php echo T_EMAIL; ?></th>
                                    <th>IP</th>
                                    <th>PlayTime</th>
                                    <th><?php echo T_LAST_LOGIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table ORDER BY PlayTime DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><span
                                                class="label
								                <?php echo $label = ($resLastUsers['AuthType'] == '2' ? ' label-danger' : $label = $resLastUsers['AuthType'] == '1' ? ' label-success' : ($resLastUsers['AuthType'] == '0' ? ' label-success' : 'label-default'));?>">
                                                <?php echo $status = ($resLastUsers['AuthType'] == '2' ? 'โดนแบน' : $status = $resLastUsers['AuthType'] == '1' ? 'ปรกติ' : ($resLastUsers['AuthType'] == '0' ? 'รอการยืนยันระบบ' : 'Unknow status'));?></span>
                                            </td>
                                            <td><?php echo $resLastUsers['Email']; ?></td>
                                            <td><?php echo $resLastUsers['LastIp']; ?></td>
                                            <td><?php echo $resLastUsers['PlayTime']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resLastUsers['LoginTime'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                        </div>
                    <div class="col-lg-6">
                        <h2>Total Cash<small> ล่าสุด 10 ไอดี</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CUSTOMERID; ?></th>
                                    <th><?php echo T_ID; ?></th>
                                    <th>Cash</th>
                                    <th>BonusCash</th>
                                    <th>CashTotal</th>
                                    <th>Reward</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                                $selectLastUsers = "SELECT TOP 10 * FROM [". DATABASE_CCA ."].[dbo].CashAccount ORDER BY Cash DESC";
                                $selectLastUsersParam = array();
                                $selectLastUsersOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                                if (sqlsrv_num_rows($selectLastUsersQuery)) {
                                    while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resLastUsers['UserNum']; ?></td>
                                            <td><?php echo $resLastUsers['ID']; ?></td>
                                            <td><?php echo number_format($resLastUsers['Cash']); ?></td>
                                            <td><?php echo number_format($resLastUsers['CashBonus']); ?></td>
                                            <td><?php echo number_format($resLastUsers['CashTotal']); ?></td>
                                            <td><?php echo number_format($resLastUsers['Reward']); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/players" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>

                    <hr>
                    <div class="col-lg-6">
                    <h2>ตัวละครออนไลน์ล่าสุด<small>10 อันดับ</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th>เลขตัวละคร</th>
                                    <th><?php echo T_YOURNAME; ?></th>
                                    <th><?php echo T_LEVEL; ?></th>
                                    <th><?php echo T_GAMEMAP; ?></th>
                                    <th>Channel</th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY LoginTime DESC";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                            <td><?php echo $resChars['LEV']?></td>
                                            <td><?php echo $resChars['WorldIdx']; ?></td>
                                            <td><?php echo $resChars['ChannelIdx']; ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resChars['CreateDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/chars" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                
                    <div class="col-lg-6">
                        <h2>ตัวละครมี Alz เยอะสุด<small>10 อันดับ</small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th>เลขตัวละคร</th>
                                    <th><?php echo T_YOURNAME; ?></th>
                                    <th><?php echo T_LEVEL; ?></th>
                                    <th><?php echo T_GAMEMAP; ?></th>
                                    <th>ALZ</th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                                $selectChars = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].cabal_character_table ORDER BY Alz desc";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                            <td><?php echo $resChars['LEV']?></td>
                                            <td><?php echo $resChars['WorldIdx']; ?></td>
                                            <td><?php echo number_format($resChars['Alz']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resChars['CreateDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/chars" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>

 
                    <div class="col-lg-6">
                        <h2>ตัวละครที่อยู่ใน War ประเทศคาเปล่า<small>10 อันดับ</small></h2>
                        <table class="table table-striped no-margn" id="datatable-default-capalla">
                            <thead>
                                <tr>
                                    <th>เลขตัวละคร</th>
                                    <th><?php echo T_YOURNAME; ?></th>
                                    <th><?php echo T_LEVEL; ?></th>
                                    <th>เวลาออนไลน์</th>
                                    <th><?php echo T_GAMEMAP; ?></th>
                                    <th>ALZ</th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "SELECT TOP 60 * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 1 AND Channelidx = 16 AND Nation = 1 ORDER BY LoginTime DESC";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                            <td><?php echo $resChars['LEV']?></td>
                                            <td><?php echo $resChars['PlayTime'] ?></td>
                                            <td><?php echo $resChars['WorldIdx']; ?></td>
                                            <td><?php echo number_format($resChars['Alz']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resChars['CreateDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/chars" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                    <div class="col-lg-6">
                        <h2>ตัวละครที่อยู่ใน War ประเทศโพยอน<small>10 อันดับ</small></h2>
                        <table class="table table-striped no-margn" id="datatable-default-pocyon">
                            <thead>
                                <tr>
                                    <th>เลขตัวละคร</th>
                                    <th><?php echo T_YOURNAME; ?></th>
                                    <th><?php echo T_LEVEL; ?></th>
                                    <th>เวลาออนไลน์</th>
                                    <th><?php echo T_GAMEMAP; ?></th>
                                    <th>ALZ</th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectChars = "SELECT TOP 60 * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE Login = 1 AND Channelidx = 16 AND Nation = 2 ORDER BY LoginTime DESC";
                                $selectCharsParam = array();
                                $selectCharsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectCharsQuery = sqlsrv_query($conn, $selectChars, $selectCharsParam, $selectCharsOpt);
                                if (sqlsrv_num_rows($selectCharsQuery)) {
                                    while ($resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                      
                                        ?>
                                        <tr>
                                            <td><?php echo $resChars['CharacterIdx']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                            <td><?php echo $resChars['LEV']?></td>
                                            <td><?php echo $resChars['PlayTime']?></td>
                                            <td><?php echo $resChars['WorldIdx']; ?></td>
                                            <td><?php echo number_format($resChars['Alz']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($resChars['CreateDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/chars" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>


                </div>
                <hr>
                <div class="row">
                    <div class="col-lg-12">
                        <h2><?php echo T_LAST_CLANS; ?> <small><?php echo H_MAX_5_RESULTS; ?></small></h2>
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_CLANID; ?></th>
                                    <th><?php echo T_CLAN_NAME; ?></th>
                                    <th><?php echo T_CLAN_TAG; ?></th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectClans = "SELECT TOP 10 * FROM [".DATABASE_SV."].[dbo].Guild ORDER BY Level DESC";
                                $selectClansParam = array();
                                $selectClansOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectClansQuery = sqlsrv_query($conn, $selectClans, $selectClansParam, $selectClansOpt);

                                if (sqlsrv_num_rows($selectClansQuery)) {
                                    while ($resClans = sqlsrv_fetch_array($selectClansQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo $resClans['GuildNo']; ?></td>
                                            <td><?php echo $userLogin->thaitrans($resClans['GuildName']); ?></td>
                                            <td><?php echo $resClans['Level']; ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($resClans['RegDate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=manager/clans" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <h2 class="panel-heading clean">
                <?php echo T_HELPDESK; ?> <small><?php echo T_HELPDESK_DESC; ?> (<?php echo H_MAX_5_RESULTS; ?>) <span class="text-red">(<?php echo T_ONLYWAITING; ?>)</span></small> 
            </h2>
            <div class="panel-body">
                <div class="row">
                    <div class="col-lg-12">
                        <table class="table table-striped no-margn">
                            <thead>
                                <tr>
                                    <th><?php echo T_TICKET_TITLE; ?></th>
                                    <th><?php echo T_TICKET_RESUME; ?></th>
                                    <th><?php echo T_TICKET_STATUS; ?></th>
                                    <th><?php echo T_CREATEDIN; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $selectTickets = "SELECT TOP 5 * FROM WEB_H_Tickets WHERE status = '1' ORDER BY id DESC";
                                $selectTicketsParam = array();
                                $selectTicketsOpt =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
                                $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, $selectTicketsParam, $selectTicketsOpt);

                                if (sqlsrv_num_rows($selectTicketsQuery)) {
                                    while ($resTickets = sqlsrv_fetch_array($selectTicketsQuery, SQLSRV_FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo substr($resTickets['title'], 0, 15); ?></td>
                                            <td><?php echo $content = (strlen($resTickets['content']) > 15 ? substr($resTickets['title'], 0, 20) . ' ...' : $resTickets['content']); ?></td>
                                            <td><?php echo $status = ($resTickets['status'] == '1' ? T_WAITING : ($resTickets['status'] == '2' ? T_REPLIED : ($resTickets['status'] == '3' ? T_CLOSED : ($resTickets['status'] == '4' ? T_SOLVED : T_UNKNOWNSTATUS)))); ?></td>
                                            <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resTickets['createdate'])); ?></td>
                                        </tr>
                                        <?php
                                    }
                                } else {
                                    echo W_NOTHING_RETURNED;
                                }
                                ?>
                            </tbody>
                        </table>
                        <a href="?url=helpdesk/a/tickets" class="btn btn-block btn-success" style="margin: 5px 0;"><?php echo T_SEE_ALL; ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>