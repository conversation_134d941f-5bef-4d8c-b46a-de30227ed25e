<?php
ob_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');
header('Content-Type: application/json');

file_put_contents('log.txt', date('Y-m-d H:i:s') . " GET: {$_GET['action']}\n", FILE_APPEND);
function thaitrans($string){
    $name2 =  mb_convert_encoding($string, 'UTF-16', 'UTF-8');
    $name3 =  iconv('windows-874', 'UTF-8',$name2);
    return $name3;
} 

function safeDate($date) {
    if (empty($date) || $date === '0000-00-00') {
        return null; // หรือ '1900-01-01' ถ้าต้องการกำหนดวันเริ่มต้น
    }
    // ตรวจสอบรูปแบบเบื้องต้น (YYYY-MM-DD)
    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        return $date;
    }
    return null;
}

function cleanTime($time) {
    if (empty($time) || $time === '00:00' || $time === '00:00:00') {
        return '00:00:00';
    }
    // แปลงเวลาให้เป็น HH:mm:ss
    if (preg_match('/^\d{2}:\d{2}$/', $time)) {
        return $time . ':00';
    }
    if (preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
        return $time;
    }
    return '00:00:00';
}

function logError($message, $data = []) {
    $logFile = __DIR__ . '/cashshop_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    if (!empty($data)) {
        $logMessage .= "Data: " . print_r($data, true) . "\n";
    }
    $logMessage .= "----------------------------------------\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

class CashShopManager {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }

public function addItem($data) {
    $logFile = __DIR__ . '/debug_additem.log';
    try {
         file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] addItem called\n" . print_r($data, true), FILE_APPEND);

        // Check duplicate SerialNum
        if (!empty($data['SerialNum']) && $data['SerialNum'] !== '0') {
            $sqlCheck = "SELECT COUNT(*) AS cnt FROM Server01.dbo.cabal_cashService_cashShop_table WHERE SerialNum = ?";
            $paramsCheck = [[$data['SerialNum'], SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR]];
            $stmtCheck = sqlsrv_prepare($this->conn, $sqlCheck, $paramsCheck);
            if (!$stmtCheck || !sqlsrv_execute($stmtCheck)) {
                $error = print_r(sqlsrv_errors(), true);
                file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Failed to check SerialNum: $error\n", FILE_APPEND);
                throw new Exception("Failed to check SerialNum: " . print_r(sqlsrv_errors(), true));
            }
            $row = sqlsrv_fetch_array($stmtCheck, SQLSRV_FETCH_ASSOC);
            if ($row['cnt'] > 0) {
                $msg = "SerialNum already exists: " . $data['SerialNum'];
                file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] $msg\n", FILE_APPEND);

                throw new Exception("SerialNum already exists: " . $data['SerialNum']);
            }
        } else {
            // Generate new SerialNum
            $data['SerialNum'] = (string)(time() * 1000000 + rand(1000, 9999));
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Generated new SerialNum: {$data['SerialNum']}\n", FILE_APPEND);

        }

        // Check duplicate SlotID in PoolID
        $sqlSlotCheck = "SELECT COUNT(*) AS cnt FROM Server01.dbo.cabal_cashService_cashShop_table WHERE PoolID = ? AND SlotID = ?";
        $paramsSlotCheck = [
            [(int)($data['PoolID'] ?? 0)],
            [(int)($data['SlotID'] ?? 0)]
        ];
        $stmtSlotCheck = sqlsrv_prepare($this->conn, $sqlSlotCheck, $paramsSlotCheck);
        if (!$stmtSlotCheck || !sqlsrv_execute($stmtSlotCheck)) {
            $error = print_r(sqlsrv_errors(), true);
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Failed to check SlotID: $error\n", FILE_APPEND);

            throw new Exception("Failed to check SlotID: " . print_r(sqlsrv_errors(), true));
        }
        $slotRow = sqlsrv_fetch_array($stmtSlotCheck, SQLSRV_FETCH_ASSOC);
        if ($slotRow['cnt'] > 0) {

            $msg = "SlotID {$data['SlotID']} in PoolID {$data['PoolID']} is already occupied";
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] $msg\n", FILE_APPEND);

            throw new Exception("SlotID {$data['SlotID']} in PoolID {$data['PoolID']} is already occupied");
        }

        // Prepare SQL insert
    $sql = "INSERT INTO [Server01].[dbo].[cabal_cashService_cashShop_table] (
            SerialNum, PoolID, TabID, SlotID, ItemKind, ItemOption, ItemPeriod, ItemName, Marker,
            DescriptionID, PeriodItemID, Cash, ItemGroup, Forcegem, ProductID, DiscountRate,
            LevelMin, LevelMax, HonorMin, HonorMax, IsPremium, IsWinIWarNation, SaleDateBegin,
            SaleDateEnd, DailySaleTimeBegin, DailySaleTimeEnd, SaleCountServer, SaleCountDaily,
            SaleCountUser, ResetCount, ResetCost, PackageItemID, SaleTimeBegin, SaleTimeEnd,
            TradeCount, Alz, SaleCountCharacter, IsDailyLimitResetUser, IsDailyLimitResetCharacter
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

       // เตรียมพารามิเตอร์แบบ string ทั้งหมด ตามตัวอย่าง
        $params = [
            [$data['SerialNum'], SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['PoolID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['TabID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SlotID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ItemKind'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ItemOption'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ItemPeriod'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ItemName'] ?? '', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['Marker'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['DescriptionID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['PeriodItemID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['Cash'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ItemGroup'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['Forcegem'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ProductID'] ?? '', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['DiscountRate'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['LevelMin'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['LevelMax'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['HonorMin'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['HonorMax'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['IsPremium'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['IsWinIWarNation'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleDateBegin'] ?? '0000-00-00', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleDateEnd'] ?? '0000-00-00', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['DailySaleTimeBegin'] ?? '00:00', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['DailySaleTimeEnd'] ?? '00:00', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleCountServer'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleCountDaily'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleCountUser'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ResetCount'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['ResetCost'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['PackageItemID'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleTimeBegin'] ?? '00:00:00', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleTimeEnd'] ?? '23:59:59', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['TradeCount'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['Alz'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['SaleCountCharacter'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['IsDailyLimitResetUser'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
            [$data['IsDailyLimitResetCharacter'] ?? '0', SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR],
        ];


        file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Prepared SQL params:\n" . print_r($params, true), FILE_APPEND);

        $stmt = sqlsrv_prepare($this->conn, $sql, $params);
        if (!$stmt || !sqlsrv_execute($stmt)) {
            $error = print_r(sqlsrv_errors(), true);
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Failed to execute statement: $error\n", FILE_APPEND);

            throw new Exception("Failed to execute statement: " . print_r(sqlsrv_errors(), true));
        }

        
        file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Item added successfully with SerialNum: {$data['SerialNum']}\n", FILE_APPEND);
        return true;
    } catch (Exception $e) {
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] Error adding item: " . $e->getMessage() . "\nData:\n" . print_r($data, true) . "\n", FILE_APPEND);
        return false;
    }
}

                    
        public function updateItem($serialNum, $data) {
                    try {
                        $sql = "UPDATE Server01.dbo.cabal_cashService_cashShop_table SET 
                            PoolID = ?, TabID = ?, SlotID = ?, ItemKind = ?, ItemOption = ?, 
                            ItemPeriod = ?, ItemName = ?, Marker = ?, DescriptionID = ?, 
                            PeriodItemID = ?, Cash = ?, ItemGroup = ?, Forcegem = ?, ProductID = ?, 
                            DiscountRate = ?, LevelMin = ?, LevelMax = ?, HonorMin = ?, HonorMax = ?, 
                            IsPremium = ?, IsWinIWarNation = ?, SaleCountServer = ?, 
                            SaleCountDaily = ?, SaleCountUser = ?, ResetCount = ?, ResetCost = ?, 
                            PackageItemID = ?, TradeCount = ?, 
                            Alz = ?, SaleCountCharacter = ?, IsDailyLimitResetUser = ?, 
                            IsDailyLimitResetCharacter = ?
                            WHERE SerialNum = ?";
                            
                            $params = [
                            $data['PoolID'] ?? 0,
                            $data['TabID'] ?? 0,
                            $data['SlotID'] ?? 0,
                            $data['ItemKind'] ?? 0,
                            $data['ItemOption'] ?? 0,
                            $data['ItemPeriod'] ?? 0,
                            $data['ItemName'] ?? '',
                            $data['Marker'] ?? 0,
                            $data['DescriptionID'] ?? 0,
                            $data['PeriodItemID'] ?? 0,
                            $data['Cash'] ?? 0,
                            $data['ItemGroup'] ?? 0,
                            $data['Forcegem'] ?? 0,
                            $data['ProductID'] ?? '',
                            $data['DiscountRate'] ?? 0,
                            $data['LevelMin'] ?? 0,
                            $data['LevelMax'] ?? 0,
                            $data['HonorMin'] ?? 0,
                            $data['HonorMax'] ?? 0,
                            $data['IsPremium'] ?? 0,
                            $data['IsWinIWarNation'] ?? 0,
                            $data['SaleCountServer'] ?? 0,
                            $data['SaleCountDaily'] ?? 0,
                            $data['SaleCountUser'] ?? 0,
                            $data['ResetCount'] ?? 0,
                            $data['ResetCost'] ?? 0,
                            $data['PackageItemID'] ?? 0,
                            $data['TradeCount'] ?? 0,
                            $data['Alz'] ?? 0,
                            $data['SaleCountCharacter'] ?? 0,
                            $data['IsDailyLimitResetUser'] ?? 0,
                            $data['IsDailyLimitResetCharacter'] ?? 0,
                            $serialNum
                        ];

                        
                        $stmt = sqlsrv_prepare($this->conn, $sql, $params);
                        if (!$stmt) {
                            throw new Exception("Failed to prepare statement: " . print_r(sqlsrv_errors(), true));
                        }
                        $result = sqlsrv_execute($stmt);
                        if (!$result) {
                            throw new Exception("Failed to execute statement: " . print_r(sqlsrv_errors(), true));
                        }
                        logError("Item updated successfully", ['SerialNum' => $serialNum]);
                        return true;
                    } catch (Exception $e) {
                        logError("Error updating item", ['error' => $e->getMessage(), 'serialNum' => $serialNum, 'data' => $data]);
                        return false;
                    }
                }

                    
                    public function deleteItem($serialNum, $poolID, $tabID, $slotID) {
                        if (!$serialNum || $poolID === null || $tabID === null || $slotID === null) {
                            return false;
                        }

                        $sql = "DELETE FROM Server01.dbo.cabal_cashService_cashShop_table
                                WHERE SerialNum = ? AND PoolID = ? AND TabID = ? AND SlotID = ?";
                        $params = [
                            [$serialNum, SQLSRV_PARAM_IN],
                            [$poolID, SQLSRV_PARAM_IN],
                            [$tabID, SQLSRV_PARAM_IN],
                            [$slotID, SQLSRV_PARAM_IN],
                        ];

                        $stmt = sqlsrv_prepare($this->conn, $sql, $params);
                        if (!$stmt || !sqlsrv_execute($stmt)) {
                            return false;
                        }

                        return true;
                    }
                    public function getItem($serialNum) {
                        try {
                            if (empty($serialNum)) {
                                throw new Exception("Invalid SerialNum: $serialNum");
                            }
                            $sql = "SELECT SerialNum, PoolID, TabID, SlotID, ItemKind, ItemOption, ItemPeriod, ItemName, 
                                        Marker, DescriptionID, PeriodItemID, Cash, ItemGroup, Forcegem, ProductID, 
                                        DiscountRate, LevelMin, LevelMax, HonorMin, HonorMax, IsPremium, 
                                        IsWinIWarNation, SaleDateBegin, SaleDateEnd, DailySaleTimeBegin, 
                                        DailySaleTimeEnd, SaleCountServer, SaleCountDaily, SaleCountUser, 
                                        ResetCount, ResetCost, PackageItemID, SaleTimeBegin, SaleTimeEnd, 
                                        TradeCount, Alz, SaleCountCharacter, IsDailyLimitResetUser, 
                                        IsDailyLimitResetCharacter 
                                    FROM Server01.dbo.cabal_cashService_cashShop_table WHERE SerialNum = ?";
                            $params = [
                                [$serialNum, SQLSRV_PARAM_IN, null, SQLSRV_SQLTYPE_NVARCHAR]
                            ];
                            $stmt = sqlsrv_prepare($this->conn, $sql, $params);
                            if (!$stmt) {
                                throw new Exception("Failed to prepare statement: " . print_r(sqlsrv_errors(), true));
                            }
                            if (!sqlsrv_execute($stmt)) {
                                throw new Exception("Failed to execute statement: " . print_r(sqlsrv_errors(), true));
                            }
                            $result = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
                            if (!$result) {
                                throw new Exception("Item not found for SerialNum: $serialNum");
                            }
                            // Convert null or invalid dates/times to empty strings
                            $result['SaleDateBegin'] = $result['SaleDateBegin'] && $result['SaleDateBegin'] !== '0000-00-00' ? $result['SaleDateBegin'] : '';
                            $result['SaleDateEnd'] = $result['SaleDateEnd'] && $result['SaleDateEnd'] !== '0000-00-00' ? $result['SaleDateEnd'] : '';
                            $result['DailySaleTimeBegin'] = $result['DailySaleTimeBegin'] && $result['DailySaleTimeBegin'] !== '00:00' ? $result['DailySaleTimeBegin'] : '';
                            $result['DailySaleTimeEnd'] = $result['DailySaleTimeEnd'] && $result['DailySaleTimeEnd'] !== '00:00' ? $result['DailySaleTimeEnd'] : '';
                            return $result;
                        } catch (Exception $e) {
                            logError("Error fetching item", ['error' => $e->getMessage(), 'serialNum' => $serialNum]);
                            return ['success' => false, 'error' => $e->getMessage()];
                        }
                    }
                    
                    public function getAllItems() {
                        try {
                            $sql = "SELECT SerialNum, PoolID, TabID, SlotID, ItemKind, ItemOption, ItemPeriod, ItemName, 
                                        Marker, DescriptionID, PeriodItemID, Cash, ItemGroup, Forcegem, ProductID, 
                                        DiscountRate, LevelMin, LevelMax, HonorMin, HonorMax, IsPremium, 
                                        IsWinIWarNation, SaleDateBegin, SaleDateEnd, DailySaleTimeBegin, 
                                        DailySaleTimeEnd, SaleCountServer, SaleCountDaily, SaleCountUser, 
                                        ResetCount, ResetCost, PackageItemID, SaleTimeBegin, SaleTimeEnd, 
                                        TradeCount, Alz, SaleCountCharacter, IsDailyLimitResetUser, 
                                        IsDailyLimitResetCharacter 
                                    FROM Server01.dbo.cabal_cashService_cashShop_table";
                            $stmt = sqlsrv_query($this->conn, $sql);
                            if ($stmt === false) {
                                throw new Exception("Query failed: " . print_r(sqlsrv_errors(), true));
                            }
                            $results = [];
                            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                $results[] = $row;
                            }
                            sqlsrv_free_stmt($stmt);
                            return $results;
                        } catch (Exception $e) {
                            logError("Error fetching all items", ['error' => $e->getMessage()]);
                            return [];
                        }
                    }
                    
                function getPoolNames($conn) {
                    $sql = "SELECT PoolID, Msg, Marker FROM Server01.dbo.cabal_cashService_msg_pool_table ORDER BY PoolID";
                    $stmt = sqlsrv_query($conn, $sql);

                    if (!$stmt) {
                        return [
                            'success' => false,
                            'error' => 'Query failed: ' . print_r(sqlsrv_errors(), true)
                        ];
                    }

                    $pools = [];
                    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                        $msgRaw = isset($row['Msg']) ? $row['Msg'] : '';

                        // แปลงข้อความ (เช่น Windows-874 → UTF-8)
                        $msg = function_exists('thaitrans')
                            ? thaitrans($msgRaw)
                            : iconv("windows-874", "utf-8//IGNORE", $msgRaw);

                        $pools[] = [
                            'PoolID' => (int)$row['PoolID'],
                            'Msg' => $msg,
                            'Marker' => (int)$row['Marker']
                        ];
                    }

                    sqlsrv_free_stmt($stmt);
                    return ['success' => true, 'pools' => $pools];
                }

                public function getItemsByPool($poolID) {
                    try {
                        $sql = "SELECT * FROM Server01.dbo.cabal_cashService_cashShop_table WHERE PoolID = ? ORDER BY SlotID";
                        $params = [[(int)$poolID]];
                        $stmt = sqlsrv_prepare($this->conn, $sql, $params);
                        if (!$stmt || !sqlsrv_execute($stmt)) {
                            throw new Exception("Failed to execute statement: " . print_r(sqlsrv_errors(), true));
                        }

                        $items = [];
                        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                            $items[] = $row;
                        }
                        return $items;
                    } catch (Exception $e) {
                        logError("Error getting items by pool", ['error' => $e->getMessage(), 'poolID' => $poolID]);
                        return [];
                    }
                }
                public function savePool($poolID, $msg, $marker) {
                        try {
                            if (!isset($poolID) || trim($msg) === '' || !isset($marker)) {
                                throw new Exception("Invalid PoolID or data");
                            }

                            // ตรวจสอบว่ามี PoolID นี้อยู่ในตารางหรือยัง
                            $checkSql = "SELECT COUNT(*) AS count FROM Server01.dbo.cabal_cashService_msg_pool_table WHERE PoolID = ?";
                            $checkStmt = sqlsrv_query($this->conn, $checkSql, [$poolID]);

                            if (!$checkStmt) {
                                throw new Exception("Check query failed: " . print_r(sqlsrv_errors(), true));
                            }

                            $row = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
                            $exists = $row['count'] > 0;

                            if ($exists) {
                                // มีอยู่แล้ว → UPDATE
                                $updateSql = "UPDATE Server01.dbo.cabal_cashService_msg_pool_table SET Msg = ?, Marker = ? WHERE PoolID = ?";
                                $updateStmt = sqlsrv_query($this->conn, $updateSql, [$msg, $marker, $poolID]);

                                if (!$updateStmt) {
                                    throw new Exception("Update failed: " . print_r(sqlsrv_errors(), true));
                                }
                            } else {
                                // ยังไม่มี → INSERT ใหม่
                                $insertSql = "INSERT INTO Server01.dbo.cabal_cashService_msg_pool_table (PoolID, Msg, Marker) VALUES (?, ?, ?)";
                                $insertStmt = sqlsrv_query($this->conn, $insertSql, [$poolID, $msg, $marker]);

                                if (!$insertStmt) {
                                    throw new Exception("Insert failed: " . print_r(sqlsrv_errors(), true));
                                }
                            }

                            return ['success' => true];

                        } catch (Exception $e) {
                            logError("❌ Error saving pool", [
                                'error' => $e->getMessage(),
                                'PoolID' => $poolID,
                                'Msg' => $msg,
                                'Marker' => $marker
                            ]);

                            return ['success' => false, 'message' => $e->getMessage()];
                        }
                    }

                    
    public function __destruct() {
        if ($this->conn) {
            sqlsrv_close($this->conn);
        }
    }

}

try {
    $conn = db_connect();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    $manager = new CashShopManager($conn);
} catch (Exception $e) {
    logError("Database connection failed", ['error' => $e->getMessage()]);
    ob_end_clean();
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'getAllItems':
            $result = $manager->getAllItems();
            ob_end_clean();
            echo json_encode($result);
            break;
            
        case 'getItem':
            $serialNum = $_POST['SerialNum'] ?? '';
            $item = $manager->getItem($serialNum);
            ob_end_clean();
            echo json_encode($item);
            break;
            
        case 'addItem':
            try {
                $data = $_POST;
                $result = $manager->addItem($data);
                ob_end_clean();
                echo json_encode(['success' => true]);
            } catch (Exception $e) {
                ob_end_clean(); // ล้าง buffer กัน echo หลุด
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage(),
                ]);
            }
            break;
            
        case 'updateItem':
            $serialNum = $_POST['SerialNum'] ?? '';
            $data = $_POST;
            unset($data['SerialNum']);
            $result = $manager->updateItem($serialNum, $data);
            ob_end_clean();
            echo json_encode(['success' => $result]); // ✅ เช่นเดียวกัน
            break;
            
        case 'deleteItem':
            $serialNum = $_POST['SerialNum'] ?? null;
            $poolID = $_POST['PoolID'] ?? null;
            $tabID = $_POST['TabID'] ?? null;
            $slotID = $_POST['SlotID'] ?? null;

            if (!$serialNum || $poolID === null || $tabID === null || $slotID === null) {
                echo json_encode(['success' => false, 'error' => 'missing_parameters']);
                exit;
            }
            $result = $manager->deleteItem($serialNum, $poolID, $tabID, $slotID);
            ob_end_clean();
            echo json_encode(['success' => $result]);
            break;

        case 'getItemsByPool':
            $poolID = $_GET['poolID'] ?? '';
            if (empty($poolID)) {
                throw new Exception("Missing poolID parameter");
            }
            $result = $manager->getItemsByPool($poolID);
            ob_end_clean();
            echo json_encode($result);
            break;
        case 'getPoolNames':
            // ปิด output buffer ถ้ามี (ป้องกัน warning ถ้าไม่มี)
            if (ob_get_level()) {
                ob_end_clean();
            }
            // ส่ง header ให้ชัดเจนว่าเป็น JSON
            header('Content-Type: application/json; charset=utf-8');
            // เรียกใช้งานฟังก์ชันดึงข้อมูล
            $result = $manager->getPoolNames($conn);
            // ตอบกลับ
            echo json_encode($result);
            break;
        case 'savePool':
            header('Content-Type: application/json');
            $input = json_decode(file_get_contents('php://input'), true);

            $poolID = $input['PoolID'] ?? null;
            $msg = $input['Msg'] ?? '';
            $marker = $input['Marker'] ?? 1;

            $result = $manager->savePool($poolID, $msg, $marker);

            ob_end_clean();
            echo json_encode(is_array($result) ? $result : ['success' => (bool)$result, 'message' => 'Unknown server error']);
            break;

        default:
            ob_end_clean();
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    logError("Unhandled exception", ['error' => $e->getMessage()]);
    ob_end_clean();
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}