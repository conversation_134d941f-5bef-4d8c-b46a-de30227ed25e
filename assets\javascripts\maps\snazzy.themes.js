// Map - Snazzy - Theme
window.SnazzyThemes = [{"id":73,"name":"A Dark World","json":"[\r\n {\r\n \"stylers\": [\r\n {\"visibility\": \"simplified\"}\r\n ]\r\n }, {\r\n \"stylers\": [\r\n {\"color\": \"#131314\"}\r\n ]\r\n }, {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n {\"color\": \"#131313\"},\r\n {\"lightness\": 7}\r\n ]\r\n }, {\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\"visibility\": \"on\"},\r\n {\"lightness\": 25}\r\n ]\r\n }\r\n ]"},{"id":42,"name":"Apple Maps-esque","json":"[\r\n\r\n // WATER\r\n\r\n {\r\n featureType: 'water',\r\n elementType: 'geometry',\r\n stylers: [{\r\n color: '#a2daf2'\r\n }]\r\n },\r\n\r\n // LANDSCAPE\r\n\r\n {\r\n featureType: 'landscape.man_made',\r\n elementType: 'geometry',\r\n stylers: [{\r\n color: '#f7f1df'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'landscape.natural',\r\n elementType: 'geometry',\r\n stylers: [{\r\n color: '#d0e3b4'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'landscape.natural.terrain',\r\n elementType: 'geometry',\r\n stylers: [{\r\n visibility: 'off'\r\n }]\r\n },\r\n\r\n // POINTS OF INTEREST\r\n\r\n {\r\n featureType: 'poi.park',\r\n elementType: 'geometry',\r\n stylers: [{\r\n color: '#bde6ab'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'poi',\r\n elementType: 'labels',\r\n stylers: [{\r\n visibility: 'off'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'poi.medical',\r\n elementType: 'geometry',\r\n stylers: [{\r\n color: '#fbd3da'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'poi.business',\r\n stylers: [{\r\n visibility: 'off'\r\n }]\r\n },\r\n\r\n // ROADS\r\n\r\n {\r\n featureType: 'road',\r\n elementType: 'geometry.stroke',\r\n stylers: [{\r\n visibility: 'off'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'road',\r\n elementType: 'labels',\r\n stylers: [{\r\n visibility: 'off'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'road.highway',\r\n elementType: 'geometry.fill',\r\n stylers: [{\r\n color: '#ffe15f'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'road.highway',\r\n elementType: 'geometry.stroke',\r\n stylers: [{\r\n color: '#efd151'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'road.arterial',\r\n elementType: 'geometry.fill',\r\n stylers: [{\r\n color: '#ffffff'\r\n }]\r\n },\r\n\r\n {\r\n featureType: 'road.local',\r\n elementType: 'geometry.fill',\r\n stylers: [{\r\n color: 'black'\r\n }]\r\n },\r\n\r\n // TRANSIT\r\n\r\n {\r\n featureType: 'transit.station.airport',\r\n elementType: 'geometry.fill',\r\n stylers: [{\r\n color: '#cfb2db'\r\n }]\r\n }\r\n\r\n ]"},{"id":68,"name":"Aqua","json":"[\r\n {\r\n \"featureType\":\"landscape\",\r\n \"stylers\":[\r\n {\r\n \"color\":\"#6c8080\"\r\n },\r\n {\r\n \"visibility\":\"simplified\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"administrative\",\r\n \"elementType\":\"labels.text\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"road\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"simplified\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"poi\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"road.highway\",\r\n \"elementType\":\"labels\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"road.highway\",\r\n \"elementType\":\"labels\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"road\",\r\n \"elementType\":\"labels.icon\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"transit\",\r\n \"elementType\":\"labels\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"elementType\":\"labels\",\r\n \"stylers\":[\r\n {\r\n \"visibility\":\"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\":\"road.highway\",\r\n \"stylers\":[\r\n {\r\n \"color\":\"#d98080\"\r\n },\r\n {\r\n \"hue\":\"#eeff00\"\r\n },\r\n {\r\n \"lightness\":100\r\n },\r\n {\r\n \"weight\":1.5\r\n }\r\n ]\r\n }\r\n]"},{"id":35,"name":"Avocado World","json":" [ \r\n\t\t{ \r\n\t\t\tfeatureType: 'water', \r\n\t\t\telementType: 'geometry', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ \"visibility\": \"on\" }, \r\n\t\t\t\t{ \"color\": \"#aee2e0\" } ] \r\n\t\t\t},\r\n\t\t{ \r\n\t\t\tfeatureType: 'landscape', \r\n\t\t\telementType: 'geometry.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#abce83' } ] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'poi', \r\n\t\t\telementType: 'geometry.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#769E72' } ] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'poi', \r\n\t\t\telementType: 'labels.text.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#7B8758' }, \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'poi', \r\n\t\t\telementType: 'labels.text.stroke', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#EBF4A4' }, \r\n\t\t\t] \t\r\n\t\t},{ \r\n\t\t\tfeatureType: 'poi.park', \r\n\t\t\telementType: 'geometry', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ visibility: 'simplified' }, \r\n\t\t\t\t{ color: '#8dab68' } \r\n\t\t\t] \r\n\t\t},{\r\n\t\t\tfeatureType: 'road', \r\n\t\t\telementType: 'geometry.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ visibility: 'simplified' }, \r\n\t\t\t]\r\n\t\t},{\r\n\t\t\tfeatureType: 'road', \r\n\t\t\telementType: 'labels.text.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#5B5B3F' } \r\n\t\t\t]\r\n\t\t},{\r\n\t\t\tfeatureType: 'road', \r\n\t\t\telementType: 'labels.text.stroke', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#ABCE83' } \r\n\t\t\t]\r\n\t\t},{\r\n\t\t\tfeatureType: 'road', \r\n\t\t\telementType: 'labels.icon', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ visibility: 'off' } \r\n\t\t\t]\t\r\n\t\t},{ \r\n\t\t\tfeatureType: 'road.local', \r\n\t\t\telementType: 'geometry', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#A4C67D' }, \r\n\t\t\t]\r\n\t\t},{ \r\n\t\t\tfeatureType: 'road.arterial', \r\n\t\t\telementType: 'geometry', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#9BBF72' } \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'road.highway', \r\n\t\t\telementType: 'geometry', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#EBF4A4' } \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'transit', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ visibility: 'off' } \r\n\t\t\t] \t \r\n\t\t},{ \r\n\t\t\tfeatureType: 'administrative', \r\n\t\t\telementType: 'geometry.stroke', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ visibility: 'on' }, \r\n\t\t\t\t{ color: '#87ae79' } \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'administrative', \r\n\t\t\telementType: 'geometry.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#7f2200' }, \r\n\t\t\t\t{ visibility: 'off' } \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'administrative', \r\n\t\t\telementType: 'labels.text.stroke', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#ffffff' }, \r\n\t\t\t\t{ visibility: 'on' }, \r\n\t\t\t\t{ weight: 4.1 } \r\n\t\t\t] \r\n\t\t},{ \r\n\t\t\tfeatureType: 'administrative', \r\n\t\t\telementType: 'labels.text.fill', \r\n\t\t\tstylers: [ \r\n\t\t\t\t{ color: '#495421' }, \r\n\t\t\t] \t\t\t\r\n\t\t},{ \r\n\t\t\tfeatureType: 'administrative.neighborhood', \r\n\t\t\telementType: 'labels', \r\n\t\t\tstylers: [ { visibility: 'off' } ] \r\n\t\t} \r\n\t]"},{"id":23,"name":"Bates Green","json":" [{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [{\r\n\t\t\thue: '#1CB2BD'\r\n\t\t}, {\r\n\t\t\tsaturation: 53\r\n\t\t}, {\r\n\t\t\tlightness: -44\r\n\t\t}, {\r\n\t\t\tvisibility: 'on'\r\n\t\t}]\r\n\t}, {\r\n\t\tfeatureType: \"road\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [{\r\n\t\t\thue: \"#1CB2BD\"\r\n\t\t}, {\r\n\t\t\tsaturation: 40\r\n\t\t}]\r\n\t}, {\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'all',\r\n\t\tstylers: [{\r\n\t\t\thue: '#BBDC00'\r\n\t\t}, {\r\n\t\t\tsaturation: 80\r\n\t\t}, {\r\n\t\t\tlightness: -20\r\n\t\t}, {\r\n\t\t\tvisibility: 'on'\r\n\t\t}]\r\n\t}, {\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'all',\r\n\t\tstylers: [{\r\n\t\t\tvisibility: 'on'\r\n\t\t}]\r\n\t}]"},{"id":43,"name":"Bentley","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#F1FF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -27.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 9.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#0099FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -20\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 36.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00FF4F\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFB300\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -38\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 11.2\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00B6FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 4.2\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -63.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#9FFF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":79,"name":"Black and White","json":"[ { \"featureType\": \"road\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"featureType\": \"poi\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"administrative\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#000000\" }, { \"weight\": 1 } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"color\": \"#000000\" }, { \"weight\": 0.8 } ] },{ \"featureType\": \"landscape\", \"stylers\": [ { \"color\": \"#ffffff\" } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"transit\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"elementType\": \"labels.text\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#ffffff\" } ] },{ \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#000000\" } ] },{ \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"on\" } ] } ]"},{"id":11,"name":"Blue","json":"[{ featureType: 'all', stylers: [{hue: '#0000b0'},{invert_lightness: 'true'},{saturation: -30} ]} ]"},{"id":92,"name":"Blue Cyan","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#333333\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.natural\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#666666\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#df2f23\" },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#cccccc\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#999999\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#aaaaaa\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#808080\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#aaaaaa\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text\" },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#c6eeee\" }\r\n ]\r\n },{\r\n }\r\n]"},{"id":61,"name":"Blue Essence","json":"[\r\n {\r\n featureType: \"landscape.natural\",\r\n elementType: \"geometry.fill\",\r\n stylers: [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#e0efef\" }\r\n ]\r\n },{\r\n featureType: \"poi\",\r\n elementType: \"geometry.fill\",\r\n stylers: [\r\n { \"visibility\": \"on\" },\r\n { \"hue\": \"#1900ff\" },\r\n { \"color\": \"#c0e8e8\" }\r\n ]\r\n },{\r\n //buildings\r\n featureType: \"landscape.man_made\",\r\n elementType: \"geometry.fill\"\r\n },{\r\n featureType: \"road\",\r\n elementType: \"geometry\",\r\n stylers: [\r\n { lightness: 100 },\r\n { visibility: \"simplified\" }\r\n ]\r\n },{\r\n featureType: \"road\",\r\n elementType: \"labels\",\r\n stylers: [\r\n { visibility: \"off\" }\r\n ]\r\n },{\r\n featureType: 'water',\r\n stylers: [\r\n { color: '#7dcdcd' }\r\n ]\r\n },{\r\n featureType: 'transit.line',\r\n elementType: 'geometry',\r\n stylers: [\r\n { visibility: 'on' },\r\n { lightness: 700 }\r\n ]\r\n }\r\n]"},{"id":60,"name":"Blue Gray","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"color\": \"#b5cbe4\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#efefef\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#83a5b0\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#bdcdd3\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ffffff\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#e3eed3\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"lightness\": 33\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\"\r\n },\r\n {\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n {},\r\n {\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n }\r\n]"},{"id":25,"name":"Blue water","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\tstylers: [{color:'#46bcec'},{visibility:'on'}]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\tstylers: [{color:'#f2f2f2'}]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\tstylers: [{saturation: -100},{lightness: 45}]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\tstylers: [{visibility: 'simplified'}]\r\n\t},{\r\n\t\tfeatureType: 'road.arterial',\r\n\t\telementType: 'labels.icon',\r\n\t\tstylers: [{visibility: 'off'}]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'labels.text.fill',\r\n\t\tstylers: [{color: '#444444'}]\r\n\t},{\r\n\t\tfeatureType: 'transit',\r\n\t\tstylers: [{visibility: 'off'}]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\tstylers: [{visibility: 'off'}]\r\n\t}\r\n]"},{"id":67,"name":"Blueprint","json":"[\r\n \r\n\t \r\n\t \r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\r\n\t\r\n\t\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\t {\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\t\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 25\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 25\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 21\r\n }\r\n ]\r\n },\r\n {\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": 0 },\r\n { \"color\": \"#4d88ea\" },\r\n { \"lightness\": 0 }\r\n ]\r\n },\r\n\r\n {\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 19\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n },\r\n {\r\n \"weight\": 1.2\r\n }\r\n ]\r\n }\r\n]\t\t"},{"id":66,"name":"Blueprint (No Labels)","json":"[\r\n {\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" },\r\n { \"saturation\": -100 }\r\n ]\r\n },\r\n\t \r\n\t \r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\r\n\t\r\n\t\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n \r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\t {\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n\t\r\n\t\r\n\t\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 25\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 25\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 21\r\n }\r\n ]\r\n },\r\n {\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"color\": \"#7b94be\" },\r\n { \"lightness\": 50 }\r\n ]\r\n },\r\n\r\n {\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 19\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 20\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000045\"\r\n },\r\n {\r\n \"lightness\": 17\r\n },\r\n {\r\n \"weight\": 1.2\r\n }\r\n ]\r\n }\r\n]\t\t\t\r\n\t"},{"id":28,"name":"Bluish","json":"[ {\r\n\t\t\t\"stylers\": [\r\n\t\t\t { \"hue\": \"#007fff\" },\r\n\t\t\t { \"saturation\": 89 }\r\n\t\t\t]\r\n\t\t },{\r\n\t\t\t\"featureType\": \"water\",\r\n\t\t\t\"stylers\": [\r\n\t\t\t { \"color\": \"#ffffff\" }\r\n\t\t\t]\r\n\t\t },{\r\n\t\t\t\"featureType\": \"administrative.country\",\r\n\t\t\t\"elementType\": \"labels\",\r\n\t\t\t\"stylers\": [\r\n\t\t\t { \"visibility\": \"off\" }\r\n\t\t\t]\r\n\t\t }\r\n]"},{"id":96,"name":"Bobby's World","json":"[ { \"featureType\": \"landscape.natural.landcover\", \"stylers\": [ { \"gamma\": 0.44 }, { \"hue\": \"#2bff00\" } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"hue\": \"#00a1ff\" }, { \"saturation\": 29 }, { \"gamma\": 0.74 } ] },{ \"featureType\": \"landscape.natural.terrain\", \"stylers\": [ { \"hue\": \"#00ff00\" }, { \"saturation\": 54 }, { \"lightness\": -51 }, { \"gamma\": 0.4 } ] },{ \"featureType\": \"transit.line\", \"stylers\": [ { \"gamma\": 0.27 }, { \"hue\": \"#0077ff\" }, { \"saturation\": -91 }, { \"lightness\": 36 } ] },{ \"featureType\": \"landscape.man_made\", \"stylers\": [ { \"saturation\": 10 }, { \"lightness\": -23 }, { \"hue\": \"#0099ff\" }, { \"gamma\": 0.71 } ] },{ \"featureType\": \"poi.business\", \"stylers\": [ { \"hue\": \"#0055ff\" }, { \"saturation\": 9 }, { \"lightness\": -46 }, { \"gamma\": 1.05 } ] },{ \"featureType\": \"administrative.country\", \"stylers\": [ { \"gamma\": 0.99 } ] },{ \"featureType\": \"administrative.province\", \"stylers\": [ { \"lightness\": 36 }, { \"saturation\": -54 }, { \"gamma\": 0.76 } ] },{ \"featureType\": \"administrative.locality\", \"stylers\": [ { \"lightness\": 33 }, { \"saturation\": -61 }, { \"gamma\": 1.21 } ] },{ \"featureType\": \"administrative.neighborhood\", \"stylers\": [ { \"hue\": \"#ff0000\" }, { \"gamma\": 2.44 } ] },{ \"featureType\": \"road.highway.controlled_access\", \"stylers\": [ { \"hue\": \"#ff0000\" }, { \"lightness\": 67 }, { \"saturation\": -40 } ] },{ \"featureType\": \"road.arterial\", \"stylers\": [ { \"hue\": \"#ff6600\" }, { \"saturation\": 52 }, { \"gamma\": 0.64 } ] },{ \"featureType\": \"road.local\", \"stylers\": [ { \"hue\": \"#006eff\" }, { \"gamma\": 0.46 }, { \"saturation\": -3 }, { \"lightness\": -10 } ] },{ \"featureType\": \"transit.line\", \"stylers\": [ { \"hue\": \"#0077ff\" }, { \"saturation\": -46 }, { \"gamma\": 0.58 } ] },{ \"featureType\": \"transit.station\", \"stylers\": [ { \"gamma\": 0.8 } ] },{ \"featureType\": \"transit.station.rail\", \"stylers\": [ { \"hue\": \"#ff0000\" }, { \"saturation\": -45 }, { \"gamma\": 0.9 } ] },{ \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"gamma\": 0.58 } ] },{ \"featureType\": \"landscape.man_made\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"gamma\": 2.01 }, { \"hue\": \"#00ffff\" }, { \"lightness\": 22 } ] },{ \"featureType\": \"transit\", \"stylers\": [ { \"saturation\": -87 }, { \"lightness\": 44 }, { \"gamma\": 1.98 }, { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi.business\", \"elementType\": \"labels.text\", \"stylers\": [ { \"gamma\": 0.06 }, { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi\", \"elementType\": \"geometry\", \"stylers\": [ { \"hue\": \"#00aaff\" }, { \"lightness\": -6 }, { \"gamma\": 2.21 } ] },{ \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"gamma\": 3.84 } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"road\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"gamma\": 9.99 } ] },{ \"featureType\": \"administrative\", \"stylers\": [ { \"gamma\": 0.01 } ] } ]"},{"id":17,"name":"Bright & Bubbly","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#19a0d8\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ffffff\"\r\n },\r\n {\r\n \"weight\": 6\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#e85113\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#efe9e4\"\r\n },\r\n {\r\n \"lightness\": -40\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#efe9e4\"\r\n },\r\n {\r\n \"lightness\": -20\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": 100\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": -100\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"labels.icon\"\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": 20\r\n },\r\n {\r\n \"color\": \"#efe9e4\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.man_made\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": 100\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": -100\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"hue\": \"#11ff00\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"lightness\": 100\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n {\r\n \"hue\": \"#4cff00\"\r\n },\r\n {\r\n \"saturation\": 58\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"color\": \"#f0e4d3\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#efe9e4\"\r\n },\r\n {\r\n \"lightness\": -25\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#efe9e4\"\r\n },\r\n {\r\n \"lightness\": -10\r\n }\r\n ]\r\n },\r\n // ----- Hides labels for Points of Interests -----\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n // hides both icon and text\r\n //\"visibility\": \"off\"\r\n \r\n // hides just the text\r\n \"visibility\": \"simplified\"\r\n }\r\n ]\r\n },\r\n // ------------------------------------------------\r\n ]"},{"id":101,"name":"Bright Dessert","json":"[\r\n {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"saturation\": -7 },\r\n { \"gamma\": 1.02 },\r\n { \"hue\": \"#ffc300\" },\r\n { \"lightness\": -10 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"stylers\": [\r\n { \"hue\": \"#ffaa00\" },\r\n { \"saturation\": -45 },\r\n { \"gamma\": 1 },\r\n { \"lightness\": -4 }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"stylers\": [\r\n { \"hue\": \"#ffaa00\" },\r\n { \"lightness\": -10 },\r\n { \"saturation\": 64 },\r\n { \"gamma\": 0.9 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"lightness\": -5 },\r\n { \"hue\": \"#00f6ff\" },\r\n { \"saturation\": -40 },\r\n { \"gamma\": 0.75 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"saturation\": -30 },\r\n { \"lightness\": 11 },\r\n { \"gamma\": 0.5 },\r\n { \"hue\": \"#ff8000\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"hue\": \"#0077ff\" },\r\n { \"gamma\": 1.25 },\r\n { \"saturation\": -22 },\r\n { \"lightness\": -31 }\r\n ]\r\n }\r\n]"},{"id":100,"name":"Brownie","json":"[\r\n {\r\n \"stylers\": [\r\n { \"hue\": \"#ff8800\" },\r\n { \"gamma\": 0.4 }\r\n ]\r\n }\r\n]"},{"id":45,"name":"Candy Colours ","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFE100\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 34.48275862068968\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -1.490196078431353\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FF009A\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -2.970297029703005\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -17.815686274509815\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFE100\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 8.600000000000009\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -4.400000000000006\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00C3FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 29.31034482758622\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -38.980392156862735\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#0078FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00FF19\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -30.526315789473685\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -22.509803921568633\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":63,"name":"Caribbean Mountain","json":"[\r\n {\r\n \"featureType\": \"poi.medical\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.business\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.place_of_worship\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"color\": \"#cec6b3\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"color\": \"#f2eee8\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#01186a\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#cec6b3\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.government\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n }\r\n]"},{"id":9,"name":"Chilled","json":"[{ featureType: 'road', elementType: 'geometry', stylers: [{'visibility': 'simplified'}]}, { featureType: 'road.arterial', stylers: [ {hue: 149}, {saturation: -78}, {lightness: 0} ]}, { featureType: 'road.highway', stylers: [{hue: -31},{saturation: -40},{lightness: 2.8} ]}, { featureType: 'poi', elementType: 'label', stylers: [{'visibility': 'off'}]}, { featureType: 'landscape', stylers: [{hue: 163},{saturation: -26},{lightness: -1.1} ]}, { featureType: 'transit', stylers: [{'visibility': 'off'}]}, { featureType: 'water',stylers: [{hue: 3},{saturation: -24.24},{lightness: -38.57} ]} ]"},{"id":77,"name":"Clean Cut","json":"[\r\n {\r\n featureType: \"road\",\r\n elementType: \"geometry\",\r\n stylers: [\r\n { lightness: 100 },\r\n { visibility: \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"color\": \"#C6E2FF\",\r\n }\r\n ]\r\n }, {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#C5E3BF\"\r\n }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#D1D1B8\"\r\n }\r\n ]\r\n }\r\n]"},{"id":102,"name":"Clean Grey","json":"[\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative.country\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative.province\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"color\": \"#e3e3e3\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.natural\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"all\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"all\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#cccccc\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.line\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.line\",\r\n \"elementType\": \"labels.text\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.station.airport\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.station.airport\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#FFFFFF\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n }\r\n]"},{"id":30,"name":"Cobalt","json":"[\r\n\t{\r\n\t\tfeatureType: \"all\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [\r\n\t\t{\r\n\t\t\tinvert_lightness: true\r\n\t\t},\r\n\t\t{\r\n\t\t\tsaturation: 10\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tlightness: 30\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tgamma: 0.5\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\thue: \"#435158\"\r\n\t\t}\r\n\t\t]\r\n\t}\r\n\t]"},{"id":80,"name":"Cool Grey","json":"[ { \"featureType\": \"landscape\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"transit\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"water\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"road\", \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"stylers\": [ { \"hue\": \"#00aaff\" }, { \"saturation\": -100 }, { \"gamma\": 2.15 }, { \"lightness\": 12 } ] },{ \"featureType\": \"road\", \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"lightness\": 24 } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry\", \"stylers\": [ { \"lightness\": 57 } ] } ]"},{"id":6,"name":"Countries","json":"[{ featureType: 'all', stylers: [{visibility: 'off'} ]},{ featureType: 'water', stylers: [{visibility: 'on'},{lightness: -100 } ]} ]"},{"id":32,"name":"Deep Green","json":" [\r\n{ \"featureType\": \"administrative\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"landscape.man_made\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#ffe24d\" } ] },{ \"featureType\": \"road\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#158c28\" } ] },{ \"featureType\": \"landscape.natural\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#37b34a\" } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"color\": \"#ffe24d\" } ] },{ \"featureType\": \"poi\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#8bc53f\" } ] },{ \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#808080\" }, { \"gamma\": 9.91 }, { \"visibility\": \"off\" } ] },{ \"elementType\": \"labels.text.fill\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"lightness\": 100 }, { \"visibility\": \"on\" } ] },{\"elementType\": \"labels.icon\",\"stylers\": [ { \"visibility\": \"off\" }] }]"},{"id":56,"name":"Esperanto","json":" [\r\n {\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },\r\n {\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#0000ff\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ff0000\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000100\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ffff00\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway.controlled_access\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#ff0000\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ffa91a\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.natural\",\r\n \"stylers\": [\r\n { \"saturation\": 36 },\r\n { \"gamma\": 0.55 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"lightness\": -100 },\r\n { \"weight\": 2.1 }\r\n ]\r\n }, \r\n {\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"invert_lightness\": true },\r\n { \"hue\": \"#ff0000\" },\r\n { \"gamma\": 3.02 },\r\n { \"lightness\": 20 },\r\n { \"saturation\": 40 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.attraction\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#ff00ee\" },\r\n { \"lightness\": -13 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.government\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#eeff00\" },\r\n { \"gamma\": 0.67 },\r\n { \"lightness\": -26 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.medical\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"hue\": \"#ff0000\" },\r\n { \"saturation\": 100 },\r\n { \"lightness\": -37 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.medical\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ff0000\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.school\",\r\n \"stylers\": [\r\n { \"hue\": \"#ff7700\" },\r\n { \"saturation\": 97 },\r\n { \"lightness\": -41 }\r\n ]\r\n }, \r\n {\r\n \"featureType\": \"poi.sports_complex\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#00ffb3\" },\r\n { \"lightness\": -71 }\r\n ]\r\n }, \r\n {\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"saturation\": 84 },\r\n { \"lightness\": -57 },\r\n { \"hue\": \"#a1ff00\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.station.airport\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"gamma\": 0.11 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.station\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#ffc35e\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit.line\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"lightness\": -100 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"gamma\": 0.35 },\r\n { \"lightness\": 20 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.business\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"gamma\": 0.35 }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.business\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#69ffff\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.place_of_worship\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#c3ffc3\" }\r\n ]\r\n }\r\n]"},{"id":53,"name":"Flat Map","json":"[{\r\n \"stylers\": [{\r\n \"visibility\": \"off\"\r\n }]\r\n }, {\r\n \"featureType\": \"road\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#ffffff\"\r\n }]\r\n }, {\r\n \"featureType\": \"road.arterial\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#fee379\"\r\n }]\r\n }, {\r\n \"featureType\": \"road.highway\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#fee379\"\r\n }]\r\n }, {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#f3f4f4\"\r\n }]\r\n }, {\r\n \"featureType\": \"water\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#7fc8ed\"\r\n }]\r\n }, {}, {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [{\r\n \"visibility\": \"off\"\r\n }]\r\n }, {\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [{\r\n \"visibility\": \"on\"\r\n }, {\r\n \"color\": \"#83cead\"\r\n }]\r\n }, {\r\n \"elementType\": \"labels\",\r\n \"stylers\": [{\r\n \"visibility\": \"off\"\r\n }]\r\n }, {\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [{\r\n \"weight\": 0.9\r\n }, {\r\n \"visibility\": \"off\"\r\n }]\r\n }]"},{"id":36,"name":"Flat green","json":"[{ \"stylers\": [ { \"hue\": \"#bbff00\" }, { \"weight\": 0.5 }, { \"gamma\": 0.5 } ] },{ \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"landscape.natural\", \"stylers\": [ { \"color\": \"#a4cc48\" } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry\", \"stylers\": [ { \"color\": \"#ffffff\" }, { \"visibility\": \"on\" }, { \"weight\": 1 } ] },{ \"featureType\": \"administrative\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"featureType\": \"road.highway\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"gamma\": 1.14 }, { \"saturation\": -18 } ] },{ \"featureType\": \"road.highway.controlled_access\", \"elementType\": \"labels\", \"stylers\": [ { \"saturation\": 30 }, { \"gamma\": 0.76 } ] },{ \"featureType\": \"road.local\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"weight\": 0.4 }, { \"lightness\": -8 } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"color\": \"#4aaecc\" } ] },{ \"featureType\": \"landscape.man_made\", \"stylers\": [ { \"color\": \"#718e32\" } ] },{ \"featureType\": \"poi.business\", \"stylers\": [ { \"saturation\": 68 }, { \"lightness\": -61 } ] },{ \"featureType\": \"administrative.locality\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"weight\": 2.7 }, { \"color\": \"#f4f9e8\" } ] },{ \"featureType\": \"road.highway.controlled_access\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"weight\": 1.5 }, { \"color\": \"#e53013\" }, { \"saturation\": -42 }, { \"lightness\": 28 } ] }]"},{"id":20,"name":"Gowalla","json":"[\t\t\r\n\t{\r\n\t\tfeatureType: \"road\",\r\n\t\telementType: \"labels\",\r\n\t\tstylers: [ { visibility: \"simplified\" }, { lightness: 20 } ]\r\n\t},{\r\n\t\tfeatureType: \"administrative.land_parcel\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [ { visibility: \"off\" } ]\r\n\t},{\r\n\t\tfeatureType: \"landscape.man_made\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [ { visibility: \"off\" } ]\r\n\t},{\r\n\t\tfeatureType: \"transit\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [ { visibility: \"off\" } ]\r\n\t},{\r\n\t\tfeatureType: \"road.local\",\r\n\t\telementType: \"labels\",\r\n\t\tstylers: [ { visibility: \"simplified\" } ]\r\n\t},{\r\n\t\tfeatureType: \"road.local\",\r\n\t\telementType: \"geometry\",\r\n\t\tstylers: [ { visibility: \"simplified\" } ]\r\n\t},{\r\n\t\tfeatureType: \"road.highway\",\r\n\t\telementType: \"labels\",\r\n\t\tstylers: [ { visibility: \"simplified\" } ]\r\n\t},{\r\n\t\tfeatureType: \"poi\",\r\n\t\telementType: \"labels\",\r\n\t\tstylers: [ { visibility: \"off\" } ]\r\n },{\r\n\t\tfeatureType: \"road.arterial\",\r\n\t\telementType: \"labels\",\r\n\t\tstylers: [ { visibility: \"off\" } ]\r\n\t},{\r\n\t\tfeatureType: \"water\",\r\n\t\telementType: \"all\",\r\n\t\tstylers: [ { hue: \"#a1cdfc\" },{ saturation: 30 },{ lightness: 49 } ]\r\n\t},{\r\n\t\tfeatureType: \"road.highway\",\r\n\t\telementType: \"geometry\",\r\n\t\tstylers: [ { hue: \"#f49935\" } ]\r\n\t},{\r\n\t\tfeatureType: \"road.arterial\",\r\n\t\telementType: \"geometry\",\r\n\t\tstylers: [ { hue: \"#fad959\" } ]\r\n\t}\r\n]"},{"id":82,"name":"Grass is greener. Water is bluer.","json":"[ { \"stylers\": [ { \"saturation\": -100 } ] },{ \"featureType\": \"water\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#0099dd\" } ] },{ \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi.park\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#aadd55\" } ] },{ \"featureType\": \"road.highway\", \"elementType\": \"labels\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"featureType\": \"road.arterial\", \"elementType\": \"labels.text\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"featureType\": \"road.local\", \"elementType\": \"labels.text\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ } ]"},{"id":89,"name":"Green","json":"[\r\n {\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#bbd5c5\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#808080\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#fcf9a2\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#bbd5c5\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#808080\" }\r\n ]\r\n }\r\n]"},{"id":5,"name":"Greyscale","json":"[{ featureType: 'all', stylers: [{saturation: -100},{gamma: 0.50} ]} ]"},{"id":48,"name":"Hard edges","json":"[\r\n {\r\n \"featureType\": \"landscape.natural\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -86 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -75 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 97 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -68 }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 91 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"transit.station\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -22 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"hue\": \"#ff004c\" },\r\n { \"saturation\": -100 },\r\n { \"lightness\": 44 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": 1 },\r\n { \"lightness\": -100 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"administrative.locality\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },\r\n{\r\n \"featureType\": \"administrative.locality\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n }\r\n]"},{"id":76,"name":"HashtagNineNineNine","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#bbbbbb' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -4 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#999999' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -33 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#999999' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -6 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#aaaaaa' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -15 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]"},{"id":41,"name":"Hints of Gold","json":"[\r\n\t\t\t{\r\n\t\t\t\tfeatureType: 'water',\r\n\t\t\t\telementType: 'all',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#252525' },\r\n\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t{ lightness: -81 },\r\n\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'landscape',\r\n\t\t\t\telementType: 'all',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#666666' },\r\n\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t{ lightness: -55 },\r\n\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'poi',\r\n\t\t\t\telementType: 'geometry',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#555555' },\r\n\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t{ lightness: -57 },\r\n\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'road',\r\n\t\t\t\telementType: 'all',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#777777' },\r\n\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t{ lightness: -6 },\r\n\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'administrative',\r\n\t\t\t\telementType: 'all',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#cc9900' },\r\n\t\t\t\t\t{ saturation: 100 },\r\n\t\t\t\t\t{ lightness: -22 },\r\n\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'transit',\r\n\t\t\t\telementType: 'all',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#444444' },\r\n\t\t\t\t\t{ saturation: 0 },\r\n\t\t\t\t\t{ lightness: -64 },\r\n\t\t\t\t\t{ visibility: 'off' }\r\n\t\t\t\t]\r\n\t\t\t},{\r\n\t\t\t\tfeatureType: 'poi',\r\n\t\t\t\telementType: 'labels',\r\n\t\t\t\tstylers: [\r\n\t\t\t\t\t{ hue: '#555555' },\r\n\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t{ lightness: -57 },\r\n\t\t\t\t\t{ visibility: 'off' }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t]"},{"id":69,"name":"Holiday","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFB000\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 71.66666666666669\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -28.400000000000006\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#E8FF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -76.6\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 113\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FF8300\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -77\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 27.400000000000006\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FF8C00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -66.6\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 34.400000000000006\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00C4FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 22.799999999999997\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -11.399999999999991\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#9FFF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -23.200000000000003\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":46,"name":"Homage to Toner","json":"[\r\n {\r\n featureType: 'water',\r\n elementType: 'all',\r\n stylers: [\r\n { hue: '#000000' },\r\n { saturation: -100 },\r\n { lightness: -100 },\r\n { visibility: 'simplified' }\r\n ]\r\n },{\r\n featureType: 'landscape',\r\n elementType: 'all',\r\n stylers: [\r\n { hue: '#FFFFFF' },\r\n { saturation: -100 },\r\n { lightness: 100 },\r\n { visibility: 'simplified' }\r\n ]\r\n },{\r\n featureType: 'landscape.man_made',\r\n elementType: 'all',\r\n stylers: [\r\n\r\n ]\r\n },{\r\n featureType: 'landscape.natural',\r\n elementType: 'all',\r\n stylers: [\r\n\r\n ]\r\n },{\r\n featureType: 'poi.park',\r\n elementType: 'geometry',\r\n stylers: [\r\n { hue: '#ffffff' },\r\n { saturation: -100 },\r\n { lightness: 100 },\r\n { visibility: 'off' }\r\n ]\r\n },{\r\n featureType: 'road',\r\n elementType: 'all',\r\n stylers: [\r\n { hue: '#333333' },\r\n { saturation: -100 },\r\n { lightness: -69 },\r\n { visibility: 'simplified' }\r\n ]\r\n },{\r\n featureType: 'poi.attraction',\r\n elementType: 'geometry',\r\n stylers: [\r\n { hue: '#ffffff' },\r\n { saturation: -100 },\r\n { lightness: 100 },\r\n { visibility: 'off' }\r\n ]\r\n },{\r\n featureType: 'administrative.locality',\r\n elementType: 'geometry',\r\n stylers: [\r\n { hue: '#ffffff' },\r\n { saturation: 0 },\r\n { lightness: 100 },\r\n { visibility: 'off' }\r\n ]\r\n },{\r\n featureType: 'poi.government',\r\n elementType: 'geometry',\r\n stylers: [\r\n { hue: '#ffffff' },\r\n { saturation: -100 },\r\n { lightness: 100 },\r\n { visibility: 'off' }\r\n ]\r\n }\r\n ]"},{"id":21,"name":"Hopper","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#165c64' },\r\n\t\t\t{ saturation: 34 },\r\n\t\t\t{ lightness: -69 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#b7caaa' },\r\n\t\t\t{ saturation: -14 },\r\n\t\t\t{ lightness: -18 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.man_made',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#cbdac1' },\r\n\t\t\t{ saturation: -6 },\r\n\t\t\t{ lightness: -9 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#8d9b83' },\r\n\t\t\t{ saturation: -89 },\r\n\t\t\t{ lightness: -12 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d4dad0' },\r\n\t\t\t{ saturation: -88 },\r\n\t\t\t{ lightness: 54 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.arterial',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#bdc5b6' },\r\n\t\t\t{ saturation: -89 },\r\n\t\t\t{ lightness: -3 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.local',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#bdc5b6' },\r\n\t\t\t{ saturation: -89 },\r\n\t\t\t{ lightness: -26 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#c17118' },\r\n\t\t\t{ saturation: 61 },\r\n\t\t\t{ lightness: -45 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.park',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#8ba975' },\r\n\t\t\t{ saturation: -46 },\r\n\t\t\t{ lightness: -28 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'transit',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#a43218' },\r\n\t\t\t{ saturation: 74 },\r\n\t\t\t{ lightness: -51 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.province',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.neighborhood',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.locality',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.land_parcel',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#3a3935' },\r\n\t\t\t{ saturation: 5 },\r\n\t\t\t{ lightness: -57 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.medical',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#cba923' },\r\n\t\t\t{ saturation: 50 },\r\n\t\t\t{ lightness: -46 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]\r\n"},{"id":24,"name":"Hot Pink","json":"[\r\n\t{\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"hue\": \"#ff61a6\" },\r\n\t\t\t{ \"visibility\": \"on\" },\r\n\t\t\t{ \"invert_lightness\": true },\r\n\t\t\t{ \"saturation\": 40 },\r\n\t\t\t{ \"lightness\": 10 }\r\n\t\t]\r\n\t}\r\n]"},{"id":7,"name":"Icy Blue","json":"[{stylers:[{hue:'#2c3e50'},{saturation:250}]},{featureType:'road',elementType:'geometry',stylers:[{lightness:50},{visibility:'simplified'}]},{featureType:'road',elementType:'labels',stylers:[{visibility:'off'}]}]"},{"id":81,"name":"Ilustração","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#71ABC3' },\r\n\t\t\t{ saturation: -10 },\r\n\t\t\t{ lightness: -21 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.natural',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#7DC45C' },\r\n\t\t\t{ saturation: 37 },\r\n\t\t\t{ lightness: -41 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.man_made',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#C3E0B0' },\r\n\t\t\t{ saturation: 23 },\r\n\t\t\t{ lightness: -12 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#A19FA0' },\r\n\t\t\t{ saturation: -98 },\r\n\t\t\t{ lightness: -20 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#FFFFFF' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t}\r\n]"},{"id":33,"name":"Jane Iredale","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#87bcba' },\r\n\t\t\t{ saturation: -37 },\r\n\t\t\t{ lightness: -17 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.man_made',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#4f6b46' },\r\n\t\t\t{ saturation: -23 },\r\n\t\t\t{ lightness: -61 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: -55 },\r\n\t\t\t{ lightness: 13 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffa200' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -22 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.local',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: -55 },\r\n\t\t\t{ lightness: -31 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'transit',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#f69d94' },\r\n\t\t\t{ saturation: 84 },\r\n\t\t\t{ lightness: 9 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: 45 },\r\n\t\t\t{ lightness: 36 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.country',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: 45 },\r\n\t\t\t{ lightness: 36 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.land_parcel',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: 45 },\r\n\t\t\t{ lightness: 36 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.government',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: 35 },\r\n\t\t\t{ lightness: -19 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.school',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#d38bc8' },\r\n\t\t\t{ saturation: -6 },\r\n\t\t\t{ lightness: -17 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.park',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#b2ba70' },\r\n\t\t\t{ saturation: -19 },\r\n\t\t\t{ lightness: -25 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]"},{"id":71,"name":"Jazzygreen","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#000000\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 44\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00F93f\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -40.95294117647059\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00F93f\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -51.15294117647059\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00F93f\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -50.35294117647059\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00F93f\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -50.35294117647059\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00F93f\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 100\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -50.35294117647059\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":65,"name":"Just places","json":"[\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#fffffa\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"lightness\": 50 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"lightness\": 40 }\r\n ]\r\n }\r\n]"},{"id":90,"name":"Light Blue Water","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#71d6ff' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -5 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'transit',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#deecec' },\r\n\t\t\t{ saturation: -73 },\r\n\t\t\t{ lightness: 72 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#bababa' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 25 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#e3e3e3' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 0 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#59cfff' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: 34 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]"},{"id":59,"name":"Light Green","json":"[\r\n {\"stylers\":[\r\n { \"hue\":\"#baf4c4\" },\r\n { \"saturation\":10 }\r\n ]},\r\n {\r\n \"featureType\":\"water\",\r\n \"stylers\":[{\r\n \"color\":\"#effefd\"\r\n }]\r\n },\r\n {\r\n \"featureType\":\"all\",\r\n \"elementType\":\"labels\",\r\n \"stylers\":[{\r\n \"visibility\":\"off\"\r\n }]\r\n },\r\n {\r\n featureType:\"administrative\",\r\n elementType:\"labels\",\r\n stylers:[\r\n {visibility:\"on\"}\r\n ]\r\n },\r\n {\r\n featureType:\"road\",\r\n elementType:\"all\",\r\n stylers:[\r\n {visibility:\"off\"}\r\n ]\r\n },\r\n {\r\n featureType:\"transit\",\r\n elementType:\"all\",\r\n stylers:[\r\n {visibility:\"off\"}\r\n ]\r\n }\r\n \r\n ]"},{"id":29,"name":"Light Monochrome","json":"[\r\n\t\t\t\t{\r\n\t\t\t\t\tfeatureType: 'water',\r\n\t\t\t\t\telementType: 'all',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#e9ebed' },\r\n\t\t\t\t\t\t{ saturation: -78 },\r\n\t\t\t\t\t\t{ lightness: 67 },\r\n\t\t\t\t\t\t{ visibility: 'simplified' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'landscape',\r\n\t\t\t\t\telementType: 'all',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#ffffff' },\r\n\t\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t\t{ lightness: 100 },\r\n\t\t\t\t\t\t{ visibility: 'simplified' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'road',\r\n\t\t\t\t\telementType: 'geometry',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#bbc0c4' },\r\n\t\t\t\t\t\t{ saturation: -93 },\r\n\t\t\t\t\t\t{ lightness: 31 },\r\n\t\t\t\t\t\t{ visibility: 'simplified' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'poi',\r\n\t\t\t\t\telementType: 'all',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#ffffff' },\r\n\t\t\t\t\t\t{ saturation: -100 },\r\n\t\t\t\t\t\t{ lightness: 100 },\r\n\t\t\t\t\t\t{ visibility: 'off' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'road.local',\r\n\t\t\t\t\telementType: 'geometry',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#e9ebed' },\r\n\t\t\t\t\t\t{ saturation: -90 },\r\n\t\t\t\t\t\t{ lightness: -8 },\r\n\t\t\t\t\t\t{ visibility: 'simplified' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'transit',\r\n\t\t\t\t\telementType: 'all',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#e9ebed' },\r\n\t\t\t\t\t\t{ saturation: 10 },\r\n\t\t\t\t\t\t{ lightness: 69 },\r\n\t\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'administrative.locality',\r\n\t\t\t\t\telementType: 'all',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#2c2e33' },\r\n\t\t\t\t\t\t{ saturation: 7 },\r\n\t\t\t\t\t\t{ lightness: 19 },\r\n\t\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'road',\r\n\t\t\t\t\telementType: 'labels',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#bbc0c4' },\r\n\t\t\t\t\t\t{ saturation: -93 },\r\n\t\t\t\t\t\t{ lightness: 31 },\r\n\t\t\t\t\t\t{ visibility: 'on' }\r\n\t\t\t\t\t]\r\n\t\t\t\t},{\r\n\t\t\t\t\tfeatureType: 'road.arterial',\r\n\t\t\t\t\telementType: 'labels',\r\n\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t{ hue: '#bbc0c4' },\r\n\t\t\t\t\t\t{ saturation: -93 },\r\n\t\t\t\t\t\t{ lightness: -2 },\r\n\t\t\t\t\t\t{ visibility: 'simplified' }\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t]"},{"id":93,"name":"Lost in the desert","json":"[\r\n {\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" },\r\n { \"color\": \"#f49f53\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"color\": \"#f9ddc5\" },\r\n { \"lightness\": -7 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"color\": \"#813033\" },\r\n { \"lightness\": 43 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.business\",\r\n \"stylers\": [\r\n { \"color\": \"#645c20\" },\r\n { \"lightness\": 38 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#1994bf\" },\r\n { \"saturation\": -69 },\r\n { \"gamma\": 0.99 },\r\n { \"lightness\": 43 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#f19f53\" },\r\n { \"weight\": 1.3 },\r\n { \"visibility\": \"on\" },\r\n { \"lightness\": 16 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.business\" },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"color\": \"#645c20\" },\r\n { \"lightness\": 39 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.school\",\r\n \"stylers\": [\r\n { \"color\": \"#a95521\" },\r\n { \"lightness\": 35 }\r\n ]\r\n },{\r\n },{\r\n \"featureType\": \"poi.medical\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#813033\" },\r\n { \"lightness\": 38 },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n },{\r\n \"elementType\": \"labels\" },{\r\n \"featureType\": \"poi.sports_complex\",\r\n \"stylers\": [\r\n { \"color\": \"#9e5916\" },\r\n { \"lightness\": 32 }\r\n ]\r\n },{\r\n },{\r\n \"featureType\": \"poi.government\",\r\n \"stylers\": [\r\n { \"color\": \"#9e5916\" },\r\n { \"lightness\": 46 }\r\n ]\r\n },{\r\n \"featureType\": \"transit.station\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit.line\",\r\n \"stylers\": [\r\n { \"color\": \"#813033\" },\r\n { \"lightness\": 22 }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"lightness\": 38 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#f19f53\" },\r\n { \"lightness\": -10 }\r\n ]\r\n },{\r\n },{\r\n },{\r\n }\r\n]"},{"id":37,"name":"Lunar Landscape","json":"\r\n[\r\n\t{\r\n\t stylers: [\r\n\t\t\t{ hue: '#ff1a00' },\r\n\t\t\t{ invert_lightness: true },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 33 },\r\n\t\t\t{ gamma: 0.5 }\r\n\t ]\r\n\t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ color: '#2D333C' }\r\n\t\t]\r\n\t}\r\n ]"},{"id":44,"name":"MapBox","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n {\r\n \"saturation\": 43\r\n },\r\n {\r\n \"lightness\": -11\r\n },\r\n {\r\n \"hue\": \"#0088ff\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"hue\": \"#ff0000\"\r\n },\r\n {\r\n \"saturation\": -100\r\n },\r\n {\r\n \"lightness\": 99\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#808080\"\r\n },\r\n {\r\n \"lightness\": 54\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ece2d9\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ccdca1\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#767676\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ffffff\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.natural\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n },\r\n {\r\n \"color\": \"#b8cb93\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.sports_complex\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.medical\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"on\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.business\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"simplified\"\r\n }\r\n ]\r\n }\r\n]"},{"id":2,"name":"Midnight Commander","json":"[{'featureType': 'water','stylers': [{ 'color': '#021019' }]},{'featureType': 'landscape','stylers': [{ 'color': '#08304b' }]},{'featureType': 'poi','elementType': 'geometry','stylers': [{ 'color': '#0c4152' },{ 'lightness': 5 }]},{'featureType': 'road.highway','elementType': 'geometry.fill','stylers': [{ 'color': '#000000' }]},{'featureType': 'road.highway','elementType': 'geometry.stroke','stylers': [{ 'color': '#0b434f' },{ 'lightness': 25 }]},{'featureType': 'road.arterial','elementType': 'geometry.fill','stylers': [{ 'color': '#000000' }]},{'featureType': 'road.arterial','elementType': 'geometry.stroke','stylers': [{ 'color': '#0b3d51' },{ 'lightness': 16 }]},{'featureType': 'road.local','elementType': 'geometry','stylers': [{ 'color': '#000000' }]},{'elementType': 'labels.text.fill','stylers': [{ 'color': '#ffffff' }]},{'elementType': 'labels.text.stroke','stylers': [{ 'color': '#000000' },{ 'lightness': 13 }]},{'featureType': 'transit','stylers': [{ 'color': '#146474' }]},{'featureType': 'administrative','elementType': 'geometry.fill','stylers': [{ 'color': '#000000' }]},{'featureType': 'administrative','elementType': 'geometry.stroke','stylers': [{ 'color': '#144b53' },{ 'lightness': 14 },{ 'weight': 1.4 }]}]"},{"id":57,"name":"Military Flat","json":"[ { \"featureType\": \"landscape\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"hue\": \"#00ff88\" }, { \"lightness\": 14 }, { \"color\": \"#667348\" }, { \"saturation\": 4 }, { \"gamma\": 1.14 } ] },{ \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"visibility\": \"simplified\" } ] },{ \"featureType\": \"administrative.country\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"color\": \"#313916\" }, { \"weight\": 0.8 } ] },{ \"featureType\": \"road\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"administrative.locality\", \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#334b1f\" } ] },{ \"featureType\": \"administrative.province\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"transit\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"visibility\": \"simplified\" } ] } ]\r\n"},{"id":10,"name":"Mixed","json":"[{ featureType: 'landscape', stylers: [{hue: '#00dd00'}]}, { featureType: 'road', stylers: [{hue: '#dd0000'}]}, { featureType: 'water', stylers: [{hue: '#000040'}]}, { featureType: 'poi.park', stylers: [{visibility: 'off'}]}, { featureType: 'road.arterial', stylers: [{hue: '#ffff00'}]}, { featureType: 'road.local', stylers: [{visibility: 'off'}]} ]"},{"id":83,"name":"Muted Blue","json":"[\r\n\t{\"featureType\": \"all\",\r\n\t\t\"stylers\":[\r\n\t\t\t{\"saturation\": 0},\r\n\t\t\t{\"hue\": \"#e7ecf0\"}\r\n\t\t]\r\n\t},\r\n\t{\"featureType\": \"road\",\r\n\t\t\"stylers\":[\r\n\t\t\t{\"saturation\": -70}\r\n\t\t]\r\n\t},\r\n\t{\"featureType\": \"transit\",\r\n\t\t\"stylers\":[\r\n\t\t\t{\"visibility\": \"off\"}\r\n\t\t]\r\n\t},\r\n\t{\"featureType\": \"poi\",\r\n\t\t\"stylers\":[\r\n\t\t\t{\"visibility\": \"off\"}\r\n\t\t]\r\n\t},\r\n\t{\"featureType\": \"water\",\r\n\t\t\"stylers\":[\r\n\t\t\t{\"visibility\": \"simplified\"},\r\n\t\t\t{\"saturation\": -60}\r\n\t\t]\r\n\t}\r\n]"},{"id":91,"name":"Muted Monotone","json":"[\r\n\t {\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"on\" },\r\n\t { \"saturation\": -100 },\r\n\t { \"gamma\": 0.54 }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"road\",\r\n\t \"elementType\": \"labels.icon\",\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"off\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"water\",\r\n\t \"stylers\": [\r\n\t { \"color\": \"#4d4946\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"poi\",\r\n\t \"elementType\": \"labels.icon\",\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"off\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"poi\",\r\n\t \"elementType\": \"labels.text\",\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"simplified\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"road\",\r\n\t \"elementType\": \"geometry.fill\",\r\n\t \"stylers\": [\r\n\t { \"color\": \"#ffffff\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"road.local\",\r\n\t \"elementType\": \"labels.text\",\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"simplified\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"water\",\r\n\t \"elementType\": \"labels.text.fill\",\r\n\t \"stylers\": [\r\n\t { \"color\": \"#ffffff\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"transit.line\",\r\n\t \"elementType\": \"geometry\",\r\n\t \"stylers\": [\r\n\t { \"gamma\": 0.48 }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"transit.station\",\r\n\t \"elementType\": \"labels.icon\",\r\n\t \"stylers\": [\r\n\t { \"visibility\": \"off\" }\r\n\t ]\r\n\t },{\r\n\t \"featureType\": \"road\",\r\n\t \"elementType\": \"geometry.stroke\",\r\n\t \"stylers\": [\r\n\t { \"gamma\": 7.18 }\r\n\t ]\r\n\t }\r\n\t]"},{"id":47,"name":"Nature","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFA800\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#53FF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -73\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 40\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FBFF00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00FFFD\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 30\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00BFFF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 6\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 8\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#679714\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 33.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -25.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n\r\n]"},{"id":86,"name":"Nature Highlight","json":"[\r\n {\r\n \"featureType\": \"administrative\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative.locality\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" },\r\n { \"saturation\": 100 },\r\n { \"color\": \"#ff4702\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"visibility\": \"simplified\" },\r\n { \"lightness\": -25 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#5f1bff\" },\r\n { \"saturation\": 100 },\r\n { \"visibility\": \"on\" },\r\n { \"lightness\": -38 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.natural.terrain\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" },\r\n { \"color\": \"#d3ff44\" },\r\n { \"lightness\": -32 },\r\n { \"saturation\": 30 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.natural\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#797679\" },\r\n { \"gamma\": 4.92 },\r\n { \"lightness\": -47 }\r\n ]\r\n },{\r\n }\r\n]"},{"id":34,"name":"Neon World","json":"[\r\n {\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"gamma\": 0.6 }\r\n ]\r\n }\r\n]"},{"id":13,"name":"Neutral Blue","json":"[{\"featureType\": \"water\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#193341\" }]},{\"featureType\": \"landscape\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2c5a71\" }]},{\"featureType\": \"road\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#29768a\" },{ \"lightness\": -37 }]},{\"featureType\": \"poi\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#406d80\" }]},{\"featureType\": \"transit\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#406d80\" }]},{\"elementType\": \"labels.text.stroke\",\"stylers\": [{ \"visibility\": \"on\" },{ \"color\": \"#3e606f\" },{ \"weight\": 2 },{ \"gamma\": 0.84 }]},{\"elementType\": \"labels.text.fill\",\"stylers\": [{ \"color\": \"#ffffff\" }]},{\"featureType\": \"administrative\",\"elementType\": \"geometry\",\"stylers\": [{ \"weight\": 0.6 },{ \"color\": \"#1a3541\" }]},{\"elementType\": \"labels.icon\",\"stylers\": [{ \"visibility\": \"off\" }]},{\"featureType\": \"poi.park\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2c5a71\" }]}]"},{"id":62,"name":"Night vision","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#001204' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -95 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.man_made',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#007F1E' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -72 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.natural',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#00C72E' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -59 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#002C0A' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -87 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#00A927' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -58 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]"},{"id":64,"name":"Old Dry Mud","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFAD00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 50.2\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -34.8\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFAD00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -19.8\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -1.8\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFAD00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 72.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -32.6\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFAD00\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 74.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -18\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#00FFA6\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -63.2\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 38\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FFC300\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 54.2\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -14.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":99,"name":"Old Map","json":"[\r\n {\r\n \"featureType\": \"administrative\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#abbaa4\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit.line\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#3f518c\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"stylers\": [\r\n { \"color\": \"#ad9b8d\" }\r\n ]\r\n }\r\n]"},{"id":22,"name":"Old Timey","json":"[{featureType:\"administrative\",stylers:[{visibility:\"off\"}]},{featureType:\"poi\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road\",stylers:[{visibility:\"simplified\"}]},{featureType:\"water\",\r\nstylers:[{visibility:\"simplified\"}]},{featureType:\"transit\",stylers:[{visibility:\"simplified\"}]},{featureType:\"landscape\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road.highway\",stylers:[{visibility:\"off\"}]},{featureType:\"road.local\",stylers:[{visibility:\"on\"}]},{featureType:\"road.highway\",elementType:\"geometry\",stylers:[{visibility:\"on\"}]},{featureType:\"water\",stylers:[{color:\"#84afa3\"},{lightness:52}]},{stylers:[{saturation:-77}]},{featureType:\"road\"}]"},{"id":88,"name":"Overseas","json":"[\r\n\t{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#00559B' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -60 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.man_made',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape.natural',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.park',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#00559B' },\r\n\t\t\t{ saturation: 100 },\r\n\t\t\t{ lightness: -53 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.locality',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.school',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#999999' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -28 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#999999' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -23 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#2C3E50' },\r\n\t\t\t{ saturation: 29 },\r\n\t\t\t{ lightness: -52 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]\r\n"},{"id":1,"name":"Pale Dawn","json":"[ { 'featureType': 'water', 'stylers': [{ 'visibility': 'on' },{ 'color': '#acbcc9' } ] },{ 'featureType': 'landscape', 'stylers': [{ 'color': '#f2e5d4' } ] },{ 'featureType': 'road.highway', 'elementType': 'geometry', 'stylers': [{ 'color': '#c5c6c6' } ] },{ 'featureType': 'road.arterial', 'elementType': 'geometry', 'stylers': [{ 'color': '#e4d7c6' } ] },{ 'featureType': 'road.local', 'elementType': 'geometry', 'stylers': [{ 'color': '#fbfaf7' } ] },{ 'featureType': 'poi.park', 'elementType': 'geometry', 'stylers': [{ 'color': '#c5dac6' } ] },{ 'featureType': 'administrative', 'stylers': [{ 'visibility': 'on' },{ 'lightness': 33 } ] },{ 'featureType': 'road' },{ 'featureType': 'poi.park', 'elementType': 'labels', 'stylers': [{ 'visibility': 'on' },{ 'lightness': 20 } ] },{ },{ 'featureType': 'road', 'stylers': [{ 'lightness': 20 } ] }]"},{"id":39,"name":"Paper","json":"[{featureType:\"administrative\",stylers:[{visibility:\"off\"}]},{featureType:\"poi\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road\",stylers:[{visibility:\"simplified\"}]},{featureType:\"water\",stylers:[{visibility:\"simplified\"}]},{featureType:\"transit\",stylers:[{visibility:\"simplified\"}]},{featureType:\"landscape\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road.highway\",stylers:[{visibility:\"off\"}]},\r\n{featureType:\"road.local\",stylers:[{visibility:\"on\"}]},{featureType:\"road.highway\",elementType:\"geometry\",stylers:[{visibility:\"on\"}]},{featureType:\"road.arterial\",stylers:[{visibility:\"off\"}]},{featureType:\"water\",stylers:[{color:\"#5f94ff\"},{lightness:26},{gamma:5.86}]},{},{featureType:\"road.highway\",stylers:[{weight:0.6},{saturation:-85},{lightness:61}]},{featureType:\"road\"},{},{featureType:\"landscape\",stylers:[{hue:\"#0066ff\"},{saturation:74},{lightness:100}]}]"},{"id":84,"name":"Pastel Tones","json":"[\r\n {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 60 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 40 },\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative.province\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"lightness\": 30 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ef8c25\" },\r\n { \"lightness\": 40 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#b6c54c\" },\r\n { \"lightness\": 40 },\r\n { \"saturation\": -40 }\r\n ]\r\n },{\r\n }\r\n]"},{"id":78,"name":"Pink & Blue","json":"[ { \"featureType\": \"landscape\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#9debff\" }, { \"weight\": 0.1 } ] },{ \"featureType\": \"water\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#ebebeb\" } ] },{ \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#51dbff\" } ] },{ \"featureType\": \"poi.park\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#51dbff\" } ] },{ \"featureType\": \"poi\" },{ \"featureType\": \"transit.line\", \"stylers\": [ { \"color\": \"#ff4e80\" }, { \"visibility\": \"off\" } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"visibility\": \"on\" }, { \"weight\": 1.5 }, { \"color\": \"#51dbff\" } ] },{ \"featureType\": \"road.arterial\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#51dbNaN\" } ] },{ \"featureType\": \"road.highway\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"simplified\" }, { \"color\": \"#51dbff\" } ] },{ \"featureType\": \"poi.business\", \"stylers\": [ { \"color\": \"#9debff\" }, { \"visibility\": \"off\" } ] },{ },{ \"featureType\": \"poi.government\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi.school\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"administrative\", \"stylers\": [ { \"visibility\": \"on\" } ] },{ \"featureType\": \"poi.medical\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi.attraction\", \"elementType\": \"geometry\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#51dbff\" } ] },{ \"featureType\": \"poi.place_of_worship\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"poi.sports_complex\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ },{ \"featureType\": \"road.arterial\", \"elementType\": \"labels.text.stroke\", \"stylers\": [ { \"color\": \"#000000\" }, { \"visibility\": \"off\" } ] },{ \"featureType\": \"road.highway\", \"elementType\": \"labels.text\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"road.highway.controlled_access\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"road\" } ]"},{"id":98,"name":"Purple Rain","json":"[\r\n {\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"hue\": \"#5e00ff\" },\r\n { \"saturation\": -79 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"saturation\": -78 },\r\n { \"hue\": \"#6600ff\" },\r\n { \"lightness\": -47 },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"lightness\": 22 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"hue\": \"#6600ff\" },\r\n { \"saturation\": -11 }\r\n ]\r\n },{\r\n },{\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"saturation\": -65 },\r\n { \"hue\": \"#1900ff\" },\r\n { \"lightness\": 8 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"weight\": 1.3 },\r\n { \"lightness\": 30 }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" },\r\n { \"hue\": \"#5e00ff\" },\r\n { \"saturation\": -16 }\r\n ]\r\n },{\r\n \"featureType\": \"transit.line\",\r\n \"stylers\": [\r\n { \"saturation\": -72 }\r\n ]\r\n },{\r\n }\r\n]"},{"id":87,"name":"Red & Green","json":"[\r\n {\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"lightness\": 16 },\r\n { \"hue\": \"#ff001a\" },\r\n { \"saturation\": -61 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"stylers\": [\r\n { \"hue\": \"#ff0011\" },\r\n { \"lightness\": 53 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"hue\": \"#00ff91\" }\r\n ]\r\n },{\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"lightness\": 63 },\r\n { \"hue\": \"#ff0000\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"hue\": \"#0055ff\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n }\r\n]"},{"id":3,"name":"Red Alert","json":"[{'featureType': 'water','elementType': 'geometry','stylers': [{ 'color': '#ffdfa6' }]},{'featureType': 'landscape','elementType': 'geometry','stylers': [{ 'color': '#b52127' }]},{'featureType': 'poi','elementType': 'geometry','stylers': [{ 'color': '#c5531b' }]},{'featureType': 'road.highway','elementType': 'geometry.fill','stylers': [{ 'color': '#74001b' },{ 'lightness': -10 }]},{'featureType': 'road.highway','elementType': 'geometry.stroke','stylers': [{ 'color': '#da3c3c' }]},{'featureType': 'road.arterial','elementType': 'geometry.fill','stylers': [{ 'color': '#74001b' }]},{'featureType': 'road.arterial','elementType': 'geometry.stroke','stylers': [{ 'color': '#da3c3c' }]},{'featureType': 'road.local','elementType': 'geometry.fill','stylers': [{ 'color': '#990c19' }]},{'elementType': 'labels.text.fill','stylers': [{ 'color': '#ffffff' }]},{'elementType': 'labels.text.stroke','stylers': [{ 'color': '#74001b' },{ 'lightness': -8 }]},{'featureType': 'transit','elementType': 'geometry','stylers': [{ 'color': '#6a0d10' },{ 'visibility': 'on' }]},{'featureType': 'administrative','elementType': 'geometry','stylers': [{ 'color': '#ffdfa6' },{ 'weight': 0.4 }]},{'featureType': 'road.local','elementType': 'geometry.stroke','stylers': [{ 'visibility': 'off' }]}]"},{"id":31,"name":"Red Hues","json":"[\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t// Style the map with the custom hue\r\n\t\t\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t\t\t{ \"hue\":\"#dd0d0d\" }\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t// Remove road labels\r\n\t\t\t\t\t\t\tfeatureType:\"road\",\r\n\t\t\t\t\t\t\telementType:\"labels\",\r\n\t\t\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t\t\t{ \"visibility\":\"off\" }\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t// Style the road\r\n\t\t\t\t\t\t\tfeatureType:\"road\",\r\n\t\t\t\t\t\t\telementType:\"geometry\",\r\n\t\t\t\t\t\t\tstylers: [\r\n\t\t\t\t\t\t\t\t{ \"lightness\":100 },\r\n\t\t\t\t\t\t\t\t{ \"visibility\":\"simplified\" }\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]"},{"id":18,"name":"Retro","json":"[{featureType:\"administrative\",stylers:[{visibility:\"off\"}]},\r\n{featureType:\"poi\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road\",elementType:\"labels\",stylers:[{visibility:\"simplified\"}]},{featureType:\"water\",stylers:[{visibility:\"simplified\"}]},{featureType:\"transit\",stylers:[{visibility:\"simplified\"}]},{featureType:\"landscape\",stylers:[{visibility:\"simplified\"}]},{featureType:\"road.highway\",stylers:[{visibility:\"off\"}]},{featureType:\"road.local\",stylers:[{visibility:\"on\"}]},{featureType:\"road.highway\",elementType:\"geometry\",stylers:[{visibility:\"on\"}]},\r\n{featureType:\"water\",stylers:[{color:\"#84afa3\"},{lightness:52}]},{stylers:[{saturation:-17},{gamma:0.36}]},{featureType:\"transit.line\",elementType:\"geometry\",stylers:[{color:\"#3f518c\"}] }]"},{"id":95,"name":"Roadie","json":"[\r\n {\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#000000\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" },\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n }\r\n]"},{"id":51,"name":"Roadtrip At Night","json":"\r\n [\r\n {\r\n \"stylers\": [\r\n {\r\n \"hue\": \"#ff1a00\"\r\n },\r\n {\r\n \"invert_lightness\": true\r\n },\r\n {\r\n \"saturation\": -100\r\n },\r\n {\r\n \"lightness\": 33\r\n },\r\n {\r\n \"gamma\": 0.5\r\n }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#2D333C\"\r\n }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#eeeeee\"\r\n },\r\n\t {\r\n\t\t\"visibility\": \"simplified\"\r\n\t }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"visibility\": \"off\"\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#ffffff\"\r\n },\r\n {\r\n \"weight\": 3\r\n }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n {\r\n \"color\": \"#2D333C\"\r\n }\r\n ]\r\n }\r\n ]\r\n\r\n"},{"id":54,"name":"RouteXL","json":"[ \r\n\t{ featureType: \"administrative\", elementType: \"all\", stylers: [ { visibility: \"on\" }, { saturation: -100 }, { lightness: 20 } ] },\r\n\t{ featureType: \"road\", elementType: \"all\", stylers: [ { visibility: \"on\" }, { saturation: -100 }, { lightness: 40 } ] },\r\n\t{ featureType: \"water\", elementType: \"all\", stylers: [ { visibility: \"on\" }, { saturation: -10 }, { lightness: 30 } ] },\r\n\t{ featureType: \"landscape.man_made\", elementType: \"all\", stylers: [ { visibility: \"simplified\" }, { saturation: -60 }, { lightness: 10 } ] },\r\n\t{ featureType: \"landscape.natural\", elementType: \"all\", stylers: [ { visibility: \"simplified\" }, { saturation: -60 }, { lightness: 60 } ] },\r\n\t{ featureType: \"poi\", elementType: \"all\", stylers: [ { visibility: \"off\" }, { saturation: -100 }, { lightness: 60 } ] }, \r\n\t{ featureType: \"transit\", elementType: \"all\", stylers: [ { visibility: \"off\" }, { saturation: -100 }, { lightness: 60 } ] }\r\n]"},{"id":94,"name":"San Andreas","json":"[\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"lightness\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"lightness\": -100 },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#006eff\" },\r\n { \"lightness\": -19 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": -16 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"hue\": \"#2bff00\" },\r\n { \"lightness\": -39 },\r\n { \"saturation\": 8 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.attraction\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"lightness\": 100 },\r\n { \"saturation\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.business\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.government\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"lightness\": 100 },\r\n { \"saturation\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.medical\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"lightness\": 100 },\r\n { \"saturation\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.place_of_worship\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"lightness\": 100 },\r\n { \"saturation\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.school\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.sports_complex\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"saturation\": -100 },\r\n { \"lightness\": 100 }\r\n ]\r\n }\r\n]"},{"id":75,"name":"Shade of green","json":"[{featureType:\"water\",elementType:\"all\",stylers:[{hue:\"#76aee3\"},{saturation:38},{lightness:-11},{visibility:\"on\"}]},{featureType:\"road.highway\",elementType:\"all\",stylers:[{hue:\"#8dc749\"},{saturation:-47},{lightness:-17},{visibility:\"on\"}]},{featureType:\"poi.park\",elementType:\"all\",stylers:[{hue:\"#c6e3a4\"},{saturation:17},{lightness:-2},{visibility:\"on\"}]},{featureType:\"road.arterial\",elementType:\"all\",stylers:[{hue:\"#cccccc\"},{saturation:-100},{lightness:13},{visibility:\"on\"}]},{featureType:\"administrative.land_parcel\",elementType:\"all\",stylers:[{hue:\"#5f5855\"},{saturation:6},{lightness:-31},{visibility:\"on\"}]},{featureType:\"road.local\",elementType:\"all\",stylers:[{hue:\"#ffffff\"},{saturation:-100},{lightness:100},{visibility:\"simplified\"}]},{featureType:\"water\",elementType:\"all\",stylers:[]}]"},{"id":38,"name":"Shades of Grey","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 17 }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 20 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 17 }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 29 },\r\n { \"weight\": 0.2 }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 18 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 16 }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 21 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 16 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"saturation\": 36 },\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 40 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 19 }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 20 }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#000000\" },\r\n { \"lightness\": 17 },\r\n { \"weight\": 1.2 }\r\n ]\r\n }\r\n]"},{"id":27,"name":"Shift Worker","json":"[\r\n { stylers: [{ saturation: -100 }, { gamma: 1 }] },\r\n { elementType: \"labels.text.stroke\", stylers: [{ visibility: \"off\" }] },\r\n { featureType: \"poi.business\", elementType: \"labels.text\", stylers: [{ visibility: \"off\" }] },\r\n { featureType: \"poi.business\", elementType: \"labels.icon\", stylers: [{ visibility: \"off\" }] },\r\n { featureType: \"poi.place_of_worship\", elementType: \"labels.text\", stylers: [{ visibility: \"off\" }] },\r\n { featureType: \"poi.place_of_worship\", elementType: \"labels.icon\", stylers: [{ visibility: \"off\" }] },\r\n { featureType: \"road\", elementType: \"geometry\", stylers: [{ visibility: \"simplified\" }] },\r\n { featureType: \"water\", stylers: [{ visibility: \"on\" }, { saturation: 50 }, { gamma: 0 }, { hue: \"#50a5d1\" }] },\r\n { featureType: \"administrative.neighborhood\", elementType: \"labels.text.fill\", stylers: [{ color: \"#333333\" }] },\r\n { featureType: \"road.local\", elementType: \"labels.text\", stylers: [{ weight: 0.5 }, { color: \"#333333\" }] },\r\n { featureType: \"transit.station\", elementType: \"labels.icon\", stylers: [{ gamma: 1 }, { saturation: 50 }] }\r\n]"},{"id":58,"name":"Simple Labels","json":"[\r\n {\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"labels.text\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n }\r\n]"},{"id":12,"name":"Snazzy Maps","json":"[{\"featureType\": \"water\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#333739\" }]},{\"featureType\": \"landscape\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2ecc71\" }]},{\"featureType\": \"poi\",\"stylers\": [{ \"color\": \"#2ecc71\" },{ \"lightness\": -7 }]},{\"featureType\": \"road.highway\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2ecc71\" },{ \"lightness\": -28 }]},{\"featureType\": \"road.arterial\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2ecc71\" },{ \"visibility\": \"on\" },{ \"lightness\": -15 }]},{\"featureType\": \"road.local\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2ecc71\" },{ \"lightness\": -18 }]},{\"elementType\": \"labels.text.fill\",\"stylers\": [{ \"color\": \"#ffffff\" }]},{\"elementType\": \"labels.text.stroke\",\"stylers\": [{ \"visibility\": \"off\" }]},{\"featureType\": \"transit\",\"elementType\": \"geometry\",\"stylers\": [{ \"color\": \"#2ecc71\" },{ \"lightness\": -34 }]},{\"featureType\": \"administrative\",\"elementType\": \"geometry\",\"stylers\": [{ \"visibility\": \"on\" },{ \"color\": \"#333739\" },{ \"weight\": 0.8 }]},{\"featureType\": \"poi.park\",\"stylers\": [{ \"color\": \"#2ecc71\" }]},{\"featureType\": \"road\",\"elementType\": \"geometry.stroke\",\"stylers\": [{ \"color\": \"#333739\" },{ \"weight\": 0.3 },{ \"lightness\": 10 }]}]"},{"id":52,"name":"Souldisco","json":"[ { \"stylers\": [ { \"saturation\": -100 }, { \"gamma\": 0.8 }, { \"lightness\": 4 }, { \"visibility\": \"on\" } ] },{ \"featureType\": \"landscape.natural\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#5dff00\" }, { \"gamma\": 4.97 }, { \"lightness\": -5 }, { \"saturation\": 100 } ] } ]"},{"id":19,"name":"Subtle","json":"[\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"visibility\": \"off\" }\r\n\t\t]\r\n\t},{\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"saturation\": -70 },\r\n\t\t\t{ \"lightness\": 37 },\r\n\t\t\t{ \"gamma\": 1.15 }\r\n\t\t]\r\n\t},{\r\n\t\t\"elementType\": \"labels\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"gamma\": 0.26 },\r\n\t\t\t{ \"visibility\": \"off\" }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"road\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"lightness\": 0 },\r\n\t\t\t{ \"saturation\": 0 },\r\n\t\t\t{ \"hue\": \"#ffffff\" },\r\n\t\t\t{ \"gamma\": 0 }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"road\",\r\n\t\t\"elementType\": \"labels.text.stroke\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"visibility\": \"off\" }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"elementType\": \"geometry\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"lightness\": 20 }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"elementType\": \"geometry\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"lightness\": 50 },\r\n\t\t\t{ \"saturation\": 0 },\r\n\t\t\t{ \"hue\": \"#ffffff\" }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"administrative.province\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"visibility\": \"on\" },\r\n\t\t\t{ \"lightness\": -50 }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"administrative.province\",\r\n\t\t\"elementType\": \"labels.text.stroke\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"visibility\": \"off\" }\r\n\t\t]\r\n\t},{\r\n\t\t\"featureType\": \"administrative.province\",\r\n\t\t\"elementType\": \"labels.text\",\r\n\t\t\"stylers\": [\r\n\t\t\t{ \"lightness\": 20 }\r\n\t\t]\r\n\t} \r\n]"},{"id":15,"name":"Subtle Grayscale","json":"[\r\n {\r\n featureType: \"landscape\",\r\n stylers: [\r\n { saturation: -100 },\r\n { lightness: 65 },\r\n { visibility: \"on\" }\r\n ]\r\n },{\r\n featureType: \"poi\",\r\n stylers: [\r\n { saturation: -100 },\r\n { lightness: 51 },\r\n { visibility: \"simplified\" }\r\n ]\r\n },{\r\n featureType: \"road.highway\",\r\n stylers: [\r\n { saturation: -100 },\r\n { visibility: \"simplified\" }\r\n ]\r\n },{\r\n featureType: \"road.arterial\",\r\n stylers: [\r\n { saturation: -100 },\r\n { lightness: 30 },\r\n { visibility: \"on\" }\r\n ]\r\n },{\r\n featureType: \"road.local\",\r\n stylers: [\r\n { saturation: -100 },\r\n { lightness: 40 },\r\n { visibility: \"on\" }\r\n ]\r\n },{\r\n featureType: \"transit\",\r\n stylers: [\r\n { saturation: -100 },\r\n { visibility: \"simplified\" }\r\n ]\r\n },{\r\n featureType: \"administrative.province\",\r\n stylers: [\r\n { visibility: \"off\" }\r\n ]\r\n /** /\r\n },{\r\n featureType: \"administrative.locality\",\r\n stylers: [\r\n { visibility: \"off\" }\r\n ]\r\n },{\r\n featureType: \"administrative.neighborhood\",\r\n stylers: [\r\n { visibility: \"on\" }\r\n ]\r\n /**/\r\n },{\r\n featureType: \"water\",\r\n elementType: \"labels\",\r\n stylers: [\r\n { visibility: \"on\" },\r\n { lightness: -25 },\r\n { saturation: -100 }\r\n ]\r\n },{\r\n featureType: \"water\",\r\n elementType: \"geometry\",\r\n stylers: [\r\n { hue: \"#ffff00\" },\r\n { lightness: -25 },\r\n { saturation: -97 }\r\n ]\r\n }\r\n ]"},{"id":49,"name":"Subtle Green","json":"[\r\n {\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"saturation\": -100 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#00ffe6\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"saturation\": 100 },\r\n { \"hue\": \"#00ffcc\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n }\r\n]"},{"id":55,"name":"Subtle Greyscale Map","json":"[\r\n\t{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.local',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#ffffff' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'transit',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#bbbbbb' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 26 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#dddddd' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -3 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t}\r\n]"},{"id":50,"name":"The Endless Atlas","json":"[\r\n\t{\r\n \t\tfeatureType: 'all',\r\n\t\telementType: 'labels.text.stroke',\r\n \t\tstylers: [\r\n \t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n \t\t]\r\n \t},{\r\n \t\tfeatureType: 'all',\r\n\t\telementType: 'labels.icon',\r\n \t\tstylers: [\r\n \t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n \t \t]\r\n \t},{\r\n\t\tfeatureType: 'water',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#D1D3D4' },\r\n\t\t\t{ saturation: -88 },\r\n\t\t\t{ lightness: -7 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'landscape',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: -91 },\r\n\t\t\t{ lightness: -34 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#414042' },\r\n\t\t\t{ saturation: -98 },\r\n\t\t\t{ lightness: -60 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#E3EBE5' },\r\n\t\t\t{ saturation: -61 },\r\n\t\t\t{ lightness: 57 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'poi.park',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#E3EBE5' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 57 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n \tfeatureType: \"administrative\",\r\n \telementType: \"geometry.fill\",\r\n \tstylers: [\r\n \t{ visibility: \"off\" }\r\n \t]\r\n \t},{\r\n\t\tfeatureType: 'administrative.country',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#E3EBE5' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 81 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.province',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#E3EBE5' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 81 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.locality',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#FFFFFF' },\r\n\t\t\t{ saturation: 0 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.locality',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: 2 },\r\n\t\t\t{ lightness: 59 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.neighborhood',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 16 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.neighborhood',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 16 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'administrative.land_parcel',\r\n\t\telementType: 'all',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 16 },\r\n\t\t\t{ visibility: 'simplified' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#939598' },\r\n\t\t\t{ saturation: -98 },\r\n\t\t\t{ lightness: -8 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.highway',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#FFFFFF' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.arterial',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#6D6E71' },\r\n\t\t\t{ saturation: -98 },\r\n\t\t\t{ lightness: -43 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.arterial',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#FFFFFF' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.local',\r\n\t\telementType: 'geometry',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#000000' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: -100 },\r\n\t\t\t{ visibility: 'on' }\r\n\t\t]\r\n\t},{\r\n\t\tfeatureType: 'road.local',\r\n\t\telementType: 'labels',\r\n\t\tstylers: [\r\n\t\t\t{ hue: '#FFFFFF' },\r\n\t\t\t{ saturation: -100 },\r\n\t\t\t{ lightness: 100 },\r\n\t\t\t{ visibility: 'off' }\r\n\t\t]\r\n\t}\r\n]"},{"id":85,"name":"Totally Pink","json":"[\r\n\t{\r\n\t\t\"featureType\": \"landscape\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#F600FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.highway\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#DE00FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": -4.6000000000000085\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": -1.4210854715202004e-14\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.arterial\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FF009A\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"road.local\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#FF0098\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"water\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#EC00FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 72.4\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t},\r\n\t{\r\n\t\t\"featureType\": \"poi\",\r\n\t\t\"stylers\": [\r\n\t\t\t{\r\n\t\t\t\t\"hue\": \"#7200FF\"\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"saturation\": 49\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"lightness\": 0\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"gamma\": 1\r\n\t\t\t}\r\n\t\t]\r\n\t}\r\n]"},{"id":72,"name":"Transport for London","json":"[ { \"elementType\": \"labels.text\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"elementType\": \"labels.icon\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"elementType\": \"geometry.stroke\", \"stylers\": [ { \"visibility\": \"off\" } ] },{ \"featureType\": \"water\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#0099cc\" } ] },{ \"featureType\": \"road\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#00314e\" } ] },{ \"featureType\": \"transit.line\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#f0f0f0\" } ] },{ \"featureType\": \"landscape.man_made\", \"stylers\": [ { \"color\": \"#adbac9\" } ] },{ \"featureType\": \"landscape.natural\", \"stylers\": [ { \"color\": \"#adb866\" } ] },{ \"featureType\": \"poi\", \"stylers\": [ { \"color\": \"#f7c742\" } ] },{ \"featureType\": \"poi.park\", \"stylers\": [ { \"color\": \"#adb866\" } ] },{ \"featureType\": \"transit.station\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"color\": \"#ff8dd3\" } ] },{ \"featureType\": \"transit.station\", \"stylers\": [ { \"color\": \"#ff8dd3\" } ] },{ \"featureType\": \"transit.line\", \"elementType\": \"geometry.fill\", \"stylers\": [ { \"visibility\": \"on\" }, { \"color\": \"#808080\" } ] },{ } ]"},{"id":4,"name":"Tripitty","json":"[ { featureType: 'water', elementType: 'all', stylers: [ { color: '#193a70' }, { visibility: 'on' } ] }, { featureType: 'road', stylers: [ { 'visibility': 'off' } ] }, { featureType: 'transit', stylers: [ { 'visibility': 'off' } ] },{ featureType: 'administrative', stylers: [ { 'visibility': 'off' } ] }, { featureType: 'landscape', elementType: 'all', stylers: [ { 'color': '#2c5ca5' } ] },{ featureType: 'poi', stylers: [ { 'color': '#2c5ca5' } ] }, { elementType: 'labels', stylers: [ { 'visibility': 'off' } ] } ]"},{"id":8,"name":"Turquoise Water","json":"[{stylers:[{hue:'#16a085'},{saturation:0}]},{featureType:'road',elementType:'geometry',stylers:[{lightness:100},{visibility:'simplified'}]},{featureType:'road',elementType:'labels',stylers:[{visibility:'off'}]}]"},{"id":16,"name":"Unimposed Topography","json":"[\r\n {\r\n featureType: 'administrative',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n visibility: 'off'\r\n }\r\n ]\r\n }, {\r\n featureType: 'poi',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n visibility: 'off'\r\n }\r\n ]\r\n }, {\r\n featureType: 'road',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n visibility: 'off'\r\n }\r\n ]\r\n }, {\r\n featureType: 'transit',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n visibility: 'off'\r\n }\r\n ]\r\n }, {\r\n featureType: 'landscape',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n hue: '#727D82'\r\n }, {\r\n lightness: -30\r\n }, {\r\n saturation: -80\r\n }\r\n ]\r\n }, {\r\n featureType: 'water',\r\n elementType: 'all',\r\n stylers: [\r\n {\r\n visibility: 'simplified'\r\n }, {\r\n hue: '#F3F4F4'\r\n }, {\r\n lightness: 80\r\n }, {\r\n saturation: -80\r\n }\r\n ]\r\n }\r\n]"},{"id":70,"name":"Unsaturated Browns","json":"[\r\n{\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"hue\": \"#ff4400\" },\r\n { \"saturation\": -68 },\r\n { \"lightness\": -4 },\r\n { \"gamma\": 0.72 }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels.icon\" },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"hue\": \"#0077ff\" },\r\n { \"gamma\": 3.1 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"hue\": \"#00ccff\" },\r\n { \"gamma\": 0.44 },\r\n { \"saturation\": -33 }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"hue\": \"#44ff00\" },\r\n { \"saturation\": -23 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"hue\": \"#007fff\" },\r\n { \"gamma\": 0.77 },\r\n { \"saturation\": 65 },\r\n { \"lightness\": 99 }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"gamma\": 0.11 },\r\n { \"weight\": 5.6 },\r\n { \"saturation\": 99 },\r\n { \"hue\": \"#0091ff\" },\r\n { \"lightness\": -86 }\r\n ]\r\n },{\r\n \"featureType\": \"transit.line\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"lightness\": -48 },\r\n { \"hue\": \"#ff5e00\" },\r\n { \"gamma\": 1.2 },\r\n { \"saturation\": -23 }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"saturation\": -64 },\r\n { \"hue\": \"#ff9100\" },\r\n { \"lightness\": 16 },\r\n { \"gamma\": 0.47 },\r\n { \"weight\": 2.7 }\r\n ]\r\n }\r\n]"},{"id":14,"name":"Vintage","json":"[{\"stylers\": [{ \"visibility\": \"off\" }]},{\"featureType\": \"water\",\"stylers\": [{ \"visibility\": \"on\" },{ \"color\": \"#2f343b\" }]},{\"featureType\": \"landscape\",\"stylers\": [{ \"visibility\": \"on\" },{ \"color\": \"#703030\" }]},{\"featureType\": \"administrative\",\"elementType\": \"geometry.stroke\",\"stylers\": [{ \"visibility\": \"on\" },{ \"color\": \"#2f343b\" },{ \"weight\": 1 }]}]"},{"id":26,"name":"Vintage Blue","json":"[\r\n {\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative.province\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#004b76\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"landscape.natural\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#fff6cb\" }\r\n ]\r\n },\r\n {\r\n \"featureType\": \"administrative.country\",\r\n \"elementType\": \"geometry.stroke\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#7f7d7a\" },\r\n { \"lightness\": 10 },\r\n { \"weight\": 1 }\r\n ]\r\n }\r\n]"},{"id":40,"name":"Vitamin C","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#004358\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#fd7400\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" },\r\n { \"lightness\": -20 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" },\r\n { \"lightness\": -17 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.stroke\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" },\r\n { \"visibility\": \"on\" },\r\n { \"weight\": 0.9 }\r\n ]\r\n },{\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" },\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" },\r\n { \"lightness\": -10 }\r\n ]\r\n },{\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#1f8a70\" },\r\n { \"weight\": 0.7 }\r\n ]\r\n }\r\n]"},{"id":74,"name":"becomeadinosaur","json":"[\r\n {\r\n \"elementType\": \"labels.text\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.natural\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#f5f5f2\" },\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"transit\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.attraction\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.man_made\",\r\n \"elementType\": \"geometry.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" },\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.business\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.medical\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.place_of_worship\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.school\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.sports_complex\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" },\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" },\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.highway\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi\",\r\n \"elementType\": \"labels.icon\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#71c8d4\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"color\": \"#e5e8e7\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"color\": \"#8ba129\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"color\": \"#ffffff\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.sports_complex\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#c7c7c7\" },\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#a0d3d3\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"color\": \"#91b65d\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.park\",\r\n \"stylers\": [\r\n { \"gamma\": 1.51 }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"on\" }\r\n ]\r\n },{\r\n \"featureType\": \"poi.government\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"elementType\": \"labels\",\r\n \"stylers\": [\r\n { \"visibility\": \"off\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.arterial\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"road.local\",\r\n \"stylers\": [\r\n { \"visibility\": \"simplified\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\" },{\r\n \"featureType\": \"road\" },{\r\n },{\r\n \"featureType\": \"road.highway\" }\r\n]"},{"id":97,"name":"manushka","json":"[\r\n {\r\n \"featureType\": \"water\",\r\n \"stylers\": [\r\n { \"color\": \"#6ebeab\" }\r\n ]\r\n },{\r\n \"featureType\": \"road\",\r\n \"stylers\": [\r\n { \"color\": \"#b5a15b\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"geometry\",\r\n \"stylers\": [\r\n { \"color\": \"#f9f9f9\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#808080\" }\r\n ]\r\n },{\r\n \"featureType\": \"administrative.locality\",\r\n \"elementType\": \"labels.text.fill\",\r\n \"stylers\": [\r\n { \"color\": \"#808080\" }\r\n ]\r\n },{\r\n \"featureType\": \"landscape.natural.terrain\",\r\n \"stylers\": [\r\n { \"color\": \"#d0d0d0\" }\r\n ]\r\n },{\r\n }\r\n]"}];