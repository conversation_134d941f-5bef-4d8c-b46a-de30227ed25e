<aside class="page-sidebar">
    <div class="page-logo">
        <a href="<?php echo HOME_WITH_PATH; ?>" class="page-logo-link press-scale-down d-flex align-items-center position-relative">
            <img src="assets/img/logo.png" alt="ASP.NET Core 3.1" aria-roledescription="logo">
            <span class="page-logo-text mr-1"><?php echo $zpanel->getConfigByValue('title', 'value', $conn); ?></span>
        </a>
    </div>
    <nav id="js-primary-nav" class="primary-nav" role="navigation">
        <div class="nav-filter">
            <div class="position-relative">
                <input type="text" id="nav_filter_input" placeholder="Filter menu" class="form-control" tabindex="0">
                <a href="#" onclick="return false;" class="btn-primary btn-search-close js-waves-off" data-action="toggle" data-class="list-filter-active" data-target=".page-sidebar">
                    <i class="fal fa-chevron-up"></i>
                </a>
            </div>
        </div>
        <div class="info-card">
            <img src="<?php echo !$userLogin->recUserInfo('url', $conn) ? 'home/images/user.png' : 'http://' . $userLogin->recUserInfo('url', $conn); ?>" class="profile-image rounded-circle" alt="<?php echo $userLogin->recUserAccount('ID', $conn); ?>">
            <div class="info-card-text">
                <a href="#" class="d-flex align-items-center text-white">
                    <span class="text-truncate text-truncate-sm d-inline-block"><?php echo $userLogin->recUserAccount('ID', $conn); ?></span>
                </a>
                <span class="d-inline-block text-truncate text-truncate-sm"><?php echo $userLogin->recUserAccount('IsDeveloper', $conn) == '0' ? 'User' : 'Administrator'; ?></span>
            </div>
            <img src="assets/img/card-backgrounds/cover-5-lg.png" class="cover" alt="cover">
            <a href="#" onclick="return false;" class="pull-trigger-btn" data-action="toggle" data-class="list-filter-active" data-target=".page-sidebar" data-focus="nav_filter_input">
                <i class="fal fa-angle-down"></i>
            </a>
        </div>

        <ul id="js-nav-menu" class="nav-menu">
            <?php renderMenuItem('', HOME_WITH_PATH, 'fal fa-home', M_HOME, !isset($getURL)); ?>

            <?php
            // ค่าเริ่มต้น (จะถูกอัปเดตด้วย JS)
            $countDonate = 0;
            ?>
            <!-- User Account Section (for all users) -->
            <?php renderSection('บัญชีของฉัน', [
                ['account', 'จัดการบัญชี', 'fal fa-user-circle', [
                    ['my-account', '📊 ข้อมูลบัญชี', '?url=account/my-account', in_array('my-account', $finalURL)],
                    ['analytics', '📈 วิเคราะห์ข้อมูล', '?url=account/analytics', in_array('analytics', $finalURL)],
                    ['statistics', '📊 สถิติการใช้งาน', '?url=account/statistics', in_array('statistics', $finalURL)],
                    ['security-check', '🔒 ตรวจสอบความปลอดภัย', '?url=account/security-check', in_array('security-check', $finalURL)],
                    ['audit-log', '📋 ประวัติการใช้งาน', '?url=account/audit-log', in_array('audit-log', $finalURL)],
                    ['test-css', '🎨 ทดสอบ CSS', '?url=account/test-css', in_array('test-css', $finalURL)],
                    ['edit-account', '✏️ แก้ไขข้อมูล', '?url=account/edit-account', in_array('edit-account', $finalURL)],
                    ['history', '🛒 ประวัติการซื้อ', '?url=account/history', in_array('history', $finalURL)],
                    ['purchases', '💳 รายการซื้อ', '?url=account/purchases', in_array('purchases', $finalURL)]
                ]]
            ]); ?>

            <?php if ($userLogin->recUserAccount('IsDeveloper', $conn)): ?>
                <?php renderSection(TL_GAMEMASTER, [
                    ['manager_account','บัญขี', 'fal fa-server', [
                        ['manage-account', SM_ACCOUNT, '?url=manager_account/manage-account', in_array('manage-account', $finalURL)],
                        ['account-analytics-admin', '📈 วิเคราะห์บัญชีผู้เล่น', '?url=manager_account/analytics', in_array('account-analytics-admin', $finalURL)],
                        ['account-analytics-simple', '📊 วิเคราะห์ (ตัวอย่าง)', '?url=manager_account/analytics-simple', in_array('account-analytics-simple', $finalURL)],
                        ['account-statistics-admin', '📊 สถิติบัญชีรวม', '?url=manager_account/statistics', in_array('account-statistics-admin', $finalURL)],
                        ['account-security-admin', '🔒 ตรวจสอบความปลอดภัย', '?url=manager_account/security-overview', in_array('account-security-admin', $finalURL)],
                        ['test-db', '🔧 ทดสอบฐานข้อมูล', '?url=manager_account/test-db', in_array('test-db', $finalURL)]
                    ]],
                    ['manager_charecter','ตัวละคร', 'fal fa-users', [
                        ['character-statistics', 'สถิติตัวละคร', '?url=manager_charecter/character-statistics', in_array('character-statistics', $finalURL)],
                        ['character-analytics', 'การวิเคราะห์', '?url=manager_charecter/character-analytics', in_array('character-analytics', $finalURL)],
                        ['character-monitor', 'ตรวจสอบสด', '?url=manager_charecter/character-monitor', in_array('character-monitor', $finalURL)],
                        ['character-export-manager', 'ส่งออกข้อมูล', '?url=manager_charecter/character-export-manager', in_array('character-export-manager', $finalURL)],
                        ['manage-player', 'ตัวละครทั้งหมด', '?url=manager_charecter/manage-player', in_array('manage-player', $finalURL)],
                        ['manage-player-lastlogin', 'เข้าเกมส์ล่าสุด', '?url=manager_charecter/manage-player-lastlogin', in_array('manage-player-lastlogin', $finalURL)],
                        ['manage-player-online', 'ผู้เล่นออนไลน์ทั่งหมด', '?url=manager_charecter/manage-player-online', in_array('manage-player-online', $finalURL)],
                        ['manage-player-banned', 'โดนแบนทั้งหมด', '?url=manager_charecter/manage-player-banned', in_array('manage-player-banned', $finalURL)],
                        ['manage-player-offline-na', SM_CHARECTER_NA, '?url=manager_charecter/manage-player-offline-na', in_array('manage-player-offline-na', $finalURL)],
                        ['manage-player-offline-ca', SM_CHARECTER_CA, '?url=manager_charecter/manage-player-offline-ca', in_array('manage-player-offline-ca', $finalURL)],
                        ['manage-player-offline-po', SM_CHARECTER_PO, '?url=manager_charecter/manage-player-offline-po', in_array('manage-player-offline-po', $finalURL)],
                        ['manage-player-war', SM_CHARECTER_WAR, '?url=manager_charecter/manage-player-war', in_array('manage-player-war', $finalURL)]
                    ]],
                    ['dungeon_systems','ดันเจียน', 'fal fa-dungeon', [
                        ['manage-dungeonpoint', 'Dungeon Point', '?url=dungeon_systems/manage-dungeonpoint', in_array('manage-dungeonpoint', $finalURL)],
                        ['manage-dungeonranking-Party', 'Dungeon Ranking Party', '?url=dungeon_systems/manage-dungeonranking-Party', in_array('manage-dungeonranking-Party', $finalURL)],
                        ['manage-dungeonranking-single', 'Dungeon Ranking Single', '?url=dungeon_systems/manage-dungeonranking-single', in_array('manage-dungeonranking-single', $finalURL)],
                        ['manage-elite-dungeon', 'Elite Dungeon Info', '?url=dungeon_systems/manage-elite-dungeon', in_array('manage-elite-dungeon', $finalURL)]
                    ]],
                    ['manager_game_history', 'History', 'fal fa-server', [
                        ['see-guild', 'History Guild', '?url=manager_game_history/see-guild', in_array('see-guild', $finalURL)],
                        ['see-rename', 'History เปลียนชื่อ', '?url=manager_game_history/see-rename', in_array('see-rename', $finalURL)],
                        ['see-deletechar', 'Manager Recover Char', '?url=manager_game_history/see-deletechar', in_array('see-deletechar', $finalURL)],
                        ['see-split-iteminventory', 'SPLIT ITEM INVENTORY', '?url=manager_game_history/see-split-iteminventory', in_array('see-split-iteminventory', $finalURL)],
                        ['see-split-itemEquipment', 'SPLIT ITEM EQUIPMENT', '?url=manager_game_history/see-split-itemEquipment', in_array('see-split-itemEquipment', $finalURL)],
                        ['see-split-iteminwarehouse', 'SPLIT ITEM WAREHOUSE', '?url=manager_game_history/see-split-iteminwarehouse', in_array('see-split-iteminwarehouse', $finalURL)],
                        ['see-mycashitem', 'History Cashinvent', '?url=manager_game_history/see-mycashitem', in_array('see-mycashitem', $finalURL)],
                        ['see-donate', 'History เติมเงิน', '?url=manager_game_history/see-donate', in_array('see-donate', $finalURL)],
                        ['see-rps', 'History RP-SHOP', '?url=manager_game_history/see-rps', in_array('see-rps', $finalURL)]
                    ]],
                    ['manager_content','กิจกรรม', 'fal fa-server', [
                        ['manage-facebook-share', 'จัดการกิจกรรม Share', '?url=manager_content/manage-facebook-share', in_array('manage-facebook-share', $finalURL)],
                        ['manage-thailotto', 'จัดการกิจกรรม ทายหวย', '?url=manager_content/manage-thailotto', in_array('manage-thailotto', $finalURL)]
                    ]],
                    ['manager_mail','เมลล์', 'fal fa-envelope', [
                        ['mail-statistics', 'สถิติ Mail', '?url=manager_mail/mail-statistics', in_array('mail-statistics', $finalURL)],
                        ['mail-analytics', 'การวิเคราะห์', '?url=manager_mail/mail-analytics', in_array('mail-analytics', $finalURL)],
                        ['mail-monitor', 'ตรวจสอบสด', '?url=manager_mail/mail-monitor', in_array('mail-monitor', $finalURL)],
                        ['mail-export-manager', 'ส่งออกข้อมูล', '?url=manager_mail/mail-export-manager', in_array('mail-export-manager', $finalURL)],
                        ['mail-settings', 'การตั้งค่า', '?url=manager_mail/mail-settings', in_array('mail-settings', $finalURL)],
                        ['see-mailsend', 'การส่งเมลล์', '?url=manager_mail/see-mailsend', in_array('see-mailsend', $finalURL)],
                        ['see-mailsend-delete', 'ประวัติการลบการส่งเมลล์', '?url=manager_mail/see-mailsend-delete', in_array('see-mailsend-delete', $finalURL)],
                        ['see-mailreceiver', 'ตรวจสอบรับเมลล์', '?url=manager_mail/see-mailreceiver', in_array('see-mailreceiver', $finalURL)],
                        ['see-mailreceiver-delete', 'ประวัติการลบการรับเมลล์', '?url=manager_mail/see-mailreceiver-delete', in_array('see-mailreceiver-delete', $finalURL)]
                    ]],
                    ['manager_war','Mission War', 'fal fa-server', [
                        ['manage-warpoint', 'ตรวจสอบคะแนนวอ', '?url=manager_war/manage-warpoint', in_array('manage-warpoint', $finalURL)],
                        ['manage-bringer', 'ตรวจสอบ Bringer', '?url=manager_war/manage-bringer', in_array('manage-bringer', $finalURL)],
                        ['manage-Forcecalibur', 'ตรวจสอบ Forcecalibur', '?url=manager_war/manage-Forcecalibur', in_array('see-mailReceiver', $finalURL)]
                    ]],
                    ['manager_store', 'ระบบเติมเงิน', 'fal fa-server', [
                        ['manage-donate', 'ตรวจสอบการ Donate', '?url=manager_store/manage-donate', in_array('manage-donate', $finalURL)],
                        ['manage-adddonate', 'แก้ไขกการเติมเงิน', '?url=manager_store/manage-adddonate', in_array('manage-adddonate', $finalURL), $userLogin->recUserPerm($conn, 'dev_masster', 'menu')]
                    ]],
                    ['manager_game','การจัดการ บริการ', 'fal fa-server', [
                        ['manage-voucher-item', 'จัดการแลกบัตรแคส', '?url=manager_game/manage-voucher-item', in_array('manage-voucher-item', $finalURL)],
                        ['manage-time-item', 'จัดการเมลล์ไอเท็ม', '?url=manager_game/manage-time-item', in_array('manage-time-item', $finalURL)],
                        ['manage-reward', 'จัดการ Reward', '?url=manager_game/manage-reward', in_array('manage-reward', $finalURL)]
                    ]],
                    ['game_systems', 'จัดการระบบเกมส์', 'fal fa-server', [
                        ['manage-item','Items system', '?url=game_systems/manage-item', in_array('manage-item', $finalURL), $userLogin->recUserPerm($conn, 'dev_masster', 'menu')],
                        ['advanced-editor','advanced editor', '?url=game_systems/advanced-editor', in_array('advanced-editor', $finalURL), $userLogin->recUserPerm($conn, 'dev_masster', 'menu')],
                        ['manage-pets', 'Pets System', '?url=game_systems/manage-pets', in_array('manage-pets', $finalURL), $userLogin->recUserPerm($conn, 'dev_masster', 'menu')],
                        ['rewardpoint-system', 'Reward point System', '?url=game_systems/rewardpoint-system', in_array('rewardpoint-system', $finalURL)],
                        ['npc_yui-Systems', 'Npc Yui System', '?url=game_systems/npc_yui-Systems', in_array('npc_yui-Systems', $finalURL)],
                        ['npc_cashShop-system', 'Cash Shop Editor', '?url=game_systems/npc_cashShop-system', in_array('npc_cashShop-system', $finalURL)],
                        ['npc_shop_editor', 'NPC Shop Editor', '?url=game_systems/npc_shop_editor', in_array('npc_shop_editor', $finalURL)],
                        ['npc_editor-system', 'npc Yui System (Beta)', '?url=game_systems/npc_yui-system_beta', in_array('npc_yui-system_beta', $finalURL)],
                        ['missionwar_rewards', 'Mission WarEvent', '?url=game_systems/missionwar_rewards', in_array('missionwar_rewards', $finalURL)],
                        ['event_data_edit_ui', 'All Event', '?url=game_systems/event_data_edit_ui', in_array('event_data_edit_ui', $finalURL)],
                        ['event_inventory_ui', 'ส่งของกิจกรรม Event ตกหล่น', '?url=game_systems/event_inventory_ui', in_array('event_inventory_ui', $finalURL)],
                        ['username_manager', 'จัดการ Username ในกิจกรรม', '?url=game_systems/username_manager', in_array('username_manager', $finalURL)],
                        ['redeem_code_generator', 'Redeem Code ทั่วไป', '?url=game_systems/redeem_code_generator', in_array('redeem_code_generator', $finalURL)],
                        ['character_redeem_code_generator', 'Redeem Code ตัวละคร', '?url=game_systems/character_redeem_code_generator', in_array('character_redeem_code_generator', $finalURL)],
                       
                    ]]
                ], ['countvoucher' => 0, 'countmailitem' => 0, 'countreward' => 0, 'countdonate' => $countDonate]); ?>
                    <script>
                    // อัปเดต badge countdonate และ countvoucher แบบเรียลไทม์
                    function updateCountDonateBadge() {
                        fetch('files/notification/fetch-donate.php')
                            .then(response => response.json())
                            .then(data => {
                                if (typeof data.countdonate_notification !== 'undefined') {
                                    let badge = document.querySelector('.badge.countdonate');
                                    if (badge) {
                                        badge.textContent = data.countdonate_notification;
                                    }
                                }
                            });
                    }
                    function updateCountVoucherBadge() {
                        fetch('files/notification/fetch-voucher.php')
                            .then(response => response.json())
                            .then(data => {
                                if (typeof data.countvoucher_notification !== 'undefined') {
                                    let badge = document.querySelector('.badge.countvoucher');
                                    if (badge) {
                                        badge.textContent = data.countvoucher_notification;
                                    }
                                }
                            });
                    }
                    setInterval(updateCountDonateBadge, 5000);
                    setInterval(updateCountVoucherBadge, 5000);
                    document.addEventListener('DOMContentLoaded', function() {
                        updateCountDonateBadge();
                        updateCountVoucherBadge();
                    });
                    </script>

                <?php if ($userLogin->recUserPerm($conn, 'web_admin', 'menu')): ?>
                    <?php renderSection(TL_ADMIN_AND_DEVALOPER, [
                        ['web-admin', M_ADMINISTRATION, 'fal fa-server', [
                            ['configuration', SM_WEBCONFIGURATION, '?url=web-admin/configuration', in_array('configuration', $finalURL)],
                            ['team', SM_TEAM, '?url=web-admin/team', in_array('team', $finalURL)],
                            ['web-log', SM_WEBLOG, '?url=web-admin/web-log', in_array('web-log', $finalURL)],
                            ['downloads', SM_DOWNLOADFIGURATION, '?url=web-admin/downloads', in_array('downloads', $finalURL)]
                        ]]
                    ]); ?>
                <?php endif; ?>

                <?php if ($userLogin->recUserPerm($conn, 'server_admin', 'menu')): ?>
                    <?php renderSection(TL_SERVER, [
                        ['server-admin', M_SERVER_ADMINISTRATION, 'fal fa-server', [
                            ['manager-linuxufw-account', 'จัดการ ให้สิทธิ account', '?url=server-admin/manager-linuxufw-account', in_array('manager-linuxufw-account', $finalURL)],
                            ['manager-linuxssh', 'จัดการ Linux Server', '?url=server-admin/manager-linuxssh', in_array('manager-linuxssh', $finalURL)],
                            ['manager-linuxfirewall', 'จัดการ Firewall', '?url=server-admin/manager-linuxfirewall', in_array('manager-linuxfirewall', $finalURL)]
                        ]]
                    ]); ?>
                <?php endif; ?>
            <?php endif; ?>
        </ul>
        <div class="filter-message js-filter-message bg-success-600"></div>
    </nav>

    <div class="nav-footer shadow-top">
        <a href="#" onclick="return false;" data-action="toggle" data-class="nav-function-minify" class="hidden-md-down">
            <i class="fal fa-angle-right"></i>
            <i class="fal fa-angle-right"></i>
        </a>
        <ul class="list-table m-auto nav-footer-buttons">
            <li><a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Chat logs"><i class="fal fa-comments"></i></a></li>
            <li><a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Support Chat"><i class="fal fa-life-ring"></i></a></li>
            <li><a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Make a call"><i class="fal fa-phone"></i></a></li>
        </ul>
    </div>
</aside>
<?php
function isMenuActive($key) {
    global $finalURL;
    return in_array($key, $finalURL);
}

function renderMenuItem($class, $href, $icon, $text, $active = false) {
    echo '<li class="' . ($active ? 'active' : '') . ($class ? ' ' . $class : '') . '">
        <a href="' . $href . '">
            <i class="' . $icon . '"></i>
            <span class="nav-link-text">' . htmlspecialchars($text) . '</span>
        </a>
    </li>';
}

function renderSection($title, $items, $badges = []) {
    echo '<li class="nav-title">' . htmlspecialchars($title);
    foreach ($badges as $key => $value) {
        $badgeClass = ($key === 'countdonate' || $key === 'countmailitem') ? 'warning' :
                      ($key === 'countvoucher' ? 'primary' : 'success');
        echo '<span class="badge badge-' . $badgeClass . ' ml-2 ' . $key . '">' . $value . '</span>';
    }
    echo '</li>';

    foreach ($items as $item) {
        if (!is_array($item) || count($item) < 4) continue;

        [$mainKey, $mainText, $mainIcon, $subItems] = $item;
        $mainActive = isMenuActive($mainKey);
        $hasActiveSub = false;

        if (!empty($subItems) && is_array($subItems)) {
            foreach ($subItems as $subItem) {
                if (!is_array($subItem) || count($subItem) < 3) continue;
                [$subKey] = array_pad($subItem, 5, true);
                if (isMenuActive($subKey)) {
                    $hasActiveSub = true;
                    break;
                }
            }
        }

        // สร้าง class แบบยืดหยุ่น
        $classList = [];
        if ($mainActive) $classList[] = 'active';
        if ($hasActiveSub) $classList[] = 'open';
        $classAttr = !empty($classList) ? 'class="' . implode(' ', $classList) . '"' : '';

        echo '<li ' . $classAttr . '>';
        echo '<a href="javascript:void(0);" title="' . htmlspecialchars($mainText) . '" data-filter-tags="' . htmlspecialchars($mainText) . '">
                <i class="' . htmlspecialchars($mainIcon) . '"></i>
                <span class="nav-link-text" data-i18n="nav.' . htmlspecialchars($mainText) . '">' . htmlspecialchars($mainText) . '</span>
              </a>';

        if (!empty($subItems) && is_array($subItems)) {
            echo '<ul>';
            foreach ($subItems as $subItem) {
                if (!is_array($subItem) || count($subItem) < 3) continue;

                $paddedSubItem = array_pad($subItem, 5, true);
                $subKey = $paddedSubItem[0];
                $subText = $paddedSubItem[1];
                $subHref = $paddedSubItem[2];
                $condition = $paddedSubItem[4] ?? true;

                // ตรวจสอบ active จาก URL
                $subActive = isMenuActive($subKey);

                if ($condition) {
                    echo '<li ' . ($subActive ? 'class="active"' : '') . '>
                            <a href="' . htmlspecialchars($subHref) . '" data-filter-tags="' . htmlspecialchars($subText) . '">
                                <span class="nav-link-text" data-i18n="nav.' . htmlspecialchars($subText) . '">' . htmlspecialchars($subText) . '</span>
                            </a>
                          </li>';
                }
            }
            echo '</ul>';
        }
        echo '</li>';
    }
}
?>