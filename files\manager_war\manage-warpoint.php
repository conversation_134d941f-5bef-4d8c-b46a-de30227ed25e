<?php 
$user->restrictionUser(true, $conn); 
$params = array();
$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );

$selectLordOfWarData = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_LordOfWar_Data_table";
$selectLordOfWarDataQuery = sqlsrv_query($conn, $selectLordOfWarData, $params, $options);
$resLordOfWarData = sqlsrv_fetch_array($selectLordOfWarDataQuery, SQLSRV_FETCH_ASSOC);
$startdate = $resLordOfWarData['startDate'];
$refreshdate = $resLordOfWarData['refreshDate'];
$overjoin= $resLordOfWarData['overWarJoinCount'];
$updatetime = ($resLordOfWarData['updateDurationMinute']/60)/24;
$refreshtimemin = ($resLordOfWarData['refreshDurationMinute']/60)/24;
$warmagicnum = $resLordOfWarData['warchannelMagicNum'];
?>
<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
<!--แก้ไข ไฟล์ที่ \home\switch\mstudio.php assets\js\app\mstudio.js-->
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-chart-area"></i> ระบบตรวจสอบ <span class="fw-300">คะแนนวอ</span>
    </h1>
    <div class="subheader-block d-lg-flex align-items-center">
        <div class="d-flex mr-4">
            <div class="mr-2">
                <span class="peity-donut"
                    data-peity="{ &quot;fill&quot;: [&quot;#967bbd&quot;, &quot;#ccbfdf&quot;],  &quot;innerRadius&quot;: 14, &quot;radius&quot;: 20 }"
                    style="display: none;">7/10</span><svg class="peity" height="40" width="40">
                    <path
                        d="M 20 0 A 20 20 0 1 1 0.9788696740969307 26.18033988749895 L 6.685208771867851 24.326237921249266 A 14 14 0 1 0 20 6"
                        data-value="7" fill="#967bbd"></path>
                    <path
                        d="M 0.9788696740969307 26.18033988749895 A 20 20 0 0 1 19.999999999999996 0 L 19.999999999999996 6 A 14 14 0 0 0 6.685208771867851 24.326237921249266"
                        data-value="3" fill="#ccbfdf"></path>
                </svg>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">Start วันที่</label>
                <h4 class="font-weight-bold mb-0"><?php echo date('d-m-Y G:i:s',strtotime($startdate)); ?></h4>
            </div>
        </div>
        <div class="d-flex mr-0">
            <div class="mr-2">
                <span class="peity-donut"
                    data-peity="{ &quot;fill&quot;: [&quot;#2196F3&quot;, &quot;#9acffa&quot;],  &quot;innerRadius&quot;: 14, &quot;radius&quot;: 20 }"
                    style="display: none;">3/10</span><svg class="peity" height="40" width="40">
                    <path
                        d="M 20 0 A 20 20 0 0 1 39.02113032590307 26.18033988749895 L 33.31479122813215 24.326237921249263 A 14 14 0 0 0 20 6"
                        data-value="3" fill="#2196F3"></path>
                    <path
                        d="M 39.02113032590307 26.18033988749895 A 20 20 0 1 1 19.999999999999996 0 L 19.999999999999996 6 A 14 14 0 1 0 33.31479122813215 24.326237921249263"
                        data-value="7" fill="#9acffa"></path>
                </svg>
            </div>
            <div>
                <label class="fs-sm mb-0 mt-2 mt-md-0">Refresh ข้อมูล</label>
                <h4 class="font-weight-bold mb-0">
                    <?php echo date('d-m-Y G:i:s',strtotime($refreshdate)); ?></h4>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-primary-300 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo $overjoin; ?> รอบ
                    <small class="m-0 l-h-n">จำนวนตัดรอบ</small>
                </h3>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-warning-400 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo $updatetime; ?> วัน
                    <small class="m-0 l-h-n">อัพเดทเวลา</small>
                </h3>
            </div>

        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-success-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo $refreshtimemin; ?> วัน
                    <small class="m-0 l-h-n">Refresh เวลา</small>
                </h3>
            </div>

        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="p-3 bg-info-200 rounded overflow-hidden position-relative text-white mb-g">
            <div class="">
                <h3 class="display-4 d-block l-h-n m-0 fw-500">
                    <?php echo $warmagicnum-1; ?> -> <?php echo $warmagicnum; ?>
                    <small class="m-0 l-h-n">วอรอบถัดไป</small>
                </h3>
            </div>
        </div>
    </div>

</div>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager War Point <span class="fw-300"><i>ตรวจสอบคะแนนวอ</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table id="" class="table table-sm table-bordered w-100">
                            <thead>
                                <tr>
									<th>UserNum</th>
									<th>UserID</th>
                                    <th>CharacterIdx</th>
                                    <th>Name</th>
                                    <th>WarChannelType</th>
                                    <th>WarchannelMagicNum</th>
                                    <th>WarPoint</th>
                                    <th>ValidPoint</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php

                                // generic function to get page
                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                    $rows = array();
                                    $i = 0;
                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                        array_push($rows, $row);
                                        $i++;
                                    }
                                    return $rows;
                                }

                                // Set the number of rows to be returned on a page.
                                $rowsPerPage = 400;

                                // Define and execute the query.  
                                // Note that the query is executed with a "scrollable" cursor.
                                $sql = "SELECT * FROM  [".DATABASE_SV."].[dbo].cabal_LordOfWar_point_table ORDER BY WarchannelMagicNum desc";

                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                if (!$stmt)
                                    die(print_r(sqlsrv_errors(), true));

                                // Get the total number of rows returned by the query.
                                $rowsReturned = sqlsrv_num_rows($stmt);
                                if ($rowsReturned === false)
                                    die(print_r(sqlsrv_errors(), true));
                                elseif ($rowsReturned == 0) {
                                    echo W_NOTHING_RETURNED;
                                    //exit();
                                } else {
                                    /* Calculate number of pages. */
                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                }

                                // Display the selected page of data.
                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                foreach ($page as $row) {
										
	
								$selectChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table Where CharacterIdx = '$row[0]'";
								$selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);
								$resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC);
								$name = $userLogin->thaitrans($resChars['Name']);

								$usernum = floor($row[0]/16);
								$selectPlayerData = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$usernum'";
								$selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $params, $options);
								$selectPlayerFetch = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);


                            ?>
                                <tr>
									<td><?php echo $selectPlayerFetch['UserNum']; ?></td>
									<td><?php echo $selectPlayerFetch['ID']; ?></td>
                                    <td><?php echo $row[0]; ?></td>
                                    <td><?php echo $userLogin->thaitrans($resChars['Name']); ?></td>
                                    <td><?php echo $row[1]; ?></td>
                                    <td><?php echo $row[2]; ?></td>
                                    <td><?php echo $row[3]; ?></td>
                                    <td><?php echo $row[4]; ?></td>
                                    <td>
                                        <form method="post" name="j_add_waritemscore" action="">
                                            <div class="j_alert"></div>
                                            <div class="form-inline">
                                                <select name="mailsend" class="custom-select my-1 mr-sm-2"
                                                    id="inlineFormCustomSelectPref">
                                                    <option value="1">ยาวอ</option>
                                                    <option value="2">ยาวอ+M</option>
                                                    <option value="3">Token War</option>
													<option value="4">Fire Shard</option>
                                                </select>
                                                <input type="hidden" name="charsidx" value="<?php echo $row[0]; ?>">
                                                <button type="submit" class="btn btn-primary btn-sm"><i class="fa fa-chevron-circle-right"></i>
                                                    Action</button>
                                            </div>
                                        </form>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>

                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php }else{ echo "ไม่มีสิทธิ์เข้าถึง"; }  ?>