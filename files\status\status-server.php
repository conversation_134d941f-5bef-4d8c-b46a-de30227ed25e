<?php
ob_start();
session_start();

require('../_app/dbinfo.inc.php');
require('../_app/general_config.inc.php');
require('../_app/variables.php');

// sql inject protection
require('../_app/php/sql_inject.php');
require('../_app/php/sql_check.php');

// zpanel class
require('../_app/php/zpanel.class.php');
$zpanel = new zpanel();

require('../_app/php/userLogin.class.php');
$userLogin = new userLogged();

$max = 3.0;

 $ch16x = $userLogin->countuseronlineforchanell($conn,'16','1') ? $userLogin->countuseronlineforchanell($conn,'16','1') : '0';
  $ch1x = $userLogin->countuseronlineforchanell($conn,'1','1') ? $userLogin->countuseronlineforchanell($conn,'1','1') : '0';
   $ch2x = $userLogin->countuseronlineforchanell($conn,'2','1') ? $userLogin->countuseronlineforchanell($conn,'2','1') : '0';
    $ch3x = $userLogin->countuseronlineforchanell($conn,'3','1') ? $userLogin->countuseronlineforchanell($conn,'3','1') : '0';
	 $ch4x = $userLogin->countuseronlineforchanell($conn,'4','1') ? $userLogin->countuseronlineforchanell($conn,'4','1') : '0';
	  $countplayer = $userLogin->countuser($conn, $getCharID) ? $userLogin->countuser($conn, $getCharID) : '0';
	   $counchar = $userLogin->countcha($conn) ? $userLogin->countcha($conn) : '0';


$ch16 = $ch16x;
$ch1 = $ch1x*$max+50;
$ch2 = $ch2x*$max+50;
$ch3 = $ch3x*$max+50;
$ch4 = $ch4x*$max+50;
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>MR.ONE DEVALLOPER</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="../assets/css/app/status.css">
  <link rel="stylesheet" href="../assets/css/bootstrap/bootstrap.min.css">
	<link href="https://fonts.googleapis.com/css?family=Kanit" rel="stylesheet"> 
  <style type="text/css" media="screen">
          body {
           font-family: 'Kanit', sans-serif;
            background-color:;
            color:#fff;
          }

 </style>
</head>
<body>
<div id="container">
<table style="width:220px ">

    <tbody>

      <tr>
        <td style="border-bottom:#404040 1px dashed "><div class="text-warning">Account: </div></td>
        <td style="border-bottom:#404040 1px dashed"><div class="text-primary"><?php echo $countplayer; ?></td></div>

      </tr>
	   <tr>
        <td style="border-bottom:#404040 1px dashed "><div class="text-warning">Char: </div></td>
         <td style="border-bottom:#404040 1px dashed"><div class="text-primary"><?php echo $counchar; ?></td></div>

      </tr>
	   <tr>
        <td style="border-bottom:#404040 1px dashed"><div class="text-danger">War: </div></td>
        <td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInner" style="width: <?php echo $ch16; ?>%;"></div><div class="barLabel">100</div></div></td>
      </tr>
      <tr>
        <td style="border-bottom:#404040 1px dashed"><div class="text-success">Chanell1: </div></td>
		<?php if($ch1 >= 1 AND $ch1 < 50){ ?>
        <td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerlow" style="width: <?php echo $ch1; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch1 >= 50 AND $ch1 < 70) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnermid" style="width: <?php echo $ch1; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch1 >= 70 AND $ch1 < 99 ) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerhight" style="width: <?php echo $ch1; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch1 >= 99 AND $ch1 > 100) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerfull" style="width:100%;"></div><div class="barLabel">100</div></div></td>
		<?php } ?>
      </tr>
      <tr>
        <td style="border-bottom:#404040 1px dashed"><div class="text-success">Chanell2: </div></td>
       <?php if($ch2 >= 1 AND $ch2 < 50){ ?>
        <td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerlow" style="width: <?php echo $ch2; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch2 >= 50 AND $ch2 < 70) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnermid" style="width: <?php echo $ch2; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch2 >= 70 AND $ch2 < 99 ) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerhight" style="width: <?php echo $ch2; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch2 >= 99 AND $ch2 < 100) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerfull" style="width: <?php echo $ch2; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } ?>
      </tr>
	   <tr>
        <td style="border-bottom:#404040 1px dashed"><div class="text-success">Chanell3: </div></td>
      <?php if($ch3 >= 1 AND $ch3 < 50){ ?>
        <td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerlow" style="width: <?php echo $ch3; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch3 >= 50 AND $ch3 < 70) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnermid" style="width: <?php echo $ch3; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch3 >= 70 AND $ch3 < 99 ) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerhight" style="width: <?php echo $ch3; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch3 >= 99 AND $ch3 < 100) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerfull" style="width: <?php echo $ch3; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } ?>
      </tr>
	   <tr>
        <td style="border-bottom:#404040 1px dashed"><div class="text-success">Chanell4: </div></td>
       <?php if($ch4 >= 1 AND $ch4 < 50){ ?>
        <td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerlow" style="width: <?php echo $ch4; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch4 >= 50 AND $ch4 < 70) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnermid" style="width: <?php echo $ch4; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch4 >= 70 AND $ch4 < 99 ) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerhight" style="width: <?php echo $ch4; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } else if($ch4 >= 99 AND $ch4 < 100) { ?>
		<td style="border-bottom:#404040 1px dashed"><div class="barOuter"><div class="barInnerfull" style="width: <?php echo $ch4; ?>%;"></div><div class="barLabel">100</div></div></td>
		<?php } ?>
      </tr>
    </tbody>
  </table>
                     
</div>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
</body></html>