 <div class="row mb-4">
     <div class="col-12">
         <div class="player-profile-card">
             <div class="profile-header">
                 <div class="profile-avatar">
                     <img src="<?php echo htmlspecialchars($selectWebAuthDataFetch['picture'] ?? 'assets/img/default-avatar.png'); ?>"
                         alt="Profile" class="avatar-img">
                     <div
                         class="status-dot <?php echo ($resPlayer && $resPlayer['Login'] == "1") ? 'online' : 'offline'; ?>">
                     </div>
                 </div>
                 <div class="profile-info">
                     <h2 class="player-name"><?php echo htmlspecialchars($resPlayer['ID'] ?? 'N/A'); ?></h2>
                     <div class="player-meta">
                         <span
                             class="user-type <?php echo ($resPlayer && $resPlayer['IsDeveloper'] ? 'developer' : 'player'); ?>">
                             <?php echo ($resPlayer && $resPlayer['IsDeveloper'] ? 'Developer' : 'Player'); ?>
                         </span>
                         <span class="user-num">UserNum:
                             <?php echo htmlspecialchars($resPlayer['UserNum'] ?? 'N/A'); ?></span>
                     </div>
                     <div class="status-badges">
                         <?php if ($resPlayer && $resPlayer['AuthType'] == "2"): ?>
                         <span class="status-badge banned">ถูกแบน</span>
                         <?php else: ?>
                         <span class="status-badge active">บัญชีปกติ</span>
                         <?php endif; ?>
                         <span
                             class="status-badge <?php echo ($resPlayer && $resPlayer['Login'] == "1") ? 'online' : 'offline'; ?>">
                             <?php echo ($resPlayer && $resPlayer['Login'] == "1") ? 'ออนไลน์' : 'ออฟไลน์'; ?>
                         </span>
                     </div>
                 </div>
                 <div class="profile-actions">
                     <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
                     <button type="button" data-toggle="modal" data-target=".modal-transparent-password"
                         class="action-btn primary">
                         <i class="fal fa-key"></i> รหัสผ่าน
                     </button>
                     <button type="button" data-toggle="modal" data-target=".modal-transparent-subpassword"
                         class="action-btn warning">
                         <i class="fal fa-lock"></i> รหัสย่อย
                     </button>
                     <button type="button" data-toggle="modal" data-target=".modal-cash-transparent"
                         class="action-btn success">
                         <i class="fal fa-coins"></i> จัดการ Cash
                     </button>
                     <?php } ?>
                     <button type="button" data-toggle="modal" data-target="#bannedModel" class="action-btn danger">
                         <i class="fal fa-user-shield"></i> สถานะ
                     </button>
                 </div>
             </div>
         </div>
     </div>
 </div>

 <!-- Enhanced Cash Management Modal -->
 <div class="modal fade modal-cash-transparent modal-enhanced" id="example-modal-backdrop-transparent" tabindex="-1"
     role="dialog" aria-hidden="true">
     <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
         <div class="modal-content">
             <div class="modal-header">
                 <div>
                     <h4 class="modal-title mb-0">
                         <i class="fal fa-coins mr-2"></i>จัดการเงินในเกม
                     </h4>
                     <p class="mb-0 mt-1 opacity-75">เพิ่มหรือลดจำนวนเงินในเกมของผู้เล่น</p>
                 </div>
                 <button type="button" class="btn btn-link text-white p-0" data-dismiss="modal" aria-label="Close">
                     <i class="fal fa-times" style="font-size: 1.5rem;"></i>
                 </button>
             </div>
             <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                 <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                // get data info
                                $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                $selectPlayerDataParam = array();
                                $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                if (isset($_POST['btn-savechange-cash']))
                                {
                                    // variables
                                    $input_cash = strip_tags(trim($_POST['plr-cash']));
                                    $input_cashbonus = strip_tags(trim($_POST['plr-cashbonus']));
                                    $input_Rpoint= strip_tags(trim($_POST['plr-Rpoint']));
                                    $input_tpoint= strip_tags(trim($_POST['plr-Tpoint']));
                                    // condition
                                    if (empty($input_cash)){
                                        $input_cash = 0;
                                    }
                                    if (empty($input_cashbonus)){
                                        $input_cashbonus = 0;
                                    }
                                    if (empty($input_Rpoint)){
                                        $input_Rpoint = 0;
                                    }
                                    if (empty($input_tpoint)){
                                        $input_tpoint = 0;
                                    }
                                   
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        // update
                                        $NewPwd = $cat_password;
                                        //$NewPwd = substr(md5(mktime()),0,8);
                                        //$NewPwd = PWDENCRYPT('$NewPwd');
                                        $updatePlayer = "UPDATE " . DATABASE_CCA . ".dbo.CashAccount SET Cash = Cash+'$input_cash', CashBonus = CashBonus+'$input_cashbonus', Rpoint = Rpoint+'$input_Rpoint' WHERE UserNum = '$PlayerUserNum'";
                                        $updatePlayerParam = array();
                                        $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer, $updatePlayerParam);
                                        if (sqlsrv_rows_affected($updatePlayerQuery)){

                                            $updateTpoint = "UPDATE " . DATABASE_NBL . ".dbo.Point SET TPoint = TPoint+'$input_tpoint' WHERE UserNum = '$PlayerUserNum'";
                                            $updateTpointParam = array();
                                            $updateTpointQuery = sqlsrv_query($conn, $updateTpoint, $updateTpointParam);
                                                if (sqlsrv_rows_affected($updatePlayerQuery)){
                                                // generate a log
                                                $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'Update all Point player account', "playerId: { {$UserID} } Cash={ {$input_cash} } CashBonus={ {$input_cash} } Rpoint={ {$input_Rpoint} } Tpoint={ {$input_tpoint} }updated!");
                                                  //header('Location: ?url=game_manager/manage-account&update=true');
                                                echo '<script type="text/javascript">';
                                                echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","Player Id: {'.$UserID.'}  Cash={'.$input_cash.'} CashBonus={'.$input_cash.'} Rpoint={'.$input_Rpoint.'} CashAccount account was successfully updated!","success");';
                                                echo '});</script>';

                                                } else {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC . '","error");';
                                            echo '});</script>';
                                        }


                                        }else{
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC . '","error");';
                                            echo '});</script>';
                                        }
                                }
                                
                        
                            ?>
                 <div class="modal-body form-enhanced">
                     <!-- Current Balance Display -->
                     <div class="row mb-4">
                         <div class="col-12">
                             <div class="alert alert-info"
                                 style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none; color: white;">
                                 <h6 class="mb-2"><i class="fal fa-info-circle mr-2"></i>ยอดเงินปัจจุบัน</h6>
                                 <div class="row text-center">
                                     <div class="col-3">
                                         <strong><?php echo number_format($selectCashDataFetch['Cash'] ?? 0); ?></strong>
                                         <br><small>Cash</small>
                                     </div>
                                     <div class="col-3">
                                         <strong><?php echo number_format($selectCashDataFetch['CashBonus'] ?? 0); ?></strong>
                                         <br><small>Cash Bonus</small>
                                     </div>
                                     <div class="col-3">
                                         <strong><?php echo number_format($selectTpointDataFetch['Rpoint'] ?? 0); ?></strong>
                                         <br><small>Rewrard Point</small>
                                     </div>
                                     <div class="col-3">
                                         <strong><?php echo number_format($selectGemDataFetch['ForcegemHave'] ?? 0); ?></strong>
                                         <br><small>Gem</small>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>

                     <!-- Input Fields -->
                     <div class="row">
                         <div class="col-md-6 mb-4">
                             <label class="form-label font-weight-bold">
                                 <i class="fal fa-coins mr-2 text-primary"></i>Cash
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text bg-primary text-white">
                                         <i class="fal fa-coins"></i>
                                     </span>
                                 </div>
                                 <input type="number" name="plr-cash" value="0" class="form-control"
                                     placeholder="จำนวนที่ต้องการเพิ่ม/ลด" min="-999999999" max="999999999">
                             </div>
                             <small class="text-muted">ใส่เลขบวกเพื่อเพิ่ม หรือเลขลบเพื่อลด</small>
                         </div>

                         <div class="col-md-6 mb-4">
                             <label class="form-label font-weight-bold">
                                 <i class="fal fa-gem mr-2 text-warning"></i>Cash Bonus
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text bg-warning text-white">
                                         <i class="fal fa-gem"></i>
                                     </span>
                                 </div>
                                 <input type="number" name="plr-cashbonus" value="0" class="form-control"
                                     placeholder="จำนวนที่ต้องการเพิ่ม/ลด" min="-999999999" max="999999999">
                             </div>
                             <small class="text-muted">ใส่เลขบวกเพื่อเพิ่ม หรือเลขลบเพื่อลด</small>
                         </div>

                         <div class="col-md-6 mb-4">
                             <label class="form-label font-weight-bold">
                                 <i class="fal fa-star mr-2 text-success"></i>R Point
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text bg-success text-white">
                                         <i class="fal fa-star"></i>
                                     </span>
                                 </div>
                                 <input type="number" name="plr-Rpoint" value="0" class="form-control"
                                     placeholder="จำนวนที่ต้องการเพิ่ม/ลด" min="-999999999" max="999999999">
                             </div>
                             <small class="text-muted">ใส่เลขบวกเพื่อเพิ่ม หรือเลขลบเพื่อลด</small>
                         </div>

                         <div class="col-md-6 mb-4">
                             <label class="form-label font-weight-bold">
                                 <i class="fal fa-trophy mr-2 text-info"></i>T Point
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text bg-info text-white">
                                         <i class="fal fa-trophy"></i>
                                     </span>
                                 </div>
                                 <input type="number" name="plr-Tpoint" value="0" class="form-control"
                                     placeholder="จำนวนที่ต้องการเพิ่ม/ลด" min="-999999999" max="999999999">
                             </div>
                             <small class="text-muted">ใส่เลขบวกเพื่อเพิ่ม หรือเลขลบเพื่อลด</small>
                         </div>
                     </div>

                     <!-- Warning Notice -->
                     <div class="alert alert-warning"
                         style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; color: white;">
                         <div class="d-flex align-items-center">
                             <i class="fal fa-exclamation-triangle mr-3" style="font-size: 1.5rem;"></i>
                             <div>
                                 <h6 class="mb-1">คำเตือน</h6>
                                 <p class="mb-0 small">การเปลี่ยนแปลงจำนวนเงินจะมีผลทันที และไม่สามารถยกเลิกได้
                                     กรุณาตรวจสอบข้อมูลให้ถูกต้องก่อนบันทึก</p>
                             </div>
                         </div>
                     </div>
                 </div>

                 <div class="modal-footer">
                     <button type="button" class="btn btn-outline-secondary action-btn" data-dismiss="modal">
                         <i class="fal fa-times mr-2"></i>ยกเลิก
                     </button>
                     <button type="submit" name="btn-savechange-cash" class="btn btn-gradient-primary action-btn">
                         <i class="fal fa-save mr-2"></i>บันทึกการเปลี่ยนแปลง
                     </button>
                 </div>
             </form>
         </div>
     </div>
 </div>




 <!-- Modal เปลียนพาสเวิส id-->
 <div class="modal fade modal-transparent-password" tabindex="-1" role="dialog" style="display: none;"
     aria-hidden="true">
     <div class="modal-dialog modal-transparent" role="document">
         <div class="modal-content">
             <div class="modal-header">
                 <h4 class="modal-title text-white">
                     Chang New Password
                     <small class="m-0 text-white opacity-70">
                         เปลียนพาสเวิส
                     </small>
                 </h4>
                 <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                     <span aria-hidden="true">
                         <i class="fal fa-times">
                         </i>
                     </span>
                 </button>
             </div>
             <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                if (isset($_POST['btn-savechangePassword'])){
                                    // variables
                                    $input_mail = strip_tags(trim($_POST['plr-email']));
                                    $input_password = strip_tags(trim($_POST['plr-pass']));
                                    $plr_isdev = strip_tags(trim($_POST['plr-isdev']));
                                    // condition
                                    if (empty($input_mail))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_EMPTY_EMAIL . '","error");';
                                        echo '});</script>';
                                    }
                                    else if (empty($input_password))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_R_PASS_EMPTY . '","error");';
                                        echo '});</script>';
                                    }
                                    else if (!filter_var($input_mail, FILTER_VALIDATE_EMAIL))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_WRONG_EMAIL . '","error");';
                                        echo '});</script>';
                                    } else {
                                                                        // get data info
                                        $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                        $selectPlayerDataParam = array();
                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                        $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        $finalPass = $resPlayer['Password'];
                                        $NewPwd = $input_password;
                                        //$NewPwd = substr(md5(mktime()),0,8);
                                        if ($plr_isdev == '-1'){
                                            $plr_isdev = $resPlayer['IsDeveloper'];
                                        }
                                        //$NewPwd = PWDENCRYPT('$NewPwd');
                                        $updatePlayer = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET Email = '$input_mail', Password = PWDENCRYPT('$NewPwd'), IsDeveloper = '$plr_isdev' WHERE UserNum = '$PlayerUserNum'";
                                        $updatePlayerParam = array();
                                        $updatePlayerQuery = sqlsrv_query($conn, $updatePlayer, $updatePlayerParam);
                                        if (sqlsrv_rows_affected($updatePlayerQuery)){
                                            // generate a log
                                            $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'update password player account', "playerId: { {$UserID} } password account was successfully updated!");
                                            if ($plr_isdev != '-1'){
                                                // update isDev UsersData table
                                                $updatePlayerData = "UPDATE " . DATABASE_ACC . ".dbo.cabal_auth_table SET IsDeveloper = '$plr_isdev' WHERE UserNum = '$PlayerUserNum'";
                                                $updatePlayerDataParam = array();
                                                $updatePlayerDataQuery = sqlsrv_query($conn, $updatePlayerData, $updatePlayerDataParam);
                                            }
                                            //header('Location: ?url=game_manager/manage-account&update=true');
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","' . S_PLR_ACCOUNT_UPDATED . '","success");';
                                            echo '});</script>';
                                        }
                                        else
                                        {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
                                            echo '});</script>';
                                        }
                                    }
                                }
                            ?>
             <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                 <div class="modal-body">
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="Username">
                                 <?php echo T_ACCOUNTINFO; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text" id="Username">
                                         <i class="ni ni-user fs-xl">
                                         </i>
                                     </span>
                                 </div>
                                 <input type="text" name="username" id="basic-addon1"
                                     value="<?php echo $resPlayer['ID']; ?>" class="form-control" placeholder="Username"
                                     aria-label="Username" aria-describedby="basic-addon1" readonly>
                             </div>
                         </div>
                     </div>
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="email">
                                 <?php echo T_EMAIL; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text">
                                         <i class="fal fa-envelope">
                                         </i>
                                     </span>
                                 </div>
                                 <input type="text" name="plr-email" id="basic-addon1"
                                     value="<?php echo $resPlayer['Email']; ?>" class="form-control" placeholder="Email"
                                     aria-label="Email" aria-describedby="basic-addon1">
                             </div>
                         </div>
                     </div>
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="npassword">
                                 <?php echo T_PASSWORD; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text">
                                         <i class="fal fa-key">
                                         </i>
                                     </span>
                                 </div>
                                 <input type="password" name="plr-pass" id="basic-addon1" class="form-control"
                                     placeholder="New password" aria-label="New password"
                                     aria-describedby="basic-addon1">
                             </div>
                         </div>
                     </div>
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="npassword">
                                 <?php echo T_ISDEV; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text">
                                         <i class="fal fa-wrench">
                                         </i>
                                     </span>
                                 </div>
                                 <select name="plr-isdev" class="custom-select">
                                     <option value="-1">สถานะไอดี :
                                         <?php echo $isDev = ($resPlayer['IsDeveloper'] ? 'DEV' : 'Player'); ?>
                                     </option>
                                     <option value="1">DEV
                                     </option>
                                     <option value="0">Player
                                     </option>
                                 </select>
                             </div>
                         </div>
                     </div>
                 </div>
                 <div class="modal-footer">
                     <button type="button" class="btn btn-secondary waves-effect waves-themed"
                         data-dismiss="modal">Close
                     </button>
                     <button type="submit" name="btn-savechangePassword"
                         class="btn btn-primary waves-effect waves-themed">
                         <?php echo B_SAVECHANGES; ?>
                     </button>
                 </div>
         </div>
         </form>
     </div>
 </div>


 <!-- Modal เปลียนชัพพาสเวิส id-->
 <div class="modal fade modal-transparent-subpassword" tabindex="-1" role="dialog" style="display: none;"
     aria-hidden="true">
     <div class="modal-dialog modal-transparent" role="document">
         <div class="modal-content">
             <div class="modal-header">
                 <h4 class="modal-title text-white">
                     Chang New Sub Password
                     <small class="m-0 text-white opacity-70">
                         เปลียนชัพพาสเวิส
                     </small>
                 </h4>
                 <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                     <span aria-hidden="true">
                         <i class="fal fa-times">
                         </i>
                     </span>
                 </button>
             </div>
             <?php
                            $getCategoryID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                if (isset($_POST['btn-savechangeSubpass'])){
                                    // variables
                                    $input_password = strip_tags(trim($_POST['plr-pass']));
                                    $pwtype= strip_tags(trim($_POST['inuput-pwtype']));
                                    // condition
									if (empty($input_password))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . W_R_PASS_EMPTY . '","error");';
                                        echo '});</script>';
                                    }
									else if (empty($getCategoryID))
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!"," ไอดีผิดพลาด","error");';
                                        echo '});</script>';
									}
                                    else if ($pwtype == "-1" || $pwtype < "0")
                                    {
                                        echo '<script type="text/javascript">';
                                        echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!"," เลือกชัพพาสเวิสที่จะเปลียน","error");';
                                        echo '});</script>';
                                    } else {
                                                                        // get data info
                                        $selectPlayerData = "SELECT * FROM " . DATABASE_ACC . ".dbo.cabal_auth_table WHERE UserNum = '$getCategoryID'";
                                        $selectPlayerDataParam = array();
                                        $selectPlayerDataQuery = sqlsrv_query($conn, $selectPlayerData, $selectPlayerDataParam);
                                        $resPlayer = sqlsrv_fetch_array($selectPlayerDataQuery, SQLSRV_FETCH_ASSOC);
                                        $UserID = $resPlayer['ID'];
                                        $PlayerUserNum = $resPlayer['UserNum'];
                                        $finalPass = $resPlayer['Password'];
                                        $NewPwd = $input_password;

                                        if (sqlsrv_rows_affected($selectPlayerDataQuery)){
                                            // generate a log
                                            $zpanel->generateWebLog($conn, '2', $PlayerUserNum, 'update Sub password player account', "playerId: { {$UserID} } password Type {$pwtype} account was successfully updated!");

                                            // updateSubpassData
                                            $updateSubpassData = "EXECUTE " . DATABASE_ACC . ".dbo.cabal_sp_sub_password_set ?,?,?";
                                            $updateSubpassDataParam = array($PlayerUserNum,$pwtype,$input_password);
                                            $updateSubpassDataQuery = sqlsrv_query($conn, $updateSubpassData, $updateSubpassDataParam);
											 if ($updateSubpassDataQuery) {
													echo '<script type="text/javascript">';
													echo 'setTimeout(function () { Swal.fire("สำเร็จ !!!","' . S_PLR_ACCOUNT_UPDATED . '","success");';
													echo '});</script>';
											 }else{
													echo '<script type="text/javascript">';
													echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
													echo '});</script>'; 
											 }
                                        }
                                        else
                                        {
                                            echo '<script type="text/javascript">';
                                            echo 'setTimeout(function () { Swal.fire("ผิดพลาด !!!","' . E_PLR_UPDATE_ACC .'","error");';
                                            echo '});</script>';
                                        }
                                    }
                                }
                            ?>
             <form method="post" ref="needconfirm" enctype="multipart/form-data" action="">
                 <div class="modal-body">
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="Username">
                                 <?php echo T_ACCOUNTINFO; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text" id="Username">
                                         <i class="ni ni-user fs-xl">
                                         </i>
                                     </span>
                                 </div>
                                 <input type="text" name="username" id="basic-addon1"
                                     value="<?php echo $resPlayer['ID']; ?>" class="form-control" placeholder="Username"
                                     aria-label="Username" aria-describedby="basic-addon1" readonly>
                             </div>
                         </div>
                     </div>

                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="npassword">
                                 <?php echo T_PASSWORD; ?>
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text">
                                         <i class="fal fa-key">
                                         </i>
                                     </span>
                                 </div>
                                 <input type="password" name="plr-pass" id="basic-addon1" class="form-control"
                                     placeholder="New password" aria-label="New password"
                                     aria-describedby="basic-addon1">
                             </div>
                         </div>
                     </div>
                     <div class="form-row">
                         <div class="col-md-12 mb-3">
                             <label class="form-label" for="npassword">
                                 เลือก SubPassword ที่จะเปลียน
                             </label>
                             <div class="input-group">
                                 <div class="input-group-prepend">
                                     <span class="input-group-text">
                                         <i class="fal fa-wrench">
                                         </i>
                                     </span>
                                 </div>
                                 <select name="inuput-pwtype" class="custom-select">
                                     <option value="-1">เลือก SubPassword ที่จะเปลียน
                                     </option>
                                     <option value="1">SubPassword เข้าเกมส์
                                     </option>
                                     <option value="2">SubPassword ล็อคช่องเก็บของ
                                     </option>
                                     <option value="3">SubPassword ล็อคของในตัวละคร
                                     </option>
                                 </select>
                             </div>
                         </div>
                     </div>
                 </div>
                 <div class="modal-footer">
                     <button type="button" class="btn btn-secondary waves-effect waves-themed"
                         data-dismiss="modal">Close
                     </button>
                     <button type="submit" name="btn-savechangeSubpass"
                         class="btn btn-primary waves-effect waves-themed">
                         <?php echo B_SAVECHANGES; ?>
                     </button>
                 </div>
         </div>
         </form>
     </div>
 </div>


<!-- Modal Ban id-->
 <div class="modal fade" id="bannedModel" tabindex="-1" role="dialog" aria-labelledby="financialModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="myModalLabel">
                            แบนไอดีพร้อมข้อมูลการแบน
                        </h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;
                            </span>
                        </button>
                    </div>
                    <form method="post" name="j_add_banned" action="">
                        <div class="modal-body">
                            <div class="col-lg-12 j_alert"></div>
                            <h5 class="text-danger">
                                การแบนไอดีต้องลงรายระเอียดการแบนให้ครบเพื่อง่ายต่อการตรวจสอบ
                            </h5>
                            <div class="form-group">
                                <div class="demo">
                                    <?php 
                                        $selectAcc = "SELECT * FROM ".DATABASE_ACC.".dbo.cabal_auth_table WHERE UserNum = '$userNum'";
                                        $selectAccQuery = sqlsrv_query($conn, $selectAcc, array());
                                        $selectAccFetch = sqlsrv_fetch_array($selectAccQuery, SQLSRV_FETCH_ASSOC);
                                    ?>
                                    <?php if ($selectAccFetch['AuthType'] == "1"): ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1" checked>
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2">
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php elseif($selectAccFetch['AuthType'] == "2"): ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1">
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2" checked>
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php else: ?>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch1radio"
                                            name="input_authType" value="1">
                                        <label class="custom-control-label" for="customSwitch1radio">UnBan
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="radio" class="custom-control-input" id="customSwitch2radio"
                                            name="input_authType" value="2" checked>
                                        <label class="custom-control-label" for="customSwitch2radio">Banned
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="plr-gc">รายระเอียด
                                </label>
                                <input class="form-control" id="input_detail" type="text" name="input_detail"
                                    placeholder="เพิ่มรายระเอียดข้อมูลการแบนไอดี">
                            </div>
                            <div class="form-group">
                                <label for="plr-gc">หลักฐาน
                                </label>
                                <input class="form-control" id="input_premise" type="text" name="input_premise"
                                    placeholder="เพิ่มหลักฐานการแบน (ถ้ามี)">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-warning waves-effect waves-themed"
                                data-dismiss="modal">
                                <?php echo B_CLOSE; ?>
                            </button>
                            <button type="submit" class="btn btn-warning waves-effect waves-themed">ยืนยันข้อมูล
                            </button>
                            <input type="hidden" name="CustomerID" value="<?php echo strip_tags(trim($_GET['id'])); ?>">
                            <input type="hidden" name="AdminCID"
                                value="<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>">
                        </div>
                    </form>
                </div>
            </div>
        </div>