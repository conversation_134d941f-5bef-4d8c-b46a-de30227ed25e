<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');


function littleEndianHexToBigInt($hex) {
    $bytes = str_split($hex, 2);
    $reversed = implode('', array_reverse($bytes));
    return hexdec($reversed);
}

function bigIntToLittleEndianHex($value, $lengthBytes) {
    $hex = dechex($value);
    $hex = str_pad($hex, $lengthBytes * 2, '0', STR_PAD_LEFT);
    $bytes = str_split($hex, 2);
    return implode('', array_reverse($bytes));
}

function splitInventory($characterId, $binaryData) {
    $items = [];
    $hex = bin2hex($binaryData);
    $maxLength = strlen($hex);
    $pos = 0;

    while (($pos + 60) <= $maxLength) {
        $chunk = substr($hex, $pos, 60);
        if ($chunk === str_repeat('00', 60)) {
            $pos += 60;
            continue;
        }

        $KindIdx    = littleEndianHexToBigInt(substr($chunk, 0, 16));
        $ItemIndex  = $KindIdx & 0x6000fff;
        $Serial     = littleEndianHexToBigInt(substr($chunk, 16, 16));
        $Option     = littleEndianHexToBigInt(substr($chunk, 32, 16));
        $Slot       = littleEndianHexToBigInt(substr($chunk, 48, 4));
        $Period     = littleEndianHexToBigInt(substr($chunk, 52, 8));

        $hexData =
            bigIntToLittleEndianHex($KindIdx, 8) .
            bigIntToLittleEndianHex($Serial, 8) .
            bigIntToLittleEndianHex($Option, 8) .
            bigIntToLittleEndianHex($Slot, 2) .
            bigIntToLittleEndianHex($Period, 4);

        $items[$Slot] = [
            'KindIdx'   => $KindIdx,
            'itemIndex' => $ItemIndex,
            'Serial'    => $Serial,
            'Option'    => $Option,
            'Period'    => $Period,
            'HexData'   => strtoupper($hexData)
        ];
        $pos += 60;
    }

    return $items;
}

if (isset($_POST['update_inventory']) && isset($_POST['hex'])) {
    // รับข้อมูล hex string จาก textarea (ไม่ต้องแปลงเป็น binary)
    $hex = preg_replace('/^0x/i', '', $_POST['hex']);
    // อัปเดตลง DB (CharacterIdx = 16) โดยตรงเป็น hex string
    $query = "UPDATE " . DATABASE_SV . ".dbo.cabal_inventory_table SET Data = CONVERT(varbinary(max), ?, 2) WHERE CharacterIdx = 16";
    $stmt = sqlsrv_query($conn, $query, array($hex));
    if ($stmt) {
        echo 'Update Success';
    } else {
        echo 'Update Failed';
    }
    exit;
}

if (isset($_GET['ajax'])) {
    $items = [];
    $query = "SELECT CharacterIdx, Data FROM " . DATABASE_SV . ".dbo.cabal_inventory_table WHERE CharacterIdx = 16";
    $stmt = sqlsrv_query($conn, $query);
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $items = splitInventory($row['CharacterIdx'], $row['Data']);
    }
    echo json_encode($items);
    exit;
}
?>
