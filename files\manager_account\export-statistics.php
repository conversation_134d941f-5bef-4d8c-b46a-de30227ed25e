<?php 
$zpanel->checkSession(true);

// Get parameters
$period = isset($_GET['period']) ? $_GET['period'] : '30';
$comparison = isset($_GET['comparison']) ? $_GET['comparison'] : 'none';
$format = isset($_GET['export']) ? $_GET['export'] : 'csv';

// Time periods
$periods = [
    '7' => '7 วันล่าสุด',
    '30' => '30 วันล่าสุด',
    '90' => '3 เดือนล่าสุด',
    '365' => '1 ปีล่าสุด'
];

// Get statistics data
$selectOverallStats = "SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -$period, GETDATE()) THEN 1 END) as active_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -$period, GETDATE()) THEN 1 END) as new_accounts,
    AVG(PlayTime) as avg_playtime,
    SUM(PlayTime) as total_playtime,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as currently_online
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";

$overallQuery = sqlsrv_query($conn, $selectOverallStats);
$overallStats = sqlsrv_fetch_array($overallQuery, SQLSRV_FETCH_ASSOC);

// Get daily trend data
$selectDailyTrend = "SELECT 
    CAST(LoginTime as DATE) as activity_date,
    COUNT(DISTINCT UserNum) as unique_players,
    COUNT(*) as total_logins,
    AVG(DATEDIFF(minute, LoginTime, ISNULL(LogoutTime, GETDATE()))) as avg_session_time
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$period, GETDATE())
    GROUP BY CAST(LoginTime as DATE)
    ORDER BY activity_date DESC";

$dailyTrendQuery = sqlsrv_query($conn, $selectDailyTrend);
$dailyTrend = array();
while ($row = sqlsrv_fetch_array($dailyTrendQuery, SQLSRV_FETCH_ASSOC)) {
    $dailyTrend[] = $row;
}

// Get financial stats
$selectFinancialStats = "SELECT 
    SUM(c.Cash) as total_cash,
    SUM(c.CashBonus) as total_cash_bonus,
    SUM(c.CashTotal) as total_cash_all,
    SUM(c.Reward) as total_rewards,
    COUNT(CASE WHEN c.Cash > 0 THEN 1 END) as accounts_with_cash
    FROM [".DATABASE_CCA."].[dbo].cabal_cash_table c";

$financialStatsQuery = sqlsrv_query($conn, $selectFinancialStats);
$financialStats = sqlsrv_fetch_array($financialStatsQuery, SQLSRV_FETCH_ASSOC);

// Export based on format
switch ($format) {
    case 'csv':
        exportCSV($overallStats, $dailyTrend, $financialStats, $period);
        break;
    case 'excel':
        exportExcel($overallStats, $dailyTrend, $financialStats, $period);
        break;
    case 'pdf':
        exportPDF($overallStats, $dailyTrend, $financialStats, $period);
        break;
    default:
        exportCSV($overallStats, $dailyTrend, $financialStats, $period);
}

function exportCSV($overallStats, $dailyTrend, $financialStats, $period) {
    $filename = 'statistics_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Overall Statistics
    fputcsv($output, ['สถิติรวม (' . $period . ' วันล่าสุด)'], ',');
    fputcsv($output, ['รายการ', 'ค่า'], ',');
    fputcsv($output, ['บัญชีทั้งหมด', number_format($overallStats['total_accounts'])], ',');
    fputcsv($output, ['ผู้เล่นที่ใช้งาน', number_format($overallStats['active_accounts'])], ',');
    fputcsv($output, ['สมาชิกใหม่', number_format($overallStats['new_accounts'])], ',');
    fputcsv($output, ['เวลาเล่นเฉลี่ย (นาที)', round($overallStats['avg_playtime'], 2)], ',');
    fputcsv($output, ['เวลาเล่นรวม (นาที)', number_format($overallStats['total_playtime'])], ',');
    fputcsv($output, ['ออนไลน์ปัจจุบัน', number_format($overallStats['currently_online'])], ',');
    fputcsv($output, [''], ','); // Empty row
    
    // Financial Statistics
    fputcsv($output, ['สถิติทางการเงิน'], ',');
    fputcsv($output, ['รายการ', 'ค่า'], ',');
    fputcsv($output, ['Cash รวมทั้งหมด', number_format($financialStats['total_cash_all'])], ',');
    fputcsv($output, ['Cash ปกติ', number_format($financialStats['total_cash'])], ',');
    fputcsv($output, ['Cash Bonus', number_format($financialStats['total_cash_bonus'])], ',');
    fputcsv($output, ['Reward Points รวม', number_format($financialStats['total_rewards'])], ',');
    fputcsv($output, ['บัญชีที่มี Cash', number_format($financialStats['accounts_with_cash'])], ',');
    fputcsv($output, [''], ','); // Empty row
    
    // Daily Trend
    fputcsv($output, ['แนวโน้มรายวัน'], ',');
    fputcsv($output, ['วันที่', 'ผู้เล่นที่แตกต่าง', 'การเข้าสู่ระบบทั้งหมด', 'เวลาเซสชันเฉลี่ย (นาที)'], ',');
    
    foreach ($dailyTrend as $day) {
        fputcsv($output, [
            $day['activity_date'],
            $day['unique_players'],
            $day['total_logins'],
            round($day['avg_session_time'], 2)
        ], ',');
    }
    
    fclose($output);
    exit;
}

function exportExcel($overallStats, $dailyTrend, $financialStats, $period) {
    // For now, export as CSV with Excel-friendly format
    // In a real implementation, you would use PHPSpreadsheet library
    exportCSV($overallStats, $dailyTrend, $financialStats, $period);
}

function exportPDF($overallStats, $dailyTrend, $financialStats, $period) {
    // For now, show HTML version that can be printed as PDF
    // In a real implementation, you would use libraries like TCPDF or mPDF
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>สถิติบัญชีรวม - <?php echo date('Y-m-d H:i:s'); ?></title>
        <style>
            body { font-family: 'Sarabun', Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 30px; }
            .section h3 { color: #333; border-bottom: 2px solid #0088cc; padding-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .number { text-align: right; }
            @media print { body { margin: 0; } }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>สถิติบัญชีรวม</h1>
            <p>ช่วงเวลา: <?php echo $period; ?> วันล่าสุด | สร้างเมื่อ: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <div class="section">
            <h3>สถิติรวม</h3>
            <table>
                <tr><th>รายการ</th><th>ค่า</th></tr>
                <tr><td>บัญชีทั้งหมด</td><td class="number"><?php echo number_format($overallStats['total_accounts']); ?></td></tr>
                <tr><td>ผู้เล่นที่ใช้งาน</td><td class="number"><?php echo number_format($overallStats['active_accounts']); ?></td></tr>
                <tr><td>สมาชิกใหม่</td><td class="number"><?php echo number_format($overallStats['new_accounts']); ?></td></tr>
                <tr><td>เวลาเล่นเฉลี่ย (ชั่วโมง)</td><td class="number"><?php echo round($overallStats['avg_playtime'] / 60, 2); ?></td></tr>
                <tr><td>ออนไลน์ปัจจุบัน</td><td class="number"><?php echo number_format($overallStats['currently_online']); ?></td></tr>
            </table>
        </div>
        
        <div class="section">
            <h3>สถิติทางการเงิน</h3>
            <table>
                <tr><th>รายการ</th><th>ค่า</th></tr>
                <tr><td>Cash รวมทั้งหมด</td><td class="number"><?php echo number_format($financialStats['total_cash_all']); ?></td></tr>
                <tr><td>Cash ปกติ</td><td class="number"><?php echo number_format($financialStats['total_cash']); ?></td></tr>
                <tr><td>Cash Bonus</td><td class="number"><?php echo number_format($financialStats['total_cash_bonus']); ?></td></tr>
                <tr><td>Reward Points รวม</td><td class="number"><?php echo number_format($financialStats['total_rewards']); ?></td></tr>
                <tr><td>บัญชีที่มี Cash</td><td class="number"><?php echo number_format($financialStats['accounts_with_cash']); ?></td></tr>
            </table>
        </div>
        
        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </body>
    </html>
    <?php
    exit;
}
?>
