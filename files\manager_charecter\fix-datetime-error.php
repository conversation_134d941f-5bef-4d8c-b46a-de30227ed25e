<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bug"></i> แก้ไข DateTime Error
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา DateTime format() Error</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ปัญหา DateTime format() error ได้รับการแก้ไขแล้ว
                </div>
                
                <h5>❌ ปัญหาที่เกิดขึ้น</h5>
                <div class="alert alert-danger">
                    <h6>Error Message:</h6>
                    <pre><code>Fatal error: Uncaught Error: Call to a member function format() on string 
in character-statistics.php:1450</code></pre>
                    
                    <h6 class="mt-3">สาเหตุ:</h6>
                    <ul class="mb-0">
                        <li><strong>SQL Query:</strong> MIN(CreateDate) และ MAX(CreateDate) return เป็น string ไม่ใช่ DateTime object</li>
                        <li><strong>PHP Code:</strong> พยายามเรียก ->format() บน string</li>
                        <li><strong>SQLSRV Driver:</strong> ไม่ auto-convert DateTime เป็น PHP DateTime object</li>
                    </ul>
                </div>
                
                <h5>✅ วิธีแก้ไข</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ โค้ดเดิม (ผิด)</h6>
                            </div>
                            <div class="card-body">
                                <h6>SQL Query:</h6>
                                <pre><code>MIN(CreateDate) as oldest_character,
MAX(CreateDate) as newest_character</code></pre>
                                
                                <h6>PHP Code:</h6>
                                <pre><code>echo $creation['oldest_character']->format('d/m/Y');</code></pre>
                                
                                <p class="mb-0"><strong>ปัญหา:</strong> string ไม่มี method format()</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ โค้ดใหม่ (ถูก)</h6>
                            </div>
                            <div class="card-body">
                                <h6>SQL Query:</h6>
                                <pre><code>CONVERT(VARCHAR, MIN(CreateDate), 103) as oldest_character,
CONVERT(VARCHAR, MAX(CreateDate), 103) as newest_character</code></pre>
                                
                                <h6>PHP Code:</h6>
                                <pre><code>echo htmlspecialchars($creation['oldest_character']);</code></pre>
                                
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> แสดงวันที่ในรูปแบบ dd/mm/yyyy</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแก้ไข</h5>
                <?php
                try {
                    echo "<h6>ทดสอบ SQL Query ที่แก้ไขแล้ว:</h6>";
                    
                    $sql = "SELECT 
                                COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 1 THEN 1 END) as created_today,
                                COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 7 THEN 1 END) as created_week,
                                COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 30 THEN 1 END) as created_month,
                                COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) <= 365 THEN 1 END) as created_year,
                                COUNT(CASE WHEN DATEDIFF(day, CreateDate, GETDATE()) > 365 THEN 1 END) as created_old,
                                CONVERT(VARCHAR, MIN(CreateDate), 103) as oldest_character,
                                CONVERT(VARCHAR, MAX(CreateDate), 103) as newest_character
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                        echo "<div class='alert alert-success'>";
                        echo "<h6><i class='fal fa-check-circle'></i> SQL Query ทำงานได้!</h6>";
                        
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm table-striped'>";
                        echo "<tr><td><strong>สร้างวันนี้</strong></td><td><span class='badge badge-success'>" . number_format($row['created_today']) . "</span></td></tr>";
                        echo "<tr><td><strong>สร้างสัปดาห์นี้</strong></td><td><span class='badge badge-info'>" . number_format($row['created_week']) . "</span></td></tr>";
                        echo "<tr><td><strong>สร้างเดือนนี้</strong></td><td><span class='badge badge-warning'>" . number_format($row['created_month']) . "</span></td></tr>";
                        echo "<tr><td><strong>สร้างปีนี้</strong></td><td><span class='badge badge-danger'>" . number_format($row['created_year']) . "</span></td></tr>";
                        echo "<tr><td><strong>สร้างเก่ากว่า 1 ปี</strong></td><td><span class='badge badge-dark'>" . number_format($row['created_old']) . "</span></td></tr>";
                        
                        if (!empty($row['oldest_character'])) {
                            echo "<tr><td><strong>ตัวละครเก่าสุด</strong></td><td><strong>" . htmlspecialchars($row['oldest_character']) . "</strong></td></tr>";
                        }
                        
                        if (!empty($row['newest_character'])) {
                            echo "<tr><td><strong>ตัวละครใหม่สุด</strong></td><td><strong>" . htmlspecialchars($row['newest_character']) . "</strong></td></tr>";
                        }
                        
                        echo "</table></div>";
                        echo "</div>";
                        
                        // ทดสอบ data types
                        echo "<div class='alert alert-info mt-3'>";
                        echo "<h6><i class='fal fa-info-circle'></i> ข้อมูล Data Types:</h6>";
                        echo "<ul>";
                        echo "<li><strong>oldest_character type:</strong> " . gettype($row['oldest_character']) . "</li>";
                        echo "<li><strong>newest_character type:</strong> " . gettype($row['newest_character']) . "</li>";
                        echo "<li><strong>oldest_character value:</strong> " . var_export($row['oldest_character'], true) . "</li>";
                        echo "<li><strong>newest_character value:</strong> " . var_export($row['newest_character'], true) . "</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h6><i class='fal fa-times'></i> SQL Query ล้มเหลว!</h6>";
                        echo "<p><strong>ข้อผิดพลาด:</strong> ไม่สามารถ execute query ได้</p>";
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h6><i class='fal fa-times'></i> Database Error!</h6>";
                    echo "<p><strong>ข้อผิดพลาด:</strong> " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
                ?>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="testDateTime()">
                        <i class="fal fa-calendar"></i> ทดสอบ DateTime
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการแก้ไข</h5>
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. SQL Query Changes
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>เปลี่ยน <code>MIN(CreateDate)</code> เป็น <code>CONVERT(VARCHAR, MIN(CreateDate), 103)</code></li>
                                    <li>เปลี่ยน <code>MAX(CreateDate)</code> เป็น <code>CONVERT(VARCHAR, MAX(CreateDate), 103)</code></li>
                                    <li>ใช้ format 103 = dd/mm/yyyy</li>
                                    <li>ผลลัพธ์เป็น string พร้อมใช้งาน</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. PHP Code Changes
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>ลบ <code>->format('d/m/Y')</code> ออก</li>
                                    <li>ใช้ <code>htmlspecialchars()</code> แทน</li>
                                    <li>เพิ่ม <code>!empty()</code> check</li>
                                    <li>ลดความซับซ้อนของโค้ด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Benefits
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>ประโยชน์:</strong></p>
                                <ul>
                                    <li>ไม่มี Fatal Error อีกต่อไป</li>
                                    <li>แสดงวันที่ในรูปแบบที่อ่านง่าย</li>
                                    <li>โค้ดง่ายและเสถียร</li>
                                    <li>ไม่ต้องจัดการ DateTime conversion</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fal fa-info-circle"></i> SQL CONVERT Formats:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>Format</th><th>Pattern</th><th>Example</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>103</td><td>dd/mm/yyyy</td><td>25/12/2023</td></tr>
                                <tr><td>101</td><td>mm/dd/yyyy</td><td>12/25/2023</td></tr>
                                <tr><td>102</td><td>yyyy.mm.dd</td><td>2023.12.25</td></tr>
                                <tr><td>120</td><td>yyyy-mm-dd hh:mi:ss</td><td>2023-12-25 14:30:00</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข SQL Query ใช้ CONVERT</li>
                                <li>✅ ลบ ->format() ออก</li>
                                <li>✅ ใช้ htmlspecialchars()</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ไม่มี Fatal Error</li>
                                <li>✅ แสดงวันที่ถูกต้อง</li>
                                <li>✅ โค้ดเสถียรและง่าย</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function testDateTime() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fal fa-check-circle"></i> DateTime Error ได้รับการแก้ไขแล้ว - ใช้ CONVERT ใน SQL และ htmlspecialchars ใน PHP</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
