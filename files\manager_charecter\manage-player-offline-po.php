<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Manage-Account && Charecter
        <small>
            ระบบ ตรวจสอบข้อมูลผู้เล่น
        </small>
    </h1>
</div>
<?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
<div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
<?php } ?>
<?php

function getPage($stmt, $pageNum, $rowsPerPage) {
    $offset = ($pageNum - 1) * $rowsPerPage;
    $rows = array();
    $i = 0;
    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
        array_push($rows, $row);
        $i++;
    }
    return $rows;
}
?>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Player Offline All <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active p-3" data-toggle="tab" href="#tab_default" role="tab">
                                <i class="fal fa-cog text-info"></i>
                                <span class="hidden-sm-down ml-1">ผู้เล่น ออฟไลน์ ทั้งหมด</span>
                            </a>
                        </li>
                    </ul>


                    <div class="tab-content pt-2">
                        <div class="tab-pane fade show active" id="tab_default" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">
                                    <div class="panel-content">
                                    </div>
                                    <div class="table-responsive-lg">
                                        <table id="dt-basic-chars" class="table table-sm table-bordered w-100">
                                            <thead>
                                                <tr>
                                                    <th>CharacterIdx</th>
                                                    <th>ชื่อ</th>
                                                    <th>War</th>
                                                    <th>Class</th>
                                                    <th>LEV</th>
                                                    <th>STR</th>
                                                    <th>DEX</th>
                                                    <th>INT</th>
                                                    <th>PNT</th>
                                                    <th>Alz</th>
                                                    <th>WorldIdx</th>
                                                    <th>LogoutTime</th>
                                                    <th>Reputation</th>
                                                    <th>LoginTime</th>
                                                    <th>PlayTime</th>
                                                    <th>ChannelIdx</th>
                                                    <th>Nation</th>
                                                    <th>Login</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                        $rowsPerPage = 800;

                                                        // Define and execute the query.  
                                                        // Note that the query is executed with a "scrollable" cursor.
                                                        $sql = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table Where Login = 0 AND Nation = 2 ORDER BY CharacterIdx DESC";

                                                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                                        if (!$stmt)
                                                            die(print_r(sqlsrv_errors(), true));

                                                        // Get the total number of rows returned by the query.
                                                        $rowsReturned = sqlsrv_num_rows($stmt);
                                                        if ($rowsReturned === false)
                                                            die(print_r(sqlsrv_errors(), true));
                                                        elseif ($rowsReturned == 0) {
                                                            echo W_NOTHING_RETURNED;
                                                            //exit();
                                                        } else {
                                                            /* Calculate number of pages. */
                                                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                                        }

                                                        // Display the selected page of data.
                                                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                                                        foreach ($page as $row) {

                                                            $name = $userLogin->thaitrans($row[1]);
															$idx = floor($row[0]/16);
															$warid = $userLogin->chartowar($row[0]);
                                                            $class = $userLogin->cabalstyle($row[12]);
                                                            $ClassType = htmlspecialchars($class['Class_Name'] ?? 'Unknown');


                                                    ?>
                                                <tr>
                                                    <td><?php echo $row[0]; ?></td>
                                                    <td><?php echo $name; ?></td>
                                                    <td><?php echo $warid; ?></td>
                                                    <td><span class="badge badge-danger badge-pill">
                                                            <?php echo $ClassType; ?>
                                                        </span></td>
                                                    <td><?php echo $row[2]; ?></td>
                                                    <td><?php echo $row[4]; ?></td>
                                                    <td><?php echo $row[5]; ?></td>
                                                    <td><?php echo $row[6]; ?></td>
                                                    <td><?php echo $row[7]; ?></td>
                                                    <td><?php echo $row[9]; ?></td>
                                                    <td><?php echo $row[10]; ?></td>
                                                    <td><?php echo $row[23]; ?></td>
                                                    <td><?php echo $row[25]; ?></td>
                                                    <td><?php echo $row[26]; ?></td>
                                                    <td><?php echo $row[27]; ?></td>
                                                    <td><?php echo $row[28]; ?></td>
                                                    <td><span
                                                            class="
								                        <?php echo $label = ($row[30] == '0' ? ' badge badge-dark badge-pill' : ($row[30] == '1' ? ' badge badge-danger badge-pill' : ($row[30] == '2' ? ' badge badge-info badge-pill' : ($row[30] == '3' ? ' badge badge-warning badge-pill' : 'badge badge-secondary badge-pill'))));?>">
                                                            <?php echo $status = ($row[30] == '0' ? 'N/A' : ($row[30] == '1' ? 'Capella' : ($row[30] == '2' ? 'Procyon' : ($row[30] == '3' ? 'GM': 'Unknow'))));?></span>
                                                    </td>
                                                    <td><span
                                                            class="
								                        <?php echo $label = ($row[34] == '1' ? ' badge badge-success badge-pill' : 'badge badge-secondary badge-pill');?>">
                                                            <?php echo $status = ($row[34] == '1' ? 'Online' : 'Offline');?></span>
                                                    </td>
                                                    <td><a href="?url=manager_account/manage-account-edit&id=<?php echo $idx; ?>"
                                                            class="btn btn-outline-warning btn-icon rounded-circle waves-effect waves-themed"><i
                                                                class="fal fa-info-circle"></i></a></td>
                                                </tr>
                                                <?php 	} ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>