<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-exclamation-triangle"></i> แก้ไข NULL Values และ Warnings
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา NULL values และ Deprecated warnings</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ปัญหา NULL values และ warnings ทั้งหมดได้รับการแก้ไขแล้ว
                </div>
                
                <h5>⚠️ ปัญหาที่เกิดขึ้น</h5>
                <div class="alert alert-danger">
                    <h6>Deprecated Warnings:</h6>
                    <pre><code>Deprecated: number_format(): Passing null to parameter #1 ($num) of type float is deprecated</code></pre>
                    
                    <h6 class="mt-3">Undefined Array Key Warnings:</h6>
                    <pre><code>Warning: Undefined array key "created_year" in character-analytics.php
Warning: Undefined array key "created_old" in character-analytics.php</code></pre>
                    
                    <h6 class="mt-3">สาเหตุ:</h6>
                    <ul class="mb-0">
                        <li><strong>ช่วงวันสั้น:</strong> เมื่อใช้ 7 วัน อาจไม่มีตัวละครที่สร้างในช่วงนั้น</li>
                        <li><strong>NULL values:</strong> SQL Query return NULL เมื่อไม่มีข้อมูล</li>
                        <li><strong>Array keys:</strong> บาง keys ไม่มีอยู่ในผลลัพธ์</li>
                    </ul>
                </div>
                
                <h5>✅ วิธีแก้ไข</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ โค้ดเดิม (ผิด)</h6>
                            </div>
                            <div class="card-body">
                                <h6>PHP Code:</h6>
                                <pre><code>echo number_format($level['avg_level']);</code></pre>
                                
                                <h6>Array Access:</h6>
                                <pre><code>$creation['created_year']
$creation['created_old']</code></pre>
                                
                                <p class="mb-0"><strong>ปัญหา:</strong> ไม่ตรวจสอบ NULL และ undefined keys</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ โค้ดใหม่ (ถูก)</h6>
                            </div>
                            <div class="card-body">
                                <h6>PHP Code:</h6>
                                <pre><code>if (($level['avg_level'] ?? 0) > 0):
    echo number_format($level['avg_level']);
endif;</code></pre>
                                
                                <h6>Array Access:</h6>
                                <pre><code>echo number_format($creation['created_today'] ?? 0);</code></pre>
                                
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> ไม่มี warnings และแสดงผลถูกต้อง</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🔧 การแก้ไขที่ทำ</h5>
                <div class="accordion" id="fixesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. Level Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การแก้ไข:</strong></p>
                                <ul>
                                    <li>เพิ่ม <code>?? 0</code> สำหรับ NULL values</li>
                                    <li>ใช้ <code>if (($value ?? 0) > 0)</code> เพื่อแสดงเฉพาะค่าที่มีข้อมูล</li>
                                    <li>เพิ่มการแสดง "ตัวละครทั้งหมด" แทนเลเวลเฉลี่ย/สูงสุด เมื่อไม่มีข้อมูล</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. Wealth & Activity Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การแก้ไข:</strong></p>
                                <ul>
                                    <li>เพิ่ม <code>?? 0</code> ในทุก number_format()</li>
                                    <li>แสดงเฉพาะสถิติที่มีค่า > 0</li>
                                    <li>เพิ่มการแสดง "Alz รวม" และ "เวลาเล่นรวม"</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Creation Analysis
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การแก้ไข:</strong></p>
                                <ul>
                                    <li>ลบ <code>created_year</code> และ <code>created_old</code> ที่ไม่มีใน SQL</li>
                                    <li>แสดงเฉพาะข้อมูลที่เหมาะสมกับช่วงวัน</li>
                                    <li>ใช้ <code>if ($days >= X)</code> เพื่อแสดงข้อมูลตามช่วงวัน</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingFour">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseFour">
                                    4. Honor Class & Progression
                                </button>
                            </h6>
                        </div>
                        <div id="collapseFour" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <p><strong>การแก้ไข:</strong></p>
                                <ul>
                                    <li>เพิ่ม <code>?? 0</code> ในทุก number_format()</li>
                                    <li>แสดง '-' เมื่อ avg_reputation = 0</li>
                                    <li>ตรวจสอบ <code>elseif ($avgRep > 0)</code> ก่อนแสดงค่า</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแก้ไข</h5>
                <?php
                // ทดสอบกับช่วงวันที่อาจไม่มีข้อมูล
                $testDays = [1, 3, 7];
                
                foreach ($testDays as $days) {
                    echo "<h6>ทดสอบ {$days} วันที่ผ่านมา:</h6>";
                    
                    try {
                        $sql = "SELECT 
                                    COUNT(CASE WHEN LEV BETWEEN 1 AND 50 THEN 1 END) as level_1_50,
                                    COUNT(CASE WHEN LEV > 200 THEN 1 END) as level_200_plus,
                                    COUNT(*) as total_characters,
                                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                                    MAX(LEV) as max_level,
                                    AVG(CAST(Alz AS BIGINT)) as avg_alz
                                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                                WHERE CreateDate >= DATEADD(day, -?, GETDATE())";
                        
                        $stmt = sqlsrv_prepare($conn, $sql, array(&$days));
                        
                        if ($stmt && sqlsrv_execute($stmt)) {
                            if ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                                echo "<div class='alert alert-info'>";
                                echo "<strong>ผลลัพธ์:</strong><br>";
                                echo "• ตัวละครทั้งหมด: " . number_format($row['total_characters'] ?? 0) . "<br>";
                                
                                if (($row['avg_level'] ?? 0) > 0) {
                                    echo "• เลเวลเฉลี่ย: " . number_format($row['avg_level'], 1) . "<br>";
                                } else {
                                    echo "• เลเวลเฉลี่ย: ไม่มีข้อมูล<br>";
                                }
                                
                                if (($row['max_level'] ?? 0) > 0) {
                                    echo "• เลเวลสูงสุด: " . number_format($row['max_level']) . "<br>";
                                } else {
                                    echo "• เลเวลสูงสุด: ไม่มีข้อมูล<br>";
                                }
                                
                                if (($row['avg_alz'] ?? 0) > 0) {
                                    echo "• Alz เฉลี่ย: " . number_format($row['avg_alz']) . "<br>";
                                } else {
                                    echo "• Alz เฉลี่ย: ไม่มีข้อมูล<br>";
                                }
                                
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='alert alert-warning'>ไม่มีข้อมูลสำหรับ {$days} วันที่ผ่านมา</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                    }
                }
                ?>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openAnalytics7()">
                        <i class="fal fa-analytics"></i> ทดสอบ Analytics 7 วัน
                    </button>
                    <button class="btn btn-info" onclick="openAnalytics1()">
                        <i class="fal fa-analytics"></i> ทดสอบ Analytics 1 วัน
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข NULL values ด้วย ?? 0</li>
                                <li>✅ ลบ undefined array keys</li>
                                <li>✅ แสดงเฉพาะข้อมูลที่มีค่า</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ไม่มี Deprecated warnings</li>
                                <li>✅ ไม่มี Undefined key warnings</li>
                                <li>✅ แสดงผลเหมาะสมกับช่วงวัน</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fal fa-info-circle"></i> เทคนิคที่ใช้:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>ปัญหา</th><th>วิธีแก้</th><th>ตัวอย่าง</th></tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>NULL values</td>
                                    <td>Null coalescing operator</td>
                                    <td><code>$value ?? 0</code></td>
                                </tr>
                                <tr>
                                    <td>Undefined keys</td>
                                    <td>ลบ keys ที่ไม่มี</td>
                                    <td><code>created_year, created_old</code></td>
                                </tr>
                                <tr>
                                    <td>แสดงค่า 0</td>
                                    <td>เงื่อนไขตรวจสอบ</td>
                                    <td><code>if (($value ?? 0) > 0)</code></td>
                                </tr>
                                <tr>
                                    <td>ข้อมูลไม่เหมาะสม</td>
                                    <td>เงื่อนไขตามช่วงวัน</td>
                                    <td><code>if ($days >= 7)</code></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openAnalytics7() {
    window.open('?url=manager_charecter/character-analytics&days=7', '_blank');
}

function openAnalytics1() {
    window.open('?url=manager_charecter/character-analytics&days=1', '_blank');
}

function refreshPage() {
    location.reload();
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
