<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> MyCashItem
        <small> ระบบตรวจสอบไอเท็ม Cash</small>
    </h1>
</div>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>MyCashItem <span class="fw-300"><i>ตารางแอดไอเท็ม</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-bordered table-hover table-striped w-100">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>UserNum</th>
                                <th>ID</th>
                                <th>ItemKindIdx</th>
                                <th>ItemOpt</th>
                                <th>DurationIdx</th>
                                <th>RegDate</th>
                                <th>IsUse</th>
                                <th>UseDate</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $selectLastUsers      = "SELECT TOP 100 * FROM [" . DATABASE_CCA . "].[dbo].MyCashItem ORDER BY id DESC";
                            $selectLastUsersParam = array();
                            $selectLastUsersOpt   = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                            if (sqlsrv_num_rows($selectLastUsersQuery)) {
                            while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                            $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '" . $resLastUsers['UserNum'] . "'";
                            $selectauthParam = array();
                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                            ?>
                            <tr>
                                <td><?php echo $resLastUsers['Id']; ?></td>
                                <td><?php echo $resLastUsers['UserNum']; ?></td>
                                <td><?php echo $selectauthFetch['ID']; ?></td>
                                <td><?php echo $resLastUsers['ItemKindIdx']; ?></td>
                                <td><?php echo $resLastUsers['ItemOpt']; ?></td>
                                <td><?php echo $resLastUsers['DurationIdx']; ?></td>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($resLastUsers['RegDate'])); ?></td>
                                <td><?php echo $resLastUsers['IsUse']; ?></td>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($resLastUsers['UseDate'])); ?></td>
                                <td>
                                        <form method="post" name="j_delete_itemsinv" action="">
                                            <div class="j_alert"></div>
                                            <input type="hidden" name="delindex" value="<?php echo $resLastUsers['Id']; ?>">
                                            <input type="hidden" name="useridx" value="<?php echo $resLastUsers['UserNum']; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm" disabled>
                                                <i class="fa fa-chevron-circle-right"></i> ลบไอเท็มนี้ <?php echo $resLastUsers['Id']; ?>
                                            </button>
                                           
                                        </form>
                                    </td>
                            </tr>
                            <?php
                        }
                        } else {
                        echo W_NOTHING_RETURNED;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>


</div>