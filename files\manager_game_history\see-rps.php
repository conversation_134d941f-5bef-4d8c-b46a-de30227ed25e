<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> RP Shop History
        <small>ประวัติการชื้อไอเท็ม RP</small>
    </h1>
</div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>History <span class="fw-300"><i>Item Shop</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-bordered table-hover table-striped w-100">
                        <thead>
                            <tr>
                                <th>idx</th>
                                <th>UserNum</th>
                                <th>ID</th>
                                <th>ไอเท็ม</th>
                                <th>สถานะ</th>
                                <th>ราคา</th>
                                <th>เวลาชื้อ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $selectLastUsers      = "SELECT * FROM [dbo].WEB_Reward_chack ORDER BY idx DESC";
                            $selectLastUsersParam = array();
                            $selectLastUsersOpt   = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $selectLastUsersQuery = sqlsrv_query($conn, $selectLastUsers, $selectLastUsersParam, $selectLastUsersOpt);

                            if (sqlsrv_num_rows($selectLastUsersQuery)) {
                            while ($resLastUsers = sqlsrv_fetch_array($selectLastUsersQuery, SQLSRV_FETCH_ASSOC)) {

                            $selectauth      = "SELECT * FROM WEB_Reward_Item WHERE id  = '" . $resLastUsers['Reward_id'] . "'";
                            $selectauthParam = array();
                            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);

                            ?>
                            <tr>
                                <td><?php echo $resLastUsers['idx']; ?></td>
                                <td><?php echo $resLastUsers['UserNum']; ?></td>
                                <td><?php echo $resLastUsers['ID']; ?></td>
                                <td><?php echo $selectauthFetch['itemname']; ?></td>
                                <td><?php if ($resLastUsers['Reward_status'] == "0"){
                                            echo '<span class="badge badge-warning">รอตรวจสอบ</span>';
                                        }elseif ($resLastUsers['Reward_status'] == "1"){
                                            echo '<span class="badge badge-success">ผ่านการตรวจสอบแล้ว</span>';
                                        }
                                    ?>
                                </td>
                                <td><span class="badge badge-warning"><?php echo $resLastUsers['Reward_price']; ?></span></td>
                                <td><?php echo date('d/m/Y', strtotime($resLastUsers['added_time'])); ?></td>
                            </tr>
                            <?php
                        }
                        } else {
                        echo W_NOTHING_RETURNED;
                        }
                        ?>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>


</div>