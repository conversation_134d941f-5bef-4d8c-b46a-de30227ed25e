# ระบบสถิติและตรวจสอบตัวละคร

ระบบสถิติและการตรวจสอบความเคลื่อนไหวสำหรับระบบจัดการตัวละครในเกม Cabal

## ฟีเจอร์หลัก

### 1. หน้าสถิติหลัก (character-statistics.php)
- แสดงสถิติรวมของระบบตัวละคร
- จำนวนตัวละครทั้งหมด/ออนไลน์
- กิจกรรมวันนี้ (ตัวละครใหม่, เข้าเกม)
- เลเวลเฉลี่ย, Alz รวม, เวลาเล่นรวม
- การกระจายตามคลาสและเลเวล
- ผู้เล่นอันดับต้น (Top 10)

### 2. การวิเคราะห์ (character-analytics.php)
- กราฟกิจกรรมรายวัน (สร้างตัวละคร, เข้าเกม)
- การกระจายตามชั่วโมง
- การกระจายเวลาเล่น
- การกระจายตามเลเวลและเวิลด์
- ตัวกรองตามช่วงเวลา (7, 30, 90 วัน)

### 3. ตรวจสอบสด (character-monitor.php)
- การตรวจสอบความเคลื่อนไหวแบบ Real-time
- การแจ้งเตือนกิจกรรมผิดปกติ
- อัพเดทอัตโนมัติ
- สถิติสดและประวัติกิจกรรมล่าสุด

### 4. การส่งออกข้อมูล (character-export-manager.php)
- ส่งออกข้อมูลในรูปแบบต่างๆ (CSV, JSON, Excel)
- เลือกประเภทข้อมูล (รายชื่อ, สถิติ, ออนไลน์, ใหม่, อันดับต้น)
- กรองตามเลเวล, วันที่
- ส่งออกด่วน

## โครงสร้างไฟล์

```
files/manager_charecter/
├── character-statistics.php        # หน้าสถิติหลัก
├── character-analytics.php         # การวิเคราะห์และกราฟ
├── character-monitor.php           # ตรวจสอบสด
├── character-export-manager.php    # จัดการการส่งออก
├── api/
│   └── character-data.php         # API สำหรับข้อมูล Real-time
├── export/
│   └── character-export.php       # ระบบส่งออกข้อมูล
└── README.md                      # เอกสารนี้
```

## ตารางฐานข้อมูลที่ใช้

### ตารางหลัก
- `cabal_character_table` - ข้อมูลตัวละครหลัก

### ฟิลด์สำคัญ
- `CharacterIdx` - รหัสตัวละคร
- `Name` - ชื่อตัวละคร
- `LEV` - เลเวล
- `Style` - คลาส (bit flags)
- `Alz` - จำนวน Alz
- `PlayTime` - เวลาเล่น (วินาที)
- `CreateDate` - วันที่สร้าง
- `LoginTime` - เข้าเกมล่าสุด
- `ChannelIdx` - ช่องที่ออนไลน์ (>0 = ออนไลน์)
- `WorldIdx` - เวิลด์

## การใช้งาน

### เข้าถึงระบบ
1. เข้าสู่หน้าใดหน้าหนึ่งในระบบตัวละคร
2. คลิกปุ่ม "สถิติตัวละคร" เพื่อเข้าสู่หน้าสถิติหลัก
3. ใช้เมนูด้านบนเพื่อนำทางไปยังฟีเจอร์ต่างๆ

### การตรวจสอบสด
1. เข้าสู่หน้า "ตรวจสอบสด"
2. คลิก "เริ่มอัพเดทอัตโนมัติ" เพื่อเปิดการอัพเดทแบบ Real-time
3. ระบบจะอัพเดทข้อมูลทุก 15 วินาที

### การส่งออกข้อมูล
1. เข้าสู่หน้า "ส่งออกข้อมูล"
2. เลือกประเภทข้อมูลและรูปแบบไฟล์
3. กำหนดเกณฑ์การกรอง (เลเวล, วันที่)
4. คลิก "ส่งออกข้อมูล"

## API Endpoints

### GET /files/manager_charecter/api/character-data.php

#### Parameters:
- `action` - ประเภทข้อมูล
  - `live_stats` - สถิติสด
  - `recent_activities` - กิจกรรมล่าสุด
  - `alerts` - การแจ้งเตือน
  - `online_stats` - สถิติออนไลน์
  - `class_stats` - สถิติตามคลาส
- `limit` - จำนวนรายการ (สำหรับ recent_activities)

#### Response Format:
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

## การแจ้งเตือน

### ประเภทการแจ้งเตือน
1. **High Level New Character** - ตัวละครเลเวลสูงที่สร้างใหม่ (>100)
2. **High Alz Character** - ตัวละครที่มี Alz มาก (>100M)
3. **Multiple Characters** - การสร้างตัวละครหลายตัวจากบัญชีเดียว

### การตั้งค่าการแจ้งเตือน
- แก้ไขค่าใน `getCharacterAlerts()` function ในไฟล์ API
- ปรับเกณฑ์การแจ้งเตือนตามต้องการ

## คลาสตัวละคร (Style Bit Flags)

```php
function getClassName($style) {
    if ($style & 1) return 'Warrior';      // 0x01
    if ($style & 2) return 'Blader';       // 0x02
    if ($style & 4) return 'Wizard';       // 0x04
    if ($style & 8) return 'Force Archer'; // 0x08
    if ($style & 16) return 'Force Shielder'; // 0x10
    if ($style & 32) return 'Force Blader';   // 0x20
    return 'Unknown';
}
```

## การปรับแต่ง

### เปลี่ยนช่วงเวลาการอัพเดท
แก้ไขใน `character-monitor.php`:
```javascript
autoRefreshInterval = setInterval(refreshData, 15000); // 15 วินาที
```

### เปลี่ยนจำนวนรายการที่แสดง
แก้ไขใน PHP files:
```php
$rowsPerPage = 1000; // จำนวนรายการต่อหน้า
```

### เพิ่มสถิติใหม่
1. เพิ่ม SQL query ใน `getCharacterStatistics()` function
2. เพิ่มการแสดงผลใน HTML
3. อัพเดท API หากต้องการ Real-time

## การส่งออกข้อมูล

### ประเภทข้อมูลที่รองรับ
1. **character_list** - รายชื่อตัวละคร
2. **character_stats** - สถิติรวม
3. **online_players** - ผู้เล่นออนไลน์
4. **new_characters** - ตัวละครใหม่
5. **top_players** - ผู้เล่นอันดับต้น
6. **class_distribution** - การกระจายตามคลาส

### รูปแบบไฟล์
- **CSV** - เหมาะสำหรับ Excel
- **JSON** - เหมาะสำหรับการประมวลผล
- **Excel** - ไฟล์ Excel โดยตรง

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **ข้อมูลไม่อัพเดท**
   - ตรวจสอบการเชื่อมต่อฐานข้อมูล
   - ตรวจสอบ JavaScript Console สำหรับ errors

2. **การส่งออกไม่ทำงาน**
   - ตรวจสอบสิทธิ์การเข้าถึงไฟล์
   - ตรวจสอบ PHP error logs

3. **กราฟไม่แสดง**
   - ตรวจสอบการโหลด Chart.js library
   - ตรวจสอบข้อมูลใน JavaScript Console

### การ Debug
1. เปิด Browser Developer Tools
2. ตรวจสอบ Network tab สำหรับ API calls
3. ตรวจสอบ Console tab สำหรับ JavaScript errors

## การอัพเดทในอนาคต

### ฟีเจอร์ที่วางแผนไว้
- [ ] การแจ้งเตือนผ่าน Email/SMS
- [ ] Dashboard แบบ Widget
- [ ] การเปรียบเทียบข้อมูลระหว่างช่วงเวลา
- [ ] การวิเคราะห์พฤติกรรมผู้เล่น
- [ ] ระบบ Ranking แบบ Real-time

### การปรับปรุง
- [ ] เพิ่มการ Cache ข้อมูล
- [ ] ปรับปรุงประสิทธิภาพ Query
- [ ] เพิ่มการ Validation ข้อมูล
- [ ] ปรับปรุง UI/UX
- [ ] เพิ่มการรองรับ Mobile

## การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ error logs
2. ตรวจสอบเอกสารนี้
3. ติดต่อทีมพัฒนา

---

**หมายเหตุ:** ระบบนี้ออกแบบมาสำหรับการใช้งานภายในเท่านั้น กรุณาใช้ด้วยความระมัดระวังและทำ backup ข้อมูลเป็นประจำ
