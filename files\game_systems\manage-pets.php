<?php $user->restrictionUser(true, $conn); ?>
<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
<?php
             if (isset($_POST['btn_savechangeLev'])) {
                // variable
                $inputPetid = strip_tags(trim($_POST['input_Petid']));
                $inputLev = strip_tags(trim($_POST['input_Lev']));
                // condition
                if (empty($inputPetid)) {
                    $returnWarning = "โปรดกรอกข้อมูล PetSerial ก่อนเปลียน Level สัตว์เลี่ยง";
                } else if (empty($inputLev)) {
                    $returnWarning = "โปรดกรอกข้อมูล Level"; 
                } else {

                //admin id //
                $adminid = $userLogin->recUserAccount('ID', $conn);
                $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                $dateNow = date('Y-m-d H:i:s');
                $selectauth = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table WHERE PetSerial = '$inputPetid'";
                $selectauthParam = array();
                $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                $PetSerial = $selectauthFetch['PetSerial'];
                    if(sqlsrv_rows_affected($selectauthQuery)){
                        $PetID = "UPDATE [".DATABASE_SV."].[dbo].cabal_pet_table SET Lev = ? WHERE PetSerial = ?";
                        $PetIDParams = array($inputLev,$inputPetid);
                        $PetIDParamsQuery = sqlsrv_query($conn, $PetID, $PetIDParams);
                            if ($PetIDParamsQuery) { 
                                $zpanel->generateWebLog($conn, '2', $adminidx, 'แก้ไข Level', "PetSerial: {$inputPetid} Level : {$inputLev} DateTime : {$dateNow}");
                                $returnSuccess = "SUCCESS :: แก้ไข PetSerial {$inputPetid} เป็น Level {$inputLev} เรียบร้อย";   
                            } else {
                            $returnError = "ERROR :: ระบบ อัพเดดเวล ผิดพลาด";  
                            }
                    }else{
                        $returnWarning = "ไม่มี PetSerial นี้";
                    }
                    
                }
            }
      
        if (isset($_POST['btn_savechange'])) {
                    // variable
                    $inputPetid = strip_tags(trim($_POST['input_Petid']));
                    $inputOption1 = strip_tags(trim($_POST['input_Opt1']));
                    $inputOption2 = strip_tags(trim($_POST['input_Opt2']));
                    $inputOption3 = strip_tags(trim($_POST['input_Opt3']));
                    $inputOption4 = strip_tags(trim($_POST['input_Opt4']));
                    $inputOption5 = strip_tags(trim($_POST['input_Opt5']));
                    $inputOption6 = strip_tags(trim($_POST['input_Opt6']));
                    $inputOption7 = strip_tags(trim($_POST['input_Opt7']));
                    $inputOption8 = strip_tags(trim($_POST['input_Opt8']));
                    $inputOption9 = strip_tags(trim($_POST['input_Opt9']));
                    $inputOption10 = strip_tags(trim($_POST['input_Opt10']));
                    // condition
                    if (empty($inputPetid)) {
                        $returnWarning = "โปรดกรอกข้อมูล PetSerial ก่อนเปลียน ออฟชั่น สัตว์เลี้ยง";
                    } else {

                    $inputOption = '0x'.$inputOption1.$inputOption2.$inputOption3.$inputOption4.$inputOption5.$inputOption6.$inputOption7.$inputOption8.$inputOption9.$inputOption10.'00000000000000000000000000000000000000000000';
                    //admin id //
                    $adminid = $userLogin->recUserAccount('ID', $conn);
                    $adminidx = $userLogin->recUserAccount('UserNum', $conn);
                    $dateNow = date('Y-m-d H:i:s');
                    $selectauth = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table WHERE PetSerial = '$inputPetid'";
                    $selectauthParam = array();
                    $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
                    $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
                    $PetSerial = $selectauthFetch['PetSerial'];
                        if(sqlsrv_rows_affected($selectauthQuery)){
                                    //$PetOpt = "EXECUTE WEB_Pets_Opt ?, ?";
                                    $PetOpt = "UPDATE [".DATABASE_SV."].[dbo].cabal_pet_table SET options = $inputOption WHERE petSerial = $PetSerial";
                                    //$PetOptParams = array($inputOption,$PetSerial);
                                    $PetOptQuery = sqlsrv_query($conn, $PetOpt, array());
                                        if ($PetOptQuery) {
                                                $zpanel->GenerateWebLog_Admin($conn, '2', $adminidx, 'แก้ไข ออฟชั่น', "PetSerial: {$inputPetid} ออฟชั่น : {$inputOption} DateTime : {$dateNow}");
                                                $returnSuccess = "SUCCESS :: แก้ไข PetSerial {$inputPetid} เป็น Option {$inputOption} เรียบร้อย";
                                        } else {
                                                $returnError = "ERROR :: ระบบ อัพเดด ออฟชั่นผิดพลาด Option {$inputOption}";
                                        }
                        }else{
                            $returnWarning = "ไม่มี PetSerial นี้";
                        }
                        
                    }
                }                
             ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> manage-pets
        <small>
            ระบบ แก้ไขสัตว์เลี้ยงผู้เล่น
        </small>
    </h1>
</div>
<div class="alert alert-primary alert-dismissible">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">
            <i class="fal fa-times"></i>
        </span>
    </button>
    <div class="d-flex flex-start w-100">
        <div class="mr-2 hidden-md-down">
            <span class="icon-stack icon-stack-lg">
                <i class="base base-6 icon-stack-3x opacity-100 color-primary-500"></i>
                <i class="base base-10 icon-stack-2x opacity-100 color-primary-300 fa-flip-vertical"></i>
                <i class="fal fa-info icon-stack-1x opacity-100 color-white"></i>
            </span>
        </div>
        <div class="d-flex flex-fill">
            <div class="flex-fill">
                <span class="h5">How it works</span>
                <br>
                -ผู้เล่น ออกจากเกมส์ไปรออยู่หน้า<code>Login หรือหน้า เลือกตัวละคร</code>
                <br>
                -ให้ตรวจสอบ <code>PetSerial</code> จาก <code>Pets Table</code> แล้วนำ <code>PetSerial</code>
                มากรอกข้อมูลลง <code>Textinput PetSerial</code>
                <br>
                -การปรับ <code>Level</code> ให้นำ <code>PetSerial</code> มาใส่ใส่ใน
                <code>Textinput PetSerial</code>แล้วเลือก Level ที่ต้องการ แล้วกดปุ่ม <code>ยืนยันข้อมูล Level</code>
                <br>
                -การปรับ <code>Option</code> ให้นำ <code>PetSerial</code> มาใส่ใส่ใน
                <code>Textinput PetSerial</code>แล้วเลือก Option ที่ต้องการ แล้วกดปุ่ม <code>ยืนยันข้อมูล Option</code>
            </div>
        </div>
    </div>
</div>
<?php if (isset($returnSuccess)) { ?>
<div class="alert alert-success"><?php echo $returnSuccess; ?></div>
<?php } elseif (isset($returnWarning)) { ?>
<div class="alert alert-warning"><?php echo $returnWarning; ?></div>
<?php } elseif (isset($returnError)) { ?>
<div class="alert alert-danger"><?php echo $returnError; ?></div>
<?php } ?>
<div class="row">
    <div class="col-xl-5">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Edit Pets <span class="fw-300"><i>Level</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content p-0">
                    <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                        <div class="panel-content">
                            <div class="form-row">
                                <div class="col-md-12 mb-3">
                                    <label for="input_Petid" class="control-label">PetSerial: </label>
                                    <input type="text" name="input_Petid" class="form-control" id="input_Petid"
                                        placeholder="PetSerial" pattern="[a-zA-Z0-9]+$"
                                        value="<?php if (isset($inputPetid)) echo $inputPetid; ?>">
                                </div>
                            </div>
                            <div class="form-row mb-2">
                                <div class="col-md-12 mb-3">
                                    <label for="input_Lev" class="control-label">Slot:</label>
                                    <div class="m-md slider-primary">
                                        <input name="input_Lev" id="petsuplevel" type="text"
                                            value="<?php if (isset($inputLev)) echo $inputLev; ?>" class="d-none"
                                            tabindex="-1" readonly="">
                                    </div>
                                    <p><code>*จะแก้ไขสัตว์เลี้ยงควรตั้งชื่อสัตว์เลี้ยงก่อน*</code></p>
                                </div>

                            </div>
                        </div>
                        <div
                            class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row">
                            <button type="submit" name="btn_savechangeLev"
                                class="btn btn-danger ml-auto waves-effect waves-themed">
                                <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง Level
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- panel end -->
    </div>

    <div class="col-xl-7">
        <div id="panel-2" class="panel">
            <div class="panel-hdr">
                <h2>
                    Edit Pet <span class="fw-300"><i>Option</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <form role="form" method="post" name="configEdit" enctype="multipart/form-data">
                <div class="panel-container show">
                    <div class="panel-content">
                        <div class="form-group">
                            <label for="input_Petid" class="control-label">PetSerial: </label>
                            <input type="text" name="input_Petid" class="form-control" id="input_Petid"
                                placeholder="PetSerial" pattern="[a-zA-Z0-9]+$"
                                value="<?php if (isset($inputPetid)) echo $inputPetid; ?>">
                        </div>
                    </div>
                    <div class="panel-content p-0">
                        <div class="panel-content">
                            <div class="form-row">
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt1">Slot1 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt1" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot1.
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt2">Slot2 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt2" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot2.
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt3">Slot3 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt3" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot3.
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label" for="input_Opt4">Slot4 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt4" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot4.
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label" for="input_Opt5">Slot5 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt5" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot5.
                                    </div>
                                </div>
                            </div>
                            <div class="form-row form-group">
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt6">Slot6 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt6" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot6.
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt7">Slot7 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt7" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot7.
                                    </div>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label" for="input_Opt8">Slot8 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt8" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot8.
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label" for="input_Opt9">Slot9 <span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt9" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot9.
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label" for="input_Opt10">Slot10<span
                                            class="text-danger">*</span></label>
                                    <select class="custom-select" name="input_Opt10" required="">
                                        <option value="00">รูว่าง</option>
                                        <option value="01">HP</option>
                                        <option value="02">MP</option>
                                        <option value="03">Attack</option>
                                        <option value="04">Magic Attack</option>
                                        <option value="05">Defense</option>
                                        <option value="06">Attack Rate</option>
                                        <option value="07">Defense Rate</option>
                                        <option value="08">Critical Damage</option>
                                        <option value="09">Critical Rate</option>
                                        <option value="0A">Min Damage</option>
                                        <option value="0B">Max HP Steal Hit</option>
                                        <option value="0C">Max MP Steal Hit</option>
                                        <option value="0D">Max Critical Rate</option>
                                        <option value="0E">Sword Skill Amp</option>
                                        <option value="0F">Magic Skill Amp</option>
                                        <option value="10">HP Steal %</option>
                                        <option value="11">MP Steal %</option>
                                        <option value="12">Evasion</option>
                                        <option value="13">HP Auto Heal</option>
                                        <option value="14">MP Auto Heal</option>
                                        <option value="15">Add. Damage</option>
                                        <option value="16">Resist AMP</option>
                                        <option value="17">Alz Drop Amount</option>
                                        <option value="18">2 Slot Drop Rate UP</option>
                                        <option value="19">Resist Cri Rate</option>
                                        <option value="1A">Resist Cri Dmg</option>
                                        <option value="1B">Resist Cri Unmove</option>
                                        <option value="1C">Resist Cri Down</option>
                                        <option value="1D">Resist Cri Knock Back</option>
                                        <option value="1E">Resist Cri Stun</option>
                                        <option value="1F">Attack x2</option>
                                        <option value="20">Magic Attack x2</option>
                                        <option value="21">Critical Damage x2</option>
                                        <option value="22">Critical Rate x2</option>
                                        <option value="23">Max Critical Rate x2</option>
                                        <option value="24">Sword Skill Amp x2</option>
                                        <option value="25">Magic Skill Amp x2</option>
                                        <option value="26">Resist Cri Rate x2</option>
                                        <option value="27">Resist Cri Dmg x2</option>
                                        <option value="28">Resist AMP x2</option>
                                        <option value="29">Attack x3</option>
                                        <option value="2A">Magic Attack x3</option>
                                        <option value="2B">Critical Damage x3</option>
                                        <option value="2C">Critical Rate x3</option>
                                        <option value="2D">Max Critical Rate x3</option>
                                        <option value="2E">Sword Skill Amp x3</option>
                                        <option value="2F">Magic Skill Amp x3</option>
                                        <option value="30">Resist Cri Rate x3</option>
                                        <option value="31">Resist Cri Dmg x3</option>
                                        <option value="32">Resist AMP x3</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please provide a valid Slot10.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row align-items-center">

                            <button type="submit" name="btn_savechange"
                                class="btn btn-primary ml-auto waves-effect waves-themed">
                                <span class="fal fa-check mr-1"></span>ยืนยันข้อมูล เปลียนแปลง Option
                            </button>
                        </div>

                    </div>
                </div>
            </form>
        </div>
    </div>


    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Pets <span class="fw-300"><i>Table</i></span>
                </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Collapse"></button>
                    <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip"
                        data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10"
                        data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active p-3" data-toggle="tab" href="#tab_default-1" role="tab">
                                <i class="fal fa-table text-success"></i>
                                <span class="hidden-sm-down ml-1">ตาราง PETS</span>
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content pt-4">
                        <div class="tab-pane fade show active" id="tab_default-1" role="tabpanel">
                            <div class="row">
                                <div class="col-xl-12">
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <table id="datatables-default" class="table table-sm table-bordered w-100">
                                                <thead>
                                                    <tr>
                                                        <th>PetSerial</th>
                                                        <th>PetId</th>
                                                        <th>OwnerCharIdx</th>
                                                        <th>ItemIdx</th>
                                                        <th>Lev</th>
                                                        <th>LevExp</th>
                                                        <th>NickName</th>
                                                        <th>Option</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php

																	// generic function to get page
																	function getPage($stmt, $pageNum, $rowsPerPage) {
																		$offset = ($pageNum - 1) * $rowsPerPage;
																		$rows = array();
																		$i = 0;
																		while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
																			array_push($rows, $row);
																			$i++;
																		}
																		return $rows;
																	}

                                                                    function str2bin($str) { 
                                                                        return '0x'.strtoupper(bin2hex($str));
                                                                      }

																	// Set the number of rows to be returned on a page.
																	$rowsPerPage = 10000;

																	// Define and execute the query.  
																	// Note that the query is executed with a "scrollable" cursor.
																	$sql = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_pet_table ORDER BY PetSerial DESC";

																	$stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
																	if (!$stmt)
																		die(print_r(sqlsrv_errors(), true));

																	// Get the total number of rows returned by the query.
																	$rowsReturned = sqlsrv_num_rows($stmt);
																	if ($rowsReturned === false)
																		die(print_r(sqlsrv_errors(), true));
																	elseif ($rowsReturned == 0) {
																		echo W_NOTHING_RETURNED;
																		//exit();
																	} else {
																		/* Calculate number of pages. */
																		$numOfPages = ceil($rowsReturned / $rowsPerPage);
																	}

																	// Display the selected page of data.
																	$pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
																	$page = getPage($stmt, $pageNum, $rowsPerPage);       
																	foreach ($page as $row) {
                                                                        
                                                                    $optionpet = str2bin($row[7]);
																		?>
                                                    <tr>
                                                        <td><?php echo $row[0]; ?></td>
                                                        <td><?php echo $row[1]; ?></td>
                                                        <td><?php echo $row[2]; ?></td>
                                                        <td><?php echo $row[3]; ?></td>
                                                        <td>
                                                            <?php if($row[4] < 5){
																					echo '<span class="badge badge-secondary badge-pill">'.$row[4].'</span>';
																					}elseif($row[4] == 5){
																						echo '<span class="badge badge-success badge-pill">'.$row[4].'</span>';
																						}elseif($row[4] == 6){
																							echo '<span class="badge badge-info badge-pill">'.$row[4].'</span>';
																							}elseif($row[4] == 7){
																								echo '<span class="badge badge-primary badge-pill">'.$row[4].'</span>';
																								}elseif($row[4] == 7){
																									echo '<span class="badge badge-primary badge-pill">'.$row[4].'</span>';
																									}elseif($row[4] == 8){
																										echo '<span class="badge badge-primary badge-pill">'.$row[4].'</span>';
																										}elseif($row[4] == 9){
																											echo '<span class="badge badge-danger badge-pill">'.$row[4].'</span>';
																											}elseif($row[4] == 10){
																												echo '<span class="badge badge-warning badge-pill">'.$row[4].'</span>';
																				}
																				?>
                                                        <td><?php echo $row[5]; ?></td>
                                                        <td><?php echo $userLogin->thaitrans($row[6]); ?></td>
                                                        <td><?php echo $optionpet; ?></td>
                                                    </tr>
                                                    <?php
																	}
																	?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!--row end -->
    </div>
    <?php } ?>