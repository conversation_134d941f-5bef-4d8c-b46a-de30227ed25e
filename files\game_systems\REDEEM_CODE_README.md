# ระบบสร้าง Redeem Code อัตโนมัติ

## คำอธิบาย
ระบบนี้ใช้สำหรับสร้าง Redeem Code อัตโนมัติสำหรับเกม โดยสามารถกำหนดไอเท็ม จำนวน และวันหมดอายุได้

## โครงสร้าง Database

### Table: WEB_Redeem_Code
```sql
CREATE TABLE [dbo].[WEB_Redeem_Code] (
  [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
  [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
  [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
  [quantity] int NULL,
  [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
  [datecreated] datetime DEFAULT getdate() NULL,
  [expiry_date] datetime NULL
)
```

### ฟิลด์อธิบาย:
- **id**: รหัสอัตโนมัติ
- **code**: รหัส Redeem Code (เช่น ABCD-1234-EFGH-5678)
- **items**: รายการไอเท็ม (รูปแบบ: itemid:option:duration)
- **quantity**: จำนวนไอเท็มต่อ Code
- **status**: สถานะ (0=ยังไม่ใช้งาน, 1=ใช้งานแล้ว)
- **datecreated**: วันที่สร้าง
- **expiry_date**: วันหมดอายุ (NULL=ไม่หมดอายุ)

## รูปแบบไอเท็ม

### รูปแบบพื้นฐาน:
```
itemid:option:duration
```

### ตัวอย่าง:
- `1:0:31` - Upgrade Core (High) ถาวร
- `10:0:31` - Upgrade Core (Medium) ถาวร  
- `1214:0:31` - ไอเท็มพิเศษ ถาวร

### หลายไอเท็ม:
```
1:0:31,10:0:31,1214:0:31
```

## รูปแบบ Code ที่รองรับ

1. **XXXX-XXXX-XXXX-XXXX** (16 ตัวอักษร + 3 ขีด)
2. **XXXXXXXX-XXXX-XXXX** (12 ตัวอักษร + 2 ขีด)
3. **XXXXXXXXXXXXXXXXXXXX** (20 ตัวอักษร ไม่มีขีด)

## วิธีการใช้งาน

### 1. เข้าสู่ระบบ
- เข้าไปที่เมนู "จัดการระบบเกมส์" > "สร้าง Redeem Code อัตโนมัติ"
- หรือเข้าผ่าน URL: `?url=game_systems/redeem_code_generator`

### 2. กรอกข้อมูล
1. **จำนวน Code**: ระบุจำนวน Code ที่ต้องการสร้าง (1-1000)
2. **Quantity**: จำนวนไอเท็มต่อ Code
3. **รายการไอเท็ม**: กรอกรายการไอเท็มตามรูปแบบ
4. **วันหมดอายุ**: กำหนดวันหมดอายุ (ไม่บังคับ)
5. **รูปแบบ Code**: เลือกรูปแบบ Code

### 3. สร้าง Code
- กดปุ่ม "สร้าง Redeem Code"
- ระบบจะสร้าง Code ที่ไม่ซ้ำกัน
- แสดงผลลัพธ์ในตาราง

### 4. ดาวน์โหลด
- กดปุ่ม "ดาวน์โหลด Codes (TXT)" เพื่อบันทึกเป็นไฟล์

## ฟีเจอร์

### การสร้าง Code อัตโนมัติ
- สร้าง Code ที่ไม่ซ้ำกันอัตโนมัติ
- รองรับหลายรูปแบบ Code
- ตรวจสอบความซ้ำซ้อน

### การจัดการไอเท็ม
- รองรับหลายไอเท็มต่อ Code
- ตรวจสอบรูปแบบไอเท็มอัตโนมัติ
- กำหนด Quantity ได้

### การจัดการวันหมดอายุ
- กำหนดวันหมดอายุได้
- Code ที่หมดอายุจะใช้งานไม่ได้
- แสดงสถานะวันหมดอายุ

### การจัดการข้อมูล
- แสดงรายการ Code ทั้งหมด
- ลบ Code ที่ไม่ต้องการ
- ดาวน์โหลดรายการ Code

## ไฟล์ที่เกี่ยวข้อง

### 1. redeem_code_generator.php
- หน้าหลักของระบบ
- UI สำหรับสร้างและจัดการ Code

### 2. class_module/redeem_code_api.php
- API สำหรับจัดการข้อมูล
- รองรับ actions: generate, list, delete

### 3. test_redeem_system.php
- ไฟล์ทดสอบระบบ
- ตรวจสอบการทำงานของระบบ

## การทดสอบระบบ
เรียกใช้ไฟล์ `test_redeem_system.php` เพื่อทดสอบ:
- การเชื่อมต่อฐานข้อมูล
- การสร้าง table อัตโนมัติ
- การสร้าง Code
- การตรวจสอบรูปแบบไอเท็ม

## ความปลอดภัย
- ตรวจสอบ session login ก่อนใช้งาน
- ตรวจสอบรูปแบบข้อมูลอย่างเข้มงวด
- ป้องกัน SQL Injection ด้วย prepared statements
- จำกัดจำนวน Code ที่สร้างต่อครั้ง

## การแก้ไขปัญหา

### ปัญหาที่อาจพบ
1. **ไม่สามารถเชื่อมต่อฐานข้อมูลได้**
   - ตรวจสอบการตั้งค่าในไฟล์ `_app/dbinfo.inc.php`

2. **ไม่สามารถสร้าง table ได้**
   - ตรวจสอบสิทธิ์ของ user ในฐานข้อมูล

3. **รูปแบบไอเท็มไม่ถูกต้อง**
   - ตรวจสอบรูปแบบ: `itemid:option:duration`
   - แยกหลายไอเท็มด้วย comma

4. **ไม่สามารถสร้าง Code ได้**
   - ตรวจสอบว่าไม่มี Code ซ้ำในฐานข้อมูล
   - ลองเปลี่ยนรูปแบบ Code

### การ Debug
- เปิดไฟล์ `test_redeem_system.php` เพื่อดูสถานะระบบ
- ตรวจสอบ console ของเบราว์เซอร์สำหรับ JavaScript errors
- ตรวจสอบ PHP error logs

## ตัวอย่างการใช้งาน

### สร้าง Code พื้นฐาน
```
จำนวน Code: 10
Items: 1:0:31,10:0:31
Quantity: 1
รูปแบบ: XXXX-XXXX-XXXX-XXXX
```

### สร้าง Code พิเศษ
```
จำนวน Code: 100
Items: 1214:0:31,1215:0:31,1216:0:31
Quantity: 5
วันหมดอายุ: 2025-12-31 23:59:59
รูปแบบ: XXXXXXXX-XXXX-XXXX
```

## การอัปเดตในอนาคต
- เพิ่มการ import รายการไอเท็มจากไฟล์
- เพิ่มการกำหนดสิทธิ์การใช้งาน Code
- เพิ่มระบบ tracking การใช้งาน
- เพิ่มการส่งออกข้อมูลในรูปแบบต่างๆ
