{"version": 3, "sources": ["summernote.css", "../../../scss/_modules/_fonts.scss", "../../../scss/_imports/_global-import.scss", "../../../scss/_mixins/mixins.scss", "../../../scss/_modules/variables.scss", "../../../scss/_modules/_placeholders.scss"], "names": [], "mappings": "AAAA;;;;;;;;;;;EAWE;ACXF,iFAAY;ADYZ;EACE,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,mCAAmC;EACnC,2NAA2N,EAAA;;AAG7N;;EAEE,qBAAqB;EACrB,uBAAuB;EACvB,kBAAkB;EAClB,kBAAkB;EAClB,wBAAwB;EACxB,oBAAoB;EACpB,oBAAoB;EACpB,sBAAsB;EACtB,kCAAkC;EAClC,mCAAmC;EACnC,WAAW,EAAA;;AAIb;EACE,kBAAkB;EAClB,aAAa,EAAA;;AAGf;EACE,yBAAyB;EACzB,oBAAoB;EACpB,4BAA4B,EAAA;;AAG9B;EACE,WAAW,EAAA;;AAGb;EACE,YAAY,EAAA;;AAGd;EACE,mBAAmB,EAAA;;AAGrB;EACE,kBAAkB,EAAA;;AAIpB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAGlB;EACE,gBAAgB,EAAA;;AAIlB;EAAa,kBAAiB,EAAA;;AAAC;EAA4B,kBAAiB;EAAC,aAAY;EAAC,YAAW;EAAC,cAAa;EAAC,sBAAqB;EAAC,YAAW,EAAA;;AAAC;EAAmD,mBAAkB;EAAC,sBAAqB;EAAC,kBAAiB;EAAC,eAAc;EAAC,gBAAe,EAAA;;AAAC;EAAkC,cAAa,EAAA;;AAAC;EAAqC,cAAa,EAAA;;AAAC;EAAgC,kBAAiB,EAAA;;AAAC;EAA+C,aAAY,EAAA;;AAAC;EAAmD,qBAAoB,EAAA;;AAAC;EAAmD,mBAAkB,EAAA;;AAAC;EAAmE,kBAAiB,EAAA;;AAAC;EAAoE,iBAAgB,EAAA;;AAAC;EAAmD,yBAAwB,EAAA;;AAAC;EAAyI,aAAY,EAAA;;AAAC;EAAuI,cAAa,EAAA;;AAAC;EAAyF,gBAAe,EAAA;;AAAC;EAAuH,sBAAqB;EAAC,WAAU;EAAC,aAAY;EAAC,cAAa;EAAC,qBAAoB,EAAA;;AAAC;EAAqK,yBAAwB,EAAA;;AAAC;EAAqH,aAAY;EAAC,WAAU;EAAC,aAAY;EAAC,YAAW;EAAC,wBAAe;UAAf,gBAAe;EAAC,8CAA6C;EAAC,eAAc;EAAC,WAAU;EAAC,sBAAqB;EAAC,YAAW;EAAC,aAAY;EAAC,0BAAyB;EAAC,8BAAqB;UAArB,sBAAqB;EAAC,gBAAe;EAAC,gBAAe,EAAA;;AAAC;EAAyE,eAAc;EAAC,MAAK;EAAC,OAAM;EAAC,sBAAqB;EAAC,aAAY,EAAA;;AAAC;EAAuG,sBAAqB,EAAA;;AAAC;EAAyG,aAAY,EAAA;;AAAC;EAA2F,cAAa;EAAC,WAAU;EAAC,eAAc;EAAC,uBAAsB;EAAC,YAAW;EAAC,gBAAe;EAAC,WAAU;EAAC,SAAQ;EAAC,6BAA4B,EAAA;;AAAC;EAAuG,SAAQ;EAAC,+BAA8B,EAAA;;AAAC;EAAmH,uBAAsB,EAAA;;AAAC;EAAmH,WAAU,EAAA;;AAAC;EAAuH,cAAa,EAAA;;AAAC;EAAuH,cAAa,EAAA;;AAAC;EAAiH,cAAa,EAAA;;AAAC;EAAuH,cAAa,EAAA;;AAAC;EAAqH,cAAa,EAAA;;AAAC;EAAyG,kBAAiB;EAAC,0BAAyB;EAAC,gBAAe;EAAC,WAAU;EAAC,yBAAwB,EAAA;;AAAC;EAA+H,iBAAgB,EAAA;;AAAC;EAAyH,yBAAwB;EAAC,oCAAmC,EAAA;;AAAC;EAAmH,yBAAwB;EAAC,oCAAmC,EAAA;;AAAC;EAAyH,yBAAwB;EAAC,oCAAmC,EAAA;;AAAC;EAAuH,yBAAwB;EAAC,oCAAmC,EAAA;;AAAC;EAAmF,yBAAwB;EAAC,8BAA6B;EAAC,+BAA8B;EAAC,0BAAyB,EAAA;;AAAC;EAAmH,gBAAe;EAAC,WAAU;EAAC,WAAU;EAAC,iBAAgB,EAAA;;AAAC;EAAiJ,WAAU;EAAC,gBAAe;EAAC,6BAA4B,EAAA;;AAAC;EAAiI,eAAc,EAAA;;AAAC;EAA+J,aAAY,EAAA;;AAAC;EAAuF,aAAY,EAAA;;AAAC;EAA2B,SAAQ,EAAA;;AAAC;EAA6D,UAAS,EAAA;;AAAC;EAAsB,aAAY;EAAC,eAAc,EAAA;;AAAC;EAAyC,qBAAoB;EAAC,gBAAe;EAAC,gBAAe;EAAC,uBAAsB;EAAC,mBAAkB;EAAC,sBAAqB,EAAA;;AAAC;EAA6B,qBAAoB,EAAA;;AAAC;EAAc,kBAAiB,EAAA;;AAAC;EAA6C,SAAQ;EAAC,oBAAmB,EAAA;;AAAC;EAA6E,eAAc;EAAC,cAAa;EAAC,iBAAgB,EAAA;;AAAC;EAAqG,YAAW;EAAC,YAAW,EAAA;;AAAC;EAAmJ,eAAc,EAAA;;AAAC;EAA2N,6BAA4B;EAAC,UAAS;EAAC,WAAU;EAAC,YAAW;EAAC,eAAc,EAAA;;AAAC;EAA6N,6BAA4B;EAAC,UAAS;EAAC,UAAS;EAAC,WAAU;EAAC,oRAAmR,EAAA;;AAAC;EAAyN,6BAA4B;EAAC,UAAS;EAAC,UAAS;EAAC,WAAU;EAAC,oRAAmR,EAAA;;AAAC;EAAwO,SAAQ;EAAC,iBAAgB,EAAA;;AAAC;EAA2uB,SAAQ;EAAC,UAAS,EAAA;;AAAC;EAAqH,gBAAe,EAAA;;AAAC;EAAuG,WAAU;EAAC,iBAAgB,EAAA;;AAAC;EAAyI,qBAAoB;EAAC,SAAQ;EAAC,YAAW,EAAA;;AAAC;EAAiK,aAAY,EAAA;;AAAC;EAAiL,eAAc;EAAC,eAAc;EAAC,kBAAiB;EAAC,6BAA4B,EAAA;;AAAC;EAA4V,eAAc;EAAC,WAAU;EAAC,cAAa;EAAC,eAAc;EAAC,WAAU;EAAC,kBAAiB,EAAA;;AAAC;EAAoX,gBAAe,EAAA;;AAAC;EAAyK,YAAW,EAAA;;AAAC;EAAuL,aAAY,EAAA;;AAAC;EAAiN,sBAAqB,EAAA;;AAAC;EAA2G,gBAAe;EAAC,YAAW,EAAA;;AAAC;EAA2I,iBAAgB,EAAA;;AAAC;EAAqF,gBAAe,EAAA;;AAAC;EAAiG,QAAO;EAAC,UAAS,EAAA;;AAAC;EAAiH,UAAS;EAAC,qBAAoB,EAAA;;AAAC;EAA+G,WAAU;EAAC,qBAAoB,EAAA;;AAAC;EAAmH,cAAa;EAAC,kBAAiB,EAAA;;AAAC;EAAmI,mBAAkB,EAAA;;AAAC;EAAiF,eAAc,EAAA;;AAAC;EAAqF,cAAa,EAAA;;AAAC;EAA6H,WAAU;EAAC,YAAW;EAAC,UAAS;EAAC,SAAQ;EAAC,sBAAqB,EAAA;;AAAC;EAAyI,sBAAqB,EAAA;;AAAC;EAA0B,UAAS;EAAC,kBAAiB;EAAC,gDAAmC;UAAnC,wCAAmC,EAAA;;AAAC;EAAwB,cAAa;EAAC,eAAc,EAAA;;AAAC;EAA6B,SAAQ,EAAA;;AAAC;EAA8C,iBAAgB;EAAC,eAAc;EAAC,cAAa;EAAC,cAAa;EAAC,kBAAiB;EAAC,0BAAyB;EAAC,mBAAkB,EAAA;;AAAC;EAA4B;IAA8B,YAAW,EAAA,EAAE;;AAAA;EAAkB,kBAAiB;EAAC,aAAY;EAAC,WAAU,EAAA;;AAAC;EAAqC,kBAAiB;EAAC,aAAY;EAAC,sBAAqB,EAAA;;AAAC;EAAyC,kBAAiB,EAAA;;AAAC;EAAgE,WAAU;EAAC,YAAW;EAAC,sBAAqB;EAAC,mBAAkB;EAAC,kBAAiB;EAAC,gBAAe;EAAC,WAAU;EAAC,+DAA8D;EAAC,yBAAwB,EAAA;;AAAC;EAA8K,UAAS;EAAC,WAAU;EAAC,sBAAqB,EAAA;;AAAC;EAA0D,sBAAqB,EAAA;;AAAC;EAAsD,SAAQ;EAAC,UAAS;EAAC,kBAAiB;EAAC,mBAAkB,EAAA;;AAAC;EAAsD,SAAQ;EAAC,WAAU;EAAC,mBAAkB;EAAC,iBAAgB,EAAA;;AAAC;EAAsD,YAAW;EAAC,UAAS;EAAC,gBAAe;EAAC,kBAAiB,EAAA;;AAAC;EAAsD,WAAU;EAAC,YAAW;EAAC,iBAAgB,EAAA;;AAAC;EAA0E,eAAc;EAAC,gBAAe;EAAC,iBAAgB,EAAA;;AAAC;EAAkE,QAAO;EAAC,SAAQ;EAAC,YAAW;EAAC,WAAU;EAAC,WAAU;EAAC,sBAAqB;EAAC,eAAc;EAAC,kBAAiB;EAAC,mBAAkB;EAAC,kBAAiB;EAAC,gBAAe;EAAC,WAAU;EAAC,+DAA8D;EAAC,yBAAwB,EAAA;;AAAC;EAAmB,gBAAe;EAAC,YAAW,EAAA;;AAAC;EAAoC,YAAW;EAAC,iBAAgB;EAAC,cAAa,EAAA;;AAAC;EAAqE,yBAAwB;EAAC,YAAW,EAAA;;AAAC;EAAuJ,cAAa;EAAC,WAAU;EAAC,gBAAe;EAAC,gBAAe;EAAC,WAAU;EAAC,mBAAkB;EAAC,qBAAoB;EAAC,yBAAwB;EAAC,UAAS;EAAC,eAAc,EAAA;;AAAC;EAAwB,gBAAe;EAAC,qBAAoB;EAAC,sBAAqB;EAAC,eAAc,EAAA;;AElS52e;4EFsrB4E;AGtrB5E;;;;;sDH4rBsD;AG5dtD;;;;;;;yBHoeyB;AGtdzB;;;;;;;;;;;;yBHmeyB;AGvczB;;;yBH2cyB;AGvazB;;;;;;;;;;;yBHmbyB;AGzZzB;;;yBH6ZyB;AG3YzB;;;yBH+YyB;AGrWzB,wBAAA;AAQA,0BAAA;ADrYA;4EFsuB4E;AI7uB5E;4EJ+uB4E;AI7uB5E,+CAAA;AAQA,+FAAA;AAQA;;;;;;kFJsuBkF;AI9tBlF;4EJguB4E;AI1tB5E;4EJ4tB4E;AI1tB5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AAmFA;4EJwkB4E;AIpkB5E;4EJskB4E;AIvjBR,kGAAA;AACG,2EAAA;AAcvE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EJshB4E;AI7f5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAiBA,iBAAA;AAQA,gBAAA;AAGA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;CJ+XC;AIlWD,UAAA;AAuBA,aAAA;AAIA;4EJ2U4E;AInU5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EJmU4E;AIjUlE,mFAAA;AAOV;4EJ6T4E;AI3TG,mEAAA;AAE/E;4EJ4T4E;AItT5E,oEAAA;AAUA;4EJ+S4E;AI3S5E;4EJ6S4E;AI3S5B,0BAAA;AACH,iBAAA;AAG7C;4EJ2S4E;AItS5E;4EJwS4E;AIlS5E;4EJoS4E;AIhS5E;4EJkS4E;AI/R5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACA,8DAAA;AACH,qDAAA;AAEjD,gCAAA;AAGA,qBAAA;AAC8D,uBAAA;AAO9D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EJ+Q4E;AIhP5E,oBAAA;AACA,eAAA;AAMA,eAAA;AAGA,uBAAA;AAQA,qBAAA;AAIA,mBAAA;AAKA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4EJqL4E;AInL5E,eAAA;AAIA;4EJkL4E;AI3K5E;4EJ6K4E;AIzK5E;4EJ2K4E;AI1J5E;4EJ4J4E;AIrJ5E;4EJuJ4E;AI/I5E;4EJiJ4E;AIzI5E;4EJ2I4E;AItI5E,oBAAA;AAGA,0DAAA;AAQA,kBAAA;AHxzBA;EACC,yDG+gB8D;EH9gB9D,oBEmQkC;EFlQlC,qBAAqB,EAAA;;AAGtB;EACC,cGyfwC,EAAA;;AHtfzC;EACC,gBAAgB;EAChB,gBAAgB,EAAA;;AAIjB;EACC,gBAAgB,EAAA;;AAGjB;;;;;;;;;;;;EAYI,gBAAgB;EAChB,cAAc;EACjB,oBEmOkC;EFlO/B,gBAAgB;EAEhB,oBAA0C,EAAA;;AAG9C;;;;EAIC,oBEyNkC,EAAA;;AFtNnC;;EAEC,mBEoNkC,EAAA;;AFjNnC;;;;EAIC,oBE6MkC,EAAA;;AF1MnC,kBAAA;AACA;EACC,cAA2B,EAAA;;AAG5B,kBAAA;AACA;EAEC,sJAAsG;EAAtG,wFAAsG;EACtG,cGnE2B;EHoExB,qBAAqB;EACrB,4BAA4B;EAC5B,6BAA6B;EAC7B,oCAAoC;EACpC,iBAAiB,EAAA;;AAGrB,+CAAA;AI9EA;;;;;;;;;;;;;;;;;;;;;;;;;;CLohCC;AKl/BD;;;;;;;;;CL4/BC;AK1zBD;;EL6zBE;AK1wBF;;;;;;;;;;;;;;;;;;;;;;;;GLmyBG;AK7uBH,aAAA;ALvCA;EACE,yBAAyB;EACzB,kBAAkB;EAClB,mBAAmB;EACnB,4CAA4C;EAC5C,+PAG+D,EAAA;;AAGjE;EACC,iCIyC2C,EAAA;;AJrC5C;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa,EAAA;;AAGd;EACC,kBAAkB;EACf,oBAAoB,EAAA;;AAGxB;EACC,0BAA0B,EAAA;;AAG3B;EACC,cAAc,EAAA;;AAGf;EACC,eAAe;EACf,kBAAkB,EAAA;;AAvCm8d;EA4Cr9d,yBIzUwB,EAAA;;AJ6Rm1d;EAgD32d,yBAAyB;EACzB,YAAY;EACZ,kBAAkB;EAClB,oBAAoB;EACpB,qBAAqB;EACrB,aAAa,EAAA;;AAGd;EACC,sBAAsB,EAAA;;AAGvB;EACC,aAAa,EAAA;;AAGd;EAEE,eAAc,EAAA;;AAIhB;EACC,kBAAkB,EAAA", "file": "summernote.css", "sourcesContent": ["/*!\n * \n * Super simple wysiwyg editor v0.8.16\n * https://summernote.org\n * \n * \n * Copyright 2013- <PERSON> and other contributors\n * summernote may be freely distributed under the MIT license.\n * \n * Date: 2020-02-19T09:12Z\n * \n */\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: auto;\n  src: url(\"webfonts/summernote.eot\");\n  src: url(\"webfonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"webfonts/summernote.woff2\") format(\"woff2\"), url(\"webfonts/summernote.woff\") format(\"woff\"), url(\"webfonts/summernote.ttf\") format(\"truetype\");}\n\n\n[class^=\"note-icon\"]:before,\n[class*=\" note-icon\"]:before {\n  display: inline-block;\n  font-family: summernote;\n  font-style: normal;\n  font-size: inherit;\n  text-decoration: inherit;\n  text-rendering: auto;\n  text-transform: none;\n  vertical-align: middle;\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  speak: none;\n}\n\n\n.note-icon-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.note-icon-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.note-icon-pull-left {\n  float: left;\n}\n\n.note-icon-pull-right {\n  float: right;\n}\n\n.note-icon.note-icon-pull-left {\n  margin-right: 0.3em;\n}\n\n.note-icon.note-icon-pull-right {\n  margin-left: 0.3em;\n}\n\n\n.note-icon-align::before {\n  content: \"\\ea01\";\n}\n\n.note-icon-align-center::before {\n  content: \"\\ea02\";\n}\n\n.note-icon-align-indent::before {\n  content: \"\\ea03\";\n}\n\n.note-icon-align-justify::before {\n  content: \"\\ea04\";\n}\n\n.note-icon-align-left::before {\n  content: \"\\ea05\";\n}\n\n.note-icon-align-outdent::before {\n  content: \"\\ea06\";\n}\n\n.note-icon-align-right::before {\n  content: \"\\ea07\";\n}\n\n.note-icon-arrow-circle-down::before {\n  content: \"\\ea08\";\n}\n\n.note-icon-arrow-circle-left::before {\n  content: \"\\ea09\";\n}\n\n.note-icon-arrow-circle-right::before {\n  content: \"\\ea0a\";\n}\n\n.note-icon-arrow-circle-up::before {\n  content: \"\\ea0b\";\n}\n\n.note-icon-arrows-alt::before {\n  content: \"\\ea0c\";\n}\n\n.note-icon-arrows-h::before {\n  content: \"\\ea0d\";\n}\n\n.note-icon-arrows-v::before {\n  content: \"\\ea0e\";\n}\n\n.note-icon-bold::before {\n  content: \"\\ea0f\";\n}\n\n.note-icon-caret::before {\n  content: \"\\ea10\";\n}\n\n.note-icon-chain-broken::before {\n  content: \"\\ea11\";\n}\n\n.note-icon-circle::before {\n  content: \"\\ea12\";\n}\n\n.note-icon-close::before {\n  content: \"\\ea13\";\n}\n\n.note-icon-code::before {\n  content: \"\\ea14\";\n}\n\n.note-icon-col-after::before {\n  content: \"\\ea15\";\n}\n\n.note-icon-col-before::before {\n  content: \"\\ea16\";\n}\n\n.note-icon-col-remove::before {\n  content: \"\\ea17\";\n}\n\n.note-icon-eraser::before {\n  content: \"\\ea18\";\n}\n\n.note-icon-float-left::before {\n  content: \"\\ea19\";\n}\n\n.note-icon-float-none::before {\n  content: \"\\ea1a\";\n}\n\n.note-icon-float-right::before {\n  content: \"\\ea1b\";\n}\n\n.note-icon-font::before {\n  content: \"\\ea1c\";\n}\n\n.note-icon-frame::before {\n  content: \"\\ea1d\";\n}\n\n.note-icon-italic::before {\n  content: \"\\ea1e\";\n}\n\n.note-icon-link::before {\n  content: \"\\ea1f\";\n}\n\n.note-icon-magic::before {\n  content: \"\\ea20\";\n}\n\n.note-icon-menu-check::before {\n  content: \"\\ea21\";\n}\n\n.note-icon-minus::before {\n  content: \"\\ea22\";\n}\n\n.note-icon-orderedlist::before {\n  content: \"\\ea23\";\n}\n\n.note-icon-pencil::before {\n  content: \"\\ea24\";\n}\n\n.note-icon-picture::before {\n  content: \"\\ea25\";\n}\n\n.note-icon-question::before {\n  content: \"\\ea26\";\n}\n\n.note-icon-redo::before {\n  content: \"\\ea27\";\n}\n\n.note-icon-rollback::before {\n  content: \"\\ea28\";\n}\n\n.note-icon-row-above::before {\n  content: \"\\ea29\";\n}\n\n.note-icon-row-below::before {\n  content: \"\\ea2a\";\n}\n\n.note-icon-row-remove::before {\n  content: \"\\ea2b\";\n}\n\n.note-icon-special-character::before {\n  content: \"\\ea2c\";\n}\n\n.note-icon-square::before {\n  content: \"\\ea2d\";\n}\n\n.note-icon-strikethrough::before {\n  content: \"\\ea2e\";\n}\n\n.note-icon-subscript::before {\n  content: \"\\ea2f\";\n}\n\n.note-icon-summernote::before {\n  content: \"\\ea30\";\n}\n\n.note-icon-superscript::before {\n  content: \"\\ea31\";\n}\n\n.note-icon-table::before {\n  content: \"\\ea32\";\n}\n\n.note-icon-text-height::before {\n  content: \"\\ea33\";\n}\n\n.note-icon-trash::before {\n  content: \"\\ea34\";\n}\n\n.note-icon-underline::before {\n  content: \"\\ea35\";\n}\n\n.note-icon-undo::before {\n  content: \"\\ea36\";\n}\n\n.note-icon-unorderedlist::before {\n  content: \"\\ea37\";\n}\n\n.note-icon-video::before {\n  content: \"\\ea38\";\n}\n\n\n.note-editor{position:relative}.note-editor .note-dropzone{position:absolute;display:none;z-index:100;color:#87cefa;background-color:#fff;opacity:.95}.note-editor .note-dropzone .note-dropzone-message{display:table-cell;vertical-align:middle;text-align:center;font-size:28px;font-weight:700}.note-editor .note-dropzone.hover{color:#098ddf}.note-editor.dragover .note-dropzone{display:table}.note-editor .note-editing-area{position:relative}.note-editor .note-editing-area .note-editable{outline:none}.note-editor .note-editing-area .note-editable sup{vertical-align:super}.note-editor .note-editing-area .note-editable sub{vertical-align:sub}.note-editor .note-editing-area .note-editable img.note-float-left{margin-right:10px}.note-editor .note-editing-area .note-editable img.note-float-right{margin-left:10px}.note-editor.note-frame,.note-editor.note-airframe{border:1px solid #a9a9a9}.note-editor.note-frame.codeview .note-editing-area .note-editable,.note-editor.note-airframe.codeview .note-editing-area .note-editable{display:none}.note-editor.note-frame.codeview .note-editing-area .note-codable,.note-editor.note-airframe.codeview .note-editing-area .note-codable{display:block}.note-editor.note-frame .note-editing-area,.note-editor.note-airframe .note-editing-area{overflow:hidden}.note-editor.note-frame .note-editing-area .note-editable,.note-editor.note-airframe .note-editing-area .note-editable{background-color:#fff;color:#000;padding:10px;overflow:auto;word-wrap:break-word}.note-editor.note-frame .note-editing-area .note-editable[contenteditable=false],.note-editor.note-airframe .note-editing-area .note-editable[contenteditable=false]{background-color:#e5e5e5}.note-editor.note-frame .note-editing-area .note-codable,.note-editor.note-airframe .note-editing-area .note-codable{display:none;width:100%;padding:10px;border:none;box-shadow:none;font-family:Menlo,Monaco,monospace,sans-serif;font-size:14px;color:#ccc;background-color:#222;resize:none;outline:none;-ms-box-sizing:border-box;box-sizing:border-box;border-radius:0;margin-bottom:0}.note-editor.note-frame.fullscreen,.note-editor.note-airframe.fullscreen{position:fixed;top:0;left:0;width:100% !important;z-index:1050}.note-editor.note-frame.fullscreen .note-editable,.note-editor.note-airframe.fullscreen .note-editable{background-color:#fff}.note-editor.note-frame.fullscreen .note-resizebar,.note-editor.note-airframe.fullscreen .note-resizebar{display:none}.note-editor.note-frame .note-status-output,.note-editor.note-airframe .note-status-output{display:block;width:100%;font-size:14px;line-height:1.42857143;height:20px;margin-bottom:0;color:#000;border:0;border-top:1px solid #e2e2e2}.note-editor.note-frame .note-status-output:empty,.note-editor.note-airframe .note-status-output:empty{height:0;border-top:0 solid transparent}.note-editor.note-frame .note-status-output .pull-right,.note-editor.note-airframe .note-status-output .pull-right{float:right !important}.note-editor.note-frame .note-status-output .text-muted,.note-editor.note-airframe .note-status-output .text-muted{color:#777}.note-editor.note-frame .note-status-output .text-primary,.note-editor.note-airframe .note-status-output .text-primary{color:#286090}.note-editor.note-frame .note-status-output .text-success,.note-editor.note-airframe .note-status-output .text-success{color:#3c763d}.note-editor.note-frame .note-status-output .text-info,.note-editor.note-airframe .note-status-output .text-info{color:#31708f}.note-editor.note-frame .note-status-output .text-warning,.note-editor.note-airframe .note-status-output .text-warning{color:#8a6d3b}.note-editor.note-frame .note-status-output .text-danger,.note-editor.note-airframe .note-status-output .text-danger{color:#a94442}.note-editor.note-frame .note-status-output .alert,.note-editor.note-airframe .note-status-output .alert{margin:-7px 0 0 0;padding:7px 10px 2px 10px;border-radius:0;color:#000;background-color:#f5f5f5}.note-editor.note-frame .note-status-output .alert .note-icon,.note-editor.note-airframe .note-status-output .alert .note-icon{margin-right:5px}.note-editor.note-frame .note-status-output .alert-success,.note-editor.note-airframe .note-status-output .alert-success{color:#3c763d !important;background-color:#dff0d8 !important}.note-editor.note-frame .note-status-output .alert-info,.note-editor.note-airframe .note-status-output .alert-info{color:#31708f !important;background-color:#d9edf7 !important}.note-editor.note-frame .note-status-output .alert-warning,.note-editor.note-airframe .note-status-output .alert-warning{color:#8a6d3b !important;background-color:#fcf8e3 !important}.note-editor.note-frame .note-status-output .alert-danger,.note-editor.note-airframe .note-status-output .alert-danger{color:#a94442 !important;background-color:#f2dede !important}.note-editor.note-frame .note-statusbar,.note-editor.note-airframe .note-statusbar{background-color:#f5f5f5;border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top:1px solid #ddd}.note-editor.note-frame .note-statusbar .note-resizebar,.note-editor.note-airframe .note-statusbar .note-resizebar{padding-top:1px;height:9px;width:100%;cursor:ns-resize}.note-editor.note-frame .note-statusbar .note-resizebar .note-icon-bar,.note-editor.note-airframe .note-statusbar .note-resizebar .note-icon-bar{width:20px;margin:1px auto;border-top:1px solid #a9a9a9}.note-editor.note-frame .note-statusbar.locked .note-resizebar,.note-editor.note-airframe .note-statusbar.locked .note-resizebar{cursor:default}.note-editor.note-frame .note-statusbar.locked .note-resizebar .note-icon-bar,.note-editor.note-airframe .note-statusbar.locked .note-resizebar .note-icon-bar{display:none}.note-editor.note-frame .note-placeholder,.note-editor.note-airframe .note-placeholder{padding:10px}.note-editor.note-airframe{border:0}.note-editor.note-airframe .note-editing-area .note-editable{padding:0}.note-popover.popover{display:none;max-width:none}.note-popover.popover .popover-content a{display:inline-block;max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;vertical-align:middle}.note-popover.popover .arrow{left:20px !important}.note-toolbar{position:relative}.note-popover .popover-content,.note-toolbar{margin:0;padding:0 0 5px 5px}.note-popover .popover-content>.note-btn-group,.note-toolbar>.note-btn-group{margin-top:5px;margin-left:0;margin-right:5px}.note-popover .popover-content .note-btn-group .note-table,.note-toolbar .note-btn-group .note-table{min-width:0;padding:5px}.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker,.note-toolbar .note-btn-group .note-table .note-dimension-picker{font-size:18px}.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher,.note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher{position:absolute !important;z-index:3;width:10em;height:10em;cursor:pointer}.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted,.note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted{position:relative !important;z-index:1;width:5em;height:5em;background:url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat}.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted,.note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted{position:absolute !important;z-index:2;width:1em;height:1em;background:url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat}.note-popover .popover-content .note-style .dropdown-style blockquote,.note-popover .popover-content .note-style .dropdown-style pre,.note-toolbar .note-style .dropdown-style blockquote,.note-toolbar .note-style .dropdown-style pre{margin:0;padding:5px 10px}.note-popover .popover-content .note-style .dropdown-style h1,.note-popover .popover-content .note-style .dropdown-style h2,.note-popover .popover-content .note-style .dropdown-style h3,.note-popover .popover-content .note-style .dropdown-style h4,.note-popover .popover-content .note-style .dropdown-style h5,.note-popover .popover-content .note-style .dropdown-style h6,.note-popover .popover-content .note-style .dropdown-style p,.note-toolbar .note-style .dropdown-style h1,.note-toolbar .note-style .dropdown-style h2,.note-toolbar .note-style .dropdown-style h3,.note-toolbar .note-style .dropdown-style h4,.note-toolbar .note-style .dropdown-style h5,.note-toolbar .note-style .dropdown-style h6,.note-toolbar .note-style .dropdown-style p{margin:0;padding:0}.note-popover .popover-content .note-color-all .note-dropdown-menu,.note-toolbar .note-color-all .note-dropdown-menu{min-width:337px}.note-popover .popover-content .note-color .dropdown-toggle,.note-toolbar .note-color .dropdown-toggle{width:20px;padding-left:5px}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette,.note-toolbar .note-color .note-dropdown-menu .note-palette{display:inline-block;margin:0;width:160px}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette:first-child,.note-toolbar .note-color .note-dropdown-menu .note-palette:first-child{margin:0 5px}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-palette-title,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-palette-title{font-size:12px;margin:2px 7px;text-align:center;border-bottom:1px solid #eee}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset,.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select{font-size:11px;margin:3px;padding:0 3px;cursor:pointer;width:100%;border-radius:5px}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select:hover,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select:hover{background:#eee}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-row,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-row{height:20px}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select-btn,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select-btn{display:none}.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn,.note-toolbar .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn{border:1px solid #eee}.note-popover .popover-content .note-para .note-dropdown-menu,.note-toolbar .note-para .note-dropdown-menu{min-width:216px;padding:5px}.note-popover .popover-content .note-para .note-dropdown-menu>div:first-child,.note-toolbar .note-para .note-dropdown-menu>div:first-child{margin-right:5px}.note-popover .popover-content .note-dropdown-menu,.note-toolbar .note-dropdown-menu{min-width:160px}.note-popover .popover-content .note-dropdown-menu.right,.note-toolbar .note-dropdown-menu.right{right:0;left:auto}.note-popover .popover-content .note-dropdown-menu.right::before,.note-toolbar .note-dropdown-menu.right::before{right:9px;left:auto !important}.note-popover .popover-content .note-dropdown-menu.right::after,.note-toolbar .note-dropdown-menu.right::after{right:10px;left:auto !important}.note-popover .popover-content .note-dropdown-menu.note-check a i,.note-toolbar .note-dropdown-menu.note-check a i{color:#00bfff;visibility:hidden}.note-popover .popover-content .note-dropdown-menu.note-check a.checked i,.note-toolbar .note-dropdown-menu.note-check a.checked i{visibility:visible}.note-popover .popover-content .note-fontsize-10,.note-toolbar .note-fontsize-10{font-size:10px}.note-popover .popover-content .note-color-palette,.note-toolbar .note-color-palette{line-height:1}.note-popover .popover-content .note-color-palette div .note-color-btn,.note-toolbar .note-color-palette div .note-color-btn{width:20px;height:20px;padding:0;margin:0;border:1px solid #fff}.note-popover .popover-content .note-color-palette div .note-color-btn:hover,.note-toolbar .note-color-palette div .note-color-btn:hover{border:1px solid #000}.note-modal .modal-dialog{outline:0;border-radius:5px;box-shadow:0 3px 9px rgba(0,0,0,.5)}.note-modal .form-group{margin-left:0;margin-right:0}.note-modal .note-modal-form{margin:0}.note-modal .note-image-dialog .note-dropzone{min-height:100px;font-size:30px;line-height:4;color:#d3d3d3;text-align:center;border:4px dashed #d3d3d3;margin-bottom:10px}@-moz-document url-prefix(){.note-modal .note-image-input{height:auto}}.note-placeholder{position:absolute;display:none;color:gray}.note-handle .note-control-selection{position:absolute;display:none;border:1px solid #000}.note-handle .note-control-selection>div{position:absolute}.note-handle .note-control-selection .note-control-selection-bg{width:100%;height:100%;background-color:#000;-webkit-opacity:.3;-khtml-opacity:.3;-moz-opacity:.3;opacity:.3;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(opacity=30);filter:alpha(opacity=30)}.note-handle .note-control-selection .note-control-handle,.note-handle .note-control-selection .note-control-sizing,.note-handle .note-control-selection .note-control-holder{width:7px;height:7px;border:1px solid #000}.note-handle .note-control-selection .note-control-sizing{background-color:#000}.note-handle .note-control-selection .note-control-nw{top:-5px;left:-5px;border-right:none;border-bottom:none}.note-handle .note-control-selection .note-control-ne{top:-5px;right:-5px;border-bottom:none;border-left:none}.note-handle .note-control-selection .note-control-sw{bottom:-5px;left:-5px;border-top:none;border-right:none}.note-handle .note-control-selection .note-control-se{right:-5px;bottom:-5px;cursor:se-resize}.note-handle .note-control-selection .note-control-se.note-control-holder{cursor:default;border-top:none;border-left:none}.note-handle .note-control-selection .note-control-selection-info{right:0;bottom:0;padding:5px;margin:5px;color:#fff;background-color:#000;font-size:12px;border-radius:5px;-webkit-opacity:.7;-khtml-opacity:.7;-moz-opacity:.7;opacity:.7;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(opacity=70);filter:alpha(opacity=70)}.note-hint-popover{min-width:100px;padding:2px}.note-hint-popover .popover-content{padding:3px;max-height:150px;overflow:auto}.note-hint-popover .popover-content .note-hint-group .note-hint-item{display:block !important;padding:3px}.note-hint-popover .popover-content .note-hint-group .note-hint-item.active,.note-hint-popover .popover-content .note-hint-group .note-hint-item:hover{display:block;clear:both;font-weight:400;line-height:1.4;color:#fff;white-space:nowrap;text-decoration:none;background-color:#428bca;outline:0;cursor:pointer}.note-toolbar .note-btn{background:#fff;border-color:#dae0e5;padding:.28rem .65rem;font-size:13px}\n\n@import './src/scss/_imports/_global-import';\r\n\r\n@font-face {\r\n  font-family: \"summernote\";\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  src: url(\"../../../webfonts/summernote.eot\");\r\n  src: url(\"../../../webfonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), \r\n       url(\"../../../webfonts/summernote.woff2\") format(\"woff2\"), \r\n       url(\"../../../webfonts/summernote.woff\") format(\"woff\"), \r\n       url(\"../../../webfonts/summernote.ttf\") format(\"truetype\");\r\n}\r\n\r\n.note-editor.note-frame {\r\n\tborder-color: $card-border-color;\r\n}\r\n\r\n\r\n.note-para .note-btn-group .dropdown-menu.show {\r\n\tdisplay: flex;\r\n}\r\n\r\n.card-header.note-toolbar > .btn-group {\r\n\tmargin-top: 0.5rem;\r\n    margin-right: 0.5rem;\r\n}\r\n\r\n.card-header.note-toolbar {\r\n\tpadding: 0 0 0.5rem 0.5rem;\r\n}\r\n\r\n.note-btn.btn-light {\r\n\tcolor: #7b7b7b;\r\n}\r\n\r\n.note-popover .popover-content {\r\n\tpadding: 0.3rem;\r\n\tborder-radius: 5px;\r\n}\r\n\r\n.note-hint-popover .popover-content .note-hint-group .note-hint-item.active, \r\n.note-hint-popover .popover-content .note-hint-group .note-hint-item:hover {\r\n\tbackground-color: $info-500;\r\n}\r\n\r\n.note-hint-popover .popover-content .note-hint-group .note-hint-item {\r\n\tdisplay: block !important;\r\n\tpadding: 5px;\r\n\tborder-radius: 4px;\r\n\tpadding-left: 0.7rem;\r\n\tpadding-right: 0.7rem;\r\n\tmargin: 3px 0;\r\n}\r\n\r\n.note-btn-group .dropdown-item {\r\n\tpadding: 0.5rem 0.5rem;\r\n}\r\n\r\n.note-popover .popover-content > .btn-group {\r\n\tmargin-top: 0;\r\n}\r\n\r\n.note-popover {\r\n\t.note-btn-group:last-child {\r\n\t\tmargin-right:0;\r\n\t}\r\n}\r\n\r\n.note-editor.note-frame .note-editing-area {\r\n\tborder-radius: 4px;\r\n}", "@import url($font-import);\r\n\r\nbody {\r\n\tfont-family: $page-font;\r\n\tfont-size: rem($fs-base);\r\n\tletter-spacing: 0.1px;\r\n}\r\n\r\n.page-content {\r\n\tcolor: $base-text-color;\r\n}\r\n\r\nh1, h2, h3, h4, h5, h6 {\r\n\tline-height: 1.3;\r\n\tfont-weight: 400;\r\n\t//color:$fusion-500;\r\n}\r\n\r\nstrong {\r\n\tfont-weight: 500;\r\n}\r\n\r\nh1 small, \r\nh2 small, \r\nh3 small, \r\nh4 small, \r\nh5 small, \r\nh6 small, \r\n.h1 small, \r\n.h2 small, \r\n.h3 small, \r\n.h4 small, \r\n.h5 small, \r\n.h6 small {\r\n    font-weight: 300;\r\n    display: block;\r\n\tfont-size: rem($fs-lg);\r\n    line-height: 1.5;\r\n    //letter-spacing: -0.2px;\r\n    margin:2px 0 ($grid-gutter-width-base / 2);\r\n}\r\n\r\nh2 small, \r\nh3 small, \r\n.h2 small, \r\n.h3 small, {\r\n\tfont-size: rem($fs-lg);\r\n}\r\n\r\nh4 small, \r\n.h4 small {\r\n\tfont-size: rem($fs-md);\r\n}\r\n\r\nh5 small, \r\nh6 small, \r\n.h5 small, \r\n.h6 small {\r\n\tfont-size: rem($fs-base);\t\r\n}\r\n\r\n/* contrast text */\r\n.text-contrast {\r\n\tcolor: lighten($black, 20%);\r\n}\r\n\r\n/* text-gradient */\r\n.text-gradient {\r\n\tbackground: -webkit-linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tbackground: linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tcolor: $primary-500;\r\n    background-clip: text;\r\n    text-fill-color: transparent;\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    text-shadow: none;\r\n}\r\n\r\n/* looking for font size? Check _helpers.scss */", "/* #BOOTSTRAP AND MIXINS - Base Unmodified Bootstrap file with theme mixins\r\n========================================================================== */\r\n@import './node_modules/bootstrap/scss/functions';\r\n@import './node_modules/bootstrap/scss/variables'; \r\n@import './node_modules/bootstrap/scss/mixins';\r\n@import './src/scss/_mixins/mixins';\r\n\r\n/* #BASE - Base Variable file along with font library, and colors.\r\n========================================================================== */\r\n@import './src/scss/_modules/variables';\r\n@import './src/scss/_modules/_fonts';\r\n@import './src/scss/_modules/_placeholders';\r\n@import './src/scss/_modules/_custom';", "/*---------------------------------------------------\r\n    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) \r\n  -------------------------------- -------------------\r\n    LESS ELEMENTS made by <PERSON> (http://fadeyev.net)\r\n    SASS port by <PERSON> (http://samuelbeek.com) \r\n  ---------------------------------------------------*/\r\n \r\n@mixin gradient-img($start: #EEE,$stop: #FFF) {\r\n  background-color: $start;\r\n  background-image: -webkit-linear-gradient(top,$start,$stop);\r\n  background-image: linear-gradient(to top,$start,$stop);\r\n}\r\n\r\n@mixin gradient($color: #F5F5F5,$start: #EEE,$stop: #FFF) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,$start),color-stop(1,$stop));\r\n    background:-ms-linear-gradient(bottom,$start,$stop);\r\n    background:-moz-linear-gradient(center bottom,$start 0%,$stop 100%);\r\n    background:-o-linear-gradient($stop,$start);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=$start,endColorstr=$stop)\r\n}\r\n\r\n@mixin bw-gradient($color: #F5F5F5,$start: 0,$stop: 255) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,#000),color-stop(1,#000));\r\n    background:-ms-linear-gradient(bottom,#000 0%,#000 100%);\r\n    background:-moz-linear-gradient(center bottom,#000 0%,#000 100%);\r\n    background:-o-linear-gradient(#000,#000);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=rgb($start,$start,$start),endColorstr=rgb($stop,$stop,$stop))\r\n}\r\n\r\n@mixin bordered($top-color: #EEE,$right-color: #EEE,$bottom-color: #EEE,$left-color: #EEE) {\r\n    border-top:solid 1px $top-color;\r\n    border-left:solid 1px $left-color;\r\n    border-right:solid 1px $right-color;\r\n    border-bottom:solid 1px $bottom-color\r\n}\r\n\r\n@mixin drop-shadow($x-axis: 0,$y-axis: 1px,$blur: 2px,$alpha: 0.1) {\r\n    //-webkit-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    //-moz-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin rounded($radius: 2px) {\r\n    border-radius:$radius\r\n}\r\n\r\n@mixin border-radius($topright: 0,$bottomright: 0,$bottomleft: 0,$topleft: 0) {\r\n    border-top-right-radius:$topright;\r\n    border-bottom-right-radius:$bottomright;\r\n    border-bottom-left-radius:$bottomleft;\r\n    border-top-left-radius:$topleft\r\n}\r\n\r\n@mixin opacity($opacity: 0.5) {\r\n    -moz-opacity:$opacity;\r\n    -khtml-opacity:$opacity;\r\n    -webkit-opacity:$opacity;\r\n    opacity:$opacity;\r\n    $opperc:$opacity * 100\r\n/*\r\n  -ms-filter: ~\"progid:DXImageTransform.Microsoft.Alpha(opacity=${opperc})\";\r\n  filter: ~\"alpha(opacity=${opperc})\";\r\n*/\r\n}\r\n\r\n@mixin transition-duration($duration: 0.2s) {\r\n    -moz-transition-duration:$duration;\r\n    -webkit-transition-duration:$duration;\r\n    -o-transition-duration:$duration;\r\n    transition-duration:$duration\r\n}\r\n\r\n@mixin transform($arguments) {\r\n    -webkit-transform:$arguments;\r\n    -moz-transform:$arguments;\r\n    -o-transform:$arguments;\r\n    -ms-transform:$arguments;\r\n    transform:$arguments\r\n}\r\n\r\n@mixin rotation($deg:5deg) {\r\n}\r\n\r\n@mixin scale($ratio:1.5) {\r\n}\r\n\r\n@mixin transition($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:all $duration $ease;\r\n    -moz-transition:all $duration $ease;\r\n    -o-transition:all $duration $ease;\r\n    transition:all $duration $ease\r\n}\r\n\r\n@mixin transition-color($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:color $duration $ease;\r\n    -moz-transition:color $duration $ease;\r\n    -o-transition:color $duration $ease;\r\n    transition:color $duration $ease\r\n}\r\n\r\n@mixin transition-border($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:border $duration $ease;\r\n    -moz-transition:border $duration $ease;\r\n    -o-transition:border $duration $ease;\r\n    transition:border $duration $ease\r\n}\r\n\r\n@mixin transition-background-color($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:background-color $duration $ease;\r\n    -moz-transition:background-color $duration $ease;\r\n    -o-transition:background-color $duration $ease;\r\n    transition:background-color $duration $ease\r\n}\r\n\r\n@mixin transition-fill($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:fill $duration $ease;\r\n    -moz-transition:fill $duration $ease;\r\n    -o-transition:fill $duration $ease;\r\n    transition:fill $duration $ease\r\n}\r\n\r\n@mixin inner-shadow($horizontal:0,$vertical:1px,$blur:2px,$alpha: 0.4) {\r\n    -webkit-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    -moz-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin box-shadow($arguments) {\r\n    //-webkit-box-shadow:$arguments;\r\n    //-moz-box-shadow:$arguments;\r\n    box-shadow:$arguments\r\n}\r\n\r\n@mixin box-sizing($sizing: border-box) {\r\n    //-ms-box-sizing:$sizing;\r\n    //-moz-box-sizing:$sizing;\r\n    //-webkit-box-sizing:$sizing;\r\n    box-sizing:$sizing\r\n}\r\n\r\n@mixin user-select($argument: none) {\r\n    -webkit-user-select:$argument;\r\n    -moz-user-select:$argument;\r\n    -ms-user-select:$argument;\r\n    user-select:$argument\r\n}\r\n\r\n@mixin columns($colwidth: 250px,$colcount: 0,$colgap: 50px,$columnRuleColor: #EEE,$columnRuleStyle: solid,$columnRuleWidth: 1px) {\r\n    -moz-column-width:$colwidth;\r\n    -moz-column-count:$colcount;\r\n    -moz-column-gap:$colgap;\r\n    -moz-column-rule-color:$columnRuleColor;\r\n    -moz-column-rule-style:$columnRuleStyle;\r\n    -moz-column-rule-width:$columnRuleWidth;\r\n    -webkit-column-width:$colwidth;\r\n    -webkit-column-count:$colcount;\r\n    -webkit-column-gap:$colgap;\r\n    -webkit-column-rule-color:$columnRuleColor;\r\n    -webkit-column-rule-style:$columnRuleStyle;\r\n    -webkit-column-rule-width:$columnRuleWidth;\r\n    column-width:$colwidth;\r\n    column-count:$colcount;\r\n    column-gap:$colgap;\r\n    column-rule-color:$columnRuleColor;\r\n    column-rule-style:$columnRuleStyle;\r\n    column-rule-width:$columnRuleWidth\r\n}\r\n\r\n@mixin translate($x:0,$y:0) {\r\n    -webkit-transform: translate($x,$y);\r\n    -moz-transform: translate($x,$y);\r\n    -ms-transform: translate($x,$y);\r\n    -o-transform: translate($x,$y);\r\n    transform: translate($x,$y);\r\n}\r\n\r\n@mixin translate3d($x:0,$y:0,$z:0) {\r\n  -webkit-transform: translate3d($x, $y, $z);\r\n      -ms-transform: translate3d($x, $y, $z); \r\n          transform: translate3d($x, $y, $z); \r\n}\r\n\r\n@mixin background-clip($argument: padding-box) {\r\n    -moz-background-clip:$argument;\r\n    -webkit-background-clip:$argument;\r\n    background-clip:$argument\r\n}\r\n\r\n@mixin transform($transforms) {\r\n     -moz-transform: $transforms;\r\n       -o-transform: $transforms;\r\n      -ms-transform: $transforms;\r\n  -webkit-transform: $transforms;\r\n          transform: $transforms;\r\n}\r\n// rotate\r\n@mixin rotate ($deg) {\r\n  @include transform(rotate(#{$deg}deg));\r\n}\r\n \r\n// scale\r\n@mixin scale($scale) {\r\n   @include transform(scale($scale));\r\n} \r\n// translate\r\n@mixin translate ($x, $y) {\r\n   @include transform(translate($x, $y));\r\n}\r\n// skew\r\n@mixin skew ($x, $y) {\r\n   @include transform(skew(#{$x}deg, #{$y}deg));\r\n}\r\n//transform origin\r\n@mixin transform-origin ($origin) {\r\n    -moz-transform-origin: $origin;\r\n       -o-transform-origin: $origin;\r\n      -ms-transform-origin: $origin;\r\n  -webkit-transform-origin: $origin;\r\n          transform-origin: $origin;\r\n}\r\n\r\n//return rgb value \r\n/*------------------------\r\n    Usage\r\n\r\n  $color-white: hexToRGBString(#fff) => \"255,255,255\"\r\n  $color-white: hexToRGBString(rgb(255,255,255)) => \"255,255,255\"\r\n  $color-white: hexToRGBString(rgba(#fff,1)) => \"255,255,255\"\r\n  \r\n------------------------*/\r\n@function hexToRGBString($hexColor) {\r\n  @return \"#{red($hexColor)},#{green($hexColor)},#{blue($hexColor)}\";\r\n}\r\n\r\n//Rem size support\r\n\r\n/*------------------------\r\n    Usage\r\n\r\n    h1 {\r\n      font-size: rem(32);\r\n    }\r\n\r\n    OR:\r\n\r\n    h1 {\r\n      font-size: rem(32px);\r\n    }\r\n------------------------*/\r\n\r\n$browser-context: 16;\r\n\r\n@function rem($pixels, $context: $browser-context) {\r\n  @if (unitless($pixels)) {\r\n    $pixels: $pixels * 1px;\r\n  }\r\n\r\n  @if (unitless($context)) {\r\n    $context: $context * 1px;\r\n  }\r\n\r\n  @return $pixels / $context * 1rem;\r\n}\r\n\r\n/*------------------------\r\n  FADE IN\r\n  e.g. @include fadeIn( 2s );\r\n------------------------*/\r\n\r\n//$prefix:'-moz-', '-webkit-', '-o-', '-ms-', '';\r\n//\r\n//@mixin keyframe-fadeIn {\r\n//  0%   { opacity:0; }\r\n//  100% { opacity:1; }\r\n//}\r\n//\r\n//@-moz-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-webkit-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-o-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-ms-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//\r\n//@mixin fadeIn( $arg ) {\r\n//  $keyframe-name:fadeIn;\r\n//  $duration:$arg;\r\n//  @each $p in $prefix {\r\n//    #{$p}animation:$keyframe-name $duration;\r\n//  }\r\n//}\r\n\r\n/*------------------------\r\nmixin that calculates if text needs to be light or dark\r\ndepending on the background color passed.\r\n\r\nFrom this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast\r\n\r\nusage:\r\n@include text-contrast($bgcolor)\r\n      \r\nColor brightness is determined by the following formula: \r\n((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000\r\n------------------------*/\r\n\r\n@mixin text-contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    color: rgba(255,255,255,1);\r\n  }\r\n\r\n  @else {\r\n    color: rgba(0,0,0,0.8);\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: contrast-ink($contrastvalue)\r\n------------------------*/\r\n\r\n@function contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    @return #ffffff;\r\n  }\r\n\r\n  @else {\r\n    @return #000000;\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: @include paint($blue-grey-50, bg-blue-grey-50);\r\n------------------------*/\r\n\r\n\r\n@mixin paint($paint:#333333,$make:bg-blue-grey-50) {\r\n\r\n    .#{$make} {\r\n      background-color: $paint;\r\n      @include text-contrast($paint)\r\n      &:hover {\r\n        @include text-contrast($paint)\r\n      }\r\n    }\r\n}\r\n\r\n@mixin brush($brush: #333,$make: red-50) {\r\n    .#{$make} {\r\n      color: $brush;\r\n    }\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin set-settings($class-element: nav-function-fixed) {\r\n\r\n    .#{$class-element} .btn-switch[data-class=\"#{$class-element}\"] {\r\n      @extend %set-settings;\r\n    }\r\n\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin paint-gradient($paint: $fusion-500, $make:bg-fusion-gradient) {\r\n\r\n    .#{$make} {\r\n      background-image: -webkit-linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n      background-image: linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n    }\r\n\r\n}\r\n\r\n/* backface visibility */\r\n@mixin backface-visibility($argument: none) {\r\n  -webkit-backface-visibility: hidden;\r\n  -moz-backface-visibility:    hidden;\r\n  -ms-backface-visibility:     hidden;\r\n   backface-visibility:        hidden;\r\n}\r\n\r\n/* generate theme button */\r\n@mixin theme-button-color ($theme-fusion:none, $theme-primary:none, $theme-info:none, $theme-success:none, $theme-warning:none, $theme-danger:none) {\r\n  background-image: -webkit-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -moz-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -ms-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: linear-gradient(to right, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n}\r\n\r\n// IE flexbox details:\r\n//\r\n// - Flexbox in IE 10:\r\n//   https://msdn.microsoft.com/en-us/library/hh673531(v=vs.85).aspx\r\n//\r\n// - IE 11 flexbox changes (includes property/value names for IE 10)\r\n//   https://msdn.microsoft.com/library/dn265027(v=vs.85).aspx\r\n\r\n@mixin flexbox ($important: false) {\r\n  display: unquote(\"-ms-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin inline-flexbox ($important: false) {\r\n  display: unquote(\"-ms-inline-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"inline-flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin align-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-line-pack: map-get($ms-map, $value) or $value;\r\n  align-content: $value;\r\n}\r\n\r\n@mixin align-items ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-align: map-get($ms-map, $value) or $value;\r\n  align-items: $value;\r\n}\r\n\r\n@mixin align-self ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-item-align: map-get($ms-map, $value) or $value;\r\n  align-self: $value;\r\n}\r\n\r\n@mixin flex ($value) {\r\n  -ms-flex: $value;\r\n  flex: $value;\r\n}\r\n\r\n@mixin flex-direction ($value) {\r\n  -ms-flex-direction: $value;\r\n  flex-direction: $value;\r\n}\r\n\r\n@mixin flex-wrap ($value) {\r\n  $ms-map: (\r\n    nowrap: none\r\n  );\r\n  -ms-flex-wrap: map-get($ms-map, $value) or $value;\r\n  flex-wrap: $value;\r\n}\r\n\r\n@mixin justify-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end,\r\n    space-around: distribute,\r\n    space-between: justify\r\n  );\r\n  -ms-flex-pack: map-get($ms-map, $value) or $value;\r\n  justify-content: $value;\r\n}\r\n\r\n@mixin order ($value) {\r\n  -ms-flex-order: $value;\r\n  order: $value;\r\n}", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* We will manually convert these primary colors to rgb for the dark mode option of the theme */\r\n$rgb-primary:\t\t\t\t\t\thexToRGBString($color-primary) !default;\r\n$rgb-success:\t\t\t\t\t\thexToRGBString($color-success) !default;\r\n$rgb-info:\t\t\t\t\t\t\thexToRGBString($color-info) !default;\r\n$rgb-warning:\t\t\t\t\t\thexToRGBString($color-warning) !default;\r\n$rgb-danger:\t\t\t\t\t\thexToRGBString($color-danger) !default;\r\n$rgb-fusion:\t\t\t\t\t\thexToRGBString($color-fusion) !default; \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"rgb-primary\":\t\t\t\t\t$rgb-primary,\r\n\t\"rgb-success\":\t\t\t\t\t$rgb-success,\r\n\t\"rgb-info\":\t\t\t\t\t\t$rgb-info,\r\n\t\"rgb-warning\":\t\t\t\t\t$rgb-warning,\r\n\t\"rgb-danger\":\t\t\t\t\t$rgb-danger,\r\n\t\"rgb-fusion\":\t\t\t\t\t$rgb-fusion,\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n//$mobile-font-size:\t\t\t\t\t\t15px; \t                                   /* bigger fontsize for mobile screens */\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n$custom-control-indicator-checked-disabled-bg:\t\t$primary-100;\r\n\r\n$custom-control-indicator-active-bg:\t\t\t\t$primary-100; \r\n$custom-control-indicator-active-border-color:\t\t$primary-100;\r\n$custom-control-indicator-active-color:\t\t\t\t$primary-100;\r\n\r\n$custom-control-indicator-focus-border-color:\t\t$primary-400;\r\n$custom-select-focus-border-color:\t\t\t\t\t$primary-500;\r\n\r\n$custom-checkbox-indicator-indeterminate-border-color: $primary-500;\r\n$custom-checkbox-indicator-indeterminate-bg: $primary-500;\r\n\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n/* custom file */\r\n$custom-file-focus-border-color:\t\t$primary-500;\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n//$modal-lg:\t\t\t\t\t\t\t\t650px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\t28px !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\t28px !default; \t\t\t\t\t\t\t\t\t\t  /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t  /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t  /* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav footer */\r\n$nav-footer-link-color:\t\t\t\t\tlighten($nav-background, 25%) !default;\r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n\r\n/* nav link level-1 */\r\n$nav-link-color-child: \t\t\t\t\tdarken($nav-link-color, 5%);\r\n$nav-link-color-child-hover:\t\t\t$white;\r\n\r\n/* nav level-1 bg */\r\n$nav-ul-ul-bg:\t\t\t\t\t\t\trgba($black,0.1);\r\n$nav-ul-padding-top:\t\t\t\t\t10px;\r\n$nav-ul-padding-bottom:\t\t\t\t\t10px;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\tvar(--theme-warning-900) !default;\r\n$settings-incompat-desc:\t\t\t\tvar(--theme-warning-900) !default;\r\n$settings-incompat-bg:\t\t\t\t\tvar(--theme-warning-50) !default;\r\n$settings-incompat-border:\t\t\t\tvar(--theme-warning-700) !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n$panel-btn-icon-width:\t\t\t\t\t2rem;\r\n$panel-btn-icon-height:\t\t\t\t\t2rem;\r\n$panel-btn-icon-font-size:\t\t\t\t1rem;\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;\r\n\r\n/* SHORTCUT BUTTON (appears on bottom right of the page) */\r\n$app-shortcut-btn-size: 49px;\r\n$menu-item-size: 45px;\r\n$menu-items:5;\r\n$menu-grid-icon: 5px;\r\n$menu-item-direction: 'top'; //top or left\r\n\r\n\r\n/* GULP WARNINGS */\r\n$ignore-warning: true;", "/* PLACEHOLDER \r\n============================================= \r\n\r\nEXAMPLE:\r\n\r\n%bg-image {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n.image-one {\r\n\t\t@extend %bg-image;\r\n\t\tbackground-image:url(/img/image-one.jpg\");\r\n}\r\n\r\nRESULT:\r\n\r\n.image-one, .image-two {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n*/\r\n\r\n%nav-bg {\r\n\tbackground-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\tbackground-color: $nav-background;\r\n}\r\n\r\n/*\r\n%shadow-hover {\r\n\tbox-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);\r\n\ttransition: all 0.2s ease-in-out;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);\r\n\t}\r\n}\r\n*/\r\n%btn-default {\r\n\t@include gradient-img($start: #f5f5f5,$stop: #f1f1f1);\r\n\tcolor: #444;\r\n\tborder: 1px solid rgba(0,0,0,0.1);\r\n\tbox-shadow: none;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder: 1px solid #c6c6c6;\r\n\t\tcolor: #333;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t&:focus {\r\n\t\tborder-color: $primary-200 !important;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground: $primary-300;\r\n\t\tcolor: $white;\r\n\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;\r\n\t}\r\n}\r\n\r\n%custom-scroll {\r\n\r\n\t&::-webkit-scrollbar-track-piece {\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\r\n &::-webkit-scrollbar-thumb:vertical {\r\n\t\tbackground-color: #666;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar {\r\n\t\theight: 4px;\r\n\t\twidth: 4px;\r\n\t}\r\n\r\n &::-webkit-scrollbar-corner {\r\n\t\twidth: 40px;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar-thumb:vertical {\r\n\tbackground-color: #666;\r\n\t}\r\n\r\n\toverflow: hidden;\r\n\toverflow-y: scroll;\r\n\t-webkit-overflow-scrolling: touch;\r\n\r\n}\r\n\r\n%user-select {\r\n\t\t-webkit-user-select: none; \r\n\t\t\t -moz-user-select: none; \r\n\t\t\t\t-ms-user-select: none;\r\n}\r\n\r\n%content-box {\r\n\tbox-sizing: content-box;\r\n}\r\n\r\n%flex-0-0-auto {\r\n\tflex: 0 0 auto;\r\n}\r\n\r\n%transform-3d {\r\n\t@include translate3d(0,0,0);\r\n}\r\n\r\n\r\n%stop-transform-3d {\r\n\t\t\t\t\t\ttransform: none;\r\n\t\t-webkit-transform: none;\r\n\t\t\t\t-ms-transform: none;\r\n}\r\n\r\n%general-animation {\r\n\ttransition: $nav-hide-animate;      \r\n}\r\n\r\n%common-animation-slow {\r\n\t@include transition(0.3s,ease-in-out);\r\n\r\n}\r\n\r\n%common-animation {\r\n\t@include transition(0.2s,ease-in-out);\r\n}\r\n\r\n%common-animation-easeout {\r\n\t@include transition(0.4s,ease-out);\r\n}\r\n\r\n%common-animation-opacity {\r\n\ttransition: opacity 0.5s ease-in-out;\r\n}\r\n\r\n%common-animation-opacity-faster {\r\n\ttransition: opacity 0.1s ease-in-out;\r\n}\r\n\r\n%stop-animation {\r\n\ttransition: none;\r\n}\r\n\r\n%font-smoothing {\r\n\t\t -webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n%set-settings {\r\n\tcolor:$white;\r\n\tbackground:$color-primary !important;\r\n\t&:before {\r\n\t\tcontent:\"ON\" !important;\r\n\t\tleft:7px !important;\r\n\t\tright:auto !important;\r\n\t}\r\n\t&:after {\r\n\t\tcontent: \" \" !important;\r\n\t\tright:0 !important;\r\n\t\tleft:auto !important;\r\n\t\tbackground:$white !important;\r\n\t\tcolor:$color-primary !important;\r\n\t}\r\n\r\n\t+ .onoffswitch-title {\r\n\t\tfont-weight:500;\r\n\t\tcolor: $primary-500;\r\n\t}\r\n}\r\n\r\n%bg-img-cover {\r\n\tbackground-size: cover;\r\n}\r\n\r\n%not-compatible {\r\n\t\tposition:relative;\r\n\t\t\r\n\t\t.onoffswitch-title {\r\n\t\t\tcolor: #da9400;\r\n\t\t\tcolor: $settings-incompat-title !important;\r\n\t\t}\r\n\t\t.onoffswitch-title-desc {\r\n\t\t\tcolor: #da9400;\r\n\t\t\tcolor: $settings-incompat-desc !important;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \"DISABLED\";\r\n\t\t\t@extend %incompatible;\r\n\t\t}\r\n}\r\n\r\n%not-compatible-override {\r\n\t\t&:before {\r\n\t\t\tdisplay:none !important;\r\n\t\t}\r\n}\r\n\r\n%ping-badge {\r\n\tposition: absolute;\r\n\tdisplay: block;\r\n\tborder-radius: 1rem;\r\n\tbackground-color: $nav-badge-bg-color;\r\n\tcolor: $nav-badge-color;\r\n\ttext-align: center;\r\n\tcursor: pointer;\r\n\t@include box-shadow(0 0 0 1px $nav-background);\r\n\tborder: 1px solid $nav-background;\r\n\tmin-width: 2rem;\r\n\tmax-width: 1.5rem;\r\n\tpadding: 2px;\r\n\tfont-weight: 500;\r\n\tline-height: normal;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n}\r\n\r\n\r\n\r\n/*%fixed-header-shadow {\r\n\t@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));\r\n}*/\r\n\r\n%header-btn {\r\n\t//@extend %btn-default;\r\n\t@include rounded($header-btn-border-radius);\r\n\tborder: 1px solid lighten($fusion-50, 30%);\r\n\theight: $header-btn-height;\r\n\twidth: $header-btn-width;\r\n\tvertical-align: middle;\r\n\tline-height: $header-btn-height - 0.125rem;\r\n\tmargin-right: $grid-gutter-width-base/4 + 0.1875rem;\r\n\tfont-size: $header-btn-font-size;\r\n\tpadding: $list-table-padding-y $list-table-padding-x;\r\n\tcursor: default;\r\n\tcolor:$header-btn-color;\r\n\tposition: relative;\r\n\t\t//background: $primary-200;\r\n\t\t//color:$primary-200;\r\n/*\r\n\t&.active {\r\n\t\t@extend %header-btn-active;\r\n\t}*/\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder-color: $primary-500;\r\n\t\tbackground: $primary-300;\r\n\t\tcolor:$white;\r\n\r\n\t}\r\n\r\n}\r\n\r\n%expanded-box {\r\n\tbox-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.125);\r\n\tborder-bottom: 1px solid rgba(0,0,0,0.06);\r\n\tborder-width: 0 0 1px 0;\r\n\tbackground: $white;\r\n\tpadding: 16px 16px 10px;\r\n}\r\n\r\n%header-btn-active {\r\n\tbackground: $header-btn-active-bg;\r\n\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t@include box-shadow(inset 0 0 3px 1px rgba(0,0,0,.37));\r\n\tcolor:$header-btn-active-color !important;\r\n}\r\n\r\n//@include media-breakpoint-up($mobile-breakpoint) {\r\n/*  %selected-dot {\r\n\t\t&:before {\r\n\t\t\tcontent: \" \";\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: inherit;\r\n\t\t\tbackground-image: none;\r\n\t\t\tborder: 2px solid rgba(0,0,0,0.2);\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 15px;\r\n\t\t\tleft: 15px;\r\n\t\t\theight: 20px;\r\n\t\t\twidth: 20px;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\theight: inherit;\r\n\t\t\twidth: inherit;\r\n\t\t\tborder: 5px solid rgba(0,0,0,0.1);\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tborder-radius: 50%;\r\n\t\t} \r\n\t}*/\r\n//}\r\n\r\n%spin-loader {\r\n\tmargin: 5px;\r\n\theight: 20px;\r\n\twidth: 20px;\r\n\tanimation: spin 0.5s infinite linear;\r\n\tborder: 2px solid $color-primary;\r\n\tborder-right-color: transparent;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n%incompatible {\r\n\tdisplay: block;\r\n\tposition: absolute;\r\n\tbackground: $warning-500;\r\n\tbackground: $settings-incompat-bg;\r\n\tfont-size: 10px;\r\n\twidth: 65px;\r\n\ttext-align: center;\r\n\tborder: 1px solid $settings-incompat-border;\r\n\theight: 22px;\r\n\tline-height: 20px;\r\n\tborder-radius: $border-radius-plus;\r\n\tright: 13px;\r\n\ttop: 26%;\r\n\tcolor:$fusion-900;\r\n}\r\n\r\n/* patterns */\r\n%pattern-0 {\r\n\tbackground-size: 10px 10px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .07) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .07) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 10px 10px transparent;\r\n}\r\n\r\n%pattern-1 {\r\n\tbackground-size: 5px 5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 5px 5px transparent;\r\n}\r\n\r\n%pattern-2 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px transparent;\r\n}\r\n\r\n%pattern-3 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px 15px transparent;\r\n}\r\n\r\n%pattern-4 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-position: 0 0, 18.5px 18.5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 37px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 37px 37px / 74px,\r\n\t\t\t\t\t\t\t\t\t transparent;\r\n}\r\n\r\n%pattern-5 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t #eee;\r\n}\r\n\r\n%pattern-6 {\r\n\tbackground-size: 50px 50px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 50px 50px transparent;\r\n}\r\n\r\n"]}