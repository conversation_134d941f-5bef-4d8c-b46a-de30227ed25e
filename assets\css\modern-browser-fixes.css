/**
 * Modern Browser Fixes
 * แก้ไขปัญหา CSS deprecation warnings และ modern browser compatibility
 */

/* 
 * แก้ไข -ms-high-contrast deprecation
 * ใช้ forced-colors แทน -ms-high-contrast สำหรับ modern browsers
 */

/* High Contrast Mode - Modern Approach */
@media (forced-colors: active) {
  .text-gradient {
    background: transparent;
    color: ButtonText;
  }
  
  .nav-function-minify:not(.nav-function-top) .primary-nav .nav-menu > li > a + ul:before {
    left: -0.25rem !important;
  }
  
  .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
    top: -1.5rem;
  }
  
  .ie-only {
    display: inline-block !important;
  }
  
  .ie-d-none {
    display: none !important;
  }
  
  /* Table hover in high contrast */
  .table-hover tbody tr:hover {
    box-shadow: none;
    background-color: Highlight;
    color: HighlightText;
  }
  
  /* High contrast navigation */
  .mod-high-contrast:not(.mod-skin-dark) .nav-menu li a,
  .mod-high-contrast:not(.mod-skin-dark) .nav-title,
  .mod-high-contrast:not(.mod-skin-dark) .nav-menu li a [class*='fa-'],
  .mod-high-contrast:not(.mod-skin-dark) .nav-menu li a .ni,
  .mod-high-contrast:not(.mod-skin-dark) .dl-ref,
  .mod-high-contrast:not(.mod-skin-dark) .btn {
    text-shadow: none;
    color: ButtonText !important;
    font-weight: 500 !important;
    border: 1px solid ButtonText;
  }
  
  .mod-high-contrast:not(.mod-skin-dark) .subheader-title,
  .mod-high-contrast:not(.mod-skin-dark) h1,
  .mod-high-contrast:not(.mod-skin-dark) h2,
  .mod-high-contrast:not(.mod-skin-dark) h3,
  .mod-high-contrast:not(.mod-skin-dark) h4,
  .mod-high-contrast:not(.mod-skin-dark) h5,
  .mod-high-contrast:not(.mod-skin-dark) .settings-panel-title a,
  .mod-high-contrast:not(.mod-skin-dark) .panel-header,
  .mod-high-contrast:not(.mod-skin-dark) .badge-detached,
  .mod-high-contrast:not(.mod-skin-dark) .btn-secondary,
  .mod-high-contrast:not(.mod-skin-dark) .btn-default,
  .mod-high-contrast:not(.mod-skin-dark) .page-header .btn,
  .mod-high-contrast:not(.mod-skin-dark) [class*="btn-outline-"] {
    text-shadow: none;
    color: ButtonText !important;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  /* Select2 fixes */
  .select2-container--default .select2-selection--single .select2-selection__clear {
    line-height: normal;
    margin-top: 8px;
    color: ButtonText;
  }
  
  /* SweetAlert2 fixes */
  .swal2-range input {
    width: 100% !important;
  }
  
  .swal2-range output {
    display: none;
  }
}

/* 
 * Fallback สำหรับ browsers ที่ยังไม่รองรับ forced-colors
 * แต่ยังคงใช้ -ms-high-contrast (IE, Edge Legacy)
 */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  [contenteditable="true"] {
    height: 110px;
  }
  
  .text-gradient {
    background: transparent;
  }
  
  .nav-function-minify:not(.nav-function-top) .primary-nav .nav-menu > li > a + ul:before {
    left: -0.25rem !important;
  }
  
  .nav-function-top .page-sidebar .primary-nav .nav-menu > li > ul:before {
    top: -1.5rem;
  }
  
  .ie-only {
    display: inline-block !important;
  }
  
  .ie-d-none {
    display: none !important;
  }
  
  .table-hover tbody tr:hover {
    box-shadow: none;
    background-color: #fffaee;
  }
  
  .select2-container--default .select2-selection--single .select2-selection__clear {
    line-height: normal;
    margin-top: 8px;
  }
  
  .swal2-range input {
    width: 100% !important;
  }
  
  .swal2-range output {
    display: none;
  }
}

/* 
 * CSS Grid และ Flexbox fallbacks สำหรับ older browsers
 */
@supports not (display: grid) {
  .grid-container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .grid-item {
    flex: 1 1 auto;
  }
}

/* 
 * Modern CSS Custom Properties fallbacks
 */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

/* Fallback สำหรับ browsers ที่ไม่รองรับ CSS Custom Properties */
.btn-primary {
  background-color: #007bff;
  background-color: var(--primary-color, #007bff);
}

.btn-secondary {
  background-color: #6c757d;
  background-color: var(--secondary-color, #6c757d);
}

.btn-success {
  background-color: #28a745;
  background-color: var(--success-color, #28a745);
}

.btn-danger {
  background-color: #dc3545;
  background-color: var(--danger-color, #dc3545);
}

.btn-warning {
  background-color: #ffc107;
  background-color: var(--warning-color, #ffc107);
}

.btn-info {
  background-color: #17a2b8;
  background-color: var(--info-color, #17a2b8);
}

/* 
 * Accessibility improvements
 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus indicators สำหรับ keyboard navigation */
.btn:focus,
.form-control:focus,
.form-select:focus,
a:focus {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

@media (forced-colors: active) {
  .btn:focus,
  .form-control:focus,
  .form-select:focus,
  a:focus {
    outline: 2px solid Highlight;
  }
}

/* 
 * Print styles
 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .btn {
    border: 1px solid #000 !important;
  }
  
  .table {
    border-collapse: collapse !important;
  }
  
  .table td,
  .table th {
    border: 1px solid #000 !important;
  }
}

/* 
 * Dark mode support
 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
  }
  
  .auto-dark-mode {
    background-color: var(--bg-color);
    color: var(--text-color);
  }
  
  .auto-dark-mode .card {
    background-color: #2d2d2d;
    border-color: var(--border-color);
  }
  
  .auto-dark-mode .table {
    color: var(--text-color);
  }
  
  .auto-dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
  }
}
