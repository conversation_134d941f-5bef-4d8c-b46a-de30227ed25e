// ===================
// ตัวแปรสถานะและข้อมูล
let currentItems = [];
let dragData = null;
let currentPoolID = null;
let poolNames = {};
let currentEditSerialNum = null; // เก็บ SerialNum ของไอเท็มที่เปิดแก้ไข
let currentEditPoolID = null;
let currentEditTabID = null;
let currentEditSlotID = null;
// ===================
// ฟังก์ชันแสดงข้อความสถานะ
function showStatusMessage(message, type = 'info') {
    // สร้าง element สำหรับแสดงข้อความ
    const statusDiv = document.createElement('div');
    statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        max-width: 400px;
        word-wrap: break-word;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: opacity 0.3s ease;
    `;

    // กำหนดสีตามประเภทข้อความ
    switch(type) {
        case 'success':
            statusDiv.style.backgroundColor = '#28a745';
            break;
        case 'error':
            statusDiv.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            statusDiv.style.backgroundColor = '#ffc107';
            statusDiv.style.color = '#212529';
            break;
        default:
            statusDiv.style.backgroundColor = '#17a2b8';
    }

    statusDiv.textContent = message;
    document.body.appendChild(statusDiv);

    // ลบข้อความหลังจาก 3 วินาที
    setTimeout(() => {
        statusDiv.style.opacity = '0';
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.parentNode.removeChild(statusDiv);
            }
        }, 300);
    }, 3000);
}

// ===================
// ฟังก์ชันดึงสีจากรหัส
function getColorFromCode(code) {
    const colorMap = {
        0: '#ff4d6d', 1: '#d2b48c', 2: '#40e0d0', 3: '#006400', 4: '#a0522d',
        5: '#9370db', 6: '#90ee90', 7: '#800080', 8: '#800080', 9: '#ff69b4',
        10: '#1e90ff', 11: '#98fb98', 21: '#2f4f4f', 22: '#d3d3d3', 23: '#ffa500',
        31: '#f5f5f5', 32: '#008000', 33: '#ff8c00', 34: '#FFFF00', 35: '#4169E1',
        36: '#1E90FF', 37: '#00FFFF', 38: '#0099CC', 39: '#00CCFF', 40: '#FFD700',
        41: '#FF6347', 42: '#deb887', 47: '#ff4500', 48: '#dc143c', 49: '#ffffff',
        50: '#ffc0cb', 51: '#ffb6c1', 52: '#ff00ff', 53: '#ff00ff', 54: '#ff1493',
        55: '#ff1493', 56: '#ff69b4', 57: '#ff69b4', 65592: '#800080'
    };
    return colorMap[code] || '#ffffff';
}

// ===================
// โหลดข้อมูลเมื่อ DOM พร้อม
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing cash shop...');
    loadPoolNames();
    loadItems();
    setupFormSubmit();
    setupDeleteButton();

    // เพิ่มการตรวจสอบ
    setTimeout(() => {
        console.log('Checking slots after 2 seconds...');
        const slots = document.querySelectorAll('.slot');
        console.log('Total slots found:', slots.length);
        slots.forEach((slot, i) => {
            if (i < 5) { // แสดงแค่ 5 slot แรก
                console.log(`Slot ${i}:`, {
                    serial: slot.dataset.serial,
                    pool: slot.dataset.pool,
                    slotId: slot.dataset.slot,
                    draggable: slot.draggable
                });
            }
        });
    }, 2000);
});

// ===================
// โหลดชื่อ Pool
async function loadPoolNames() {
    try {
        const res = await fetch('files/game_systems/class_module/cashshop_manager.php?action=getPoolNames');
        const json = await res.json();

        console.log('[loadPoolNames] Response JSON:', json); // ✅ แสดงข้อมูลที่ fetch มา

        if (json.success && Array.isArray(json.pools)) {
            json.pools.forEach(p => {
                poolNames[p.PoolID] = {
                    name: p.Msg || '',
                    color: null,
                    marker: p.Marker ?? 0
                };
            });

            console.log('[loadPoolNames] Parsed poolNames:', poolNames); // ✅ ตรวจสอบข้อมูลหลัง parse
        } else {
            console.warn('[loadPoolNames] Invalid response format or data missing');
        }
    } catch (err) {
        console.warn('[loadPoolNames] Failed:', err);
    }
}

// ===================
// โหลดรายการไอเท็มทั้งหมด
async function loadItems(specificPoolID = null) {
    const container = document.getElementById('tabContentArea');
    if (!container) return;

    // ถ้าระบุ pool เฉพาะ ให้แสดง loading เฉพาะ tab นั้น
    if (specificPoolID) {
        const specificTab = document.getElementById(`pool-${specificPoolID}`);
        if (specificTab) {
            specificTab.innerHTML = '<p style="text-align:center;padding:20px;">Updating...</p>';
        }
    } else {
        container.innerHTML = '<p>Loading...</p>';
    }

    try {
        const res = await fetch('files/game_systems/class_module/cashshop_manager.php?action=getAllItems');
        const data = await res.json();

        if (specificPoolID) {
            // อัปเดตเฉพาะ pool ที่ระบุ
            renderSpecificPool(data, specificPoolID);
        } else {
            // อัปเดตทั้งหมด
            renderTabsByPool(data);
        }
    } catch (err) {
        console.error('[loadItems] Failed:', err);
        if (specificPoolID) {
            const specificTab = document.getElementById(`pool-${specificPoolID}`);
            if (specificTab) {
                specificTab.innerHTML = '<p class="text-danger">Failed to update items.</p>';
            }
        } else {
            container.innerHTML = '<p class="text-danger">Failed to load items.</p>';
        }
    }
}

// ===================
// สร้าง UI Tab และ Grid ของไอเท็ม
function renderTabsByPool(items) {
    currentItems = items;
    const shopTabs = document.getElementById('shopTabs');
    const tabContent = document.getElementById('tabContentArea');

    if (!shopTabs || !tabContent) return;

    const poolMap = {};
    items.forEach(item => {
        if (!poolMap[item.PoolID]) poolMap[item.PoolID] = new Array(128).fill(null);
        poolMap[item.PoolID][item.SlotID] = item;
    });

    const poolIDs = Object.keys(poolMap);
    if (!poolIDs.includes(currentPoolID)) currentPoolID = poolIDs[0] || null;

    shopTabs.innerHTML = '';
    tabContent.innerHTML = '';

    poolIDs.forEach((poolID) => {
        const tabId = `pool-${poolID}`;

        // ✅ ใช้ชื่อจาก poolNames ถ้ามี
        const rawName = poolNames[poolID]?.name || `Pool ${poolID}`;

        // ✅ แปลง UCS-2 / null-byte string ให้เป็นปกติ
        const cleanedName = rawName.replace(/\x00/g, '');

        // ✅ ดึง code สี
        const colorMatch = cleanedName.match(/^\$(\d{1,3})#/);
        const colorCode = colorMatch ? parseInt(colorMatch[1]) : null;
        const nameColor = getColorFromCode(colorCode);

        // ✅ ตัด code สีออกจากชื่อ
        const displayName = cleanedName.replace(/^\$\d{1,3}#/, '').trim();

        const tabLabel = `<span style="font-size:12px; font-weight: bold; color: ${nameColor}">${displayName}</span>
            <sup>
                <span class="badge badge-warning ml-2">
                    NEW
                </span>
            </sup>
        `;

        shopTabs.innerHTML += `
            <li class="nav-item">
                <a class="nav-link ${poolID === currentPoolID ? 'active' : ''}" data-toggle="tab" href="#${tabId}">
                    ${tabLabel}
                </a>
            </li>`;

        const slots = poolMap[poolID].map((item, slotID) => {
            const hasItem = item && item.SerialNum;
            const dataAttr = hasItem
                ? `data-serial="${item.SerialNum}" data-pool="${poolID}" data-slot="${slotID}"`
                : `data-pool="${poolID}" data-slot="${slotID}"`;

            let draggableAttr = hasItem ? 'draggable="true"' : 'draggable="false"';
            let cursorStyle = hasItem ? 'cursor:grab;' : 'cursor:default;';
            let slotBg = hasItem ? 'background:#222;' : 'background:#2e2e2e;';

            // สร้างรูปไอเท็ม
            let itemImageHtml = '';
            if (hasItem && item.ItemKind) {
                itemImageHtml = `
                    <img src="assets/images/items/${item.ItemKind}.png"
                         alt="${item.ItemName}"
                         style="width:250%;height:250%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);object-fit:cover;object-position:center;pointer-events:none;z-index:1;border-radius:4px;opacity:0;transition:opacity 0.3s ease;"
                         onerror="this.onerror=null; this.src='assets/images/items/default.png';"
                         onload="this.style.opacity='1';">
                `;
            }

            return `
                <div class="card p-2 text-center slot" style="width:93px;height:93px;margin:2px;border:1px solid #555;border-radius:8px;${cursorStyle}position:relative;overflow:hidden;${slotBg}" ${draggableAttr} ${dataAttr}>
                    ${itemImageHtml}
                    <strong style="position:absolute;top:2px;left:4px;font-size:11px;color:#bbb;z-index:5;text-shadow:1px 1px 2px #000;">${slotID}</strong>
                    ${hasItem ? `
                        <div style="position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent, rgba(0,0,0,0.8));z-index:4;padding:2px;">
                            <span class="small itemname" style="color:#fff;font-size:9px;font-weight:bold;display:block;text-align:center;text-shadow:1px 1px 2px #000;">${item.ItemName}</span>
                        </div>
                        <div style="position:absolute;right:2px;top:2px;z-index:5;display:flex;flex-direction:column;align-items:flex-end;gap:1px;">
                            <span class="badge badge-primary" style="font-size:8px;display:block;padding:1px 2px;background:rgba(0,123,255,0.8);">💰${item.Cash}</span>
                            <span class="badge badge-danger" style="font-size:8px;display:block;padding:1px 2px;background:rgba(220,53,69,0.8);">💎${item.Forcegem}</span>
                        </div>
                    ` : `<span class="small text-muted" style="background:rgba(0,0,0,0.5);color:#999;display:block;position:absolute;top:35px;left:0;right:0;z-index:3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">Empty</span>`}
                </div>`;
        }).join('');

        tabContent.innerHTML += `
            <div class="tab-pane fade ${poolID === currentPoolID ? 'show active' : ''}" id="${tabId}">
                <div class="d-flex flex-wrap gap-1">${slots}</div>
            </div>`;
    });

    const activeTab = document.querySelector(`a[href="#pool-${currentPoolID}"]`);
    if (activeTab) new bootstrap.Tab(activeTab).show();

    setupSlotListeners();
}

// ===================
// อัปเดตเฉพาะ pool ที่ระบุ
function renderSpecificPool(items, poolID) {
    console.log(`[renderSpecificPool] Updating pool ${poolID}`);

    currentItems = items;
    const poolMap = {};
    items.forEach(item => {
        if (!poolMap[item.PoolID]) poolMap[item.PoolID] = new Array(128).fill(null);
        poolMap[item.PoolID][item.SlotID] = item;
    });

    // ตรวจสอบว่า pool ที่ระบุมีอยู่หรือไม่
    if (!poolMap[poolID]) {
        console.warn(`[renderSpecificPool] Pool ${poolID} not found`);
        return;
    }

    const tabContent = document.getElementById(`pool-${poolID}`);
    if (!tabContent) {
        console.warn(`[renderSpecificPool] Tab content for pool ${poolID} not found`);
        return;
    }

    // สร้าง slots สำหรับ pool นี้
    const slots = poolMap[poolID].map((item, slotID) => {
        const hasItem = item && item.SerialNum;
        const dataAttr = hasItem
            ? `data-serial="${item.SerialNum}" data-pool="${poolID}" data-slot="${slotID}"`
            : `data-pool="${poolID}" data-slot="${slotID}"`;

        let draggableAttr = hasItem ? 'draggable="true"' : 'draggable="false"';
        let cursorStyle = hasItem ? 'cursor:grab;' : 'cursor:default;';
        let slotBg = hasItem ? 'background:#222;' : 'background:#2e2e2e;';

        // สร้างรูปไอเท็ม
        let itemImageHtml = '';
        if (hasItem && item.ItemKind) {
            itemImageHtml = `
                <img src="assets/images/items/${item.ItemKind}.png"
                     alt="${item.ItemName}"
                     style="width:250%;height:250%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);object-fit:cover;object-position:center;pointer-events:none;z-index:1;border-radius:4px;opacity:0;transition:opacity 0.3s ease;"
                     onerror="this.onerror=null; this.src='assets/images/items/default.png';"
                     onload="this.style.opacity='1';">
            `;
        }

        return `
            <div class="card p-2 text-center slot" style="width:93px;height:93px;margin:2px;border:1px solid #555;border-radius:8px;${cursorStyle}position:relative;overflow:hidden;${slotBg}" ${draggableAttr} ${dataAttr}>
                ${itemImageHtml}
                <strong style="position:absolute;top:2px;left:4px;font-size:11px;color:#bbb;z-index:5;text-shadow:1px 1px 2px #000;">${slotID}</strong>
                ${hasItem ? `
                    <div style="position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent, rgba(0,0,0,0.8));z-index:4;padding:2px;">
                        <span class="small itemname" style="color:#fff;font-size:9px;font-weight:bold;display:block;text-align:center;text-shadow:1px 1px 2px #000;">${item.ItemName}</span>
                    </div>
                    <div style="position:absolute;right:2px;top:2px;z-index:5;display:flex;flex-direction:column;align-items:flex-end;gap:1px;">
                        <span class="badge badge-primary" style="font-size:8px;display:block;padding:1px 2px;background:rgba(0,123,255,0.8);">💰${item.Cash}</span>
                        <span class="badge badge-danger" style="font-size:8px;display:block;padding:1px 2px;background:rgba(220,53,69,0.8);">💎${item.Forcegem}</span>
                    </div>
                ` : `<span class="small text-muted" style="background:rgba(0,0,0,0.5);color:#999;display:block;position:absolute;top:35px;left:0;right:0;z-index:3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">Empty</span>`}
            </div>`;
    }).join('');

    // อัปเดต content ของ tab
    tabContent.innerHTML = `<div class="d-flex flex-wrap gap-1">${slots}</div>`;

    // ตั้งค่า event listeners ใหม่
    setupSlotListeners();

    console.log(`[renderSpecificPool] Pool ${poolID} updated successfully`);
}

// ===================
// แสดง tab ที่ระบุเป็น active
function showActiveTab(poolID) {
    console.log(`[showActiveTab] Switching to pool ${poolID}`);

    // อัปเดต currentPoolID
    currentPoolID = poolID;

    // ลบ active class จาก tab ทั้งหมด
    document.querySelectorAll('.nav-link').forEach(tab => {
        tab.classList.remove('active');
    });

    // ลบ active class จาก tab content ทั้งหมด
    document.querySelectorAll('.tab-pane').forEach(content => {
        content.classList.remove('show', 'active');
    });

    // เพิ่ม active class ให้ tab ที่ระบุ
    const targetTab = document.querySelector(`a[href="#pool-${poolID}"]`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // เพิ่ม active class ให้ tab content ที่ระบุ
    const targetContent = document.getElementById(`pool-${poolID}`);
    if (targetContent) {
        targetContent.classList.add('show', 'active');
    }

    // ใช้ Bootstrap tab API ถ้ามี
    if (targetTab && typeof bootstrap !== 'undefined' && bootstrap.Tab) {
        try {
            const tab = new bootstrap.Tab(targetTab);
            tab.show();
        } catch (e) {
            console.warn('[showActiveTab] Bootstrap Tab API failed:', e);
        }
    }

    console.log(`[showActiveTab] Switched to pool ${poolID} successfully`);
}




// ===================
// ตั้ง event ให้ slot ทั้งหมด (drag, drop, click)
function setupSlotListeners() {
    setTimeout(() => {
        console.log('Setting up slot listeners...');
        const slots = document.querySelectorAll('.slot');
        console.log('Found slots:', slots.length);

        slots.forEach((slot, index) => {
            console.log(`Setting up slot ${index}:`, {
                serial: slot.dataset.serial,
                pool: slot.dataset.pool,
                slotId: slot.dataset.slot,
                draggable: slot.draggable
            });

            // ลบ event listeners เก่าก่อน (ถ้ามี)
            slot.removeEventListener('dragstart', handleDragStart);
            slot.removeEventListener('dragend', handleDragEnd);
            slot.removeEventListener('dragover', handleDragOver);
            slot.removeEventListener('dragleave', handleDragLeave);
            slot.removeEventListener('drop', handleDrop);
            slot.removeEventListener('click', handleClick);

            // เพิ่ม event listeners ใหม่
            slot.addEventListener('dragstart', handleDragStart);
            slot.addEventListener('dragend', handleDragEnd);
            slot.addEventListener('dragover', handleDragOver);
            slot.addEventListener('dragleave', handleDragLeave);
            slot.addEventListener('drop', handleDrop);
            slot.addEventListener('click', handleClick);
        });
    }, 100);
}

// แยก event handlers ออกมาเป็นฟังก์ชันแยก
function handleDragStart(e) {
    const slot = e.currentTarget;
    console.log('handleDragStart:', slot.dataset);

    // ป้องกันการลาก slot ว่าง
    if (!slot.dataset.serial) {
        console.log('Preventing drag of empty slot');
        e.preventDefault();
        return false;
    }

    // เพิ่ม class สำหรับ visual feedback
    slot.classList.add('dragging');
    dragStart(e);
}

function handleDragEnd(e) {
    console.log('handleDragEnd');
    // รีเซ็ต visual feedback ของทุก slot เมื่อ drag จบ
    document.querySelectorAll('.slot').forEach(s => {
        s.classList.remove('dragging', 'drag-over');
    });
}

function handleDragOver(e) {
    dragOver(e);
}

function handleDragLeave(e) {
    dragLeave(e);
}

function handleDrop(e) {
    console.log('handleDrop');
    // รีเซ็ต visual feedback เมื่อ drop
    const target = e.currentTarget;
    target.classList.remove('drag-over');
    dropItem(e);
}

function handleClick(e) {
    const slot = e.currentTarget;
    const serial = slot.dataset.serial;
    const pool = slot.dataset.pool;
    const slotID = slot.dataset.slot;

    console.log('handleClick:', { serial, pool, slotID });

    currentPoolID = pool;

    if (serial) {
        const item = currentItems.find(i => i.SerialNum === serial);
        if (item) editItem(item);
    } else {
        openAddModalWithSlot(slotID, pool);
    }
}

// ===================
// Drag & Drop ฟังก์ชัน
function dragStart(e) {
    const slot = e.currentTarget;

    console.log('dragStart called:', {
        serial: slot.dataset.serial,
        slot: slot.dataset.slot,
        pool: slot.dataset.pool
    });

    dragData = {
        serial: slot.dataset.serial,
        slot: parseInt(slot.dataset.slot),
        pool: slot.dataset.pool
    };
    e.dataTransfer.effectAllowed = 'move';

    // สร้าง drag image ที่ติดเมาส์
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 93;
    canvas.height = 93;

    // วาดพื้นหลัง
    ctx.fillStyle = '#222';
    ctx.fillRect(0, 0, 93, 93);
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, 93, 93);

    // เขียนข้อความ
    ctx.fillStyle = '#fff';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Slot ' + slot.dataset.slot, 46, 46);

    // ใช้ canvas เป็น drag image
    e.dataTransfer.setDragImage(canvas, 46, 46);
}

function dragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    // เพิ่ม visual feedback เมื่อ drag over
    const target = e.currentTarget;
    target.classList.add('drag-over');
}

function dragLeave(e) {
    // รีเซ็ต visual feedback เมื่อ drag leave
    const target = e.currentTarget;
    target.classList.remove('drag-over');
}

async function dropItem(e) {
    e.preventDefault();
    const target = e.currentTarget;
    const targetSlot = parseInt(target.dataset.slot);
    const targetPool = target.dataset.pool;

    console.log('dropItem called:', {
        dragData: dragData,
        targetSlot: targetSlot,
        targetPool: targetPool
    });

    // ตรวจสอบว่ามีข้อมูล drag และไม่ใช่การลากไปที่เดิม
    if (!dragData) {
        console.log('No drag data');
        return;
    }

    if (dragData.slot === targetSlot && dragData.pool === targetPool) {
        console.log('Same slot, ignoring');
        dragData = null;
        return;
    }

    // ป้องกันการลากไอเท็มไปยัง pool อื่น
    if (targetPool !== dragData.pool) {
        showStatusMessage('❌ ไม่สามารถย้ายไอเท็มไปยัง Pool อื่นได้', 'error');
        dragData = null;
        return;
    }

    // หาไอเท็มที่กำลังลาก (itemA) และไอเท็มที่อยู่ในตำแหน่งปลายทาง (itemB)
    const itemA = currentItems.find(i => i.SerialNum == dragData.serial);
    const itemB = currentItems.find(i => parseInt(i.SlotID) === targetSlot && i.PoolID == targetPool);

    console.log('Items found:', {
        itemA: itemA ? { SerialNum: itemA.SerialNum, ItemName: itemA.ItemName, SlotID: itemA.SlotID } : null,
        itemB: itemB ? { SerialNum: itemB.SerialNum, ItemName: itemB.ItemName, SlotID: itemB.SlotID } : null,
        currentItems: currentItems.length
    });

    // ตรวจสอบว่าไอเท็มที่ลากมีอยู่จริง
    if (!itemA) {
        showStatusMessage('❌ ไม่พบไอเท็มที่ต้องการย้าย', 'error');
        dragData = null;
        return;
    }

    const updates = [];

    // กรณีที่มีไอเท็มอยู่ในตำแหน่งปลายทางแล้ว - ทำการสลับ
    if (itemB) {
        console.log('Swapping items:', itemA.ItemName, 'with', itemB.ItemName);

        // แสดงข้อความยืนยันการสลับ
        const confirmSwap = confirm(
            `คุณต้องการสลับตำแหน่งไอเท็มหรือไม่?\n\n` +
            `"${itemA.ItemName}" (Slot ${dragData.slot}) ↔ "${itemB.ItemName}" (Slot ${targetSlot})`
        );

        if (!confirmSwap) {
            dragData = null;
            return;
        }

        // สลับตำแหน่ง slot
        const originalSlotA = itemA.SlotID;
        const originalSlotB = itemB.SlotID;

        itemA.SlotID = originalSlotB;
        itemB.SlotID = originalSlotA;

        console.log('Swapping slots:', {
            itemA: { name: itemA.ItemName, from: originalSlotA, to: itemA.SlotID },
            itemB: { name: itemB.ItemName, from: originalSlotB, to: itemB.SlotID }
        });

        // เพิ่มทั้งสองไอเท็มเข้าไปใน updates
        updates.push(updateItem(itemA));
        updates.push(updateItem(itemB));
    } else {
        // กรณีที่ตำแหน่งปลายทางว่าง - เพียงแค่ย้าย
        console.log('Moving item:', itemA.ItemName, 'from slot', itemA.SlotID, 'to slot', targetSlot);
        itemA.SlotID = targetSlot;
        updates.push(updateItem(itemA));
    }

    // ส่งการอัปเดตทั้งหมดและโหลดข้อมูลใหม่
    try {
        const responses = await Promise.all(updates);
        console.log('Update responses:', responses);

        const results = await Promise.all(responses.map(r => r.json()));
        console.log('Update results:', results);

        // ตรวจสอบว่าการอัปเดตสำเร็จหรือไม่
        const allSuccess = results.every(result => result.success);

        if (allSuccess) {
            // แสดงข้อความสำเร็จ
            if (itemB) {
                showStatusMessage(`✅ สลับตำแหน่งไอเท็ม "${itemA.ItemName}" และ "${itemB.ItemName}" สำเร็จ`, 'success');
            } else {
                showStatusMessage(`✅ ย้ายไอเท็ม "${itemA.ItemName}" ไปยัง Slot ${targetSlot} สำเร็จ`, 'success');
            }
        } else {
            showStatusMessage('❌ เกิดข้อผิดพลาดในการอัปเดตไอเท็ม', 'error');
        }

        // อัปเดตเฉพาะ pool ที่มีการเปลี่ยนแปลง
        const affectedPools = new Set([targetPool]);
        if (itemB && itemB.PoolID !== targetPool) {
            affectedPools.add(itemB.PoolID);
        }

        // อัปเดตแต่ละ pool ที่ได้รับผลกระทบ
        for (const poolId of affectedPools) {
            await loadItems(poolId);
        }

        // แสดง tab ที่มีการเปลี่ยนแปลง
        showActiveTab(targetPool);

    } catch (error) {
        console.error('Error updating items:', error);
        showStatusMessage('❌ เกิดข้อผิดพลาดในการอัปเดตไอเท็ม', 'error');
        // อัปเดตเฉพาะ pool ที่เกี่ยวข้องเพื่อรีเซ็ตสถานะ
        await loadItems(targetPool);
    }

    // รีเซ็ต dragData
    dragData = null;
}

function updateItem(item) {
    console.log('Updating item:', item);
    return fetch('files/game_systems/class_module/cashshop_manager.php?action=updateItem', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(Object.entries(item))
    }).then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response;
    }).catch(error => {
        console.error('Error in updateItem:', error);
        throw error;
    });
}

// ===================
// เปิด modal เพิ่มไอเท็มใหม่ที่ slot ว่าง
function openAddModalWithSlot(slotID, poolID) {
    const form = document.getElementById('itemForm');
    form.reset();
    form.SerialNum.value = '0';
    form.SlotID.value = slotID;
    form.PoolID.value = poolID;
    form.TabID.value = poolID;
    currentEditSerialNum = null;
    document.getElementById('deleteBtn').style.display = 'none';
    new bootstrap.Modal(document.getElementById('itemModal')).show();
}

// ===================
// และในฟังก์ชัน editItem() ที่เปิด modal แก้ไข ควรเซ็ตค่าตัวแปรเหล่านี้ด้วย
function editItem(item) {
    const form = document.getElementById('itemForm');
    form.reset();
    for (const key in item) {
        if (form[key]) form[key].value = item[key];
    }
    // กำหนดตัวแปร global เพื่อใช้ลบ
    currentEditSerialNum = item.SerialNum;
    currentEditPoolID = item.PoolID;
    currentEditTabID = item.TabID;
    currentEditSlotID = item.SlotID;

    document.getElementById('deleteBtn').style.display = 'inline-block';
    new bootstrap.Modal(document.getElementById('itemModal')).show();
}

// ===================
// ตั้ง event การ submit ฟอร์ม เพื่อส่งข้อมูลแบบ POST
function setupFormSubmit() {
    const form = document.getElementById('itemForm');
    if (!form) return;

    form.addEventListener('submit', function (e) {
        e.preventDefault();

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = new FormData(form);

        // ปรับเวลาให้มีวินาที
        ['SaleTimeBegin', 'SaleTimeEnd', 'DailySaleTimeBegin', 'DailySaleTimeEnd'].forEach(key => {
            const val = formData.get(key);
            if (val && /^\d{2}:\d{2}$/.test(val)) {
                formData.set(key, val + ':00');
            }
        });

        // ลบ field ที่ไม่มีค่าออก (ไม่ส่งค่าเปล่า)
        ['SaleDateBegin', 'SaleDateEnd', 'SaleTimeBegin', 'SaleTimeEnd', 'DailySaleTimeBegin', 'DailySaleTimeEnd'].forEach(key => {
            if (!formData.get(key)) {
                formData.delete(key);
            }
        });

        // Log ข้อมูลที่จะส่ง
        console.log('[Form Submit] Sending Data:');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}:`, value);
        }

        const isNew = formData.get('SerialNum') === '0';
        const action = isNew ? 'addItem' : 'updateItem';

        console.log(`[Form Submit] Action = ${action}`);

        fetch(`files/game_systems/class_module/cashshop_manager.php?action=${action}`, {
            method: 'POST',
            body: formData
        })
        .then(res => {
            console.log('[Fetch] Raw response:', res);
            return res.json();
        })
        .then(res => {
            console.log('[Fetch] Parsed JSON response:', res);
            if (res.success) {
                bootstrap.Modal.getInstance(document.getElementById('itemModal')).hide();
                // อัปเดตเฉพาะ pool ที่เกี่ยวข้อง
                if (currentPoolID) {
                    loadItems(currentPoolID);
                    showActiveTab(currentPoolID);
                } else {
                    loadItems();
                }
                alert('✅ Item saved successfully!');
            } else {
                console.error('[Error] Server responded with error:', res.error);
                alert('❌ ' + (res.error || 'Failed to save item'));
            }
        })
        .catch(err => {
            console.error('[Exception] Fetch failed:', err);
            alert('❌ Error saving item, check console.');
        });
    });
}

// ===================
function setupDeleteButton() {
    const deleteBtn = document.getElementById('deleteBtn');
    if (!deleteBtn) return;

    deleteBtn.addEventListener('click', () => {
        console.log('[Delete] Preparing to delete:', {
            SerialNum: currentEditSerialNum,
            PoolID: currentEditPoolID,
            TabID: currentEditTabID,
            SlotID: currentEditSlotID
        });

        if (
            currentEditSerialNum == null ||
            currentEditPoolID == null ||
            currentEditTabID == null ||
            currentEditSlotID == null
        ) {
            alert('❌ ข้อมูลไม่ครบหรือไม่ถูกต้องสำหรับลบไอเท็ม');
            return;
        }

        if (!confirm('คุณต้องการลบไอเท็มนี้ใช่หรือไม่?')) return;

        fetch('files/game_systems/class_module/cashshop_manager.php?action=deleteItem', {
            method: 'POST',
            body: new URLSearchParams({
                SerialNum: currentEditSerialNum,
                PoolID: currentEditPoolID,
                TabID: currentEditTabID,
                SlotID: currentEditSlotID
            })
        })
        .then(res => res.json())
        .then(res => {
            if (res.success) {
                bootstrap.Modal.getInstance(document.getElementById('itemModal')).hide();
                // อัปเดตเฉพาะ pool ที่เกี่ยวข้อง
                if (currentEditPoolID) {
                    loadItems(currentEditPoolID);
                    showActiveTab(currentEditPoolID);
                } else {
                    loadItems();
                }
                alert('✅ ลบไอเท็มเรียบร้อยแล้ว');
            } else {
                alert('❌ ลบไอเท็มไม่สำเร็จ: ' + (res.error || 'Unknown error'));
            }
        })
        .catch(err => {
            console.error('Delete Error:', err);
            alert('❌ เกิดข้อผิดพลาดในการลบไอเท็ม');
        });
    });
}
