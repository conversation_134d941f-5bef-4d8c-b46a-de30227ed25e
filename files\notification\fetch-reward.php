<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../_app/dbinfo.inc.php');
require('../../_app/general_config.inc.php');

if(isset($_POST['view'])){

$params = array();
$options = array( "Scrollable" => SQLSRV_CURSOR_KEYSET );

$output = '';
$status_query = "SELECT * FROM WEB_Reward_chack WHERE Reward_status = 0";
$result = sqlsrv_query($conn, $status_query, $params, $options);
$count = sqlsrv_num_rows($result);
$data = array(
    'countreward_notification'  => $count
);
echo json_encode($data);

}

?>