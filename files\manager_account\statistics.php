<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.panel-featured-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.panel-featured-left:hover::before {
    transform: translateX(100%);
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.panel-featured-primary {
    border-left-color: #0088cc;
    background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Progress Groups */
.progress-group {
    margin-bottom: 20px;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
}

.progress-sm {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Real-time Updates */
.real-time-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background: #5cb85c;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Advanced Controls */
.advanced-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-label {
    font-weight: 600;
    min-width: 120px;
}

.btn-export {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
}

.comparison-mode {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
    }

    .control-label {
        min-width: auto;
        margin-bottom: 5px;
    }
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.3);
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #11998e;
}

input:checked + .slider:before {
    transform: translateX(26px);
}
</style>

<header class="page-header">
    <h2>สถิติบัญชีรวม (Admin) <span class="real-time-indicator" title="อัปเดตแบบ Real-time"></span></h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Statistics</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Time periods for statistics
$periods = [
    '7' => '7 วันล่าสุด',
    '30' => '30 วันล่าสุด',
    '90' => '3 เดือนล่าสุด',
    '365' => '1 ปีล่าสุด'
];

$selectedPeriod = isset($_GET['period']) ? $_GET['period'] : '30';
$comparisonMode = isset($_GET['comparison']) ? $_GET['comparison'] : 'none';
$exportFormat = isset($_GET['export']) ? $_GET['export'] : '';

// Handle export requests
if ($exportFormat) {
    switch ($exportFormat) {
        case 'csv':
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="statistics_' . date('Y-m-d') . '.csv"');
            // Export logic will be added here
            exit;
        case 'pdf':
            // PDF export logic will be added here
            exit;
        case 'excel':
            // Excel export logic will be added here
            exit;
    }
}

// Overall statistics
$selectOverallStats = "SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as active_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as new_accounts,
    AVG(PlayTime) as avg_playtime,
    SUM(PlayTime) as total_playtime,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as currently_online
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$overallQuery = sqlsrv_query($conn, $selectOverallStats);
$overallStats = sqlsrv_fetch_array($overallQuery, SQLSRV_FETCH_ASSOC);

// Daily activity trend
$selectDailyTrend = "SELECT 
    CAST(LoginTime as DATE) as activity_date,
    COUNT(DISTINCT UserNum) as unique_players,
    COUNT(*) as total_logins,
    AVG(DATEDIFF(minute, LoginTime, ISNULL(LogoutTime, GETDATE()))) as avg_session_time
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY CAST(LoginTime as DATE)
    ORDER BY activity_date DESC";
$dailyTrendQuery = sqlsrv_query($conn, $selectDailyTrend);
$dailyTrend = array();
while ($row = sqlsrv_fetch_array($dailyTrendQuery, SQLSRV_FETCH_ASSOC)) {
    $dailyTrend[] = $row;
}

// Registration trend
$selectRegTrend = "SELECT 
    CAST(createDate as DATE) as reg_date,
    COUNT(*) as new_registrations
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE createDate >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY CAST(createDate as DATE)
    ORDER BY reg_date DESC";
$regTrendQuery = sqlsrv_query($conn, $selectRegTrend);
$regTrend = array();
while ($row = sqlsrv_fetch_array($regTrendQuery, SQLSRV_FETCH_ASSOC)) {
    $regTrend[] = $row;
}

// Character level distribution
$selectLevelDist = "SELECT 
    CASE 
        WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
        WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
        WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
        WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
        ELSE '200+'
    END as level_range,
    COUNT(*) as character_count
    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
    WHERE LEV > 0
    GROUP BY 
        CASE 
            WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
            WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
            WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
            WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
            ELSE '200+'
        END
    ORDER BY level_range";
$levelDistQuery = sqlsrv_query($conn, $selectLevelDist);
$levelDistribution = array();
while ($row = sqlsrv_fetch_array($levelDistQuery, SQLSRV_FETCH_ASSOC)) {
    $levelDistribution[] = $row;
}

// Financial statistics
$selectFinancialStats = "SELECT 
    SUM(c.Cash) as total_cash,
    SUM(c.CashBonus) as total_cash_bonus,
    SUM(c.CashTotal) as total_cash_all,
    SUM(c.Rpoint) as total_rewards,
    COUNT(CASE WHEN c.Cash > 0 THEN 1 END) as accounts_with_cash,
    COUNT(p.DonateID) as total_purchases
    FROM [".DATABASE_CCA."].[dbo].CashAccount c
    LEFT JOIN WEB_MyPurchases p ON c.UserNum = p.UserNum";
$financialStatsQuery = sqlsrv_query($conn, $selectFinancialStats);
$financialStats = sqlsrv_fetch_array($financialStatsQuery, SQLSRV_FETCH_ASSOC);

// Top countries by IP
$selectCountryStats = "SELECT TOP 10
    SUBSTRING(LastIp, 1, CHARINDEX('.', LastIp + '.') - 1) + '.*.*.*' as ip_range,
    COUNT(*) as account_count
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LastIp IS NOT NULL
    GROUP BY SUBSTRING(LastIp, 1, CHARINDEX('.', LastIp + '.') - 1)
    ORDER BY account_count DESC";
$countryStatsQuery = sqlsrv_query($conn, $selectCountryStats);
$countryStats = array();
while ($row = sqlsrv_fetch_array($countryStatsQuery, SQLSRV_FETCH_ASSOC)) {
    $countryStats[] = $row;
}

// Peak hours analysis
$selectPeakHours = "SELECT 
    DATEPART(hour, LoginTime) as hour_of_day,
    COUNT(*) as login_count,
    COUNT(DISTINCT UserNum) as unique_players
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY DATEPART(hour, LoginTime)
    ORDER BY login_count DESC";
$peakHoursQuery = sqlsrv_query($conn, $selectPeakHours);
$peakHours = array();
while ($row = sqlsrv_fetch_array($peakHoursQuery, SQLSRV_FETCH_ASSOC)) {
    $peakHours[] = $row;
}
?>

<div class="row">
    <!-- Advanced Controls -->
    <div class="col-md-12">
        <div class="advanced-controls">
            <div class="row">
                <div class="col-md-6">
                    <div class="control-group">
                        <span class="control-label">ช่วงเวลา:</span>
                        <div class="btn-group" role="group">
                            <?php foreach ($periods as $period => $label): ?>
                                <a href="?url=manager_account/statistics&period=<?php echo $period; ?>&comparison=<?php echo $comparisonMode; ?>"
                                   class="btn btn-sm <?php echo $selectedPeriod == $period ? 'btn-light' : 'btn-outline-light'; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="control-group">
                        <span class="control-label">โหมดเปรียบเทียบ:</span>
                        <div class="btn-group" role="group">
                            <a href="?url=manager_account/statistics&period=<?php echo $selectedPeriod; ?>&comparison=none"
                               class="btn btn-sm <?php echo $comparisonMode == 'none' ? 'btn-light' : 'btn-outline-light'; ?>">
                                ปกติ
                            </a>
                            <a href="?url=manager_account/statistics&period=<?php echo $selectedPeriod; ?>&comparison=previous"
                               class="btn btn-sm <?php echo $comparisonMode == 'previous' ? 'btn-light' : 'btn-outline-light'; ?>">
                                เปรียบเทียบช่วงก่อน
                            </a>
                            <a href="?url=manager_account/statistics&period=<?php echo $selectedPeriod; ?>&comparison=year"
                               class="btn btn-sm <?php echo $comparisonMode == 'year' ? 'btn-light' : 'btn-outline-light'; ?>">
                                เปรียบเทียบปีก่อน
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="control-group">
                        <span class="control-label">ส่งออกข้อมูล:</span>
                        <div class="btn-group" role="group">
                            <button class="btn btn-export btn-sm" onclick="exportData('csv')">
                                <i class="fa fa-file-text-o"></i> CSV
                            </button>
                            <button class="btn btn-export btn-sm" onclick="exportData('excel')">
                                <i class="fa fa-file-excel-o"></i> Excel
                            </button>
                            <button class="btn btn-export btn-sm" onclick="exportData('pdf')">
                                <i class="fa fa-file-pdf-o"></i> PDF
                            </button>
                        </div>
                    </div>
                    <div class="control-group">
                        <span class="control-label">อัปเดตอัตโนมัติ:</span>
                        <label class="switch">
                            <input type="checkbox" id="autoRefresh" checked>
                            <span class="slider round"></span>
                        </label>
                        <span class="text-light ml-2">ทุก 30 วินาที</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($comparisonMode != 'none'): ?>
<div class="row">
    <div class="col-md-12">
        <div class="comparison-mode">
            <h4><i class="fa fa-bar-chart"></i> โหมดเปรียบเทียบ:
                <?php
                echo $comparisonMode == 'previous' ? 'เปรียบเทียบกับช่วงก่อนหน้า' : 'เปรียบเทียบกับปีก่อน';
                ?>
            </h4>
            <p class="mb-0">ข้อมูลจะแสดงการเปรียบเทียบระหว่างช่วงเวลาที่เลือกกับช่วงเวลาอ้างอิง พร้อมแสดงเปอร์เซ็นต์การเปลี่ยนแปลง</p>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Overall Statistics -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $overallStats['currently_online']; ?> ออนไลน์</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['active_accounts']); ?></strong>
                                <span class="text-secondary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['new_accounts']); ?></strong>
                                <span class="text-tertiary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-clock"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">เวลาเล่นเฉลี่ย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo round($overallStats['avg_playtime'] / 60, 1); ?></strong>
                                <span class="text-quaternary">ชั่วโมง</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">รวม <?php echo round($overallStats['total_playtime'] / 60, 0); ?> ชั่วโมง</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Daily Activity Trend -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">แนวโน้มกิจกรรมรายวัน</h2>
            </header>
            <div class="panel-body">
                <canvas id="dailyTrendChart" height="100"></canvas>
            </div>
        </section>
    </div>

    <!-- Level Distribution -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การกระจาย Level</h2>
            </header>
            <div class="panel-body">
                <canvas id="levelDistChart" height="150"></canvas>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Peak Hours -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ช่วงเวลาที่มีผู้เล่นมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ชั่วโมง</th>
                                <th>จำนวนการเข้าสู่ระบบ</th>
                                <th>ผู้เล่นที่แตกต่าง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($peakHours, 0, 10) as $hour): ?>
                                <tr>
                                    <td><?php echo sprintf('%02d:00', $hour['hour_of_day']); ?></td>
                                    <td><?php echo number_format($hour['login_count']); ?></td>
                                    <td><?php echo number_format($hour['unique_players']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>

    <!-- Financial Overview -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติทางการเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="text-center mb-lg">
                            <span class="text-xl text-weight-bold text-primary"><?php echo number_format($financialStats['total_cash_all']); ?></span>
                            <p class="text-sm text-muted mb-none">Cash รวมทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-center mb-lg">
                            <span class="text-xl text-weight-bold text-success"><?php echo number_format($financialStats['total_purchases']); ?></span>
                            <p class="text-sm text-muted mb-none">การซื้อทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['accounts_with_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">บัญชีที่มี Cash</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_rewards']); ?></span>
                            <p class="text-xs text-muted mb-none">Reward Points รวม</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- IP Range Statistics -->
    <div class="col-md-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติ IP Range ที่มีผู้เล่นมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>IP Range</th>
                                <th>จำนวนบัญชี</th>
                                <th>เปอร์เซ็นต์</th>
                                <th>กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($countryStats as $country): ?>
                                <?php $percentage = ($country['account_count'] / $overallStats['total_accounts']) * 100; ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($country['ip_range']); ?></td>
                                    <td><?php echo number_format($country['account_count']); ?></td>
                                    <td><?php echo round($percentage, 2); ?>%</td>
                                    <td>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar progress-bar-primary" style="width: <?php echo $percentage; ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Daily Trend Chart
    var dailyCtx = document.getElementById('dailyTrendChart').getContext('2d');
    var dailyData = {
        labels: [
            <?php 
            $dates = array();
            $uniquePlayers = array();
            $totalLogins = array();
            foreach (array_reverse($dailyTrend) as $day) {
                $dates[] = "'" . date('m/d', strtotime($day['activity_date'])) . "'";
                $uniquePlayers[] = $day['unique_players'];
                $totalLogins[] = $day['total_logins'];
            }
            echo implode(',', $dates);
            ?>
        ],
        datasets: [{
            label: 'ผู้เล่นที่แตกต่าง',
            data: [<?php echo implode(',', $uniquePlayers); ?>],
            borderColor: '#0088cc',
            backgroundColor: 'rgba(0, 136, 204, 0.1)',
            borderWidth: 2,
            fill: false
        }, {
            label: 'การเข้าสู่ระบบทั้งหมด',
            data: [<?php echo implode(',', $totalLogins); ?>],
            borderColor: '#5cb85c',
            backgroundColor: 'rgba(92, 184, 92, 0.1)',
            borderWidth: 2,
            fill: false
        }]
    };
    
    new Chart(dailyCtx, {
        type: 'line',
        data: dailyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Level Distribution Chart
    var levelCtx = document.getElementById('levelDistChart').getContext('2d');
    var levelData = {
        labels: [
            <?php 
            $levelLabels = array();
            $levelCounts = array();
            foreach ($levelDistribution as $level) {
                $levelLabels[] = "'" . $level['level_range'] . "'";
                $levelCounts[] = $level['character_count'];
            }
            echo implode(',', $levelLabels);
            ?>
        ],
        datasets: [{
            data: [<?php echo implode(',', $levelCounts); ?>],
            backgroundColor: ['#0088cc', '#5cb85c', '#f0ad4e', '#d9534f', '#5bc0de'],
            borderWidth: 0
        }]
    };
    
    new Chart(levelCtx, {
        type: 'doughnut',
        data: levelData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Initialize auto-refresh
    document.getElementById('autoRefresh').addEventListener('change', toggleAutoRefresh);
    startAutoRefresh();

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+R or F5 for manual refresh
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
            e.preventDefault();
            refreshStatistics();
        }

        // Ctrl+E for export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportData('csv');
        }
    });

    // Add tooltips for better UX
    $('[data-toggle="tooltip"]').tooltip();

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});

// Auto-refresh functionality
let autoRefreshInterval;
let refreshEnabled = true;

function toggleAutoRefresh() {
    const checkbox = document.getElementById('autoRefresh');
    refreshEnabled = checkbox.checked;

    if (refreshEnabled) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
}

function startAutoRefresh() {
    if (autoRefreshInterval) clearInterval(autoRefreshInterval);

    autoRefreshInterval = setInterval(function() {
        if (refreshEnabled) {
            // Refresh data without full page reload
            refreshStatistics();
        }
    }, 30000); // 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

function refreshStatistics() {
    // Add loading indicator
    const indicator = document.querySelector('.real-time-indicator');
    if (indicator) {
        indicator.style.background = '#f0ad4e';
        indicator.style.animation = 'pulse 0.5s infinite';
    }

    // Simulate data refresh (in real implementation, this would be an AJAX call)
    setTimeout(function() {
        if (indicator) {
            indicator.style.background = '#5cb85c';
            indicator.style.animation = 'pulse 2s infinite';
        }

        // Show notification
        showNotification('ข้อมูลได้รับการอัปเดตแล้ว', 'success');
    }, 1000);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function exportData(format = 'csv') {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', format);
    currentUrl.searchParams.set('period', '<?php echo $selectedPeriod; ?>');
    currentUrl.searchParams.set('comparison', '<?php echo $comparisonMode; ?>');

    // Show loading notification
    showNotification('กำลังเตรียมไฟล์สำหรับดาวน์โหลด...', 'info');

    // Open export URL
    window.open(currentUrl.toString(), '_blank');
}
</script>
</script>
