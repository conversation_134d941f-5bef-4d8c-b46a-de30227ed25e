<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Enhanced Cards */
.panel-featured-left {
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.panel-featured-left:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.panel-featured-primary {
    border-left-color: #0088cc;
}

.panel-featured-secondary {
    border-left-color: #5cb85c;
}

.panel-featured-tertiary {
    border-left-color: #f0ad4e;
}

.panel-featured-quaternary {
    border-left-color: #d9534f;
}

/* Widget Summary */
.widget-summary {
    display: flex;
    align-items: center;
    padding: 15px;
}

.widget-summary-col-icon {
    margin-right: 15px;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, #0088cc, #0066aa);
}

.summary-icon.bg-secondary {
    background: linear-gradient(135deg, #5cb85c, #449d44);
}

.summary-icon.bg-tertiary {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
}

.summary-icon.bg-quaternary {
    background: linear-gradient(135deg, #d9534f, #c9302c);
}

.summary .title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary .amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.summary-footer {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

/* Progress Groups */
.progress-group {
    margin-bottom: 20px;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
}

.progress-sm {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-summary {
        flex-direction: column;
        text-align: center;
    }

    .widget-summary-col-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .summary .amount {
        font-size: 20px;
    }
}
</style>

<header class="page-header">
    <h2>สถิติบัญชีรวม (Admin)</h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Statistics</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Time periods for statistics
$periods = [
    '7' => '7 วันล่าสุด',
    '30' => '30 วันล่าสุด',
    '90' => '3 เดือนล่าสุด',
    '365' => '1 ปีล่าสุด'
];

$selectedPeriod = isset($_GET['period']) ? $_GET['period'] : '30';

// Overall statistics
$selectOverallStats = "SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as active_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as new_accounts,
    AVG(PlayTime) as avg_playtime,
    SUM(PlayTime) as total_playtime,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as currently_online
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$overallQuery = sqlsrv_query($conn, $selectOverallStats);
$overallStats = sqlsrv_fetch_array($overallQuery, SQLSRV_FETCH_ASSOC);

// Daily activity trend
$selectDailyTrend = "SELECT 
    CAST(LoginTime as DATE) as activity_date,
    COUNT(DISTINCT UserNum) as unique_players,
    COUNT(*) as total_logins,
    AVG(DATEDIFF(minute, LoginTime, ISNULL(LogoutTime, GETDATE()))) as avg_session_time
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY CAST(LoginTime as DATE)
    ORDER BY activity_date DESC";
$dailyTrendQuery = sqlsrv_query($conn, $selectDailyTrend);
$dailyTrend = array();
while ($row = sqlsrv_fetch_array($dailyTrendQuery, SQLSRV_FETCH_ASSOC)) {
    $dailyTrend[] = $row;
}

// Registration trend
$selectRegTrend = "SELECT 
    CAST(createDate as DATE) as reg_date,
    COUNT(*) as new_registrations
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE createDate >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY CAST(createDate as DATE)
    ORDER BY reg_date DESC";
$regTrendQuery = sqlsrv_query($conn, $selectRegTrend);
$regTrend = array();
while ($row = sqlsrv_fetch_array($regTrendQuery, SQLSRV_FETCH_ASSOC)) {
    $regTrend[] = $row;
}

// Character level distribution
$selectLevelDist = "SELECT 
    CASE 
        WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
        WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
        WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
        WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
        ELSE '200+'
    END as level_range,
    COUNT(*) as character_count
    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
    WHERE LEV > 0
    GROUP BY 
        CASE 
            WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
            WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
            WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
            WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
            ELSE '200+'
        END
    ORDER BY level_range";
$levelDistQuery = sqlsrv_query($conn, $selectLevelDist);
$levelDistribution = array();
while ($row = sqlsrv_fetch_array($levelDistQuery, SQLSRV_FETCH_ASSOC)) {
    $levelDistribution[] = $row;
}

// Financial statistics
$selectFinancialStats = "SELECT 
    SUM(c.Cash) as total_cash,
    SUM(c.CashBonus) as total_cash_bonus,
    SUM(c.CashTotal) as total_cash_all,
    SUM(c.Reward) as total_rewards,
    COUNT(CASE WHEN c.Cash > 0 THEN 1 END) as accounts_with_cash,
    COUNT(p.DonateID) as total_purchases
    FROM [".DATABASE_CCA."].[dbo].cabal_cash_table c
    LEFT JOIN WEB_MyPurchases p ON c.CustomerID = p.CustomerID";
$financialStatsQuery = sqlsrv_query($conn, $selectFinancialStats);
$financialStats = sqlsrv_fetch_array($financialStatsQuery, SQLSRV_FETCH_ASSOC);

// Top countries by IP
$selectCountryStats = "SELECT TOP 10
    SUBSTRING(LastIp, 1, CHARINDEX('.', LastIp + '.') - 1) + '.*.*.*' as ip_range,
    COUNT(*) as account_count
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LastIp IS NOT NULL
    GROUP BY SUBSTRING(LastIp, 1, CHARINDEX('.', LastIp + '.') - 1)
    ORDER BY account_count DESC";
$countryStatsQuery = sqlsrv_query($conn, $selectCountryStats);
$countryStats = array();
while ($row = sqlsrv_fetch_array($countryStatsQuery, SQLSRV_FETCH_ASSOC)) {
    $countryStats[] = $row;
}

// Peak hours analysis
$selectPeakHours = "SELECT 
    DATEPART(hour, LoginTime) as hour_of_day,
    COUNT(*) as login_count,
    COUNT(DISTINCT UserNum) as unique_players
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY DATEPART(hour, LoginTime)
    ORDER BY login_count DESC";
$peakHoursQuery = sqlsrv_query($conn, $selectPeakHours);
$peakHours = array();
while ($row = sqlsrv_fetch_array($peakHoursQuery, SQLSRV_FETCH_ASSOC)) {
    $peakHours[] = $row;
}
?>

<div class="row">
    <!-- Period Selector -->
    <div class="col-md-12">
        <section class="panel">
            <div class="panel-body">
                <div class="btn-group" role="group">
                    <?php foreach ($periods as $period => $label): ?>
                        <a href="?url=manager_account/statistics&period=<?php echo $period; ?>" 
                           class="btn <?php echo $selectedPeriod == $period ? 'btn-primary' : 'btn-default'; ?>">
                            <?php echo $label; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
                <div class="pull-right">
                    <button class="btn btn-success" onclick="exportData()">
                        <i class="fa fa-download"></i> ส่งออกข้อมูล
                    </button>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Overall Statistics -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $overallStats['currently_online']; ?> ออนไลน์</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['active_accounts']); ?></strong>
                                <span class="text-secondary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['new_accounts']); ?></strong>
                                <span class="text-tertiary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-clock"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">เวลาเล่นเฉลี่ย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo round($overallStats['avg_playtime'] / 60, 1); ?></strong>
                                <span class="text-quaternary">ชั่วโมง</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">รวม <?php echo round($overallStats['total_playtime'] / 60, 0); ?> ชั่วโมง</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Daily Activity Trend -->
    <div class="col-xl-8 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">แนวโน้มกิจกรรมรายวัน</h2>
            </header>
            <div class="panel-body">
                <canvas id="dailyTrendChart" height="100"></canvas>
            </div>
        </section>
    </div>

    <!-- Level Distribution -->
    <div class="col-xl-4 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">การกระจาย Level</h2>
            </header>
            <div class="panel-body">
                <canvas id="levelDistChart" height="150"></canvas>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- Peak Hours -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">ช่วงเวลาที่มีผู้เล่นมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ชั่วโมง</th>
                                <th>จำนวนการเข้าสู่ระบบ</th>
                                <th>ผู้เล่นที่แตกต่าง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($peakHours, 0, 10) as $hour): ?>
                                <tr>
                                    <td><?php echo sprintf('%02d:00', $hour['hour_of_day']); ?></td>
                                    <td><?php echo number_format($hour['login_count']); ?></td>
                                    <td><?php echo number_format($hour['unique_players']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>

    <!-- Financial Overview -->
    <div class="col-xl-6 col-lg-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติทางการเงิน</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="text-center mb-lg">
                            <span class="text-xl text-weight-bold text-primary"><?php echo number_format($financialStats['total_cash_all']); ?></span>
                            <p class="text-sm text-muted mb-none">Cash รวมทั้งหมด</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-center mb-lg">
                            <span class="text-xl text-weight-bold text-success"><?php echo number_format($financialStats['total_purchases']); ?></span>
                            <p class="text-sm text-muted mb-none">การซื้อทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['accounts_with_cash']); ?></span>
                            <p class="text-xs text-muted mb-none">บัญชีที่มี Cash</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-center">
                            <span class="text-lg text-weight-semibold"><?php echo number_format($financialStats['total_rewards']); ?></span>
                            <p class="text-xs text-muted mb-none">Reward Points รวม</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<div class="row">
    <!-- IP Range Statistics -->
    <div class="col-md-12">
        <section class="panel">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>
                <h2 class="panel-title">สถิติ IP Range ที่มีผู้เล่นมากที่สุด</h2>
            </header>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>IP Range</th>
                                <th>จำนวนบัญชี</th>
                                <th>เปอร์เซ็นต์</th>
                                <th>กราฟ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($countryStats as $country): ?>
                                <?php $percentage = ($country['account_count'] / $overallStats['total_accounts']) * 100; ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($country['ip_range']); ?></td>
                                    <td><?php echo number_format($country['account_count']); ?></td>
                                    <td><?php echo round($percentage, 2); ?>%</td>
                                    <td>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar progress-bar-primary" style="width: <?php echo $percentage; ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Chart Scripts -->
<script src="assets/vendor/chart.js/chart.min.js"></script>
<script>
$(document).ready(function() {
    // Daily Trend Chart
    var dailyCtx = document.getElementById('dailyTrendChart').getContext('2d');
    var dailyData = {
        labels: [
            <?php 
            $dates = array();
            $uniquePlayers = array();
            $totalLogins = array();
            foreach (array_reverse($dailyTrend) as $day) {
                $dates[] = "'" . date('m/d', strtotime($day['activity_date'])) . "'";
                $uniquePlayers[] = $day['unique_players'];
                $totalLogins[] = $day['total_logins'];
            }
            echo implode(',', $dates);
            ?>
        ],
        datasets: [{
            label: 'ผู้เล่นที่แตกต่าง',
            data: [<?php echo implode(',', $uniquePlayers); ?>],
            borderColor: '#0088cc',
            backgroundColor: 'rgba(0, 136, 204, 0.1)',
            borderWidth: 2,
            fill: false
        }, {
            label: 'การเข้าสู่ระบบทั้งหมด',
            data: [<?php echo implode(',', $totalLogins); ?>],
            borderColor: '#5cb85c',
            backgroundColor: 'rgba(92, 184, 92, 0.1)',
            borderWidth: 2,
            fill: false
        }]
    };
    
    new Chart(dailyCtx, {
        type: 'line',
        data: dailyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Level Distribution Chart
    var levelCtx = document.getElementById('levelDistChart').getContext('2d');
    var levelData = {
        labels: [
            <?php 
            $levelLabels = array();
            $levelCounts = array();
            foreach ($levelDistribution as $level) {
                $levelLabels[] = "'" . $level['level_range'] . "'";
                $levelCounts[] = $level['character_count'];
            }
            echo implode(',', $levelLabels);
            ?>
        ],
        datasets: [{
            data: [<?php echo implode(',', $levelCounts); ?>],
            backgroundColor: ['#0088cc', '#5cb85c', '#f0ad4e', '#d9534f', '#5bc0de'],
            borderWidth: 0
        }]
    };
    
    new Chart(levelCtx, {
        type: 'doughnut',
        data: levelData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});

function exportData() {
    // Export functionality
    window.open('?url=manager_account/export-statistics&period=<?php echo $selectedPeriod; ?>', '_blank');
}
</script>
