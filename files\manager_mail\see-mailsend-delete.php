<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> ตรวจการลบเมลล์

    </h1>
    <div class="subheader-block">
        <a href="?url=manager_mail/mail-statistics" class="btn btn-primary btn-sm">
            <i class="fal fa-chart-bar"></i> สถิติ Mail
        </a>
        <a href="?url=manager_mail/see-mailsend" class="btn btn-success btn-sm">
            <i class="fal fa-paper-plane"></i> เมลล์ปกติ
        </a>
    </div>
</div>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>Mail Delete <span class="fw-300"><i>ตารางการลบเมลล์</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                <table id="datatables-default" class="table table-sm table-bordered w-100">
                    <thead>
                        <tr>
							<th>เวลาลบเมลล์</th>
                            <th>ผู้ส่ง(CharIdx)</th>
							<th>SentMailID</th>
                            <th>DeliveryTime</th>
                            <th>Alz</th>
                            <th>ItemKindIdx</th>
                            <th>ItemOption</th>
                            <th>ItemDurationIdx</th>
                            <th>ผู้รับ(CharName)</th>
                            <th>Title</th>
                            <th>Content</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 2000;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM [".DATABASE_WEB."].[dbo].cabal_mail_sent_deleted_logs ORDER BY DateDeleted DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr>
                               <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[0])); ?></td>
								<td><?php echo $row[1]; ?></td>
								<td><?php echo $row[2]; ?></td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[4])); ?></td>
                                <td><?php echo number_format($row[5]); ?></td>
								<td><?php echo $row[6]; ?></td>
                                <td><?php echo $row[7]; ?></td>
                                <td><?php echo $row[8]; ?></td>
                                <td><?php echo $userLogin->thaitrans($row[9]); ?></td>
                                <td><?php echo $userLogin->thaitrans($row[10]); ?></td>
                                <td><?php echo $userLogin->thaitrans($row[11]); ?></td>
                       
                                </tr>
                            <?php
                        }
                        ?>
                        </tbody>
                    </table>   
                </div>
            </div>
        </div>
    </div>
</div>
