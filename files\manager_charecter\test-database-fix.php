<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-database"></i> ทดสอบการแก้ไข Database Constants
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา Undefined constant "DATABASE_AC"</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> เปลี่ยนจาก DATABASE_AC เป็น DATABASE_ACC ตาม constant ที่ถูกต้อง
                </div>
                
                <h5>❌ ปัญหาที่เกิดขึ้น</h5>
                <div class="alert alert-danger">
                    <h6>Fatal Error:</h6>
                    <pre><code>Uncaught Error: Undefined constant "DATABASE_AC" in character-data.php:99</code></pre>
                    <p><strong>สาเหตุ:</strong> ใช้ constant ที่ไม่มีอยู่ในระบบ</p>
                </div>
                
                <h5>✅ การแก้ไขที่ทำ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ ก่อนแก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>INNER JOIN [".DATABASE_AC."].[dbo].cabal_auth_table</code></pre>
                                <p class="mb-0"><strong>ปัญหา:</strong> DATABASE_AC ไม่ได้ถูกกำหนดในระบบ</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ หลังแก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>INNER JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table</code></pre>
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> ใช้ DATABASE_ACC ที่ถูกต้อง</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 Database Constants ที่มีในระบบ</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>Constant</th>
                                <th>Value</th>
                                <th>Description</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $constants = [
                                'DATABASE_ACC' => ['value' => DATABASE_ACC, 'desc' => 'Account Database', 'used' => true],
                                'DATABASE_CCA' => ['value' => DATABASE_CCA, 'desc' => 'Cabal Cash Database', 'used' => false],
                                'DATABASE_ACT' => ['value' => DATABASE_ACT, 'desc' => 'Authentication Database', 'used' => false],
                                'DATABASE_NBL' => ['value' => DATABASE_NBL, 'desc' => 'Netcafe Billing Database', 'used' => false],
                                'DATABASE_SV' => ['value' => DATABASE_SV, 'desc' => 'Server Database', 'used' => true],
                                'DATABASE_WEB' => ['value' => DATABASE_WEB, 'desc' => 'Web Panel Database', 'used' => false],
                                'DATABASE_EDATA' => ['value' => DATABASE_EDATA, 'desc' => 'Event Data Database', 'used' => false]
                            ];
                            
                            foreach ($constants as $name => $info) {
                                $statusClass = $info['used'] ? 'success' : 'secondary';
                                $statusText = $info['used'] ? 'ใช้งานใน API' : 'ไม่ได้ใช้';
                                
                                echo "<tr>";
                                echo "<td><code>{$name}</code></td>";
                                echo "<td><strong>{$info['value']}</strong></td>";
                                echo "<td>{$info['desc']}</td>";
                                echo "<td><span class='badge badge-{$statusClass}'>{$statusText}</span></td>";
                                echo "</tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบ API หลังแก้ไข</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testLiveStats()">
                        <i class="fal fa-chart-line"></i> ทดสอบ Live Stats
                    </button>
                    <button class="btn btn-info" onclick="testOnlineStats()">
                        <i class="fal fa-users"></i> ทดสอบ Online Stats
                    </button>
                    <button class="btn btn-warning" onclick="testWithDebug()">
                        <i class="fal fa-bug"></i> ทดสอบ Debug Mode
                    </button>
                    <button class="btn btn-success" onclick="openCharacterMonitor()">
                        <i class="fal fa-external-link"></i> เปิด Character Monitor
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">🔍 รายละเอียดการแก้ไข</h5>
                <div class="accordion" id="fixAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. ตำแหน่งที่แก้ไข
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#fixAccordion">
                            <div class="card-body">
                                <p><strong>ไฟล์:</strong> <code>files/manager_charecter/api/character-data.php</code></p>
                                
                                <p><strong>บรรทัดที่ 99:</strong> ฟังก์ชัน getLiveStats()</p>
                                <pre><code>// เช็คผู้เล่นออนไลน์จาก Auth table
INNER JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table a ON c.UserNum = a.UserNum</code></pre>
                                
                                <p><strong>บรรทัดที่ 431:</strong> ฟังก์ชัน getOnlineStats()</p>
                                <pre><code>// เช็คผู้เล่นออนไลน์จาก Auth table
INNER JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table a ON c.UserNum = a.UserNum</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. ผลกระทบของการแก้ไข
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#fixAccordion">
                            <div class="card-body">
                                <p><strong>ฟังก์ชันที่ได้รับผลกระทบ:</strong></p>
                                <ul>
                                    <li><code>getLiveStats()</code> - สถิติตัวละครแบบ real-time</li>
                                    <li><code>getOnlineStats()</code> - สถิติผู้เล่นออนไลน์</li>
                                </ul>
                                
                                <p><strong>การปรับปรุง:</strong></p>
                                <ul>
                                    <li>✅ API จะไม่ error อีกต่อไป</li>
                                    <li>✅ การเช็คผู้เล่นออนไลน์จะทำงานได้</li>
                                    <li>✅ Character Monitor จะแสดงข้อมูลได้ถูกต้อง</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข DATABASE_AC → DATABASE_ACC</li>
                                <li>✅ แก้ไขใน 2 ตำแหน่ง</li>
                                <li>✅ API จะไม่ Fatal Error อีก</li>
                                <li>✅ ฟังก์ชันออนไลน์ทำงานได้</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ ใช้ constant ที่ถูกต้อง</li>
                                <li>✅ เชื่อมต่อ Auth table ได้</li>
                                <li>✅ Character Monitor ทำงานปกติ</li>
                                <li>✅ ไม่มี JavaScript errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testLiveStats() {
    await testAPI('live_stats', 'Live Stats API');
}

async function testOnlineStats() {
    await testAPI('online_stats', 'Online Stats API');
}

async function testWithDebug() {
    await testAPI('live_stats', 'Live Stats (Debug)', '&debug=1');
}

async function testAPI(action, title, extraParams = '') {
    const resultDiv = document.getElementById('test-results');
    
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> กำลังทดสอบ ' + title + '...</div>';
    
    try {
        const url = 'files/manager_charecter/api/character-data.php?action=' + action + extraParams;
        
        const response = await fetch(url, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> ' + title + ' Test Result</h6>';
        html += '<p><strong>URL:</strong> ' + url + '</p>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Content-Type:</strong> ' + (response.headers.get('content-type') || 'Not set') + '</p>';
        
        // ตรวจสอบว่าเป็น JSON หรือ HTML
        if (responseText.trim().startsWith('{')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>Response Type:</strong> ✅ JSON</p>';
                html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                
                if (data.error) {
                    html += '<p><strong>Error:</strong> ' + data.error + '</p>';
                }
                
                if (data.success && data.data) {
                    if (typeof data.data === 'object') {
                        html += '<p><strong>Data Keys:</strong> ' + Object.keys(data.data).join(', ') + '</p>';
                        
                        // แสดงข้อมูลเฉพาะ
                        if (action === 'live_stats' && data.data.online_characters !== undefined) {
                            html += '<p><strong>Online Characters:</strong> ' + data.data.online_characters.toLocaleString() + '</p>';
                        }
                        if (action === 'online_stats' && data.data.total_online !== undefined) {
                            html += '<p><strong>Total Online:</strong> ' + data.data.total_online.toLocaleString() + '</p>';
                        }
                    }
                }
                
                if (data.debug_info) {
                    html += '<details><summary>Debug Information</summary>';
                    html += '<pre>' + JSON.stringify(data.debug_info, null, 2) + '</pre>';
                    html += '</details>';
                }
                
            } catch (parseError) {
                html += '<p><strong>Response Type:</strong> ❌ Invalid JSON</p>';
                html += '<p><strong>Parse Error:</strong> ' + parseError.message + '</p>';
            }
        } else if (responseText.includes('Fatal error') || responseText.includes('Uncaught Error')) {
            html += '<p><strong>Response Type:</strong> ❌ PHP Fatal Error</p>';
            html += '<p><strong>Error Found:</strong> ยังมี PHP errors</p>';
        } else if (responseText.includes('<!DOCTYPE')) {
            html += '<p><strong>Response Type:</strong> ❌ HTML Document</p>';
        } else {
            html += '<p><strong>Response Type:</strong> ❓ Unknown</p>';
        }
        
        // แสดง response ถ้าไม่ใหญ่เกินไป
        if (responseText.length > 0 && responseText.length < 2000) {
            html += '<details><summary>Raw Response (' + responseText.length + ' chars)</summary>';
            html += '<pre>' + responseText + '</pre>';
            html += '</details>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> ' + title + ' Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

function openCharacterMonitor() {
    window.open('?url=manager_charecter/character-monitor', '_blank');
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

details {
    margin-top: 0.5rem;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

summary:hover {
    text-decoration: underline;
}
</style>
