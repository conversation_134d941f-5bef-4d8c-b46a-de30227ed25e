<?php
session_start();
header('Content-Type: application/json');

// ตรวจสอบ login
if (!isset($_SESSION['userLogin'])) {
    echo json_encode(['success' => false, 'message' => 'ไม่มีการ login']);
    exit;
}

require_once("../../../_app/dbinfo.inc.php");
require_once("../../../_app/general_config.inc.php");

$conn = db_connect();

if (!$conn) {
    echo json_encode(['success' => false, 'message' => 'ไม่สามารถเชื่อมต่อฐานข้อมูลได้']);
    exit;
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'generate':
        generateCodes($conn);
        break;
    case 'list':
        listCodes($conn);
        break;
    case 'delete':
        deleteCode($conn);
        break;
    case 'delete_all_unused':
        deleteAllUnusedCodes($conn);
        break;
    case 'delete_all_codes':
        deleteAllCodes($conn);
        break;
    case 'edit':
        editCode($conn);
        break;
    case 'list_usage_logs':
        listUsageLogs($conn);
        break;
    case 'delete_usage_log':
        deleteUsageLog($conn);
        break;
    case 'delete_all_usage_logs':
        deleteAllUsageLogs($conn);
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Action ไม่ถูกต้อง']);
        break;
}

function generateCodes($conn) {
    // สร้าง table ถ้าไม่มี
    $createSql = "
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_Code' AND xtype='U')
    CREATE TABLE [dbo].[WEB_Redeem_Code] (
        [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
        [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
        [quantity] int NULL,
        [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
        [datecreated] datetime DEFAULT getdate() NULL,
        [expiry_date] datetime NULL
    )";
    sqlsrv_query($conn, $createSql);
    
    $count = intval($_POST['count'] ?? 1);
    $items = trim($_POST['items'] ?? '');
    $quantity = intval($_POST['quantity'] ?? 1);
    $expiry_date = $_POST['expiry_date'] ?? '';
    $code_format = $_POST['code_format'] ?? 'XXXX-XXXX-XXXX-XXXX';
    
    // ตรวจสอบข้อมูล
    if ($count < 1 || $count > 1000) {
        echo json_encode(['success' => false, 'message' => 'จำนวน Code ต้องอยู่ระหว่าง 1-1000']);
        return;
    }
    
    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายการไอเท็ม']);
        return;
    }
    
    // ตรวจสอบรูปแบบไอเท็ม
    $itemList = explode(',', $items);
    foreach ($itemList as $item) {
        $item = trim($item);
        if (!preg_match('/^\d+:\d+:\d+$/', $item)) {
            echo json_encode(['success' => false, 'message' => "รูปแบบไอเท็มไม่ถูกต้อง: $item"]);
            return;
        }
    }
    
    $generatedCodes = [];
    $successCount = 0;
    
    for ($i = 0; $i < $count; $i++) {
        $code = generateUniqueCode($conn, $code_format);

        if ($code) {
            // จัดการวันหมดอายุ
            $params = [$code, $items, $quantity];

            if (!empty($expiry_date) && $expiry_date !== 'null') {
                // ตรวจสอบและแปลงรูปแบบวันที่
                $formatted_date = formatExpiryDate($expiry_date);
                if ($formatted_date) {
                    $sql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated, expiry_date) VALUES (?, ?, ?, '0', GETDATE(), ?)";
                    $params[] = $formatted_date;
                } else {
                    // หากรูปแบบวันที่ไม่ถูกต้อง ให้ insert โดยไม่มีวันหมดอายุ
                    $sql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated) VALUES (?, ?, ?, '0', GETDATE())";
                    $expiry_date = null;
                }
            } else {
                $sql = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status, datecreated) VALUES (?, ?, ?, '0', GETDATE())";
                $expiry_date = null;
            }

            $stmt = sqlsrv_query($conn, $sql, $params);

            if ($stmt) {
                $successCount++;
                $generatedCodes[] = [
                    'id' => $successCount,
                    'code' => $code,
                    'items' => $items,
                    'quantity' => $quantity,
                    'status' => '0',
                    'datecreated' => date('Y-m-d H:i:s'),
                    'expiry_date' => $expiry_date
                ];
            } else {
                // Log error for debugging
                $errors = sqlsrv_errors();
                error_log("Insert failed for code $code: " . print_r($errors, true));

                // หากเป็น error เกี่ยวกับวันที่ ให้แจ้งผู้ใช้
                if ($errors && strpos(print_r($errors, true), 'datetime') !== false) {
                    echo json_encode(['success' => false, 'message' => 'รูปแบบวันหมดอายุไม่ถูกต้อง กรุณาตรวจสอบรูปแบบวันที่']);
                    return;
                }
            }
        }
    }
    
    if ($successCount > 0) {
        echo json_encode([
            'success' => true,
            'message' => "สร้าง $successCount codes สำเร็จ",
            'codes' => $generatedCodes
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => "ไม่สามารถสร้าง codes ได้ กรุณาตรวจสอบข้อมูลและลองใหม่อีกครั้ง",
            'debug_info' => [
                'requested_count' => $count,
                'items' => $items,
                'quantity' => $quantity,
                'expiry_date' => $expiry_date,
                'code_format' => $code_format
            ]
        ]);
    }
}

function generateUniqueCode($conn, $format) {
    $maxAttempts = 100;
    
    for ($i = 0; $i < $maxAttempts; $i++) {
        $code = generateCodeByFormat($format);
        
        // ตรวจสอบว่า code ซ้ำหรือไม่
        $checkSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code WHERE code = ?";
        $checkStmt = sqlsrv_query($conn, $checkSql, [$code]);
        
        if ($checkStmt) {
            $result = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
            if ($result && $result['count'] == 0) {
                return $code;
            }
        }
    }
    
    return false;
}

function generateCodeByFormat($format) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < strlen($format); $i++) {
        if ($format[$i] === 'X') {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        } else {
            $code .= $format[$i];
        }
    }
    
    return $code;
}

function listCodes($conn) {
    // สร้าง table ถ้าไม่มี
    $createSql = "
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_Code' AND xtype='U')
    CREATE TABLE [dbo].[WEB_Redeem_Code] (
        [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [code] nvarchar(32) COLLATE Thai_CI_AS NULL,
        [items] nvarchar(100) COLLATE Thai_CI_AS NULL,
        [quantity] int NULL,
        [status] nvarchar(1) COLLATE Thai_CI_AS NULL,
        [datecreated] datetime DEFAULT getdate() NULL,
        [expiry_date] datetime NULL
    )";
    sqlsrv_query($conn, $createSql);
    
    $sql = "SELECT TOP 100 * FROM WEB_Redeem_Code ORDER BY datecreated DESC";
    $stmt = sqlsrv_query($conn, $sql);
    
    $codes = [];
    if ($stmt) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // แปลง DateTime objects เป็น string
            if (isset($row['datecreated']) && $row['datecreated'] instanceof DateTime) {
                $row['datecreated'] = $row['datecreated']->format('Y-m-d H:i:s');
            }
            if (isset($row['expiry_date']) && $row['expiry_date'] instanceof DateTime) {
                $row['expiry_date'] = $row['expiry_date']->format('Y-m-d H:i:s');
            }
            $codes[] = $row;
        }
    }
    
    echo json_encode(['success' => true, 'codes' => $codes]);
}

function formatExpiryDate($dateString) {
    try {
        // ลองแปลงวันที่หลายรูปแบบ
        $formats = [
            'Y-m-d\TH:i',           // HTML datetime-local format
            'Y-m-d H:i:s',          // Standard SQL format
            'Y-m-d H:i',            // Without seconds
            'Y-m-d',                // Date only
        ];

        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateString);
            if ($date !== false) {
                return $date->format('Y-m-d H:i:s');
            }
        }

        // ลองใช้ strtotime
        $timestamp = strtotime($dateString);
        if ($timestamp !== false) {
            return date('Y-m-d H:i:s', $timestamp);
        }

        return false;
    } catch (Exception $e) {
        error_log("Date format error: " . $e->getMessage());
        return false;
    }
}

function deleteCode($conn) {
    $id = intval($_POST['id'] ?? 0);

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    $sql = "DELETE FROM WEB_Redeem_Code WHERE id = ? AND status = '0'";
    $stmt = sqlsrv_query($conn, $sql, [$id]);

    if ($stmt) {
        echo json_encode(['success' => true, 'message' => 'ลบ Code สำเร็จ']);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}

function deleteAllUnusedCodes($conn) {
    // ตรวจสอบจำนวน codes ที่จะลบ
    $countSql = "SELECT COUNT(*) as count FROM WEB_Redeem_Code WHERE status = '0'";
    $countStmt = sqlsrv_query($conn, $countSql);

    if (!$countStmt) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
        return;
    }

    $countResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
    $unusedCount = $countResult['count'];

    if ($unusedCount == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่มี codes ที่ยังไม่ได้ใช้งานให้ลบ']);
        return;
    }

    // ลบ codes ที่ยังไม่ใช้งาน
    $deleteSql = "DELETE FROM WEB_Redeem_Code WHERE status = '0'";
    $deleteStmt = sqlsrv_query($conn, $deleteSql);

    if ($deleteStmt) {
        echo json_encode([
            'success' => true,
            'message' => "ลบ codes ที่ยังไม่ได้ใช้งานสำเร็จ",
            'deleted_count' => $unusedCount
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}

function deleteAllCodes($conn) {
    // ตรวจสอบจำนวน codes ทั้งหมด
    $countAllSql = "SELECT COUNT(*) as total_count FROM WEB_Redeem_Code";
    $countAllStmt = sqlsrv_query($conn, $countAllSql);

    if (!$countAllStmt) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
        return;
    }

    $countAllResult = sqlsrv_fetch_array($countAllStmt, SQLSRV_FETCH_ASSOC);
    $totalCount = $countAllResult['total_count'];

    if ($totalCount == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่มี codes ให้ลบ']);
        return;
    }

    // ตรวจสอบจำนวน codes แยกตามสถานะ
    $countUsedSql = "SELECT COUNT(*) as used_count FROM WEB_Redeem_Code WHERE status = '1'";
    $countUsedStmt = sqlsrv_query($conn, $countUsedSql);
    $usedResult = sqlsrv_fetch_array($countUsedStmt, SQLSRV_FETCH_ASSOC);
    $usedCount = $usedResult['used_count'];

    $countUnusedSql = "SELECT COUNT(*) as unused_count FROM WEB_Redeem_Code WHERE status = '0'";
    $countUnusedStmt = sqlsrv_query($conn, $countUnusedSql);
    $unusedResult = sqlsrv_fetch_array($countUnusedStmt, SQLSRV_FETCH_ASSOC);
    $unusedCount = $unusedResult['unused_count'];

    // ลบ codes ทั้งหมด
    $deleteAllSql = "DELETE FROM WEB_Redeem_Code";
    $deleteAllStmt = sqlsrv_query($conn, $deleteAllSql);

    if ($deleteAllStmt) {
        echo json_encode([
            'success' => true,
            'message' => "ลบ codes ทั้งหมดสำเร็จ",
            'deleted_count' => $totalCount,
            'used_count' => $usedCount,
            'unused_count' => $unusedCount
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}

function editCode($conn) {
    $id = intval($_POST['id'] ?? 0);
    $items = trim($_POST['items'] ?? '');
    $quantity = intval($_POST['quantity'] ?? 1);
    $expiry_date = $_POST['expiry_date'] ?? '';

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    if (empty($items)) {
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกรายการไอเท็ม']);
        return;
    }

    if ($quantity < 1) {
        echo json_encode(['success' => false, 'message' => 'จำนวนต้องมากกว่า 0']);
        return;
    }

    // ตรวจสอบรูปแบบไอเท็ม
    $itemList = explode(',', $items);
    foreach ($itemList as $item) {
        $item = trim($item);
        if (!preg_match('/^\d+:\d+:\d+$/', $item)) {
            echo json_encode(['success' => false, 'message' => "รูปแบบไอเท็มไม่ถูกต้อง: $item"]);
            return;
        }
    }

    // ตรวจสอบว่า code ยังไม่ได้ใช้งาน
    $checkSql = "SELECT status FROM WEB_Redeem_Code WHERE id = ?";
    $checkStmt = sqlsrv_query($conn, $checkSql, [$id]);

    if (!$checkStmt) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
        return;
    }

    $checkResult = sqlsrv_fetch_array($checkStmt, SQLSRV_FETCH_ASSOC);
    if (!$checkResult) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบ Code ที่ต้องการแก้ไข']);
        return;
    }

    if ($checkResult['status'] != '0') {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถแก้ไข Code ที่ใช้งานแล้วได้']);
        return;
    }

    // อัปเดตข้อมูล
    $params = [$items, $quantity, $id];

    if (!empty($expiry_date) && $expiry_date !== 'null') {
        $formatted_date = formatExpiryDate($expiry_date);
        if ($formatted_date) {
            $sql = "UPDATE WEB_Redeem_Code SET items = ?, quantity = ?, expiry_date = ? WHERE id = ? AND status = '0'";
            $params = [$items, $quantity, $formatted_date, $id];
        } else {
            $sql = "UPDATE WEB_Redeem_Code SET items = ?, quantity = ?, expiry_date = NULL WHERE id = ? AND status = '0'";
        }
    } else {
        $sql = "UPDATE WEB_Redeem_Code SET items = ?, quantity = ?, expiry_date = NULL WHERE id = ? AND status = '0'";
    }

    $stmt = sqlsrv_query($conn, $sql, $params);

    if ($stmt) {
        echo json_encode(['success' => true, 'message' => 'แก้ไขข้อมูลสำเร็จ']);
    } else {
        $errors = sqlsrv_errors();
        error_log("Update failed: " . print_r($errors, true));
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถแก้ไขข้อมูลได้']);
    }
}

function listUsageLogs($conn) {
    try {
        // สร้าง table ถ้าไม่มี (ใช้โครงสร้างที่ถูกต้อง)
        $createSql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_Redeem_CodesUsed' AND xtype='U')
        CREATE TABLE [dbo].[WEB_Redeem_CodesUsed] (
            [id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            [CodeID] int NOT NULL,
            [UserNum] int NULL,
            [code] nvarchar(20) COLLATE Thai_CI_AS NULL,
            [dateused] datetime DEFAULT getdate() NULL
        )";
        $createStmt = sqlsrv_query($conn, $createSql);

        if (!$createStmt) {
            $errors = sqlsrv_errors();
            error_log("Error creating WEB_Redeem_CodesUsed table: " . print_r($errors, true));
        }

        // ลองดึงข้อมูลจากตารางที่มีอยู่
        $sql = "SELECT TOP 100 * FROM WEB_Redeem_CodesUsed ORDER BY dateused DESC";
        $stmt = sqlsrv_query($conn, $sql);

        $logs = [];
        if ($stmt) {
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                // แปลง DateTime objects เป็น string และจัดการชื่อคอลัมน์
                if (isset($row['dateused']) && $row['dateused'] instanceof DateTime) {
                    $row['dateused'] = $row['dateused']->format('Y-m-d H:i:s');
                }
                // เพิ่ม used_date เพื่อความเข้ากันได้
                $row['used_date'] = $row['dateused'] ?? null;
                $logs[] = $row;
            }

            echo json_encode([
                'success' => true,
                'logs' => $logs,
                'count' => count($logs),
                'message' => 'ดึงข้อมูล usage logs สำเร็จ'
            ]);
        } else {
            // หากไม่สามารถดึงข้อมูลได้ ให้ log error
            $errors = sqlsrv_errors();
            error_log("Error fetching usage logs: " . print_r($errors, true));

            echo json_encode([
                'success' => false,
                'logs' => [],
                'count' => 0,
                'message' => 'ไม่สามารถดึงข้อมูล usage logs ได้',
                'error_details' => $errors
            ]);
        }

    } catch (Exception $e) {
        error_log("Exception in listUsageLogs: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'logs' => [],
            'count' => 0,
            'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
        ]);
    }
}

function deleteUsageLog($conn) {
    $id = intval($_POST['id'] ?? 0);

    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
        return;
    }

    $sql = "DELETE FROM WEB_Redeem_CodesUsed WHERE id = ?";
    $stmt = sqlsrv_query($conn, $sql, [$id]);

    if ($stmt) {
        echo json_encode(['success' => true, 'message' => 'ลบ Log การใช้งานสำเร็จ']);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}

function deleteAllUsageLogs($conn) {
    // ตรวจสอบจำนวน logs ที่จะลบ
    $countSql = "SELECT COUNT(*) as count FROM WEB_Redeem_CodesUsed";
    $countStmt = sqlsrv_query($conn, $countSql);

    if (!$countStmt) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถตรวจสอบข้อมูลได้']);
        return;
    }

    $countResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
    $logCount = $countResult['count'];

    if ($logCount == 0) {
        echo json_encode(['success' => false, 'message' => 'ไม่มี Log การใช้งานให้ลบ']);
        return;
    }

    // ลบ logs ทั้งหมด
    $deleteSql = "DELETE FROM WEB_Redeem_CodesUsed";
    $deleteStmt = sqlsrv_query($conn, $deleteSql);

    if ($deleteStmt) {
        echo json_encode([
            'success' => true,
            'message' => "ลบ Log การใช้งานทั้งหมดสำเร็จ",
            'deleted_count' => $logCount
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถลบข้อมูลได้']);
    }
}
?>
