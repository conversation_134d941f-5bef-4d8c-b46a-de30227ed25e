# ระบบจัดการ Username ในกิจกรรม (Username Manager)

## คำอธิบาย
ระบบนี้ใช้สำหรับจัดการ username ในกิจกรรมต่างๆ โดยสามารถเพิ่ม username เข้าไปใน tables ต่างๆ ที่เกี่ยวข้องกับกิจกรรม

## Tables ที่รองรับ
- WEB_Discord_boost_userLog
- WEB_Donate_Title_userLog
- WEB_Event_RewardLog
- WEB_EventOBT_DataLog
- WEB_Facebook_Share
- WEB_Gamer_Steamer_userLog

## โครงสร้าง Table
ทุก table จะมีโครงสร้างเหมือนกัน:
```sql
[id] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
[userid] varchar(50) COLLATE Thai_CI_AS NULL,
[status] tinyint NULL DEFAULT 1,
[added_time] datetime NOT NULL DEFAULT GETDATE()
```

## วิธีการใช้งาน

### 1. เข้าสู่ระบบ
- เข้าไปที่เมนู "จัดการระบบเกมส์" > "จัดการ Username ในกิจกรรม"
- หรือเข้าผ่าน URL: `?url=game_systems/username_manager`

### 2. เลือก Table
- เลือก table ที่ต้องการจัดการจาก dropdown "เลือก Table"
- ระบบจะแสดงข้อมูลของ table ที่เลือก

### 3. จัดการ Username
- **เพิ่ม Username**: กรอก username ในช่อง "Username" และกดปุ่ม "เพิ่ม Username"
- **แก้ไข Username**: กดปุ่ม "แก้ไข" ในแถวที่ต้องการแก้ไข จะเปิด modal สำหรับแก้ไข username และ status
- **ลบ Username**: กดปุ่ม "ลบ" ในแถวที่ต้องการลบ
- **ดูข้อมูล**: ระบบจะแสดงรายการ username ทั้งหมดในตาราง

## ฟีเจอร์

### การสร้าง Table อัตโนมัติ
- หาก table ยังไม่มีอยู่ในฐานข้อมูล ระบบจะสร้างให้อัตโนมัติ
- โครงสร้าง table จะเป็นไปตามมาตรฐานที่กำหนด

### การตรวจสอบข้อมูลซ้ำ
- ระบบจะตรวจสอบไม่ให้ username ซ้ำกันใน table เดียวกัน
- เมื่อแก้ไข username จะตรวจสอบไม่ให้ซ้ำกับ username อื่น (ยกเว้น record ปัจจุบัน)

### การแก้ไขข้อมูล
- สามารถแก้ไข username และ status ได้
- มี modal สำหรับแก้ไขข้อมูลที่ใช้งานง่าย
- ตรวจสอบความถูกต้องของข้อมูลก่อนบันทึก

### การแสดงสถานะ
- แสดงสถานะของ username (เปิดใช้งาน/ปิดใช้งาน)
- แสดงเวลาที่เพิ่มข้อมูล

## ไฟล์ที่เกี่ยวข้อง

### 1. username_manager.php
- หน้าหลักของระบบ
- ประกอบด้วย UI สำหรับจัดการ username

### 2. class_module/username_manager_api.php
- API สำหรับจัดการข้อมูล
- รองรับ actions: list, add, edit, delete

### 3. test_username_system.php
- ไฟล์ทดสอบระบบ
- ใช้สำหรับตรวจสอบการทำงานของระบบ

## การทดสอบระบบ
เรียกใช้ไฟล์ `test_username_system.php` เพื่อทดสอบ:
- การเชื่อมต่อฐานข้อมูล
- การดึงข้อมูลจาก WEB_Event_Data
- การตรวจสอบ tables ที่มีอยู่
- การสร้าง table ใหม่

## ความปลอดภัย
- ตรวจสอบ session login ก่อนใช้งาน
- ตรวจสอบ table ที่อนุญาตเท่านั้น
- ป้องกัน SQL Injection ด้วย prepared statements

## การแก้ไขปัญหา

### ปัญหาที่อาจพบ
1. **ไม่สามารถเชื่อมต่อฐานข้อมูลได้**
   - ตรวจสอบการตั้งค่าในไฟล์ `_app/dbinfo.inc.php`

2. **ไม่สามารถสร้าง table ได้**
   - ตรวจสอบสิทธิ์ของ user ในฐานข้อมูล

3. **ไม่สามารถเพิ่มข้อมูลได้**
   - ตรวจสอบว่า username ไม่ซ้ำกัน
   - ตรวจสอบ format ของข้อมูล

### การ Debug
- เปิดไฟล์ `test_username_system.php` เพื่อดูสถานะระบบ
- ตรวจสอบ console ของเบราว์เซอร์สำหรับ JavaScript errors
- ตรวจสอบ PHP error logs

## การอัปเดตในอนาคต
- เพิ่มการ import username จากไฟล์ CSV
- เพิ่มการ export ข้อมูล
- เพิ่มการกรองและค้นหาข้อมูล
- เพิ่มการจัดการสิทธิ์ผู้ใช้งาน
