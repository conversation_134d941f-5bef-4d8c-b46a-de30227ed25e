<?php
ob_start();
session_start();
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");	
header("Cache-Control: post-check=0, pre-check=0", false);	
header("Pragma: no-cache");

// require database connection
require('../../_app/dbinfo.inc.php');
require('../../_app/php/zpanel.class.php');
require('../../_app/general_config.inc.php');
$zpanel = new zpanel();

// post action
$action = strip_tags(trim($_POST['action']));

switch ($action) {

 case 'adminitems':
    
    $inputid  = strip_tags(trim($_POST['input_id']));
    $inputitem  = strip_tags(trim($_POST['input_Item']));
    $inputOption  = strip_tags(trim($_POST['input_Option']));
    $inputDur  = strip_tags(trim($_POST['input_Dur']));
    $inputUpgrade  = strip_tags(trim($_POST['input_Upgrade']));
    $inputSlot     = strip_tags(trim($_POST['input_Slot']));
    $inputQuantity = strip_tags(trim($_POST['input_quantity']));

    $adminuser = strip_tags(trim($_POST['admin-user']));
    $adminuseridx = strip_tags(trim($_POST['admin-useridx']));
    
    $binding_id       = 4096 + $inputitem;
    $binding_chars    = 528384 + $inputitem;
    $binding_chars_eq = 1572864 + $inputitem;

        if (empty($inputid)) {
            echo 'warning:check';
        }elseif (empty($inputitem)) {
            echo 'warning:checkitem';
        }elseif ($inputOption == "") {
            $inputOption = 0;
        }else{


                if ($inputSlot == "0") {
                $inputOption = $inputOption;
               } elseif ($inputSlot == "1") {
                $inputOption = 268435456;
               } elseif ($inputSlot == "2") {
                $inputOption = 536870912;
               } elseif ($inputSlot == "3") {
                $inputOption = 805306368;
               } elseif ($inputSlot == "4") {
                $inputOption = 1073741824;
               } else {
                echo 'Error:check';
               }

               $itemhex    = dechex($inputitem);
               $itemdexlen = strlen($itemhex);
               if (strlen($itemhex) == "1") {
                $itemdex = hexdec($inputUpgrade . '0' . dechex($inputitem));
               } elseif (strlen($itemhex) == "2") {
                $itemdex = hexdec($inputUpgrade . '0' . dechex($inputitem));
               } elseif (strlen($itemhex) == "3") {
                $itemdex = hexdec($inputUpgrade . dechex($inputitem));
               } elseif (strlen($itemhex) == "4") {
                $itemdex = hexdec($inputUpgrade . dechex($inputitem));
               } elseif (strlen($itemhex) > 4) {
                $itemdex = $inputitem;
               } else {
                echo 'Error:check';
               }


               $detail  = 'HEXCODE: ' . $itemhex . ' รหัสไอเท็ม: ' . $itemdex . ' ออฟชั่น: ' . $inputOption . ' ตำแหน่ง: ' . $itemdexlen;
               $dateNow = date('Y-m-d H:i:s');
               $selectauth      = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE ID  = '$inputid'";
               $selectauthParam = array();
               $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
               $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
               $UserNum         = $selectauthFetch['UserNum'];
             
            if (sqlsrv_rows_affected($selectauthQuery)) {
                for ($i = 0; $i <= $inputQuantity; $i++) {
                 $CashItemID       = "EXECUTE [" . DATABASE_CCA . "].[dbo].up_AddMyCashItemByItem ?, ?, ?, ?, ?, ?, ?";
                 $CashItemIDParams = array($UserNum, '0', '1', $itemdex, $inputOption, $inputDur,'Web Management System');
                 $CashItemIDQuery  = sqlsrv_query($conn, $CashItemID, $CashItemIDParams);
                }
                if ($CashItemIDQuery) {
                 $zpanel->GenerateWebLog_Admin($conn, '3', $adminuseridx, 'แอดไอเท็ม', "ให้ไอดี {$inputid} รหัสไอเท็ม : {$inputitem}/{$itemdex} ออฟชั่น : {$inputOption} Dur : {$inputDur}");
                       // echo json_encode( array("status" => 1,"message" => "Record updated.") );
                        echo "Success:check";
                     } else {
                        echo 'Error:check';
                     }   
            } else {
                echo 'Error:check';
            }
           // echo $output;
        }

        
break;

case 'add_donate'://ส่งโปรเติมเงิน

    $donateid = strip_tags(trim($_POST['id']));
    $userid = strip_tags(trim($_POST['userid']));
    $statusck  = strip_tags(trim($_POST['statuscheck']));

        if($statusck == ""){
            echo 'Error:check';
        }elseif(empty($userid)){
            echo 'Error:check';
        }else{
            
            $selectwalletData = "SELECT * FROM  ".DATABASE_WEB.".dbo.WEB_Truewallet WHERE id = '$donateid'";
            $selectwalletDataParam = array();
            $selectwalletDataQuery = sqlsrv_query($conn, $selectwalletData, $selectwalletDataParam);
            $selectwalletDataFetch = sqlsrv_fetch_array($selectwalletDataQuery, SQLSRV_FETCH_ASSOC);
            $status_check = $selectwalletDataFetch['Status'];
            $user = $selectwalletDataFetch['Userid'];
            $amount = $selectwalletDataFetch['Amount'];

            if($status_check == "1"){
                echo 'Error:check';
            }elseif($status_check == "0"){
                echo 'Error:check';
            }elseif($statusck == "0"){
                $updatestatus1 = "UPDATE [".DATABASE_WEB."].[dbo].WEB_Truewallet SET Status = '$statusck',Amount = '0' where id = '$donateid'";
                $updatestatus1Params = array($updatestatus1);
                $updatestatus1Query = sqlsrv_query($conn, $updatestatus1, $updatestatus1Params);
                echo 'Error:check1';
            }else{
            if($selectwalletDataQuery){
                    $updatestatus = "UPDATE [".DATABASE_WEB."].[dbo].WEB_Truewallet SET Status = '$statusck',Amount = '$amount' where id = '$donateid'";
                    $updatestatusParams = array($updatestatus);
                    $updatestatusQuery = sqlsrv_query($conn, $updatestatus, $updatestatusParams);
                        if($updatestatusQuery){
                            $AmountOpt = "EXECUTE [".DATABASE_WEB."].[dbo].WEB_DonationPacks_TrueW ?, ?";
                            $AmountParams = array($user,$amount);
                            $AmountQuery = sqlsrv_query($conn, $AmountOpt, $AmountParams);
                            if ($AmountQuery) {
                                    echo 'success';
                                } else {
                                    echo 'Error:check';
                                }
                        }else{
                            echo 'Error:check';
                        }
                    
               
                  
            }else{
                echo 'Error:check';
            }
        }    
        /*
        $selectUsersData = "SELECT * FROM  ".DATABASE_CCA.".dbo.CashAccount WHERE UserNum = '$CustomerIDz'";
        $selectUsersDataParam = array();
        $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersData, $selectUsersDataParam);
        $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
        $reward = $selectUsersDataFetch['Reward'];
           if($selectUsersDataQuery){
                if($RewardTypez >= 1 && $RewardTypez <= 3 && $reward >= 200){
                    $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward_point ?,?";
                    $RewardPlayerParams = array($CustomerIDz,$RewardTypez);
                    $RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                        if ($RewardPlayerQuery) {
                                echo 'success:200';
                        } else {
                                echo 'Error:check';
                        }
            }elseif($RewardTypez >= 4 && $RewardTypez <= 12 && $reward >= 400){
                    $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward_point ?,?";
                    $RewardPlayerParams = array($CustomerIDz,$RewardTypez);
                    $RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                        if ($RewardPlayerQuery) {
                                echo 'success:400';
                        } else {
                                echo 'Error:check';
                        }

            }elseif($RewardTypez >= 13 && $RewardTypez <= 15 && $reward >= 600){
                    $RewardPlayer = "EXECUTE [".DATABASE_CCA."].[dbo].cabal_reward_point ?,?";
                    $RewardPlayerParams = array($CustomerIDz,$RewardTypez);
                    $RewardPlayerQuery = sqlsrv_query($conn, $RewardPlayer, $RewardPlayerParams);
                        if ($RewardPlayerQuery) {
                                echo 'success:600';
                        } else {
                                echo 'Error:check';
                        }

            }else{ echo 'Error:check'; }
                
            }else{ echo 'Error:check'; }
*/
        }
break;
case 'add_bringer'://content bringer
    $charidx = strip_tags(trim($_POST['charsidx']));
    $UserNum = floor($charidx/8);

        if($charidx == ""){
            echo 'Error:check';
        }elseif(empty($charidx)){
            echo 'Error:check';
        }else{
            $selectauth  = "SELECT * FROM [" . DATABASE_ACC . "].[dbo].cabal_auth_table WHERE UserNum  = '$UserNum'";
            $selectauthParam = array();
            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
            $Useridx  = $selectauthFetch['UserNum'];
                    if($selectauthQuery){
                            $CashItemID  = sqlsrv_query($conn,"EXECUTE [" . DATABASE_CCA . "].[dbo].up_AddMyCashItemByItem '$UserNum','0', '1','531979','0','12'",array());
                            $CashItemID  = sqlsrv_query($conn,"EXECUTE [" . DATABASE_CCA . "].[dbo].up_AddMyCashItemByItem '$UserNum','0', '1','531981','0','12'",array());
                        
                              echo 'success';
                               
                        }else{
                            echo 'Error:check';
                        }
        }
break;
case 'recoverdel_char'://content bringer
    $delindexs = strip_tags(trim($_POST['delindex']));
    $charsidx = strip_tags(trim($_POST['charidx']));

    //$UserNum = floor($charidx/8);

        if(empty($delindexs)){
            echo 'Error:check';
        }else{
            $selectauth  = "SELECT * FROM [". DATABASE_SV ."].[dbo].[deleted_cabal_character_table] WHERE idx = '$delindexs'";
            $selectauthParam = array();
            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);
            $selectauthFetch = sqlsrv_fetch_array($selectauthQuery, SQLSRV_FETCH_ASSOC);
            $idxindex  = $selectauthFetch['idx'];
                       if($selectauthQuery){
                            $RecoverPlayer = "EXECUTE [".DATABASE_SV."].[dbo].cabal_tool_restore_character_web ?,?";
                            $RecoverPlayerParams = array($delindexs,$charsidx);
                            $RecoverPlayerQuery = sqlsrv_query($conn, $RecoverPlayer, $RecoverPlayerParams);
                                if ($RecoverPlayerQuery) {
                                        echo 'success';
                                } else {
                                        echo 'Error:check';
                                }
                       }else{
                        echo 'Error:check';
                     }                 
              
        }
break;
case 'delete_itemsinv'://content bringer
    $delindexs = strip_tags(trim($_POST['delindex']));
    $usernums = strip_tags(trim($_POST['useridx']));

    //$UserNum = floor($charidx/8);

        if(empty($delindexs)){
            echo 'Error:check';
        }elseif(empty($usernums)){
            echo 'Error:check';
        }else{
            $selectauth  = "SELECT * FROM [". DATABASE_CCA ."].[dbo].[MyCashItem] WHERE Id = '$delindexs'";
            $selectauthParam = array();
            $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);

                       if($selectauthQuery){
                            $DeleteItemsPlayer = "EXECUTE WEB_DeleteItem_inventory ?,?";
                            $DeleteItemsPlayerParams = array($delindexs,$usernums);
                            $DeleteItemsPlayerQuery = sqlsrv_query($conn, $DeleteItemsPlayer, $DeleteItemsPlayerParams);
                                if ($DeleteItemsPlayerQuery) {
                                        echo 'success';
                                } else {
                                        echo 'Error:check';
                                }
                       }else{
                        echo 'Error:check';
                     }                
              
        }
break;
case 'delete_mycashitem':
    $mycashitemid = strip_tags(trim($_POST['mycashid']));
    $usernum = strip_tags(trim($_POST['useridx']));

    //$UserNum = floor($charidx/8);

        if(empty($mycashitemid)){
            echo 'Error:check';
        }elseif(empty($usernum)){
            echo 'Error:check';
        }else{
			 $selectauth  = "SELECT * FROM [". DATABASE_CCA ."].[dbo].[MyCashItem] WHERE Id = '$mycashitemid'";
             $selectauthParam = array();
             $selectauthQuery = sqlsrv_query($conn, $selectauth, $selectauthParam);

                        if($selectauthQuery){
                             $DeleteItemsPlayer = "EXECUTE WEB_DeleteItem_inventory ?,?";
                             $DeleteItemsPlayerParams = array($mycashitemid,$usernum);
                             $DeleteItemsPlayerQuery = sqlsrv_query($conn, $DeleteItemsPlayer, $DeleteItemsPlayerParams);
                                if ($DeleteItemsPlayerQuery) {
                                        echo 'success';
                                 } else {
                                         echo 'Error:check';
                                 }
                        }else{
                        echo 'Error:check';
                      }                
              
        }
break;
    default:
        echo 'default';
        break;       
}
?>