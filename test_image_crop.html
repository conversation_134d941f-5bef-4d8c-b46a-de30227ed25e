<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Crop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .method {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .slot {
            width: 93px;
            height: 93px;
            border: 1px solid #555;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            background: #2e2e2e;
            margin: 10px auto;
        }
        
        .slot.has-item {
            background: #222;
        }
        
        /* วิธีที่ 1: object-fit: contain (รูปเล็ก ไม่เต็ม slot) */
        .method-contain img {
            width: 80%;
            height: 80%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: contain;
            border-radius: 4px;
        }
        
        /* วิธีที่ 2: object-fit: cover (รูปเต็ม slot, crop จากกลาง) */
        .method-cover img {
            width: 150%;
            height: 150%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: cover;
            object-position: center;
            border-radius: 6px;
        }
        
        /* วิธีที่ 3: background-image (ควบคุมได้มากที่สุด) */
        .method-background {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 6px;
        }
        
        /* วิธีที่ 4: scale แบบกำหนดเอง */
        .method-scale img {
            width: 120%;
            height: 120%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: cover;
            border-radius: 4px;
        }
        
        .slot-number {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: 11px;
            color: #bbb;
            z-index: 2;
            text-shadow: 1px 1px 2px #000;
        }
        
        .item-name {
            background: rgba(0,0,0,0.8);
            color: #fff;
            padding: 2px 4px;
            font-size: 9px;
            font-weight: bold;
            border-radius: 2px;
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            z-index: 3;
            text-align: center;
        }
        
        h3 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .description {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 15px;
        }
        
        .controls {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .controls label {
            display: block;
            margin: 10px 0 5px 0;
        }
        
        .controls input, .controls select {
            width: 100%;
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #555;
            background: #222;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ ทดสอบการแสดงรูปภาพใน Slot</h1>
        
        <div class="controls">
            <h3>🎛️ ควบคุมการแสดงผล</h3>
            <label>เลือกรูปไอเท็ม:</label>
            <select id="itemSelect" onchange="updateImages()">
                <option value="1">Item 1 (Sword)</option>
                <option value="2">Item 2 (Shield)</option>
                <option value="3">Item 3 (Helmet)</option>
                <option value="100">Item 100</option>
                <option value="500">Item 500</option>
                <option value="1000">Item 1000</option>
            </select>
            
            <label>ขนาดรูป (สำหรับวิธีที่ 4):</label>
            <input type="range" id="scaleRange" min="80" max="200" value="120" onchange="updateScale()">
            <span id="scaleValue">120%</span>
        </div>
        
        <div class="comparison">
            <div class="method">
                <h3>วิธีที่ 1: object-fit: contain</h3>
                <div class="description">รูปเล็ก ไม่เต็ม slot<br>เหมาะสำหรับดูรูปทั้งหมด</div>
                <div class="slot has-item method-contain">
                    <div class="slot-number">0</div>
                    <img id="img1" src="assets/images/items/1.png" alt="Item">
                    <div class="item-name">Sword +15</div>
                </div>
            </div>
            
            <div class="method">
                <h3>วิธีที่ 2: object-fit: cover</h3>
                <div class="description">รูปเต็ม slot, crop จากกลาง<br><strong>แนะนำ!</strong></div>
                <div class="slot has-item method-cover">
                    <div class="slot-number">1</div>
                    <img id="img2" src="assets/images/items/1.png" alt="Item">
                    <div class="item-name">Sword +15</div>
                </div>
            </div>
            
            <div class="method">
                <h3>วิธีที่ 3: background-image</h3>
                <div class="description">ใช้ CSS background<br>ควบคุมได้มากที่สุด</div>
                <div class="slot has-item method-background" id="bgSlot">
                    <div class="slot-number">2</div>
                    <div class="item-name">Sword +15</div>
                </div>
            </div>
            
            <div class="method">
                <h3>วิธีที่ 4: scale กำหนดเอง</h3>
                <div class="description">ปรับขนาดได้ตามต้องการ<br>ใช้ slider ด้านบน</div>
                <div class="slot has-item method-scale">
                    <div class="slot-number">3</div>
                    <img id="img4" src="assets/images/items/1.png" alt="Item">
                    <div class="item-name">Sword +15</div>
                </div>
            </div>
        </div>
        
        <div style="background: #333; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h3>📝 คำแนะนำ:</h3>
            <ul>
                <li><strong>วิธีที่ 2 (object-fit: cover)</strong> เหมาะสำหรับระบบ cash shop เพราะ:
                    <ul>
                        <li>รูปเต็ม slot ดูสวยงาม</li>
                        <li>Crop จากจุดกลางของรูปต้นฉบับ</li>
                        <li>ไม่บิดเบี้ยวรูป</li>
                        <li>ใช้งานง่าย</li>
                    </ul>
                </li>
                <li><strong>วิธีที่ 3 (background-image)</strong> ใช้เมื่อต้องการควบคุมตำแหน่ง crop แบบละเอียด</li>
                <li><strong>วิธีที่ 4 (scale กำหนดเอง)</strong> ใช้เมื่อต้องการปรับขนาดตามไอเท็มแต่ละชนิด</li>
            </ul>
        </div>
    </div>

    <script>
        function updateImages() {
            const itemId = document.getElementById('itemSelect').value;
            const imagePath = `assets/images/items/${itemId}.png`;
            
            // อัปเดตรูปภาพ
            document.getElementById('img1').src = imagePath;
            document.getElementById('img2').src = imagePath;
            document.getElementById('img4').src = imagePath;
            
            // อัปเดต background image
            document.getElementById('bgSlot').style.backgroundImage = `url('${imagePath}')`;
            
            // อัปเดตชื่อไอเท็ม
            const itemNames = {
                '1': 'Sword +15',
                '2': 'Shield +10', 
                '3': 'Helmet +7',
                '100': 'Magic Item',
                '500': 'Rare Weapon',
                '1000': 'Epic Armor'
            };
            
            const itemName = itemNames[itemId] || `Item ${itemId}`;
            document.querySelectorAll('.item-name').forEach(el => {
                el.textContent = itemName;
            });
        }
        
        function updateScale() {
            const scale = document.getElementById('scaleRange').value;
            document.getElementById('scaleValue').textContent = scale + '%';
            
            const img = document.getElementById('img4');
            img.style.width = scale + '%';
            img.style.height = scale + '%';
        }
        
        // เริ่มต้น
        updateImages();
    </script>
</body>
</html>
