<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');
// userLogged class

function thaitrans($string){
    $name2 =  mb_convert_encoding($string, 'UTF-16', 'UTF-8');
    $name3 =  iconv('windows-874', 'UTF-8',$name2);
    return $name3;
}

$sql = "SELECT EventID, Name FROM EventData.dbo.cabal_ems_event_table WHERE NpcIndex = 49";
$stmt = sqlsrv_query($conn, $sql);

$shops = [];
while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $shops[] = [
        "id" => $row["EventID"],
        "name" => thaitrans($row["Name"])
    ];
}

header('Content-Type: application/json');
echo json_encode($shops);
