-- สร้างตาราง VoucherPackages สำหรับเก็บข้อมูล Voucher Packages
-- ใช้สำหรับระบบ Voucher Shop

USE [YourDatabaseName]; -- เปลี่ยนเป็นชื่อ database ของคุณ
GO

-- ลบตารางเก่าถ้ามี (ระวัง: จะลบข้อมูลทั้งหมด)
IF OBJECT_ID('dbo.VoucherPackages', 'U') IS NOT NULL
    DROP TABLE dbo.VoucherPackages;
GO

-- สร้างตาราง VoucherPackages
CREATE TABLE dbo.VoucherPackages (
    PackageID INT IDENTITY(1,1) PRIMARY KEY,
    PackageName NVARCHAR(100) NOT NULL,
    PackageDescription NVARCHAR(255) NOT NULL,
    PackageValue INT NOT NULL,
    ItemNum INT NOT NULL,
    PackageIcon NVARCHAR(50) NOT NULL DEFAULT 'fas fa-gift',
    PackageColor NVARCHAR(20) NOT NULL DEFAULT '#ddf247',
    IsActive BIT NOT NULL DEFAULT 1,
    SortOrder INT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME2 NULL,
    CreatedBy NVARCHAR(50) NULL,
    UpdatedBy NVARCHAR(50) NULL,
    
    -- Constraints
    CONSTRAINT CK_VoucherPackages_PackageValue CHECK (PackageValue > 0),
    CONSTRAINT CK_VoucherPackages_ItemNum CHECK (ItemNum > 0),
    CONSTRAINT CK_VoucherPackages_SortOrder CHECK (SortOrder >= 0)
);
GO

-- สร้าง Index สำหรับ performance
CREATE NONCLUSTERED INDEX IX_VoucherPackages_IsActive_SortOrder 
ON dbo.VoucherPackages (IsActive, SortOrder);
GO

CREATE NONCLUSTERED INDEX IX_VoucherPackages_PackageValue 
ON dbo.VoucherPackages (PackageValue);
GO

-- เพิ่มข้อมูลเริ่มต้น
INSERT INTO dbo.VoucherPackages (
    PackageName, 
    PackageDescription, 
    PackageValue, 
    ItemNum, 
    PackageIcon, 
    PackageColor, 
    IsActive, 
    SortOrder,
    CreatedBy
) VALUES 
-- แพ็คเริ่มต้น
('Voucher Gold 1,000', 'CABAL Gift Voucher (Gold) - แพ็คเริ่มต้น', 1000, 1, 'fas fa-gift', '#ffd700', 1, 1, 'SYSTEM'),

-- แพ็คยอดนิยม
('Voucher Gold 2,000', 'CABAL Gift Voucher (Gold) - แพ็คยอดนิยม', 2000, 2, 'fas fa-gift', '#ff8c00', 1, 2, 'SYSTEM'),

-- แพ็คคุ้มค่า
('Voucher Gold 3,000', 'CABAL Gift Voucher (Gold) - แพ็คคุ้มค่า', 3000, 3, 'fas fa-gift', '#ff6347', 1, 3, 'SYSTEM'),

-- แพ็คพิเศษ
('Voucher Gold 4,000', 'CABAL Gift Voucher (Gold) - แพ็คพิเศษ', 4000, 4, 'fas fa-gift', '#dc143c', 1, 4, 'SYSTEM'),

-- แพ็คพรีเมี่ยม
('Voucher Gold 5,000', 'CABAL Gift Voucher (Gold) - แพ็คพรีเมี่ยม', 5000, 5, 'fas fa-gift', '#b22222', 1, 5, 'SYSTEM'),

-- แพ็ค VIP
('Voucher Gold 10,000', 'CABAL Gift Voucher (Gold) - แพ็คสุดพิเศษ 👑 VIP EXCLUSIVE', 10000, 6, 'fas fa-crown', '#8b0000', 1, 6, 'SYSTEM');
GO

-- สร้าง Stored Procedure สำหรับดึงข้อมูล Active Packages
CREATE PROCEDURE sp_GetActiveVoucherPackages
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        PackageID,
        PackageName,
        PackageDescription,
        PackageValue,
        ItemNum,
        PackageIcon,
        PackageColor,
        IsActive,
        SortOrder,
        CreatedDate
    FROM dbo.VoucherPackages 
    WHERE IsActive = 1 
    ORDER BY SortOrder ASC, PackageValue ASC;
END;
GO

-- สร้าง Stored Procedure สำหรับจัดการ Packages (CRUD)
CREATE PROCEDURE sp_ManageVoucherPackage
    @Action NVARCHAR(10), -- 'SELECT', 'INSERT', 'UPDATE', 'DELETE'
    @PackageID INT = NULL,
    @PackageName NVARCHAR(100) = NULL,
    @PackageDescription NVARCHAR(255) = NULL,
    @PackageValue INT = NULL,
    @ItemNum INT = NULL,
    @PackageIcon NVARCHAR(50) = NULL,
    @PackageColor NVARCHAR(20) = NULL,
    @IsActive BIT = NULL,
    @SortOrder INT = NULL,
    @UpdatedBy NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @Action = 'SELECT'
    BEGIN
        IF @PackageID IS NOT NULL
            SELECT * FROM dbo.VoucherPackages WHERE PackageID = @PackageID;
        ELSE
            SELECT * FROM dbo.VoucherPackages ORDER BY SortOrder ASC, PackageValue ASC;
    END
    
    ELSE IF @Action = 'INSERT'
    BEGIN
        INSERT INTO dbo.VoucherPackages (
            PackageName, PackageDescription, PackageValue, ItemNum, 
            PackageIcon, PackageColor, IsActive, SortOrder, CreatedBy
        ) VALUES (
            @PackageName, @PackageDescription, @PackageValue, @ItemNum,
            ISNULL(@PackageIcon, 'fas fa-gift'), ISNULL(@PackageColor, '#ddf247'), 
            ISNULL(@IsActive, 1), ISNULL(@SortOrder, 0), @UpdatedBy
        );
        
        SELECT SCOPE_IDENTITY() AS NewPackageID;
    END
    
    ELSE IF @Action = 'UPDATE'
    BEGIN
        UPDATE dbo.VoucherPackages 
        SET 
            PackageName = ISNULL(@PackageName, PackageName),
            PackageDescription = ISNULL(@PackageDescription, PackageDescription),
            PackageValue = ISNULL(@PackageValue, PackageValue),
            ItemNum = ISNULL(@ItemNum, ItemNum),
            PackageIcon = ISNULL(@PackageIcon, PackageIcon),
            PackageColor = ISNULL(@PackageColor, PackageColor),
            IsActive = ISNULL(@IsActive, IsActive),
            SortOrder = ISNULL(@SortOrder, SortOrder),
            UpdatedDate = GETDATE(),
            UpdatedBy = @UpdatedBy
        WHERE PackageID = @PackageID;
        
        SELECT @@ROWCOUNT AS RowsAffected;
    END
    
    ELSE IF @Action = 'DELETE'
    BEGIN
        -- Soft delete (set IsActive = 0)
        UPDATE dbo.VoucherPackages 
        SET IsActive = 0, UpdatedDate = GETDATE(), UpdatedBy = @UpdatedBy
        WHERE PackageID = @PackageID;
        
        SELECT @@ROWCOUNT AS RowsAffected;
    END
END;
GO

-- สร้าง View สำหรับดูข้อมูล Active Packages
CREATE VIEW vw_ActiveVoucherPackages
AS
SELECT 
    PackageID,
    PackageName,
    PackageDescription,
    PackageValue,
    ItemNum,
    PackageIcon,
    PackageColor,
    SortOrder,
    CreatedDate,
    CASE 
        WHEN PackageValue >= 10000 THEN 'VIP'
        WHEN PackageValue >= 5000 THEN 'Premium'
        WHEN PackageValue >= 3000 THEN 'Special'
        ELSE 'Standard'
    END AS PackageType
FROM dbo.VoucherPackages 
WHERE IsActive = 1;
GO

-- Grant permissions (ปรับตามระบบ permission ของคุณ)
-- GRANT SELECT, INSERT, UPDATE ON dbo.VoucherPackages TO [YourWebUser];
-- GRANT EXECUTE ON sp_GetActiveVoucherPackages TO [YourWebUser];
-- GRANT EXECUTE ON sp_ManageVoucherPackage TO [YourWebUser];
-- GRANT SELECT ON vw_ActiveVoucherPackages TO [YourWebUser];

PRINT 'VoucherPackages table และ stored procedures สร้างเสร็จสิ้น!';
PRINT 'ข้อมูลเริ่มต้น 6 packages ถูกเพิ่มแล้ว';
PRINT 'ใช้ sp_GetActiveVoucherPackages เพื่อดึงข้อมูล packages ที่ active';
PRINT 'ใช้ sp_ManageVoucherPackage เพื่อจัดการข้อมูล packages';
GO
