<?php
session_start();
if (!isset($_SESSION['userLogin'])) {
    exit("❌ ไม่มีการ login");
}

require_once("../../../_app/dbinfo.inc.php");
require_once("../../../_app/general_config.inc.php");

require_once("../../../_app/php/zpanel.class.php");
$zpanel = new zpanel();

// userLogged class
require_once("../../../_app/php/userLogin.class.php");
$userLogin = new userLogged();

// ตรวจสอบประเภทฟอร์ม
$formType = $_POST['form_type'] ?? 'event';

if ($formType === 'npcscript') {
    // ฟอร์มจากฟอร์มที่ 2: อัปเดต Script1 ของ NPC
    $EventID = filter_var($_POST['npc_event_id'] ?? 0, FILTER_VALIDATE_INT);
    $npcName = trim($_POST['npc_script_name'] ?? '');

    if (!$EventID || empty($npcName)) {
        exit("❌ ข้อมูล NPC Script ไม่ถูกต้อง");
    }

    $win874_npc = @iconv("UTF-8", "Windows-874//IGNORE", $npcName);
    if ($win874_npc === false || strlen($win874_npc) === 0) {
        exit("❌ แปลงรหัส NPC Script ล้มเหลว");
    }

    $sql = "UPDATE " . DATABASE_EDATA . ".dbo.cabal_ems_event_npcscript_table 
            SET Script1 = ? 
            WHERE EventID = ?";
    $stmt = sqlsrv_prepare($conn, $sql, [
        [$win874_npc, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_STREAM(SQLSRV_ENC_BINARY), SQLSRV_SQLTYPE_VARBINARY(strlen($win874_npc))],
        [$EventID, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT]
    ]);

    if (!$stmt || !sqlsrv_execute($stmt)) {
        exit("❌ อัปเดต Script ล้มเหลว อาจเกิดจากข้อมูลซ้ำหรือข้อผิดพลาดในการเชื่อมต่อ");
    }

    echo "✅ อัปเดต Script1 เรียบร้อยแล้ว";
    exit;
} else {
    // ฟอร์มหลัก: อัปเดตชื่อ Event
    $EventID = filter_var($_POST['Eventidx'] ?? 0, FILTER_VALIDATE_INT);
    $input_name = trim($_POST['name_thai'] ?? '');

    if (!$EventID || empty($input_name)) {
        exit("❌ ข้อมูลไม่ครบหรือไม่ถูกต้อง");
    }

    $win874_bytes = @iconv("UTF-8", "Windows-874//IGNORE", $input_name);
    if ($win874_bytes === false || strlen($win874_bytes) === 0) {
        exit("❌ แปลงรหัสผิดพลาด");
    }

    $sql = "UPDATE EventData.dbo.cabal_ems_event_table SET Name = ? WHERE EventID = ?";
    $stmt = sqlsrv_prepare($conn, $sql, [
        [$win874_bytes, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_STREAM(SQLSRV_ENC_BINARY), SQLSRV_SQLTYPE_VARBINARY(strlen($win874_bytes))],
        [$EventID, SQLSRV_PARAM_IN, SQLSRV_PHPTYPE_INT]
    ]);

    if (!$stmt || !sqlsrv_execute($stmt)) {
        exit("❌ เกิดข้อผิดพลาดในการอัปเดตข้อมูล ชื่ออาจจะช้ำกับคนอื่น");
    }

    echo "✅ อัปเดตชื่อเรียบร้อยแล้ว";
    exit;
}
?>