<aside class="page-sidebar">
    <div class="page-logo">
        <a href="<?php echo HOME_WITH_PATH; ?>"
            class="page-logo-link press-scale-down d-flex align-items-center position-relative">
            <img src="assets/img/logo.png" alt="ASP.NET Core 3.1" aria-roledescription="logo">
            <span class="page-logo-text mr-1"><?php echo $zpanel->getConfigByValue('title', 'value', $conn) ?></span>
        </a>
    </div>
    <nav id="js-primary-nav" class="primary-nav" role="navigation">
        <div class="nav-filter">
            <div class="position-relative">
                <input type="text" id="nav_filter_input" placeholder="Filter menu" class="form-control" tabindex="0">
                <a href="#" onclick="return false;" class="btn-primary btn-search-close js-waves-off"
                    data-action="toggle" data-class="list-filter-active" data-target=".page-sidebar">
                    <i class="fal fa-chevron-up"></i>
                </a>
            </div>
        </div>
        <div class="info-card">
            <img src="<?php if (!$userLogin->recUserInfo('url', $conn)) {
												echo 'home/images/user.png';
											} else {
												echo 'http://'.$userLogin->recUserInfo('url', $conn);
											} ?>" class="profile-image rounded-circle" alt="<?php echo $userLogin->recUserAccount('ID', $conn); ?>">
            <div class="info-card-text">
                <a href="#" class="d-flex align-items-center text-white">
                    <span
                        class="text-truncate text-truncate-sm d-inline-block"><?php echo $userLogin->recUserAccount('ID', $conn); ?></span>
                </a>
                <span class="d-inline-block text-truncate text-truncate-sm"><?php if ($userLogin->recUserAccount('IsDeveloper', $conn) == '0') {
												echo 'User';
											} else {
												echo 'Administrator';
											} ?></span>
            </div>
            <img src="assets/img/card-backgrounds/cover-5-lg.png" class="cover" alt="cover">
            <a href="#" onclick="return false;" class="pull-trigger-btn" data-action="toggle"
                data-class="list-filter-active" data-target=".page-sidebar" data-focus="nav_filter_input">
                <i class="fal fa-angle-down"></i>
            </a>
        </div>

        <ul id="js-nav-menu" class="nav-menu">
            <li class="<?php if (!isset($getURL)) {
                                            echo 'active';
                                    } ?>"><a href="<?php echo HOME_WITH_PATH; ?>">
                    <i class="fal fa-home"></i>
                    <span class="nav-link-text"><?php echo M_HOME; ?></span></a>
            </li>

            <?php if ($userLogin->recUserAccount('IsDeveloper', $conn)) { ?>
            <?php if ($userLogin->recUserPerm($conn, 'management', 'menu')) { ?>

            <li class="nav-title"><?php echo TL_GAMEMASTER; ?>
                <span class="badge badge-primary ml-2 countvoucher">0</span>
                <span class="badge badge-warning ml-2 countmailitem">0</span>
                <span class="badge badge-success ml-2 countreward">0</span>
            </li>

            <li <?php if (in_array('manager_account', $finalURL)) {
                                        echo 'class="active open"';
                                } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMACCOUNT; ?>"
                    data-filter-tags=" <?php echo SM_GMACCOUNT; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMACCOUNT; ?>">
                        <?php echo SM_GMACCOUNT; ?></span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('manage-account', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_account/manage-account" data-filter-tags="<?php echo SM_ACCOUNT; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_ACCOUNT; ?></span>
                        </a>
                    </li>
                </ul>
            </li>
            <li <?php if (in_array('manager_charecter', $finalURL)) {
                                        echo 'class="active open"';
                                } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMPLAYER; ?>"
                    data-filter-tags=" <?php echo SM_GMPLAYER; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMPLAYER; ?>">
                        <?php echo SM_GMPLAYER; ?></span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('manage-player', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player" data-filter-tags="สร้างตัวล่าสุด">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">ตัวละครทั้งหมด</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-lastlogin', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-lastlogin" data-filter-tags="เข้าเกมส์ล่าสุด">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions">เข้าเกมส์ล่าสุด</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-online', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-online" data-filter-tags="ผู้เล่นออนไลน์ทั่งหมด">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions">ผู้เล่นออนไลน์ทั่งหมด</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-banned', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-banned" data-filter-tags="ผู้เล่นออนไลน์ทั่งหมด">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">โดนแบนทั้งหมด</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-offline-na', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-offline-na"
                            data-filter-tags="<?php echo SM_CHARECTER_NA; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_CHARECTER_NA; ?></span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-offline-ca', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-offline-ca"
                            data-filter-tags="<?php echo SM_CHARECTER_CA; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_CHARECTER_CA; ?></span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-offline-po', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-offline-po"
                            data-filter-tags="<?php echo SM_CHARECTER_PO; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_CHARECTER_PO; ?></span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-player-war', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_charecter/manage-player-war"
                            data-filter-tags="<?php echo SM_CHARECTER_WAR; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_CHARECTER_WAR; ?></span>
                        </a>
                    </li>
                </ul>
            </li>
            <li <?php if (in_array('manager_game', $finalURL)) {
                                        echo 'class="active open"';
                                } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMMANAGER; ?>"
                    data-filter-tags=" <?php echo SM_GMMANAGER; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMMANAGER; ?>">
                        <?php echo SM_GMMANAGER; ?></span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('manage-item', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=manager_game/manage-item"
                            data-filter-tags="<?php echo SM_ITEMMANAGER; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts"><?php echo SM_ITEMMANAGER; ?></span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-pets', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game/manage-pets" data-filter-tags="<?php echo SM_PETMANAGER; ?>">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions"><?php echo SM_PETMANAGER; ?></span>
                        </a>
                    </li>

                    <li <?php
                                    if (in_array('manage-voucher-item', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game/manage-voucher-item" data-filter-tags="จัดการแลกบัตรแคส">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions">จัดการแลกบัตรแคส</span>
                            <span class="dl-ref label bg-primary-500 ml-2 countvoucher">0</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-time-item', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game/manage-time-item" data-filter-tags="จัดการเมลล์ไอเท็มแลกแคส">
                            <span class="nav-link-text"
                                data-i18n="nav.solution_overview_editions">จัดการเมลล์ไอเท็ม</span>
                            <span class="dl-ref label bg-primary-500 ml-2 countmailitem">0</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('manage-reward', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game/manage-reward" data-filter-tags="จัดการ Reward">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">จัดการ
                                Reward</span>
                            <span class="dl-ref label bg-primary-500 ml-2 countreward">0</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li <?php if (in_array('npc_shopeditor', $finalURL)) {
                                        echo 'class="active open"';
                                } ?>>
                <a href="javascript:void(0);" title="จัดการ npc"
                    data-filter-tags=" จัดการ npc">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.จัดการ npc">
                       Npc & Shop Systems</span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('rewardpoint-system', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=npc_shopeditor/rewardpoint-system"
                            data-filter-tags="จัดการไอเท็มระบบสะสมแต้ม">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts">Reward point System</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('npc_yui-Systems', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=npc_shopeditor/npc_yui-Systems"
                            data-filter-tags="จัดการไอเท็มระบบสะสมแต้ม">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts">Npc Yui Systems</span>
                        </a>
                    </li>
                                        <li <?php
                                    if (in_array('npc_cashShop-system', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=npc_shopeditor/npc_cashShop-system"
                            data-filter-tags="จัดการไอเท็มระบบสะสมแต้ม">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts">Cash Shop Editor</span>
                        </a>
                    </li>
                                        <li <?php
                                    if (in_array('npc_shop_editor', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=npc_shopeditor/npc_shop_editor"
                            data-filter-tags="จัดการไอเท็มระบบสะสมแต้ม">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts">NPC Shop Editor</span>
                        </a>
                    </li>
                                                            <li <?php
                                    if (in_array('npc_editor-system', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=npc_shopeditor/npc_yui-system_beta"
                            data-filter-tags="จัดการไอเท็มระบบสะสมแต้ม">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts">npc Yui Systems (Beta)</span>
                        </a>
                    </li>
                </ul>
            </li>


            <li class="<?php if (in_array('manager_game_history', $finalURL)) {
                                   echo ' active open';
                        } ?>">
                <a href="javascript:void(0);" data-filter-tags="ui components">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.ui_components">Management History</span>
                </a>
                <ul>

                    <li <?php
                                    if (in_array('see-guild', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game_history/see-guild" data-filter-tags="Manager Guild">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">History
                                Guild</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-rename', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game_history/see-rename" data-filter-tags="Manager เปลียนชื่อ">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">History
                                เปลียนชื่อ</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-deletechar', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=manager_game_history/see-deletechar"
                            data-filter-tags="Manager Recover Char">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">Manager Recover
                                Char</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-split-iteminventory', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=manager_game_history/see-split-iteminventory"
                            data-filter-tags="Manager Recover Char">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">SPLIT ITEM
                                INVENTORY</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-split-itemEquipment', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=manager_game_history/see-split-itemEquipment"
                            data-filter-tags="Manager Recover Char">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">SPLIT ITEM
                                EQUIPMENT</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-split-iteminwarehouse', $finalURL)) {
                                        echo ' class=" active"';
                                } ?>><a href="?url=manager_game_history/see-split-iteminwarehouse"
                            data-filter-tags="Manager Recover Char">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">SPLIT ITEM
                                WAREHOUSE</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-mycashitem', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game_history/see-mycashitem" data-filter-tags="History Cashinvent">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">History
                                Cashinvent</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-donate', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game_history/see-donate" data-filter-tags="History เติมเงิน">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">History
                                เติมเงิน</span>
                        </a>
                    </li>
                    <li <?php
                                    if (in_array('see-rps', $finalURL)) {
                                        echo ' class=" active"';
                                        } ?>>
                        <a href="?url=manager_game_history/see-rps" data-filter-tags="History RP-SHOP">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">History
                                RP-SHOP</span>
                        </a>
                    </li>

                </ul>
            </li>

            <li class="nav-title">DEV MASTER
                <span class="badge badge-warning ml-2 countdonate">0</span>
            </li>
            <li class="<?php if (in_array('manager_store', $finalURL)) {
                                   echo ' active open';
                        } ?>">
                <a href="javascript:void(0);" data-filter-tags="ui components">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.ui_components">ระบบเติมเงิน</span>
                </a>
                <ul>

                    <li <?php if (in_array('manage-donate', $finalURL)) {
                                        echo ' class="active"';
                                } ?>>
                        <a href="?url=manager_store/manage-donate" data-filter-tags="ui components">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">ตรวจสอบการ
                                Donate</span>
                            <span class="dl-ref label bg-primary-500 ml-2 countdonate">0</span>
                        </a>
                    </li>
                    <?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
                    <li <?php if (in_array('manage-adddonate', $finalURL)) {
                                        echo ' class="active"';
                                } ?>>
                        <a href="?url=manager_store/manage-adddonate" data-filter-tags="ui components">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_accordions">แก้ไขกการเติมเงิน</span>
                        </a>
                    </li>
                    <?php } ?>
                </ul>
            </li>


            <li <?php if (in_array('manager_mail', $finalURL)) {
                                        echo 'class="active open"';
                                } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMMAILMANAGER; ?>"
                    data-filter-tags=" <?php echo SM_GMMAILMANAGER; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMMAILMANAGER; ?>">
                        <?php echo SM_GMMAILMANAGER; ?></span>
                </a>
                <ul>

                    <li <?php if (in_array('see-mailsend', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_mail/see-mailsend" title="ตรวจสอบส่งเมลล์"
                            data-filter-tags="ตรวจสอบส่งเมลล์">
                            <span class="nav-link-text" data-i18n="nav.utilities_sendmail">ตรวจสอบส่งเมลล์</span>
                        </a>
                    </li>
                    <li <?php if (in_array('see-mailReceiver', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_mail/see-mailReceiver" title="ตรวจสอบรับเมลล์"
                            data-filter-tags="ตรวจสอบรับเมลล์">
                            <span class="nav-link-text" data-i18n="nav.utilities_receivermail">ตรวจสอบรับเมลล์</span>
                        </a>
                    </li>
                    <li <?php if (in_array('see-mailDelete', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_mail/see-mailDelete" title="ตรวจสอบรับเมลล์"
                            data-filter-tags="ตรวจสอบรับเมลล์">
                            <span class="nav-link-text" data-i18n="nav.utilities_deletemail">ตรวจสอบลบเมลล์</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li <?php if (in_array('manager_war', $finalURL)) {
                               echo 'class="active open"';
                     } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMCTMANAGERWAR; ?>"
                    data-filter-tags=" <?php echo SM_GMCTMANAGERWAR; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMCTMANAGERWAR; ?>">
                        <?php echo SM_GMCTMANAGERWAR; ?></span>
                </a>
                <ul>
                    <li <?php if (in_array('manage-warpoint', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_war/manage-warpoint" title="ตรวจสอบส่งเมลล์"
                            data-filter-tags="ตรวจสอบ Bringer">
                            <span class="nav-link-text" data-i18n="nav.utilities_sendmail">ตรวจสอบคะแนนวอ</span>
                        </a>
                    </li>
                    <li <?php if (in_array('manage-bringer', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_war/manage-bringer" title="ตรวจสอบส่งเมลล์"
                            data-filter-tags="ตรวจสอบ Bringer">
                            <span class="nav-link-text" data-i18n="nav.utilities_sendmail">ตรวจสอบ
                                Bringer</span>
                        </a>
                    </li>
                    <li <?php if (in_array('see-mailReceiver', $finalURL)) {
                                echo ' class="active"';
                        } ?>>
                        <a href="?url=manager_war/manage-Forcecalibur" title="ตรวจสอบรับเมลล์"
                            data-filter-tags="ตรวจสอบ Forcecalibur">
                            <span class="nav-link-text" data-i18n="nav.utilities_receivermail">ตรวจสอบ
                                Forcecalibur</span>
                        </a>
                    </li>
                </ul>
            </li>

            <li <?php if (in_array('manager_content', $finalURL)) {
                               echo 'class="active open"';
                     } ?>>
                <a href="javascript:void(0);" title="<?php echo SM_GMCTMANAGERCT; ?>"
                    data-filter-tags=" <?php echo SM_GMCTMANAGERCT; ?>">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.<?php echo SM_GMCTMANAGERCT; ?>">
                        <?php echo SM_GMCTMANAGERCT; ?></span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('manage-facebook-share', $finalURL)) {
                                            echo ' class=" active';
                                        } ?>>
                        <a href="?url=manager_content/manage-facebook-share" data-filter-tags="จัดการระบบเติมเงิน">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">จัดการกิจกรรม
                                Share</span>
                        </a>
                    <li <?php
                                    if (in_array('manage-thailotto', $finalURL)) {
                                            echo ' class=" active';
                                        } ?>>
                        <a href="?url=manager_content/manage-thailotto" data-filter-tags="จัดการกิจกรรม war">
                            <span class="nav-link-text" data-i18n="nav.solution_overview_editions">จัดการกิจกรรม
                                ทายหวย</span>
                        </a>
                    </li>
                </ul>
            </li>

            <?php } ?>

            <?php if ($userLogin->recUserPerm($conn, 'web_admin', 'menu')) { ?>
            <li class="nav-title"><?php echo TL_ADMIN_AND_DEVALOPER; ?></li>
            <li class="<?php
                                     if (in_array('web-admin', $finalURL)) {
                                        echo ' active open';
                                    } ?>">
                <a href="javascript:void(0);" data-filter-tags="ui components">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text" data-i18n="nav.ui_components"><?php echo M_ADMINISTRATION; ?></span>
                </a>
                <ul>
                    <li <?php
                                    if (in_array('configuration', $finalURL)) {
                                        echo ' class="active"';
                                } ?>><a href="?url=web-admin/configuration" data-filter-tags="ui components alerts">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_alerts"><?php echo SM_WEBCONFIGURATION; ?></span>
                        </a>
                    </li>

                    <li <?php
                                    if (in_array('team', $finalURL)) {
                                        echo ' class="active"';
                                } ?>>
                        <a href="?url=web-admin/team" title="<?php echo SM_TEAM; ?>"
                            data-filter-tags="ui components accordions">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_accordions"><?php echo SM_TEAM; ?></span>
                        </a>
                    </li>

                    <li <?php
                                    if (in_array('web-log', $finalURL)) {
                                        echo ' class="active"';
                                } ?>>
                        <a href="?url=web-admin/web-log" title="<?php echo SM_WEBLOG; ?>"
                            data-filter-tags="ui components accordions">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_accordions"><?php echo SM_WEBLOG; ?></span>
                        </a>
                    </li>

                    <li <?php
                                    if (in_array('downloads', $finalURL)) {
                                        echo ' class="active"';
                                } ?>>
                        <a href="?url=web-admin/downloads" title="<?php echo SM_DOWNLOADFIGURATION; ?>"
                            data-filter-tags="ui components accordions">
                            <span class="nav-link-text"
                                data-i18n="nav.ui_components_accordions"><?php echo SM_DOWNLOADFIGURATION; ?></span>
                        </a>
                    </li>
                </ul>
            </li>
            <?php } ?>
            <?php if ($userLogin->recUserPerm($conn, 'server_admin', 'menu')) { ?>
            <li class="nav-title"><?php echo TL_SERVER; ?></li>
            <li class="<?php
                    if (in_array('server-admin', $finalURL)) {
                        echo ' active open';
                    } ?>">
                <a href="javascript:void(0);" data-filter-tags="ui components">
                    <i class="fal fa-server"></i>
                    <span class="nav-link-text"
                        data-i18n="nav.ui_components"><?php echo M_SERVER_ADMINISTRATION; ?></span>
                </a>
                <ul>
                    <li <?php
                            if (in_array('manager-linuxufw-account', $finalURL)) {
                                echo ' class="active"';
                            } ?>><a href="?url=server-admin/manager-linuxufw-account"
                            data-filter-tags="ui components alerts">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">จัดการ ให้สิทธิ
                                account</span>
                        </a>
                    </li>
                    <li <?php
                            if (in_array('manager-linuxssh', $finalURL)) {
                                echo ' class="active"';
                            } ?>><a href="?url=server-admin/manager-linuxssh" data-filter-tags="ui components alerts">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">จัดการ Linux Server</span>
                        </a>
                    </li>
                    <li <?php
                            if (in_array('manager-linuxfirewall', $finalURL)) {
                                echo ' class="active"';
                            } ?>><a href="?url=server-admin/manager-linuxfirewall"
                            data-filter-tags="ui components alerts">
                            <span class="nav-link-text" data-i18n="nav.ui_components_alerts">จัดการ Firewall</span>
                        </a>
                    </li>

                </ul>
            </li>
            <?php }
            } //} ?>
        </ul>
        <div class="filter-message js-filter-message bg-success-600"></div>
    </nav>

    <div class="nav-footer shadow-top">
        <a href="#" onclick="return false;" data-action="toggle" data-class="nav-function-minify"
            class="hidden-md-down">
            <i class="fal fa-angle-right"></i>
            <i class="fal fa-angle-right"></i>
        </a>
        <ul class="list-table m-auto nav-footer-buttons">
            <li>
                <a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Chat logs">
                    <i class="fal fa-comments"></i>
                </a>
            </li>
            <li>
                <a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Support Chat">
                    <i class="fal fa-life-ring"></i>
                </a>
            </li>
            <li>
                <a href="javascript:void(0);" data-toggle="tooltip" data-placement="top" title="Make a call">
                    <i class="fal fa-phone"></i>
                </a>
            </li>
        </ul>
    </div>
</aside>