﻿<?php $user->restrictionUser(true, $conn); ?>
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager Recover Char<span class="fw-300"><i>กู้ข้อมูลตัวละคร</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table id="datatables-default" class="table table-bordered table-hover table-striped w-100">
                            <thead>
                                <tr>
                                    <th>idx</th>
                                    <th>CharacterIdx</th>
                                    <th>Name</th>
                                    <th>LEV</th>
                                    <th>Alz</th>
                                    <th>Reputation</th>
                                    <th>deleted</th>
                                    <th><?php echo T_ACTION; ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 1000;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM  [SERVER01].[dbo].[deleted_cabal_character_table] ORDER BY deleted DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);
                      
                        foreach ($page as $row) {

                            $name = $userLogin->thaitrans($row[2]);

                            ?>
                                <tr>
                                    <td><?php echo $row[0]; ?></td>
                                    <td><?php echo $row[1]; ?></td>
                                    <td><?php echo $name; ?></td>
                                    <td><?php echo $row[3]; ?></td>
                                    <td><?php echo $row[10]; ?></td>
                                    <td><?php echo $row[26]; ?></td>
                                    <td><?php echo $row[36]; ?></td>
                                    <td>
                                        <form method="post" name="j_recover_char" action="">
                                            <div class="j_alert"></div>
                                            <input type="hidden" name="delindex" value="<?php echo $row[0]; ?>">
                                            <input type="hidden" name="charidx" value="<?php echo $row[1]; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm"><i
                                                    class="fa fa-chevron-circle-right"></i> กู้ข้อมูล</button>
                                           
                                        </form>
                                    </td>

                                </tr>
                                <?php	} ?>
                            </tbody>
                        </table>

                    </div>
                </div>

                </section>