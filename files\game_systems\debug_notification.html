<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notification System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .notification-item { border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .notification-unread { border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Debug Notification System</h1>
        
        <!-- Test Database Connection -->
        <div class="section">
            <h2>1. Test Database & Tables</h2>
            <button onclick="testDatabase()">🔗 Test Database Connection</button>
            <button onclick="testTables()">📋 Check Tables</button>
            <div id="dbResults"></div>
        </div>
        
        <!-- Test Notification Creation -->
        <div class="section">
            <h2>2. Test Notification Creation</h2>
            <button onclick="testNotificationCreation()">➕ Create Test Notification</button>
            <button onclick="testSendItem()">📤 Test Send Item (Full Process)</button>
            <div id="createResults"></div>
        </div>
        
        <!-- Test Notification API -->
        <div class="section">
            <h2>3. Test Notification API</h2>
            <div class="form-group">
                <label for="adminUsername">Admin Username:</label>
                <input type="text" id="adminUsername" value="test_admin">
            </div>
            <button onclick="loadNotifications()">📥 Load Notifications</button>
            <button onclick="getNotificationStats()">📊 Get Statistics</button>
            <button onclick="clearNotifications()">🗑️ Clear Test Notifications</button>
            <div id="apiResults"></div>
        </div>
        
        <!-- Notification Display -->
        <div class="section">
            <h2>4. Notification Display</h2>
            <div id="notificationDisplay"></div>
        </div>
        
        <!-- Real-time Test -->
        <div class="section">
            <h2>5. Real-time Test</h2>
            <button onclick="startPolling()">▶️ Start Polling</button>
            <button onclick="stopPolling()">⏹️ Stop Polling</button>
            <span id="pollingStatus">Stopped</span>
            <div id="pollingResults"></div>
        </div>
    </div>

    <script>
        let pollingInterval = null;
        
        function showResult(containerId, type, title, content) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>Time: ${new Date().toLocaleString()}</small>
            `;
            container.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testDatabase() {
            try {
                const response = await fetch('test_notification.php');
                const text = await response.text();
                
                if (response.ok) {
                    showResult('dbResults', 'success', 'Database Test Completed', 'Check the detailed results below');
                    
                    // Open results in new window
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(text);
                    newWindow.document.close();
                } else {
                    showResult('dbResults', 'error', 'Database Test Failed', `Status: ${response.status}\n${text}`);
                }
            } catch (error) {
                showResult('dbResults', 'error', 'Database Test Error', error.message);
            }
        }
        
        async function testTables() {
            try {
                const response = await fetch('test_send_item.php');
                const text = await response.text();
                
                if (response.ok) {
                    showResult('dbResults', 'info', 'Table Check Completed', 'Check the detailed results in new window');
                    
                    // Open results in new window
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(text);
                    newWindow.document.close();
                } else {
                    showResult('dbResults', 'error', 'Table Check Failed', `Status: ${response.status}\n${text}`);
                }
            } catch (error) {
                showResult('dbResults', 'error', 'Table Check Error', error.message);
            }
        }
        
        async function testNotificationCreation() {
            try {
                const testData = {
                    action: 'create',
                    send_id: Math.floor(Math.random() * 10000),
                    player_username: 'test_player_' + Date.now(),
                    item_id: 123,
                    item_code: '0000007B00000000',
                    options_code: '0000000000000000',
                    quantity: 1,
                    duration: 31,
                    send_method: 'mail',
                    status: 'sent_to_mail',
                    admin_username: document.getElementById('adminUsername').value
                };
                
                const response = await fetch('notification_system.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('createResults', 'success', 'Notification Created', JSON.stringify(result, null, 2));
                } else {
                    showResult('createResults', 'error', 'Notification Creation Failed', JSON.stringify(result, null, 2));
                }
            } catch (error) {
                showResult('createResults', 'error', 'Notification Creation Error', error.message);
            }
        }
        
        async function testSendItem() {
            try {
                const testData = {
                    playerUsername: 'test_player_' + Date.now(),
                    itemCode: '0000007B00000000',
                    optionsCode: '0000000000000000',
                    quantity: 1,
                    duration: 31,
                    sendMethod: 'mail',
                    adminUsername: document.getElementById('adminUsername').value
                };
                
                showResult('createResults', 'info', 'Sending Item...', JSON.stringify(testData, null, 2));
                
                const response = await fetch('send_item.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('createResults', 'success', 'Item Sent Successfully', JSON.stringify(result, null, 2));
                    
                    // Auto-load notifications after successful send
                    setTimeout(() => {
                        loadNotifications();
                    }, 2000);
                } else {
                    showResult('createResults', 'error', 'Item Send Failed', JSON.stringify(result, null, 2));
                }
            } catch (error) {
                showResult('createResults', 'error', 'Item Send Error', error.message);
            }
        }
        
        async function loadNotifications() {
            try {
                const admin = document.getElementById('adminUsername').value;
                const response = await fetch(`notification_system.php?admin=${admin}&limit=10`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('apiResults', 'success', 'Notifications Loaded', `Found ${result.count} notifications`);
                    displayNotifications(result.notifications);
                } else {
                    showResult('apiResults', 'error', 'Load Notifications Failed', JSON.stringify(result, null, 2));
                }
            } catch (error) {
                showResult('apiResults', 'error', 'Load Notifications Error', error.message);
            }
        }
        
        async function getNotificationStats() {
            try {
                const admin = document.getElementById('adminUsername').value;
                const response = await fetch(`notification_system.php?admin=${admin}&stats=true`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('apiResults', 'info', 'Notification Statistics', JSON.stringify(result.stats, null, 2));
                } else {
                    showResult('apiResults', 'error', 'Get Statistics Failed', JSON.stringify(result, null, 2));
                }
            } catch (error) {
                showResult('apiResults', 'error', 'Get Statistics Error', error.message);
            }
        }
        
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationDisplay');
            container.innerHTML = '<h3>Recent Notifications:</h3>';
            
            if (notifications.length === 0) {
                container.innerHTML += '<p>No notifications found.</p>';
                return;
            }
            
            notifications.forEach(notification => {
                const div = document.createElement('div');
                div.className = `notification-item ${!notification.is_read ? 'notification-unread' : ''}`;
                div.innerHTML = `
                    <strong>${notification.title}</strong>
                    <p>${notification.message}</p>
                    <small>
                        Type: ${notification.type} | 
                        Priority: ${notification.priority} | 
                        Created: ${notification.created_at} |
                        Read: ${notification.is_read ? 'Yes' : 'No'}
                    </small>
                    ${notification.details ? `<pre>${JSON.stringify(JSON.parse(notification.details), null, 2)}</pre>` : ''}
                `;
                container.appendChild(div);
            });
        }
        
        function startPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
            
            document.getElementById('pollingStatus').textContent = 'Running...';
            
            pollingInterval = setInterval(async () => {
                try {
                    const admin = document.getElementById('adminUsername').value;
                    const response = await fetch(`notification_system.php?admin=${admin}&limit=5&unread_only=true`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const container = document.getElementById('pollingResults');
                        container.innerHTML = `
                            <p>Last poll: ${new Date().toLocaleString()}</p>
                            <p>Unread notifications: ${result.count}</p>
                        `;
                        
                        if (result.count > 0) {
                            displayNotifications(result.notifications);
                        }
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                }
            }, 10000); // Poll every 10 seconds
        }
        
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
            document.getElementById('pollingStatus').textContent = 'Stopped';
        }
        
        async function clearNotifications() {
            if (!confirm('Clear all test notifications?')) {
                return;
            }
            
            // This would require a special endpoint to clear test notifications
            showResult('apiResults', 'info', 'Clear Notifications', 'This feature would need to be implemented in the API');
        }
        
        // Auto-load notifications on page load
        window.addEventListener('load', function() {
            showResult('apiResults', 'info', 'Page Loaded', 'Ready to test notification system');
        });
    </script>
</body>
</html>
