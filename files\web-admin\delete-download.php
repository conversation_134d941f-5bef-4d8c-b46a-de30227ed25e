<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn); ?>

<header class="page-header page-header-left-inline-breadcrumb">
    <h2 class="font-weight-bold text-6">ยืนยันการลบข้อมูลดาวโหลด</h2>
    <div class="right-wrapper">
        <ol class="breadcrumbs">
            <li><span>ลบข้อมูลดาวโหลดที่ไม่ได้ใช้งาน</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fas fa-chevron-left"></i></a>
    </div>
</header>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getIDx = filter_input(INPUT_GET, 'downid', FILTER_VALIDATE_INT);
                $getBan = $_GET['delete'];

                // If there is a variable and delete is not empty then execute the function
                if (isset($getBan) && !(empty($getBan))) {
                    if ($getBan == "wait") {
						echo '<a href="?url=web-admin/downloads" class="btn btn-default" style="margin: 5px 0;">Return to download page</a></p>';
                        echo '<p class="text-red text-bolder">Are you sure you want to Delete this ?</p>';
                        echo '<a href="?url=web-admin/delete-download&downid=' . $getIDx . '&delete=true" class="btn btn-danger">Yes, Delete</a>';
                    } else if ($getBan == "true") {
                        $selectChar = "SELECT * FROM WEB_Downloads WHERE id = '$getIDx'";
                        $selectCharQuery = sqlsrv_query($conn, $selectChar, array());
                        $selectCharFetch = sqlsrv_fetch_array($selectCharQuery, SQLSRV_FETCH_ASSOC);

                        $banPlayer = "DELETE WEB_Downloads WHERE id = ?";
                        $banPlayerParam = array($selectCharFetch[id]);
                        $banPlayerQuery = sqlsrv_query($conn, $banPlayer, $banPlayerParam);
                        if (sqlsrv_rows_affected($banPlayerQuery)) {
                            $returnSuccess = S_ACCOUNT_BANNED;
                            echo '<a href="?url=web-admin/downloads" class="btn btn-default" style="margin: 5px 0;">Return to download page</a>';
                        } else {
                            $returnWarning = 'ไม่มีข้อมูลที่ต้องการลบ';
                        }
                   
                    }else if ($getBan != "wait" || $getBan != "true") {
                        $returnError = 'ส่งค่าไปยังตัวแปรไม่ถูกต้อง!';
                    }
                }else{

                    $selectPlrAccount = "SELECT * FROM WEB_Downloads WHERE id ='$getIDx'";
                    $selectPlrAccountParam = array();
                    $selectPlrAccountOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectPlrAccountQuery = sqlsrv_query($conn, $selectPlrAccount, $selectPlrAccountParam, $selectPlrAccountOpt);
                    $selectPlrAccountRows = sqlsrv_num_rows($selectPlrAccountQuery);
                    if ($selectPlrAccountRows) {
						echo '<a href="?url=web-admin/downloads" class="btn btn-default" style="margin: 5px 0;">Return to download page</a></p>';
						echo '<p class="text-red text-bolder">Are you sure you want to Delete this ?</p>';
                        echo '<a href="?url=web-admin/delete-download&downid=' . $getIDx . '&delete=true" class="btn btn-danger">Yes, Delete</a>';     
                    } else {
                        $returnWarning = 'ไม่สามารถค้นหาข้อมูลดาวโหลดนี้ได้ กลับไปยังหน้า การจัดการดาวโหลด';
                    }
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>