<style>
.profile-image {
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3);
    /* Optional: Adds a subtle outer glow */
}

.status-indicator {
    /* Adjust positioning as needed */
    position: absolute;
    bottom: 8px;
    /* Distance from bottom */
    right: 8px;
    /* Distance from right */
    width: 25px;
    /* Slightly larger for detail page */
    height: 25px;
    border-radius: 50%;
    border: 3px solid #fff;
    /* White border for contrast, thicker */
}

.width-120 {
    min-width: 120px;
}

.grid {
    display: grid;
    grid-template-columns: repeat(8, 0.01fr);
    gap: 10px;
    margin-top: 10px;
}

.slot {
    background-color: #1f1e1e8c;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    width: 110px;
    height: 110px;
    text-align: center;
    line-height: 1.1;
    padding: 1px;
    margin: 2px;
    border: 1px solid #555;
    border-radius: 8px;
    cursor: pointer;
}

.slot.empty {
    background: rgb(177, 177, 177);
    color: #888;
    opacity: 0.5;
}

.label {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 16px;
    color: rgb(82, 101, 114);
    border-radius: 4px;
    padding: 2px 6px;
    margin-top: 2px;
    margin-bottom: 4px;
    display: inline-block;
}

.itemindex {
    color: #27ae60;
    border-radius: 4px;
    padding: 2px 6px;
    margin-bottom: 2px;
    font-weight: 600;
    display: inline-block;
}

.option {
    color: #e67e22;
    border-radius: 4px;
    padding: 2px 6px;
    margin-bottom: 2px;
    font-weight: 600;
    display: inline-block;
}

#hexAll {
    width: 100%;
    height: 100px;
    margin-top: 30px;
    font-family: monospace;
}

.tab-header {
    margin-top: 20px;
}

</style>



<?php if ($userLogin->recUserPerm($conn, 'dev_masster', 'menu')) { ?>
           <div class="card shadow-lg mb-4 rounded-lg overflow-hidden">
            <div class="card-header bg-warning text-dark py-3">
                <h5 class="mb-0"><i class="fas fa-boxes mr-2"></i>คลัง (Inventory View)</h5>
            </div>
            <div class="card-body">
                <script>
                // ส่ง itemNameMap ไป JS
                window.itemNameMap = <?php echo json_encode($itemNameMap, JSON_UNESCAPED_UNICODE); ?>;
                // Path รูปภาพ slot (relative)
                window.itemImageBasePath = 'assets/images/items/';
                </script>
                <div class="panel-content mb-4">
                    <ul class="nav nav-tabs" id="tabNav" role="tablist"></ul>
                    <div class="tab-content border border-top-0 p-3" id="tabs"></div>
                    <h3 class="mt-4">🔢 HexData รวมทั้งหมด</h3>
                    <div style="position:relative;">
                        <textarea id="hexAll" class="form-control mb-2" readonly></textarea>
                        <div id="hexAllOverlay" style="pointer-events:none;position:absolute;left:0;top:0;width:100%;height:100%;border-radius:4px;display:none;"></div>
                    </div>
                    <button id="updateInventoryBtn" class="btn btn-primary mb-4">Update Inventory</button>
                </div>
            </div>
        </div>               
<?php } ?>

      
<!-- Modal for editing slot -->
<div class="modal fade" id="editSlotModal" tabindex="-1" role="dialog" aria-labelledby="editSlotModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content bg-dark text-light">
            <form id="editSlotForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSlotModalLabel">แก้ไข Slot <span id="editSlotLabel"></span></h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close"
                        id="closeModalBtn">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="editSlotNumber" name="slot">
                    <div class="form-group">
                        <label for="editItemIndex">ItemIndex:</label>
                        <input type="number" class="form-control" id="editItemIndex" name="itemIndex">
                    </div>
                    <div class="form-group">
                        <label for="editSerial">Serial:</label>
                        <input type="number" class="form-control" id="editSerial" name="serial">
                    </div>
                    <div class="form-group">
                        <label for="editOption">Option:</label>
                        <input type="number" class="form-control" id="editOption" name="option">
                    </div>
                    <div class="form-group">
                        <label for="editPeriod">Period:</label>
                        <input type="number" class="form-control" id="editPeriod" name="period">
                    </div>
                    <div class="form-group">
                        <label for="editKindIdx">KindIdx:</label>
                        <input type="number" class="form-control" id="editKindIdx" name="kindIdx">
                    </div>
                    <div class="form-group">
                        <label for="modalHexPreview">HexData</label>
                        <textarea class="form-control" id="modalHexPreview" readonly></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelEditSlot"
                        data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-success">บันทึก</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateHex(slot, data) {
    const slotHex = slot.toString(16).padStart(4, '0');
    const slotLE = slotHex.match(/.{2}/g).reverse().join('');
    return data.HexData.slice(0, 48) + slotLE + data.HexData.slice(52);
}

function bigIntToLittleEndianHexJS(value, lengthBytes) {
    let hex = value.toString(16);
    hex = hex.padStart(lengthBytes * 2, '0');
    let bytes = hex.match(/.{2}/g);
    return bytes.reverse().join('');
}

// Track last hex for change detection
let lastHexAll = '';

function rebuildHexAll(gridData) {
    const hexAll = [];
    Object.keys(gridData).sort((a, b) => parseInt(a) - parseInt(b)).forEach(slot => {
        if (gridData[slot]) hexAll.push(gridData[slot].HexData);
    });
    const hexStr = '0x' + hexAll.join('').toUpperCase();
    $('#hexAll').val(hexStr);
    // Highlight if changed
    if (lastHexAll && hexStr !== lastHexAll) {
        $('#hexAllOverlay').show().css({
            background: '#fffbe0',
            border: '2px solid #ffc107',
            boxShadow: '0 0 8px #ffe082'
        });
    } else {
        $('#hexAllOverlay').hide();
    }



}

// รับ charIdx จาก PHP ตัวแปรฝั่ง server (CharacterIdx)
// รับ charIdx จาก PHP ตัวแปรฝั่ง server (CharacterIdx)
var charIdx = <?php echo isset($characterData['CharacterIdx']) ? (int)$characterData['CharacterIdx'] : 0; ?>;

$.getJSON('_app/php/inventory_data.php?ajax=1&charIdx=' + encodeURIComponent(charIdx), function(data) {
    const gridData = {};
    Object.keys(data).forEach(s => gridData[s] = data[s]);

    const tabNav = $('#tabNav');
    const tabs = $('#tabs');
    const slotsPerTab = 64;
    const maxSlot = Math.max(...Object.keys(gridData).map(n => parseInt(n)));
    const tabCount = Math.ceil((maxSlot + 1) / slotsPerTab);

    for (let t = 0; t < tabCount; t++) {
        const start = t * slotsPerTab;
        const end = start + slotsPerTab - 1;
        const tabId = 'tab_' + t;
        // Bootstrap 4 nav-tabs
        tabNav.append(`
            <li class="nav-item">
                <a class="nav-link${t === 0 ? ' active' : ''}" id="${tabId}-tab" data-toggle="tab" href="#${tabId}" role="tab" aria-controls="${tabId}" aria-selected="${t === 0 ? 'true' : 'false'}">เก็บของ ${t+1}</a>
            </li>
        `);

        const grid = $('<div class="grid"></div>');
        for (let s = start; s <= end; s++) {
            const slotDiv = $('<div class="slot" data-slot="' + s + '" id="slot_' + s + '"></div>');
            slotDiv.append(`<div class="label">${s}</div>`);
            let itemIndex = gridData[s] && gridData[s].itemIndex ? gridData[s].itemIndex : null;
            if (itemIndex && !isNaN(itemIndex) && parseInt(itemIndex) > 0) {
                // Set background image for slot (zoomed and centered)
                let imgPath = window.itemImageBasePath + itemIndex + '.png';
                slotDiv.css({
                    backgroundImage: `url('${imgPath}')`,
                    backgroundSize: '300% 300%', // zoom in
                    backgroundPosition: 'center center',
                    backgroundRepeat: 'no-repeat',
                    backgroundColor: '#222'
                });
            } else {
                // Default/fallback background
                slotDiv.css({
                    background: '#b1b1b1',
                    color: '#888',
                    opacity: 0.5
                });
                slotDiv.addClass('empty');
            }
            if (gridData[s]) {
                let itemName = '';
                if (window.itemNameMap && window.itemNameMap[itemIndex]) {
                    itemName = window.itemNameMap[itemIndex];
                }
                slotDiv.append(`<div class="itemindex">${itemIndex}</div>`);
                slotDiv.append(`<div class="option">${gridData[s].Option}</div>`);
                slotDiv.append(`<div class="itename">${itemName}</div>`);
                if (gridData[s]._changed) {
                    slotDiv.addClass('slot-changed');
                }
            } else {
                slotDiv.append(`<div>Emtry</div>`);
            }
            grid.append(slotDiv);
        }
        // Bootstrap 4 tab-pane
        tabs.append(
            `<div class="tab-pane fade${t === 0 ? ' show active' : ''}" id="${tabId}" role="tabpanel" aria-labelledby="${tabId}-tab">${grid.prop('outerHTML')}</div>`
            );
    }

    // Bootstrap 4 tabs: handled by Bootstrap JS, no need for manual activation
    rebuildHexAll(gridData);

    $('.slot').draggable({
        helper: "clone",
        revert: "invalid"
    });

    $('.slot').droppable({
        accept: ".slot",
        drop: function(event, ui) {
            const fromSlot = $(ui.draggable).data('slot');
            const toSlot = $(this).data('slot');

            const temp = gridData[fromSlot];
            gridData[fromSlot] = gridData[toSlot];
            gridData[toSlot] = temp;

            if (gridData[fromSlot]) gridData[fromSlot].HexData = updateHex(fromSlot, gridData[fromSlot]);
            if (gridData[toSlot]) gridData[toSlot].HexData = updateHex(toSlot, gridData[toSlot]);

            // Mark both slots as changed
            if (gridData[fromSlot]) gridData[fromSlot]._changed = true;
            if (gridData[toSlot]) gridData[toSlot]._changed = true;

            // Reload slot content and background using direct DOM manipulation
            const reload = (slot) => {
                const div = document.getElementById('slot_' + slot);
                if (!div) return;
                div.innerHTML = '';
                div.classList.remove('empty', 'slot-changed');
                // Reset all background/image styles
                div.style.backgroundImage = '';
                div.style.backgroundSize = '';
                div.style.backgroundPosition = '';
                div.style.backgroundRepeat = '';
                div.style.backgroundColor = '';
                div.style.background = '';
                div.style.color = '';
                div.style.opacity = '';
                // Label
                const label = document.createElement('div');
                label.className = 'label';
                label.textContent = slot;
                div.appendChild(label);
                let itemIndex = gridData[slot] && gridData[slot].itemIndex ? gridData[slot].itemIndex : null;
                if (itemIndex && !isNaN(itemIndex) && parseInt(itemIndex) > 0) {
                    let imgPath = window.itemImageBasePath + itemIndex + '.png';
                    div.style.backgroundImage = `url('${imgPath}')`;
                    div.style.backgroundSize = '300% 300%';
                    div.style.backgroundPosition = 'center center';
                    div.style.backgroundRepeat = 'no-repeat';
                    div.style.backgroundColor = '#222';
                } else {
                    div.style.background = '#b1b1b1';
                    div.style.color = '#888';
                    div.style.opacity = 0.5;
                    div.classList.add('empty');
                }
                if (gridData[slot]) {
                    let itemName = '';
                    if (window.itemNameMap && window.itemNameMap[itemIndex]) {
                        itemName = window.itemNameMap[itemIndex];
                    }
                    const itemIndexDiv = document.createElement('div');
                    itemIndexDiv.className = 'itemindex';
                    itemIndexDiv.textContent = gridData[slot].itemIndex;
                    div.appendChild(itemIndexDiv);
                    const optionDiv = document.createElement('div');
                    optionDiv.className = 'option';
                    optionDiv.textContent = gridData[slot].Option;
                    div.appendChild(optionDiv);
                    const itemNameDiv = document.createElement('div');
                    itemNameDiv.className = 'itename';
                    itemNameDiv.textContent = itemName;
                    div.appendChild(itemNameDiv);
                    if (gridData[slot]._changed) div.classList.add('slot-changed');
                } else {
                    const emptyDiv = document.createElement('div');
                    emptyDiv.textContent = 'Emtry';
                    div.appendChild(emptyDiv);
                }
            };

            reload(fromSlot);
            reload(toSlot);
            rebuildHexAll(gridData);
        }
    });


    // คลิกขวา (contextmenu) ที่ slot เพื่อแก้ไข
    $(document).on('contextmenu', '.slot', function(e) {
        e.preventDefault();
        const slot = $(this).data('slot');
        const data = gridData[slot] || {
            itemIndex: '',
            Serial: '',
            Option: '',
            Period: '',
            KindIdx: ''
        };
        $('#editSlotNumber').val(slot);
        $('#editSlotLabel').text(slot);
        $('#editItemIndex').val(data.itemIndex || '0');
        $('#editSerial').val(data.Serial || '0');
        $('#editOption').val(data.Option || '0');
        $('#editPeriod').val(data.Period || '0');
        $('#editKindIdx').val(data.KindIdx || '0');

        // แสดง/ซ่อนปุ่มลบไอเท็มใน modal footer
        let removeBtn = $('#editSlotModal .modal-footer .remove-item-btn');
        if (gridData[slot]) {
            if (removeBtn.length === 0) {
                $('<button type="button" class="btn btn-danger remove-item-btn" style="margin-right:auto;">ลบไอเท็ม</button>')
                    .prependTo($('#editSlotModal .modal-footer'));
            }
            $('#editSlotModal .remove-item-btn').data('slot', slot).show();
        } else {
            if (removeBtn.length > 0) removeBtn.hide();
        }

        $('#editSlotModal').modal('show');

        // ฟังก์ชัน encode itemIndex ลง KindIdx (เหมือน PHP)
        function setItemIndexInKindIdx(kindIdx, itemIndex) {
            // ลบ bit itemIndex เดิมออก แล้วใส่ใหม่
            return (kindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        }

        // ฟังก์ชันอัพเดท HexData แบบเรียลไทม์
        function updateModalHex() {
            let KindIdx = parseInt($('#editKindIdx').val()) || 0;
            const itemIndex = parseInt($('#editItemIndex').val()) || 0;
            const Serial = parseInt($('#editSerial').val()) || 0;
            const Option = parseInt($('#editOption').val()) || 0;
            const slotNum = parseInt($('#editSlotNumber').val()) || 0;
            const Period = parseInt($('#editPeriod').val()) || 0;
            // encode itemIndex ลง KindIdx
            KindIdx = setItemIndexInKindIdx(KindIdx, itemIndex);
            const hexData =
                bigIntToLittleEndianHexJS(KindIdx, 8) +
                bigIntToLittleEndianHexJS(Serial, 8) +
                bigIntToLittleEndianHexJS(Option, 8) +
                bigIntToLittleEndianHexJS(slotNum, 2) +
                bigIntToLittleEndianHexJS(Period, 4);
            $('#modalHexPreview').text(hexData.toUpperCase());
        }
        updateModalHex();
        $('#editSlotForm input').off('input.modalhex').on('input.modalhex', updateModalHex);
    });


    // ปิด modal เมื่อกดปุ่มยกเลิก หรือปุ่ม X (Bootstrap 4)
    $('#cancelEditSlot, #closeModalBtn').on('click', function() {
        $('#editSlotModal').modal('hide');
    });



    $('#editSlotForm').on('submit', function(e) {
        e.preventDefault();

        const slot = parseInt($('#editSlotNumber').val());
        const itemIndex = parseInt($('#editItemIndex').val()) || 0;
        const Serial = parseInt($('#editSerial').val()) || 0;
        const Option = parseInt($('#editOption').val()) || 0;
        const Period = parseInt($('#editPeriod').val()) || 0;
        let KindIdx = parseInt($('#editKindIdx').val()) || 0;

        // encode itemIndex ลง KindIdx ก่อนสร้าง HexData
        KindIdx = (KindIdx & ~0x6000fff) | (itemIndex & 0x6000fff);
        const hexData = $('#modalHexPreview').text();

        gridData[slot] = {
            KindIdx: KindIdx,
            itemIndex: itemIndex,
            Serial: Serial,
            Option: Option,
            Period: Period,
            HexData: hexData,
            _changed: true // Mark as changed
        };

        // อัพเดท UI
        const div = $('#slot_' + slot);
        div.empty().removeClass('empty slot-changed');
        div.append(`<div class="label">${slot}</div>`);
        div.append(`<div class="itemindex">${itemIndex}</div>`);
        div.append(`<div class="option">${Option}</div>`);
        if (gridData[slot]._changed) div.addClass('slot-changed');

        $('#editSlotModal').modal('hide');
        rebuildHexAll(gridData);
    });

    // ปุ่มลบไอเท็มออกจาก slot (เฉพาะใน modal)
    $(document).on('click', '#editSlotModal .remove-item-btn', function(e) {
        e.stopPropagation();
        const slot = $(this).data('slot');
        // แสดง confirm dialog ก่อนลบ
        if (confirm('คุณต้องการลบไอเท็มในช่องนี้ใช่หรือไม่?')) {
            // ลบข้อมูล slot และอัปเดท UI
            delete gridData[slot];
            const div = $('#slot_' + slot);
            div.empty().removeClass('slot-changed').addClass('empty');
            div.append(`<div class="label">${slot}</div>`);
            div.append(`<div>Emtry</div>`);
            rebuildHexAll(gridData);
            $('#editSlotModal').modal('hide');
        }
    });

    // ฟังก์ชันแปลง hex string เป็น binary (base64)
    function hexToBase64(hex) {
        return btoa(hex.match(/.{1,2}/g).map(byte => String.fromCharCode(parseInt(byte, 16))).join(''));
    }

    $('#updateInventoryBtn').on('click', function() {
        const hex = $('#hexAll').val().replace(/^0x/i, '');
        // ส่งไปอัปเดตฝั่ง PHP (base64) พร้อม charIdx
        $.ajax({
            url: '_app/php/inventory_data.php',
            method: 'POST',
            data: {
                update_inventory: 1,
                hex: hex,
                charIdx: charIdx
            },
            success: function(res) {
                alert(res);
                // ลบ highlight ทุก slot ที่ถูกแก้ไขหลังจากบันทึกสำเร็จ
                Object.keys(gridData).forEach(function(slot) {
                    if (gridData[slot] && gridData[slot]._changed) {
                        delete gridData[slot]._changed;
                        $('#slot_' + slot).removeClass('slot-changed');
                    }
                });
                // Reset hex highlight
                lastHexAll = $('#hexAll').val();
                $('#hexAllOverlay').hide();
            },
            error: function() {
                alert('Update failed');
            }
        });
    });
    // CSS สำหรับ slot ที่ถูกแก้ไข และ hexAll highlight
    $('<style>\n.slot-changed {\n  border: 2px solid #ff9800 !important;\n  box-shadow: 0 0 8px 2px #ff9800a0;\n  background: #fff3e0 !important;\n}\n#hexAllOverlay {\n  transition: background 0.3s, border 0.3s, box-shadow 0.3s;\n  z-index:2;\n}\n</style>')
        .appendTo('head');
});
</script>