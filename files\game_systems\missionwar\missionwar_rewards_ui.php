<?php
// missionwar_rewards_ui.php - UI for managing WEB_MissionWar_rewards
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8">
    <title>Mission War Rewards Management</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">Mission War Rewards Management</h2>
    <button class="btn btn-success mb-3" id="addRewardBtn">เพิ่มรางวัลใหม่</button>
    <div class="table-responsive">
        <table class="table table-bordered table-hover bg-white" id="rewardsTable">
            <thead class="thead-dark">
                <tr>
                    <th>RewardID</th>
                    <th>SlotID</th>
                    <th>ItemKindIdx</th>
                    <th>ItemOption</th>
                    <th>ItemDurationIdx</th>
                    <th>Alz</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="rewardModal" tabindex="-1" role="dialog" aria-labelledby="rewardModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="rewardModalLabel">เพิ่ม/แก้ไขรางวัล</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="rewardForm">
      <div class="modal-body">
        <input type="hidden" id="RewardID" name="RewardID">
        <div class="form-group">
            <label for="SlotID">SlotID</label>
            <input type="number" class="form-control" id="SlotID" name="SlotID" required>
        </div>
        <div class="form-group">
            <label for="ItemKindIdx">ItemKindIdx</label>
            <input type="number" class="form-control" id="ItemKindIdx" name="ItemKindIdx" required>
        </div>
        <div class="form-group">
            <label for="ItemOption">ItemOption</label>
            <input type="number" class="form-control" id="ItemOption" name="ItemOption" required>
        </div>
        <div class="form-group">
            <label for="ItemDurationIdx">ItemDurationIdx</label>
            <input type="number" class="form-control" id="ItemDurationIdx" name="ItemDurationIdx" required>
        </div>
        <div class="form-group">
            <label for="Alz">Alz</label>
            <input type="number" class="form-control" id="Alz" name="Alz" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
        <button type="submit" class="btn btn-primary">บันทึก</button>
      </div>
      </form>
    </div>
  </div>
</div>

<script>
function loadRewards() {
    $.get('missionwar_rewards.php?action=list', function(data) {
        let rewards = JSON.parse(data);
        let html = '';
        rewards.forEach(function(r) {
            html += `<tr>
                <td>${r.RewardID}</td>
                <td>${r.SlotID}</td>
                <td>${r.ItemKindIdx}</td>
                <td>${r.ItemOption}</td>
                <td>${r.ItemDurationIdx}</td>
                <td>${r.Alz}</td>
                <td>
                    <button class='btn btn-sm btn-info editBtn' data-id='${r.RewardID}'>แก้ไข</button>
                    <button class='btn btn-sm btn-danger deleteBtn' data-id='${r.RewardID}'>ลบ</button>
                </td>
            </tr>`;
        });
        $('#rewardsTable tbody').html(html);
    });
}

$(function() {
    loadRewards();
    $('#addRewardBtn').click(function() {
        $('#rewardForm')[0].reset();
        $('#RewardID').val('');
        $('#rewardModal').modal('show');
    });
    $(document).on('click', '.editBtn', function() {
        let id = $(this).data('id');
        $.get('missionwar_rewards.php?action=get&RewardID=' + id, function(data) {
            let r = JSON.parse(data);
            $('#RewardID').val(r.RewardID);
            $('#SlotID').val(r.SlotID);
            $('#ItemKindIdx').val(r.ItemKindIdx);
            $('#ItemOption').val(r.ItemOption);
            $('#ItemDurationIdx').val(r.ItemDurationIdx);
            $('#Alz').val(r.Alz);
            $('#rewardModal').modal('show');
        });
    });
    $(document).on('click', '.deleteBtn', function() {
        if(confirm('ลบข้อมูลนี้?')) {
            let id = $(this).data('id');
            $.get('missionwar_rewards.php?action=delete&RewardID=' + id, function() {
                loadRewards();
            });
        }
    });
    $('#rewardForm').submit(function(e) {
        e.preventDefault();
        let data = {
            RewardID: $('#RewardID').val(),
            SlotID: $('#SlotID').val(),
            ItemKindIdx: $('#ItemKindIdx').val(),
            ItemOption: $('#ItemOption').val(),
            ItemDurationIdx: $('#ItemDurationIdx').val(),
            Alz: $('#Alz').val()
        };
        let action = data.RewardID ? 'edit' : 'add';
        $.ajax({
            url: 'missionwar_rewards.php?action=' + action,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#rewardModal').modal('hide');
                loadRewards();
            }
        });
    });
});
</script>
</body>
</html>
