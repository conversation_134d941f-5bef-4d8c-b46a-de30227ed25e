<script>
            $(document).ready(function(){
                $('input[type=radio][name=skinchange]').change(function(){
                    $('.irs').removeClassPrefix('irs--').addClass(this.value);
                });
                var ionskin = "flat";
                $("#slotitem").ionRangeSlider( {
                    skin: ionskin,
                    min: 0,
                    max: 4,
                    from: 0,
                    step: 1, 
                    grid: true,
                    grid_num: 4,
                    grid_snap: false 
                });
           
                $('input[type=radio][name=skinchange]').change(function(){
                    $('.irs').removeClassPrefix('irs--').addClass(this.value);
                });
                var ionskin = "flat";
                $("#petsuplevel").ionRangeSlider( {
                    skin: ionskin,
                    min: 0,
                    max: 10,
                    from: 0,
                    step: 1, 
                    grid: true,
                    grid_num: 10,
                    grid_snap: false 
                });
            });
</script>
<script>
        $(document).ready(function(){
                $('#dt-iteminvent').dataTable({
                    responsive: true,
                    dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                    buttons: [
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'print',
                            text: '<i class="fal fa-print"></i>',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-default'
                        }

                    ],
                    columnDefs: [
                        {
                            targets: -1,
                            orderable: false,
                        },

                    ]

                });
            });

        $(document).ready(function() {
            $('#dt-itemlist').dataTable({
                responsive: true,
                processing: true,
                pageLength: false,
                ajax: '_data/items.json',
                deferRender: true,
                scrollY: 500,
                scrollCollapse: true,
                scroller: true,
                columnDefs: [{
                    targets: -1,
                    responsivePriority: -1,
                    orderable: false,
                }],
            });
        });
        $(document).ready(function() {
            $('#dt-monsterlist').dataTable({
                responsive: true,
                processing: true,
                pageLength: false,
                ajax: '_data/monsters.json',
                deferRender: true,
                scrollY: 500,
                scrollCollapse: true,
                scroller: true,
                columnDefs: [{
                    targets: -1,
                    responsivePriority: -1,
                    orderable: false,
                }],
            });
        });
        $(document).ready(function() {
            $('#dt-DGlist').dataTable({
                responsive: true,
                processing: true,
                pageLength: false,
                ajax: '_data/dun_world.json',
                deferRender: true,
                scrollY: 500,
                scrollCollapse: true,
                scroller: true,
                columnDefs: [{
                    targets: -1,
                    responsivePriority: -1,
                    orderable: false,
                }],
            });
        });
        $(document).ready(function() {
            $('#dt-quest').dataTable({
                responsive: true,
                processing: true,
                pageLength: false,
                ajax: '_data/quest.json',
                deferRender: true,
                scrollY: 500,
                scrollCollapse: true,
                scroller: true,
                columnDefs: [{
                    targets: -1,
                    responsivePriority: -1,
                    orderable: false,
                }],
            });
        });
    </script>
    <!-- account -->
     <script>
        $(document).ready(function() {
            var columnSet = [{
                    title: "UserNum",
                    id: "UserNum",
                    data: "UserNum",
                    placeholderMsg: "Server Generated ID",
                    type: "readonly"
                }, {
                    title: "ID",
                    id: "ID",
                    data: "ID",
                    type: "readonly"
                }, {
                    title: "Login",
                    id: "Login",
                    data: "Login",
                    type: "text"
                }, {
                    title: "LoginTime",
                    id: "LoginTime",
                    data: "LoginTime",
                    type: "text"
                }, {
                    title: "LogoutTime",
                    id: "LogoutTime",
                    data: "LogoutTime",
                    type: "number"
                }, {
                    title: "AuthType",
                    id: "AuthType",
                    data: "AuthType",
                    type: "text"
                }, {
                    title: "PlayTime",
                    id: "PlayTime",
                    data: "PlayTime",
                    type: "text"
                }, {
                    title: "LastIp",
                    id: "LastIp",
                    data: "LastIp",
                    type: "text"
                }, {
                    title: "createDate",
                    id: "createDate",
                    data: "createDate",
                    type: "text"
                }, {
                    title: "Email",
                    id: "Email",
                    data: "Email",
                    type: "text"
                }, {
                    title: "IP",
                    id: "IP",
                    data: "IP",
                    type: "text"
                }, {
                    title: "Phone_Number",
                    id: "Phone_Number",
                    data: "Phone_Number",
                    type: "text"
                }, {
                    title: "Action",
                    id: "Action",
                    data: "Action",
                    type: "text"
                }]
                /* start data table */
            var myTable = $('#dt-account-datatables').dataTable({
                dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                ajax: "_data/account_data.json",
                columns: columnSet,
                select: 'single',
                altEditor: true,
                responsive: true,
                buttons: [{
                    text: '<i class="fal fa-sync mr-1"></i> Synchronize',
                    name: 'refresh',
                    className: 'btn-primary btn-sm'
                }],
                columnDefs: [{
                    targets: 2,
                    render: function(data, type, full, meta) {
                        var badge = {
                            "1": {
                                'title': 'Online',
                                'class': 'badge-success'
                            },
                            "0": {
                                'title': 'Offline',
                                'class': 'badge-danger'
                            }
                        };
                        if (typeof badge[data] === 'undefined') {
                            return data;
                        }
                        return '<span class="badge ' + badge[data].class + ' badge-pill">' + badge[data].title + '</span>';
                    },
                },  {
                    targets: 5,
                    render: function(data, type, full, meta) {
                        var package = {
                            "0": {
                                'title': 'N/A',
                                'class': 'badge-Secondary'
                            },
                            "1": {
                                'title': 'ปรกติ',
                                'class': 'badge-success'
                            },
                            "2": {
                                'title': 'Banned',
                                'class': 'badge-danger'
                            },
                            "3": {
                                'title': 'GM',
                                'class': 'bg-warning-100 text-white'
                            }
                        };
                        if (typeof package[data] === 'undefined') {
                            return data;
                        }
                        return '<span class="badge ' + package[data].class + ' badge-pill">' + package[data].title + '</span>';
                    },
                },{
                    targets: 7,
                    render: function(data, type, full, meta) {
                        //var number = Number(data.replace(/[^0-9.-]+/g,""));
                        if (data >= 0) {
                            return '<span class="text-success fw-500">' + data + '</span>';
                        } else {
                            return '<span class="text-danger fw-500">' + data + '</span>';
                        }
                    },
                },{
                    targets: 12,
                    render: function(data, type, full, meta) {
                        //var number = Number(data.replace(/[^0-9.-]+/g,""));
                        if (data) {
                            return '<td><a href="javascript:void(0);"  class="btn btn-sm btn-icon btn-outline-primary rounded-circle shadow-0" data-toggle="dropdown" aria-expanded="false" title="More options">' +
							'<i class="fal fa-ellipsis-v"></i></a>' +
							'<div class="dropdown-menu" style="">' +
							'<a class="dropdown-item" href="?url=manager/see-player&id='+ data +'">จัดการบัญชีผู้เล่น</a>' +
							'<a class="dropdown-item" href="?url=manager/see-player&id='+ data +'">จัดการตัวละคร</a>' +
							'</div>' +
						'</div></td>';    
                        } 
                    },
                }, ],
              
            });
        });
    </script>
    <!-- CharacterIdx -->
     <script>
        $(document).ready(function() {
            var columnSet = [{
                    title: "CharacterIdx",
                    id: "CharacterIdx",
                    data: "CharacterIdx",
                    placeholderMsg: "Server Generated ID",
                    type: "readonly"
                }, {
                    title: "Name",
                    id: "Name",
                    data: "Name",
                    type: "readonly"
                }, {
                    title: "LEV",
                    id: "LEV",
                    data: "LEV",
                    type: "text"
                }, {
                    title: "STR",
                    id: "STR",
                    data: "STR",
                    type: "text"
                }, {
                    title: "DEX",
                    id: "DEX",
                    data: "DEX",
                    type: "number"
                }, {
                    title: "INT",
                    id: "INT",
                    data: "INT",
                    type: "text"
                }, {
                    title: "PNT",
                    id: "PNT",
                    data: "PNT",
                    type: "text"
                }, {
                    title: "Alz",
                    id: "Alz",
                    data: "Alz",
                    type: "text"
                }, {
                    title: "WorldIdx",
                    id: "WorldIdx",
                    data: "WorldIdx",
                    type: "text"
                }, {
                    title: "LogoutTime",
                    id: "LogoutTime",
                    data: "LogoutTime",
                    type: "text"
                }, {
                    title: "Reputation",
                    id: "Reputation",
                    data: "Reputation",
                    type: "text"
                }, {
                    title: "LoginTime",
                    id: "LoginTime",
                    data: "LoginTime",
                    type: "text"
                }, {
                    title: "PlayTime",
                    id: "PlayTime",
                    data: "PlayTime",
                    type: "text"
                }, {
                    title: "ChannelIdx",
                    id: "ChannelIdx",
                    data: "ChannelIdx",
                    type: "text"
                }, {
                    title: "Nation",
                    id: "Nation",
                    data: "Nation",
                    type: "text"
                }, {
                    title: "Login",
                    id: "Login",
                    data: "Login",
                    type: "text"
                }]
                /* start data table */
            var myTable = $('#dt-Characterdatatables').dataTable({
                dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                ajax: "_data/character_data.json",
                columns: columnSet,
                select: 'single',
                altEditor: true,
                responsive: true,
                buttons: [{
                    extend: 'selected',
                    text: '<i class="fal fa-times mr-1"></i> Delete',
                    name: 'delete',
                    className: 'btn-danger btn-sm mr-1'
                }, {
                    extend: 'selected',
                    text: '<i class="fal fa-edit mr-1"></i> Edit',
                    name: 'edit',
                    className: 'btn-primary btn-sm mr-1'
                }, {
                    text: '<i class="fal fa-sync mr-1"></i> Synchronize',
                    name: 'refresh',
                    className: 'btn-primary btn-sm'
                }],
                columnDefs: [{
                    targets: 15,
                    render: function(data, type, full, meta) {
                        var badge = {
                            "1": {
                                'title': 'Online',
                                'class': 'badge-success'
                            },
                            "0": {
                                'title': 'Offline',
                                'class': 'badge-danger'
                            }
                        };
                        if (typeof badge[data] === 'undefined') {
                            return data;
                        }
                        return '<span class="badge ' + badge[data].class + ' badge-pill">' + badge[data].title + '</span>';
                    },
                },  {
                    targets: 14,
                    render: function(data, type, full, meta) {
                        var package = {
                            "0": {
                                'title': 'N/A',
                                'class': 'badge-success'
                            },
                            "1": {
                                'title': 'Capella',
                                'class': 'badge-warning'
                            },
                            "2": {
                                'title': 'Procyon',
                                'class': 'badge-danger'
                            },
                            "3": {
                                'title': 'GM',
                                'class': 'bg-danger-100 text-white'
                            }
                        };
                        if (typeof package[data] === 'undefined') {
                            return data;
                        }
                        return '<span class="badge ' + package[data].class + ' badge-pill">' + package[data].title + '</span>';
                    },
                },{
                    targets: 7,
                    render: function(data, type, full, meta) {
                        //var number = Number(data.replace(/[^0-9.-]+/g,""));
                        if (data >= 0) {
                            return '<span class="text-success fw-500">' + data + '</span>';
                        } else {
                            return '<span class="text-danger fw-500">' + data + '</span>';
                        }
                    },
                }, ],
                /* default callback for insertion: mock webservice, always success */
                onEditRow: function(dt, rowdata, success, error) {
                   // console.log("Missing AJAX configuration for UPDATE");
                    success(rowdata);
                    Swal.fire({
                        icon: 'error',
                        title: 'ERROR',
                        text:  JSON.stringify(rowdata, null, 4),
                        timer: 5000
                    })
                    // demo only below:
                    events.prepend('<p class="text-info fw-500">' + JSON.stringify(rowdata, null, 4) + '</p>');
                },
                onDeleteRow: function(dt, rowdata, success, error) {
                    console.log("Missing AJAX configuration for DELETE");
                    success(rowdata);
                    // demo only below:
                    events.prepend('<p class="text-danger fw-500">' + JSON.stringify(rowdata, null, 4) + '</p>');
                },
            });
        });
    </script>
 <script>
        $(document).ready(function() {
            var columnSet = [{
                    title: "PetSerial",
                    id: "PetSerial",
                    data: "PetSerial",
                    placeholderMsg: "Server Generated ID",
                    type: "readonly"
                }, {
                    title: "PetId",
                    id: "PetId",
                    data: "PetId",
                    type: "text"
                }, {
                    title: "OwnerCharIdx",
                    id: "OwnerCharIdx",
                    data: "OwnerCharIdx",
                    type: "text"
                }, {
                    title: "ItemIdx",
                    id: "ItemIdx",
                    data: "ItemIdx",
                    type: "text"
                }, {
                    title: "Lev",
                    id: "Lev",
                    data: "Lev",
                    type: "number"
                }, {
                    title: "LevExp",
                    id: "LevExp",
                    data: "LevExp",
                    type: "text"
                }, {
                    title: "NickName",
                    id: "NickName",
                    data: "NickName",
                    type: "text"
                }]
                /* start data table */
            var myTable = $('#dt-petsdatatables').dataTable({
                /* check datatable buttons page for more info on how this DOM structure works */
                dom: "<'row mb-3'<'col-sm-12 col-md-6 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-6 d-flex align-items-center justify-content-end'B>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                ajax: "_data/pets_data.json",
                columns: columnSet,
                /* selecting multiple rows will not work */
                select: 'single',
                /* altEditor at work */
                altEditor: true,
                responsive: true,
                /* buttons uses classes from bootstrap, see buttons page for more details */
                buttons: [{
                    extend: 'selected',
                    text: '<i class="fal fa-times mr-1"></i> Delete',
                    name: 'delete',
                    className: 'btn-primary btn-sm mr-1'
                }, {
                    extend: 'selected',
                    text: '<i class="fal fa-edit mr-1"></i> Edit',
                    name: 'edit',
                    className: 'btn-primary btn-sm mr-1'
                }, {
                    text: '<i class="fal fa-plus mr-1"></i> Add',
                    name: 'add',
                    className: 'btn-success btn-sm mr-1'
                }, {
                    text: '<i class="fal fa-sync mr-1"></i> Synchronize',
                    name: 'refresh',
                    className: 'btn-primary btn-sm'
                }],
                columnDefs: [{
                    targets: 1,
                    render: function(data, type, full, meta) {
                        var badge = {
                            "active": {
                                'title': 'Active',
                                'class': 'badge-success'
                            },
                            "inactive": {
                                'title': 'Inactive',
                                'class': 'badge-warning'
                            },
                            "disabled": {
                                'title': 'Disabled',
                                'class': 'badge-danger'
                            },
                            "partial": {
                                'title': 'Partial',
                                'class': 'bg-danger-100 text-white'
                            }
                        };
                        if (typeof badge[data] === 'undefined') {
                            return data;
                        }
                        return '<span class="badge ' + badge[data].class + ' badge-pill">' + badge[data].title + '</span>';
                    },
                }, {
                    targets: 0,
                    type: 'currency',
                    render: function(data, type, full, meta) {
                        //var number = Number(data.replace(/[^0-9.-]+/g,""));
                        if (data >= 0) {
                            return '<span class="text-success fw-500">' + data + '</span>';
                        } else {
                            return '<span class="text-danger fw-500">' + data + '</span>';
                        }
                    },
                }, ],
                /* default callback for insertion: mock webservice, always success */
                onAddRow: function(dt, rowdata, success, error) {
                    console.log("Missing AJAX configuration for INSERT");
                    success(rowdata);
                    // demo only below:
                    events.prepend('<p class="text-success fw-500">' + JSON.stringify(rowdata, null, 4) + '</p>');
                },
                onEditRow: function(dt, rowdata, success, error) {
                    console.log("Missing AJAX configuration for UPDATE");
                    success(rowdata);
                    // demo only below:
                    events.prepend('<p class="text-info fw-500">' + JSON.stringify(rowdata, null, 4) + '</p>');
                },
                onDeleteRow: function(dt, rowdata, success, error) {
                    console.log("Missing AJAX configuration for DELETE");
                    success(rowdata);
                    // demo only below:
                    events.prepend('<p class="text-danger fw-500">' + JSON.stringify(rowdata, null, 4) + '</p>');
                },
            });
        });
    </script>
<script>
		$(document).ready(function () {
			$(function() {
				$('.select2').select2();
				$(".select2-placeholder-multiple").select2({
					placeholder: "Select State"
				}); 
				$(".js-hide-search").select2({
					minimumResultsForSearch: 1 / 0
				}); 
				$(".js-max-length").select2({
					maximumSelectionLength: 2,
					placeholder: "Select maximum 2 items"
				});
				$(".select2-placeholder").select2({
					placeholder:"Select a state",
					allowClear: true
				});
				$(".js-select2-icons").select2({
					minimumResultsForSearch: 1 / 0,
					templateResult: icon,
					templateSelection: icon,
					escapeMarkup: function (elm) {
						return elm
					}
				});
				function icon(elm) {
					elm.element;
					return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
				}
				$(".js-data-example-ajax").select2({
				  ajax: {
				    url: "_data/repositories",
				    dataType: 'json',
				    delay: 250,
				    data: function (params) {
				      return {
				        q: params.term, // search term
				        page: params.page
				      };
				    },
				    processResults: function (data, params) {
				      // parse the results into the format expected by Select2
				      // since we are using custom formatting functions we do not need to
				      // alter the remote JSON data, except to indicate that infinite
				      // scrolling can be used
				      params.page = params.page || 1;
				      return {
				        results: data.items,
				        pagination: {
				          more: (params.page * 30) < data.total_count
				        }
				      };
				    },
				    cache: true
				  },
				  placeholder: 'Search for a repository',
				  escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
				  minimumInputLength: 1,
				  templateResult: formatRepo,
				  templateSelection: formatRepoSelection
				});
				function formatRepo (repo) {
				  if (repo.loading) {
				    return repo.text;
				  }
				  var markup = "<div class='select2-result-repository clearfix d-flex'>" +
				    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
				    "<div class='select2-result-repository__meta'>" +
				      "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";
				  if (repo.description) {
				    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
				  }
				  markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
				    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
				    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
				    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
				  "</div>" +
				  "</div></div>";
				  return markup;
				}
				function formatRepoSelection (repo) {
				  return repo.full_name || repo.text;
				}
			});
		});
	</script>