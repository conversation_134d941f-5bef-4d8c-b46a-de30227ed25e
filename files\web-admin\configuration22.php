<?php

/**
| Copyright 2015
| Developed by FDEV
| All rights reserved.
| NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
if (isset($_GET['action']) && 'update' == $_GET['action']) {
 $returnSuccess = S_CONFIG_UPDATED . "   Created date is " . date("m-d-Y h:i:s");
}
?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-credit-card-front"></i> <?php echo PT_WEB_CONFIG; ?><sup class="badge badge-primary fw-500">ADDON</sup>
        <small>
        <?php echo PT_WEB_CONFIG; ?>
        </small>
    </h1>
</div>
<?php
if (isset($_POST['btn_savechange'])) {
 // get all inputs in one array
 $configForm = filter_input_array(INPUT_POST, FILTER_DEFAULT);

 // condition
 if (empty($configForm['input_title'])) {
  $returnWarning = W_EMPTY_TITLE;
 } elseif (empty($configForm['input_appname'])) {
  $returnWarning = W_EMPTY_APPNAME;
 } else {

  // date now
  $getDateNow = date('Y-m-d H:i:s');

  // update title
  $updateConfigTitle      = "UPDATE WEB_Config SET value = '$configForm[input_title]' WHERE config_name = 'title'";
  $updateConfigTitleQuery = sqlsrv_query($conn, $updateConfigTitle, array());
  $updateConfigTitleRows  = sqlsrv_rows_affected($updateConfigTitleQuery);

  // we have some updates because the table structure
  if ($updateConfigTitleRows) {

   // update description
   $updateConfigDesc      = "UPDATE WEB_Config SET value = '$configForm[input_desc]' WHERE config_name = 'description'";
   $updateConfigDescQuery = sqlsrv_query($conn, $updateConfigDesc, array());
   $updateConfigDescRows  = sqlsrv_rows_affected($updateConfigDescQuery);

   // update appname
   $updateConfigAppname      = "UPDATE WEB_Config SET value = '$configForm[input_appname]' WHERE config_name = 'app_name'";
   $updateConfigAppnameQuery = sqlsrv_query($conn, $updateConfigAppname, array());
   $updateConfigAppnameRows  = sqlsrv_rows_affected($updateConfigAppnameQuery);

   // update store config
   $updateConfigStore      = "UPDATE WEB_Config SET value = '$configForm[input_store]' WHERE config_name = 'store'";
   $updateConfigStoreQuery = sqlsrv_query($conn, $updateConfigStore, array());
   $updateConfigStoreRows  = sqlsrv_rows_affected($updateConfigStoreQuery);

   // update maintenance
   $updateConfigMaintenance      = "UPDATE WEB_Config SET value = '$configForm[input_maintenance]' WHERE config_name = 'maintenance'";
   $updateConfigMaintenanceQuery = sqlsrv_query($conn, $updateConfigMaintenance, array());
   $updateConfigMaintenanceRows  = sqlsrv_rows_affected($updateConfigMaintenanceQuery);

   // update register
   $updateConfigRegister      = "UPDATE WEB_Config SET value = '$configForm[input_register]' WHERE config_name = 'register'";
   $updateConfigRegisterQuery = sqlsrv_query($conn, $updateConfigRegister, array());
   $updateConfigRegisterRows  = sqlsrv_rows_affected($updateConfigRegisterQuery);

   // update CashGiff
   $updateConfigCashGiff      = "UPDATE WEB_Config SET value = '$configForm[input_CashGiff]' WHERE config_name = 'CashGiff'";
   $updateConfigCashGiffQuery = sqlsrv_query($conn, $updateConfigCashGiff, array());
   $updateConfigCashGiffRows  = sqlsrv_rows_affected($updateConfigCashGiffQuery);

   // update CashGiff
   $updateConfigReward      = "UPDATE WEB_Config SET value = '$configForm[input_Reward]' WHERE config_name = 'Reward'";
   $updateConfigRewardQuery = sqlsrv_query($conn, $updateConfigReward, array());
   $updateConfigRewardRows  = sqlsrv_rows_affected($updateConfigRewardQuery);

   // update CashGiff
   $updateConfigDonate      = "UPDATE WEB_Config SET value = '$configForm[input_Donate]' WHERE config_name = 'Donate'";
   $updateConfigDonateQuery = sqlsrv_query($conn, $updateConfigDonate, array());
   $updateConfigDonateRows  = sqlsrv_rows_affected($updateConfigDonateQuery);

   if ($updateConfigDonateRows) {
    // update last_update column
    $updateLastUpdate      = "UPDATE WEB_Config SET value = '$getDateNow' WHERE config_name = 'last_update'";
    $updateLastUpdateQuery = sqlsrv_query($conn, $updateLastUpdate, array());
    $updateLastUpdateRows  = sqlsrv_rows_affected($updateLastUpdateQuery);

      if (sqlsrv_rows_affected($updateLastUpdateQuery)):
         echo '<script>
              Swal.fire(
                "SUCCESS !",
                "อัพเดตระบบให้เรียบร้อยแล้ว.",
                "success"
             )   
         </script>';
        else:
            echo '<script>
              Swal.fire(
              "ERROR !",
              "Error ไม่สามารถทำรายการได้.",
            "error"
           )   
         </script>';
      endif;
   }
  } else {
   $returnError = E_FAILED_UPDATE;
  }
 }
}
?>
<?php if (isset($returnSuccess)) { ?>
<div class="alert alert-success" role="alert"><?php echo $returnSuccess; ?></div>
<?php } elseif (isset($returnWarning)) { ?>
<div class="alert alert-warning" role="alert"><?php echo $returnWarning; ?></div>
<?php } elseif (isset($returnError)) { ?>
<div class="alert alert-danger" role="alert"><?php echo $returnError; ?></div>
<?php } ?>
<!-- start: page -->

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                <?php echo T_HEADER_INFO; ?> <span class="fw-300"><i><?php echo T_HEADER_INFO_DESC; ?></i></span>
                </h2>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <form class="ecommerce-form action-buttons-fixed" method="post">
                        <div class="form-group row">
                            <label for="input_title" class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_TITLE; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <input type="text" class="form-control form-control-modern" name="input_title" id="input_title" 
                                        value="<?php echo $zpanel->getConfigByValue('title', 'value', $conn); ?>" required />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="input_desc" class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_DESC; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <input type="text" class="form-control form-control-modern" name="input_desc" id="input_desc"
                                        value="<?php echo $zpanel->getConfigByValue('description', 'value', $conn); ?>" required />
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_appname" class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_APPNAME; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <input type="text" class="form-control form-control-modern" name="input_appname"
                                        id="input_appname"
                                        value="<?php echo (!$zpanel->getConfigByValue('app_name', 'value', $conn) ? 'zPANEL' : $zpanel->getConfigByValue('app_name', 'value', $conn)); ?>" />
                                </div>
                            </div>
                        </div>
                
                        <div class="form-group row">
                            <label for="input_maintenance" class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_MAINTENANCE_MODE; ?></label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_maintenance" id="input_maintenance" >
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('maintenance', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            Activated</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('maintenance', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            Deactivated</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_store" class="col-form-label col-12 col-lg-3 form-label text-lg-right">Store Shop (ระบบ เว็บช็อป)</label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_store" id="input_store">
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('store', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            เปิด</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('store', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            ปิดระบบ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_register" class="col-form-label col-12 col-lg-3 form-label text-lg-right"><?php echo T_WEB_REGISTER; ?> (ระบบสมัครสมาชิก)</label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_register" id="input_register">
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('register', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            เปิด</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('register', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            ปิดระบบ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_CashGiff" class="col-form-label col-12 col-lg-3 form-label text-lg-right">CashCard (ระบบบัตรแคส)</label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_CashGiff" id="input_CashGiff">
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('CashGiff', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            เปิด</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('CashGiff', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            ปิดระบบ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_Reward" class="col-form-label col-12 col-lg-3 form-label text-lg-right">Reward (ระบบรีวาด)</label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_Reward" id="input_Reward">
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('Reward', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            เปิด</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('Reward', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            ปิดระบบ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="input_Donate" class="col-form-label col-12 col-lg-3 form-label text-lg-right">Donate(ระบบเติมเงิน)</label>
                            <div class="col-12 col-lg-6 demo-v-spacing-sm">
                                <div class="input-group">
                                <select class="form-control form-control-modern" name="input_Donate"  id="input_Donate">
                                        <option value="1"
                                            <?php if ($zpanel->getConfigByValue('Donate', 'value', $conn) == '1') {echo ' selected="selected"';} ?>>
                                            เปิด</option>
                                        <option value="0"
                                            <?php if ($zpanel->getConfigByValue('Donate', 'value', $conn) == '0') {echo ' selected="selected"';} ?>>
                                            ปิดระบบ</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-12 col-lg-6">
                                <button type="submit" name="btn_savechange" value=""
                                class="btn btn-primary waves-effect waves-themed"
                                data-loading-text="Loading...">
                                <i class="bx bx-save text-4 mr-2"></i> <?php echo B_SAVECHANGES; ?>
                            </button>
                            </div>
                        </div>
                    </form>
                    <!-- modal -->
                </div>
            </div>
        </div>
    </div>
</div>
