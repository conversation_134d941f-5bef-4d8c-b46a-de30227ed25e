<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_MANAGECODES; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12 well well-sm">
                <a href="?url=manager/codes" class="btn btn-default" style="margin: 5px 0;">&laquo; <?php echo B_RETURNCODEPAGE; ?></a>
                <?php
                $formCode = filter_input_array(INPUT_POST, FILTER_DEFAULT);
                if (isset($formCode) && $formCode['btn_create']) {
                    // variables access
                    $inputCode = strip_tags(trim($formCode['input_code']));
                    $inputItems = strip_tags(trim($formCode['input_items']));
                    $inputQuantity = strip_tags(trim($formCode['input_quantity']));

                    if (empty($inputCode)) {
                        $returnWarning = W_C_CODEEMPTY;
                    } else if (strlen($inputCode) < 4 || strlen($inputCode) > 35) {
                        $returnWarning = W_C_CODE_LENGHT;
                    } else if (empty($inputItems)) {
                        $returnWarning = W_C_ITEMS;
                    } else if ($inputQuantity == '-1') {
                        $returnWarning = W_C_QUANTITY;
                    } else {

                        // select codes
                        $selectCode = "SELECT * FROM WEB_Redeem_Code WHERE code = '$inputCode'";
                        $selectParam = array();
                        $selectOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectCodeQuery = sqlsrv_query($conn, $selectCode, $selectParam, $selectOption);
                        
                        if (!sqlsrv_num_rows($selectCodeQuery)) {
                            $insertCode = "INSERT INTO WEB_Redeem_Code (code, items, quantity, status) VALUES ('$inputCode', '$inputItems', '$inputQuantity', '1')";
                            $insertCodeParam = array();
                            $insertCodeQuery = sqlsrv_query($conn, $insertCode, $insertCodeParam);

                            if ($insertCodeQuery) {
                                $returnSuccess = S_CODECREATED;
                            } else {
                                $returnError = E_CODE;
                            }
                        } else {
                            $returnWarning = W_CODE_EXISTS;
                        }
                    }
                }
                ?>
                <?php if (isset($returnWarning)) { ?>
                    <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
                <?php } elseif (isset($returnSuccess)) { ?>
                    <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
                <?php } ?>
                <form role="form" method="post" enctype="multipart/form-data">  
                    <div class="form-group">
                        <label for="input_code" class="text-red"><h3>Code</h3></label>
                        <input type="text" name="input_code" class="form-control" placeholder="Code here ..." value="<?php if (isset($inputEmail)) echo $inputEmail; ?>">
                    </div>

                    <div class="form-group">
                        <label for="input_code" class="text-red"><h3>Items</h3></label>
                        <input type="text" name="input_items" class="form-control" placeholder="Items separated by comma ...">
                        <span class="help-block text-red">E.g: 101088, 101087 etc ... max. 16 items</span>
                    </div>
                    <div class="form-group">
                        <label for="input_code" class="text-red"><h3>Quantity</h3></label>
                        <select class="form-control" name="input_quantity">
                            <option value="-1"><?php echo T_SELECT_QUANTITY; ?></option>
                            <option value="1">1 Item</option>
                            <option value="5">5 Items</option>
                            <option value="10">10 Items</option>
                            <option value="20">20 Items</option>
                        </select>
                        <span class="help-block text-red">Quantity of items is the quantity of each item</span>
                    </div>

                    <input type="submit" name="btn_create" class="btn btn-success btn-block" value="<?php echo B_CREATE; ?>">
                </form>
            </div>
        </div>
    </div>
</div>
