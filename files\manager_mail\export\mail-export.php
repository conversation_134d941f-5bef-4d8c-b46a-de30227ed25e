<?php
require('../../../_app/dbinfo.inc.php');
require('../../../_app/general_config.inc.php');

// Simple authentication check
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$export_type = $_GET['type'] ?? 'csv';
$data_type = $_GET['data'] ?? 'statistics';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');

try {
    switch ($data_type) {
        case 'statistics':
            exportStatistics($conn, $export_type, $date_from, $date_to);
            break;
            
        case 'received_mails':
            exportReceivedMails($conn, $export_type, $date_from, $date_to);
            break;
            
        case 'sent_mails':
            exportSentMails($conn, $export_type, $date_from, $date_to);
            break;
            
        case 'deleted_mails':
            exportDeletedMails($conn, $export_type, $date_from, $date_to);
            break;
            
        default:
            throw new Exception('Invalid data type');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo "Error: " . $e->getMessage();
}

function exportStatistics($conn, $format, $date_from, $date_to) {
    $data = [];
    
    // Daily statistics
    $sql = "SELECT 
                CAST(DeliveryTime AS DATE) as date,
                COUNT(*) as total_mails,
                SUM(CASE WHEN Alz > 0 THEN 1 ELSE 0 END) as mails_with_alz,
                SUM(CASE WHEN ItemKindIdx > 0 THEN 1 ELSE 0 END) as mails_with_items,
                SUM(Alz) as total_alz,
                AVG(Alz) as avg_alz,
                COUNT(DISTINCT ReceiverCharIdx) as unique_receivers,
                COUNT(DISTINCT SenderCharName) as unique_senders
            FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
            WHERE CAST(DeliveryTime AS DATE) BETWEEN ? AND ?
            GROUP BY CAST(DeliveryTime AS DATE)
            ORDER BY date";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $data[] = [
                'Date' => $row['date']->format('Y-m-d'),
                'Total Mails' => $row['total_mails'],
                'Mails with Alz' => $row['mails_with_alz'],
                'Mails with Items' => $row['mails_with_items'],
                'Total Alz' => $row['total_alz'],
                'Average Alz' => round($row['avg_alz'], 2),
                'Unique Receivers' => $row['unique_receivers'],
                'Unique Senders' => $row['unique_senders']
            ];
        }
    }
    
    outputData($data, $format, 'mail_statistics_' . $date_from . '_to_' . $date_to);
}

function exportReceivedMails($conn, $format, $date_from, $date_to) {
    $data = [];
    
    $sql = "SELECT 
                ReceivedMailID,
                ReceiverCharIdx,
                DeliveryTime,
                Alz,
                ItemKindIdx,
                ItemOption,
                ItemDurationIdx,
                IsReceivedItem,
                IsReceivedAlz,
                SenderCharIdx,
                SenderCharName,
                Title,
                Content
            FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table 
            WHERE CAST(DeliveryTime AS DATE) BETWEEN ? AND ?
            ORDER BY DeliveryTime DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $data[] = [
                'Mail ID' => $row['ReceivedMailID'],
                'Receiver' => $row['ReceiverCharIdx'],
                'Delivery Time' => $row['DeliveryTime']->format('Y-m-d H:i:s'),
                'Alz' => $row['Alz'],
                'Item Code' => $row['ItemKindIdx'],
                'Item Option' => $row['ItemOption'],
                'Item Duration' => $row['ItemDurationIdx'],
                'Item Received' => $row['IsReceivedItem'] ? 'Yes' : 'No',
                'Alz Received' => $row['IsReceivedAlz'] ? 'Yes' : 'No',
                'Sender ID' => $row['SenderCharIdx'],
                'Sender Name' => $row['SenderCharName'],
                'Title' => $row['Title'],
                'Content' => $row['Content']
            ];
        }
    }
    
    outputData($data, $format, 'received_mails_' . $date_from . '_to_' . $date_to);
}

function exportSentMails($conn, $format, $date_from, $date_to) {
    $data = [];
    
    $sql = "SELECT 
                SenderCharIdx,
                SentMailID,
                DeliveryTime,
                Alz,
                ItemKindIdx,
                ItemOption,
                ItemDurationIdx,
                ReceiverCharName,
                Title,
                Content
            FROM [".DATABASE_SV."].[dbo].cabal_mail_sent_table 
            WHERE CAST(DeliveryTime AS DATE) BETWEEN ? AND ?
            ORDER BY DeliveryTime DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $data[] = [
                'Sender ID' => $row['SenderCharIdx'],
                'Mail ID' => $row['SentMailID'],
                'Delivery Time' => $row['DeliveryTime']->format('Y-m-d H:i:s'),
                'Alz' => $row['Alz'],
                'Item Code' => $row['ItemKindIdx'],
                'Item Option' => $row['ItemOption'],
                'Item Duration' => $row['ItemDurationIdx'],
                'Receiver Name' => $row['ReceiverCharName'],
                'Title' => $row['Title'],
                'Content' => $row['Content']
            ];
        }
    }
    
    outputData($data, $format, 'sent_mails_' . $date_from . '_to_' . $date_to);
}

function exportDeletedMails($conn, $format, $date_from, $date_to) {
    $data = [];
    
    // Deleted received mails
    $sql = "SELECT 
                'Received' as mail_type,
                DateDeleted,
                MailID,
                ReceiverCharIdx,
                Alz,
                ItemKindIdx,
                ItemOption,
                ItemDurationIdx,
                SenderCharName,
                Title,
                Content
            FROM [".DATABASE_WEB."].[dbo].cabal_mail_received_deleted_logs 
            WHERE CAST(DateDeleted AS DATE) BETWEEN ? AND ?
            ORDER BY DateDeleted DESC";
    
    $stmt = sqlsrv_prepare($conn, $sql, array(&$date_from, &$date_to));
    if ($stmt && sqlsrv_execute($stmt)) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $data[] = [
                'Mail Type' => $row['mail_type'],
                'Delete Date' => $row['DateDeleted']->format('Y-m-d H:i:s'),
                'Mail ID' => $row['MailID'],
                'Character ID' => $row['ReceiverCharIdx'],
                'Alz' => $row['Alz'],
                'Item Code' => $row['ItemKindIdx'],
                'Item Option' => $row['ItemOption'],
                'Item Duration' => $row['ItemDurationIdx'],
                'Sender Name' => $row['SenderCharName'],
                'Title' => $row['Title'],
                'Content' => $row['Content']
            ];
        }
    }
    
    outputData($data, $format, 'deleted_mails_' . $date_from . '_to_' . $date_to);
}

function outputData($data, $format, $filename) {
    if (empty($data)) {
        throw new Exception('No data found for the specified date range');
    }
    
    switch ($format) {
        case 'csv':
            outputCSV($data, $filename);
            break;
            
        case 'json':
            outputJSON($data, $filename);
            break;
            
        case 'excel':
            outputExcel($data, $filename);
            break;
            
        default:
            throw new Exception('Invalid export format');
    }
}

function outputCSV($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    if (!empty($data)) {
        fputcsv($output, array_keys($data[0]));
        
        // Data rows
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
}

function outputJSON($data, $filename) {
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    echo json_encode([
        'export_date' => date('Y-m-d H:i:s'),
        'filename' => $filename,
        'total_records' => count($data),
        'data' => $data
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function outputExcel($data, $filename) {
    // For Excel export, we'll use HTML table format that Excel can read
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    echo '<html><head><meta charset="utf-8"></head><body>';
    echo '<table border="1">';
    
    if (!empty($data)) {
        // Headers
        echo '<tr>';
        foreach (array_keys($data[0]) as $header) {
            echo '<th>' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr>';
        
        // Data rows
        foreach ($data as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . htmlspecialchars($cell) . '</td>';
            }
            echo '</tr>';
        }
    }
    
    echo '</table>';
    echo '</body></html>';
}
?>
