<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-calculator"></i> ทดสอบการคำนวณ Honor Class จาก Reputation Points
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">คำนวณ Honor Class จาก Reputation Points ตาม XML Config</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> ระบบคำนวณ Honor Class จาก Reputation Points ตาม XML config แล้ว
                </div>
                
                <h5>📊 Honor Class Ranges ตาม XML Config</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="thead-dark">
                            <tr>
                                <th>Class</th>
                                <th>Min Reputation</th>
                                <th>Promote Reputation</th>
                                <th>Range</th>
                                <th>สี</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-secondary">
                                <td><span class="badge badge-secondary">Class 0</span></td>
                                <td>-10,000</td>
                                <td>10,000</td>
                                <td>-10,000 ถึง 9,999</td>
                                <td><span class="badge badge-secondary">เทา</span></td>
                            </tr>
                            <tr class="table-success">
                                <td><span class="badge badge-success">Class 1</span></td>
                                <td>10,000</td>
                                <td>20,000</td>
                                <td>10,000 ถึง 19,999</td>
                                <td><span class="badge badge-success">เขียว</span></td>
                            </tr>
                            <tr class="table-success">
                                <td><span class="badge badge-success">Class 5</span></td>
                                <td>150,000</td>
                                <td>300,000</td>
                                <td>150,000 ถึง 299,999</td>
                                <td><span class="badge badge-success">เขียว</span></td>
                            </tr>
                            <tr class="table-info">
                                <td><span class="badge badge-info">Class 10</span></td>
                                <td>5,000,000</td>
                                <td>10,000,000</td>
                                <td>5M ถึง 9.99M</td>
                                <td><span class="badge badge-info">น้ำเงิน</span></td>
                            </tr>
                            <tr class="table-warning">
                                <td><span class="badge badge-warning">Class 15</span></td>
                                <td>150,000,000</td>
                                <td>250,000,000</td>
                                <td>150M ถึง 249.99M</td>
                                <td><span class="badge badge-warning">เหลือง</span></td>
                            </tr>
                            <tr class="table-danger">
                                <td><span class="badge badge-danger">Class 19</span></td>
                                <td>1,400,000,000</td>
                                <td>2,000,000,000</td>
                                <td>1.4B ถึง 1.99B</td>
                                <td><span class="badge badge-danger">แดง</span></td>
                            </tr>
                            <tr class="table-dark">
                                <td><span class="badge badge-dark">Class 20</span></td>
                                <td>2,000,000,000</td>
                                <td>4,000,000,001</td>
                                <td>2B+</td>
                                <td><span class="badge badge-dark">ดำ</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">🧮 ทดสอบการคำนวณ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">ตัวอย่างการคำนวณ</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                function calculateHonorClass($reputation) {
                                    if ($reputation === null || $reputation < -10000) return ['class' => 'No Class', 'value' => 0];
                                    elseif ($reputation >= -10000 && $reputation < 10000) return ['class' => 'Class 0', 'value' => 0];
                                    elseif ($reputation >= 10000 && $reputation < 20000) return ['class' => 'Class 1', 'value' => 1];
                                    elseif ($reputation >= 20000 && $reputation < 40000) return ['class' => 'Class 2', 'value' => 2];
                                    elseif ($reputation >= 40000 && $reputation < 80000) return ['class' => 'Class 3', 'value' => 3];
                                    elseif ($reputation >= 80000 && $reputation < 150000) return ['class' => 'Class 4', 'value' => 4];
                                    elseif ($reputation >= 150000 && $reputation < 300000) return ['class' => 'Class 5', 'value' => 5];
                                    elseif ($reputation >= 300000 && $reputation < 600000) return ['class' => 'Class 6', 'value' => 6];
                                    elseif ($reputation >= 600000 && $reputation < 1200000) return ['class' => 'Class 7', 'value' => 7];
                                    elseif ($reputation >= 1200000 && $reputation < 2500000) return ['class' => 'Class 8', 'value' => 8];
                                    elseif ($reputation >= 2500000 && $reputation < 5000000) return ['class' => 'Class 9', 'value' => 9];
                                    elseif ($reputation >= 5000000 && $reputation < 10000000) return ['class' => 'Class 10', 'value' => 10];
                                    elseif ($reputation >= 10000000 && $reputation < 20000000) return ['class' => 'Class 11', 'value' => 11];
                                    elseif ($reputation >= 20000000 && $reputation < 40000000) return ['class' => 'Class 12', 'value' => 12];
                                    elseif ($reputation >= 40000000 && $reputation < 80000000) return ['class' => 'Class 13', 'value' => 13];
                                    elseif ($reputation >= 80000000 && $reputation < 150000000) return ['class' => 'Class 14', 'value' => 14];
                                    elseif ($reputation >= 150000000 && $reputation < 250000000) return ['class' => 'Class 15', 'value' => 15];
                                    elseif ($reputation >= 250000000 && $reputation < 500000000) return ['class' => 'Class 16', 'value' => 16];
                                    elseif ($reputation >= 500000000 && $reputation < 900000000) return ['class' => 'Class 17', 'value' => 17];
                                    elseif ($reputation >= 900000000 && $reputation < 1400000000) return ['class' => 'Class 18', 'value' => 18];
                                    elseif ($reputation >= 1400000000 && $reputation < 2000000000) return ['class' => 'Class 19', 'value' => 19];
                                    elseif ($reputation >= 2000000000) return ['class' => 'Class 20', 'value' => 20];
                                    else return ['class' => 'No Class', 'value' => 0];
                                }
                                
                                $testCases = [
                                    0,
                                    5000,
                                    15000,
                                    100000,
                                    500000,
                                    7500000,
                                    50000000,
                                    1000000000,
                                    2500000000
                                ];
                                
                                echo "<ul class='list-unstyled'>";
                                foreach ($testCases as $points) {
                                    $result = calculateHonorClass($points);
                                    $colorClass = '';
                                    if ($result['value'] == 0) $colorClass = 'secondary';
                                    elseif ($result['value'] >= 1 && $result['value'] <= 5) $colorClass = 'success';
                                    elseif ($result['value'] >= 6 && $result['value'] <= 10) $colorClass = 'info';
                                    elseif ($result['value'] >= 11 && $result['value'] <= 15) $colorClass = 'warning';
                                    elseif ($result['value'] >= 16 && $result['value'] <= 19) $colorClass = 'danger';
                                    elseif ($result['value'] >= 20) $colorClass = 'dark';
                                    
                                    echo "<li>";
                                    echo "<strong>" . number_format($points) . " points</strong> → ";
                                    echo "<span class='badge badge-{$colorClass}'>" . $result['class'] . "</span>";
                                    echo "</li>";
                                }
                                echo "</ul>";
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">ข้อมูลจริงจาก Database</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $sql = "SELECT 
                                                MIN(Reputation) as min_rep,
                                                MAX(Reputation) as max_rep,
                                                AVG(CAST(Reputation AS BIGINT)) as avg_rep,
                                                COUNT(*) as total_count,
                                                COUNT(CASE WHEN Reputation IS NULL THEN 1 END) as null_count
                                            FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                                    
                                    $result = sqlsrv_query($conn, $sql);
                                    
                                    if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                        echo "<ul class='list-unstyled'>";
                                        echo "<li><strong>ตัวละครทั้งหมด:</strong> " . number_format($row['total_count']) . "</li>";
                                        echo "<li><strong>NULL Values:</strong> " . number_format($row['null_count']) . "</li>";
                                        echo "<li><strong>Min Reputation:</strong> " . number_format($row['min_rep']) . "</li>";
                                        echo "<li><strong>Max Reputation:</strong> " . number_format($row['max_rep']) . "</li>";
                                        echo "<li><strong>Avg Reputation:</strong> " . number_format($row['avg_rep']) . "</li>";
                                        echo "</ul>";
                                        
                                        // คำนวณ Honor Class ของค่าเฉลี่ย
                                        $avgResult = calculateHonorClass($row['avg_rep']);
                                        echo "<div class='alert alert-info mt-3'>";
                                        echo "<strong>Honor Class เฉลี่ย:</strong> ";
                                        echo "<span class='badge badge-primary'>" . $avgResult['class'] . "</span>";
                                        echo "</div>";
                                    }
                                    
                                } catch (Exception $e) {
                                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 Honor Class Distribution จาก Database</h5>
                <?php
                try {
                    $sql = "SELECT 
                                CASE 
                                    WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                    WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                    WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                    WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                                    WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                                    WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                                    WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                    WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                                    WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                                    WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                                    WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                                    WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                    WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                                    WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                                    WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                                    WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                                    WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                                    WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                                    WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                                    WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                                    WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                                    WHEN Reputation >= 2000000000 THEN 'Class 20'
                                    ELSE 'No Class'
                                END as honor_class,
                                COUNT(*) as count,
                                AVG(CAST(Reputation AS BIGINT)) as avg_reputation
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            GROUP BY 
                                CASE 
                                    WHEN Reputation IS NULL OR Reputation < -10000 THEN 'No Class'
                                    WHEN Reputation >= -10000 AND Reputation < 10000 THEN 'Class 0'
                                    WHEN Reputation >= 10000 AND Reputation < 20000 THEN 'Class 1'
                                    WHEN Reputation >= 20000 AND Reputation < 40000 THEN 'Class 2'
                                    WHEN Reputation >= 40000 AND Reputation < 80000 THEN 'Class 3'
                                    WHEN Reputation >= 80000 AND Reputation < 150000 THEN 'Class 4'
                                    WHEN Reputation >= 150000 AND Reputation < 300000 THEN 'Class 5'
                                    WHEN Reputation >= 300000 AND Reputation < 600000 THEN 'Class 6'
                                    WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 'Class 7'
                                    WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 'Class 8'
                                    WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 'Class 9'
                                    WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 'Class 10'
                                    WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 'Class 11'
                                    WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 'Class 12'
                                    WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 'Class 13'
                                    WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 'Class 14'
                                    WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 'Class 15'
                                    WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 'Class 16'
                                    WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 'Class 17'
                                    WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 'Class 18'
                                    WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 'Class 19'
                                    WHEN Reputation >= 2000000000 THEN 'Class 20'
                                    ELSE 'No Class'
                                END
                            ORDER BY 
                                CASE 
                                    WHEN Reputation IS NULL OR Reputation < -10000 THEN 0
                                    WHEN Reputation >= -10000 AND Reputation < 10000 THEN 0
                                    WHEN Reputation >= 10000 AND Reputation < 20000 THEN 1
                                    WHEN Reputation >= 20000 AND Reputation < 40000 THEN 2
                                    WHEN Reputation >= 40000 AND Reputation < 80000 THEN 3
                                    WHEN Reputation >= 80000 AND Reputation < 150000 THEN 4
                                    WHEN Reputation >= 150000 AND Reputation < 300000 THEN 5
                                    WHEN Reputation >= 300000 AND Reputation < 600000 THEN 6
                                    WHEN Reputation >= 600000 AND Reputation < 1200000 THEN 7
                                    WHEN Reputation >= 1200000 AND Reputation < 2500000 THEN 8
                                    WHEN Reputation >= 2500000 AND Reputation < 5000000 THEN 9
                                    WHEN Reputation >= 5000000 AND Reputation < 10000000 THEN 10
                                    WHEN Reputation >= 10000000 AND Reputation < 20000000 THEN 11
                                    WHEN Reputation >= 20000000 AND Reputation < 40000000 THEN 12
                                    WHEN Reputation >= 40000000 AND Reputation < 80000000 THEN 13
                                    WHEN Reputation >= 80000000 AND Reputation < 150000000 THEN 14
                                    WHEN Reputation >= 150000000 AND Reputation < 250000000 THEN 15
                                    WHEN Reputation >= 250000000 AND Reputation < 500000000 THEN 16
                                    WHEN Reputation >= 500000000 AND Reputation < 900000000 THEN 17
                                    WHEN Reputation >= 900000000 AND Reputation < 1400000000 THEN 18
                                    WHEN Reputation >= 1400000000 AND Reputation < 2000000000 THEN 19
                                    WHEN Reputation >= 2000000000 THEN 20
                                    ELSE 0
                                END";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result) {
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm table-striped'>";
                        echo "<thead class='thead-dark'>";
                        echo "<tr><th>Honor Class</th><th>จำนวนผู้เล่น</th><th>%</th><th>Avg Reputation</th></tr>";
                        echo "</thead><tbody>";
                        
                        $totalCount = 0;
                        $data = [];
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $data[] = $row;
                            $totalCount += $row['count'];
                        }
                        
                        foreach ($data as $row) {
                            $percentage = $totalCount > 0 ? ($row['count'] / $totalCount) * 100 : 0;
                            $honorClass = $row['honor_class'];
                            
                            // กำหนดสี
                            $colorClass = 'secondary';
                            if (strpos($honorClass, 'Class') !== false) {
                                $classNum = (int)str_replace('Class ', '', $honorClass);
                                if ($classNum == 0) $colorClass = 'secondary';
                                elseif ($classNum >= 1 && $classNum <= 5) $colorClass = 'success';
                                elseif ($classNum >= 6 && $classNum <= 10) $colorClass = 'info';
                                elseif ($classNum >= 11 && $classNum <= 15) $colorClass = 'warning';
                                elseif ($classNum >= 16 && $classNum <= 19) $colorClass = 'danger';
                                elseif ($classNum >= 20) $colorClass = 'dark';
                            }
                            
                            echo "<tr>";
                            echo "<td><span class='badge badge-{$colorClass}'>" . htmlspecialchars($honorClass) . "</span></td>";
                            echo "<td>" . number_format($row['count']) . "</td>";
                            echo "<td>" . number_format($percentage, 1) . "%</td>";
                            
                            $avgRep = $row['avg_reputation'];
                            if ($avgRep >= 1000000000) {
                                echo "<td>" . number_format($avgRep / 1000000000, 1) . "B</td>";
                            } elseif ($avgRep >= 1000000) {
                                echo "<td>" . number_format($avgRep / 1000000, 1) . "M</td>";
                            } elseif ($avgRep >= 1000) {
                                echo "<td>" . number_format($avgRep / 1000, 1) . "K</td>";
                            } else {
                                echo "<td>" . number_format($avgRep) . "</td>";
                            }
                            
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table></div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-success" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ คำนวณ Honor Class จาก Reputation Points</li>
                                <li>✅ ใช้ ranges ตาม XML config</li>
                                <li>✅ รองรับ Class 0-20</li>
                                <li>✅ แสดง Average Reputation</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ จัดการ NULL values</li>
                                <li>✅ รองรับค่าติดลบ</li>
                                <li>✅ แสดงสีตาม Honor Class</li>
                                <li>✅ ข้อมูลถูกต้องตาม XML</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function refreshPage() {
    location.reload();
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
