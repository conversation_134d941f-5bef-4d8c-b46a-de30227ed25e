<?php $user->restrictionUser(true, $conn); ?>
<!--แก้ไข ไฟล์ที่ \home\switch\mstudio.php assets\js\app\mstudio.js-->
<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>
                    Manager Voucher item <span class="fw-300"><i>จัดการแลกบัตรแคส</i></span>
                </h2>

            </div>
            <div class="panel-container show">
                <div class="panel-content">

                    <!-- datatable start -->
                    <div class="table-responsive-lg">
                        <table class="table table-bordered table-hover table-striped w-100">
                            <thead>
                                <tr>
                                    <th>CharacterIdx</th>
									<th>Name</th>
									<th>อาชีพ</th>
                                    <th>WarChannelType</th>
                                    <th>Nation</th>
                                    <th>รับของกิจกรรม(ชุด)</th>
                                    <th>รับของกิจกรรม(หัว)</th>
                                    <th>แอดไอเท็ม</th>
                                    <th>ลบไอเท็ม/th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php

                                // generic function to get page
                                function getPage($stmt, $pageNum, $rowsPerPage) {
                                    $offset = ($pageNum - 1) * $rowsPerPage;
                                    $rows = array();
                                    $i = 0;
                                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                        array_push($rows, $row);
                                        $i++;
                                    }
                                    return $rows;
                                }

                                // Set the number of rows to be returned on a page.
                                $rowsPerPage = 250;

                                // Define and execute the query.  
                                // Note that the query is executed with a "scrollable" cursor.
                                $sql = "SELECT * FROM  [".DATABASE_SV."].[dbo].cabal_LordOfWar_table where WarChannelType = 5 ORDER BY Nation asc";

                                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                                if (!$stmt)
                                    die(print_r(sqlsrv_errors(), true));

                                // Get the total number of rows returned by the query.
                                $rowsReturned = sqlsrv_num_rows($stmt);
                                if ($rowsReturned === false)
                                    die(print_r(sqlsrv_errors(), true));
                                elseif ($rowsReturned == 0) {
                                    echo W_NOTHING_RETURNED;
                                    //exit();
                                } else {
                                    /* Calculate number of pages. */
                                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                                }

                                // Display the selected page of data.
                                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                                $page = getPage($stmt, $pageNum, $rowsPerPage);

                                foreach ($page as $row) {
										
								$params = array();
								$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );	
								$selectChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table Where CharacterIdx = '$row[0]'";
								$selectCharsQuery = sqlsrv_query($conn, $selectChars, $params, $options);
								$resChars = sqlsrv_fetch_array($selectCharsQuery, SQLSRV_FETCH_ASSOC);
								$name = $userLogin->thaitrans($resChars['Name']);

                                $UserNum = floor($row[0]/8);

										 $ClassType = $userLogin->recCharecter($resChars['CharacterIdx'],'ClassStyle', $conn);
										 $ChangeClass = $userLogin->recCharecter($resChars['CharacterIdx'],'ChangeClass', $conn);
										 if  ($ClassType== 1 AND $ChangeClass== NULL ) { $ClassType = "WA"; }
										 else if ($ClassType== 2 ) { $ClassType = "BL"; }
										 else if ($ClassType== 3 ) { $ClassType = "WI"; }
										 else if ($ClassType== 4 AND $ChangeClass== NULL ) { $ClassType = "FA"; }
										 else if ($ClassType== 5 ) { $ClassType = "FS"; }
										 else if ($ClassType== 6 ) { $ClassType = "FB"; }
										 else if ($ClassType== 1 AND $ChangeClass== 7 ) { $ClassType = "GL"; }
										 else if ($ClassType== 4 AND $ChangeClass== 8 ) { $ClassType = "FG"; }
										 else if ($ClassType== 3 AND $ChangeClass== 9 ) { $ClassType = "DM"; }
										 else {  $ClassType = "NO"; }

                                $selectitemcash = "SELECT * FROM [".DATABASE_CCA."].[dbo].MyCashItem Where UserNum = '$UserNum' AND ItemKindIdx = '531979'";
								$itemcashQuery = sqlsrv_query($conn, $selectitemcash, $params, $options);
								$resitemcash = sqlsrv_fetch_array($itemcashQuery, SQLSRV_FETCH_ASSOC);
                                if ($resitemcash[IsUse] == 0 AND $resitemcash[ItemKindIdx] == '531979'){
                                    $isuse = "<span class='badge badge-warning'>ยังไม่รับ(ชุด)</span>";
                                    $disable = "disabled";
                                }elseif($resitemcash[IsUse] == 1 AND $resitemcash[ItemKindIdx] == '531979'){
                                    $isuse = "<span class='badge badge-danger'>รับแล้ว(ชุด)</span>";
                                    $disable = "disabled";
                                }else{
                                    $isuse = "<span class='badge badge-primary'>ยังไม่แอด(ชุด)</span>";
                                }
                                $selectitemcash = "SELECT * FROM [".DATABASE_CCA."].[dbo].MyCashItem Where UserNum = '$UserNum' AND ItemKindIdx = '531981'";
								$itemcashQuery = sqlsrv_query($conn, $selectitemcash, $params, $options);
								$resitemcash = sqlsrv_fetch_array($itemcashQuery, SQLSRV_FETCH_ASSOC);

                                if ($resitemcash[IsUse] == 0 AND $resitemcash[ItemKindIdx] == '531981'){
                                    $isuse2 = "<span class='badge badge-warning'>ยังไม่รับ(หัว)</span>";
                                    $disable = "disabled";
                                }elseif($resitemcash[IsUse] == 1 AND $resitemcash[ItemKindIdx] == '531981'){
                                    $isuse2 = "<span class='badge badge-danger'>รับแล้ว(หัว)</span>";
                                    $disable = "disabled";
                                }else{
                                    $isuse2 = "<span class='badge badge-primary'>ยังไม่แอด(หัว)</span>";
                                }
                               


                            ?>
                                <tr>
                                    <td><?php echo $row[0]; ?></td>
                                    <td><?php echo $name; ?></td>
									<td><?php echo $ClassType; ?></td>
									<td><?php echo $row[1]; ?></td>
                                    <td><span
                                            class="<?php echo $label = ($row[2] == '1' ? ' badge badge-danger badge-pill' : $label = $row[2] == '2' ? ' badge badge-info badge-pill' : ($row[2] == '3' ? ' badge badge-warning badge-pill' : 'badge badge-secondary badge-pill'));?>">
                                            <?php echo $status = ($row[2] == '1' ? 'Capella' : $status = $row[2] == '2' ? 'Procyon' : ($row[2] == '3' ? 'GM' : 'Unknow status'));?></span>
                                    </td>
                                    <td><?php echo $isuse; ?></td>
                                    <td><?php echo $isuse2; ?></td>
                                    <td>
                                        <form method="post" name="j_add_bringer" action="">
                                            <div class="j_alert"></div>
                                            <input type="hidden" name="charsidx" value="<?php echo $row[0]; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm" <?php echo $disable; ?>><i
                                                    class="fa fa-chevron-circle-right"></i> ยืนยันไอเท็ม</button>
                                            
                                        </form>
                                    </td>
                                    <td>
                                        <form method="post" name="j_add_bringerdelitem" action="">
                                            <div class="j_alert"></div>
                                            <input type="hidden" name="UserID" value="<?php echo $UserNum; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm"><i
                                                    class="fa fa-chevron-circle-right"></i> ลบไอเท็มหมดอายุ</button>
                                            
                                        </form>
                                    </td>

                                </tr>
                                <?php } ?>
                            </tbody>

                        </table>
                        <!-- datatable end -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>