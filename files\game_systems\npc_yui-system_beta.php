<?php $zpanel->checkSession(true); ?>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .slot-button {
            width: 50px;
            height: 50px;
            margin: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0;
        }
        .btn-success {
            color: white;
        }
        #slotGrid {
            display: grid;
            grid-template-columns: repeat(8, 50px);
            gap: 4px;
        }
        .slot-button.drag-over {
        border: 2px dashed #007bff;
        background-color: #e9f5ff;
  }
    </style>

<body class="container py-4">
    <h2 class="mb-4">NPC Shop Editor</h2>

    <div class="mb-3 d-flex align-items-center gap-2">
        <select id="shopSelector" class="form-select w-auto"></select>
        <button id="toggleBtn" class="btn btn-secondary">Toggle Active</button>
        <button class="btn btn-success" onclick="createShop()">Create New Shop</button>
    </div>

    <div id="shopStatus" class="mb-3 text-muted"></div>
    <div id="slotGrid"></div>

    <!-- Modal -->
    <div class="modal fade" id="slotModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="slotForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Slot <span id="modalSlot"></span></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-2">
                            <label class="form-label">Item</label>
                            <input list="itemList" id="itemInput" class="form-control" required>
                            <datalist id="itemList"></datalist>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Item Option</label>
                            <input type="number" id="optionInput" class="form-control" value="0" required>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Alz Price</label>
                            <input type="number" id="alzInput" class="form-control" value="0" required>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Duration Index</label>
                            <input type="number" id="durationInput" class="form-control" value="0" required>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">WEXP Price</label>
                            <input type="number" id="pointInput" class="form-control" value="0" required>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Expiration Time</label>
                            <input type="datetime-local" id="expireInput" class="form-control" value="1970-01-01T00:00">
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Daily Limit</label>
                            <input type="number" id="dailyInput" class="form-control" value="0" required>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">Weekly Limit</label>
                            <input type="number" id="weeklyInput" class="form-control" value="0" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" id="slotNumber">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-danger" onclick="deleteSlot()">Delete</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    let currentEventId = -1;
    let currentUseFlag = 0;
    let itemDict = {};
    let currentSlotData = {};

async function loadShops() {
    try {
        const res = await fetch('files/game_systems/class_module/load_shops.php');
        const text = await res.text();
        console.log("[loadShops] Raw Response:", text);

        const shops = JSON.parse(text);
        const selector = document.getElementById('shopSelector');
        selector.innerHTML = '<option value="">Select Shop</option>';
        shops.forEach(s => {
            const opt = document.createElement('option');
            opt.value = s.id;
            opt.text = `${s.id} - ${s.name}`;
            selector.appendChild(opt);
        });
    } catch (err) {
        console.error("[loadShops] Error:", err);
    }
}


async function loadItems() {
    try {
        const res = await fetch('files/game_systems/class_module/load_items.php');
        const text = await res.text();
        console.log("[loadItems] Raw Response:", text);

        itemDict = JSON.parse(text);
        const list = document.getElementById('itemList');
        list.innerHTML = '';
        for (const [id, name] of Object.entries(itemDict)) {
            const opt = document.createElement('option');
            opt.value = `${id} - ${name}`;
            list.appendChild(opt);
        }
    } catch (err) {
        console.error("[loadItems] Error:", err);
    }
}

// ฟังก์ชันบันทึก slot (ใช้แทน POST save_slot.php)
async function saveSlot(slot, data) {
    try {
        await fetch('files/game_systems/class_module/save_slot.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                eventId: currentEventId,
                slot,
                itemId: data.itemId,
                itemOption: data.itemOption,
                alz: data.alz,
                duration: data.duration,
                point: data.point,
                expire: data.expire,
                daily: data.daily,
                weekly: data.weekly
            })
        });
    } catch (err) {
        console.error("[saveSlot] Error:", err);
    }
}

// ฟังก์ชันลบ slot ตามเลขช่อง
async function deleteSlotByNumber(slot) {
    try {
        await fetch('files/game_systems/class_module/delete_slot.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ eventId: currentEventId, slot })
        });
    } catch (err) {
        console.error("[deleteSlotByNumber] Error:", err);
    }
}

// ฟังก์ชันสลับข้อมูล slot แบบปลอดภัย
async function swapSlotData(fromSlot, toSlot) {
    try {
        if (fromSlot === toSlot) return;
        const fromData = currentSlotData[fromSlot];
        const toData = currentSlotData[toSlot] || null;

        if (!fromData) {
            console.warn(`No data in fromSlot ${fromSlot}`);
            return;
        }

        // สลับข้อมูลในตัวแปรก่อน
        currentSlotData[toSlot] = fromData;
        if (toData) {
            currentSlotData[fromSlot] = toData;
        } else {
            delete currentSlotData[fromSlot];
        }

        // ส่งคำสั่งไปยัง server
        await Promise.all([
            saveSlot(toSlot, fromData),
            toData ? saveSlot(fromSlot, toData) : deleteSlotByNumber(fromSlot)
        ]);

        await loadShopItems(currentEventId); // refresh
    } catch (err) {
        console.error("[swapSlotData] Error:", err);
    }
}

async function loadShopItems(eventId) {
    try {
        const res = await fetch(`files/game_systems/class_module/load_shop_items.php?eventId=${eventId}`);
        const text = await res.text();
        console.log("[loadShopItems] Raw Response:", text);

        const data = JSON.parse(text);
        currentUseFlag = parseInt(data.useFlag);
        document.getElementById('toggleBtn').textContent = currentUseFlag ? 'Deactivate' : 'Activate';

        const grid = document.getElementById('slotGrid');
        grid.innerHTML = '';
        currentSlotData = data.items;

        document.getElementById('shopStatus').textContent = `Items in use: ${Object.keys(data.items).length}/64`;

        for (let i = 0; i < 128; i++) {
            const btn = document.createElement('button');
            btn.className = 'btn btn-outline-secondary slot-button';
            btn.textContent = i;
            btn.onclick = () => openSlotModal(i);

                // ทำให้ปุ่มลากได้
                btn.setAttribute('draggable', true);
                btn.dataset.slot = i;

                btn.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', i); // บอกว่าเรากำลังลาก slot ไหน
                });

                btn.addEventListener('dragover', (e) => {
                    e.preventDefault(); // จำเป็นเพื่อให้ drop ทำงาน
                    btn.classList.add('drag-over');
                });

                btn.addEventListener('dragleave', () => {
                    btn.classList.remove('drag-over');
                });

                btn.addEventListener('drop', (e) => {
                    e.preventDefault();
                    btn.classList.remove('drag-over');

                    const fromSlot = parseInt(e.dataTransfer.getData('text/plain'));
                    const toSlot = parseInt(e.target.dataset.slot);
                    swapSlotData(fromSlot, toSlot);
                });

            if (data.items[i]) btn.classList.add('btn-success');
            grid.appendChild(btn);
        }
    } catch (err) {
        console.error("[loadShopItems] Error:", err);
    }
}

    function openSlotModal(slot) {
        if (currentEventId <= 0) return;
        document.getElementById('slotNumber').value = slot;
        document.getElementById('modalSlot').textContent = slot;

        const slotInfo = currentSlotData[slot];
        if (slotInfo) {
            document.getElementById('itemInput').value = `${slotInfo.itemId} - ${itemDict[slotInfo.itemId] || ''}`;
            document.getElementById('optionInput').value = slotInfo.itemOption;
            document.getElementById('alzInput').value = slotInfo.alz;
            document.getElementById('durationInput').value = slotInfo.duration;
            document.getElementById('pointInput').value = slotInfo.point;
            document.getElementById('expireInput').value = slotInfo.expire.replace(" ", "T");
            document.getElementById('dailyInput').value = slotInfo.daily;
            document.getElementById('weeklyInput').value = slotInfo.weekly;
        } else {
            document.getElementById('itemInput').value = '';
            document.getElementById('optionInput').value = 0;
            document.getElementById('alzInput').value = 0;
            document.getElementById('durationInput').value = 0;
            document.getElementById('pointInput').value = 0;
            document.getElementById('expireInput').value = "1970-01-01T00:00";
            document.getElementById('dailyInput').value = 0;
            document.getElementById('weeklyInput').value = 0;
        }

        new bootstrap.Modal(document.getElementById('slotModal')).show();
    }

    document.getElementById('shopSelector').addEventListener('change', function () {
        currentEventId = parseInt(this.value);
        if (currentEventId > 0) loadShopItems(currentEventId);
    });

    document.getElementById('slotForm').addEventListener('submit', async function (e) {
        e.preventDefault();
        const itemRaw = document.getElementById('itemInput').value;
        const itemId = parseInt(itemRaw.split('-')[0].trim());
        const slot = parseInt(document.getElementById('slotNumber').value);
        const data = {
            eventId: currentEventId,
            slot,
            itemId,
            itemOption: parseInt(document.getElementById('optionInput').value),
            alz: parseInt(document.getElementById('alzInput').value),
            duration: parseInt(document.getElementById('durationInput').value),
            point: parseInt(document.getElementById('pointInput').value),
            expire: document.getElementById('expireInput').value.replace("T", " "),
            daily: parseInt(document.getElementById('dailyInput').value),
            weekly: parseInt(document.getElementById('weeklyInput').value)
        };
        await fetch('files/game_systems/class_module/save_slot.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        bootstrap.Modal.getInstance(document.getElementById('slotModal')).hide();
        loadShopItems(currentEventId);
    });

async function deleteSlot() {
    try {
        const slot = parseInt(document.getElementById('slotNumber').value);
        const res = await fetch('files/game_systems/class_module/delete_slot.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ eventId: currentEventId, slot })
        });
        const text = await res.text();
        console.log("[deleteSlot] Response:", text);

        bootstrap.Modal.getInstance(document.getElementById('slotModal')).hide();
        loadShopItems(currentEventId);
    } catch (err) {
        console.error("[deleteSlot] Error:", err);
    }
}
document.getElementById('toggleBtn').addEventListener('click', async function () {
    if (currentEventId <= 0) return;
    try {
        const newFlag = currentUseFlag ? 0 : 1;
        const res = await fetch(`files/game_systems/class_module/toggle_shop.php?eventId=${currentEventId}&newFlag=${newFlag}`);
        const text = await res.text();
        console.log("[toggle_shop] Response:", text);

        currentUseFlag = newFlag;
        this.textContent = newFlag ? 'Deactivate' : 'Activate';
    } catch (err) {
        console.error("[toggle_shop] Error:", err);
    }
});

async function createShop() {
    const id = prompt("Enter EventID:", "1000");
    const name = prompt("Enter Shop Name:", "New Shop");
    const title = prompt("Enter Shop Title:", "Shop Title");
    const desc = prompt("Enter Description:", "Shop Description");
    if (!id || !name || !title || !desc) return;

    try {
        const res = await fetch('files/game_systems/class_module/create_shop.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, name, title, desc })
        });
        const text = await res.text();
        console.log("[createShop] Response:", text);

        alert('Shop created!');
        loadShops();
    } catch (err) {
        console.error("[createShop] Error:", err);
        alert('Failed to create shop.');
    }
}

    loadItems();
    loadShops();
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>