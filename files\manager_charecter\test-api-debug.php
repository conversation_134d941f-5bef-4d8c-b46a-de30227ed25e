<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-bug"></i> Debug API JSON Response Issues
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขปัญหา "Unexpected token '<', "<!DOCTYPE"... is not valid JSON"</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fal fa-exclamation-triangle"></i>
                    <strong>ปัญหา:</strong> API กำลังส่งคืน HTML แทนที่จะเป็น JSON
                </div>
                
                <h5>🔍 ทดสอบ API Endpoints</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="testAPIEndpoint('live_stats')">
                        <i class="fal fa-chart-line"></i> Test Live Stats
                    </button>
                    <button class="btn btn-info" onclick="testAPIEndpoint('recent_activities')">
                        <i class="fal fa-history"></i> Test Recent Activities
                    </button>
                    <button class="btn btn-warning" onclick="testAPIEndpoint('alerts')">
                        <i class="fal fa-bell"></i> Test Alerts
                    </button>
                </div>
                
                <div id="api-test-results"></div>
                
                <h5 class="mt-4">🔧 Direct API Tests</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Test 1: Direct API Call</h6>
                            </div>
                            <div class="card-body">
                                <p>ทดสอบเรียก API โดยตรง:</p>
                                <a href="?url=manager_charecter/api/character-data&action=live_stats" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fal fa-external-link"></i> Open Live Stats API
                                </a>
                                <a href="?url=manager_charecter/api/character-data&action=recent_activities&limit=5" target="_blank" class="btn btn-sm btn-outline-info">
                                    <i class="fal fa-external-link"></i> Open Activities API
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Test 2: PHP Direct Test</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    // ทดสอบ function โดยตรง
                                    if (function_exists('getLiveStats')) {
                                        echo '<div class="alert alert-success alert-sm">✅ getLiveStats function exists</div>';
                                    } else {
                                        echo '<div class="alert alert-danger alert-sm">❌ getLiveStats function not found</div>';
                                    }
                                    
                                    // ทดสอบ database connection
                                    if ($conn) {
                                        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                                        $result = sqlsrv_query($conn, $sql);
                                        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                            echo '<div class="alert alert-success alert-sm">✅ Database: ' . number_format($row['count']) . ' characters</div>';
                                        } else {
                                            echo '<div class="alert alert-danger alert-sm">❌ Database query failed</div>';
                                        }
                                    } else {
                                        echo '<div class="alert alert-danger alert-sm">❌ Database connection failed</div>';
                                    }
                                } catch (Exception $e) {
                                    echo '<div class="alert alert-danger alert-sm">❌ Error: ' . $e->getMessage() . '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📋 Troubleshooting Steps</h5>
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. API ส่งคืน HTML แทน JSON
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>สาเหตุที่เป็นไปได้:</strong></p>
                                <ul>
                                    <li>API endpoint ไม่ถูกต้อง</li>
                                    <li>Routing system ไม่ทำงาน</li>
                                    <li>PHP errors ทำให้ส่งคืน error page</li>
                                    <li>Authentication ล้มเหลว</li>
                                </ul>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <ol>
                                    <li>ตรวจสอบ URL pattern ใน routing</li>
                                    <li>เปิด error reporting ใน API</li>
                                    <li>ตรวจสอบ authentication</li>
                                    <li>ใช้ try-catch ใน API</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. JavaScript Fetch Issues
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>ปัญหาที่เป็นไปได้:</strong></p>
                                <ul>
                                    <li>CORS issues</li>
                                    <li>Credentials ไม่ถูกส่ง</li>
                                    <li>Headers ไม่ถูกต้อง</li>
                                    <li>URL encoding issues</li>
                                </ul>
                                
                                <p><strong>วิธีแก้ไข:</strong></p>
                                <pre><code>fetch('?url=manager_charecter/api/character-data&action=live_stats', {
    method: 'GET',
    credentials: 'same-origin',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json'
    }
})</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Error Handling Best Practices
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#troubleshootingAccordion">
                            <div class="card-body">
                                <p><strong>ปรับปรุง Error Handling:</strong></p>
                                <pre><code>try {
    const response = await fetch(url);
    const text = await response.text();
    
    try {
        const data = JSON.parse(text);
        // ใช้ data
    } catch (parseError) {
        console.error('Invalid JSON:', text.substring(0, 200));
        console.error('Parse error:', parseError);
    }
} catch (fetchError) {
    console.error('Fetch error:', fetchError);
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">🎯 การแก้ไขที่ทำ</h5>
                <div class="alert alert-success">
                    <h6><i class="fal fa-check-circle"></i> สิ่งที่แก้ไขแล้ว:</h6>
                    <ul class="mb-0">
                        <li>✅ เพิ่ม authentication ใน API</li>
                        <li>✅ ปรับปรุง error handling ใน JavaScript</li>
                        <li>✅ ใช้ text() แล้วค่อย parse JSON</li>
                        <li>✅ เพิ่ม debug information</li>
                        <li>✅ ปรับปรุง console logging</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testAPIEndpoint(action) {
    const resultDiv = document.getElementById('api-test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-spinner fa-spin"></i> Testing ' + action + '...</div>';
    
    try {
        console.log('Testing API endpoint:', action);
        
        const url = '?url=manager_charecter/api/character-data&action=' + action + (action === 'recent_activities' ? '&limit=5' : '');
        console.log('URL:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);
        
        const responseText = await response.text();
        console.log('Response text (first 500 chars):', responseText.substring(0, 500));
        
        let html = '<div class="alert alert-' + (response.ok ? 'success' : 'danger') + '">';
        html += '<h6><i class="fal fa-' + (response.ok ? 'check' : 'times') + '"></i> ' + action.toUpperCase() + ' Test Result</h6>';
        html += '<p><strong>Status:</strong> ' + response.status + ' ' + response.statusText + '</p>';
        html += '<p><strong>Content-Type:</strong> ' + (response.headers.get('content-type') || 'Not set') + '</p>';
        
        // ตรวจสอบว่าเป็น JSON หรือไม่
        if (responseText.trim().startsWith('{') || responseText.trim().startsWith('[')) {
            try {
                const data = JSON.parse(responseText);
                html += '<p><strong>JSON Valid:</strong> ✅ Yes</p>';
                html += '<p><strong>Success:</strong> ' + (data.success ? '✅ Yes' : '❌ No') + '</p>';
                
                if (data.success && data.data) {
                    if (Array.isArray(data.data)) {
                        html += '<p><strong>Data Count:</strong> ' + data.data.length + '</p>';
                    } else {
                        html += '<p><strong>Data Keys:</strong> ' + Object.keys(data.data).join(', ') + '</p>';
                    }
                }
                
                if (data.error) {
                    html += '<p><strong>Error:</strong> ' + data.error + '</p>';
                }
                
                html += '<details><summary>JSON Response</summary><pre>' + JSON.stringify(data, null, 2) + '</pre></details>';
            } catch (parseError) {
                html += '<p><strong>JSON Valid:</strong> ❌ No - ' + parseError.message + '</p>';
                html += '<details><summary>Raw Response</summary><pre>' + responseText + '</pre></details>';
            }
        } else {
            html += '<p><strong>JSON Valid:</strong> ❌ No - Response is not JSON</p>';
            html += '<p><strong>Response Type:</strong> ' + (responseText.includes('<!DOCTYPE') ? 'HTML Document' : 'Unknown') + '</p>';
            html += '<details><summary>Raw Response (first 1000 chars)</summary><pre>' + responseText.substring(0, 1000) + '</pre></details>';
        }
        
        html += '</div>';
        resultDiv.innerHTML = html;
        
    } catch (error) {
        console.error('Test error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger"><h6><i class="fal fa-times"></i> ' + action.toUpperCase() + ' Test Failed</h6><p>Error: ' + error.message + '</p></div>';
    }
}

// Auto-test on page load
$(document).ready(function() {
    console.log('🔍 API Debug page loaded');
    
    // Test live_stats automatically
    setTimeout(() => {
        testAPIEndpoint('live_stats');
    }, 1000);
});
</script>

<style>
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

details {
    margin-top: 0.5rem;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

summary:hover {
    text-decoration: underline;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}
</style>
