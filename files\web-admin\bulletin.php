<?php
/**
  | Copyright 2015
  | Developed by FDEV
  | All rights reserved.
  | NOTE: This CMS have a some files with pagination, all files is the same code.
 */
$user->restrictionUser(true, $conn);
if (isset($_GET['action']) && $_GET['action'] == 'update') {
    $returnSuccess = S_CONFIG_UPDATED;
}
?>
<div class="page-header"><h1><?php echo PT_BULLETIN_MANAGER; ?> <small><?php echo PT_BULLETIN_MANAGER_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h2>Bulletins</h2>
        </div>
        <div class="panel-body" style="padding: 0;">
            <div class="col-lg-12" style="padding: 0;">
                <?php

                // generic function to get page
                function getPage($stmt, $pageNum, $rowsPerPage) {
                    $offset = ($pageNum - 1) * $rowsPerPage;
                    $rows = array();
                    $i = 0;
                    while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                        array_push($rows, $row);
                        $i++;
                    }
                    return $rows;
                }

                // Set the number of rows to be returned on a page.
                $rowsPerPage = 10;

                // Define and execute the query.  
                // Note that the query is executed with a "scrollable" cursor.
                $sql = "SELECT * FROM WEB_Bulletin";

                $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                if (!$stmt)
                    die(print_r(sqlsrv_errors(), true));

                // Get the total number of rows returned by the query.
                $rowsReturned = sqlsrv_num_rows($stmt);
                if ($rowsReturned === false)
                    die(print_r(sqlsrv_errors(), true));
                elseif ($rowsReturned == 0) {
                    echo W_NOTHING_RETURNED;
                    //exit();
                } else {
                    /* Calculate number of pages. */
                    $numOfPages = ceil($rowsReturned / $rowsPerPage);
                }

                // Display the selected page of data.
                $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                $page = getPage($stmt, $pageNum, $rowsPerPage);

                foreach ($page as $row) {
                    ?>
                    <div>
                        <a href="#" class="list-group-item" style="border-radius: 0;">
                            <h4 class="list-group-item-heading"><?php echo $row[2]; ?></h4>
                            <p class="list-group-item-text">
                            <?php echo htmlspecialchars_decode($row[3]); ?>
                            </p>
                        </a>
                        <div class="well well-sm" style="margin-bottom: 0;">
                            <div class="input-group">
                                <a href="?url=bulletin/a/edit&id=<?php echo $row[0]; ?>" class="btn btn-sm btn-info flat"><span class="glyphicon glyphicon-edit"></span> Editar</a>
                                <a href="?url=bulletin/a/manager&id=<?php echo $row[0]; ?>&delete=wait" class="btn btn-sm btn-danger flat"><span class="glyphicon glyphicon-remove"></span> Deletar</a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
    </div>
</div>
<a href="?url=bulletin/a/create" class="btn btn-success btn-block flat">Create a new bulletin</a>



