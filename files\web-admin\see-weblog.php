<?php $user->restrictionUser(true, $conn); ?>
<div class="page-header"><h1><?php echo PT_WEBLOG; ?> <small><?php echo PT_WEBLOG_DESC; ?></small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <?php
                $getLogID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

                // get server log by ID
                $selectWebLogID = "SELECT * FROM WEB_Log WHERE id = '$getLogID'";
                $selectWebLogParam = array();
                $selectWebLogOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectWebLogQuery = sqlsrv_query($conn, $selectWebLogID, $selectWebLogParam, $selectWebLogOpt);
                $selectWebLogRows = sqlsrv_num_rows($selectWebLogQuery);

                if ($selectWebLogRows) {
                    while ($resWebLog = sqlsrv_fetch_array($selectWebLogQuery, SQLSRV_FETCH_ASSOC)) {
                        ?>
                        <div class="col-lg-6">
                            <h4 class="text-red">LogID</h4>
                            <p><?php echo $resWebLog['LogID']; ?></p>
                            <hr>
                            <h4 class="text-red"><?php echo T_ACTION; ?></h4>
                            <p><?php echo $resWebLog['action']; ?></p>
                            <hr>
                            <h4 class="text-red"><?php echo T_MESSAGE; ?></h4>
                            <p><?php echo $resWebLog['msg']; ?></p>
                        </div>

                        <div class="col-lg-6">
                            <h4 class="text-red">Report Time</h4>
                            <p><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($resWebLog['datereported'])); ?></p>
                            <hr>
                            <h4 class="text-red">Admin CustomerID</h4>
                            <p><?php echo $resWebLog['UserNum']; ?></p>
                        </div>
                        <?php
                    }
                }else{
                    $returnWarning = W_NOTHING_RETURNED;
                }
                ?>
                <?php if (isset($returnSuccess)) { ?>
                    <div class="alert alert-success"><?php echo $returnSuccess; ?></div>
                <?php } elseif (isset($returnWarning)) { ?>
                    <div class="alert alert-warning"><?php echo $returnWarning; ?></div>
                <?php } elseif (isset($returnError)) { ?>
                    <div class="alert alert-danger"><?php echo $returnError; ?></div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>