/**
 * Notification System Styles
 * Custom styles for the notification system
 */

/* Notification Bell */
#notification-bell {
    position: relative;
    border: none;
    background: transparent;
    color: #007bff;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

#notification-bell:hover {
    background-color: #f8f9fa;
    color: #0056b3;
    transform: scale(1.1);
}

#notification-bell:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Notification Badge */
#notification-badge {
    font-size: 0.7rem;
    min-width: 1.2rem;
    height: 1.2rem;
    line-height: 1.2rem;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Notification Dropdown */
#notification-dropdown {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0;
}

#notification-dropdown .dropdown-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    margin: 0;
}

#notification-dropdown .dropdown-divider {
    margin: 0;
}

/* Notification Items */
.notification-item {
    padding: 0.75rem 1rem;
    border: none;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa !important;
}

.notification-item.bg-light {
    background-color: #e3f2fd !important;
}

.notification-item .fw-bold {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.notification-item .text-muted {
    font-size: 0.8rem;
    line-height: 1.3;
}

.notification-item .badge {
    font-size: 0.7rem;
}

/* Notification Icons */
.notification-item i {
    font-size: 1.1rem;
    margin-right: 0.5rem;
}

/* Empty State */
.notification-empty {
    padding: 2rem 1rem;
    text-align: center;
    color: #6c757d;
}

.notification-empty i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

/* Loading State */
.notification-loading {
    padding: 1rem;
    text-align: center;
}

.notification-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Modal */
.notification-modal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
}

.notification-modal .notification-item {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
    border-radius: 0;
}

.notification-modal .notification-item:last-child {
    border-bottom: none;
}

/* Notification Stats */
.notification-stats {
    display: flex;
    justify-content: space-around;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

.notification-stats .stat-item {
    text-align: center;
    flex: 1;
}

.notification-stats .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.notification-stats .stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

/* Priority Indicators */
.notification-priority-high {
    border-left: 4px solid #dc3545 !important;
}

.notification-priority-medium {
    border-left: 4px solid #ffc107 !important;
}

.notification-priority-low {
    border-left: 4px solid #28a745 !important;
}

/* Type Indicators */
.notification-type-error {
    background-color: #f8d7da !important;
}

.notification-type-warning {
    background-color: #fff3cd !important;
}

.notification-type-success {
    background-color: #d1edff !important;
}

.notification-type-info {
    background-color: #d1ecf1 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    #notification-dropdown {
        width: 300px !important;
        right: 0 !important;
        left: auto !important;
    }
    
    .notification-item {
        padding: 0.5rem 0.75rem;
    }
    
    .notification-item .fw-bold {
        font-size: 0.85rem;
    }
    
    .notification-item .text-muted {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    #notification-dropdown {
        width: 280px !important;
    }
    
    .notification-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .notification-stats .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}

/* Animation for new notifications */
.notification-new {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mark as read button */
.notification-mark-read {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-mark-read {
    opacity: 1;
}

/* Notification filters */
.notification-filters {
    padding: 0.75rem 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.notification-filters .form-select {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Notification actions */
.notification-actions {
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #notification-dropdown {
        background-color: #343a40;
        border-color: #495057;
    }
    
    #notification-dropdown .dropdown-header {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .notification-item {
        color: #fff;
    }
    
    .notification-item:hover {
        background-color: #495057 !important;
    }
    
    .notification-item.bg-light {
        background-color: #495057 !important;
    }
    
    .notification-stats {
        background-color: #495057;
    }
    
    .notification-filters {
        background-color: #495057;
        border-color: #6c757d;
    }
    
    .notification-actions {
        background-color: #495057;
        border-color: #6c757d;
    }
}
