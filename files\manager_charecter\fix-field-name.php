<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-database"></i> แก้ไข Field Name: ReputationClass → Reputation
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">แก้ไขชื่อฟิลด์ให้ถูกต้อง</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>แก้ไขแล้ว:</strong> เปลี่ยนจาก ReputationClass เป็น Reputation ในทุกไฟล์แล้ว
                </div>
                
                <h5>🔧 การแก้ไขที่ทำ</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">❌ ชื่อฟิลด์เดิม</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>SELECT ReputationClass FROM cabal_character_table</code></pre>
                                <p class="mb-0"><strong>ปัญหา:</strong> ชื่อฟิลด์ไม่ถูกต้อง</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">✅ ชื่อฟิลด์ใหม่</h6>
                            </div>
                            <div class="card-body">
                                <pre><code>SELECT Reputation FROM cabal_character_table</code></pre>
                                <p class="mb-0"><strong>ผลลัพธ์:</strong> ชื่อฟิลด์ถูกต้องแล้ว</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📁 ไฟล์ที่แก้ไข</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th>ไฟล์</th>
                                <th>การแก้ไข</th>
                                <th>จำนวนที่แก้</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>character-statistics.php</code></td>
                                <td>SQL Query หลัก + Top Players</td>
                                <td>3 ตำแหน่ง</td>
                                <td><span class="badge badge-success">แก้ไขแล้ว</span></td>
                            </tr>
                            <tr>
                                <td><code>test-reputation-nation.php</code></td>
                                <td>SQL Query ทดสอบ</td>
                                <td>1 ตำแหน่ง</td>
                                <td><span class="badge badge-success">แก้ไขแล้ว</span></td>
                            </tr>
                            <tr>
                                <td><code>debug-honor-class.php</code></td>
                                <td>Debug queries</td>
                                <td>2 ตำแหน่ง</td>
                                <td><span class="badge badge-success">แก้ไขแล้ว</span></td>
                            </tr>
                            <tr>
                                <td><code>test-honor-complete.php</code></td>
                                <td>Complete test query</td>
                                <td>1 ตำแหน่ง</td>
                                <td><span class="badge badge-success">แก้ไขแล้ว</span></td>
                            </tr>
                            <tr>
                                <td><code>fix-honor-unknown.php</code></td>
                                <td>Documentation</td>
                                <td>3 ตำแหน่ง</td>
                                <td><span class="badge badge-success">แก้ไขแล้ว</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h5 class="mt-4">🧪 ทดสอบการแก้ไข</h5>
                <?php
                // ทดสอบ SQL Query ใหม่
                try {
                    echo "<h6>ทดสอบ SQL Query ที่แก้ไขแล้ว:</h6>";
                    
                    $sql = "SELECT 
                                CASE 
                                    WHEN Reputation IS NULL THEN 'No Class'
                                    WHEN Reputation = 0 THEN 'No Class'
                                    WHEN Reputation BETWEEN 1 AND 20 THEN 'Class ' + CAST(Reputation AS VARCHAR)
                                    WHEN Reputation < 0 THEN 'No Class'
                                    WHEN Reputation > 20 THEN 'Class 20+'
                                    ELSE 'No Class'
                                END as honor_class,
                                ISNULL(Reputation, 0) as honor_value,
                                COUNT(*) as count
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                            GROUP BY Reputation
                            ORDER BY Reputation";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    if ($result) {
                        echo "<div class='alert alert-success'>";
                        echo "<h6><i class='fal fa-check-circle'></i> SQL Query ทำงานได้!</h6>";
                        echo "<p><strong>ผลลัพธ์:</strong> Query ใช้ฟิลด์ Reputation ได้สำเร็จ</p>";
                        
                        $rowCount = 0;
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $rowCount++;
                            if ($rowCount <= 3) { // แสดงแค่ 3 แถวแรก
                                echo "<p><strong>ตัวอย่าง:</strong> " . $row['honor_class'] . " (Value: " . $row['honor_value'] . ") = " . number_format($row['count']) . " คน</p>";
                            }
                        }
                        
                        echo "<p><strong>จำนวนแถวทั้งหมด:</strong> " . $rowCount . " Honor Classes</p>";
                        echo "</div>";
                        
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h6><i class='fal fa-times'></i> SQL Query ล้มเหลว!</h6>";
                        echo "<p><strong>ข้อผิดพลาด:</strong> ไม่สามารถ execute query ได้</p>";
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h6><i class='fal fa-times'></i> Database Error!</h6>";
                    echo "<p><strong>ข้อผิดพลาด:</strong> " . $e->getMessage() . "</p>";
                    echo "<p><strong>สาเหตุที่เป็นไปได้:</strong></p>";
                    echo "<ul>";
                    echo "<li>ฟิลด์ Reputation ไม่มีอยู่ในตาราง</li>";
                    echo "<li>ชื่อฟิลด์ที่ถูกต้องอาจเป็น ReputationClass</li>";
                    echo "<li>ตารางไม่มีฟิลด์นี้</li>";
                    echo "</ul>";
                    echo "</div>";
                }
                ?>
                
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="testDatabase()">
                        <i class="fal fa-database"></i> ทดสอบ Database
                    </button>
                    <button class="btn btn-success" onclick="checkFieldName()">
                        <i class="fal fa-search"></i> ตรวจสอบชื่อฟิลด์
                    </button>
                    <button class="btn btn-warning" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปการแก้ไข</h5>
                <div class="accordion" id="changesAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. SQL Queries
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#changesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>เปลี่ยน <code>ReputationClass</code> เป็น <code>Reputation</code> ในทุก SELECT</li>
                                    <li>เปลี่ยน <code>ISNULL(ReputationClass, 0)</code> เป็น <code>ISNULL(Reputation, 0)</code></li>
                                    <li>เปลี่ยน <code>GROUP BY ReputationClass</code> เป็น <code>GROUP BY Reputation</code></li>
                                    <li>เปลี่ยน <code>ORDER BY ReputationClass</code> เป็น <code>ORDER BY Reputation</code></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. PHP Variables
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#changesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>เปลี่ยน <code>$row['ReputationClass']</code> เป็น <code>$row['Reputation']</code></li>
                                    <li>เปลี่ยน <code>$repClass</code> เป็น <code>$reputation</code></li>
                                    <li>อัพเดท logic การตรวจสอบค่า</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. Documentation
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#changesAccordion">
                            <div class="card-body">
                                <p><strong>การเปลี่ยนแปลง:</strong></p>
                                <ul>
                                    <li>อัพเดทข้อความใน error messages</li>
                                    <li>แก้ไข code examples</li>
                                    <li>อัพเดท comments ในโค้ด</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fal fa-info-circle"></i> หมายเหตุ:</h6>
                    <p class="mb-0">หากยังมีปัญหา อาจต้องตรวจสอบชื่อฟิลด์ที่ถูกต้องในฐานข้อมูลจริง โดยใช้คำสั่ง:</p>
                    <pre class="mt-2"><code>SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'cabal_character_table' 
AND COLUMN_NAME LIKE '%Reputation%'</code></pre>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> สรุปการแก้ไข:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข character-statistics.php</li>
                                <li>✅ แก้ไข test-reputation-nation.php</li>
                                <li>✅ แก้ไข debug-honor-class.php</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ แก้ไข test-honor-complete.php</li>
                                <li>✅ แก้ไข fix-honor-unknown.php</li>
                                <li>✅ ใช้ฟิลด์ Reputation ทุกที่</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function testDatabase() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> ดูผลการทดสอบด้านบน - หาก SQL Query ทำงานได้แสดงว่าฟิลด์ถูกต้อง</div>';
}

function checkFieldName() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fal fa-search"></i> ใช้คำสั่ง SQL ด้านล่างเพื่อตรวจสอบชื่อฟิลด์ที่ถูกต้องในฐานข้อมูล</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
pre {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.875em;
}

.table th, .table td {
    font-size: 0.875rem;
}
</style>
