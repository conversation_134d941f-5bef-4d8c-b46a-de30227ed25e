<?php $user->restrictionUser(true, $conn); ?>


<div class="row">
    <div class="col">
        <?php
                                    // variables for this page only
                                    $getID = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
                                    $getSubAction = filter_input(INPUT_GET, 'sub_action', FILTER_DEFAULT);
                                    // scripts
                                    if(isset($getAction)){
                                        if($getAction == 'removedev'){
                                            $removeDevAcc = "UPDATE [". DATABASE_ACC ."].[dbo].cabal_auth_table SET IsDeveloper = '0' WHERE UserNum = '$getID'";
                                            $removeDevAccQuery = sqlsrv_query($conn, $removeDevAcc, array());
                                            if($removeDevAccQuery):
                                                $removeDevData = "UPDATE [". DATABASE_ACC ."].[dbo].cabal_auth_table SET IsDeveloper = '0' WHERE UserNum = '$getID'";
                                                $removeDevDataQuery = sqlsrv_query($conn, $removeDevData, array());
                                                if($removeDevDataQuery):
                                                    $returnSuccess = 'DEV removed from PlayerID: ' . $getID;
                                                else:
                                                    $returnError = 'Occurs an error, contact the Developer';
                                                endif;
                                                
                                            endif;
                                        }elseif($getAction == 'saperm'){
                                            if(isset($getSubAction) && $getSubAction == 'disable'){
                                                $updateSAPerm = "UPDATE WEB_Perm_Menus SET server_admin = '0' WHERE UserNum = '$getID'";
                                                $returnMessage = "Server Admin permission removed from Player!";
                                            }else{
                                                $updateSAPerm = "UPDATE WEB_Perm_Menus SET server_admin = '1' WHERE UserNum = '$getID'";
                                                $returnMessage = "Server Admin permission has been added to Player!";
                                            }
                                            $updateSAPermQuery = sqlsrv_query($conn, $updateSAPerm, array());
                                            if($updateSAPermQuery):
                                                $returnSuccess = $returnMessage;
                                            else:
                                                $returnError = "Occurs an error when try update Permissions!";
                                            endif;
                                        }elseif($getAction == 'waperm'){
                                            if(isset($getSubAction) && $getSubAction == 'disable'){
                                                $updateWAPerm = "UPDATE WEB_Perm_Menus SET web_admin = '0' WHERE UserNum = '$getID'";
                                                $returnMessage = "Web Admin permission removed from Player!";
                                            }else{
                                                $updateWAPerm = "UPDATE WEB_Perm_Menus SET web_admin = '1' WHERE UserNum = '$getID'";
                                                $returnMessage = "Web Admin permission has been added to Player!";
                                            }
                                            $updateWAPermQuery = sqlsrv_query($conn, $updateWAPerm, array());
                                            if($updateWAPermQuery):
                                                $returnSuccess = $returnMessage;
                                            else:
                                                $returnError = "Occurs an error when try update Permissions!";
                                            endif;
                                        }elseif($getAction == 'manperm'){
                                            if(isset($getSubAction) && $getSubAction == 'disable'){
                                                $updateMANPerm = "UPDATE WEB_Perm_Menus SET management = '0' WHERE UserNum = '$getID'";
                                                $returnMessage = "Management permission removed from Player!";
                                            }else{
                                                $updateMANPerm = "UPDATE WEB_Perm_Menus SET management = '1' WHERE UserNum = '$getID'";
                                                $returnMessage = "Management permission has been added to Player!";
                                            }
                                            $updateMANPermQuery = sqlsrv_query($conn, $updateMANPerm, array());
                                            if($updateMANPermQuery):
                                                $returnSuccess = $returnMessage;
                                            else:
                                                $returnError = "Occurs an error when try update Permissions!";
                                            endif;
                                        }elseif($getAction == 'banperm'){
                                            if(isset($getSubAction) && $getSubAction == 'disable'){
                                                $updateBANPerm = "UPDATE WEB_Perm_Extras SET ban_perm = '0' WHERE UserNum = '$getID'";
                                                $returnMessage = "Ban permission removed from Player!";
                                            }else{
                                                $updateBANPerm = "UPDATE WEB_Perm_Extras SET ban_perm = '1' WHERE UserNum = '$getID'";
                                                $returnMessage = "Ban permission has been added to Player!";
                                            }
                                            $updateBANPermQuery = sqlsrv_query($conn, $updateBANPerm, array());
                                            if($updateBANPermQuery):
                                                $returnSuccess = $returnMessage;
                                            else:
                                                $returnError = "Occurs an error when try update Permissions!";
                                            endif;
                                            
                                        }
                                    }
                                    ?>
        <?php if (isset($returnSuccess)) { ?>
        <div class="alert alert-success j_dismiss"><?php echo $returnSuccess; ?></div>
        <?php } elseif (isset($returnWarning)) { ?>
        <div class="alert alert-warning j_dismiss"><?php echo $returnWarning; ?></div>
        <?php } elseif (isset($returnError)) { ?>
        <div class="alert alert-danger j_dismiss"><?php echo $returnError; ?></div>
        <?php } elseif (isset($returnWarningGLOBAL)) { ?>
        <div class="alert alert-warning"><?php echo $returnWarningGLOBAL; ?></div>
        <?php } ?>

        <div class="card card-modern">
            <div class="card-body">
                <div class="datatables-header-footer-wrapper">
                    <div class="datatable-header">
                        <div class="row align-items-center mb-3">


                            <h2 class="text-red">DEV Team Permissions <a href="#" class="btn btn-danger"
                                    data-target="#infoBanModal" data-toggle="modal"><i
                                        class="glyphicon glyphicon-exclamation-sign"></i>
                                    ATTENTION!</a> </h2>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>UserNum</th>
                                        <th>E-mail</th>
                                        <th>Permission Level</th>
                                        <th>Last login</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                        $selectTeam = "SELECT * FROM [". DATABASE_ACC ."].[dbo].cabal_auth_table WHERE IsDeveloper >= '1'";
                        $selectTeamParam = array();
                        $selectTeamQuery = sqlsrv_query($conn, $selectTeam, $selectTeamParam);
                        if ($selectTeamQuery):
                            while ($resTeam = sqlsrv_fetch_array($selectTeamQuery, SQLSRV_FETCH_ASSOC)):
                                ?>
                                    <tr>
                                        <td><?php echo $resTeam['UserNum']; ?></td>
                                        <td><?php echo $resTeam['Email']; ?></td>
                                        <td><?php echo $resTeam['IsDeveloper']; ?></td>
                                        <td><?php echo date('d/m/Y \a\t\ H:i', strtotime($resTeam['lastlogindate'])); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group flex-wrap">
                                                <button type="button"
                                                    class="mb-1 mt-1 mr-1 btn btn-default dropdown-toggle"
                                                    data-toggle="dropdown">Action <span class="caret"></span></button>
                                                <div class="dropdown-menu" role="menu">
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=removedev&id=<?php echo $resTeam['UserNum']; ?>">ลบ
                                                        DEV</a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php
                            endwhile;
                        endif;
                        ?>
                                </tbody>
                            </table>
                            <hr>

                            <h2 class="text-red">Menu permissions <a href="#" class="btn btn-danger"
                                    data-target="#infoBanModal" data-toggle="modal"><i
                                        class="glyphicon glyphicon-exclamation-sign"></i> ATTENTION!</a> <a href="#"
                                    class="btn btn-info" data-target="#menuPermModal" data-toggle="modal"><i
                                        class="glyphicon glyphicon-edit"></i> REGISTER NEW MENU PERM</a></h2>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>UserNum</th>
                                        <th>Server Admin</th>
                                        <th>Web Admin</th>
                                        <th>Management</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                        $selectTMenuPerm = "SELECT * FROM WEB_Perm_Menus";
                        $selectTMenuPermParam = array();
                        $selectTMenuPermOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectTMenuPermQuery = sqlsrv_query($conn, $selectTMenuPerm, $selectTMenuPermParam, $selectTMenuPermOpt);
                        if (sqlsrv_num_rows($selectTMenuPermQuery)):
                            while ($resMenuPerm = sqlsrv_fetch_array($selectTMenuPermQuery, SQLSRV_FETCH_ASSOC)):
                                ?>
                                    <tr>
                                        <td><?php echo $resMenuPerm['UserNum']; ?></td>
                                        <td><?php echo $serverAdmin = (($resMenuPerm['server_admin']) ? '<span class="label label-success">Yes</span>' : '<span class="label label-danger">No</span>'); ?>
                                        </td>
                                        <td><?php echo $webAdmin = (($resMenuPerm['web_admin']) ? '<span class="label label-success">Yes</span>' : '<span class="label label-danger">No</span>'); ?>
                                        </td>
                                        <td><?php echo $management = (($resMenuPerm['management']) ? '<span class="label label-success">Yes</span>' : '<span class="label label-danger">No</span>'); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group flex-wrap">
                                                <button type="button"
                                                    class="mb-1 mt-1 mr-1 btn btn-default dropdown-toggle"
                                                    data-toggle="dropdown">Action <span class="caret"></span></button>
                                                <div class="dropdown-menu" role="menu">
                                                    <?php if(!$resMenuPerm['server_admin']): ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=saperm&id=<?php echo $resMenuPerm['UserNum']; ?>">
                                                        Active Server Admin</a>
                                                    <?php else: ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=saperm&sub_action=disable&id=<?php echo $resMenuPerm['UserNum']; ?>">Disable
                                                        Server Admin</a>
                                                    <?php endif; if(!$resMenuPerm['web_admin']): ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=waperm&id=<?php echo $resMenuPerm['UserNum']; ?>">Active
                                                        Web Admin</a>
                                                    <?php else: ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=waperm&sub_action=disable&id=<?php echo $resMenuPerm['UserNum']; ?>">Disable
                                                        Web Admin</a>
                                                    <?php endif; if(!$resMenuPerm['management']): ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=manperm&id=<?php echo $resMenuPerm['UserNum']; ?>">Active
                                                        Management</a>
                                                    <?php else: ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=manperm&sub_action=disable&id=<?php echo $resMenuPerm['UserNum']; ?>">Disable
                                                        Management</a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <?php
                            endwhile;
                        else:
                            echo '<div class="alert alert-warning">No one have menu permissions</div>';
                        endif;
                        ?>
                                </tbody>
                            </table>
                            <hr>



                            <h2 class="text-red">Extra permissions <a href="#" class="btn btn-danger"
                                    data-target="#infoBanModal" data-toggle="modal"><i
                                        class="glyphicon glyphicon-exclamation-sign"></i> ATTENTION!</a> <a href="#"
                                    class="btn btn-info" data-target="#extraPermModal" data-toggle="modal"><i
                                        class="glyphicon glyphicon-edit"></i> REGISTER NEW EXTRA PERM</a></h2>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>UserNum</th>
                                        <th>Ban Permission</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                        $selectTExtraPerm = "SELECT * FROM WEB_Perm_Extras";
                        $selectTExtraPermParam = array();
                        $selectTExtraPermQuery = sqlsrv_query($conn, $selectTExtraPerm, $selectTExtraPermParam, array("Scrollable" => SQLSRV_CURSOR_KEYSET));
                        if (sqlsrv_num_rows($selectTExtraPermQuery)):
                            while ($resExtraPerm = sqlsrv_fetch_array($selectTExtraPermQuery, SQLSRV_FETCH_ASSOC)):
                                ?>
                                    <tr>
                                        <td><?php echo $resExtraPerm['UserNum']; ?></td>
                                        <td><?php echo $banPerm = (($resExtraPerm['ban_perm']) ? '<div class="label label-success">Yes</div>' : '<div class="label label-danger">No</div>'); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group flex-wrap">
                                                <button type="button"
                                                    class="mb-1 mt-1 mr-1 btn btn-default dropdown-toggle"
                                                    data-toggle="dropdown">Action <span class="caret"></span></button>
                                                <div class="dropdown-menu" role="menu">
                                                    <?php if(!$resExtraPerm['ban_perm']): ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=banperm&id=<?php echo $resExtraPerm['UserNum']; ?>">Enable
                                                        BAN Perm</a>
                                                    <?php else: ?>
                                                    <a class="dropdown-item text-1"
                                                        href="?url=web-admin/team&action=banperm&sub_action=disable&id=<?php echo $resExtraPerm['UserNum']; ?>">Disable
                                                        BAN Perm</a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php
                            endwhile;
                        else:
                            echo '<div class="alert alert-warning">No one have extra permissions</div>';
                        endif;
                        ?>
                                </tbody>
                            </table>
                        </div>
                        <hr class="solid mt-5 opacity-4">


                    </div>
                </div>

            </div>
        </div>


        <!-- INFO BAN MODAL -->
        <div class="modal fade" id="infoBanModal" tabindex="-1" role="dialog" aria-labelledby="infoBanModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="infoBanModalLabel">ATTENTION!</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>

                    </div>

                    <div class="modal-body">
                        <h4 class="text-red">How to use ban perm and other permissions</h4>
                        <hr>
                        <h3 class="text-red">Menu Permission</h3>
                        <dl>
                            <dt class="text-blue">Server Admin</dt>
                            <dd>User can access all functions in "Server Admin" menu</dd>
                            <dt class="text-blue">Web Admin</dt>
                            <dd>User can access all functions in "Web Admin" menu</dd>
                            <dt class="text-blue">Management</dt>
                            <dd>This is important, if user have this permission him can't have "Ban Perm" too.
                                If you
                                want
                                grant
                                ban access, set Ban Perm only!</dd>
                        </dl>
                        <h3 class="text-red">Extra Permission</h3>
                        <dl>
                            <dt class="text-blue">Ban Permission</dt>
                            <dd>User can ban players, but attention, dont give to him "Management Menu"
                                permission
                                because
                                him
                                will can access all functions from Management menu.</dd>
                        </dl>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default"
                            data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- end INFO BAN MODAL -->

        <!-- REGISTER MENU PERM MODAL -->
        <div class="modal fade" id="menuPermModal" tabindex="-1" role="dialog" aria-labelledby="menuPermModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="menuPermModalLabel">REGISTER NEW MENU PERMISSION</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>

                    </div>
                    <form method="post" name="j_menuperm" action="">
                        <div class="modal-body">
                            <div class="col-lg-12 j_alert"></div>
                            <div class="form-group">
                                <input name="CustomerID" class="form-control"
                                    placeholder="Player or Admin CustomerID ..." />
                            </div>

                            <div class="form-group">
                                <label class="text-red">Server Admin</label>
                                <div class="form-group">
                                    <label>ENABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_saperm_on" name="server_admin" type="radio" value="1">
                                        <label for="input_saperm_on"></label>
                                    </div>

                                    <label>DISABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_saperm_off" name="server_admin" type="radio" value="0">
                                        <label for="input_saperm_off"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="text-red">Web Admin</label>
                                <div class="form-group">
                                    <label>ENABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_waperm_on" name="web_admin" type="radio" value="1">
                                        <label for="input_waperm_on"></label>
                                    </div>

                                    <label>DISABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_waperm_off" name="web_admin" type="radio" value="0">
                                        <label for="input_waperm_off"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="text-red">Management</label>
                                <div class="form-group">
                                    <label>ENABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_manag_on" name="management" type="radio" value="1">
                                        <label for="input_manag_on"></label>
                                    </div>

                                    <label>DISABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_manag_off" name="management" type="radio" value="0">
                                        <label for="input_manag_off"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input type="submit" class="btn btn-success" name="registerMenuPerm"
                                value="<?php echo B_REGISTER; ?>" />
                            <button type="button" name="closeModal" class="btn btn-default"
                                data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- end REGISTER MENU MODAL -->

        <!-- REGISTER EXTRA PERM MODAL -->
        <div class="modal fade" id="extraPermModal" tabindex="-1" role="dialog" aria-labelledby="extraPermModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="extraPermModalLabel">REGISTER NEW EXTRA PERMISSION</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>

                    </div>

                    <form method="post" name="j_extraperm" action="">
                        <div class="modal-body">
                            <div class="col-lg-12 j_alert"></div>
                            <div class="form-group">
                                <input name="CustomerID" class="form-control"
                                    placeholder="Player or Admin CustomerID ..." />
                            </div>

                            <div class="form-group">
                                <label class="text-red">Ban Permission</label>
                                <div class="form-group">
                                    <label>ENABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_banperm_on" name="ban_perm" type="radio" value="1">
                                        <label for="input_banperm_on"></label>
                                    </div>

                                    <label>DISABLE</label>
                                    <div class="switch-button showcase-switch-button">
                                        <input id="input_banperm_off" name="ban_perm" type="radio" value="0">
                                        <label for="input_banperm_off"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input type="submit" class="btn btn-success" name="registerExtraPerm"
                                value="<?php echo B_REGISTER; ?>" />
                            <button type="button" name="closeModal" class="btn btn-default"
                                data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- end REGISTER EXTRA PERM MODAL -->