<?php $user->restrictionUser(true, $conn); ?>
<script type="text/javascript" charset="utf8" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-2.0.3.js"></script>
<div class="row">
    <div class="col-lg-12">
         <section class="panel">
            <header class="panel-heading">
                <h2><?php echo PT_MANAGECHARS; ?> <small><?php echo PT_MANAGECHARS_DESC; ?></small></h2>
            </header>
	<div class="panel-body">
    <div class="col-lg-12" style="margin: 10px 5px;">
        <form method="post" action="?url=manager/cresults" class="form-inline pull-right">
            <div class="form-group">
                <input type="text" class="form-control" name="search" placeholder="Search by เลขตัวละคร">
                <input type="submit" class="btn btn-info" name="btn_search" value="Search">
            </div>
        </form>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-body no-padd" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
                <?php if (isset($_GET['update']) || isset($_GET['update']) == 'true') { ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                <?php } ?>
                <table class="table table-bordered table-striped mb-none" id="datatable-default">
                    <thead>
                        <tr>
                            <th>Character</th>
                            <th>Name</th>
                            <th>LEV</th>
                            <th>Alz</th>
                            <th>แมพ</th>
							<th>อาชีพ</th>
							<th>Rank</th>
							<th>แนล</th>
							<th>ประเทศ</th>
							<th>เปลียนอาชีพ</th>
                            <th>Warid</th>
                            <th><?php echo T_ACTION; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        //header('Content-Type: text/html; charset=windows-874');
                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 2000;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table Where ChannelIdx = 16 AND Login = 1 ORDER BY LoginTime DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) { ?>
                            <tr
                            <?php
                            $selectUsersDataChar = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx = '$row[0]'";
                            $selectUsersDataQuery = sqlsrv_query($conn, $selectUsersDataChar, array());
                            $selectUsersDataFetch = sqlsrv_fetch_array($selectUsersDataQuery, SQLSRV_FETCH_ASSOC);
                            $style = $userLogin->decode_style($row[12]);
                            $usernum = floor($selectUsersDataFetch['CharacterIdx']/8);
                            $htmlimg = '<img src="assets/images/icons/vb.png">';

                            $selectUsersDataID = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = '$usernum'";
                            $selectUsersDataIDQuery = sqlsrv_query($conn, $selectUsersDataID, array());
                            $selectUsersDataIDFetch = sqlsrv_fetch_array($selectUsersDataIDQuery, SQLSRV_FETCH_ASSOC);

                            if ($selectUsersDataIDFetch['AuthType'] == '2' ||  $selectUsersDataIDFetch['AuthType'] == '3' ||  $selectUsersDataIDFetch['AuthType'] == '4') {   
								echo ' class="bg-red text-white"';
                            }
                            ?>
                                >
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $userLogin->thaitrans($selectUsersDataFetch['Name']); ?></td>
                                <td><?php echo $row[2]; ?></td>
                                <td><?php echo $row[9]; ?></td>
                                <td><?php echo $row[10]; ?></td>
								<td><?php echo $style['Class_Name']; ?></td>
								<td><?php echo $row[25]; ?></td>
								<td><?php echo $row[28]; ?></td>
								<td><img src="assets/images/cabal/<?php echo  $userLogin->nation($row[30]); ?>.gif" data-toggle="tooltip" data-title="<?php echo  $userLogin->nation($row[30]); ?>!" title="<?php echo  $userLogin->nation($row[30]); ?>!" class="img-circle"  style="width:30px; height: 30px;"></td>
                                <td><?php echo $row[38]; ?></td>
								<td><?php echo $warid = $userLogin->chartowar($row[0]); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                            <?php echo B_ACTION; ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu pull-right" role="menu">
                                            <?php if ($userLogin->recUserPerm($conn, 'ban_perm', 'extra')) { ?>
                                                <li><a href="?url=manager/see-char&id=<?php echo $row[0]; ?>"><?php echo B_SEEPLRINFO; ?></a></li>
                                                <li><a href="?url=manager/edit-char&id=<?php echo $row[0]; ?>">Edit</a></li>
												<li><a href="?url=manager/edit-char-forrank&id=<?php echo $row[0]; ?>">เพิ่มยศ</a></li>
                                                <li><a href="?url=manager/edit-char-delrank&id=<?php echo $row[0]; ?>">ลบยศ</a></li>
                                                <li><a href="?url=manager/see-char&id=<?php echo $row[0]; ?>&delete=wait">ลบตัวละคร</a></li>
                                            <?php } ?>
                                            <?php if ($selectUsersDataIDFetch['AuthType'] == '2' || $selectUsersDataIDFetch['AuthType'] == '3' || $selectUsersDataIDFetch['AuthType'] == '4') { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=unban-wait"><span class="text-white"><?php echo B_UNBAN; ?></span></a></li>
                                            <?php } else { ?>
                                                <li class="bg-red text-white"><a href="?url=manager/see-player&id=<?php echo $usernum; ?>&ban=wait"><span class="text-white"><?php echo B_BAN; ?></span></a></li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=manager/chars&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=manager/chars&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=manager/chars&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <a href="?url=manager/create-player" class="btn btn-info btn-block flat">Create account</a>
    </div>
</div>
</div>
</section>
