<?php $user->restrictionUser(true, $conn); ?>

<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-crown"></i> ทดสอบ Honor System ครบถ้วน
    </h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Honor Class + Title + Points ตาม Cabal Wiki</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fal fa-check-circle"></i>
                    <strong>เสร็จสมบูรณ์:</strong> ระบบ Honor ครบถ้วนตาม Cabal Wiki พร้อม Title และ Honor Points
                </div>
                
                <h5>🏆 ระบบ Honor ที่เพิ่มใหม่</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fal fa-medal"></i> Honor Class</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li><span class="badge badge-secondary">0</span> No Class</li>
                                    <li><span class="badge badge-success">1-5</span> Class 1-5</li>
                                    <li><span class="badge badge-info">6-10</span> Class 6-10</li>
                                    <li><span class="badge badge-warning">11-15</span> Class 11-15</li>
                                    <li><span class="badge badge-danger">16-19</span> Class 16-19</li>
                                    <li><span class="badge badge-dark">20</span> Class 20</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fal fa-star"></i> Honor Points</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0 small">
                                    <li>Class 1: <strong>10,000</strong></li>
                                    <li>Class 5: <strong>150,000</strong></li>
                                    <li>Class 10: <strong>5,000,000</strong></li>
                                    <li>Class 15: <strong>150,000,000</strong></li>
                                    <li>Class 20: <strong>2,000,000,000</strong></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fal fa-crown"></i> Honor Titles</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Class 11+:</strong></p>
                                <ul class="list-unstyled mb-0 small">
                                    <li><span class="text-danger">Capella:</span> Praetor</li>
                                    <li><span class="text-primary">Procyon:</span> Knight</li>
                                    <li><span class="text-secondary">Neutral:</span> Vagabond</li>
                                </ul>
                                <p class="mt-2"><strong>Class 20:</strong></p>
                                <ul class="list-unstyled mb-0 small">
                                    <li><span class="text-danger">Capella:</span> The Quaestor</li>
                                    <li><span class="text-primary">Procyon:</span> The Sovereign</li>
                                    <li><span class="text-secondary">Neutral:</span> Spirit Ascetic</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4">📊 Honor Class Distribution จากฐานข้อมูล</h5>
                <?php
                // ดึงข้อมูล Honor Class พร้อม Honor Points
                try {
                    $sql = "SELECT
                                CASE
                                    WHEN Reputation IS NULL THEN 'No Class'
                                    WHEN Reputation = 0 THEN 'No Class'
                                    WHEN Reputation BETWEEN 1 AND 20 THEN 'Class ' + CAST(Reputation AS VARCHAR)
                                    WHEN Reputation < 0 THEN 'No Class'
                                    WHEN Reputation > 20 THEN 'Class 20+'
                                    ELSE 'No Class'
                                END as honor_class,
                                ISNULL(Reputation, 0) as honor_value,
                                COUNT(*) as count,
                                CASE
                                    WHEN Reputation IS NULL OR Reputation = 0 THEN 0
                                    WHEN Reputation = 1 THEN 10000
                                    WHEN Reputation = 2 THEN 20000
                                    WHEN Reputation = 3 THEN 40000
                                    WHEN Reputation = 4 THEN 80000
                                    WHEN Reputation = 5 THEN 150000
                                    WHEN Reputation = 6 THEN 300000
                                    WHEN Reputation = 7 THEN 600000
                                    WHEN Reputation = 8 THEN 1200000
                                    WHEN Reputation = 9 THEN 2500000
                                    WHEN Reputation = 10 THEN 5000000
                                    WHEN Reputation = 11 THEN 10000000
                                    WHEN Reputation = 12 THEN 20000000
                                    WHEN Reputation = 13 THEN 40000000
                                    WHEN Reputation = 14 THEN 80000000
                                    WHEN Reputation = 15 THEN 150000000
                                    WHEN Reputation = 16 THEN 250000000
                                    WHEN Reputation = 17 THEN 500000000
                                    WHEN Reputation = 18 THEN 900000000
                                    WHEN Reputation = 19 THEN 1400000000
                                    WHEN Reputation = 20 THEN 2000000000
                                    ELSE 0
                                END as required_honor_points
                            FROM [".DATABASE_SV."].[dbo].cabal_character_table
                            GROUP BY Reputation
                            ORDER BY Reputation";
                    
                    $result = sqlsrv_query($conn, $sql);
                    
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm table-striped'>";
                    echo "<thead class='thead-dark'>";
                    echo "<tr>";
                    echo "<th>Honor Class</th>";
                    echo "<th>Honor Points</th>";
                    echo "<th>จำนวนผู้เล่น</th>";
                    echo "<th>%</th>";
                    echo "<th>Capella Title</th>";
                    echo "<th>Procyon Title</th>";
                    echo "<th>Neutral Title</th>";
                    echo "</tr>";
                    echo "</thead><tbody>";
                    
                    $totalCount = 0;
                    $honorData = [];
                    
                    if ($result) {
                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                            $honorData[] = $row;
                            $totalCount += $row['count'];
                        }
                    }
                    
                    // Include ฟังก์ชัน Honor Title
                    function getHonorTitleLocal($honorClass, $nation) {
                        $titles = [
                            11 => ['Capella' => 'Praetor', 'Procyon' => 'Knight', 'Neutral' => 'Vagabond'],
                            12 => ['Capella' => 'Praetor of Silence', 'Procyon' => 'Knight of Dawn', 'Neutral' => 'Disciple'],
                            13 => ['Capella' => 'Praetor of Insight', 'Procyon' => 'Knight of Fighting Spirit', 'Neutral' => 'Spirit Ascetic'],
                            14 => ['Capella' => 'Praetor of Tolerance', 'Procyon' => 'Knight of Passion', 'Neutral' => 'Spirit Ascetic'],
                            15 => ['Capella' => 'Praetor of Glory', 'Procyon' => 'Knight of Protection', 'Neutral' => 'Spirit Ascetic'],
                            16 => ['Capella' => 'Praetor of Faith', 'Procyon' => 'Knight of Valor', 'Neutral' => 'Spirit Ascetic'],
                            17 => ['Capella' => 'Praetor of Will', 'Procyon' => 'Knight of Devotion', 'Neutral' => 'Spirit Ascetic'],
                            18 => ['Capella' => 'Praetor of Truth', 'Procyon' => 'Knight of Judgment', 'Neutral' => 'Spirit Ascetic'],
                            19 => ['Capella' => 'Praetor of Brilliance', 'Procyon' => 'Knight of Tempest', 'Neutral' => 'Spirit Ascetic'],
                            20 => ['Capella' => 'The Quaestor', 'Procyon' => 'The Sovereign', 'Neutral' => 'Spirit Ascetic']
                        ];
                        return $titles[$honorClass][$nation] ?? '';
                    }
                    
                    foreach ($honorData as $row) {
                        $honorClass = $row['honor_class'];
                        $honorValue = $row['honor_value'];
                        $count = $row['count'];
                        $honorPoints = $row['required_honor_points'];
                        $percentage = $totalCount > 0 ? ($count / $totalCount) * 100 : 0;
                        
                        // กำหนดสี
                        $colorClass = 'secondary';
                        if ($honorValue == 0) $colorClass = 'secondary';
                        elseif ($honorValue >= 1 && $honorValue <= 5) $colorClass = 'success';
                        elseif ($honorValue >= 6 && $honorValue <= 10) $colorClass = 'info';
                        elseif ($honorValue >= 11 && $honorValue <= 15) $colorClass = 'warning';
                        elseif ($honorValue >= 16 && $honorValue <= 19) $colorClass = 'danger';
                        elseif ($honorValue >= 20) $colorClass = 'dark';
                        
                        echo "<tr>";
                        echo "<td><span class='badge badge-{$colorClass}'>" . htmlspecialchars($honorClass) . "</span></td>";
                        
                        // Honor Points
                        if ($honorPoints > 0) {
                            if ($honorPoints >= 1000000000) {
                                echo "<td><span class='text-warning'><i class='fal fa-star'></i> " . number_format($honorPoints / 1000000000, 1) . "B</span></td>";
                            } elseif ($honorPoints >= 1000000) {
                                echo "<td><span class='text-warning'><i class='fal fa-star'></i> " . number_format($honorPoints / 1000000, 1) . "M</span></td>";
                            } elseif ($honorPoints >= 1000) {
                                echo "<td><span class='text-warning'><i class='fal fa-star'></i> " . number_format($honorPoints / 1000, 1) . "K</span></td>";
                            } else {
                                echo "<td><span class='text-warning'><i class='fal fa-star'></i> " . number_format($honorPoints) . "</span></td>";
                            }
                        } else {
                            echo "<td><span class='text-muted'>-</span></td>";
                        }
                        
                        echo "<td>" . number_format($count) . "</td>";
                        echo "<td>" . number_format($percentage, 1) . "%</td>";
                        
                        // Titles
                        $capellaTitle = getHonorTitleLocal($honorValue, 'Capella');
                        $procyonTitle = getHonorTitleLocal($honorValue, 'Procyon');
                        $neutralTitle = getHonorTitleLocal($honorValue, 'Neutral');
                        
                        echo "<td>" . ($capellaTitle ? "<small class='text-danger'>{$capellaTitle}</small>" : "<span class='text-muted'>-</span>") . "</td>";
                        echo "<td>" . ($procyonTitle ? "<small class='text-primary'>{$procyonTitle}</small>" : "<span class='text-muted'>-</span>") . "</td>";
                        echo "<td>" . ($neutralTitle ? "<small class='text-secondary'>{$neutralTitle}</small>" : "<span class='text-muted'>-</span>") . "</td>";
                        
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table></div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <h5 class="mt-4">🧪 ทดสอบระบบ</h5>
                <div class="btn-group mb-3">
                    <button class="btn btn-primary" onclick="openCharacterStatistics()">
                        <i class="fal fa-chart-bar"></i> เปิดหน้าสถิติ
                    </button>
                    <button class="btn btn-info" onclick="testTopPlayers()">
                        <i class="fal fa-trophy"></i> ทดสอบ Top Players
                    </button>
                    <button class="btn btn-success" onclick="testHonorTitles()">
                        <i class="fal fa-crown"></i> ทดสอบ Honor Titles
                    </button>
                    <button class="btn btn-warning" onclick="refreshPage()">
                        <i class="fal fa-sync"></i> รีเฟรช
                    </button>
                </div>
                
                <div id="test-results"></div>
                
                <h5 class="mt-4">📋 สรุปฟีเจอร์ที่เพิ่ม</h5>
                <div class="accordion" id="featuresAccordion">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h6 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                    1. Honor Class + Points
                                </button>
                            </h6>
                        </div>
                        <div id="collapseOne" class="collapse show" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>แสดง Honor Class 0-20 ตาม Cabal Wiki</li>
                                    <li>แสดง Honor Points ที่ต้องการสำหรับแต่ละ Class</li>
                                    <li>จัดการ NULL และค่าผิดปกติ</li>
                                    <li>รองรับ Class 20+ สำหรับค่าเกิน 20</li>
                                    <li>ใช้สีตามความยากในการได้รับ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                    2. Honor Titles ตาม Nation
                                </button>
                            </h6>
                        </div>
                        <div id="collapseTwo" class="collapse" data-parent="#featuresAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>Title แตกต่างกันตาม Nation (Capella/Procyon/Neutral)</li>
                                    <li>เริ่มมี Title ตั้งแต่ Class 11</li>
                                    <li>Class 20 มี Title พิเศษ (The Quaestor/The Sovereign)</li>
                                    <li>แสดงใน Top Players table</li>
                                    <li>ใช้สีตาม Nation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" id="headingThree">
                            <h6 class="mb-0">
                                <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                    3. การแสดงผลที่ปรับปรุง
                                </button>
                            </h6>
                        </div>
                        <div id="collapseThree" class="collapse" data-parent="#fixesAccordion">
                            <div class="card-body">
                                <ul>
                                    <li>เพิ่มคอลัมน์ Honor Points ในตาราง</li>
                                    <li>เพิ่มคอลัมน์ Honor Title ใน Top Players</li>
                                    <li>แสดง Honor Points ในรูปแบบ K/M/B</li>
                                    <li>ลิงก์ไปยัง Cabal Wiki</li>
                                    <li>ไอคอนและสีที่เหมาะสม</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mt-4">
                    <h6><i class="fal fa-check-circle"></i> ระบบ Honor ครบถ้วนแล้ว:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ Honor Class 0-20 ตาม Cabal Wiki</li>
                                <li>✅ Honor Points ที่ต้องการ</li>
                                <li>✅ Honor Titles ตาม Nation</li>
                                <li>✅ จัดการ NULL และค่าผิดปกติ</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>✅ สีและไอคอนที่เหมาะสม</li>
                                <li>✅ แสดงใน Top Players</li>
                                <li>✅ รองรับ Class 20+</li>
                                <li>✅ ไม่มี Unknown อีกต่อไป</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openCharacterStatistics() {
    window.open('?url=manager_charecter/character-statistics', '_blank');
}

function testTopPlayers() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="fal fa-info-circle"></i> เปิดหน้าสถิติเพื่อดู Top Players ที่มี Honor Title</div>';
}

function testHonorTitles() {
    const resultDiv = document.getElementById('test-results');
    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fal fa-crown"></i> Honor Titles ทำงานตาม Nation:<br>• Capella: Praetor series<br>• Procyon: Knight series<br>• Neutral: Vagabond/Spirit Ascetic</div>';
}

function refreshPage() {
    location.reload();
}
</script>

<style>
.table th, .table td {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
